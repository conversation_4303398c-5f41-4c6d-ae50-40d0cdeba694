.job_build_image: &job_build_configuration
  allow_failure: true
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  script:
    - chmod 777 ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - source ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - echo "${REGISTRY} ="
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"${REGISTRY}\":{\"auth\":\"$(echo -n "json_key:${CI_REGISTRY_KEY}" | base64 | tr -d '\n' )\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor --cache=false
      --build-arg "RELEASE=${RELEASE}"
      --build-arg "MODE=${MODE}"  
      --build-arg "TYPE=${TYPE}"
      --build-arg "CI_JOB_TOKEN=${CI_JOB_TOKEN}"
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/images/${IMAGE_NAME}/dockerfile"
      --destination "${REGISTRY}/${IMAGE_NAME}:${TAG}"
    - echo "${NAME_POD}=true" >> build.env
  artifacts:
    reports:
      dotenv: build.env

task_branch_k8s:
  stage: prebuild
  rules:
    - if: $CI_COMMIT_TAG =~ /^Ver-\d+\.\d+.\d+.*(|\+\w+)$/
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  image: cr.yandex/crpsvv5iscafkhlubkmt/foquz-debian-builder-base:1.0.0
  script:
    - chmod 777 ./images/scripts/versions.sh
    - ./images/scripts/versions.sh
  artifacts:
    reports:
      dotenv: build.env

build-foquz-widget-dom-js:
  stage: build
  rules:
    - if: $CI_COMMIT_TAG =~ /^Ver-\d+\.\d+.\d+.*(|\+\w+)$/
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  variables:
    IMAGE_NAME: "foquz-widget-dom-js"
    NAME_POD: "WIDJET_DOM_JS"
  <<: *job_build_configuration

set-version-task:
  stage: deploy
  rules:
    - if: $CI_COMMIT_TAG =~ /^Ver-\d+\.\d+.\d+.*(|\+\w+)$/
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  variables:
    SET_VERSION: true
    TAG: "$TAG"
    RELEASE: "$RELEASE"
    MODE: "$MODE"
    TYPE: "$TYPE"
    BRANCH: "$CI_COMMIT_BRANCH"
    SOURCE_CI_PROJECT_TITLE: "$CI_PROJECT_TITLE"
    SOURCE_CI_PROJECT_PATH: "$CI_PROJECT_PATH"
    SOURCE_CI_COMMIT_SHA: "$CI_COMMIT_SHA"
    SOURCE_CI_COMMIT_MESSAGE: "$CI_COMMIT_TITLE"
    SOURCE_CI_PIPELINE_CREATED_AT: "$CI_PIPELINE_CREATED_AT"
    WIDGET: true
    CONTAINER_foquz_widget_dom_js: "$WIDJET_DOM_JS"
  trigger:
    project: 'doxsw/foquz-app-deploy'
    branch: 'stage'

    



