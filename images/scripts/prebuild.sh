#!/bin/sh

echo "$MODE"
echo "$TYPE"
echo "$IMAGE_NAME"
echo "$TAG"


  if [ -e "images/dockerignore/$IMAGE_NAME/$TYPE$MODE" ]
  then
      echo "images/dockerignore/$IMAGE_NAME/$TYPE$MODE > .dockerignore";
      cp "images/dockerignore/$IMAGE_NAME/$TYPE$MODE"  ".dockerignore"
  else
    if [ -e "images/dockerignore/$IMAGE_NAME/$MODE" ]
    then
      echo "images/dockerignore/$IMAGE_NAME/$MODE > .dockerignore";
      cp "images/dockerignore/$IMAGE_NAME/$MODE"  ".dockerignore"
    else
       if [ -e "images/dockerignore/$IMAGE_NAME/$TYPE" ]
       then
         echo "images/dockerignore/$IMAGE_NAME/$TYPE > .dockerignore";
         cp "images/dockerignore/$IMAGE_NAME/$TYPE"  ".dockerignore"
       else
         if [ -e "images/dockerignore/$IMAGE_NAME/.dockerignore" ]
         then
           echo "images/dockerignore/$IMAGE_NAME/.dockerignore > .dockerignore";
           cp "images/dockerignore/$IMAGE_NAME/.dockerignore"  ".dockerignore"
        fi
       fi
    fi
  fi


if [ -e "images/$IMAGE_NAME/$TYPE$MODE.dockerfile" ]
then
    echo "images/$IMAGE_NAME/$TYPE$MODE.dockerfile > images/$IMAGE_NAME/dockerfile";
    cp "images/$IMAGE_NAME/$TYPE$MODE.dockerfile"  "images/$IMAGE_NAME/dockerfile"
else
  if [ -e "images/$IMAGE_NAME/$MODE.dockerfile" ]
  then
    echo "images/$IMAGE_NAME/$MODE.dockerfile > images/$IMAGE_NAME/dockerfile";
    cp "images/$IMAGE_NAME/$MODE.dockerfile"  "images/$IMAGE_NAME/dockerfile"
  else
     if [ -e "images/$IMAGE_NAME/$TYPE.dockerfile" ]
     then
       echo "images/$IMAGE_NAME/$TYPE.dockerfile > images/$IMAGE_NAME/dockerfile";
       cp "images/$IMAGE_NAME/$TYPE.dockerfile"  "images/$IMAGE_NAME/dockerfile"
     fi
  fi
fi
