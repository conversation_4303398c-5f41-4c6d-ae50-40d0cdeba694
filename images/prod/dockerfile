FROM  cr.yandex/crpsvv5iscafkhlubkmt/node:20-alpine AS node

COPY ./ /app

RUN cd /app \
    && export NODE_ENV=production-instance \
    && npm install \
    && npm run build -- --mode production
&& rm -R /app/dist/mocked-assets  \
    && rm  /app/dist/mockServiceWorker.js

COPY ./ /app2

RUN cd /app2 \
    && export NODE_ENV=production-preview \
    && npm install \
    && npm run build -- --mode production \
&& rm -R /app2/dist/mocked-assets  \
    && rm  /app2/dist/mockServiceWorker.js


FROM alpine:latest

COPY --from=node  /app/dist /jsbuild
COPY --from=node  /app2/dist /jsbuild-preview
