get-tag:
  stage: prebuild
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  image: cr.yandex/crpsvv5iscafkhlubkmt/foquz-debian-builder-base:1.0.0
  script:
    - chmod 777 ./images/versions.sh
    - ./images/versions.sh
  artifacts:
    reports:
      dotenv: build.env

# сборка в конейнер для кластера
build-image:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: ['']
  variables:
    IMAGE_NAME: poll-vue-dev
    NAME_POD: "POLL_VUE"
  script:
    - mkdir -p /kaniko/.images
    - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(echo -n "json_key:${CI_REGISTRY_KEY}" | base64 | tr -d '\n' )\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/images/dev/dockerfile"
      --destination "${CI_REGISTRY}/${IMAGE_NAME}:${TAG}"
      --destination "${CI_REGISTRY}/foquz-quiz-js:${TAG}"
    - echo "${NAME_POD}=true" >> build.env
  artifacts:
    reports:
      dotenv: build.env

deploy-stand:
  stage: deploy
  variables:
    SET_VERSION: true
    TAG: "$TAG"
    RELEASE: "$RELEASE"
    MODE: "$MODE"
    TYPE: "$TYPE"
    BRANCH: "$CI_COMMIT_BRANCH"
    SOURCE_CI_PROJECT_TITLE: "$CI_PROJECT_TITLE"
    SOURCE_CI_PROJECT_PATH: "$CI_PROJECT_PATH"
    SOURCE_CI_COMMIT_SHA: "$CI_COMMIT_SHA"
    SOURCE_CI_COMMIT_MESSAGE: "$CI_COMMIT_TITLE"
    SOURCE_CI_PIPELINE_CREATED_AT: "$CI_PIPELINE_CREATED_AT"
    CONTAINER_foquz_quiz_js: true
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  trigger:
      project: 'doxsw/foquz-app-deploy'
      branch: 'stage'
