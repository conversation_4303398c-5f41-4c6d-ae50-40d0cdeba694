FROM  cr.yandex/crpsvv5iscafkhlubkmt/node:20-alpine AS node

COPY ./ /app

RUN cd /app \
    && export NODE_ENV=staging \
    && npm install \
    && npm run lint:fix \
    && npm run build -- --mode staging \
&& rm -R /app/dist/mocked-assets  \
    && rm  /app/dist/mockServiceWorker.js

COPY ./ /app2

RUN cd /app2 \
    && export NODE_ENV=staging-preview \
    && npm install \
    && npm run lint:fix \
    && npm run build -- --mode staging-preview \
&& rm -R /app2/dist/mocked-assets  \
    && rm  /app2/dist/mockServiceWorker.js

FROM cr.yandex/crpsvv5iscafkhlubkmt/node:20-alpine

COPY --from=node  /app/dist /jsbuild
COPY --from=node  /app2/dist /jsbuild-preview
