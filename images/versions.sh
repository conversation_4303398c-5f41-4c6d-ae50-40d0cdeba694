#!/bin/bash
TYPE="saas"

echo

if [ "$CI_COMMIT_BRANCH" = "release" ] ||  [ "$CI_COMMIT_BRANCH" = "bugfixes" ]
then
  v=$(curl -Ss --request GET --header "PRIVATE-TOKEN: $GIT_TOKEN" "https://doxsw.gitlab.yandexcloud.net/api/v4/projects/doxsw%2Ffoquz-quiz/repository/tags"  | jq -r '.[0] | .name')
  v1=$(echo $v | awk -F'.' '{print $1}')
  v2=$(echo $v | awk -F'.' '{print $2}')
  v3=$(echo $v | awk -F'.' '{print $3}')
  v1=${v1:4:5}
  if [ "$CI_COMMIT_BRANCH" = "release" ]
  then
    let v2=v2+1
    TAG="$v1.$v2.0-dev-$CI_PIPELINE_IID"
    RELEASE="release"
    MODE="development"
  fi
  if [ "$CI_COMMIT_BRANCH" = "bugfixes" ]
  then
    let v3=v3+1
    TAG="$v1.$v2.$v3-dev-$CI_PIPELINE_IID"
    RELEASE="bugfixes"
    MODE="development"
  fi
else
    BRANCH=$(echo $CI_COMMIT_BRANCH | awk 'match($0, /TASK-[0-9]+?/) { print substr( $0, RSTART+5, RLENGTH-5 )}' )
    TAG=$(echo "task-$BRANCH-$CI_PIPELINE_IID")
    RELEASE=$(echo "task-$BRANCH")
    MODE="development"
fi

echo $TAG
echo $TYPE
echo $RELEASE
echo $MODE

echo "TAG=$TAG" > build.env
echo "RELEASE=$RELEASE" >> build.env
echo "TYPE=$TYPE" >> build.env
echo "MODE=$MODE" >> build.env

