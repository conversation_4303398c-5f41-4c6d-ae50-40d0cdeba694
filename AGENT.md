# Agent Configuration

## Commands
- **Build**: `npm run build` (includes type checking)
- **Dev**: `npm run dev` (development server)
- **Test**: `npm run test` (Playwright e2e tests)
- **Test single**: `npx playwright test <test-file-path>` (run specific test)
- **Type check**: `npm run type-check` (Vue TypeScript checking)
- **Lint**: `npm run lint` (ESLint)
- **Lint fix**: `npm run lint:fix` (ESLint with auto-fix)

## Architecture
- **Structure**: Feature Sliced Design (FSD) - `app/`, `entities/`, `features/`, `widgets/`, `shared/`
- **State management**: Pinia stores
- **API**: Mock Service Worker (MSW) for testing, real API calls in `entities/poll/api`
- **Testing**: Playwright e2e tests with MSW mocks
- **Main entities**: poll, question types, points system, custom themes

## Code Style
- **ESLint**: Uses @antfu/eslint-config with Vue 3 support
- **TypeScript**: Gradual migration (strict mode), new files should use .ts/.vue with `<script lang="ts">`
- **Imports**: Use path aliases - `@/`, `@shared/`, `@entities/`, `@features/`, `@widgets/`
- **Naming**: kebab-case for files, camelCase for variables, PascalCase for components
- **Vue**: Composition API with `<script setup>`, reactive refs, computed properties
- **Store**: Pinia composables pattern with `useStore` prefix
- **Error handling**: Sentry integration for production errors
