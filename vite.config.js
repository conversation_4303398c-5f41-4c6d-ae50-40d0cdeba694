import path from "path";
/** @type {import('vite').UserConfig} */
export default {
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@components": path.resolve(__dirname, "./src/components"),
    },
  },
  css: {
    modules: {
      // generateScopedName: "_fc_[hash:base64:5]",
    },
  },

  build: {
    cssCodeSplit: process.env.NODE_ENV !== "production",
    rollupOptions: {
      input: {
        widget: "./widget.js",
      },
      output: {
        format: "esm",
        entryFileNames: "[name].js",
        chunkFileNames: "[hash].js",
        assetFileNames: "styles.[ext]",
        inlineDynamicImports: false,
        // manualChunks: {
        //   initCustomScrollbar: ["./src/helpers/initCustomScrollbar.js"],
        // },
      },
    },
  },
};
