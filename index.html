<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/x-icon" href="/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>FOQUZ Widget (DOM version)</title>
  <script>
    document.addEventListener("foquz::loaded", () => {
      console.log("FQZWidget is ready!", window.FQZWidget);
    });

    document.addEventListener("foquz::shown", () => {
      console.log("FQZWidget is now visible");
    });

    document.addEventListener("foquz::hidden", () => {
      console.log("FQZWidget is now hidden");
    });

    document.addEventListener("foquz::destroyed", () => {
      console.log("FQZWidget has been destroyed");
    });

    document.addEventListener("foquz::completed", () => {
      console.log("FQZWidget has been completed");
    });
  </script>
</head>
<body style="min-height: 4000px; min-width: 4000px;">
<div>widget should appear in a few seconds...</div>
<div id="my-awesome-button"></div>
<!-- <script>
window.FOQUZ_SDK = {
  widget: {
    widget_code: 'badab6eb8f6bb49d2d7141692ec9da96',
    id_parent_element: "widget_foquz"
  },
  params: {
    phone: '+79997775588',
  },
};

(function (f, o, q, u, z) {
  u = o.createElement("script");
  z = o.getElementsByTagName("script")[0];
  u.async = 1;
  u.src = q + '?t=' + Date.now();
  z ? z.parentNode.insertBefore(u, z) : o.head.appendChild(u);
})(window, document, "https://doxswf.ru/widgets/poll/widget_new.js");
</script> -->
<script>
  window.FOQUZ_SDK = {
    _test: {
      root: "https://widget.devfoquz.ru/",
      assetsRoot: "https://devfoquz.ru/",
      stylesPath: "dist/styles.css", // Для локальной загрузки стилей. Используется на этой странице
      // pollLoadDelay: 5000, // Установка задержки загрузки опроса (5 секунд)
    },
    widget: {
      widget_code: "746d1df7e69b7567707c0849c9e11c40",
      id_parent_element: "my-awesome-button",
      // useCustomScrollbar: false,
    },
    params: {
      // phone: "+79997775588",
      hello: "world. One Two Three",
      p1: 1,
      p2: 2,
      // lang: "en",
    },
  };
  // (function (f, o, q, u, z) {
  //   u = o.createElement("script");
  //   z = o.getElementsByTagName("script")[0];
  //   u.async = 1;
  //   u.src = q + '?t=' + Date.now();
  //   z ? z.parentNode.insertBefore(u, z) : o.head.appendChild(u);
  // })(window, document, "http://localhost:5173/dist/widget_dom.js");
</script>
<script type="module" src="/widget.js"></script>
</body>
</html>
