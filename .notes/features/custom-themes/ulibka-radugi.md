# Улыбка радуги - Custom Theme

## Business Goal
Create custom theme for "Улыбка радуги" brand.

## General Requirements

### Typography & Layout
- Content width matches design
- Use Rubik font from Google Fonts
- Question text: size and alignment from question settings (if not set, use default from design)
- Additional description: size and alignment from question settings (if not set, use default from design)
- "Optional" label: fixed size, color and alignment (if not set, use default from design)
- Questions content aligned to top (no need to make it centered and scrollable)
- Fixed text sizes for mobile view
- Error messages aligned left

### Header
- Fixed on scroll
- Settings from poll design:
  - Enable/disable header
  - Logo
  - Background color, height, padding
- Logo dimensions match design for desktop/mobile
- Fixed bottom border and shadow

### Progress Bar
- Styles from design
- Fixed on scroll

### Page Numbers
- Gray color from design
- Active element fill - primary-active

### Buttons
- Styles for primary buttons (Submit, Next, Complete, etc)
- Styles for secondary buttons (Back, Test Report, Print)
- Button names from design/question settings
- Buttons always below content, no scroll fixing
- Next button clickable by default, activates on input

### Question Types Styling

#### NPS Rating
Standard:
- Monochrome gray scale
- Selected element in purple

Variants:
- No dividing lines between variants
- Left-aligned variant texts
- Larger default text size
- Media variant display consistent for single/multiple choice

#### Star Rating
- Scale color configurable in question settings
- Left alignment

#### Regular Rating
- Elements stretch to content
- Scale color configurable in question settings

#### Answer Options
- Selected element highlighted in green
- Green border on input fields focus

#### Text Input
- Maximum width for desktop/tablet per design
- Date/phone/email/number controls width as current
- Left-aligned input fields
- Green border on focus
- Placeholder example in design

#### Date/Time
- Control widths as current implementation
- Styles match Text Input type

#### Address
- Styling matches Text Input type

#### File Upload
- Border color matches input fields
- Icon color from UI kit - gray default

#### Survey
- Styling matches Text Input type

#### Priority
- Add borders to variant blocks
- Icon color from UI kit - gray default
- Border color matches input fields
- Full content width blocks

#### Scale
Standard:
- Slider colors from UI kit
- Input field color matches Text Input
- Full content width block

Variants:
- Same as standard
- Left-aligned variant names
- No dividing lines between variants

#### Image/Video Selection
- Current display implementation
- Primary-active fill color for selected element
- Show max elements fitting content width
- Left alignment

#### Gallery Rating
- Scale color #8327D8 (star borders and fill)
- Show max elements fitting content width
- Left alignment

#### Smile Rating
- Current display implementation
- Left alignment

#### Simple Matrix
- Gray color from design
- Primary-active fill for active element
- Left-aligned variant names
- No dividing lines between variants
- Matrix scale stretched to content width
- Dropdown styling matches input fields

#### 3D Matrix
- Content stretched to full width
- Dropdown styling:
  - Border color matches input fields
  - Gray default icon color
  - Green for selected state
- Dividing line color from UI kit - gray-main

#### Star Rating for Options
- Full content width block
- Star scale width as current
- Left-aligned variant names
- No dividing lines between variants
- Configurable star color

#### Semantic Differential
- Gray color from design
- Primary-active fill for active element
- Left-aligned scale rows
- No dividing lines between rows

#### Classifier
- Icon color from UI kit - gray default
- Left alignment

### Intermediate/Start/End Screens
- Image from question settings (380x380px desktop, 290x290px mobile)
- No image rounding
- Text alignment and sizes from Text on Page control
- Block layout matches design
- Content vertically centered
