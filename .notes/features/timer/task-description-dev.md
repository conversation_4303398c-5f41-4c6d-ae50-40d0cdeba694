# Задача: Реализация таймера обратного отсчета для прохождения опроса

## Бизнес-цель

Реализовать отображение таймера обратного отсчета времени для заполнения анкеты в новом прохождени
опросов.

## Настройки

В настройках опроса существует параметр "Время заполнения анкеты", позволяющий задать ограничение
времени прохождения в часах, минутах или секундах.

## Макеты

UIKIT: [LINK_PLACEHOLDER_1] Прохождение: [LINK_PLACEHOLDER_2]

## Детальное описание задачи

Необходимо реализовать обработку параметра "Время заполнения анкеты" в новом интерфейсе прохождени
опросов.

### Детали реализации

1.  **Переменная времени в старом прохождении:** В старой версии прохождения опроса для параметра
    "Время заполнения анкеты" использовалась переменная `time_to_pass` (#1241). [LINK_PLACEHOLDER_8]
2.  **Отображение таймера:** Если в настройках опроса указано "Время заполнения анкеты", в процессе
    прохождения опроса должен отображаться таймер обратного отсчета. Пример реализации в старом
    прохождении: [LINK_PLACEHOLDER_9]. Пример макета: [LINK_PLACEHOLDER_10], [LINK_PLACEHOLDER_11].
    _ Стили таймера:
    _ Размеры текста фиксированные, согласно макету.
    _ Шрифт: "Наименование шрифта" из настроек дизайна. [LINK_PLACEHOLDER_12],
    [LINK_PLACEHOLDER_13]
    _ Цвет текста: "Цвет текста на фоне" из настроек дизайна. [LINK_PLACEHOLDER_14],
    [LINK_PLACEHOLDER_15] \* Время отображается жирным шрифтом. [LINK_PLACEHOLDER_16]
3.  **Момент запуска таймера:** Таймер обратного отсчета должен запускаться при переходе респондент
    к первому вопросу, который не является стартовым экраном.
4.  **Расположение таймера:**
    _ Несколько вопросов на странице: Таймер отображается на подложке вместе с кнопками-стрелкам
    [LINK_PLACEHOLDER_28], [LINK_PLACEHOLDER_29]
    _ Один вопрос на странице: Таймер отображается на подложке с кнопками "Назад" / "Далее". \* Отображение при прокрутке раздела до конца: [LINK_PLACEHOLDER_30], [LINK_PLACEHOLDER_31]
5.  **Отключение таймера:** Таймер должен автоматически отключаться при переходе к конечному экрану
    или стандартному завершению опроса.
6.  **Информационная страница об истечении времени:** Если время на прохождение опроса истекло,
    респонденту вместо вопросов должна отображаться информационная страница. [LINK_PLACEHOLDER_32],
    [LINK_PLACEHOLDER_33]
    _ Текст страницы: "К сожалению, время для прохождения опроса истекло"
    _ Стили текста информационной страницы:
    _ Цвет текста: "Цвет текста на фоне" из настроек дизайна. [LINK_PLACEHOLDER_34]
    _ Шрифт: "Наименование шрифта" из настроек дизайна. [LINK_PLACEHOLDER_35]
    _ Размер текста: "Размер заголовка" из настроек дизайна (фиксированный размер для
    мобильной версии, как в макете). [LINK_PLACEHOLDER_36]
    _ Стили иконки: [LINK_PLACEHOLDER_37] \* Цвет иконки: "Цвет текста на фоне" из настроек дизайна. [LINK_PLACEHOLDER_38]
7.  **Обновление страницы после истечения времени:** Для анкет, привязанных к клиенту, при обновлен
    страницы после истечения времени также должен отображаться информационный экран из пункта 6.
8.  **Рекламный блок для тарифа "Базовый":** На информационной странице об истечении времени для
    тарифа "Базовый" необходимо показывать рекламный блок.
9.  **Адаптивность:** Раздел должен быть адаптивным и корректно отображаться на различных экранах.
10. **Мультиязычность:** Необходимо проверить поддержку мультиязычности.

## Технические детали реализации

1.  **Инициализация данных таймера:**

        *   В методе `fetchPoll` файла `src/entities/poll/model/store.js` добавить обработку данных

    таймера, которые приходят с бэкенда. \* Данные таймера будут приходить в объекте `pollData` в следующем формате:

        ```typescript
        timer: {
          enabled: boolean // включен ли таймер

    status: 'not-started' | 'active' | 'finished' // статус таймера
    duration: number // общее время на прохождение опроса в секундах
    elapsedTime: number // сколько времени прошло с начала опроса в секундах
    }

    ```

        *   Создать в сторе `poll` реактивный объект `timer` (ref) для хранения этих данных.
        *   Добавить подробный JSDoc на русском языке для объекта `timer` и его полей.

    ```

2.  **Запуск таймера:**

        *   В файле `src/entities/poll/model/store.js` добавить функцию `setupTimer`.
        *   Функция `setupTimer` должна запускать таймер (обновлять `elapsedTime` каждую секунду), есл

    `timer.status === 'not-started'`.
    _ Функция `setupTimer` должна вызываться при переходе на первую страницу, не являющуюся
    стартовым экраном.
    _ Добавить в `src/entities/poll/api/index.js` функцию `setupTimer` с вызовом метода
    `p/setup-timer`. Функция должна принимать `authKey`.

3.  **Отображение таймера:**

    - Создать новый UI-компонент `src/shared/ui/PollTimer.vue`.
    - Компонент `PollTimer` должен принимать пропсы:
      - `label` (string, необязательный) - текст метки таймера.
      - `elapsedTime` (number) - количество прошедших секунд.
    - Компонент должен отображать метку (если передана) и время в формате `ЧЧ:ММ:СС`.
    - Добавить компонент `PollTimer` в `src/widgets/ui/PollActions.vue`.
    - Таймер должен быть абсолютно спозиционирован справа.
    - Таймер должен отображаться только если `timer.enabled === true`.

4.  **Окончание времени:**

        *   Если `timer.status === 'finished'` или `timer.elapsedTime >= timer.dura`, скрыть

    опрос (`PollContent`) и отобразить информационный экран `InfoScreen` с типом `timer-finished`.
    _ Добавить пустой svg файл для иконки в `InfoScreen`.
    _ Переход на `InfoScreen` должен быть плавным.

## Файлы, которые будут затронуты

- `src/entities/poll/model/store.js`
- `src/entities/poll/api/index.js`
- `src/widgets/ui/PollActions.vue`
- `src/widgets/ui/PollContent.vue`
- `src/widgets/ui/InfoScreen.vue`
- `src/shared/ui/PollTimer.vue` // new
- `src/shared/constants/index.js` (возможно)
- `src/shared/helpers/index.js` (возможно)

## Дополнительно

- Код должен быть лаконичным и простым для чтения.
- При необходимости, добавляйте комментарии на русском языке.
