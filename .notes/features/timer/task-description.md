Бизнес-цель: Реализовать отображение таймера обратного отсчета времени для заполнения анкеты в новом
прохождении опросов.

Настройки:

В настройках опроса существует параметр "Время заполнения анкеты", позволяющий задать ограничение по
времени прохождения в часах, минутах или секундах.

Макеты:

UIKIT: [LINK_PLACEHOLDER_1] Прохождение: [LINK_PLACEHOLDER_2]

Описание задачи:

Необходимо реализовать обработку параметра "Время заполнения анкеты" в новом интерфейсе прохождения
опросов.

Детали реализации:

1 Переменная времени в старом прохождении: В старой версии прохождения опроса для параметра "Время
заполнения анкеты" использовалась переменная time_to_pass (#1241). [LINK_PLACEHOLDER_8]
2 Отображение таймера: Если в настройках опроса указано "Время заполнения анкеты", в процессе
прохождения опроса должен отображаться таймер обратного отсчета. Пример реализации в старом
прохождении: [LINK_PLACEHOLDER_9]. Пример макета: [LINK_PLACEHOLDER_10], [LINK_PLACEHOLDER_11].
• Стили таймера:
• Размеры текста фиксированные, согласно макету.
• Шрифт: "Наименование шрифта" из настроек дизайна. [LINK_PLACEHOLDER_12],
[LINK_PLACEHOLDER_13]
• Цвет текста: "Цвет текста на фоне" из настроек дизайна. [LINK_PLACEHOLDER_14],
[LINK_PLACEHOLDER_15]
• Время отображается жирным шрифтом. [LINK_PLACEHOLDER_16]
3 Момент запуска таймера: Таймер обратного отсчета должен запускаться при переходе респондента к
первому вопросу. Это может быть после стартового экрана или текстового блока.
• Примеры опросов:
• Со стартовым экраном: [LINK_PLACEHOLDER_17] --> [LINK_PLACEHOLDER_18],
[LINK_PLACEHOLDER_19], [LINK_PLACEHOLDER_20]
• С текстовым блоком: [LINK_PLACEHOLDER_21] --> [LINK_PLACEHOLDER_22], [LINK_PLACEHOLDER_23],
[LINK_PLACEHOLDER_24]
• Вопросы на одной странице: [LINK_PLACEHOLDER_25], [LINK_PLACEHOLDER_26],
[LINK_PLACEHOLDER_27]
4 Расположение таймера:
• Несколько вопросов на странице: Таймер отображается на подложке вместе с кнопками-стрелками.
[LINK_PLACEHOLDER_28], [LINK_PLACEHOLDER_29]
• Один вопрос на странице: Таймер отображается на подложке с кнопками "Назад" / "Далее".
• Отображение при прокрутке раздела до конца: [LINK_PLACEHOLDER_30], [LINK_PLACEHOLDER_31]
5 Отключение таймера: Таймер должен автоматически отключаться при переходе к конечному экрану или
стандартному завершению опроса.
6 Информационная страница об истечении времени: Если время на прохождение опроса истекло,
респонденту вместо вопросов должна отображаться информационная страница. [LINK_PLACEHOLDER_32],
[LINK_PLACEHOLDER_33]
• Текст страницы: "К сожалению, время для прохождения опроса истекло"
• Стили текста информационной страницы:
• Цвет текста: "Цвет текста на фоне" из настроек дизайна. [LINK_PLACEHOLDER_34]
• Шрифт: "Наименование шрифта" из настроек дизайна. [LINK_PLACEHOLDER_35]
• Размер текста: "Размер заголовка" из настроек дизайна (фиксированный размер для мобильной
версии, как в макете). [LINK_PLACEHOLDER_36]
• Стили иконки: [LINK_PLACEHOLDER_37]
• Цвет иконки: "Цвет текста на фоне" из настроек дизайна. [LINK_PLACEHOLDER_38]
7 Обновление страницы после истечения времени: Для анкет, привязанных к клиенту, при обновлении
страницы после истечения времени также должен отображаться информационный экран из пункта 6.
8 Рекламный блок для тарифа "Базовый": На информационной странице об истечении времени для тарифа
"Базовый" необходимо показывать рекламный блок.
9 Адаптивность: Раздел должен быть адаптивным и корректно отображаться на различных экранах.
10 Мультиязычность: Необходимо проверить поддержку мультиязычности.
