**Task: Frontend (Vue) - Add VariantsController Functionality to NPS Rating with Variants**

**Goal:** Implement a `VariantsController` feature for the "NPS Rating / For Variants" question type. This feature is already implemented in Knockout, but needs to be ported to Vue. This task focuses on a _single_, shared `VariantsController` for all NPS variants.

**Description:**

1.  **Pass `VariantsController` State:** Ensure the standard NPS Rating/Variants question component receives the new `VariantsController` option's state.

2.  **Disabled `VariantsController`:** If the `VariantsController` option is disabled, no changes are needed.

3.  **Enabled `VariantsController`:**

    - The `VariantsController` should be hidden by default.
    - The `VariantsController` should smoothly appear if _any_ selected variant's rating falls within a specified range.

4.  **`VariantsController` Answer Types (Illustrative, already implemented in Star Rating. Reference these for the UI):**

    - **Single Choice (No File):** Radio buttons.
    - **Multiple Choice (No File):** Checkboxes. Consider the "Max selected answers" parameter, blocking further selections when the limit is reached.
    - **Text Answer:** Textarea.
    - **Single/Multiple Choice (With Files):**
      - Display radio buttons or checkboxes, depending on single/multiple choice setting.
      - Show image previews.
      - For videos, show poster, darkened background, and play icon.
      - Scale image/poster to fit.
      - Clicking video plays fullscreen.
      - Clicking image toggles selection state.
      - Variant text (if present): Fixed size, font from design settings ("Font Name"), color from design settings ("Main Background Color").
      - Gallery: Use left/right arrows for slider navigation. Styles are fixed. Hide arrows when at start/end, or if only two gallery items.

5.  **Next Button Validation:**

    - When multiple choice and there is minimal selected choice params, show error message.

6.  **Responsiveness:** Ensure the component works correctly on all screen sizes.

7.  **Multilingual:** Verify that multilingual functionality works.

8.  **Answer Display:** Check the answer display functionality.

9.  **Donor-Recipient:** Verify the Donor-Recipient functionality.

<implementation-details>

- **API:** The backend API is already implemented.
- **Existing Implementations:** Refer to the "Star Rating Variants" question type for existing implementations of the single/multiple choice and text answer `VariantsController` types.

IMPORTANT: for each function add detailed JSDoc in Russian

- **Implementation Steps:**

1. Add EXTRA_QUESTION_TYPE const to one of constants file (SINGLE, COMMON_PER_VARIANT, DIFFERENT_PER_VARIANT). Add JS Doc in Russian
2. Implement `variantsController` for the whole question if extra_question_type === EXTRA_QUESTION_TYPE.SINGLE
3. Add Variants.vue to NpsQuestion.vue (see StarVariantsQuestion.vue as an example).
4. Add prop to Variants.vue `mediaView` ('default' | 'gallery'). If it's gallery we should render VariantsGallery.vue (not implemented yet)
5. Implement VariantsGalleryItem.vue. It should look exactly like `FilePreview.vue` but with checkbox on the top right of preview. Take all styles from FilePreview.vue and all props from FileCheck.vue
6. Implement VariantsGallery.vue. It should contain swiper-container with slides as `VariantsGalleryItem.vue`. See `Gallery.vue` as an example of how initialize swiper. It should have slidesPerView = 'auto' with slight gap between items. If items are all fit in container, hide arrows. If not, show arrows. Apply gradient shadows as in `CommandContent.vue`, ()
   </implementation-details>

<server-data-example>

Based on `StarsVariantsQuestionModel.js`, the `VariantsController` likely uses a structure similar to this (this is the data that should be passed to the `VariantsController` component):

```json
{
  "extra_required": 0,
  "variantsType": 0,
  "textFieldParam": {
    "min": 0,
    "max": 250
  },
  "placeholderText": "Placeholder text",
  "isHaveCustomField": false,
  "dropdownVariants": false,
  "self_variant_text": "Other",
  "self_variant_nothing": 0,
  "self_variant_comment_required": 0,
  "detail_answers": [
    // Note: There is an extra_question flag (0 | 1) for each variant
    // Note: Variants can include files (check file_id, file_url, preview_url)
    {
      "id": 118160,
      "type": 0,
      "variant": "вар 1",
      "description": "",
      "position": 1,
      "points": null,
      "without_points": 0,
      "is_deleted": 0,
      "extra_question": 0,
      "need_extra": 1,
      "comment_required": 0,
      "dictionary_element_id": null,
      "file_id": null,
      "file_url": null,
      "preview_url": null,
      "detail_question": "",
      "question_detail_id": null,
      "extra_question_rate_from": 0,
      "extra_question_rate_to": 10,
      "extra_required": 1,
      "min_choose_extra_variants": null,
      "max_choose_extra_variants": null,
      "variants_with_files": 0,
      "self_variant_text": "",
      "self_variant_placeholder_text": "",
      "variants_element_type": 0,
      "for_all_rates": 1,
      "placeholder_text": "",
      "self_variant_minlength": 0,
      "self_variant_maxlength": 250,
      "text_variant_minlength": 0,
      "text_variant_maxlength": 250,
      "is_self_answer": 0
    },
    {
      "id": 118161,
      "type": 0,
      "variant": "вар 2",
      "description": "",
      "position": 2,
      "points": null,
      "without_points": 0,
      "is_deleted": 0,
      "extra_question": 0,
      "need_extra": 1,
      "comment_required": 0,
      "dictionary_element_id": null,
      "file_id": null,
      "file_url": null,
      "preview_url": null,
      "detail_question": "",
      "question_detail_id": null,
      "extra_question_rate_from": 0,
      "extra_question_rate_to": 10,
      "extra_required": 1,
      "min_choose_extra_variants": null,
      "max_choose_extra_variants": null,
      "variants_with_files": 0,
      "self_variant_text": "",
      "self_variant_placeholder_text": "",
      "variants_element_type": 0,
      "for_all_rates": 1,
      "placeholder_text": "",
      "self_variant_minlength": 0,
      "self_variant_maxlength": 250,
      "text_variant_minlength": 0,
      "text_variant_maxlength": 250,
      "is_self_answer": 0
    },
    {
      "id": 118162,
      "type": 0,
      "variant": "вар 3",
      "description": "",
      "position": 3,
      "points": null,
      "without_points": 0,
      "is_deleted": 0,
      "extra_question": 0,
      "need_extra": 1,
      "comment_required": 0,
      "dictionary_element_id": null,
      "file_id": null,
      "file_url": null,
      "preview_url": null,
      "detail_question": "",
      "question_detail_id": null,
      "extra_question_rate_from": 0,
      "extra_question_rate_to": 10,
      "extra_required": 1,
      "min_choose_extra_variants": null,
      "max_choose_extra_variants": null,
      "variants_with_files": 0,
      "self_variant_text": "",
      "self_variant_placeholder_text": "",
      "variants_element_type": 0,
      "for_all_rates": 1,
      "placeholder_text": "",
      "self_variant_minlength": 0,
      "self_variant_maxlength": 250,
      "text_variant_minlength": 0,
      "text_variant_maxlength": 250,
      "is_self_answer": 0
    },
    {
      "id": 118163,
      "type": 0,
      "variant": "ai_images_6725d0d9f2a56",
      "description": null,
      "position": 4,
      "points": null,
      "without_points": 0,
      "is_deleted": 0,
      "extra_question": 1,
      "need_extra": 0,
      "comment_required": 0,
      "dictionary_element_id": null,
      "file_id": 1465,
      "file_url": "/uploads/1/5d91/89ff325a2d0202639c83e63e8e5d.jpeg",
      "preview_url": "/uploads/preview/1/5d91/89ff325a2d0202639c83e63e8e5d.jpeg",
      "detail_question": "",

      // @NOTE: this field will contain variant id if EXTRA_QUESTION_TYPE === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT
      "question_detail_id": null,
      "extra_question_rate_from": null,
      "extra_question_rate_to": null,
      "extra_required": 1,
      "min_choose_extra_variants": null,
      "max_choose_extra_variants": null,
      "variants_with_files": 0,
      "self_variant_text": "",
      "self_variant_placeholder_text": "",
      "variants_element_type": 1,
      "for_all_rates": 0,
      "placeholder_text": "",
      "self_variant_minlength": 0,
      "self_variant_maxlength": 250,
      "text_variant_minlength": 0,
      "text_variant_maxlength": 250,
      "is_self_answer": 0
    },
    {
      "id": 118164,
      "type": 0,
      "variant": "тихий",
      "description": null,
      "position": 5,
      "points": null,
      "without_points": 0,
      "is_deleted": 0,
      "extra_question": 1,
      "need_extra": 0,
      "comment_required": 0,
      "dictionary_element_id": null,
      "file_id": 1466,
      "file_url": "/uploads/1/0791/188944df409cef2396ac3e8b39b4.jpeg",
      "preview_url": "/uploads/preview/1/0791/188944df409cef2396ac3e8b39b4.jpeg",
      "detail_question": "",
      "question_detail_id": null,
      "extra_question_rate_from": null,
      "extra_question_rate_to": null,
      "extra_required": 1,
      "min_choose_extra_variants": null,
      "max_choose_extra_variants": null,
      "variants_with_files": 0,
      "self_variant_text": "",
      "self_variant_placeholder_text": "",
      "variants_element_type": 1,
      "for_all_rates": 0,
      "placeholder_text": "",
      "self_variant_minlength": 0,
      "self_variant_maxlength": 250,
      "text_variant_minlength": 0,
      "text_variant_maxlength": 250,
      "is_self_answer": 0
    },
    {
      "id": 118165,
      "type": 0,
      "variant": "Indian_Ocean (5)",
      "description": null,
      "position": 6,
      "points": null,
      "without_points": 0,
      "is_deleted": 0,
      "extra_question": 1,
      "need_extra": 0,
      "comment_required": 0,
      "dictionary_element_id": null,
      "file_id": 1467,
      "file_url": "/uploads/1/1478/1a0bf04bca1823336953c71931fc.jpeg",
      "preview_url": "/uploads/preview/1/1478/1a0bf04bca1823336953c71931fc.jpeg",
      "detail_question": "",
      "question_detail_id": null,
      "extra_question_rate_from": null,
      "extra_question_rate_to": null,
      "extra_required": 1,
      "min_choose_extra_variants": null,
      "max_choose_extra_variants": null,
      "variants_with_files": 0,
      "self_variant_text": "",
      "self_variant_placeholder_text": "",
      "variants_element_type": 1,
      "for_all_rates": 0,
      "placeholder_text": "",
      "self_variant_minlength": 0,
      "self_variant_maxlength": 250,
      "text_variant_minlength": 0,
      "text_variant_maxlength": 250,
      "is_self_answer": 0
    }
  ],
  "skip": 0,
  "skip_text": "Skip",
   "translations": {
      "name": "Question name",
      "description": "Question description",
      "description_html": "<p>Question description HTML</p>",
       "messages": {
          "PlaceholderText": "Placeholder"
       },
      "detailLangs": {
        "12768":{
            "question": "Name",
            "placeholder": "Placeholder",
             "description": null,
            "description_html": null,
            "sub_description": null
        }
      },
    },
  "comment_enabled": 0,
  "comment_required": 0,
  "answerText": "Clarifying Question Text",

  // @NOTE: 0 (extra disabled) | 1 (one extra question for all variants) | 2 (the same extra question for each variant) | 3 (different extra question for each variant)
  "extra_question_type": 0,

  "extra_question_rate_from": 1,
  "extra_question_rate_to": 4,

  // NOTE: If it's on then extra variants have media
  "variants_with_files": 1,

  "self_variant_file": {
    "file_id": 1468,
    "file_url": "/uploads/2/08ea/2583315312cd466626c3320ff4b6.jpeg",
    "preview_url": "/uploads/preview/2/08ea/2583315312cd466626c3320ff4b6.jpeg"
  },
}
```

</server-data-example>
<file-list>

Vue Components (UI):

• src/entities/question/ui/NpsRatingQuestion.vue: This is the main component for the NPS Rating
question, and where you'll need to integrate the VariantsController.
• src/entities/question/ui/StarRatingVariantsQuestion.vue: The task description explicitly
mentions this as a reference for existing implementations of VariantsController, especially for
UI elements and answer types.
• src/entities/question/ui/Variants.vue: This component is mentioned as needing to be added to
NpsQuestion.vue and will likely be the main container for the VariantsController UI.
• src/entities/question/ui/VariantsGallery.vue (Not yet implemented, but mentioned in the task):
You'll need to create this component for the gallery view of variants.
• src/entities/question/ui/VariantsGalleryItem.vue (Not yet implemented, but mentioned in the
task): You'll need to create this component for individual items in the VariantsGallery.
• src/shared/ui/Gallery/Gallery.vue: Mentioned as an example for initializing Swiper in
VariantsGallery.vue.
• src/shared/ui/FilePreview.vue: Mentioned as a reference for the look and styles of
VariantsGalleryItem.vue.
• src/shared/ui/FileCheck.vue: Mentioned as a reference for props in VariantsGalleryItem.vue
(specifically for the checkbox).
• src/shared/ui/Command/CommandContent.vue: Mentioned as a reference for gradient shadows in
VariantsGallery.vue.

Controllers and Models:

• src/entities/question/controllers/VariantsController.js: This is the controller you'll be working
with and likely need to adapt for the NPS question type.
• src/entities/question/model/StarsVariantsQuestionModel.js: Likely contains the data structure
and logic for variants in Star Rating questions, which can be a good reference for NPS variants.
• src/entities/question/model/NpsRatingQuestionModel.js: The model for the NPS Rating question,
which you'll need to modify to incorporate the VariantsController.
• src/entities/question/model/BaseQuestion.js: The base class for all question models, might be
relevant for understanding common question logic.

Constants and Helpers:

• src/shared/constants/index.js: You'll need to add EXTRA_QUESTION_TYPE constants here.
• src/shared/helpers/string.js: Contains declOfNum which is used in VariantsController.js and
might be needed.
</file-list>
