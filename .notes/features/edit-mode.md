Implementation Plan (Frontend - Focused Points):

 1 Frontend Route and Parameter Handling:
    * Goal:  Enable the frontend to recognize and handle edit=1 and view=N URL parameters to initiate "edit" mode and
      navigate to a specific question.
    * Actions:
       * Update routing to recognize the edit parameter.
       * Implement logic to read and store the edit and view parameters upon initial load.
       * Pass parameters to `fetchPoll`
    * Likely files to edit:
       * src/app/App.vue (for initial parameter parsing and potentially setting up the edit mode state)
       * src/entities/poll/model/store.js (to store and manage the isEditMode state and potentially the view parameter)
 2 Update Poll Store (src/entities/poll/model/store.js):
    * Goal: Modify the Poll Store to manage the "edit" mode state, adjust data fetching for editing, and handle answer
      updates in "edit" mode.
    * Actions:
       * Add a reactive state variable isEditMode (e.g., ref(false)) to track if the questionnaire is in edit mode.
       * In fetchPoll:
          * Adjust the API endpoint URL or add parameters to fetchPollData to indicate "edit" mode to the
            backend. (ADD `edit: 1` and `view: N` to `p/answer` call if those params are present)
          * Set isEditMode to true if the edit=1 parameter is present.
       * If edit mode is enabled and view question is set, find apropriate page to view and use setPageIndex
       * Make sure question in edit mode is always visible. It should not be hidden by view logic or by hidden recipient logic
       * In goNext:
          * When isEditMode is true, ensure saveAnswer API calls are correctly updating existing answers. Pass `editMode: true` to saveAnswer
    * Likely files to edit:
       * src/entities/poll/model/store.js
 3 Update API Calls (src/entities/poll/api/index.js):
    * Goal: Modify the API calls to support fetching data for editing and correctly updating answers in "edit" mode.
    * Actions:
       * fetchPollData:
          * Modify the fetchPollData function to include a parameters `edit: 1` and `view: N` to be passed to the backend if needed
       * saveAnswer:
          * Ensure the saveAnswer function correctly sends updates to existing answers by making sure `edit: 1` is sent when needed.
    * Likely files to edit:
       * src/entities/poll/api/index.js

