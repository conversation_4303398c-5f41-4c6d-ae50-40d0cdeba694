Business Goal

Implement a simplified version of the survey.

Simplified Version

• Access the simplified version by adding &simple=1 to the survey URL.
<edit-instructions>

- src/shared/store/useSimplifiedStore.ts - Create new store for simplified mode state management. Add URL parameter check and simplified mode initialization

Implementation Notes:

useSimplifiedStore.ts:

```typescript
// Key features to implement:
- isSimplifiedMode: boolean
- toggleSimplifiedMode(): void
- initializeSimplifiedMode(): void
- Persist simplified mode state
- Handle URL parameter sync
```

</edit-instructions>

style.css:

```css
// Add CSS variables for simplified mode:
:root {
  --fqz-poll-simplified-max-width: 680px;
  --fqz-poll-simplified-mobile-breakpoint: 766px;
}

// Colors in simplified mode should use:
// Background: var(--fqz-poll-main-place-color)
// Text: var(--fqz-poll-text-on-place)
```

App.vue:

```css
// Add conditional max-width classes:
.poll-content {
  width: 100%;
}

.poll-content--simplified {
  max-width: var(--fqz-poll-simplified-max-width);
  margin-left: auto;
  margin-right: auto;
  background-color: var(--fqz-poll-main-place-color);
  color: var(--fqz-poll-text-on-place);
}
```

Functionality is similar to the standard version.

Design

• Background: Use the main background image if available; otherwise, no background. The background
color is taken from the "Main background color" design setting.
<edit-instructions>

- src/app/App.vue - Update background handling
- src/shared/helpers/design.js - Update design settings handling
  </edit-instructions>

• Header: Hidden in the simplified version.
<edit-instructions>

- src/app/App.vue - Update header visibility logic
- src/widgets/ui/Header.vue - Add simplified mode check
  </edit-instructions>

• Test Mode Label: Hidden.
<edit-instructions>

- src/widgets/ui/TestModeLabel.vue - Add simplified mode check
  </edit-instructions>

• Progress Bar/Page Navigation: Initially hidden, but may be enabled later via design settings.
<edit-instructions>

- src/app/App.vue - Update progress bar visibility logic
- src/widgets/ui/PollPaginator.vue - Add simplified mode check
  </edit-instructions>

• Content Styling:

-• Question Text: Color is taken from the "Text color on place" design setting.
-• Additional Description: Color is taken from the "Text color on place" design setting.
-• Optional Label: Color is taken from the "Text color on place" design setting.
<edit-instructions>

- src/widgets/ui/QuestionItem.vue - Update text color handling
- src/shared/helpers/design.js - Update color variables
  </edit-instructions>

• Controls: Same styles as the standard version.
• Buttons: Same styles as the standard version. The "Back" button is always hidden.
<edit-instructions>

- src/widgets/ui/PollActions.vue - Update back button visibility
  </edit-instructions>

• Error Texts: Same styles as the standard version.
• Maximum Content Width: 680px.
• Mobile Version: Display the mobile version starting at 766px device width.
<edit-instructions>

- src/app/App.vue - Add max-width and media query styles
- src/app/style.css - Add CSS variables for simplified mode
  </edit-instructions>

-• Dropdowns/Calendars: Adjust dropdown position to fit within the window height, centering it
-vertically relative to the select element.
<edit-instructions>

- src/shared/ui/Select/FcSelect.vue - Update dropdown positioning
- src/shared/ui/DatePicker/DatePicker.vue - Update calendar positioning

To update dropdown positioning, we should pass special prop to Popover and PopoverContent components it uses radix-vue library.
</edit-instructions>

Survey Settings

• Respondent Consent: Shown in the simplified version.
• "Created in Foquz" Link: Hidden in the simplified version. -

<edit-instructions>
- src/widgets/ui/Footer.vue - Update footer visibility based on simplified mode -

• Points Functionality: Included in the simplified version.

"foquz" Label

• Display based on API response.
• Position in the top left corner of the screen.
• Non-clickable and always visible.

<edit-instructions>
- src/shared/ui/FoquzLabel.vue - Create new component for simplified mode label

-Implementation notes:

// Key features:

- Non-clickable display
- Fixed positioning in top left
- Minimal styling
- Show/hide based on API response
  </edit-instructions>

- Multilingual Support
- -• The simplified version supports multiple languages. -<edit-instructions>
  -- src/shared/store/useSimplifiedStore.ts - Add language support handling -</edit-instructions>

- Address Field
- -• Refer to the standard version description.
  -• Consider multilingual support.
  -• Verify mobile display. -<edit-instructions>
  -- src/entities/question/model/AddressQuestionModel.js - Verify multilingual support
  -- src/entities/question/ui/AddressQuestion.vue - Update mobile styles -</edit-instructions>
