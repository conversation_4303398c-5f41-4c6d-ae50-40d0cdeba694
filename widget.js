import api from "./src/api.js";
import initializeWidget from "./src/components/core/widget/index.js";

async function enableMocking() {
  if (process.env.NODE_ENV !== "development") {
    return;
  }

  const { worker } = await import("./mocks/browser");

  // `worker.start()` returns a Promise that resolves
  // once the Service Worker is up and ready to intercept requests.
  return worker.start();
}

(async () => {
  // Ensure the FOQUZ_SDK object exists
  if (
    !window.FOQUZ_SDK ||
    !window.FOQUZ_SDK.widget ||
    !window.FOQUZ_SDK.widget.widget_code
  ) {
    console.error("FOQUZ_SDK is not properly configured.");
    return;
  }

  // Configuration and Constants
  var ASSETS_ROOT_URL = "https://foquz.ru/";
  var ROOT_URL = "https://widget.foquz.ru/";

  var config = {
    root: window.FOQUZ_SDK?._test?.root || ROOT_URL,
    assetsRoot: window.FOQUZ_SDK?._test?.assetsRoot || ASSETS_ROOT_URL,
    id_parent_element: window.FOQUZ_SDK?.widget?.id_parent_element,
    transitionDuration: 500,
    additionalParams: window.FOQUZ_SDK?.params || {},
    pollLoadDelay: window.FOQUZ_SDK?._test?.pollLoadDelay || 0,
    widgetCode: window.FOQUZ_SDK?.widget?.widget_code,
  };

  const useCustomScrollbar = window.FOQUZ_SDK?.widget?.useCustomScrollbar;

  config.useCustomScrollbar =
    useCustomScrollbar !== undefined ? useCustomScrollbar : true;

  config.stylesPath =
    window.FOQUZ_SDK._test?.stylesPath ||
    `${config.assetsRoot}widgets/dom/styles.css`;

  // Extract the widget code from the FOQUZ_SDK object
  var widgetCode = window.FOQUZ_SDK.widget.widget_code;

  // Initialization
  const init = async () => {
    try {
      const shouldEnableApiMock =
        import.meta.env.VITE_ENABLE_API_MOCK === "true";

      if (shouldEnableApiMock) {
        await enableMocking();
      }

      // Determine if the device is mobile based on screen width
      const isMobile = window.screen.width < 600 ? 1 : 0;

      let [widgetData, initCustomScrollbar] = await Promise.all([
        api.fetchWidgetData(
          window.location.href,
          widgetCode,
          document.cookie,
          config.root,
          config.additionalParams,
          null,
          isMobile,
        ),
        config.useCustomScrollbar
          ? import("@/helpers/initCustomScrollbar").catch((error) => {
              console.error("Failed to load custom scrollbar library:", error);
              return null;
            })
          : Promise.resolve(null),
      ]);
      if (initCustomScrollbar?.default) {
        initCustomScrollbar = initCustomScrollbar.default;
      }

      // Initialize widget without pollData

      initializeWidget(widgetData, config, initCustomScrollbar);
    } catch (error) {
      console.error("Failed to fetch widget data:", error);
    }
  };

  // Initialize widget
  init();
})();
