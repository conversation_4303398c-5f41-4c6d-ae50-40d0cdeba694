
# Gemini Codebase Analysis

## Project Overview

This project is a web application built with Vue.js for creating and managing polls or surveys. It appears to feature a modular architecture with a clear separation of concerns, including entities for polls and questions, features like custom themes, and a dedicated section for UI components.

## Technologies

- **Framework**: Vue.js
- **Build Tool**: Vite
- **Language**: TypeScript
- **Styling**: Tailwind CSS, SASS
- **State Management**: Pinia
- **Testing**: Playwright for end-to-end testing
- **Linting**: ESLint
- **API Mocking**: Mock Service Worker (MSW)

## Key Scripts

- `npm run dev`: Starts the development server.
- `npm run build`: Builds the application for production.
- `npm run test`: Runs the Playwright end-to-end tests.
- `npm run lint`: Lints the source code.
- `npm run type-check`: Performs a static type check using `vue-tsc`.

### Points System

The application includes a points system feature, which is enabled via a server-side flag. This feature allows for scoring within polls. The `pointsStore` (a Pinia store) manages the state, fetching points data and interpretation ranges from the server. The `useVariablesReplacer` composable then displays the points to the user by replacing placeholders in the text with the scored points.

## Directory Structure

- `src/`: Contains the main application source code.
  - `app/`: Core application setup, including the main Vue component and entry point.
  - `entities/`: Business logic entities, such as `poll` and `question`.
  - `features/`: Distinct application features.
  - `shared/`: Reusable components, helpers, and utilities.
  - `widgets/`: Larger, composite UI components.
- `tests/e2e/`: End-to-end tests written with Playwright.
- `mocks/`: Mock data and handlers for API mocking with MSW.
- `public/`: Static assets that are publicly accessible.
