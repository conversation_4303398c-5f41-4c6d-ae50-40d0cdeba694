{"compilerOptions": {"target": "ES2020", "jsx": "preserve", "lib": ["ES2020", "DOM", "DOM.Iterable"], "useDefineForClassFields": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["./src/*"], "@shared/*": ["./src/shared/*"], "@entities/*": ["./src/entities/*"], "@features/*": ["./src/features/*"], "@widgets/*": ["./src/widgets/*"]}, "resolveJsonModule": true, "allowImportingTsExtensions": true, "allowJs": true, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noEmit": true, "isolatedModules": true, "skipLibCheck": true}, "references": [{"path": "./tsconfig.node.json"}], "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.js", "src/**/*.jsx"]}