# Tech Stack

## Core Technologies

- **Vue 3** - Frontend framework with Composition API
- **Vite** - Build tool and dev server
- **TypeScript** - Gradual migration in progress (strict mode enabled)
- **Pinia** - State management
- **SCSS/CSS** - Styling with preprocessor support

## Key Libraries

- **Radix Vue** - Headless UI components
- **Tailwind CSS** - Utility-first CSS framework
- **Zod** - Schema validation
- **Lodash** - Utility functions (debounce, groupBy, merge, etc.)
- **Swiper** - Touch slider component
- **Fancybox** - Lightbox/modal library
- **Maskito** - Input masking
- **VueUse** - Vue composition utilities

## Development Tools

- **ESLint** with @antfu/eslint-config
- **Playwright** - E2E testing
- **MSW** - API mocking for development/testing
- **Vue DevTools** - Development debugging

## Common Commands

### Development

```bash
npm run dev              # Start dev server
npm run dev:local        # Dev with local environment
npm run dev:test         # Test environment on port 5178
```

### Building

```bash
npm run build            # Production build with type checking
npm run build:production # Explicit production build
npm run preview          # Preview production build
```

### Code Quality

```bash
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues
npm run type-check       # TypeScript type checking
npm run type-check:watch # Watch mode type checking
```

### Testing

```bash
npm run test             # Run Playwright E2E tests
```

## Environment Configuration

- Multiple environment files (.env.development, .env.production, etc.)
- Conditional compilation with preprocessor directives
- Sentry integration (optional)
- API mocking can be enabled via environment variables or URL params
