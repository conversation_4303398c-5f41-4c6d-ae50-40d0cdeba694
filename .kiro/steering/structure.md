# Project Structure

This project follows **Feature Sliced Design (FSD)** architecture for clean separation of concerns and scalability.

## FSD Layer Structure

```
src/
├── app/           # Application initialization and global setup
├── widgets/       # Complex UI compositions using multiple entities/features
├── features/      # Self-contained product features
├── entities/      # Business logic and domain models
└── shared/        # Reusable utilities, components, and constants
```

## Layer Rules

### `app/` - Application Layer
- Application initialization (`main.js`)
- Global styles and configuration
- Root component (`App.vue`)

### `widgets/` - Widget Layer
- Complex UI components that use stores and business logic
- Components combining multiple entities/features
- Examples: `Header`, `Footer`, `PollContent`, `QuestionsPage`
- **Rule**: If component uses stores (`usePollStore`, `useTranslationsStore`), place in `widgets/`

### `features/` - Feature Layer
- Self-contained product features with their own:
  - Models and business logic
  - API integration
  - State management (stores)
  - UI components
  - Types and schemas
- Examples: `points/`, `custom-themes/`

### `entities/` - Entity Layer
- Core business entities and domain models
- Controllers for business logic
- API integration for specific entities
- Examples: `poll/`, `question/`

### `shared/` - Shared Layer
- **`shared/ui/`** - Pure UI components without business logic
- **`shared/api/`** - Common API utilities
- **`shared/constants/`** - Application constants
- **`shared/helpers/`** - Utility functions
- **`shared/store/`** - Global stores
- **`shared/composables/`** - Reusable Vue composables

## Path Aliases

```javascript
@/          -> ./src/
@shared/    -> ./src/shared/
@entities/  -> ./src/entities/
@features/  -> ./src/features/
@widgets/   -> ./src/widgets/
```

## File Organization Patterns

### Component Structure
- Each feature/entity has its own `ui/` folder for components
- Controllers go in `controllers/` folder
- Models in `model/` folder
- API functions in `api/` folder

### TypeScript Migration
- New files should use `.ts` extension or `<script lang="ts">` in Vue files
- Existing `.js` files can be gradually migrated
- Strict TypeScript configuration is enabled

### Testing
- E2E tests in `tests/e2e/`
- Test utilities in `tests/e2e/testUtils.js`
- Mock data in `mocks/data/`
- API handlers in `mocks/handlers/`

## Key Directories

- `public/` - Static assets (disabled in production)
- `mocks/` - MSW API mocking setup
- `docs/` - Project documentation
- `images/` - Docker configuration
- `.kiro/` - Kiro AI assistant configuration