# Design Document

## Overview

The file upload composable will extract all file upload and screenshot functionality from the VariantsController into a reusable Vue 3 composable. This composable will provide a clean API for managing file attachments, screenshots, validation, and error handling while maintaining full TypeScript support and reactive state management.

## Architecture

### Composable Structure

The composable will follow the standard Vue 3 composition API pattern:

```typescript
// useFileUpload.ts
export function useFileUpload(options: FileUploadOptions) {
  // Reactive state
  // Computed properties
  // Methods
  // Return public API
}
```

### Key Components

1. **State Management**: Reactive refs and computed properties for files, screenshots, errors, and loading states
2. **Validation Layer**: File type, size, and count validation with configurable limits
3. **Error Handling**: Centralized error management with temporary error display
4. **Translation Support**: Integration with translation system for internationalized messages
5. **Type Safety**: Full TypeScript support with proper interfaces and type definitions

## Components and Interfaces

### Core Types

```typescript
interface FileUploadOptions {
  maxFiles: number
  filesizeLimit: number // in MB
  allowedFileTypes?: string[]
  translationFunction: (key: string, params?: Record<string, any>) => ComputedRef<string>
  questionScreenshot?: QuestionScreenshotConfig | null
}

interface QuestionScreenshotConfig {
  make_screenshot_enabled: number
  upload_enabled: number
  max_files: number
  description?: string
  button_text?: string
  screenshot_button_text?: string
}

interface FileAttachment {
  id: string
  type: 'file' | 'screenshot'
  name: string
  file?: File
  isUploading: boolean
  previewUrl?: string | null
  fullUrl?: string | null
  timestamp?: number
  html?: string
  selection?: any
  client?: any
  isMobile?: boolean
}

interface ScreenshotData {
  timestamp: number
  html: string
  selection: any
  client: any
  isMobile: boolean
}

interface FileUploadReturn {
  // State
  files: Ref<FileAttachment[]>
  screenshots: Ref<FileAttachment[]>
  allAttachments: ComputedRef<FileAttachment[]>
  error: Ref<string | null>
  errorKind: Ref<string>

  // Computed
  isMaxFilesReached: ComputedRef<boolean>
  shouldShowUploadButton: ComputedRef<boolean>
  shouldShowScreenshotButton: ComputedRef<boolean>

  // Methods
  uploadFiles: (files: File[]) => Promise<void>
  addScreenshot: (screenshotData: ScreenshotData) => void
  removeAttachment: (index: number) => void
  clearError: () => void
  getAttachmentData: () => AttachmentData

  // Validation methods
  isFileTypeAllowed: (file: File) => boolean
  getFileType: (file: File) => FileType | null
}

interface AttachmentData {
  screenshots: Array<{
    html: string
    timestamp: number
    selection: any
    client: any
    isMobile: boolean
  }>
  files: Array<{
    name: string
    file: File
    previewUrl?: string | null
    fullUrl?: string | null
  }>
}
```

### Composable Implementation Structure

```typescript
export function useFileUpload(options: FileUploadOptions): FileUploadReturn {
  // 1. Reactive State
  const files = ref<FileAttachment[]>([])
  const screenshots = ref<FileAttachment[]>([])
  const error = ref<string | null>(null)
  const errorKind = ref<string>('default')

  // 2. Computed Properties
  const allAttachments = computed(() => [...files.value, ...screenshots.value])
  const isMaxFilesReached = computed(() => allAttachments.value.length >= options.maxFiles)

  // 3. Validation Methods
  const isFileTypeAllowed = (file: File) => { /* implementation */ }
  const getFileType = (file: File) => { /* implementation */ }

  // 4. Core Methods
  const uploadFiles = async (filesToUpload: File[]) => { /* implementation */ }
  const addScreenshot = (screenshotData: ScreenshotData) => { /* implementation */ }
  const removeAttachment = (index: number) => { /* implementation */ }

  // 5. Error Management
  const showTemporaryError = (message: string, kind = 'default') => { /* implementation */ }
  const clearError = () => { error.value = null }

  // 6. Return Public API
  return {
    files: readonly(files),
    screenshots: readonly(screenshots),
    allAttachments,
    error: readonly(error),
    errorKind: readonly(errorKind),
    isMaxFilesReached,
    shouldShowUploadButton,
    shouldShowScreenshotButton,
    uploadFiles,
    addScreenshot,
    removeAttachment,
    clearError,
    getAttachmentData,
    isFileTypeAllowed,
    getFileType
  }
}
```

## Data Models

### File Attachment Model

The composable will use a unified attachment model that can represent both uploaded files and screenshots:

```typescript
interface FileAttachment {
  id: string // Unique identifier
  type: 'file' | 'screenshot' // Attachment type
  name: string // Display name
  file?: File // Original File object (for files)
  isUploading: boolean // Upload status
  previewUrl?: string | null // Preview/thumbnail URL
  fullUrl?: string | null // Full resolution URL

  // Screenshot-specific fields
  timestamp?: number // Screenshot timestamp
  html?: string // Screenshot HTML content
  selection?: any // Screenshot selection data
  client?: any // Client information
  isMobile?: boolean // Mobile device flag
}
```

### Configuration Model

```typescript
interface FileUploadOptions {
  maxFiles: number // Maximum number of files
  filesizeLimit: number // File size limit in MB
  allowedFileTypes?: string[] // Override default allowed types
  translationFunction: TranslationFn // Translation function
  questionScreenshot?: QuestionScreenshotConfig | null
}
```

## Error Handling

### Error Types and Messages

The composable will handle several types of errors:

1. **File Type Errors**: When uploaded files have disallowed extensions
2. **File Size Errors**: When files exceed the size limit
3. **File Count Errors**: When trying to upload more files than allowed
4. **Upload Errors**: When the upload process fails
5. **Screenshot Errors**: When screenshot capture fails

### Error Display Strategy

- Errors will be stored in reactive state and displayed in the UI
- Temporary errors will auto-clear after 3 seconds
- Different error types can have different display durations
- Error messages will be translatable

### Error Recovery

- Users can dismiss errors manually
- Failed uploads can be retried
- Invalid files are filtered out, valid files continue processing

## Testing Strategy

### Unit Tests

1. **State Management Tests**

   - Test reactive state updates
   - Test computed property calculations
   - Test state initialization

2. **Validation Tests**

   - Test file type validation with various extensions
   - Test file size validation with different file sizes
   - Test file count limits

3. **Error Handling Tests**

   - Test error message generation
   - Test temporary error clearing
   - Test error state management

4. **File Management Tests**
   - Test file addition and removal
   - Test screenshot handling
   - Test attachment data retrieval

### Integration Tests

1. **Component Integration**

   - Test composable usage in Vue components
   - Test reactive updates in UI
   - Test event handling

2. **Translation Integration**
   - Test translated error messages
   - Test translation function integration

### Mock Strategy

- Mock File objects for testing
- Mock translation functions
- Mock upload API calls
- Mock screenshot data

## Migration Strategy

### Phase 1: Create Composable

- Implement the composable with full functionality
- Add comprehensive tests
- Ensure TypeScript support

### Phase 2: Update VariantsController

- Integrate composable into VariantsController
- Remove duplicate file upload code
- Maintain backward compatibility

### Phase 3: Update Components

- Update Variants.vue to use new composable methods
- Update VariantsFileAttachment.vue if needed
- Test all file upload workflows

### Phase 4: Cleanup

- Remove unused methods from VariantsController
- Update documentation
- Verify all tests pass

## Performance Considerations

### Memory Management

- Use readonly refs for exposed state to prevent external mutations
- Properly clean up file URLs created with URL.createObjectURL()
- Limit the number of concurrent uploads

### Reactivity Optimization

- Use shallowRef for complex objects when deep reactivity isn't needed
- Minimize computed property dependencies
- Use proper Vue 3 reactivity patterns

### File Handling

- Validate files before processing to avoid unnecessary work
- Process files in batches if needed
- Provide upload progress feedback where possible

## Security Considerations

### File Validation

- Always validate file types on both client and server
- Check file sizes to prevent large uploads
- Sanitize file names and metadata

### Content Security

- Use proper MIME type checking
- Validate file headers, not just extensions
- Implement proper error handling to avoid information leakage

### Upload Security

- Implement proper authentication for upload endpoints
- Use secure upload URLs
- Validate uploaded content on the server side
