# Copilot Commit Message Instructions

## Format
All commit messages should follow this format:

```
#TASK-NUMBER: Фронтенд. {description in Russian (brief one)}
```

## Examples
- `#TASK-5238: Фронтенд. Добавлена поддержка переводов для виджета загрузки файлов`
- `#TASK-1234: Фронтенд. Исправлена ошибка валидации формы`
- `#TASK-5678: Фронтенд. Обновлен дизайн кнопок в опросах`

## Guidelines
- Always start with `#TASK-NUMBER:`
- Follow with `Фронтенд.` (Frontend in Russian)
- Keep description brief and in Russian
- Use past tense for completed actions
- Focus on what was changed, not how it was changed