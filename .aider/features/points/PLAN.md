<business_goal>Бизнес-цель</business_goal>
<functionality>

<title>Функционал с баллами</title>
<item step="1">Отображение баллов на конечном экране</item>
<item step="2">Отчёт о тестировании</item>
<item step="3">Печатная версия отчёта о тестировании</item>

<important>
Баллы можно указать только для 5 типов вопросов:

    Варианты ответов
    Дата/время
    Приоритет
    Выбор изображения/видео
    Простая матрица
</important>
</functionality>

<task-1>
    <description>
    В рамках задачи нужно реализовать функционал опроса с баллами и сделать отображение баллов на конечном экране.
    Для типа вопроса Промежуточный блок / Конечный экран для опроса с баллами появляются переменные:
        <variables>
            <title>Переменные</title>
            <link>VARIABLES_LINK_PLACEHOLDER</link>
            <variable>Общее кол-во баллов</variable>
            <variable>Набранное кол-во баллов</variable>
            <variable>Набранное кол-во баллов в %</variable>
            <variable>Интерпретация баллов: Результат</variable>
            <variable>Интерпретация баллов: Описание результата</variable>
        </variables>
        <option>
        <text>Опция Кнопка «Отчет о тестировании»</text>
        </option>
        <link>ITEM_4_LINK_PLACEHOLDER</link>
        </item>
        </general>
        <text>Значения переменных по количеству баллов считает бэк:</text>
        <variable>Общее кол-во баллов</variable>
        <variable>Набранное кол-во баллов</variable>
        <variable>Набранное кол-во баллов в %</variable>
        </item>
        <item>
        <text>Пока бэк считает, то можно пройти время, поэтому вместо значения для каждой переменной нужно отображать лоадер.</text>
        <text>Размер лоадера должен зависеть от размера текста (по аналогии с кнопкой Копировать в буфер, задача #4392).</text>
        <text>Цвет кнопки из настроек дизайна Цвет текста на фоне</text>
        </item>
        <item>
        <text>Значения переменных для Интерпретации баллов берётся из настроек вопроса.</text>
        <text>Нужно понять в какой диапазон попадают баллы, и в зависимости от этого показывать значения переменных Интерпретации баллов: Результат и Интерпретации баллов: Описание результата.</text>
    </description>
    <implementation-steps>
    THIS FEATURE SHOUL BE IMPLEMENTED IN src/features/points/* (follow Featured-Sliced-Design pattern)

    1. implement pointsStore.js: Add flag isEnabled: "polLData.poll.point_system". Add export of isEnabled. Add pointsInterpretationRanges ref (pollData.variables.scoresInterpretationRanges):
            "scoresInterpretationRanges": [
                {
                    "id": 1386,
                    "foquz_poll_id": 52128,
                    "min": 1,
                    "max": 2,
                    "result": "нормально",
                    "description": "надо немного потренироваться",
                    "position": 0
                },
                {
                    "id": 1387,
                    "foquz_poll_id": 52128,
                    "min": 3,
                    "max": 4,
                    "result": "отлично!",
                    "description": "вы справились на отлично",
                    "position": 1
                }
            ],
    2. Add calculatePoints function to pointsStore.js
    3. This store should be initialized in store.js
    4. points will be fetched from /p/get-points endpoint (in points/api/index.js). It accepts authKey and returns this:
    ```
    {
    "questions": [
        {
        "questionId": 170106,
        "points": 1,
        "maxPoints": 2
        },
        {
        "questionId": 170107,
        "points": 0,
        "maxPoints": 1
        },
        {
        "questionId": 170114,
        "points": 0,
        "maxPoints": 1
        },
        {
        "questionId": 170117,
        "points": 0,
        "maxPoints": 1
        },
        {
        "questionId": 170121,
        "points": 0,
        "maxPoints": 3
        }
    ],
    "points": 1,
    "maxPoints": 8
    }
    ```
    Add `pointsData` ref to pointsStore.js
    Implement `fetchPoints` function to pointsStore.js
    Implement `calculatePoints` function to pointsStore.js
    Implement `currentPointsInterpretation` computed to pointsStore.js

    5. Update interBlockModel and interBlock.vue. add refs:
        scores_button	1
        scores_button_text
        if pointsStore.isEnabled and type of interBlock is intermediate or end:
            add scores_button and scores_button_text to the start of the buttons
        DO NOT HANDLE CLICK ON BUTTON YET

    6. Update store.addVariables function with points: {enabled, isLoading, currentPointsInterpretation}:
    7. Update BasequestionModel if needed

    THE STEPS ABOVE ARE ALREADY IMPLEMENTED. FOLLOW INSTRUCTIONS BELOW.
    </implementation-steps>

</task-1>
