#!/bin/bash

# Скрипт сборки и деплоя для foquz-widget-dom внутрь ядра (foquz-core)
# Собирает проект и перемещает файлы в директорию foquz-core web/widgets/dom/
# Поддерживает параметры командной строки --source-dir и --target-dir

set -e

# Значения по умолчанию для путей
DEFAULT_SOURCE_DIR="$(pwd)"
DEFAULT_TARGET_DIR="$HOME/projects/foquz-repositories/foquz-core/web/widgets/dom"

# Функция отображения справки
show_help() {
    echo "Использование: $0 [ОПЦИИ]"
    echo ""
    echo "Опции:"
    echo "  --source-dir DIR    Исходная директория проекта (по умолчанию: текущая директория)"
    echo "  --target-dir DIR    Целевая директория для деплоя (по умолчанию: $DEFAULT_TARGET_DIR)"
    echo "  -h, --help          Показать эту справку"
    echo ""
    echo "Примеры:"
    echo "  $0"
    echo "  $0 --source-dir /path/to/source --target-dir /path/to/target"
}

# Инициализация переменных значениями по умолчанию
SOURCE_DIR="$DEFAULT_SOURCE_DIR"
TARGET_DIR="$DEFAULT_TARGET_DIR"

# Парсинг аргументов командной строки
while [[ $# -gt 0 ]]; do
    case $1 in
        --source-dir)
            SOURCE_DIR="$2"
            shift 2
            ;;
        --target-dir)
            TARGET_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Неизвестная опция: $1"
            show_help
            exit 1
            ;;
    esac
done

# Переменная для директории сборки
BUILD_DIR="$SOURCE_DIR/dist"

# Проверка существования исходной директории
if [[ ! -d "$SOURCE_DIR" ]]; then
    echo "Ошибка: Исходная директория не существует: $SOURCE_DIR"
    exit 1
fi

# Создание целевой директории если она не существует
mkdir -p "$TARGET_DIR"

echo "Исходная директория: $SOURCE_DIR"
echo "Целевая директория: $TARGET_DIR"
echo ""

echo "Сборка foquz-widget-dom..."
cd "$SOURCE_DIR"
npm run build

echo "Удаление старых файлов из целевой директории..."
rm -rf "$TARGET_DIR"/*

echo "Перемещение собранных файлов в целевую директорию..."
if [[ -d "$BUILD_DIR" ]] && [[ "$(ls -A "$BUILD_DIR" 2>/dev/null)" ]]; then
    mv "$BUILD_DIR"/* "$TARGET_DIR"/
    echo "Деплой успешно завершен!"
else
    echo "Ошибка: Директория сборки пуста или не существует: $BUILD_DIR"
    exit 1
fi
