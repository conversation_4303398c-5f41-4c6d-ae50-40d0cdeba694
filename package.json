{"name": "foquz-widget-dom", "description": "FOQUZ Widget (DOM Version) which is inserted directly into the DOM for use in any website with improved performance.", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:test": "vite dev --mode test --port 3002", "build": "vite build", "preview": "vite preview", "lint:js": "eslint . --fix", "lint:css": "stylelint '**/*.{css,scss,less}' --fix", "lint": "npm run lint:js && npm run lint:css", "size": "size-limit"}, "devDependencies": {"@eslint/js": "^9.3.0", "@faker-js/faker": "^8.4.1", "@playwright/test": "^1.44.1", "@size-limit/file": "^11.2.0", "@types/node": "^20.14.1", "eslint": "^9.3.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "globals": "^15.2.0", "lightningcss": "^1.25.0", "msw": "^2.3.1", "playwright": "^1.44.0", "prettier-eslint": "^16.3.0", "size-limit": "^11.2.0", "stylelint": "^16.5.0", "stylelint-config-standard": "^36.0.0", "vite": "^5.2.10"}, "dependencies": {"@foquz/foquz-screenshot": "^1.0.26", "@maskito/core": "^2.3.2", "@maskito/kit": "^2.3.2", "lodash.get": "^4.4.2", "lodash.merge": "^4.6.2", "lodash.shuffle": "^4.2.0", "nanoid": "^5.1.5", "resize-observer-polyfill": "^1.5.1", "simplebar": "^6.2.7", "vanjs-core": "^1.5.0", "vanjs-ext": "^0.6.0"}, "msw": {"workerDirectory": ["public"]}, "size-limit": [{"path": "dist/widget.js", "limit": "63 KB", "gzip": true}]}