{"name": "poll-vue-app", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "npx vite dev", "dev:local": "npx vite dev --mode development-local", "dev:test": "NODE_ENV=test npx vite dev --mode test --port 5178", "build": "vue-tsc && npx vite build", "build:production": "NODE_ENV=production npx vite build", "preview": "npx vite preview", "lint": "npx eslint src", "lint:fix": "npx eslint src --fix", "test": "npx playwright test", "type-check": "vue-tsc --noEmit", "type-check:watch": "vue-tsc --noEmit --watch"}, "dependencies": {"@fancyapps/ui": "^5.0.36", "@internationalized/date": "^3.5.5", "@maskito/core": "^3.0.0", "@maskito/kit": "^3.0.0", "@maskito/vue": "^3.0.0", "@sentry/vite-plugin": "^2.22.6", "@sentry/vue": "^8.27.0", "@unhead/vue": "^1.11.7", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^10.11.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "event-emitter": "^0.3.5", "fetch-jsonp": "^1.3.0", "install": "^0.13.0", "lodash.debounce": "^4.0.8", "lodash.groupby": "^4.6.0", "lodash.kebabcase": "^4.1.1", "lodash.merge": "^4.6.2", "lodash.pick": "^4.4.0", "lodash.shuffle": "^4.2.0", "lodash.throttle": "^4.1.1", "lucide-vue-next": "^0.424.0", "nanoid": "^5.1.5", "npm": "^10.8.2", "pinia": "^2.1.7", "radix-vue": "^1.9.4", "simplebar-vue": "^2.3.5", "swiper": "^11.1.12", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "unplugin-preprocessor-directives": "^1.0.3", "vite": "^5.3.1", "vue": "^3.4.29", "vue-draggable-plus": "^0.5.3", "zod": "^3.24.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}, "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@playwright/test": "^1.47.0", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.10.5", "@vue/tsconfig": "^0.7.0", "eslint": "^9.10.0", "eslint-plugin-format": "^0.1.2", "msw": "^2.4.3", "postcss-safe-important": "^2.0.1", "sass": "^1.86.0", "typescript": "^5.7.2", "vite-plugin-vue-devtools": "^7.6.1", "vue-tsc": "^2.2.0"}, "msw": {"workerDirectory": ["public"]}}