stages:
  - prebuild
  - build
  - deploy
  - main
  - test
  - update_snapshots

variables:
  TOKEN_TESTS_SCREENSHOTS: $token_tests_screenshots
  TAG: latest




main:
  stage: main
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_PIPELINE_SOURCE == 'api'
  tags:
    - devfoquz 
  script: 
    - nvm use 20
    - npm install
    # TODO: Добавить проверку lint:css для линтинга css файлов
    # Пока в стилях возвращается много ошибок, временно линтим только js
    - npm run lint:js
    - npm run build
    - npm run size
  artifacts:
    when: always
    paths:
      - node_modules/
      - dist/
    expire_in: 30 days


run_playwright_tests:
  stage: test
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_PIPELINE_SOURCE == 'api'
  tags:
    - devfoquz
  dependencies:
    - main
  image: mcr.microsoft.com/playwright:v1.44.1-jammy
  allow_failure: true
  script:
    - nvm use 20
    - npm ci
    - |
      set +e
      PLAYWRIGHT_JSON_OUTPUT_FILE=test-results.json npx playwright test --reporter=json
      PLAYWRIGHT_EXIT_CODE=$?
      set -e
      if [ $PLAYWRIGHT_EXIT_CODE -ne 0 ]; then
        echo "Playwright tests failed with exit code $PLAYWRIGHT_EXIT_CODE"
      fi
    - |
      if [ -n "$(git status --porcelain tests/e2e/snapshots/)" ]; then
        git config --global user.email "<EMAIL>"
        git config --global user.name "CI Bot"
        git add tests/e2e/snapshots/ 
        git commit -m "Add new screenshots from CI Bot"
        git push https://widget-tests-scrinshots:${TOKEN_TESTS_SCREENSHOTS}@doxsw.gitlab.yandexcloud.net/doxsw/foquz-widget-dom.git HEAD:$CI_COMMIT_REF_NAME
        echo "New screenshots committed and pushed."
      else
        echo "No new screenshots to commit."
      fi
      
    # Отправляем отчет о тестах в Mattermost
    - |
      if [ -f "test-results.json" ]; then
        jq -n --argfile playwright_report test-results.json \
          --arg project_name "$CI_PROJECT_NAME" \
          --arg project_url "$CI_PROJECT_URL" \
          --arg pipeline_id "$CI_PIPELINE_ID" \
          --arg pipeline_url "$CI_PIPELINE_URL" \
          --arg job_id "$CI_JOB_ID" \
          --arg job_url "$CI_JOB_URL" \
          --arg commit_sha "$CI_COMMIT_SHA" \
          --arg commit_short_sha "$CI_COMMIT_SHORT_SHA" \
          --arg commit_ref_name "$CI_COMMIT_REF_NAME" \
          --arg commit_message "$CI_COMMIT_MESSAGE" \
          --arg commit_author "$CI_COMMIT_AUTHOR" \
          --arg pipeline_source "$CI_PIPELINE_SOURCE" \
          '{
            playwright_report: $playwright_report,
            gitlab_data: {
              project_name: $project_name,
              project_url: $project_url,
              pipeline_id: $pipeline_id,
              pipeline_url: $pipeline_url,
              job_id: $job_id,
              job_url: $job_url,
              commit: {
                sha: $commit_sha,
                short_sha: $commit_short_sha,
                ref_name: $commit_ref_name,
                message: $commit_message,
                author: $commit_author
              },
              pipeline_source: $pipeline_source
            }
          }' > extended-test-results.json

        curl -X POST "${TEST_REPORT_URL}" \
          -H "Content-Type: application/json" \
          -d @extended-test-results.json \
          || echo "Failed to send report to Mattermost"
      else
        echo "No test results file found"
      fi
  artifacts:
    when: always
    paths:
      - tests/e2e/playwright-report/
      - tests/e2e/snapshots/
      - test-results/
      - extended-test-results.json
      - test-results.json

    expire_in: 30 days

update_snapshots:
  stage: update_snapshots
  tags:
    - devfoquz
  dependencies:
    - main
  script:
    - nvm use 20
    - npm ci
    - npx playwright test --update-snapshots
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "CI Bot"
    - git add --all
    - git commit -m "Update snapshots from CI Bot"
    - git push https://widget-tests-scrinshots:${TOKEN_TESTS_SCREENSHOTS}@doxsw.gitlab.yandexcloud.net/doxsw/foquz-widget-dom.git HEAD:$CI_COMMIT_REF_NAME

  when: manual
  allow_failure: true
  artifacts:
    when: always
    paths:
      - tests/e2e/playwright-report/
      - tests/e2e/snapshots/
      - test-results/ 
    expire_in: 30 days 

include:
  - 'images/cicd/build-images-ci.yml'