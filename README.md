# Foquz Прохождение опроса (Vue 3)

## Описание

Данный проект представляет собой веб-приложение для проведения опросов, разработанное с использованием Vue 3 и Vite. Приложение позволяет пользователям проходить опросы с различными типами вопросов, логикой и кастомизируемым, адаптивным дизайном. Является обновленной версией [Прохождения опроса на knockoutjs](https://doxsw.gitlab.yandexcloud.net/doxsw/foquz/-/tree/release50/ko/pages/poll/process?ref_type=heads)

## Архитектура

Проект использует [Feature Sliced Design (FSD)](https://feature-sliced.design/) архитектуру для организации кода.

Структура выглядит следующим образом:


`app/`<br/>
`entities/`<br/>
`shared/`<br/>
`features/`<br/>
`widgets/`

**Основной файл приложения:** `src/app/main.js`:

Инициализирует Vue приложение и подключает Pinia для управления состоянием.

- `src/app/style.css`: Глобальные стили приложения.

### Слой `widgets`

Слой содержит сложные композиционные компоненты, которые объединяют несколько сущностей и фич для реализации конкретных пользовательских интерфейсов.


Основные компоненты:
- `src/widgets/ui/`: Содержит базовые UI компоненты:
  - Header - шапка опроса
  - Footer - подвал опроса
  - PollContent - основной контент опроса
  - PollBody - тело опроса
  - QuestionsPage - страница с вопросами

Принципы размещения компонентов:
- Если компонент использует сторы (`usePollStore`, `useTranslationsStore` и т.д.), он размещается в `widgets/`
- Если компонент является чисто UI-компонентом без бизнес-логики, он размещается в `shared/ui/`
- Компоненты в `widgets/` могут использовать несколько сущностей и фич для реализации сложных интерфейсов

### Сущности:

- `src/entities/poll/`: Содержит модели и логику, связанную с опросами.
- `src/entities/question/`: Содержит модели и контроллеры для различных типов вопросов.

### Features (реализация различных фич):

В этой папке располагаются директории, реализующие различные фичи (продуктовые или технические). Каждая фича является самодостаточной и содержит все необходимые файлы для своей работы (модели, компоненты, утилиты, сторы и т.д.).

- `src/features/points/`: Реализация фичи с баллами. Содержит:
  - Модели и логику, связанную с баллами
  - API для работы с баллами
  - Стор для управления состоянием баллов
  - Логику интерпретации баллов
  - Отчет о тестировании

### Shared (общие ресурсы):

- `src/shared/constants/`: Константы, используемые в приложении.
- `src/shared/helpers/`: Вспомогательные функции и утилиты.
- `src/shared/api/`: Функции для работы с API. На данный момент папка содержит утилиты и функцию с запросами к API КЛАДР, так как основные запросы по апи опроса лежат в `entities/poll/api` у нас все апи относится к сущности `poll` на данный момент

### Локализация:

- `src/shared/store/translationsStore.js`: Стор для работы с переводами.

–––––––––––

Данное описание архитектуры является предварительным и будет меняться.

## Используемые библиотеки

- [Vue 3](https://v3.vuejs.org/)
- [Vite](https://vitejs.dev/)
- [Pinia](https://pinia.vuejs.org/) - для управления состоянием
- [Playwright](https://playwright.dev/) - для e2e тестирования
- [MSW](https://mswjs.io/) - для добавления API моков
- [ESLint](https://eslint.org/) - для линтинга
- [Prettier](https://prettier.io/) - для форматирования

## TypeScript

Проект поддерживает постепенный переход на TypeScript. Настроена конфигурация, позволяющая использовать как JavaScript, так и TypeScript файлы одновременно.

### Команды для работы с TypeScript

- `npm run type-check` - проверка типов в проекте
- `npm run type-check:watch` - проверка типов в режиме наблюдения
- `npm run build` - сборка проекта (включает проверку типов)

### Миграция на TypeScript

Для постепенной миграции на TypeScript:

1. Создавайте новые компоненты и файлы с расширением `.ts` или `.vue` с `<script lang="ts">`
2. При работе с существующими файлами:
   - Переименуйте файл с `.js` на `.ts`
   - Добавьте необходимые типы
   - Исправьте ошибки типизации

TypeScript настроен в режиме строгой типизации (`strict: true`).

## Дополнительные ссылки

- https://feature-sliced.design/ - Feature Sliced Design (FSD)
- https://habr.com/ru/companies/inDrive/articles/693768/ - Статья по архитектуре FSD на хабре

## Тестирование
 
Для информации о том, как добавлять и запускать тесты, пожалуйста, обратитесь к [руководству по тестированию](./docs/TESTING.md).
