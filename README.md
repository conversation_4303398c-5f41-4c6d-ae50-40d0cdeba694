# Foquz Widget DOM

Этот проект представляет собой виджет для встраивания опросов на веб-страницы. В отличии от предыдущей версии, данный виджет не использует айфреймы, а встраивается напрямую в DOM дерево.

Для рендеринга компонентов и реактивности используется [VanJS](https://vanjs.org/) (размером < 1kb)

## Содержание

- [Установка и использование](#установка-и-использование)
- [Разработка](#разработка)
- [Используемые технологии](#используемые-технологии)

## Установка и использование

Для установки зависимостей выполните команду:

```bash
npm install
```

Для сборки проекта выполните команду:

```bash
npm run build
```

Для запуска сервера разработки выполните команду:

```bash
npm run dev
```

## Разработка

### Структура проекта

- `src/`: Содержит исходный код виджета.
  - `components/`: Содержит компоненты VanJS.
    - `questions/`: Содержит компоненты, связанные с вопросами.
    - `ui/`: Содержит UI-компоненты.
  - `api/`: Содержит функции API.
  - `validation/`: Содержит функции валидации.
- `public/`: Содержит публичные ресурсы.
- `styles/`: Содержит CSS-стили.

### Скрипты

- `npm run build`: Сборка проекта.
- `npm run dev`: Запуск сервера разработки.

### Конфигурация

- `vite.config.js`: Конфигурация для Vite.
- `eslint.config.js`: Конфигурация для ESLint.

## Используемые технологии

- [**VanJS**](https://vanjs.org/): Легковесный JavaScript-фреймворк для создания компонентов и добавление реактивности.
- [**Vite**](https://vitejs.dev/): Инструмент сборки, предоставляющий быстрый сервер разработки и оптимизированные сборки.
- [**ESLint**](https://eslint.org/): Инструмент для выявления и исправления проблем в JavaScript-коде.
- [**CSS модули**](https://github.com/css-modules/css-modules): Используются для изоляции стилей. После компиляции стилей получаются классы вида `_fc-widget_82a8a_1`.

## Тестирование:

@TODO: Детально описать: как запускать тесты, используемые технологии для тестирования, техники тестирования виджета и вопросов
@TODO: Добавить запуск тестов в gitlab-CI
