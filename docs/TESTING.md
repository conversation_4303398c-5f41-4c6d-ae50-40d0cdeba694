# Тестирование приложения

## Как запустить тесты

Для запуска тестов используется команда:

```bash
npm run test
```

_Первый запуск тестов может привести к ошибкам, если браузеры не установлены. Для их установки выполните команду:_

```bash
npx playwright install
```

## Как работают тесты

Тесты используют комбинацию [Playwright](https://playwright.dev/) для e2e тестирования и [Mock Service Worker (MSW)](https://mswjs.io/) для моков API.

### Playwright

Playwright используется для автоматизации браузера и проведения end-to-end тестов. Он позволяет симулировать действия пользователя и проверять корректность работы приложения в реальных условиях.

### Mock Service Worker (MSW)

MSW используется для перехвата сетевых запросов и предоставления моковых ответов. Это позволяет тестировать приложение без зависимости от реального бэкенда.

## Как включить моки API в среде разработки

1. Создайте файл `.env.local` в корневой директории проекта.
2. Добавьте следующую строку в файл:

```
VITE_ENABLE_API_MOCK=true
```

3. В файле `src/app/main.js` используется функция `enableApiMockingIfNeeded()`, которая проверяет значение `VITE_ENABLE_API_MOCK` или параметр `_fz_enable_mocks` в URL и включает моки API, если оно установлено в `true`.

Например, URL с включенными моками может выглядеть так: `http://localhost:5173/star-rating?_fz_enable_mocks=true`

## FAQ

### Как добавить тесты для нового функционала?

1. Включите `VITE_ENABLE_API_MOCK=true` в файле `.env.local` (если файла нет, то создайте его), чтобы убедиться, что API корректно замокан. Для проверки можно зайти на страницу [http://localhost:5173/star-rating-3-stars-big-always-show-labels](http://localhost:5173/star-rating-3-stars-big-always-show-labels) и убедиться, что отображается вопрос из моковых данных (должен быть звездный рейтинг с 3 большими звездами и метками).

1.1 ЛИБО, вместо пункта 1, можно добавить в url параметр `_fz_enable_mocks=true`, чтобы включить моки API. Например, [http://localhost:5173/star-rating-3-stars-big-always-show-labels?_fz_enable_mocks=true](http://localhost:5173/star-rating-3-stars-big-always-show-labels?_fz_enable_mocks=true)

2. Добавьте моковые данные API в директорию `mocks/data`. Сейчас все обработчики для опроса содержатся в файле `mocks/handlers/poll-handlers.js`. По сути для добавления новых моковых данных нужно добавить новый ключ в объект `keysToData`, примерно вот так:

```javascript
const keysToData = {
  'my-awesome-key': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Новый вопрос',
          description_html: '<p>Новый вопрос<p>',
          type: STARS_QUESTION,
        },
      ],
    }),
  // ...
}
```

Объект `keysToData` содержит моковые данные для различных ключей опросов.
Каждый ключ соответствует уникальному идентификатору опроса.
Значение ключа - это функция, возвращающая объект с данными.
В данном случае, 'my-awesome-key' - это ключ для нового опроса.
Функция `defaultQuestionsData()` создает базовую структуру данных опроса,
а в параметре `questions` мы определяем список вопросов для этого опроса.
Каждый вопрос имеет свои свойства, такие как описание и тип.
Это позволяет легко создавать и настраивать моковые данные для различных сценариев тестирования.

Например, вот так выглядит ключ для NPS рейтинга с комментарием и пропуском (файл `mocks/data/questions/ratingNpsData.js`):

```javascript
const ratingNpsData = {
  'rating-nps-with-skip-and-comment': () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          comment_enabled: 1,
          skip: 1,
          skip_text: 'Пропустить вопрос',
        }),
      ],
    }),
  // ...
}
```

Можно открыть страницу [http://localhost:5173/rating-nps-with-skip-and-comment](http://localhost:5173/rating-nps-with-skip-and-comment) и убедиться, что отображается новый NPS рейтинг с комментарием и пропуском:

![nps-example](./images/nps-example.webp)

3. Напишите тест с использованием Playwright. Пример:

```javascript
import { expect, test } from '@playwright/test'

test('проверка новой функциональности', async ({ page }) => {
  await page.goto('/my-awesome-key') // открываем анкету с ключом "my-awesome-key"

  // Проверяем, что на странице отображается новый вопрос
  const questionText = await page.getByText('Новый вопрос')
  expect(questionText).toBeVisible()

  // Делаем скриншот страницы
  // Скриншоты делать не обязательно, но по ним легко проверять,
  // что нет регресса в плане дизайна и верстки
  await expect(page).toHaveScreenshot('my-awesome-key.png')
})
```

В качестве примера можно посмотреть на [тесты для звездного рейтинга](./tests/e2e/questions/starRating.test.js). Они достаточно просты и понятны.

4. Запустите тест с помощью команды `npm run test` и убедитесь, что он проходит успешно.

Помните, что при добавлении новых тестов важно также обновлять моковые данные API, чтобы они соответствовали ожидаемому поведению новой функциональности.
