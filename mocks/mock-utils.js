const API_VERSION = import.meta.env.VITE_API_VERSION;
const ROOT_BASE_URL = import.meta.env.VITE_ROOT_BASE_URL;
const ASSETS_BASE_URL = import.meta.env.VITE_ASSETS_BASE_URL;

export const rootApiUrl = (path, apiVersion = API_VERSION) => {
  return `${ROOT_BASE_URL}${apiVersion}/${path}`;
};

export const assetsApiUrl = (path) => {
  return `${ASSETS_BASE_URL}${path}`;
};

export const mockLangMessages = {
  "Обязательное поле": "Required field",
  "Поле обязательно для заполнения": "The field must be filled in",
  "Неверный формат": "Invalid format",
  "Некорректный формат": "Incorrect format",
  "Некорректный формат интервала": "Incorrect interval format",
  "Некорректное значение": "Incorrect value",
  "Некорректный диапазон": "Incorrect range",
  "Неверный формат ссылки": "Incorrect link format",
  "Некорректный период": "Incorrect period",
  "{count} символ": "{count} symbol",
  "{count} символа": "{count} symbols",
  "{count} символов": "{count} symbols",
  "{count} вариант": "{count} variant",
  "{count} варианта": "{count} variants",
  "{count} вариантов": "{count} variants",
  "Должно быть введено хотя бы {characters}":
    "At least {characters} must be entered",
  "Должно быть введено не более {characters}":
    "No more than {characters} must be entered",
  "Должно быть заполнено хотя бы одно поле":
    "At least one field must be filled in",
  "Нужно добавить хотя бы {options}": "At least {options} must be added",
  "Название повторяется": "This name already exists",
  "Нужно выбрать хотя бы один вариант ответа":
    "You must select at least one answer",
  "Можно загружать файлы форматов:": "You can upload files in the formats:",
  "Размер файла не должен превышать {size} Мб":
    "File must not exceed {size} MB",
  "Необходимо добавить хотя бы одно изображение":
    "You must add at least one picture",
  "Необходимо добавить хотя бы одно видео": "You must add at least one video",
  "Максимальное количество файлов:": "Maximum number of files",
  "Нужно поставить оценку": "You need to evaluate",
  "Нужно поставить все оценки": "You need to evaluate everything",
  "Не все оценки поставлены": "Not all grades are set",
  "Необходимо ответить хотя бы на {number}": "Please answer at least {number}",
  "Введите свой вариант": "Enter your answer",
  "Выберите значение в списке и/или введите свой вариант":
    "Select a value from the list and/or enter your answer",
  "Выберите значение в списке": "Select a value from the list",
  "Нужно выбрать один из вариантов": "You need to select one of the options",
  "Нужно изменить порядок": "You need to change the order",
  "Ваш комментарий": "Your comment",
  Выбрать: "Select",
  "Ваш выбор": "Your choice",
  "Нет результатов, удовлетворяющих условию поиска":
    "No results matching your search",
  "Загрузите файл": "Upload file",
  "Загрузить файл": "Upload file",
  "Загрузить фото": "Upload photo",
  "Загрузить видео": "Upload video",
  "Загрузить фото, видео": "Upload photo, video",
  "Загрузить аудио": "Upload audio",
  "Загрузить видео, аудио": "Upload video, audio",
  "Загрузить фото, аудио": "Upload photo, audio",
  "Загрузить фото, видео, аудио": "Upload photo, video, audio",
  Ужасно: "Very bad",
  Плохо: "Bad",
  Нормально: "Normal",
  Хорошо: "Good",
  Отлично: "Excellent",
  "Всё понравилось": "I liked everything",
  "Написать жалобу": "Submit a complaint",
  "Текст жалобы": "Complaint text",
  "Введите текст жалобы или загрузите фотографию":
    "Enter the text of your complaint or upload a photo",
  Отменить: "Cancel",
  Отправить: "Send",
  "Спасибо за обратную связь!": "Thank you for your feedback!",
  "Мы свяжемся с вами в течение 24 часов": "We'll contact you within 24 hours",
  "Отправлена жалоба № {number}": "Complaint № {number} was sent",
  "Ваш адрес {email} будет отписан от рассылки":
    "Your email address {email} will be unsubscribed",
  "Вы будете отписаны от рассылки": "You will be unsubscribed",
  "Создано в Foquz": "Created in Foquz",
  Скопировать: "Copy",
  Скопировано: "Copied",
  "Оставшееся время": "Remaining time",
  "Тестовый режим": "Test mode",
  Необязательный: "Optional",
  Назад: "Back",
  "Опрос успешно пройден!": "The survey has been successfully completed!",
  "К сожалению, время для прохождения опроса истекло":
    "Unfortunately, the time to complete the survey has expired",
  "К сожалению, ссылка устарела<br>и опрос больше не актуален":
    "Unfortunately, the link has expired<br> and the survey is no longer available",
  "К сожалению, опрос ещё не доступен для прохождения. Попробуйте позднее.":
    "Unfortunately, the survey is not yet available. Try again later.",
  "К сожалению, опрос больше не доступен для прохождения.":
    "Unfortunately, the survey is no longer available.",
  "Здесь был опрос, но его уже, к сожалению, нет.":
    "There was a survey here, but unfortunately it's no longer available.",
  "Опрос по техническим причинам недоступен.<br>В ближайшее время проблемы будут устранены.<br>Приносим извинения за доставленные неудобства.":
    "The survey is not available for technical reasons.<br> The issue will be resolved soon.<br> We apologize for the inconvenience.",
  "К сожалению, лимит для прохождения опроса в тестовом режиме исчерпан.<br>Для дальнейшего прохождения опрос необходимо опубликовать.":
    "Unfortunately, the limit for taking the survey in test mode has been reached.<br> To continue taking the survey, it must be published.",
  "Опрос недоступен.<br>Опрос был отключен, удален или время приема ответов для него истекло.":
    "Survey is unavailable.<br> The survey has been disabled, deleted, or the response time for the survey has expired.",
  "Создайте свой опрос в FOQUZ": "Create your request in Foquz",
  "Демо-доступ на 1 месяц и до 50 собранных анкет":
    "Demo access for 1 month and up to 50 collected surveys",
  Создать: "Create",
  Отписаться: "Unsubscribe",
  "Я хочу остаться": "I want to stay",
  "Вы отписаны от рассылки": "You are unsubscribed",
  "Мы заботимся о&nbsp;конфиденциальности ваших данных. Используя сервис Foquz, вы&nbsp;соглашаетесь с&nbsp;<a href='https://foquz.ru/agreement.pdf' target='_blank'>пользовательским соглашением</a> и&nbsp;<a href='https://foquz.ru/confidential.pdf' target='_blank'>политикой конфиденциальности</a>":
    "We care about your data privacy. Using the Foquz service, you are agree with the&nbsp;<a href='https://foquz.ru/agreement.pdf' target='_blank'>User agreement</a>&nbsp;and&nbsp;<a href='https://foquz.ru/confidential.pdf' target='_blank'>Privacy&nbsp;Policy</a>",
  Закрыть: "Close",
  "Копировать промокод": "Copy promo code",
  "Промокод скопирован": "Promo code copied",
  "Добавить вариант": "Add variant",
  Удалить: "Delete",
  Логин: "Login",
  Пароль: "Password",
  "Неверный логин или пароль": "Incorrect login or password",
  Войти: "Sign in",
  Авторизация: "Sign in to the survey",
  "Этот опрос доступен только авторизованным пользователям":
    "This survey is available to authorized users only",
  "Нужно поставить оценку всем блюдам": "You need to assess all the dishes",
  "Недопустимый тип файла": "Invalid file type",
  "Файл слишком большой": "File too large",
  "Произошла ошибка, попробуйте ещё раз": "An error occurred, please try again",
  "Не удалось загрузить файлы": "Failed to upload files",
  "Не удалось загрузить библиотеку скриншотов":
    "Failed to load screenshot library",
  "Не удалось сделать скриншот": "Failed to take screenshot",
  "Для создания скриншота выделите нужную область и нажмите «Готово»":
    "To create a screenshot, select the desired area and click 'Done'",
  Отмена: "Cancel",
  "Сделать скриншот экрана": "Take screenshot",
  Готово: "Done",
};

export const defaultPollLangsData = (defaultLangId = 1) => [
  {
    id: 13687,
    foquz_poll_id: 43680,
    lang_id: 1,
    checked: 1,
    default: defaultLangId === 1 ? 1 : 0,
    back_text: "",
    next_text: "",
    finish_text: "",
    unrequired_text: "",
  },
  {
    id: 13688,
    foquz_poll_id: 43680,
    lang_id: 2,
    checked: 1,
    default: defaultLangId === 2 ? 1 : 0,
    back_text: "Back",
    next_text: "Next",
    finish_text: "Success!",
    unrequired_text: "Unreqiured",
    messages: mockLangMessages,
  },
];
