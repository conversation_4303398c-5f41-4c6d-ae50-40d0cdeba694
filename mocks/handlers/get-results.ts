import { http, HttpResponse } from 'msw'
import { getResultsData } from '../data/points/get-results'
import { assetsApiUrl } from '../mock-utils'

export default [
  http.get(assetsApiUrl('foquz/api/p/get-results'), ({ request }) => {
    const url = new URL(request.url)
    const key = url.searchParams.get('key')

    if (key && getResultsData[key]) {
      return HttpResponse.json(getResultsData[key]())
    }

    // Return empty response for unknown keys
    return HttpResponse.json({
      id: 0,
      displaySettings: null,
      displayPages: [],
      questions: [],
      points: {
        answer_points: 0,
        points_max: 0,
        percent: 0,
      },
      poll: null,
      contact: null,
    })
  }),
]
