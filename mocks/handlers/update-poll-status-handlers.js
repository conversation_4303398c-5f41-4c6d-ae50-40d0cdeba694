import { HttpResponse, http } from "msw";
import { assetsApiUrl } from "../mock-utils";

const updatePollStatusHandlers = [
  http.post(assetsApiUrl("foquz/api/p/change-status"), async ({ request }) => {
    const url = new URL(request.url);
    const authKey = url.searchParams.get("authKey");
    const key = url.searchParams.get("key");

    if (!authKey && !key) {
      return new HttpResponse(null, { status: 400, statusText: "Bad Request" });
    }

    const formData = await request.formData();
    const status = formData.get("status");

    if (status !== "done" && status !== "open") {
      return new HttpResponse(null, { status: 400, statusText: "Bad Request" });
    }

    return HttpResponse.json(
      {
        success: true,
        message: `Poll status updated successfully to ${status}`,
      },
      { status: 200 },
    );
  }),
];

export default updatePollStatusHandlers;
