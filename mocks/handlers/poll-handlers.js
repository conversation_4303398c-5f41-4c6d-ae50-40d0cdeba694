import { http, HttpResponse } from 'msw'
import { customThemesMockData } from '../data/custom-themes'
import { defaultPollsMap } from '../data/default-polls'
import displayPagesMockData from '../data/displayPagesMockData'
import logicMockData from '../data/logicMockData'
import { pointsData } from '../data/points'
import classifierMockData from '../data/questions/classifierMockData'
import dateQuestionMockData from '../data/questions/DateQuestionMockData'
import diffMockData from '../data/questions/diffMockData'
import galleryMockData from '../data/questions/galleryMockData'
import intermediateBlockMockData from '../data/questions/interBlockMockData'
import matrixMockData from '../data/questions/matrixMockData'
import mediaVariantsMockData from '../data/questions/mediaVariantsMockData'
import npsExtraQuestionsMockData from '../data/questions/npsExtraQuestionsMockData'
import priorityMockData from '../data/questions/priorityMockData'
import quizMockData from '../data/questions/quizMockData'
import ratingNpsData from '../data/questions/ratingNpsData'
import ratingScaleMockData from '../data/questions/ratingScaleMockData'
import scaleMockData from '../data/questions/scaleMockData'
import smileRatingMockData from '../data/questions/smileRatingMockData'
import starRatingMockData from '../data/questions/starRatingMockData'
import starRatingVariantsMockData from '../data/questions/starRatingVariantsMockData'
import textQuestionMockData from '../data/questions/textQuestionMockData'
import variantsMockData from '../data/questions/variantsData'
import viewLogicMockData from '../data/questions/viewLogicMockData'
import variablesMockData from '../data/variablesMockData'
import { assetsApiUrl } from '../mock-utils'

const keysToData = {
  ...defaultPollsMap,
  ...starRatingMockData,
  ...ratingScaleMockData,
  ...ratingNpsData,
  ...smileRatingMockData,
  ...variantsMockData,
  ...textQuestionMockData,
  ...dateQuestionMockData,
  ...starRatingVariantsMockData,
  ...quizMockData,
  ...scaleMockData,
  ...priorityMockData,
  ...mediaVariantsMockData,
  ...galleryMockData,
  ...matrixMockData,
  ...diffMockData,
  ...classifierMockData,
  ...intermediateBlockMockData,
  ...displayPagesMockData,
  ...logicMockData,
  ...viewLogicMockData,
  ...variablesMockData,
  ...pointsData,
  ...npsExtraQuestionsMockData,

  // @NOTE: Временно отключено, пока данные тесты будем прогонять в отдельной ветке
  // ...customThemesMockData,
}

function getPollData(key, { additionalParams = {} } = {}) {
  if (key.includes(':')) {
    // eslint-disable-next-line no-unused-vars
    const [_, pollKey] = key.split(':')
    return keysToData[pollKey] ? keysToData[pollKey](additionalParams) : keysToData.default(additionalParams)
  }
  return keysToData[key] ? keysToData[key](additionalParams) : keysToData.default(additionalParams)
}

export default [
  http.post(assetsApiUrl('foquz/api/p/answer'), async ({ request }) => {
    const url = new URL(request.url)
    const params = new URLSearchParams(url.search)
    const key = params.get('key')
    const formData = await request.formData()

    const formDataObject = Object.fromEntries(formData)

    for (const key in formDataObject) {
      if (key.startsWith('params[')) {
        formDataObject[key.replace(/params\[(.*)\]/g, 'URL.$1')] = formDataObject[key]
        delete formDataObject[key]
      }
    }

    const data = getPollData(key, { additionalParams: formDataObject })

    // Add all URL params to data.variables

    if (data.variables && formData.entries().length > 0) {
      for (const [key, value] of formData.entries()) {
        // parse "params[some-key]" to "some-key"
        const parsedKey = key.replace('params[', '').replace(']', '')
        data.variables[`URL.${parsedKey}`] = value
      }
    }

    return HttpResponse.json(data)
  }),
]
