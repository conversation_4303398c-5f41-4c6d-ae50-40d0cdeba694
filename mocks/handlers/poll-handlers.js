import { http, HttpResponse } from "msw";
import { assetsApiUrl } from "../mock-utils";
import defaultQuestionsData from "../data/questions/defaultQuestionData";
import starRatingMockData from "../data/questions/StarRatingData";
import ratingMockData from "../data/questions/ratingData";
import { QUESTION_TYPES } from "../../src/helpers";
import ratingNpsData, {
  ratingNpsOptions,
} from "../data/questions/ratingNpsData";
import variantsMockData, {
  variantsOptions,
} from "../data/questions/variantsData";
import smileRatingData from "../data/questions/smileRatingData";
import textMockData, { textOptions } from "../data/questions/textData";
import intermediateBlockMockData, {
  intermediateBlockOptions,
} from "../data/questions/intermediateBlockData";
import {
  INTERMEDIATE_BLOCK_TYPES,
  VARIANT_TYPES,
} from "../../src/helpers/question-helpers/questionTypes";
import logicMockData from "../data/viewLogicData";
import localizationMockData from "../data/localizationData";

const keysToData = {
  default: () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Звездный рейтинг",
          description_html: "<p>Звездный рейтинг<p>",
        },
      ],
    }),
  "default-2-questions": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Звездный рейтинг",
          description_html: "<p>Звездный рейтинг<p>",
          id: 1,
          question_id: 34391,
        },
        {
          description: "Второй вопрос",
          description_html: "<p>Второй вопрос<p>",
          type: QUESTION_TYPES.RATING,
          id: 2,
          question_id: 34392,
          starRatingOptions: {
            count: 5,
            size: "md",
            labelsArray: ["1", "2", "3", "4", "5"],
          },
        },
      ],
    }),
  "default-with-subdescription": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Звездный рейтинг",
          description_html: "<p>Звездный рейтинг<p>",
          subdescription: "<p>Оцените наш сервис</p>",
        },
      ],
    }),
  "default-with-unrequired-text": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Звездный рейтинг",
          description_html: "<p>Звездный рейтинг<p>",
          subdescription: "<p>Оцените наш сервис</p>",
          unrequired_text: "Необязательный",
          isRequired: 0,
        },
      ],
    }),
  "default-with-unrequired-custom-text": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Звездный рейтинг",
          description_html: "<p>Звездный рейтинг<p>",
          subdescription: "Оцените наш сервис",
          isRequired: 0,
        },
      ],
      design: {
        unrequired_text: "Необязательный (проходите, если хотите)",
      },
    }),
  "default-without-prev-button": () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
    }),
  "default-without-prev-button-2-questions": () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
      questions: [
        {
          type: QUESTION_TYPES.STAR_RATING,
          starRatingOptions: {
            count: 5,
          },
        },
        {
          type: QUESTION_TYPES.RATING,
          starRatingOptions: {
            count: 5,
          },
        },
      ],
    }),
  "default-with-bg-color": () =>
    defaultQuestionsData({
      design: {
        main_place_color: "rgba(255, 0, 0, 0)",
        background_color: "rgba(255, 0, 0, 1)",
      },
    }),
  "default-with-bg-image": () =>
    defaultQuestionsData({
      design: {
        main_place_color: "rgba(255, 0, 0, 0)",
        background_color: "rgba(255, 0, 0, 0.5)",
        background_image: "/uploads/summer.jpg",
      },
    }),
  "default-with-mobile-bg-image": () =>
    defaultQuestionsData({
      design: {
        main_place_color: "rgba(255, 0, 0, 0)",
        background_color: "rgba(255, 0, 0, 0.5)",
        background_image: "/uploads/summer.jpg",
        mobile_background_image: "/uploads/clouds.jpg",
        darkening_background: 1,
        text_on_place: "rgba(255, 255, 255, 1)",
      },
    }),
  "replace-placeholders": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({ id: 1 }),
        {
          type: QUESTION_TYPES.STAR_RATING,
          description: "Ответ на первый вопрос: {ANSWER.1}",
          description_html: "<p>Ответ на первый вопрос: {ANSWER.1}<p>",
          subdescription: "URL param test: {URL.test}",
          starRatingOptions: {
            count: 5,
          },
          id: 2,
        },
        variantsOptions({
          description: "Почему {ANSWER.2}?",
          description_html: "<p>Почему {ANSWER.2}?<p>",
          isHaveCustomField: 1,
          type: QUESTION_TYPES.VARIANTS,
          variantsType: VARIANT_TYPES.MULTIPLE,
          self_variant_text: "Свой вариант",
          selfVariantPlaceholderText: "Введите свой вариант",
          selfVariantParam: {
            min: 0,
            max: 50,
          },
          id: 3,
        }),
        textOptions({
          description: "Ответ на третий вопрос: {ANSWER.3}",
          description_html: "<p>Ответ на третий вопрос: {ANSWER.3}<p>",
          id: 4,
        }),
        intermediateBlockOptions({
          id: 5,
          endScreenImages: [
            {
              logo: "/uploads/summer.jpg",
              external_logo: "https://example.com/external-logo1.png",
              description: "{URL.alt}",
              description_html: "<p>{URL.alt}<p>",
              position: 1,
              width: 133,
              height: 200,
              link: "{URL.link}",
            },
          ],
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE,
            agreement: 0,
            text: `<p>Промежуточный результат</p>
                 <p>&nbsp;</p>
                 <p>Ответ на первый вопрос {ANSWER.1}</p>
                 <p>Ответ на второй вопрос {ANSWER.2}</p>
                 <p>Ответ на третий вопрос {ANSWER.3}</p>
                 <p>Ответ на четверный вопрос {ANSWER.4}</p>
                 <p>Ответ на ДЕСЯТЫЙ вопрос (должно быть пусто) {ANSWER.10}</p>
                 <p>&nbsp;</p>
                 <p>Значение параметра test в url = {URL.test}</p>
                 <p>Значение параметра test2 в url = {URL.test2}</p>
                 <p>&nbsp;</p>
                 <p>Характеритика 1 филиала = {FILIAL.param1}</p>
                 <p>Характеритика 2 филиала = {FILIAL.param2}</p>
                 <p>Характеритика 3 филиала = {FILIAL.param3}</p>`,
          },
        }),
      ],
      variables: {
        fio: "",
        order: null,
        codes: {
          123: "PROMO-123",
        },
        scoresInterpretationRanges: [],
        "FILIAL.param1":
          "https://wavehouse.ru/wp-content/uploads/2017/04/mini.jpeg",
        "FILIAL.param2": "https://rg.ru/uploads/images/192/68/94/987654.jpg",
        "FILIAL.param3":
          "https://naked-science.ru/wp-content/uploads/2017/08/field_image_1_624.jpg",
      },
    }),
  ...starRatingMockData,
  ...ratingMockData,
  ...ratingNpsData,
  ...variantsMockData,
  ...smileRatingData,
  ...textMockData,
  ...intermediateBlockMockData,
  ...logicMockData,
  ...localizationMockData,
  "custom-design": () =>
    defaultQuestionsData({
      questions: [
        {
          type: QUESTION_TYPES.STAR_RATING,
          show_labels: 1,
          starRatingOptions: {
            count: 5,
            size: "md",
            color: "red",
            labelsArray: ["1", "2", "3", "4", "5"],
          },
        },
      ],
      design: {
        main_color: "rgba(255, 0, 0, 1)",
        background_color: "rgba(0, 255, 0, 1)",
        header_color: "#0000FF",
        star_color: "#FF00FF",
        rating_color: "#00FFFF",
        nps_color_from: "#FFFF00",
        nps_color_to: "#000000",
        sem_diff_color_from: "#800080",
        sem_diff_color_to: "#FFA500",
        is_use_header: 1,
        font_family: "Courier, sans-serif",
        title_font_size: "24",
        font_size: "12",
        text_on_bg: "rgba(255, 255, 255, 1)",
        text_on_place: "rgba(0, 0, 0, 1)",
        link_color: "rgba(255, 0, 0, 1)",
        from_template: 0,
        logo_link: "http://custom-logo.com",
        logo_type: "text",
        logo_text: "Custom Logo",
        logo_font_family: "Courier, sans-serif",
        logo_color: "#FF0000",
        back_text: "Go Back",
        back_button_background_color: "rgba(255, 255, 255, 1)",
        back_button_text_color: "rgba(0, 0, 0, 1)",
        back_button_stroke_color: "rgba(0, 0, 0, 1)",
        back_button_radius: 12,
        next_button_background_color: "rgba(234, 100, 0, 1)",
        next_button_text_color: "rgba(255, 255, 255, 1)",
        next_button_stroke_color: "rgba(255, 255, 255, 1)",
        next_button_radius: 12,
        start_button_background_color: "rgba(0, 255, 0, 1)",
        start_button_text_color: "rgba(0, 0, 0, 1)",
        start_button_stroke_color: "rgba(0, 0, 0, 1)",
        start_button_radius: 12,
        next_text: "Next",
        finish_text: "Finish",
        finish_link: "http://finish.com",
        unrequired_text: "Optional",
        show_process: 1,
        darkening_background: 1,
        place_under_buttons: "light",
        show_prev_button: 1,
        main_place_color: "rgba(255, 255, 0, 1)",
        choose_language: 1,
        full_width: 1,
        disable_question_autoscroll: 0,
        backgroundImage: "/img/custom-bg.jpg",
        logo: "/img/custom-logo.png",
        mainColor: "#FF0000",
        logoLink: "http://custom-logo.com",
        bgColor: "rgba(0, 255, 0, 1)",
        headerColor: "#0000FF",
        starColor: "#FF00FF",
        ratingColor: "#00FFFF",
        npsColorFrom: "#FFFF00",
        npsColorTo: "#000000",
        semDiffColorFrom: "#800080",
        semDiffColorTo: "#FFA500",
        textOnBg: "#FFFFFF",
        textOnPlace: "#000000",
        linkColor: "#FF0000",
        fontFamily: "Arial, sans-serif",
        titleFontSize: "24",
        fontSize: "12",
        isUseHeader: true,
        templateId: 0,
        logoType: "text",
        logoText: "Custom Logo",
        logoFontFamily: "Impact, Charcoal, sans-serif",
        logoColor: "#FF0000",
        backText: "Go Back",
        nextText: "Next",
        finishText: "Finish",
        finishLink: "http://finish.com",
        placeUnderButtons: "light",
      },
    }),
};

function getPollData(key) {
  if (key.includes(":")) {
    // eslint-disable-next-line no-unused-vars
    const [_, pollKey] = key.split(":");
    return keysToData[pollKey] ? keysToData[pollKey]() : keysToData.default();
  }
  return keysToData[key] ? keysToData[key]() : keysToData.default();
}

export default [
  http.post(assetsApiUrl("foquz/api/p/answer"), async ({ request }) => {
    const url = new URL(request.url);
    const params = new URLSearchParams(url.search);
    const key = params.get("key");

    let data = getPollData(key);

    return HttpResponse.json(data);
  }),
];
