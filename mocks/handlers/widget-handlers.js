import { http, HttpResponse } from "msw";
import { rootApiUrl } from "../mock-utils";
import { generateWidgetData } from "../data/widgetData";
import {
  WIDGET_APPEARANCE,
  WIDGET_BUTTON_POSITION,
  WIDGET_BUTTON_TYPE,
  WIDGET_FORM,
} from "../../src/components/core/widget/constants";

// widget-handlers.js

const widgetData = {
  469: {
    id: "469",
    is_active: "1",
    company_id: "782",
    code: "7631557e2657f1a329b1941a14fdc3b9",
    poll_id: "45304",
    name: "Новый виджет (2)",
    filial_id: "",
    appearance: "0",
    form: "5",
    button_type: "0",
    position: "1",
    button_text: "",
    font: "Arial, Helvetica, sans-serif",
    font_size: "14",
    bold: "0",
    italic: "0",
    text_color: "rgb(0, 0, 0)",
    background_color: "rgb(255, 255, 255)",
    stroke: "1",
    simple: "0",
    show_until: "4",
    targeting: "0",
    triggers: null,
    triggers_status: {
      cookies: "0",
      client_tags: "0",
      devices: "0",
      coverage: "0",
      url: "0",
      event: "0",
    },
    created_at: "2024-07-05 09:09:28",
    updated_at: "2024-07-06 07:24:10",
  },
};

function handleWidgetData(widgetId, overrides = {}) {
  return {
    link: "https://widget-bogdan.devfoquz.ru/p/9c7e57d3a5d8e91c7a7a6895a5a2e9ec",
    key: "9c7e57d3a5d8e91c7a7a6895a5a2e9ec",
    time: null,
    widget: {
      ...widgetData[widgetId],
      ...overrides,
    },
    clientUUID: "*************-4843-9093-4d971066058d",
  };
}

const keysToData = {
  "appearance-click": () =>
    handleWidgetData("469", { appearance: WIDGET_APPEARANCE.WITH_CLICK }),
  "button-type-icon": () =>
    handleWidgetData("469", {
      appearance: WIDGET_APPEARANCE.WITH_CLICK,
      button_type: WIDGET_BUTTON_TYPE.ICON,
    }),
  "position-floating-right": () =>
    handleWidgetData("469", {
      appearance: WIDGET_APPEARANCE.WITH_CLICK,
      position: WIDGET_BUTTON_POSITION.FIXED_RIGHT,
    }),
  "custom-button-style": () =>
    handleWidgetData("469", {
      button_text: "Click Me!",
      font_size: "16",
      bold: "1",
      text_color: "rgb(255, 0, 0)",
      background_color: "rgb(0, 255, 0)",
      appearance: WIDGET_APPEARANCE.WITH_CLICK,
    }),
  "simplified-page-stop": () =>
    handleWidgetData("469", {
      appearance: WIDGET_APPEARANCE.WITHOUT_CLICK,
      form: WIDGET_FORM.SIMPLIFIED_PAGE_STOP,
    }),
  "close-on-finish-click": () =>
    handleWidgetData("469", {
      close_by_finish_button: "1",
    }),
};

function getWidgetData(key) {
  if (key.includes(":")) {
    const [widgetKey] = key.split(":");
    return keysToData[widgetKey]
      ? keysToData[widgetKey]()
      : handleWidgetData("469");
  }
  return keysToData[key] ? keysToData[key]() : handleWidgetData("469");
}

export default [
  http.post(rootApiUrl("visit"), async ({ request }) => {
    const data = await request.formData();
    const link = data.get("link");
    const time = data.get("time");
    const key = data.get("key");

    if (link === "null") {
      return HttpResponse.json(generateWidgetData({ link: null }));
    }

    if (key) {
      return HttpResponse.json(getWidgetData(key));
    }

    return HttpResponse.json(generateWidgetData({ time }));
  }),
];
