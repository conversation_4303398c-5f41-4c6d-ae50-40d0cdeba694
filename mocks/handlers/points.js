import { http, HttpResponse } from 'msw'
import { assetsApiUrl } from '../mock-utils'

const pointsMocks = {
  'default': {
    questions: [
      { questionId: 210, points: 0, maxPoints: 2, correctAnswer: { date: '02.01.2024' } },
      { questionId: 211, points: 2, maxPoints: 2, correctAnswer: { date: '03.01' } },
      { questionId: 212, points: 2, maxPoints: 2, correctAnswer: { time: '14:00' } },
      { questionId: 213, points: 2, maxPoints: 2, correctAnswer: { date: '05.01.2024', time: '15:00' } },
      { questionId: 214, points: 2, maxPoints: 2, correctAnswer: { date: '06.01', time: '16:00' } },
    ],
    points: 8,
    maxPoints: 10,
  },
  'points-complex': {
    questions: [
      {
        questionId: 406,
        points: 2,
        maxPoints: 2,
        correctAnswer: {
          id: [1],
        },
      },
      {
        questionId: 407,
        points: 3,
        maxPoints: 6,
        correctAnswer: {
          id: [1, 2],
        },
      },
      {
        questionId: 401,
        points: 2,
        maxPoints: 2,
        correctAnswer: [
          { row: 'Качество продукта', column: 'Отлично' },
          { row: 'Скорость доставки', column: 'Отлично' },
          { row: 'Работа поддержки', column: 'Отлично' },
        ],
      },
      {
        questionId: 402,
        points: 4,
        maxPoints: 6,
        correctAnswer: [
          { row: 'Удобство использования', column: ['Частично удовлетворен', 'Полностью удовлетворен'] },
          { row: 'Цена', column: ['Частично удовлетворен', 'Полностью удовлетворен'] },
          { row: 'Функциональность', column: ['Частично удовлетворен', 'Полностью удовлетворен'] },
        ],
      },
    ],
    points: 11,
    maxPoints: 16,
  },
  'additional-question-types': {
    points: 8,
    maxPoints: 10,
    questions: [
      { questionId: 303, points: 2, maxPoints: 4, correctAnswer: { id: [1, 4] } },
      { questionId: 304, points: 6, maxPoints: 6, correctAnswer: { id: [1, 2, 3] } },
      { questionId: 305, points: 4, maxPoints: 8, correctAnswer: { id: [4, 1, 3, 2] } },
    ],
  },
}

export default [
  http.get(assetsApiUrl('foquz/api/p/get-points'), ({ request }) => {
    const url = new URL(request.url)
    const key = url.searchParams.get('key')

    const data = pointsMocks[key] || pointsMocks.default

    return HttpResponse.json(data)
  }),
]
