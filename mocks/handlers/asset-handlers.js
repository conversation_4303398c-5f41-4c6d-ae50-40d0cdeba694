import { http, HttpResponse } from 'msw'
import { assetsApiUrl } from '../mock-utils'

async function handleAssets(request) {
  const url = new URL(request.url)
  const assetsToReplace = ['/uploads/', '/img/']
  let assetPath = url.pathname.replace(assetsToReplace[0], '')
  assetPath = assetPath.replace(assetsToReplace[1], '')
  assetPath = assetPath.replace('/svg', '')

  // Map to your local asset
  const localAssetPath = `/mocked-assets/${assetPath}`

  try {
    const response = await fetch(localAssetPath)
    if (!response.ok)
      throw new Error('Asset not found')

    const blob = await response.blob()
    let contentType = response.headers.get('Content-Type')

    if (url.pathname.endsWith('.svg'))
      contentType = 'image/svg+xml'

    return new HttpResponse(blob, {
      status: 200,
      headers: {
        'Content-Type': contentType || 'application/octet-stream',
      },
    })
  }
  catch (error) {
    console.error('Failed to load mocked asset:', error)
    return new HttpResponse(null, { status: 404 })
  }
}
const assetHandlers = [
  http.get(assetsApiUrl('uploads/*'), async ({ request }) => {
    return handleAssets(request)
  }),
  http.get(assetsApiUrl('img/*'), async ({ request }) => {
    return handleAssets(request)
  }),
]

export default assetHandlers
