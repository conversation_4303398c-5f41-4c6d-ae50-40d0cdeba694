import { HttpResponse, http } from "msw";
import { assetsApiUrl } from "../mock-utils";

const assetHandlers = [
  http.get(assetsApiUrl("uploads/*"), async ({ request }) => {
    const url = new URL(request.url);
    const assetPath = url.pathname.replace("/uploads/", "");

    // Map to your local asset
    const localAssetPath = `/mocked-assets/${assetPath}`;

    try {
      const response = await fetch(localAssetPath);
      if (!response.ok) throw new Error("Asset not found");

      const blob = await response.blob();
      return new HttpResponse(blob, {
        status: 200,
        headers: {
          "Content-Type":
            response.headers.get("Content-Type") || "application/octet-stream",
        },
      });
    } catch (error) {
      console.error("Failed to load mocked asset:", error);
      return new HttpResponse(null, { status: 404 });
    }
  }),
];

export default assetHandlers;
