import { http, HttpResponse } from 'msw'
import { assetsApiUrl } from '../mock-utils'

// In-memory storage for uploaded files
const uploadedFileStore = new Map()

export default [
  http.post(assetsApiUrl('foquz/api/p/upload-files'), async ({ request }) => {
    const formData = await request.formData()
    const files = formData.getAll('files[]')

    // Transform uploaded files into response format
    const uploadedFiles = await Promise.all(files.map(async (file) => {
      // Store file in memory
      const fileUrl = `/uploads/tmp/${file.name}`
      uploadedFileStore.set(fileUrl, file)

      const posterUrl = file.type.startsWith('image/') ? fileUrl : null

      return {
        name: file.name,
        url: fileUrl,
        poster: posterUrl,
        type: file.type.startsWith('image/')
          ? 'image'
          : file.type.startsWith('video/')
            ? 'video'
            : file.type.startsWith('audio/') ? 'audio' : 'document',
      }
    }))

    return HttpResponse.json({
      files: uploadedFiles,
    })
  }),

  // Add handler to serve uploaded files
  http.get(assetsApiUrl('uploads/tmp/:filename'), async ({ params }) => {
    const fileUrl = `/uploads/tmp/${params.filename}`
    const file = uploadedFileStore.get(fileUrl)

    if (!file) {
      return new HttpResponse(null, { status: 404 })
    }

    return new HttpResponse(file)
  }),
]
