import {
  INTERMEDIATE_BLOCK_TYPES,
  MATRIX_3D_QUESTION,
  QUIZ_QUESTION,
} from '@entities/question/model/types'
import { POLL_NAVIGATION_TYPE } from '@shared/constants'
import MaskTypes from '@shared/constants/maskTypes'
import defaultQuestionsData from '../defaultQuestionsData'
import { intermediateBlockOptions } from '../questions/interBlockMockData'

export const complexMockData2 = {
  'points-complex-questions-2': () => defaultQuestionsData({
    showAdv: false,
    answer: {
      auth_key: 'points-report-complex-questions-2',
    },
    design: {
      show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
      disable_question_autoscroll: 1,
    },
    poll: {
      point_system: 1,
      displaySetting: {
        id: 1631,
        poll_id: 50984,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      displayPages: [
        {
          id: 2442,
          foquz_poll_id: 50984,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            { id: 401 }, // Quiz full
            { id: 402 }, // Quiz simple (with skip)
            { id: 403 }, // Matrix 3D
          ],
        },
      ],
    },
    questions: [
      // Quiz full
      {
        type: QUIZ_QUESTION,
        id: 1,
        question_id: 401,
        description: 'Анкета полная',
        description_html: '<p>Анкета полная</p>',
        isRequired: 0,
        values: [
          {
            id: 1,
            label: 'Без маски',
            maskType: MaskTypes.NoMask,
            isRequired: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 2,
            label: 'Без маски - Многострочное',
            isRequired: 0,
            maskType: MaskTypes.NoMask,
            isTextarea: 1,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 3,
            label: 'ФИО',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Name,
            isTextarea: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
            maskConfig: {
              name: {
                visible: true,
                required: false,
                placeholderText: 'Имя',
                minLength: 0,
                maxLength: 50,
              },
              surname: {
                visible: true,
                required: false,
                placeholderText: 'Фамилия',
                minLength: 0,
                maxLength: 50,
              },
              patronymic: {
                visible: true,
                required: false,
                placeholderText: 'Отчество',
                minLength: 0,
                maxLength: 50,
              },
            },
          },
          {
            id: 4,
            label: 'Телефон',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Phone,
            isTextarea: 0,
            placeholderText: '+7 (___) ___-____',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 5,
            label: 'Email',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Email,
            isTextarea: 0,
            placeholderText: '<EMAIL>',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 6,
            label: 'Число',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Number,
            isTextarea: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 7,
            label: 'Сайт',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Site,
            isTextarea: 0,
            placeholderText: 'https://',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 8,
            label: 'Дата',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Date,
            isTextarea: 0,
            placeholderText: 'DD.MM.YYYY',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 9,
            label: 'День и месяц',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.DateMonth,
            isTextarea: 0,
            placeholderText: 'DD.MM',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 10,
            label: 'Период',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Period,
            isTextarea: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
        ],
      },

      // Quiz simple (with skip)
      {
        type: QUIZ_QUESTION,
        id: 2,
        question_id: 402,
        description: 'Анкета (в которой мы пропускаем ответы)',
        description_html: '<p>Анкета (в которой мы пропускаем ответы)</p>',
        isRequired: 0,
        values: [
          {
            id: 1,
            label: 'Без маски',
            maskType: MaskTypes.NoMask,
            isRequired: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 2,
            label: 'Без маски - Многострочное',
            isRequired: 0,
            maskType: MaskTypes.NoMask,
            isTextarea: 1,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 3,
            label: 'ФИО',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Name,
            isTextarea: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
            maskConfig: {
              name: {
                visible: true,
                required: false,
                placeholderText: 'Имя',
                minLength: 0,
                maxLength: 50,
              },
              surname: {
                visible: true,
                required: false,
                placeholderText: 'Фамилия',
                minLength: 0,
                maxLength: 50,
              },
              patronymic: {
                visible: true,
                required: false,
                placeholderText: 'Отчество',
                minLength: 0,
                maxLength: 50,
              },
            },
          },
          {
            id: 4,
            label: 'Телефон',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Phone,
            isTextarea: 0,
            placeholderText: '+7 (___) ___-____',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 5,
            label: 'Email',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Email,
            isTextarea: 0,
            placeholderText: '<EMAIL>',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 6,
            label: 'Число',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Number,
            isTextarea: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 7,
            label: 'Сайт',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Site,
            isTextarea: 0,
            placeholderText: 'https://',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 8,
            label: 'Дата',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Date,
            isTextarea: 0,
            placeholderText: 'DD.MM.YYYY',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 9,
            label: 'День и месяц',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.DateMonth,
            isTextarea: 0,
            placeholderText: 'DD.MM',
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
          {
            id: 10,
            label: 'Период',
            value: '',
            isRequired: 0,
            maskType: MaskTypes.Period,
            isTextarea: 0,
            textFieldParam: {
              min: 0,
              max: 250,
            },
          },
        ],
      },

      // Matrix 3D
      {
        type: MATRIX_3D_QUESTION,
        id: 3,
        question_id: 403,
        description: '3D матрица. Пах-шот методически притягивает теоретический баинг и селлинг, отмечает Рассел.',
        description_html: '<p>3D матрица. Пах-шот методически притягивает теоретический баинг и селлинг, отмечает Рассел.</p>',
        isRequired: 0,
        matrixElements: {
          rows: [
            {
              id: 1,
              name: 'Салат Греческий',
              description: 'Салат Греческий',
              position: 1,
            },
            {
              id: 2,
              name: 'Сыр Фета',
              description: 'Сыр Фета',
              position: 2,
            },
            {
              id: 3,
              name: 'Салат «Цезарь»',
              description: 'Салат «Цезарь»',
              position: 3,
            },
            {
              id: 4,
              name: 'Курица',
              description: 'Курица',
              position: 4,
            },
          ],
          columns: [
            {
              id: 1,
              name: 'Салат',
              description: 'Салат',
              position: 1,
              variants: [
                {
                  id: '1',
                  name: 'Отвратительно',
                  persistentId: '1',
                },
                {
                  id: '2',
                  name: 'Хорошо',
                  persistentId: '2',
                },
                {
                  id: '3',
                  name: 'Отлично',
                  persistentId: '3',
                },
              ],
            },
            {
              id: 2,
              name: 'Салат «Цезарь»',
              description: 'Салат «Цезарь»',
              position: 2,
              variants: [
                {
                  id: '1',
                  name: 'Отвратительно',
                  persistentId: '1',
                },
                {
                  id: '2',
                  name: 'Хорошо',
                  persistentId: '2',
                },
                {
                  id: '3',
                  name: 'Отлично',
                  persistentId: '3',
                },
              ],
            },
            {
              id: 3,
              name: 'Сыр Фета',
              description: 'Сыр Фета',
              position: 3,
              variants: [
                {
                  id: '1',
                  name: 'Отвратительно',
                  persistentId: '1',
                },
                {
                  id: '2',
                  name: 'Хорошо',
                  persistentId: '2',
                },
                {
                  id: '3',
                  name: 'Отлично',
                  persistentId: '3',
                },
              ],
            },
            {
              id: 4,
              name: 'Курица',
              description: 'Курица',
              position: 4,
              variants: [
                {
                  id: '1',
                  name: 'Отвратительно',
                  persistentId: '1',
                },
                {
                  id: '2',
                  name: 'Хорошо',
                  persistentId: '2',
                },
                {
                  id: '3',
                  name: 'Отлично',
                  persistentId: '3',
                },
              ],
            },
          ],
        },
        variantsType: 1, // multiple choice
        skip: 1,
        skip_text: 'Затрудняюсь ответить',
        skip_variant: 1,
        skip_row: 1,
        skip_column: 1,
        showTooltips: 1,
        comment_enabled: 1,
        comment_placeholder: 'Комментарий',
        comment_required: 0,
        matrixSettings: {
          rowsAboveVariants: 1,
        },
      },
      intermediateBlockOptions({
        id: 55,
        question_id: 555,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: '<p>Конечный блок</p>',
          agreement: 0,
          scores_button_text: 'Отчет о тестировании',
          scores_button: 1,
        },
      }),
    ],
  }),
}

export default complexMockData2
