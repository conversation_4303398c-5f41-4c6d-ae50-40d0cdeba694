import {
  ASSESMENT_VARIANT_TYPES,
  INTERMEDIATE_BLOCK_TYPES,
  NPS_QUESTION,
  RATING_NPS_DESIGN,
  RATING_QUESTION,
  SCALE_QUESTION,
  SMILE_QUESTION,
  STAR_VARIANTS_QUESTION,
  STARS_QUESTION,
} from '@entities/question/model/types'
import { POLL_NAVIGATION_TYPE } from '@shared/constants'
import defaultQuestionsData from '../defaultQuestionsData'
import { intermediateBlockOptions } from '../questions/interBlockMockData'

export const ratingsMockData = {
  'points-rating-questions': () => defaultQuestionsData({
    showAdv: false,
    answer: {
      auth_key: 'points-report-ratings',
    },
    design: {
      show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
      disable_question_autoscroll: 1,
    },
    poll: {
      point_system: 1,
      displaySetting: {
        id: 1630,
        poll_id: 50983,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      variables: {
        scoresInterpretationRanges: [
          {
            id: 1386,
            foquz_poll_id: 50983,
            min: 0,
            max: 3,
            result: 'критик',
            description: 'требуется значительное улучшение',
            position: 0,
          },
          {
            id: 1387,
            foquz_poll_id: 50983,
            min: 4,
            max: 7,
            result: 'нейтрал',
            description: 'есть потенциал для улучшения',
            position: 1,
          },
          {
            id: 1388,
            foquz_poll_id: 50983,
            min: 8,
            max: 10,
            result: 'промоутер',
            description: 'отличный результат',
            position: 2,
          },
        ],
      },
      displayPages: [
        {
          id: 2441,
          foquz_poll_id: 50983,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            { id: 301 }, // Stars basic
            { id: 302 }, // Stars with comment
            { id: 333 }, // Scale basic
            { id: 304 }, // Scale with variants
            { id: 305 }, // Scale with skip
            { id: 306 }, // Smile rating basic
            { id: 307 }, // Smile rating with comment
            { id: 320 }, // Star variants basic
            { id: 311 }, // Star variants with assessments
            { id: 308 }, // NPS basic
            { id: 309 }, // NPS with variants
            { id: 312 }, // Rating scale question
            { id: 313 }, // Rating scale with assessment variants
          ],
        },
      ],
    },
    questions: [
      // Stars basic
      {
        type: STARS_QUESTION,
        id: 1,
        question_id: 301,
        description: 'Звездный рейтинг базовый',
        description_html: '<p>Звездный рейтинг базовый</p>',
        isRequired: 0,
        starRatingOptions: {
          count: 5,
          color: '#FFD700',
          labelsArray: ['метка1', 'метка2', 'метка3', 'метка4', 'метка5'],
        },
      },

      // Stars with comment
      {
        type: STARS_QUESTION,
        id: 2,
        question_id: 302,
        description: 'Звездный рейтинг с комментарием',
        description_html: '<p>Звездный рейтинг с комментарием</p>',
        isRequired: 0,
        comment_enabled: 1,
        comment_required: 0,
        placeholderText: 'Пожалуйста, оставьте комментарий',
        starRatingOptions: {
          count: 9,
          color: '#FFD700',
          labelsArray: ['метка1', 'метка2', 'метка3', 'метка4', 'метка5', 'метка6', 'метка7', 'метка8', 'метка9'],
        },
      },
      {
        type: STARS_QUESTION,
        id: 3,
        question_id: 303,
        description: 'Звездный рейтинг с уточняющим вопросом',
        description_html: '<p>Звездный рейтинг с уточняющим вопросом</p>',
        isRequired: 0,
        starRatingOptions: {
          count: 5,
          color: '#FFD700',
          extra_question_rate_from: 1,
          extra_question_rate_to: 4,
          labelsArray: ['метка1', 'метка2', 'метка3', 'метка4', 'метка5'],
        },
        variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
        answerText: 'Почему такая низкая оценка?',
        isHaveCustomField: true,
        self_variant_text: 'Свой вариант',
        detail_answers: [
          { id: 1, variant: 'Качество обслуживания', is_deleted: 0, position: 0 },
          { id: 2, variant: 'Чистота помещения', is_deleted: 0, position: 1 },
          { id: 3, variant: 'Соотношение цена/качество', is_deleted: 0, position: 2 },
        ],
      },

      // Scale basic
      {
        type: SCALE_QUESTION,
        id: 33,
        question_id: 333,
        description: 'Шкала базовая',
        description_html: '<p>Шкала базовая</p>',
        isRequired: 0,
        scaleRatingSetting: {
          start: 0,
          end: 10,
          step: 1,
        },
      },

      // Scale with variants
      {
        type: SCALE_QUESTION,
        id: 4,
        question_id: 304,
        description: 'Шкала с вариантами',
        description_html: '<p>Шкала с вариантами</p>',
        isRequired: 0,
        set_variants: 1,
        scaleRatingSetting: {
          start: 0,
          end: 10,
          step: 1,
        },
        detail_answers: [
          { id: 1, variant: 'Совсем не удовлетворен', is_deleted: 0, position: 0 },
          { id: 2, variant: 'Частично удовлетворен', is_deleted: 0, position: 1 },
          { id: 3, variant: 'Полностью удовлетворен', is_deleted: 0, position: 2 },
        ],
        comment_enabled: 1,
        comment_required: 0,
        placeholderText: 'Пожалуйста, оставьте комментарий',
      },

      // Scale with skip
      {
        type: SCALE_QUESTION,
        id: 5,
        question_id: 305,
        description: 'Шкала с пропуском',
        description_html: '<p>Шкала с пропуском</p>',
        isRequired: 0,
        skip: 1,
        skip_text: 'Затрудняюсь ответить',
        scaleRatingSetting: {
          start: 0,
          end: 10,
          step: 1,
        },
      },

      // Rating scale question
      {
        type: RATING_QUESTION,
        id: 12,
        question_id: 312,
        description: 'Рейтинговая шкала',
        description_html: '<p>Рейтинговая шкала</p>',
        isRequired: 0,
        starRatingOptions: {
          count: 5,
          color: '#FFD700',
          labelsArray: ['Очень плохо', 'Плохо', 'Нормально', 'Хорошо', 'Отлично'],
        },
        comment_enabled: 1,
        comment_required: 0,
        placeholderText: 'Дополнительные комментарии',
        skip: 0,
        skip_text: '',
        random_variants_order: 0,
      },

      // Rating scale with assessment variants
      {
        type: RATING_QUESTION,
        id: 13,
        question_id: 313,
        description: 'Рейтинговая шкала с уточняющими вопросами',
        description_html: '<p>Рейтинговая шкала с уточняющими вопросами</p>',
        isRequired: 0,
        starRatingOptions: {
          count: 5,
          color: '#FFD700',
          labelsArray: ['Очень плохо', 'Плохо', 'Нормально', 'Хорошо', 'Отлично'],
        },
        comment_enabled: 0,
        comment_required: 0,
        placeholderText: 'Дополнительные комментарии',
        skip: 0,
        skip_text: '',
        random_variants_order: 0,
        set_variants: 1,
        variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
        assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
        isHaveCustomField: true,
        answerText: 'Пожалуйста, уточните почему вы поставили такую оценку',
        detail_answers: [
          {
            id: 1,
            variant: 'Качество обслуживания',
            is_deleted: 0,
            position: 0,
            extra_question: 0,
            need_extra: 1,
          },
          {
            id: 2,
            variant: 'Чистота помещения',
            is_deleted: 0,
            position: 1,
            extra_question: 0,
            need_extra: 1,
          },
          {
            id: 3,
            variant: 'Соотношение цена/качество',
            is_deleted: 0,
            position: 2,
            extra_question: 0,
            need_extra: 0,
          },
        ],
      },

      // Smile rating basic
      {
        type: SMILE_QUESTION,
        id: 6,
        question_id: 306,
        description: 'Смайлы базовые',
        description_html: '<p>Смайлы базовые</p>',
        isRequired: 0,
        smileType: 'face',
        count: 5,
        smiles: Array.from({ length: 5 }, (_, index) => ({
          id: index + 1,
          label: `mark ${index + 1}`,
          url: `/uploads/smiles/face/${index + 1}.svg`,
          smile_url: `/uploads/smiles/face/${index + 1}.svg`,
        })),
      },

      // Smile rating with comment
      {
        type: SMILE_QUESTION,
        id: 7,
        question_id: 307,
        description: 'Смайлы с комментарием',
        description_html: '<p>Смайлы с комментарием</p>',
        isRequired: 0,
        smileType: 'face',
        count: 5,
        comment_enabled: 1,
        comment_required: 0,
        placeholderText: 'Пожалуйста, оставьте комментарий',
        smiles: Array.from({ length: 5 }, (_, index) => ({
          id: index + 1,
          label: `mark ${index + 1}`,
          url: `/uploads/smiles/face/${index + 1}.svg`,
          smile_url: `/uploads/smiles/face/${index + 1}.svg`,
        })),
      },

      // Star variants basic
      {
        type: STAR_VARIANTS_QUESTION,
        id: 20,
        question_id: 320,
        description: 'Звездный рейтинг с вариантами',
        description_html: '<p>Звездный рейтинг с вариантами</p>',
        isRequired: 0,
        starRatingOptions: {
          count: 10,
          color: '#FFD700',
          extra_question_rate_from: 1,
          extra_question_rate_to: 4,
        },
        textFieldParam: {
          min: 10,
          max: 50,
        },
        extra_required: 0,
        variantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
        assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
        isHaveCustomField: true,
        set_variants: 1,
        skip_variant: 1,
        skip: 1,
        answerText: 'Почему такая низкая оценка?',
        detail_answers: [
          {
            id: 1,
            variant: 'Качество обслуживания',
            is_deleted: 0,
            position: 0,
            extra_question: 0,
            need_extra: 1,
          },
          {
            id: 2,
            variant: 'Чистота помещения',
            is_deleted: 0,
            position: 1,
            extra_question: 0,
            need_extra: 1,
          },
          {
            id: 3,
            variant: 'Соотношение цена/качество',
            is_deleted: 0,
            position: 2,
            extra_question: 0,
            need_extra: 0,
          },
        ],
        comment_enabled: 1,
        comment_required: 0,
        placeholderText: 'Дополнительные комментарии',
        skip: 0,
        skip_text: '',
        random_variants_order: 0,
      },

      // Star variants with assessments
      {
        type: STAR_VARIANTS_QUESTION,
        id: 11,
        question_id: 311,
        description: 'Звездный рейтинг с уточняющими вопросами',
        description_html: '<p>Звездный рейтинг с уточняющими вопросами</p>',
        isRequired: 0,
        starRatingOptions: {
          count: 5,
          color: '#FFD700',
          extra_question_rate_from: 1,
          extra_question_rate_to: 4,
        },
        textFieldParam: {
          min: 10,
          max: 50,
        },
        extra_required: 1,
        answerText: 'Почему такая низкая оценка?',
        variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
        assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
        isHaveCustomField: true,
        set_variants: 1,
        skip_variants: 1,
        skip: 1,
        detail_answers: [
          {
            id: 1,
            variant: 'Качество обслуживания',
            is_deleted: 0,
            position: 0,
            extra_question: 0,
            need_extra: 1,
          },
          {
            id: 2,
            variant: 'Чистота помещения',
            is_deleted: 0,
            position: 1,
            extra_question: 0,
            need_extra: 1,
          },
          {
            id: 3,
            variant: 'Плохое обслуживание',
            is_deleted: 0,
            position: 2,
            extra_question: 1,
            need_extra: 0,
          },
          {
            id: 4,
            variant: 'Плохая чистота',
            is_deleted: 0,
            position: 3,
            extra_question: 1,
            need_extra: 0,
          },
          {
            id: 5,
            variant: 'Плохое соотношение цена/качество',
            is_deleted: 0,
            position: 4,
            extra_question: 1,
            need_extra: 0,
          },
        ],
        comment_enabled: 0,
        comment_required: 0,
        placeholderText: 'Дополнительные комментарии',
        skip_text: '',
        random_variants_order: 0,
      },

      // NPS basic
      {
        type: NPS_QUESTION,
        id: 8,
        question_id: 308,
        description: 'NPS базовый',
        description_html: '<p>NPS базовый</p>',
        isRequired: 0,
        npsRatingSetting: {
          design: RATING_NPS_DESIGN.COLORED,
          start_point_color: '#f96261',
          end_point_color: '#00c968',
          start_label: null,
          end_label: null,
        },
        comment_enabled: 1,
        comment_required: 0,
        placeholderText: 'Пожалуйста, оставьте комментарий',
      },

      // NPS with variants
      {
        type: NPS_QUESTION,
        id: 9,
        question_id: 309,
        description: 'NPS с вариантами',
        description_html: '<p>NPS с вариантами</p>',
        isRequired: 0,
        set_variants: 1,
        npsRatingSetting: {
          design: RATING_NPS_DESIGN.COLORED,
          start_point_color: '#f96261',
          end_point_color: '#00c968',
          start_label: null,
          end_label: null,
        },
        comment_enabled: 1,
        comment_required: 0,
        placeholderText: 'Пожалуйста, оставьте комментарий',
        skip: 1,
        detail_answers: [
          { id: 1, variant: 'Критик', is_deleted: 0, position: 0 },
          { id: 2, variant: 'Нейтрал', is_deleted: 0, position: 1 },
          { id: 3, variant: 'Промоутер', is_deleted: 0, position: 2 },
        ],
      },
      intermediateBlockOptions({
        id: 25,
        question_id: 225,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: '<p>Конечный блок</p>',
          agreement: 0,
          scores_button_text: 'Отчет о тестировании',
          scores_button: 1,
        },
        endScreenImages: [],
      }),
    ],
  }),
}

export default ratingsMockData
