import {
  CLASSIFIER_QUESTION,
  DIFF_QUESTION,
  FILE_QUESTION,
  FILIALS_QUESTION,
  INTER_BLOCK,
  INTERMEDIATE_BLOCK_TYPES,
  PRIORITY_QUESTION,
  VARIANTS_QUESTION,
} from '@entities/question/model/types'
import { POLL_NAVIGATION_TYPE } from '@shared/constants'
import defaultQuestionsData from '../defaultQuestionsData'
import { classifierQuestionOptions } from '../questions/classifierMockData'
import { intermediateBlockOptions } from '../questions/interBlockMockData'

export const additionalMockData = {
  'points-additional-questions': () => defaultQuestionsData({
    showAdv: false,
    design: {
      show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
      disable_question_autoscroll: 1,
    },
    answer: {
      auth_key: 'points-report-additional-questions',
    },
    poll: {
      point_system: 1,
      displaySetting: {
        id: 1630,
        poll_id: 50983,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      variables: {
        scoresInterpretationRanges: [
          {
            id: 1386,
            foquz_poll_id: 50983,
            min: 0,
            max: 5,
            result: 'начальный уровень',
            description: 'требуется дополнительное обучение',
            position: 0,
          },
          {
            id: 1387,
            foquz_poll_id: 50983,
            min: 6,
            max: 10,
            result: 'средний уровень',
            description: 'хорошее понимание материала',
            position: 1,
          },
          {
            id: 1388,
            foquz_poll_id: 50983,
            min: 11,
            max: 15,
            result: 'продвинутый уровень',
            description: 'отличное владение материалом',
            position: 2,
          },
        ],
      },
      displayPages: [
        {
          id: 2441,
          foquz_poll_id: 50983,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            { id: 301 }, // Inter block
            { id: 302 }, // Filials
            { id: 303 }, // Basic variants
            { id: 304 }, // Media variants
            { id: 305 }, // Priority
            { id: 306 }, // Diff
            { id: 342 }, // Diff
            { id: 343 }, // Diff
            { id: 344 }, // Diff
            { id: 345 }, // Diff
            { id: 346 }, // Diff
            { id: 347 }, // Diff
            { id: 348 }, // Diff
            { id: 307 }, // Classifier
            { id: 308 }, // File upload
          ],
        },
      ],
    },
    questions: [
      // Inter block question
      {
        type: INTER_BLOCK,
        id: 1,
        question_id: 301,
        description: 'Интерблок. Промежуточный текст',
        description_html: '<p>Интерблок. Промежуточный текст</p>',
        isRequired: 0,
        content: 'Это промежуточный текстовый блок между вопросами',
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE,
          text: 'Это промежуточный текстовый блок между вопросами',
          agreement: 0,
          logos_backcolor: '#FFFFFF',
        },
        endScreenImages: [],
        showQuestion: true,
      },
      // Filials question -> replaced with classifier question
      {
        ...classifierQuestionOptions({
          dictionary_list_type: 'list',
          description_html: '<p>Выбор филиала</p>',
        }),
        type: FILIALS_QUESTION,
        isRequired: 0,
        id: 2,
        question_id: 302,
      },
      // Basic variants question
      {
        type: VARIANTS_QUESTION,
        id: 3,
        question_id: 303,
        description: 'Варианты. Выберите предпочтительный способ связи',
        description_html: '<p>Варианты. Выберите предпочтительный способ связи</p>',
        isRequired: 0,
        variantsType: 1, // multiple choice
        detail_answers: [
          {
            id: 1,
            variant: 'Телефон',
            description: '',
            position: 1,
            is_deleted: 0,
            comment_required: 0,
          },
          {
            id: 2,
            variant: 'Email',
            description: '',
            position: 2,
            is_deleted: 0,
            comment_required: 0,
          },
          {
            id: 3,
            variant: 'Мессенджер',
            description: '',
            position: 3,
            is_deleted: 0,
            comment_required: 0,
          },
          {
            id: 4,
            variant: 'Личная встреча',
            description: '',
            position: 4,
            is_deleted: 0,
            comment_required: 0,
          },
        ],
        show_tooltips: 0,
        self_variant_comment_required: 0,
        self_variant_text: 'Свой вариант',
        self_variant_placeholder_text: 'Введите свой вариант',
        self_variant_description: '',
        self_variant_nothing: 0,
        self_variant_param: { min: 0, max: 250 },
        isHaveCustomField: 1,
        skip: 0,
        dropdownVariants: 0,
        variants_with_files: 0,
        comment_enabled: 1,
        comment_required: 0,
        comment_label: 'Ваш комментарий',
        placeholderText: 'Введите комментарий',
        textFieldParam: {
          min: 0,
          max: 250,
        },
        showQuestion: true,
        questionLogic: [],
        questionViewLogic: [],
      },
      // Basic variants question
      {
        type: VARIANTS_QUESTION,
        id: 4,
        question_id: 304,
        description: 'Варианты с медиа.',
        description_html: '<p>Варианты с медиа.</p>',
        isRequired: 0,
        variantsType: 1, // multiple choice
        detail_answers: [
          { id: 1, variant: 'Вариант 1', is_deleted: 0, file_url: '/uploads/img-preview-1.jpeg', preview_url: '/uploads/img-preview-1.jpeg' },
          { id: 2, variant: 'Вариант 2', is_deleted: 0, file_url: '/uploads/img-preview-2.jpeg', preview_url: '/uploads/img-preview-2.jpeg' },
          { id: 3, variant: 'Вариант 3', is_deleted: 0, file_url: '/uploads/img-preview-3.jpeg', preview_url: '/uploads/img-preview-3.jpeg' },
        ],
        show_tooltips: 0,
        self_variant_comment_required: 0,
        self_variant_text: 'Свой вариант',
        self_variant_placeholder_text: 'Введите свой вариант',
        self_variant_description: '',
        self_variant_nothing: 0,
        self_variant_param: { min: 0, max: 250 },
        isHaveCustomField: 1,
        skip: 0,
        dropdownVariants: 0,
        variants_with_files: 1,
        comment_enabled: 1,
        comment_required: 0,
        comment_label: 'Ваш комментарий',
        placeholderText: 'Введите комментарий',
        textFieldParam: {
          min: 0,
          max: 250,
        },
        showQuestion: true,
        questionLogic: [],
        questionViewLogic: [],
      },
      // Priority question
      {
        type: PRIORITY_QUESTION,
        id: 5,
        question_id: 305,
        description: 'Приоритеты. Расставьте по важности',
        description_html: '<p>Приоритеты. Расставьте по важности</p>',
        isRequired: 0,
        reorder_required: 0,
        detail_answers: [
          {
            id: 1,
            variant: 'Цена',
            position: 0,
            is_deleted: 0,
          },
          {
            id: 2,
            variant: 'Качество',
            position: 1,
            is_deleted: 0,
          },
          {
            id: 3,
            variant: 'Скорость',
            position: 2,
            is_deleted: 0,
          },
          {
            id: 4,
            variant: 'Надежность',
            position: 3,
            is_deleted: 0,
          },
        ],
        comment_enabled: 1,
        comment_required: 0,
        comment_label: 'Ваш комментарий',
        placeholderText: 'Введите комментарий',
        textFieldParam: {
          min: 0,
          max: 1000,
        },
        showQuestion: true,
        questionLogic: [],
        questionViewLogic: [],
      },

      // Diff question
      {
        type: DIFF_QUESTION,
        id: 6,
        question_id: 306,
        description: 'Сравнение. Оцените разницу',
        description_html: '<p>Сравнение. Оцените разницу</p>',
        isRequired: 0,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        skip_variant: 0,
        enableGallery: false,
        semDifSetting: {
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            question_id: 306,
            start_label: 'Неудобно',
            end_label: 'Удобно',
            position: 1,
          },
          {
            id: 2,
            question_id: 306,
            start_label: 'Плохая функциональность',
            end_label: 'Отличная функциональность',
            position: 2,
          },
          {
            id: 3,
            question_id: 306,
            start_label: 'Плохой дизайн',
            end_label: 'Отличный дизайн',
            position: 3,
          },
        ],
        comment_enabled: 0,
        comment_required: 0,
        comment_label: 'Ваш комментарий',
        placeholderText: 'Введите ваш комментарий здесь',
        gallery: [],
        questionLogic: [],
        questionViewLogic: [],
        showQuestion: true,
        skipped: 0,
        wrongCondition: false,
      },
      // Diff question - Single row circles
      {
        type: DIFF_QUESTION,
        id: 42,
        question_id: 342,
        description: 'Семантический дифференциал. Одна строка, круги',
        description_html: '<p>Семантический дифференциал. Одна строка, круги</p>',
        isRequired: 0,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        skip_variant: 0,
        enableGallery: false,
        semDifSetting: {
          form: 'circle',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            question_id: 342,
            start_label: '',
            end_label: '',
            position: 1,
          },
        ],
        showQuestion: true,
        skipped: 0,
        wrongCondition: false,
      },

      // Diff question - Single row with right label
      {
        type: DIFF_QUESTION,
        id: 43,
        question_id: 343,
        description: 'Семантический дифференциал. Одна строка, метка справа',
        description_html: '<p>Семантический дифференциал. Одна строка, метка справа</p>',
        isRequired: 0,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        skip_variant: 0,
        enableGallery: false,
        semDifSetting: {
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            question_id: 343,
            start_label: '',
            end_label: 'Отличная упаковка',
            position: 1,
          },
        ],
        showQuestion: true,
        skipped: 0,
        wrongCondition: false,
      },

      // Diff question - Multiple rows
      {
        type: DIFF_QUESTION,
        id: 44,
        question_id: 344,
        description: 'Семантический дифференциал. Несколько строк',
        description_html: '<p>Семантический дифференциал. Несколько строк</p>',
        isRequired: 0,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        skip_variant: 0,
        enableGallery: false,
        semDifSetting: {
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            question_id: 344,
            start_label: 'Ужасная упаковка',
            end_label: 'Отличная упаковка',
            position: 1,
          },
          {
            id: 2,
            question_id: 344,
            start_label: 'Отвратительная работа оператора на приеме заказа, мы в шоке',
            end_label: 'Отличная работа оператора',
            position: 2,
          },
          {
            id: 3,
            question_id: 344,
            start_label: 'Отвратительная доставка',
            end_label: 'Прекрасная доставка',
            position: 3,
          },
        ],
        showQuestion: true,
        skipped: 0,
        wrongCondition: false,
      },

      // Diff question - Partially labeled
      {
        type: DIFF_QUESTION,
        id: 45,
        question_id: 345,
        description: 'Семантический дифференциал. Частично без меток',
        description_html: '<p>Семантический дифференциал. Частично без меток</p>',
        isRequired: 0,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        skip_variant: 0,
        enableGallery: false,
        semDifSetting: {
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            question_id: 345,
            start_label: 'Ужасная упаковка',
            end_label: '',
            position: 1,
          },
          {
            id: 2,
            question_id: 345,
            start_label: '',
            end_label: 'Отличная работа оператора',
            position: 2,
          },
          {
            id: 3,
            question_id: 345,
            start_label: 'Отвратительная доставка',
            end_label: '',
            position: 3,
          },
        ],
        showQuestion: true,
        skipped: 0,
        wrongCondition: false,
      },

      // Diff question - Left labels only
      {
        type: DIFF_QUESTION,
        id: 46,
        question_id: 346,
        description: 'Семантический дифференциал. Метки с одной стророны',
        description_html: '<p>Семантический дифференциал. Метки с одной стророны</p>',
        isRequired: 0,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        skip_variant: 0,
        enableGallery: false,
        semDifSetting: {
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            question_id: 346,
            start_label: 'Упаковка',
            end_label: '',
            position: 1,
          },
          {
            id: 2,
            question_id: 346,
            start_label: 'Работа оператора',
            end_label: '',
            position: 2,
          },
          {
            id: 3,
            question_id: 346,
            start_label: 'Доставка',
            end_label: '',
            position: 3,
          },
        ],
        showQuestion: true,
        skipped: 0,
        wrongCondition: false,
      },

      // Diff question - Circles with labels
      {
        type: DIFF_QUESTION,
        id: 47,
        question_id: 347,
        description: 'Семантический дифференциал. Круги',
        description_html: '<p>Семантический дифференциал. Круги</p>',
        isRequired: 0,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        skip_variant: 0,
        enableGallery: false,
        semDifSetting: {
          form: 'circle',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            question_id: 347,
            start_label: 'Ужасная упаковка',
            end_label: 'Отличная упаковка',
            position: 1,
          },
          {
            id: 2,
            question_id: 347,
            start_label: 'Отвратительная работа оператора на приеме заказа',
            end_label: 'Отличная работа оператора',
            position: 2,
          },
          {
            id: 3,
            question_id: 347,
            start_label: 'Отвратительная доставка',
            end_label: 'Прекрасная доставка',
            position: 3,
          },
          {
            id: 4,
            question_id: 347,
            start_label: 'Плохой вкус',
            end_label: 'Отличный вкус. Живая сессия, следовательно, диссонирует мелодический аккорд.',
            position: 4,
          },
          {
            id: 5,
            question_id: 347,
            start_label: 'Продукты несвежие',
            end_label: 'Свежие продукты',
            position: 5,
          },
        ],
        showQuestion: true,
        skipped: 0,
        wrongCondition: false,
      },
      // Classifier question
      {
        type: CLASSIFIER_QUESTION,
        id: 7,
        question_id: 307,
        description: 'Классификатор. Древовидный',
        description_html: '<p>Классификатор. Древовидный</p>',
        isRequired: 0,
        dictionaryTree: {
          'Высокий приоритет': {
            id: 1,
            title: 'Высокий приоритет',
            description: '',
            position: 1,
            isCategory: true,
            children: {
              'Задача 1': {
                id: 99,
                title: 'Задача 1',
                description: '',
                position: 1,
                isCategory: false,
              },
              'Задача 2': {
                id: 99,
                title: 'Задача 2',
                description: '',
                position: 2,
                isCategory: false,
              },
            },
          },
          'Средний приоритет': {
            id: 2,
            title: 'Средний приоритет',
            description: '',
            position: 2,
            isCategory: true,
            children: {
              'Задача 3': {
                id: 799077,
                title: 'Задача 3',
                description: '',
                position: 1,
                isCategory: false,
              },
            },
          },
          'Низкий приоритет': {
            id: 3,
            title: 'Низкий приоритет',
            description: '',
            position: 3,
            isCategory: true,
            children: {
              'Задача 4': {
                id: 799079,
                title: 'Задача 4',
                description: '',
                position: 1,
                isCategory: false,
              },
            },
          },
          'Задача 5': {
            id: 799080,
            title: 'Задача 5',
            description: '',
            position: 4,
            isCategory: false,
          },
        },
        dictionary_sort: 'default',
        dictionary_list_type: 'tree',
        dropdownVariants: 0,
        assessmentVariantsType: 1,
        disable_select_category: 0,
        show_tooltips: 1,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        comment_enabled: 0,
        comment_required: 0,
        comment_label: 'Дополнительные комментарии',
        placeholderText: 'Введите ваши комментарии здесь',
        textFieldParam: {
          min: 0,
          max: 250,
        },
        showQuestion: true,
        questionLogic: [],
        questionViewLogic: [],
      },
      // File upload question
      {
        type: FILE_QUESTION,
        id: 8,
        question_id: 308,
        description: 'Загрузка файла. Прикрепите документ',
        description_html: '<p>Загрузка файла. Прикрепите документ</p>',
        isRequired: 0,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        maxFileSize: 5242880, // 5MB in bytes
        multiple: false,
      },
      intermediateBlockOptions({
        id: 55,
        question_id: 555,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: '<p>Конечный блок</p>',
          agreement: 0,
          scores_button_text: 'Отчет о тестировании',
          scores_button: 1,
        },
      }),
    ],
  }),
}

export default additionalMockData
