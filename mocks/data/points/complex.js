import {
  GALLERY_QUESTION,
  INTERMEDIATE_BLOCK_TYPES,
  MATRIX_QUESTION,
  MEDIA_VARIANTS_QUESTION,
} from '@entities/question/model/types'
import { POLL_NAVIGATION_TYPE } from '@shared/constants'
import defaultQuestionsData from '../defaultQuestionsData'
import { intermediateBlockOptions } from '../questions/interBlockMockData'

export const complexMockData = {
  'points-complex-questions': () => defaultQuestionsData({
    showAdv: false,
    answer: {
      auth_key: 'points-report-complex-questions',
    },
    design: {
      show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
      disable_question_autoscroll: 1,
    },
    poll: {
      point_system: 1,
      displaySetting: {
        id: 1631,
        poll_id: 50984,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      variables: {
        scoresInterpretationRanges: [
          {
            id: 1386,
            foquz_poll_id: 50984,
            min: 1,
            max: 3,
            result: 'удовлетворительно',
            description: 'требуется улучшение',
            position: 0,
          },
          {
            id: 1387,
            foquz_poll_id: 50984,
            min: 4,
            max: 6,
            result: 'хорошо',
            description: 'хороший результат',
            position: 1,
          },
          {
            id: 1388,
            foquz_poll_id: 50984,
            min: 7,
            max: 10,
            result: 'отлично',
            description: 'превосходный результат',
            position: 2,
          },
        ],
      },
      displayPages: [
        {
          id: 2442,
          foquz_poll_id: 50984,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            { id: 401 }, // Matrix basic
            { id: 402 }, // Matrix with skip row
            { id: 404 }, // Gallery basic
            { id: 405 }, // Gallery with multiple choice
            { id: 406 }, // Media variants single choice
            { id: 407 }, // Media variants multiple choice
          ],
        },
      ],
    },
    questions: [
      // Matrix basic
      {
        type: MATRIX_QUESTION,
        id: 1,
        question_id: 401,
        description: 'Матрица базовая',
        description_html: '<p>Матрица базовая</p>',
        isRequired: 0,
        matrixSettings: {
          rows: ['Качество продукта', 'Скорость доставки', 'Работа поддержки'],
          cols: ['Плохо', 'Нормально', 'Хорошо', 'Отлично'],
          minRowsReq: 3,
          multiple_choice: '0',
          type: 'standart',
        },
      },

      // Matrix with skip row
      {
        type: MATRIX_QUESTION,
        id: 2,
        question_id: 402,
        description: 'Матрица с пропуском строки и уточняющим вопросом',
        description_html: '<p>Матрица с пропуском строки и уточняющим вопросом</p>',
        isRequired: 0,
        skip_variant: 1,
        variantsType: 1,
        skip_text: 'Затрудняюсь ответить',
        answerText: 'Почему вы не удовлетворены?',
        isHaveCustomField: 1,
        matrixSettings: {
          rows: ['Удобство использования', 'Цена', 'Функциональность'],
          cols: ['Не удовлетворен', 'Частично удовлетворен', 'Полностью удовлетворен'],
          extra_question: {
            rows: ['Удобство использования', 'Цена', 'Функциональность'],
            cols: ['Не удовлетворен', 'Частично удовлетворен', 'Полностью удовлетворен'],
          },
          minRowsReq: 0,
          multiple_choice: '1',
          type: 'standart',
        },
        detail_answers: [
          {
            id: 1,
            question: 'Плохое удобство использования',
            is_deleted: 0,
            extra_question: 1,
          },
          {
            id: 2,
            question: 'Цена',
            is_deleted: 0,
            extra_question: 1,
          },
          {
            id: 3,
            question: 'Функциональность',
            is_deleted: 0,
            extra_question: 1,
          },
        ],
      },

      // Gallery basic
      {
        type: GALLERY_QUESTION,
        id: 4,
        question_id: 404,
        description: 'Галерея базовая',
        description_html: '<p>Галерея базовая</p>',
        isRequired: 0,
        variantsType: 0, // single choice
        gallery: [
          {
            id: 1,
            src: '/uploads/clouds.jpg',
            url: '/uploads/clouds.jpg',
            poster: '/uploads/clouds.jpg',
            description: 'Изображение 1',
          },
          {
            id: 2,
            src: '/uploads/summer.jpg',
            url: '/uploads/summer.jpg',
            poster: '/uploads/summer.jpg',
            description: 'Изображение 2',
          },
          {
            id: 3,
            src: '/uploads/summer.jpg',
            url: '/uploads/summer.jpg',
            poster: '/uploads/summer.jpg',
            description: 'Изображение 3',
          },
        ],
      },
      // Media variants single choice
      {
        type: MEDIA_VARIANTS_QUESTION,
        id: 6,
        question_id: 406,
        description: 'Медиа варианты одиночный выбор',
        description_html: '<p>Медиа варианты одиночный выбор</p>',
        isRequired: 0,
        variantsType: 0, // single choice
        gallery: [
          {
            id: 1,
            src: '/uploads/clouds.jpg',
            url: '/uploads/clouds.jpg',
            poster: '/uploads/clouds.jpg',
            description: 'Изображение 1',
          },
          {
            id: 2,
            src: '/uploads/summer.jpg',
            url: '/uploads/summer.jpg',
            poster: '/uploads/summer.jpg',
            description: 'Изображение 2',
          },
        ],
      },

      // Media variants multiple choice
      {
        type: MEDIA_VARIANTS_QUESTION,
        id: 7,
        question_id: 407,
        description: 'Медиа варианты множественный выбор',
        description_html: '<p>Медиа варианты множественный выбор</p>',
        isRequired: 0,
        variantsType: 1, // multiple choice
        isHaveComment: true,
        comment_required: 0,
        placeholderText: 'Опишите причину выбора',
        gallery: [
          {
            id: 1,
            src: '/uploads/clouds.jpg',
            url: '/uploads/clouds.jpg',
            poster: '/uploads/clouds.jpg',
            description: 'Изображение 1',
          },
          {
            id: 2,
            src: '/uploads/summer.jpg',
            url: '/uploads/summer.jpg',
            poster: '/uploads/summer.jpg',
            description: 'Изображение 2',
          },
          {
            id: 3,
            src: '/uploads/summer.jpg',
            url: '/uploads/summer.jpg',
            poster: '/uploads/summer.jpg',
            description: 'Изображение 3',
          },
        ],
      },
      intermediateBlockOptions({
        id: 55,
        question_id: 555,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: '<p>Конечный блок</p>',
          agreement: 0,
          scores_button_text: 'Отчет о тестировании',
          scores_button: 1,
        },
      }),
    ],
  }),
}

export default complexMockData
