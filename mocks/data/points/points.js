import {
  ADDRESS_QUESTION,
  DATE_QUESTION,
  INTERMEDIATE_BLOCK_TYPES,
  TEXT_QUESTION,
  TEXT_VARIANT_TYPES,
} from '@entities/question/model/types'
import { POLL_NAVIGATION_TYPE } from '@shared/constants'
import MaskTypes from '@shared/constants/maskTypes'
import defaultQuestionsData from '../defaultQuestionsData'
import { intermediateBlockOptions } from '../questions/interBlockMockData'

export const pointsMockData = {
  'points-text-questions': () => defaultQuestionsData({
    showAdv: false,
    answer: {
      auth_key: 'default',
    },
    design: {
      show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
      disable_question_autoscroll: 1,
    },
    poll: {
      point_system: 1,
      status: 'open',
      displaySetting: {
        id: 1629,
        poll_id: 50982,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      variables: {
        scoresInterpretationRanges: [
          {
            id: 1386,
            foquz_poll_id: 50982,
            min: 1,
            max: 2,
            result: 'нормально',
            description: 'надо немного потренироваться',
            position: 0,
          },
          {
            id: 1387,
            foquz_poll_id: 50982,
            min: 3,
            max: 4,
            result: 'отлично!',
            description: 'вы справились на отлично',
            position: 1,
          },
        ],
      },
      displayPages: [
        {
          id: 2440,
          foquz_poll_id: 50982,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            { id: 201 }, // Simple text
            { id: 202 }, // Multiline text
            { id: 203 }, // URL text
            { id: 204 }, // FIO text
            { id: 205 }, // Date text
            { id: 206 }, // Day and month text
            { id: 207 }, // Period text
            { id: 208 }, // Phone text
            { id: 209 }, // Email text
            { id: 210 }, // Date question
            { id: 211 }, // Date question
            { id: 212 }, // Date question
            { id: 213 }, // Date question
            { id: 214 }, // Date question
            { id: 215 }, // Address question
          ],
        },
      ],
    },
    questions: [
      // Simple text question
      {
        type: TEXT_QUESTION,
        id: 1,
        question_id: 201,
        description: 'Текстовый. Как называется наша компания?',
        description_html: '<p>Текстовый. Как называется наша компания?</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.NoMask,
        isRequired: 0,
        placeholderText: 'Введите название компании',
      },
      // // Multiline text question
      {
        type: TEXT_QUESTION,
        id: 2,
        question_id: 202,
        description: 'Текстовый многострочный. Как называется наша компания?',
        description_html: '<p>Текстовый многострочный. Как называется наша компания?</p>',
        variantsType: TEXT_VARIANT_TYPES.TEXTAREA,
        maskType: MaskTypes.NoMask,
        isRequired: 0,
        textFieldParam: {
          min: 10,
          max: 1000,
        },
        placeholderText: 'Введите текст',
      },
      // URL text question
      {
        type: TEXT_QUESTION,
        id: 3,
        question_id: 203,
        description: 'Текстовый. Сайт компании',
        description_html: '<p>Текстовый. Сайт компании</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Site,
        isRequired: 0,
        placeholderText: 'Введите URL сайта',
      },
      // FIO text question
      {
        type: TEXT_QUESTION,
        id: 4,
        question_id: 204,
        description: 'Текстовый. ФИО',
        description_html: '<p>Текстовый. ФИО</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Name,
        isRequired: 0,
        maskConfig: {
          name: {
            visible: true,
            required: false,
            placeholderText: 'Имя',
            minLength: 0,
            maxLength: 50,
          },
          surname: {
            visible: true,
            required: false,
            placeholderText: 'Фамилия',
            minLength: 0,
            maxLength: 50,
          },
          patronymic: {
            visible: true,
            required: false,
            placeholderText: 'Отчество',
            minLength: 0,
            maxLength: 50,
          },
        },
      },
      // Date text question
      {
        type: TEXT_QUESTION,
        id: 5,
        question_id: 205,
        description: 'Текстовый. Дата',
        description_html: '<p>Текстовый. Дата</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Date,
        isRequired: 0,
        placeholderText: 'DD.MM.YYYY',
      },
      // Day and month text question
      {
        type: TEXT_QUESTION,
        id: 6,
        question_id: 206,
        description: 'Текстовый. День и месяц',
        description_html: '<p>Текстовый. День и месяц</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.DateMonth,
        isRequired: 0,
        placeholderText: 'DD.MM',
      },
      // Period text question
      {
        type: TEXT_QUESTION,
        id: 7,
        question_id: 207,
        description: 'Текстовый. Период',
        description_html: '<p>Текстовый. Период</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Period,
        isRequired: 0,
        placeholderText: 'DD.MM.YYYY',
      },
      // Phone text question
      {
        type: TEXT_QUESTION,
        id: 8,
        question_id: 208,
        description: 'Текстовый. Телефон',
        description_html: '<p>Текстовый. Телефон</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Phone,
        isRequired: 0,
        placeholderText: '+7 (___) ___-____',
      },
      // Email text question
      {
        type: TEXT_QUESTION,
        id: 9,
        question_id: 209,
        description: 'Текстовый. Email',
        description_html: '<p>Текстовый. Email</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Email,
        isRequired: 0,
        placeholderText: '<EMAIL>',
      },
      // Date question
      {
        type: DATE_QUESTION,
        id: 10,
        question_id: 210,
        description: 'Дата. Самый прибыльный день в году',
        description_html: '<p>Дата. Самый прибыльный день в году</p>',
        maskType: MaskTypes.Date,
        isRequired: 0,
        dateType: 0,
        only_date_month: 0,
      },
      // Date question
      {
        type: DATE_QUESTION,
        id: 11,
        question_id: 211,
        description: 'Дата. День и месяц',
        description_html: '<p>Дата. День и месяц</p>',
        maskType: MaskTypes.DateMonth,
        isRequired: 0,
        dateType: 0,
        only_date_month: 1,
      },
      {
        type: DATE_QUESTION,
        id: 12,
        question_id: 212,
        description: 'Дата. Время',
        description_html: '<p>Дата. Время</p>',
        maskType: MaskTypes.Time,
        isRequired: 0,
        dateType: 1,
        only_date_month: 0,
      },
      {
        type: DATE_QUESTION,
        id: 13,
        question_id: 213,
        description: 'Дата. Дата и время',
        description_html: '<p>Дата. Дата и время</p>',
        maskType: MaskTypes.DateTime,
        isRequired: 0,
        dateType: 2,
        only_date_month: 0,
      },
      // Date month time question
      {
        type: DATE_QUESTION,
        id: 14,
        question_id: 214,
        description: 'Дата. День и месяц и время',
        description_html: '<p>Дата. День и месяц и время</p>',
        maskType: MaskTypes.DateMonthTime,
        isRequired: 0,
        dateType: 2,
        only_date_month: 1,
      },
      // Address question
      {
        type: ADDRESS_QUESTION,
        id: 15,
        question_id: 215,
        description: 'Адрес. Выберите расположение филиала',
        description_html: '<p>Адрес. Выберите расположение филиала</p>',
        isRequired: 0,
        addressType: 'full',
        placeholderText: 'Введите адрес',
      },
      intermediateBlockOptions({
        id: 16,
        question_id: 216,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: '<p>Конечный блок</p>',
          agreement: 0,
          scores_button_text: 'Отчет о тестировании',
          scores_button: 1,
        },
        endScreenImages: [],
      }),
    ],

  }),
}

export default pointsMockData
