import type { GetResultsAddressQuestion, GetResultsDateQuestion, GetResultsGalleryQuestion, GetResultsMatrix3dQuestion, GetResultsMatrixQuestion, GetResultsMediaVariantsQuestion, GetResultsNpsQuestion, GetResultsQuizQuestion, GetResultsRatingScaleQuestion, GetResultsResponse, GetResultsScaleQuestion, GetResultsSmileQuestion, GetResultsStarsQuestion, GetResultsStarVariantsQuestion, GetResultsTextQuestion } from '@/features/points/schemas/getResults'
import { MaskTypes } from '@shared/constants/maskTypes'

const defaultData = {
  id: 1,
  displaySettings: null,
  displayPages: [],
  questions: [
    // Simple text question
    {
      type: 2,
      id: 201,
      description: 'Текстовый. Как называется наша компания?',
      description_html: '<p>Текстовый. Как называется наша компания?</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.NoMask,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: 'Тестовый ответ',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // Multiline text
    {
      type: 2,
      id: 202,
      description: 'Текстовый многострочный. Как называется наша компания?',
      description_html: '<p>Текстовый многострочный. Как называется наша компания?</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.NoMask,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: 'Многострочный\nтестовый\nответ',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // URL text
    {
      type: 2,
      id: 203,
      description: 'Текстовый. Сайт компании',
      description_html: '<p>Текстовый. Сайт компании</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.Site,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: 'https://example.com',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // FIO text
    {
      type: 2,
      id: 204,
      description: 'Текстовый. ФИО',
      description_html: '<p>Текстовый. ФИО</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.Name,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: {
          name: 'Иван',
          surname: 'Иванов',
          patronymic: 'Иванович',
        },
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // Date text
    {
      type: 2,
      id: 205,
      description: 'Текстовый. Дата',
      description_html: '<p>Текстовый. Дата</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.Date,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: '01.01.2024',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // Day and month text
    {
      type: 2,
      id: 206,
      description: 'Текстовый. День и месяц',
      description_html: '<p>Текстовый. День и месяц</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.DateMonth,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: '3 января',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // Period text
    {
      type: 2,
      id: 207,
      description: 'Текстовый. Период',
      description_html: '<p>Текстовый. Период</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.Period,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: '01.01.2024-31.12.2024',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // Phone text
    {
      type: 2,
      id: 208,
      description: 'Текстовый. Телефон',
      description_html: '<p>Текстовый. Телефон</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.Phone,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: '+7 (999) 888-77-66',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // Email text
    {
      type: 2,
      id: 209,
      description: 'Текстовый. Email',
      description_html: '<p>Текстовый. Email</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      mask_type: MaskTypes.Email,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: '<EMAIL>',
        rightAnswer: null,
      },
    } as GetResultsTextQuestion,
    // Date questions - updated to match schema
    {
      type: 3,
      id: 210,
      description: 'Дата. Самый прибыльный день в году',
      description_html: '<p>Дата. Самый прибыльный день в году</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 0,
        points: 1,
        max_points: 1,
        comment: null,
        answer: '01.01.2024',
        correct_answer: {
          text: '01.01.2024',
        },
        rightAnswer: true,
      },
      rightAnswer: {
        decodedAnswer: {
          date: '01.01.2024',
          time: null,
          dateFormat: null,
        },
      },
    } as GetResultsDateQuestion,
    {
      type: 3,
      id: 211,
      description: 'Дата. День и месяц',
      description_html: '<p>Дата. День и месяц</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 0,
        points: 0,
        max_points: 1,
        comment: null,
        answer: '4 января',
        correct_answer: {
          text: '1 января',
        },
        rightAnswer: false,
      },
      rightAnswer: {
        decodedAnswer: {
          date: '1 января',
          time: null,
          dateFormat: null,
        },
      },
    } as GetResultsDateQuestion,
    {
      type: 3,
      id: 212,
      description: 'Дата. Время',
      description_html: '<p>Дата. Время</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 0,
        points: 0,
        max_points: 1,
        comment: null,
        answer: '14:00',
        correct_answer: {
          text: '15:00',
        },
        rightAnswer: false,
      },
      rightAnswer: {
        decodedAnswer: {
          date: null,
          time: '15:00',
          dateFormat: null,
        },
      },
    } as GetResultsDateQuestion,
    {
      type: 3,
      id: 213,
      description: 'Дата. Дата и время',
      description_html: '<p>Дата. Дата и время</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 0,
        points: 0,
        max_points: 1,
        comment: null,
        answer: '06.01.2024 15:00',
        correct_answer: {
          text: '05.01.2024 15:00',
        },
        rightAnswer: false,
      },
      rightAnswer: {
        decodedAnswer: {
          date: '05.01.2024',
          time: '15:00',
          dateFormat: null,
        },
      },
    } as GetResultsDateQuestion,
    {
      type: 3,
      id: 214,
      description: 'Дата. День и месяц и время',
      description_html: '<p>Дата. День и месяц и время</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 0,
        points: 0,
        max_points: 1,
        comment: null,
        answer: '6 января 16:00',
        correct_answer: {
          text: '1 января 15:00',
        },
        rightAnswer: false,
      },
      rightAnswer: {
        decodedAnswer: {
          date: '1 января',
          time: '15:00',
          dateFormat: null,
        },
      },
    } as GetResultsDateQuestion,
    // Address
    {
      type: 4,
      id: 215,
      description: 'Адрес. Выберите расположение филиала',
      description_html: '<p>Адрес. Выберите расположение филиала</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 1,
        points: null,
        max_points: null,
        comment: null,
        answer: null,
        rightAnswer: null,
      },
    } as GetResultsAddressQuestion,
  ],
  points: {
    answer_points: 1,
    points_max: 5,
    percent: 20,
  },
  poll: {
    name: 'Тестовый опрос',
  },
  contact: {
    name: 'Иванов Иван Иванович',
    phone: '+79998887766',
    email: '<EMAIL>',
  },
}

const ratingsData: GetResultsResponse = {
  id: 50983,
  displaySettings: null,
  displayPages: [],
  questions: [
    // Stars basic
    {
      type: 15,
      id: 301,
      description: 'Звездный рейтинг базовый',
      description_html: '<p>Звездный рейтинг базовый</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      clarifyingQuestion: null,
      starRatingOptions: {
        count: 5,
        color: '#FFD700',
        size: 'medium',
        labelsArray: ['метка1', 'метка2', 'метка3', 'метка4', 'метка5'],
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        rating: 3,
      },
    } as GetResultsStarsQuestion,

    // Stars with comment
    {
      type: 15,
      id: 302,
      description: 'Звездный рейтинг с комментарием',
      description_html: '<p>Звездный рейтинг с комментарием</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      clarifyingQuestion: null,
      starRatingOptions: {
        count: 9,
        color: '#FFD700',
        size: 'medium',
        labelsArray: ['метка1', 'метка2', 'метка3', 'метка4', 'метка5', 'метка6', 'метка7', 'метка8', 'метка9'],
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Тестовый комментарий',
        rating: 4,
      },
    } as GetResultsStarsQuestion,

    // Scale basic
    {
      type: 20,
      id: 333,
      description: 'Шкала базовая',
      description_html: '<p>Шкала базовая</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      set_variants: 0,
      scaleRatingSetting: {
        start: 0,
        end: 10,
        step: 1,
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: { rating: '5' },
      },
    } as GetResultsScaleQuestion,

    // Scale with variants
    {
      type: 20,
      id: 304,
      description: 'Шкала с вариантами',
      description_html: '<p>Шкала с вариантами</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      set_variants: 1,
      scaleRatingSetting: {
        start: 0,
        end: 10,
        step: 1,
      },
      variants: [
        { id: 1, variant: 'Совсем не удовлетворен' },
        { id: 2, variant: 'Частично удовлетворен' },
        { id: 3, variant: 'Полностью удовлетворен' },
      ],
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Комментарий к шкале',
        answer: {
          1: '0',
          2: '8',
          3: '9',
        },
      },
    } as GetResultsScaleQuestion,

    // Scale with skip
    {
      type: 20,
      id: 305,
      description: 'Шкала с пропуском',
      description_html: '<p>Шкала с пропуском</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: 'Затрудняюсь ответить',
      isDeleted: 0,
      set_variants: 0,
      scaleRatingSetting: {
        start: 0,
        end: 10,
        step: 1,
      },
      answer: {
        skipped: 1,
        points: null,
        max_points: null,
        comment: null,
        answer: { value: '8' },
      },
    } as GetResultsScaleQuestion,

    // Smile rating basic
    {
      type: 11,
      id: 306,
      description: 'Смайлы базовые',
      description_html: '<p>Смайлы базовые</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        answer: {
          smile: {
            id: 4,
            smile_url: '/uploads/smiles/face/4.svg',
            label: 'mark 4',
          },
          comment: null,
        },
      },
    } as GetResultsSmileQuestion,

    // Smile rating with comment
    {
      type: 11,
      id: 307,
      description: 'Смайлы с комментарием',
      description_html: '<p>Смайлы с комментарием</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Комментарий к смайлам',
        answer: {
          smile: {
            id: 5,
            smile_url: '/uploads/smiles/face/5.svg',
            label: 'mark 5',
          },
          comment: 'Комментарий к смайлам',
        },
      },
    } as GetResultsSmileQuestion,

    // Star variants basic
    {
      type: 7,
      id: 320,
      description: 'Звездный рейтинг с вариантами',
      description_html: '<p>Звездный рейтинг с вариантами</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      clarifyingQuestion: null,
      self_variant_text: 'Свой вариант',
      variants: [
        { id: 1, variant: 'Качество обслуживания', extra_question: 0 },
        { id: 2, variant: 'Чистота помещения', extra_question: 0 },
        { id: 3, variant: 'Соотношение цена/качество', extra_question: 0 },
      ],
      starRatingOptions: {
        count: 10,
        color: '#FFD700',
        size: 'medium',
        labelsArray: Array.from({ length: 10 }).fill('').map((_, i) => `метка${i + 1}`),
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Комментарий к звездному рейтингу с вариантами',
        answer: {
          answer: {
            1: '2',
            2: '9',
            3: '-1',
          },
          comment: 'Комментарий к звездному рейтингу с вариантами',
        },
      },
    } as GetResultsStarVariantsQuestion,

    // Star variants with assessments
    {
      type: 7,
      id: 311,
      description: 'Звездный рейтинг с уточняющими вопросами',
      description_html: '<p>Звездный рейтинг с уточняющими вопросами</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      clarifyingQuestion: 'Почему такая низкая оценка?',
      self_variant_text: 'Свой вариант',
      variants: [
        {
          id: 1,
          variant: 'Качество обслуживания',
          extra_question: 0,
        },
        {
          id: 2,
          variant: 'Чистота помещения',
          extra_question: 0,
        },
        {
          id: 3,
          variant: 'Плохое обслуживание',
          extra_question: 1,
        },
        {
          id: 4,
          variant: 'Плохая чистота',
          extra_question: 1,
        },
        {
          id: 5,
          variant: 'Плохое соотношение цена/качество',
          extra_question: 1,
        },
      ],
      starRatingOptions: {
        count: 5,
        color: '#FFD700',
        size: 'medium',
        labelsArray: Array.from({ length: 5 }).fill('').map((_, i) => `метка${i + 1}`),
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: {
          answer: {
            1: '3', // 1 - вариант. 3 звезды
            2: '4', // 2 - вариант. 4 звезды
            3: '-1',
            extra: {
              1: {
                0: '3',
                self_variant: 'Плохое отношение к клиентам',
              }, // 1 - вариант. Выбран УВ с ID 3
              2: ['4'], // 2 - вариант. Выбран УВ с ID 4
              self_variant: { answer: 'Плохое отношение к клиентам' },
            },
          },
          comment: null,
        },
      },
    } as GetResultsStarVariantsQuestion,

    // NPS basic
    {
      type: 12,
      id: 308,
      description: 'NPS базовый',
      description_html: '<p>NPS базовый</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      fromOne: 0,
      set_variants: 0,
      variants: [],
      self_variant_text: null,
      clarifyingQuestion: null,
      extra_question_type: 0,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Комментарий к NPS',
        answer: { rating: '0' },
      },
    } as GetResultsNpsQuestion,

    // NPS with variants
    {
      type: 12,
      id: 309,
      description: 'NPS с вариантами',
      description_html: '<p>NPS с вариантами</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      set_variants: 1,
      fromOne: 0,

      variants: [
        { id: 1, variant: 'Критик', extra_question: 0, file_id: null, file_url: null, preview_url: null, detail_question: null, question_detail_id: null, is_self_answer: 0, variants_with_files: 0 },
        { id: 2, variant: 'Нейтрал', extra_question: 0, file_id: null, file_url: null, preview_url: null, detail_question: null, question_detail_id: null, is_self_answer: 0, variants_with_files: 0 },
        { id: 3, variant: 'Промоутер', extra_question: 0, file_id: null, file_url: null, preview_url: null, detail_question: null, question_detail_id: null, is_self_answer: 0, variants_with_files: 0 },
      ],
      self_variant_text: null,
      clarifyingQuestion: null,
      extra_question_type: 0,
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Комментарий к NPS с вариантами',
        answer: {
          1: '0',
          2: '-1',
          3: '10',
        },
      },
    } as GetResultsNpsQuestion,

    // Rating scale
    {
      type: 18,
      id: 312,
      description: 'Рейтинговая шкала',
      description_html: '<p>Рейтинговая шкала</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      clarifyingQuestion: null,
      self_variant_text: null,
      starRatingOptions: {
        count: 5,
        color: '#FFD700',
        size: 'medium',
        labelsArray: ['Очень плохо', 'Плохо', 'Нормально', 'Хорошо', 'Отлично'],
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Комментарий к рейтинговой шкале',
        rating: 5,
      },
    } as GetResultsRatingScaleQuestion,

    // Rating scale with assessment variants
    {
      type: 18,
      id: 313,
      description: 'Рейтинговая шкала с уточняющими вопросами',
      description_html: '<p>Рейтинговая шкала с уточняющими вопросами</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      clarifyingQuestion: 'Пожалуйста, уточните почему вы поставили такую оценку',
      self_variant_text: 'Свой вариантик!',
      variants: [
        {
          id: 1,
          variant: 'Качество обслуживания',
          preview_url: null,
          file_url: null,
          file_id: null,
        },
        {
          id: 2,
          variant: 'Чистота помещения',
          preview_url: null,
          file_url: null,
          file_id: null,
        },
        {
          id: 3,
          variant: 'Соотношение цена/качество',
          preview_url: null,
          file_url: null,
          file_id: null,
        },
      ],
      starRatingOptions: {
        count: 5,
        color: '#FFD700',
        size: 'medium',
        labelsArray: ['Очень плохо', 'Плохо', 'Нормально', 'Хорошо', 'Отлично'],
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        rating: 4,
        selectedIds: ['1', '2', '3'],
        selfVariant: 'Плохое отношение к клиентам',
      },
    } as GetResultsRatingScaleQuestion,
  ],
  points: {
    answer_points: 0,
    points_max: 0,
    percent: 0,
  },
  poll: {
    name: 'Тестовый опрос с рейтингами',
  },
  contact: null,
}

const complexData: GetResultsResponse = {
  id: 50984,
  displaySettings: null,
  displayPages: [],
  questions: [
    // Matrix basic
    {
      type: 13,
      id: 401,
      description: 'Матрица базовая',
      description_html: '<p>Матрица базовая</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      clarifyingQuestion: null,
      self_variant_text: null,
      variants: [],
      answer: {
        skipped: 0,
        points: 3,
        max_points: 10,
        comment: null,
        rightAnswer: false,
        answer: {
          answer: {
            'Качество продукта': ['Хорошо'],
            'Скорость доставки': ['Хорошо'],
            'Работа поддержки': ['Хорошо'],
          },
          comment: null,
          extra: null,
        },
        correct_answer: [
          {
            row: 'Качество продукта',
            col: 'Хорошо',
            text: '',
          },
          {
            row: 'Скорость доставки',
            col: 'Отлично',
            text: '',
          },
          {
            row: 'Работа поддержки',
            col: 'Отлично',
            text: '',
          },
        ],
        without_points: false,
      },
    } as GetResultsMatrixQuestion,

    // Matrix with skip row
    {
      type: 13,
      id: 402,
      description: 'Матрица с пропуском строки и уточняющим вопросом',
      description_html: '<p>Матрица с пропуском строки и уточняющим вопросом</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: 'Затрудняюсь ответить',
      isDeleted: 0,
      clarifyingQuestion: 'Почему вы не удовлетворены?',
      self_variant_text: null,
      variants: [
        { id: 1, variant: 'Плохое удобство использования' },
        { id: 2, variant: 'Цена' },
        { id: 3, variant: 'Функциональность' },
      ],
      answer: {
        skipped: 0,
        points: 3,
        max_points: 10,
        comment: 'Тестовый комментарий к матрице',
        rightAnswer: false,
        answer: {
          answer: {
            'Удобство использования': ['Частично удовлетворен', 'Полностью удовлетворен'],
            'Цена': ['Частично удовлетворен'],
            'Функциональность': ['-1'],
          },
          comment: 'Тестовый комментарий к матрице',
          extra: {
            'Удобство использования': ['1', '2'],
            'Цена': {
              0: '1',
              1: '2',
              self_variant: 'Что-то свое',
            },
          },
        },
        without_points: false,
        correct_answer: [
          [
            {
              row: 'Удобство использования',
              col: 'Не удовлетворен',
              text: '',
            },
            {
              row: 'Удобство использования',
              col: 'Частично удовлетворен',
              text: '',
            },
            {
              row: 'Удобство использования',
              col: 'Полностью удовлетворен',
              text: '',
            },
          ],
          [
            {
              row: 'Цена',
              col: 'Не удовлетворен',
              text: '',
            },
            {
              row: 'Цена',
              col: 'Частично удовлетворен',
              text: '',
            },
            {
              row: 'Цена',
              col: 'Полностью удовлетворен',
              text: '',
            },
          ],
        ],
      },
    } as GetResultsMatrixQuestion,

    // Gallery basic
    {
      type: 10,
      id: 404,
      description: 'Галерея базовая',
      description_html: '<p>Галерея базовая</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      chooseMedia: [
        {
          id: 1,
          url: '/uploads/clouds.jpg',
          src: '/uploads/clouds.jpg',
          poster: '/uploads/clouds.jpg',
          description: 'Изображение 1',
        },
        {
          id: 2,
          url: '/uploads/summer.jpg',
          src: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Изображение 2',
        },
        {
          id: 3,
          url: '/uploads/summer.jpg',
          src: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Изображение 3',
        },
      ],
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: null,
        answer: {
          answer: {
            1: '3',
            // 2: '4',
            3: '5',
          },
          comment: null,
        },
      },
    } as GetResultsGalleryQuestion,

    // Media variants single choice
    {
      type: 9,
      id: 406,
      description: 'Медиа варианты одиночный выбор',
      description_html: '<p>Медиа варианты одиночный выбор</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      chooseMedia: [
        {
          id: 1,
          url: '/uploads/clouds.jpg',
          src: '/uploads/clouds.jpg',
          poster: '/uploads/clouds.jpg',
          description: 'Изображение 1',
          points: null,
        },
        {
          id: 2,
          url: '/uploads/summer.jpg',
          src: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Изображение 2',
          points: null,
        },
      ],
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        correct_answer: [
          {
            id: 1,
            file_url: null,
            preview_url: null,
          },
        ],
        answer: {
          answer: ['1'],
          comment: null,
        },
      },
    } as GetResultsMediaVariantsQuestion,

    // Media variants multiple choice
    {
      type: 9,
      id: 407,
      description: 'Медиа варианты множественный выбор',
      description_html: '<p>Медиа варианты множественный выбор</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      chooseMedia: [
        {
          id: 1,
          url: '/uploads/clouds.jpg',
          src: '/uploads/clouds.jpg',
          poster: '/uploads/clouds.jpg',
          description: 'Изображение 1',
          points: null,
        },
        {
          id: 2,
          url: '/uploads/summer.jpg',
          src: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Изображение 2',
          points: null,
        },
        {
          id: 3,
          url: '/uploads/summer.jpg',
          src: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Изображение 3',
          points: null,
        },
      ],
      answer: {
        skipped: 0,
        points: 3,
        max_points: 10,
        answer: {
          answer: ['2', '3'],
          comment: 'Комментарий к медиа вариантам',
        },
        correct_answer: [
          {
            id: 1,
            file_url: null,
            preview_url: null,
          },
          {
            id: 3,
            file_url: null,
            preview_url: null,
          },
        ],
      },
    } as GetResultsMediaVariantsQuestion,
  ],
  points: {
    answer_points: 0,
    points_max: 0,
    percent: 0,
  },
  poll: {
    name: 'Тестовый опрос с комплексными вопросами',
  },
  contact: null,
}

const complex2Data: GetResultsResponse = {
  id: 50985,
  displaySettings: null,
  displayPages: [],
  questions: [
    // Quiz full
    {
      type: 6,
      id: 401,
      description: 'Анкета полная',
      description_html: '<p>Анкета полная</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      formFields: [
        {
          id: 1,
          maskType: MaskTypes.NoMask,
        },
        {
          id: 2,
          maskType: MaskTypes.NoMask,
        },
        {
          id: 3,
          maskType: MaskTypes.Name,
        },
        {
          id: 4,
          maskType: MaskTypes.Phone,
        },
        {
          id: 5,
          maskType: MaskTypes.Email,
        },
        {
          id: 6,
          maskType: MaskTypes.Number,
        },
        {
          id: 7,
          maskType: MaskTypes.Site,
        },
        {
          id: 8,
          maskType: MaskTypes.Date,
        },
        {
          id: 9,
          maskType: MaskTypes.DateMonth,
        },
        {
          id: 10,
          maskType: MaskTypes.Period,
        },
      ],
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        answer: {
          values: [
            {
              id: 1,
              label: 'Без маски',
              value: 'Тестовый ответ',
            },
            {
              id: 2,
              label: 'Без маски - Многострочное',
              value: 'Многострочный тестовый ответ',
            },
            {
              id: 3,
              label: 'ФИО',
              value: {
                name: 'Иван',
                surname: 'Иванов',
                patronymic: 'Иванович',
              },
            },
            {
              id: 4,
              label: 'Телефон',
              value: '+7 (999) 123-45-67',
            },
            {
              id: 5,
              label: 'Email',
              value: '<EMAIL>',
            },
            {
              id: 6,
              label: 'Число',
              value: '42',
            },
            {
              id: 7,
              label: 'Сайт',
              value: 'https://example.com',
            },
            {
              id: 8,
              label: 'Дата',
              value: '01.01.2024',
            },
            {
              id: 9,
              label: 'День и месяц',
              value: '15 марта',
            },
            {
              id: 10,
              label: 'Период',
              value: '01.01.2024-31.12.2024',
            },
          ],
        },
      },
    } as GetResultsQuizQuestion,

    // Quiz simple with skip
    {
      type: 6,
      id: 402,
      description: 'Анкета (в которой мы пропускаем ответы)',
      description_html: '<p>Анкета (в которой мы пропускаем ответы)</p>',
      commentEnabled: 0,
      commentLabel: null,
      skipText: null,
      isDeleted: 0,
      formFields: [
        {
          id: 1,
          maskType: MaskTypes.NoMask,
        },
        {
          id: 2,
          maskType: MaskTypes.NoMask,
        },
        {
          id: 3,
          maskType: MaskTypes.Name,
        },
      ],
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        answer: {
          values: [
            {
              id: 1,
              label: 'Без маски',
              value: 'Простой ответ',
            },
            {
              id: 2,
              label: 'Без маски - Многострочное',
              value: 'Многострочный ответ',
            },
            {
              id: 3,
              label: 'ФИО',
              value: {
                name: 'Петр',
                surname: 'Петров',
                patronymic: 'Петрович',
              },
            },
          ],
        },
      },
    } as GetResultsQuizQuestion,

    // Matrix 3D
    {
      type: 21,
      id: 403,
      description: '3D матрица. Пах-шот методически притягивает теоретический баинг и селлинг, отмечает Рассел.',
      description_html: '<p>3D матрица. Пах-шот методически притягивает теоретический баинг и селлинг, отмечает Рассел.</p>',
      commentEnabled: 1,
      commentLabel: null,
      skipText: 'Затрудняюсь ответить',
      isDeleted: 0,
      matrixElements: {
        columns: [
          {
            id: 1,
            name: 'Салат',
            variants: [
              { id: 1, name: 'Отвратительно' },
              { id: 2, name: 'Хорошо' },
              { id: 3, name: 'Отлично' },
            ],
          },
          {
            id: 2,
            name: 'Салат «Цезарь»',
            variants: [
              { id: 1, name: 'Отвратительно' },
              { id: 2, name: 'Хорошо' },
              { id: 3, name: 'Отлично' },
            ],
          },
          {
            id: 3,
            name: 'Сыр Фета',
            variants: [
              { id: 1, name: 'Отвратительно' },
              { id: 2, name: 'Хорошо' },
              { id: 3, name: 'Отлично' },
            ],
          },
          {
            id: 4,
            name: 'Курица',
            variants: [
              { id: 1, name: 'Отвратительно' },
              { id: 2, name: 'Хорошо' },
              { id: 3, name: 'Отлично' },
            ],
          },
        ],
        rows: [
          { id: 1, name: 'Салат Греческий' },
          { id: 2, name: 'Сыр Фета' },
          { id: 3, name: 'Салат «Цезарь»' },
          { id: 4, name: 'Курица' },
        ],
      },
      answer: {
        skipped: 0,
        points: null,
        max_points: null,
        comment: 'Комментарий к 3D матрице',
        answer: {
          answer: {
            1: {
              1: ['1', '2', '3'],
              2: ['2'],
              3: [''],
            },
            2: {
              1: ['3'],
              2: ['2'],
              3: ['1'],
            },
            3: {
              1: ['3'],
              2: ['2'],
              3: ['1'],
              4: ['-1'],
            },
          },
          comment: 'Комментарий к 3D матрице',
        },
      },
    } as GetResultsMatrix3dQuestion,
  ],
  points: {
    answer_points: 0,
    points_max: 0,
    percent: 0,
  },
  poll: {
    name: 'Тестовый опрос с комплексными вопросами 2',
  },
  contact: null,
}

export const getResultsData: Record<string, () => GetResultsResponse> = {
  'default': () => defaultData,
  'points-report-ratings': () => ratingsData,
  'points-report-complex-questions': () => complexData,
  'points-report-complex-questions-2': () => complex2Data,
  'points-report-additional-questions': () => ({
    id: 50986,
    displaySettings: null,
    displayPages: [],
    questions: [
      // Basic variants
      {
        type: 1,
        id: 303,
        description: '<p>Варианты. Выберите предпочтительный способ связи</p>',
        commentEnabled: 1,

        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        variants: [
          { id: 1, variant: 'Телефон', file_url: null, preview_url: null, file_id: null },
          { id: 2, variant: 'Email', file_url: null, preview_url: null, file_id: null },
          { id: 3, variant: 'Мессенджер', file_url: null, preview_url: null, file_id: null },
          { id: 4, variant: 'Личная встреча', file_url: null, preview_url: null, file_id: null },
        ],
        answer: {
          skipped: 0,
          points: 5,
          max_points: 10,
          selectedIds: ['1', '2', '4'],
          selfVariant: 'Свой вариант',
          comment: 'Текст комментария к вариантам',
          correct_answer: [
            { id: 1, points: 2, text: 'Телефон' },
            { id: 3, points: 3, text: 'Мессенджер' },
            { id: 4, points: 5, text: 'Личная встреча' },
          ],
        },
        self_variant_text: 'Свой вариант',
        rightAnswer: { id: [] },
      },

      // Media variants
      {
        type: 1,
        id: 304,
        description: '<p>Варианты с медиа</p>',
        commentEnabled: 1,

        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        variants: [
          { id: 1, variant: 'Вариант 1', file_url: '/uploads/img-preview-1.jpeg', preview_url: '/uploads/img-preview-1.jpeg', file_id: 1 },
          { id: 2, variant: 'Вариант 2', file_url: '/uploads/img-preview-2.jpeg', preview_url: '/uploads/img-preview-2.jpeg', file_id: 2 },
          { id: 3, variant: 'Вариант 3', file_url: '/uploads/img-preview-3.jpeg', preview_url: '/uploads/img-preview-3.jpeg', file_id: 3 },
        ],
        answer: {
          skipped: 0,
          points: 5,
          max_points: 10,
          selectedIds: ['1', '2', '3'],
          selfVariant: 'Свой вариант для медиа',
          comment: 'Текст комментария к медиа вариантам',
          correct_answer: [
            { id: 2, points: 3, text: 'Вариант 2' },
            { id: 3, points: 7, text: 'Вариант 3' },
          ],
        },
        self_variant_text: 'Свой вариант',
        rightAnswer: { id: [] },
      },

      // Priority
      {
        type: 8,
        id: 305,
        description: '<p>Приоритеты. Расставьте по важности</p>',
        commentEnabled: 0,

        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        variants: [
          { id: 1, variant: 'Цена', position: 0 },
          { id: 2, variant: 'Качество', position: 1 },
          { id: 3, variant: 'Скорость', position: 2 },
          { id: 4, variant: 'Надежность', position: 3 },
        ],
        answer: {
          skipped: 0,
          points: 0,
          max_points: 10,
          answer: {
            variants: ['4', '2', '1', '3'],
            comment: null,
          },
          rightAnswer: false,
          correct_answer: ['1', '3', '2', '4'],
        },
        rightAnswer: { decodedAnswer: null },
      },

      // Diff questions (306, 342-348)
      {
        type: 14,
        id: 306,
        description: '<p>Сравнение. Оцените разницу</p>',
        commentEnabled: 0,

        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        differentialRows: [
          { id: 1, question_id: 306, start_label: 'Неудобно', end_label: 'Удобно', position: 1 },
          { id: 2, question_id: 306, start_label: 'Плохая функциональность', end_label: 'Отличная функциональность', position: 2 },
          { id: 3, question_id: 306, start_label: 'Плохой дизайн', end_label: 'Отличный дизайн', position: 3 },
        ],
        semDifSetting: {
          id: 1,
          foquz_question_id: 306,
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            answer: {
              1: '3',
              2: '4',
              3: '5',
              4: '',
              5: '',
            },
            comment: null,
          },
        },
      },

      // Diff - Single row circles
      {
        type: 14,
        id: 342,
        description: '<p>Семантический дифференциал. Одна строка, круги</p>',
        commentEnabled: 0,
        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        differentialRows: [
          { id: 1, question_id: 342, start_label: '', end_label: '', position: 1 },
        ],
        semDifSetting: {
          id: 1,
          foquz_question_id: 342,
          form: 'circle',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            answer: {
              1: '5',
              2: '',
              3: '',
              4: '',
              5: '',
            },
            comment: null,
          },
        },
      },

      // Diff - Single row with right label
      {
        type: 14,
        id: 343,
        description: '<p>Семантический дифференциал. Одна строка, метка справа</p>',
        commentEnabled: 0,
        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        differentialRows: [
          { id: 1, question_id: 343, start_label: '', end_label: 'Отличная упаковка', position: 1 },
        ],
        semDifSetting: {
          id: 1,
          foquz_question_id: 343,
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            answer: {
              1: '5',
              2: '',
              3: '',
              4: '',
              5: '',
            },
            comment: null,
          },
        },
      },

      // Diff - Multiple rows
      {
        type: 14,
        id: 344,
        description: '<p>Семантический дифференциал. Несколько строк</p>',
        commentEnabled: 0,
        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        differentialRows: [
          { id: 1, question_id: 344, start_label: 'Ужасная упаковка', end_label: 'Отличная упаковка', position: 1 },
          { id: 2, question_id: 344, start_label: 'Отвратительная работа оператора', end_label: 'Отличная работа оператора', position: 2 },
          { id: 3, question_id: 344, start_label: 'Отвратительная доставка', end_label: 'Прекрасная доставка', position: 3 },
        ],
        semDifSetting: {
          id: 1,
          foquz_question_id: 344,
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            answer: {
              1: '1',
              2: '2',
              3: '3',
              4: '',
              5: '',
            },
            comment: null,
          },
        },
      },

      // Diff - Partially labeled
      {
        type: 14,
        id: 345,
        description: '<p>Семантический дифференциал. Частично без меток</p>',
        commentEnabled: 0,
        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        differentialRows: [
          { id: 1, question_id: 345, start_label: 'Ужасная упаковка', end_label: '', position: 1 },
          { id: 2, question_id: 345, start_label: '', end_label: 'Отличная работа оператора', position: 2 },
          { id: 3, question_id: 345, start_label: 'Отвратительная доставка', end_label: '', position: 3 },
        ],
        semDifSetting: {
          id: 1,
          foquz_question_id: 345,
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            answer: {
              1: '1',
              2: '2',
              3: '3',
              4: '',
              5: '',
            },
            comment: null,
          },
        },
      },

      // Diff - Left labels only
      {
        type: 14,
        id: 346,
        description: '<p>Семантический дифференциал. Метки с одной стророны</p>',
        commentEnabled: 0,
        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        differentialRows: [
          { id: 1, question_id: 346, start_label: 'Упаковка', end_label: '', position: 1 },
          { id: 2, question_id: 346, start_label: 'Работа оператора', end_label: '', position: 2 },
          { id: 3, question_id: 346, start_label: 'Доставка', end_label: '', position: 3 },
        ],
        semDifSetting: {
          id: 1,
          foquz_question_id: 346,
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            answer: {
              1: '1',
              2: '2',
              3: '3',
              4: '',
              5: '',
            },
            comment: null,
          },
        },
      },

      // Diff - Circles with labels
      {
        type: 14,
        id: 347,
        description: '<p>Семантический дифференциал. Круги</p>',
        commentEnabled: 0,
        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        differentialRows: [
          { id: 1, question_id: 347, start_label: 'Ужасная упаковка', end_label: 'Отличная упаковка', position: 1 },
          { id: 2, question_id: 347, start_label: 'Отвратительная работа оператора', end_label: 'Отличная работа оператора', position: 2 },
          { id: 3, question_id: 347, start_label: 'Отвратительная доставка', end_label: 'Прекрасная доставка', position: 3 },
          { id: 4, question_id: 347, start_label: 'Плохой вкус', end_label: 'Отличный вкус', position: 4 },
          { id: 5, question_id: 347, start_label: 'Продукты несвежие', end_label: 'Свежие продукты', position: 5 },
        ],
        semDifSetting: {
          id: 1,
          foquz_question_id: 347,
          form: 'circle',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            answer: {
              1: '1',
              2: '2',
              3: '3',
              4: '4',
              5: '5',
            },
            comment: null,
          },
        },
      },

      // Classifier
      {
        type: 19,
        id: 307,
        description: '<p>Классификатор. Древовидный</p>',
        commentEnabled: 0,

        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        variants: [
          { id: '1', value: 'Высокий приоритет', description: '', path: 'Высокий приоритет / Задача 1' },
          { id: '2', value: 'Средний приоритет', description: '', path: 'Средний приоритет / Задача 5' },
          { id: '3', value: 'Низкий приоритет', description: '', path: 'Низкий приоритет / Задача 4' },
          { id: '4', value: 'Задача 5', description: '', path: 'Задача 5' },
        ],
        dictionary_list_type: 'tree',
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          selectedIds: ['1', '2', '3', '4'],
          selfVariant: null,
          comment: null,
        },
      },

      // File upload
      {
        type: 5,
        id: 308,
        description: '<p>Загрузка файла. Прикрепите документ</p>',
        commentEnabled: 0,

        commentLabel: null,
        skipText: null,
        isDeleted: 0,
        answer: {
          skipped: 0,
          points: null,
          max_points: null,
          answer: {
            files: [],
            comment: null,
          },
        },
      },
    ],
    points: {
      answer_points: 5,
      points_max: 20,
      percent: 25,
    },
    poll: {
      name: 'Тестовый опрос с дополнительными типами вопросов',
    },
    contact: null,
  }),
}
