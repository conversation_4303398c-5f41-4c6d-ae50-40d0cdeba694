import merge from "lodash.merge";

const data = {
  link: "https://test-160524.doxswf.ru/p/937875f85e16c18f3bf90c7105bddaaf",
  key: "937875f85e16c18f3bf90c7105bddaaf",
  time: null,
  widget: {
    id: "353",
    is_active: "1",
    company_id: "753",
    code: "266bf3abe3884ae75e4c67fdd4ad8486",
    poll_id: "40013",
    name: "Новый виджет",
    filial_id: null,
    appearance: "0",
    form: "5",
    button_type: "0",
    position: "2",
    button_text: null,
    font: null,
    font_size: null,
    bold: "0",
    italic: "0",
    text_color: null,
    background_color: null,
    stroke: "0",
    simple: "0",
    show_until: "0",
    targeting: "0",
    triggers: {
      url: {
        time: {
          value: "2",
          active: "0",
          timer_type: "1",
        },
        hashtag: "0",
        conditions: [
          {
            value:
              "https://doxswf.ru/chto-takoe-tone-of-voice-tonalnost-obshcheniya-v-klientskom-opyte",
            condition: "1",
          },
          {
            value: "http://localhost:5173/",
            condition: "1",
          },
        ],
        visits_count: {
          value: "",
          active: "0",
          flush_counter: "0",
        },
      },
    },
    triggers_status: {
      url: "0",
      cookies: "0",
      devices: "0",
      coverage: "0",
    },
    created_at: "2024-05-17 12:32:39",
    updated_at: "2024-05-22 10:18:40",
  },
  clientUUID: "48a7ab05-d6e3-4329-a3a8-74eff6b7523e",
};

export const generateWidgetData = (opts) => {
  return merge(data, opts);
};
