import { RATING_QUESTION, STARS_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import { mockLangMessages } from '../mock-utils'

/**
 * Стандартные данные, которые возвращаются при получении опроса
 * @param {object} data - Дополнительные данные, которые можно добавить к стандартным
 * @returns {object} - Данные опроса
 */
export default function defaultQuestionsData(data = {}) {
  return merge(
    {
      answer: {
        id: 788012,
        auth_key: 'f71f34cb96fd8c9c0a3081327cdc9dde',
        status: 'in-progress',
      },
      timer: {
        enabled: false,
        status: 'not-started',
      },
      poll: {
        id: 47744,
        description: null,
        title: '',
        point_system: 0,
        is_published: false,
        startPage: {
          id: 82647,
          created_at: '2024-09-08 06:54:54',
          updated_at: '2024-09-08 06:54:54',
          foquz_poll_id: 47744,
          type: 'start',
          name: '<strong>Заголовок</strong>',
          description: 'Текст раздела',
          image_url: null,
          is_show_complain: 0,
          is_show_unsubscribe: 0,
          is_promocode: 0,
          promocode: null,
          enabled: 0,
          poll_text: null,
          unsubscribe_text: null,
          complaint_text: null,
          socNetworksOptions: null,
        },
        endPage: {
          id: 82648,
          created_at: '2024-09-08 06:54:54',
          updated_at: '2024-09-08 06:54:54',
          foquz_poll_id: 47744,
          type: 'end',
          name: '<strong>Заголовок</strong>',
          description: 'Текст раздела',
          image_url: null,
          is_show_complain: 0,
          is_show_unsubscribe: 0,
          is_promocode: 0,
          promocode: null,
          enabled: 0,
          poll_text: null,
          unsubscribe_text: null,
          complaint_text: null,
          socNetworksOptions: null,
        },
        foquzPollLangs: [
          {
            id: 16195,
            foquz_poll_id: 47744,
            poll_lang_id: 1,
            lang_id: 1,
            checked: 1,
            default: 1,
            back_text: null,
            next_text: null,
            finish_text: null,
            unrequired_text: 'Необязательный',
            messages: [],
          },
          {
            id: 16196,
            foquz_poll_id: 47744,
            poll_lang_id: 2,
            lang_id: 2,
            checked: 1,
            default: 0,
            back_text: 'Back',
            next_text: 'Next',
            finish_text: 'Finish',
            unrequired_text: 'Unrequired',
            messages: mockLangMessages,
          },
        ],
        displaySetting: {},
        displayPages: [],
      },
      questions: [
        {
          id: 1,
          question_id: 154703,
          answer: null,
          alias: '',
          name: '',
          description: 'Звездный рейтинг',
          description_html: '<p>Звездный рейтинг</p>',
          subdescription: '',
          type: STARS_QUESTION,
          mediaType: 0,
          questionContent: '',
          assessmentType: 0,
          assessmentVariantsType: 1,
          assessmentVariants: [],
          detail_answers: [],
          filials: [],
          dictionaryTree: {},
          priorityAnswer: null,
          answerType: 2,
          forAllRates: 0,
          answerText: '',
          stars: null,
          maskType: 0,
          dateType: null,
          category: 0,
          b_name: 0,
          gallery: [],
          enableGallery: false,
          smileType: null,
          variantsType: 1,
          comment: '',
          comment_label: 'Ваш комментарий',
          comment_enabled: 0,
          comment_required: 0,
          isRequired: 1,
          isValid: false,
          checkAll: 0,
          minDishPrice: 0,
          isHaveComment: false,
          isHaveCustomField: 0,
          filesLength: 4,
          fileTypes: [],
          textFieldParam: {
            min: 0,
            max: 250,
          },
          selfVariantParam: {
            min: 0,
            max: 250,
          },
          isCustomFieldChecked: false,
          radioButtonCheckedValue: '',
          textFieldValue: '',
          value: '',
          dateValue: '',
          timeValue: '',
          files: [],
          variants: [],
          values: [],
          arRegionsIDs: [],
          arDistrictsIDs: [],
          arCityIDs: [],
          arStreetsIDs: [],
          placeholderText: null,
          selectPlaceholderText: null,
          selfVariantPlaceholderText: null,
          chooseType: null,
          smiles: [],
          chooseMedia: [],
          maskConfig: {},
          npsRatingSetting: null,
          semDifSetting: null,
          matrixElements: [],
          reorder_required: false,
          matrixSettings: null,
          showQuestion: true,
          questionLogic: [],
          questionViewLogic: [],
          starRatingOptions: {
            id: 32266,
            foquz_question_id: 154703,
            color: 'rgb(248, 205, 28)',
            count: 5,
            size: 'md',
            labels: '["","","","",""]',
            extra_question_rate_from: null,
            extra_question_rate_to: null,
            labelsArray: [
              '',
              '',
              '',
              '',
              '',
            ],
          },
          min_choose_variants: null,
          max_choose_variants: null,
          self_variant_text: null,
          intermediateBlock: null,
          dropdownVariants: 0,
          only_date_month: 0,
          random_variants_order: 0,
          wrongCondition: false,
          endScreenImages: [],
          skip: 0,
          skip_text: '',
          skip_variant: 0,
          skip_row: 0,
          skip_column: 0,
          show_tooltips: 0,
          skipped: 0,
          show_numbers: 0,
          show_labels: 0,
          fromOne: 0,
          donor: null,
          donor_rows: null,
          donor_columns: null,
          donorSelected: 1,
          donorColumnsSelected: 1,
          langs: [
            {
              id: 13111,
              foquz_question_id: 154703,
              foquz_poll_lang_id: 16196,
              name: '',
              description: '<p>Star Rating</p>',
              description_html: '<p>Star Rating</p>',
              sub_description: '<p>Additional description</p>',
              placeholder_text: null,
              select_placeholder_text: null,
              self_variant_placeholder_text: null,
              self_variant_description: null,
              comment_label: '',
              skip_text: null,
              detail_question: null,
              self_variant_text: null,
              labels: '["","","","",""]',
              text: null,
            },
          ],
          dictionary_id: null,
          dictionary_element_id: null,
          dictionary_list_type: 'list',
          dictionary_sort: 'default',
          set_variants: 0,
          scaleRatingSetting: {},
          isHaveExtra: false,
          extra_required: 1,
          disable_select_category: 0,
          variants_with_files: 0,
          self_variant_comment_required: 0,
          self_variant_nothing: 0,
          self_variant_description: null,
        },
        {
          id: 2,
          question_id: 154704,
          answer: null,
          alias: '',
          name: '',
          description: 'Рейтинг',
          description_html: '<p>Рейтинг</p>',
          subdescription: '',
          type: RATING_QUESTION,
          mediaType: 0,
          questionContent: '',
          assessmentType: 0,
          assessmentVariantsType: 1,
          assessmentVariants: [],
          detail_answers: [],
          filials: [],
          dictionaryTree: [],
          priorityAnswer: null,
          answerType: 2,
          forAllRates: 0,
          answerText: '',
          stars: null,
          maskType: 0,
          dateType: null,
          category: 0,
          b_name: 0,
          gallery: [],
          enableGallery: false,
          smileType: null,
          variantsType: 1,
          comment: '',
          comment_label: 'Ваш комментарий',
          comment_enabled: 0,
          comment_required: 0,
          isRequired: 1,
          isValid: false,
          checkAll: 0,
          minDishPrice: 0,
          isHaveComment: false,
          isHaveCustomField: 0,
          filesLength: 4,
          fileTypes: [],
          textFieldParam: {
            min: 0,
            max: 250,
          },
          selfVariantParam: {
            min: 0,
            max: 250,
          },
          isCustomFieldChecked: false,
          radioButtonCheckedValue: '',
          textFieldValue: '',
          value: '',
          dateValue: '',
          timeValue: '',
          files: [],
          variants: [],
          values: [],
          arRegionsIDs: [],
          arDistrictsIDs: [],
          arCityIDs: [],
          arStreetsIDs: [],
          placeholderText: null,
          selectPlaceholderText: null,
          selfVariantPlaceholderText: null,
          chooseType: null,
          smiles: [],
          chooseMedia: [],
          maskConfig: {},
          npsRatingSetting: null,
          semDifSetting: null,
          matrixElements: {},
          reorder_required: false,
          matrixSettings: null,
          showQuestion: true,
          questionLogic: [],
          questionViewLogic: [],
          starRatingOptions: {
            id: 32267,
            foquz_question_id: 154704,
            color: 'rgb(63, 101, 241)',
            count: 5,
            size: 'md',
            labels: '["","","","",""]',
            extra_question_rate_from: null,
            extra_question_rate_to: null,
            labelsArray: [
              '',
              '',
              '',
              '',
              '',
            ],
          },
          min_choose_variants: null,
          max_choose_variants: null,
          self_variant_text: null,
          intermediateBlock: null,
          dropdownVariants: 0,
          only_date_month: 0,
          random_variants_order: 0,
          wrongCondition: false,
          endScreenImages: [],
          skip: 0,
          skip_text: '',
          skip_variant: 0,
          skip_row: 0,
          skip_column: 0,
          show_tooltips: 0,
          skipped: 0,
          show_numbers: 0,
          show_labels: 0,
          fromOne: 0,
          donor: null,
          donor_rows: null,
          donor_columns: null,
          donorSelected: 1,
          donorColumnsSelected: 1,
          langs: [],
          dictionary_id: null,
          dictionary_element_id: null,
          dictionary_list_type: 'list',
          dictionary_sort: 'default',
          set_variants: 0,
          scaleRatingSetting: [],
          isHaveExtra: false,
          extra_required: 1,
          disable_select_category: 0,
          variants_with_files: 0,
          self_variant_comment_required: 0,
          self_variant_nothing: 0,
          self_variant_description: null,
          lang: {
            id: 17343,
            foquz_poll_id: 49242,
            poll_lang_id: 1,
            checked: 0,
            default: 1,
            back_text: '',
            next_text: '',
            finish_text: '',
            unrequired_text: 'Необязательный',
            messages: [],
            questions: {
              10001: { // question_id
                // translations data
              },
            },
          },
        },
      ],
      design: {
        id: 13196,
        foquz_poll_id: 47744,
        background_image: '',
        mobile_background_image: null,
        logo_image: '/uploads/foquz-logo.png',
        main_color: 'rgba(63, 101, 241, 1)',
        background_color: 'rgba(207, 216, 220, 1)',
        header_color: 'rgba(0, 0, 0, 0.5)',
        star_color: '#F8CD1C',
        rating_color: '#3F65F1',
        nps_color_from: '#F96261',
        nps_color_to: '#00C968',
        sem_diff_color_from: '#73808D',
        sem_diff_color_to: '#73808D',
        is_use_header: 1,
        font_family: 'Arial, Helvetica, sans-serif',
        title_font_size: '30',
        font_size: '14',
        text_on_bg: 'rgba(255, 255, 255, 1)',
        text_on_place: 'rgba(0, 0, 0, 1)',
        link_color: 'rgba(255, 255, 255, 1)',
        from_template: 1,
        logo_link: 'http://foquz.ru',
        logo_type: 'image',
        logo_text: null,
        logo_font_family: null,
        logo_color: null,
        logo_position: 1,
        logo_margins: 15,
        small_header_mobile: 0,
        logo_height: 15,
        logo_text_size: 14,
        logo_text_bold: 0,
        logo_text_italic: 0,
        back_text: null,
        back_button_background_color: 'rgba(255, 255, 255, 0)',
        back_button_text_color: 'rgba(255, 255, 255, 1)',
        back_button_stroke_color: 'rgba(255, 255, 255, 1)',
        back_button_radius: 24,
        next_text: null,
        next_button_background_color: 'rgba(63, 101, 241, 0)',
        next_button_text_color: 'rgba(255, 255, 255, 1)',
        next_button_stroke_color: 'rgba(63, 101, 241, 1)',
        next_button_radius: 24,
        start_button_background_color: 'rgba(63, 101, 241, 1)',
        start_button_text_color: 'rgba(255, 255, 255, 1)',
        start_button_stroke_color: 'rgba(63, 101, 241, 1)',
        start_button_radius: 24,
        finish_text: null,
        finish_link: null,
        unrequired_text: 'Необязательный',
        show_process: 1,
        darkening_background: 1,
        place_under_buttons: 'dark',
        show_prev_button: 1,
        main_place_color: 'rgba(255, 255, 255, 1)',
        choose_language: 1,
        full_width: 0,
        disable_question_autoscroll: 0,
        backgroundImage: '/img/themes/background4.jpg',
        logo: '/img/poll-design__custom-logo.png',
        mainColor: 'rgba(63, 101, 241, 1)',
        logoLink: 'http://foquz.ru',
        bgColor: 'rgba(207, 216, 220, 1)',
        headerColor: 'rgba(0, 0, 0, 0.5)',
        starColor: '#F8CD1C',
        ratingColor: '#3F65F1',
        npsColorFrom: '#F96261',
        npsColorTo: '#00C968',
        semDiffColorFrom: '#73808D',
        semDiffColorTo: '#73808D',
        textOnBg: 'rgba(255, 255, 255, 1)',
        textOnPlace: 'rgba(0, 0, 0, 1)',
        linkColor: 'rgba(255, 255, 255, 1)',
        fontFamily: 'Arial, Helvetica, sans-serif',
        titleFontSize: '30',
        fontSize: '14',
        isUseHeader: true,
        templateId: 1,
        logoType: 'image',
        logoText: null,
        logoFontFamily: null,
        logoColor: null,
        backText: null,
        nextText: null,
        finishText: null,
        finishLink: null,
        placeUnderButtons: 'dark',
      },
      isActive: true,
      isAnswersLimitsOver: false,
      showFoquzLink: true,
      showFoquzLabel: true,
      allowEditAfterDone: false,
      showAdv: true,
      variables: {
        'fio': '',
        'order': null,
        'codes': [],
        'scoresInterpretationRanges': [],
        'FILIAL.param1': '',
        'FILIAL.param2': '',
        'FILIAL.param3': '',
      },
      auth: {
        needAuth: false,
        authUrl: null,
      },
    },
    data,
  )
}
