import { QUESTION_TYPES } from "@/helpers/question-helpers/questionTypes";
import defaultQuestionsData from "./questions/defaultQuestionData";
import { ratingOptions } from "./questions/ratingData";
import { ratingNpsOptions } from "./questions/ratingNpsData";
import { smileRatingOptions } from "./questions/smileRatingData";
import { textOptions } from "./questions/textData";
import { variantsOptions } from "./questions/variantsData";
import {
  INTERMEDIATE_BLOCK_TYPES,
  VARIANT_TYPES,
} from "../../src/helpers/question-helpers/questionTypes";
import { intermediateBlockOptions } from "./questions/intermediateBlockData";
import {
  LOGIC_CONDITION_TYPES,
  PARAMETER_CONDITIONS,
  VISIBILITY_CONDITIONS,
} from "../../src/helpers/question-helpers/getAnswerStateByType";

const questionsData = defaultQuestionsData({
  questions: [
    // 1. Star Rating
    {
      id: 1,
      question_id: 1001,
      type: QUESTION_TYPES.STAR_RATING,
      description: "How would you rate our service?",
      description_html: "<p>How would you rate our service?<p>",
      starRatingOptions: {
        count: 5,
      },
    },
    // 2. Rating
    {
      id: 2,
      question_id: 1002,
      type: QUESTION_TYPES.RATING,
      description: "Rate your overall experience",
      description_html: "<p>Rate your overall experience<p>",
      starRatingOptions: ratingOptions(),
    },
    // 3. NPS Rating
    {
      ...ratingNpsOptions(),
      id: 3,
      question_id: 1003,
      type: QUESTION_TYPES.RATING_NPS,
      description: "How likely are you to recommend us?",
      description_html: "<p>How likely are you to recommend us?<p>",
    },
    // 4. Smile Rating
    {
      ...smileRatingOptions({
        smiles: [
          {
            id: 1,
            url: `/uploads/smiles/yellow/1.svg`,
            smile_url: `/uploads/smiles/yellow/1.svg`,
          },
          {
            id: 2,
            url: `/uploads/smiles/yellow/2.svg`,
            smile_url: `/uploads/smiles/yellow/2.svg`,
          },
          {
            id: 3,
            url: `/uploads/smiles/yellow/3.svg`,
            smile_url: `/uploads/smiles/yellow/3.svg`,
          },
          {
            id: 4,
            url: `/uploads/smiles/yellow/4.svg`,
            smile_url: `/uploads/smiles/yellow/4.svg`,
          },
          {
            id: 5,
            url: `/uploads/smiles/yellow/5.svg`,
            smile_url: `/uploads/smiles/yellow/5.svg`,
          },
        ],
      }),
      id: 4,
      question_id: 1004,
      type: QUESTION_TYPES.SMILE_RATING,
      description: "How satisfied are you with our product?",
      description_html: "<p>How satisfied are you with our product?<p>",
    },
    // 5. Text Response
    {
      ...textOptions({
        description: "Please provide any additional feedback",
        description_html: "<p>Please provide any additional feedback<p>",
      }),
      id: 5,
      question_id: 1005,
    },
    // 6. Variants
    {
      ...variantsOptions({
        description: "Which of our services did you use?",
        description_html: "<p>Which of our services did you use?<p>",
        variantsType: VARIANT_TYPES.MULTIPLE,
      }),
      id: 6,
      question_id: 1006,
    },
    // End screens with different questionViewLogic
    {
      ...intermediateBlockOptions({
        id: 7,
        question_id: 1007,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 1",
        },
        questionViewLogic: [
          // Показать только если оценка звезд равна 5
          {
            visibility: VISIBILITY_CONDITIONS.SHOW_ONLY_IF,
            condition_type: LOGIC_CONDITION_TYPES.ANSWER,
            condition_question_id: 1001,
            variants: [5],
          },
        ],
      }),
    },
    {
      ...intermediateBlockOptions({
        id: 8,
        question_id: 1008,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 2",
        },
        questionViewLogic: [
          // Скрыть если рейтинг равен 1
          {
            visibility: VISIBILITY_CONDITIONS.HIDE_IF,
            condition_type: LOGIC_CONDITION_TYPES.ANSWER,
            condition_question_id: 1002,
            variants: [1],
          },
        ],
      }),
    },
    {
      ...intermediateBlockOptions({
        id: 9,
        question_id: 1009,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 3",
        },
        questionViewLogic: [
          // Показать только если тип пользователя "премиум"
          {
            visibility: VISIBILITY_CONDITIONS.SHOW_ONLY_IF,
            condition_type: LOGIC_CONDITION_TYPES.PARAMETER,
            parameter_condition: PARAMETER_CONDITIONS.EQUALS,
            parameter: "userType",
            parameter_value: "premium",
          },
        ],
      }),
    },
    {
      ...intermediateBlockOptions({
        id: 10,
        question_id: 1010,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 4",
        },
        questionViewLogic: [
          // Скрыть если имя пользователя короче 5 символов
          {
            visibility: VISIBILITY_CONDITIONS.HIDE_IF,
            condition_type: LOGIC_CONDITION_TYPES.PARAMETER,
            parameter: "username",
            parameter_value: "xxxxx",
            parameter_condition: PARAMETER_CONDITIONS.LESS_THAN,
          },
        ],
      }),
    },
    {
      ...intermediateBlockOptions({
        id: 11,
        question_id: 1011,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 5",
        },
        questionViewLogic: [
          // Показать только если оценка NPS 8 или выше
          {
            visibility: VISIBILITY_CONDITIONS.SHOW_ONLY_IF,
            condition_type: LOGIC_CONDITION_TYPES.ANSWER,
            condition_question_id: 1003,
            variants: [7, 8, 9],
          },
        ],
      }),
    },
    {
      ...intermediateBlockOptions({
        id: 12,
        question_id: 1012,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 6",
        },
        questionViewLogic: [
          // Скрыть если оценка со смайликом "грустный" и ниже
          {
            visibility: VISIBILITY_CONDITIONS.HIDE_IF,
            condition_type: LOGIC_CONDITION_TYPES.ANSWER,
            condition_question_id: 1004,
            variants: [1, 2],
          },
        ],
      }),
    },
    {
      ...intermediateBlockOptions({
        id: 15,
        question_id: 1015,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 8",
        },
        questionViewLogic: [
          // Показать только если выбраны Варианты "Раз" и "Cвой вариант"
          {
            visibility: VISIBILITY_CONDITIONS.SHOW_ONLY_IF,
            condition_type: LOGIC_CONDITION_TYPES.ANSWER,
            condition_question_id: 1006,
            variants: [93561, 0],
          },
        ],
      }),
    },
    {
      ...intermediateBlockOptions({
        id: 16,
        question_id: 1016,
        intermediateBlock: {
          screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          text: "End screen 10",
        },
        questionViewLogic: [
          // Показать только если оценка звезд 4 или выше
          {
            visibility: VISIBILITY_CONDITIONS.SHOW_ONLY_IF,
            condition_type: LOGIC_CONDITION_TYPES.ANSWER,
            condition_question_id: 1001,
            variants: [4, 5, 6, 7, 8, 9, 10],
          },
          // И скрыть если это новый клиент
          {
            visibility: VISIBILITY_CONDITIONS.HIDE_IF,
            condition_type: LOGIC_CONDITION_TYPES.PARAMETER,
            parameter: "isNewCustomer",
            parameter_value: 1,
          },
        ],
      }),
    },
  ],
});

const logicMockData = {
  "logic-test": () => ({
    ...questionsData,
    answer: {
      custom_fields: '{"username": "John"}',
    },
  }),
  "logic-test-userType-premium": () => ({
    ...questionsData,
    answer: {
      custom_fields: '{"userType": "premium"}',
    },
  }),
  "logic-test-username-short": () => ({
    ...questionsData,
    answer: {
      custom_fields: '{"username": "Bob"}',
    },
  }),
  "logic-test-username-long": () => ({
    ...questionsData,
    answer: {
      custom_fields: '{"username": "Alexander"}',
    },
  }),
  "logic-test-isNewCustomer-1": () => ({
    ...questionsData,
    answer: {
      custom_fields: '{"isNewCustomer": 1, "username": "John"}',
    },
  }),
  "logic-test-isNewCustomer-0": () => ({
    ...questionsData,
    answer: {
      custom_fields: '{"isNewCustomer": 0, "username": "John"}',
    },
  }),
};

export default logicMockData;
