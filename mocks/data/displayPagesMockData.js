import { ASSESMENT_VARIANT_TYPES, DATE_QUESTION, MATRIX_3D_QUESTION, MATRIX_QUESTION, MEDIA_VARIANTS_QUESTION, RATING_QUESTION, STARS_QUESTION, TEXT_QUESTION, TEXT_VARIANT_TYPES } from '@entities/question/model/types'
import { POLL_NAVIGATION_TYPE } from '@shared/constants'
import MaskTypes from '@shared/constants/maskTypes'
import defaultQuestionsData from './defaultQuestionsData'
import { galleryOptions } from './questions/galleryMockData'
import { getQuizMockData } from './questions/quizMockData'
import { ratingNpsOptions } from './questions/ratingNpsData'
import { scaleOptions } from './questions/scaleMockData'
import { smileRatingOptions } from './questions/smileRatingMockData'

const displayPagesMockData = {
  'text-questions-examples': () => defaultQuestionsData({
    design: {
      show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
      disable_question_autoscroll: 1,
    },
    poll: {
      displaySetting: {
        id: 1629,
        poll_id: 50982,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      displayPages: [
        {
          id: 2440,
          foquz_poll_id: 50982,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            { id: 201 }, // Simple text
            { id: 202 }, // Multiline text
            { id: 203 }, // URL text
            { id: 204 }, // FIO text
            { id: 205 }, // Date text
            { id: 206 }, // Day and month text
            { id: 207 }, // Period text
          ],
        }
      ],
    },
    questions: [
      // Simple text question
      {
        type: TEXT_QUESTION,
        id: 1,
        question_id: 201,
        description: 'Текстовый. Как называется наша компания?',
        description_html: '<p>Текстовый. Как называется наша компания?</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.NoMask,
        isRequired: 1,
        placeholderText: 'Введите название компании',
      },
      // Multiline text question
      {
        type: TEXT_QUESTION,
        id: 2,
        question_id: 202,
        description: 'Текстовый многострочный. Как называется наша компания?',
        description_html: '<p>Текстовый многострочный. Как называется наша компания?</p>',
        variantsType: TEXT_VARIANT_TYPES.TEXTAREA,
        maskType: MaskTypes.NoMask,
        isRequired: 1,
        textFieldParam: {
          min: 10,
          max: 1000,
        },
        placeholderText: 'Введите текст',
      },
      // URL text question
      {
        type: TEXT_QUESTION,
        id: 3,
        question_id: 203,
        description: 'Текстовый. Сайт компании',
        description_html: '<p>Текстовый. Сайт компании</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Url,
        isRequired: 1,
        placeholderText: 'Введите URL сайта',
      },
      // FIO text question
      {
        type: TEXT_QUESTION,
        id: 4,
        question_id: 204,
        description: 'Текстовый. ФИО',
        description_html: '<p>Текстовый. ФИО</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Name,
        isRequired: 1,
        maskConfig: {
          name: {
            visible: 'true',
            required: 'true',
            placeholderText: 'Имя',
            minLength: 0,
            maxLength: 50,
          },
          surname: {
            visible: 'true',
            required: 'true',
            placeholderText: 'Фамилия',
            minLength: 0,
            maxLength: 50,
          },
          patronymic: {
            visible: 'true',
            required: 'false',
            placeholderText: 'Отчество',
            minLength: 0,
            maxLength: 50,
          },
        },
      },
      // Date text question
      {
        type: TEXT_QUESTION,
        id: 5,
        question_id: 205,
        description: 'Текстовый. Дата',
        description_html: '<p>Текстовый. Дата</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Date,
        isRequired: 1,
        placeholderText: 'DD.MM.YYYY',
      },
      // Day and month text question
      {
        type: TEXT_QUESTION,
        id: 6,
        question_id: 206,
        description: 'Текстовый. День и месяц',
        description_html: '<p>Текстовый. День и месяц</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.DayMonth,
        isRequired: 1,
        placeholderText: 'DD.MM',
      },
      // Period text question
      {
        type: TEXT_QUESTION,
        id: 7,
        question_id: 207,
        description: 'Текстовый. Период',
        description_html: '<p>Текстовый. Период</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Period,
        isRequired: 1,
        placeholderText: 'DD.MM.YYYY-DD.MM.YYYY',
      },
    ],
  }),
  'multiple-questions-per-page': () => defaultQuestionsData({
    design: {
      show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
      disable_question_autoscroll: 1,
    },
    poll: {
      displaySetting: {
        id: 1628,
        poll_id: 50981,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      displayPages: [
        {
          id: 2434,
          foquz_poll_id: 50981,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            { id: 101 }, // Star rating basic
            { id: 102 }, // Star rating with comment
            { id: 103 }, // Rating with single choice assessment
            { id: 104 }, // Rating with text assessment and skip
          ],
        },
        {
          id: 2435,
          foquz_poll_id: 50981,
          name: null,
          random_order: 0,
          order: 2,
          random_exclusion: 0,
          questions: [
            { id: 105 }, // Media variants multiple choice
            { id: 106 }, // Text FIO
            { id: 107 }, // Text multiline
          ],
        },
        {
          id: 2436,
          foquz_poll_id: 50981,
          name: null,
          random_order: 0,
          order: 3,
          random_exclusion: 0,
          questions: [
            { id: 112 }, // Scale with skip
            { id: 113 }, // Scale with variants and skip_variant
            { id: 114 }, // Gallery question
            { id: 115 }, // Smile rating with 5 icons
          ],
        },
        {
          id: 2437,
          foquz_poll_id: 50981,
          name: null,
          random_order: 0,
          order: 4,
          random_exclusion: 0,
          questions: [
            { id: 116 }, // NPS rating with comment and skip
            { id: 117 }, // NPS rating with variants
            { id: 118 }, // Matrix with row skip
            { id: 119 }, // 3D Matrix
          ],
        },
      ],
    },
    questions: [
      // Star rating basic
      {
        type: STARS_QUESTION,
        id: 1,
        question_id: 101,
        description: 'Базовый звездный рейтинг',
        description_html: '<p>Базовый звездный рейтинг</p>',
        starRatingOptions: {
          count: 5,
        },
      },
      // Star rating with comment
      {
        type: STARS_QUESTION,
        id: 2,
        question_id: 102,
        description: 'Звездный рейтинг с комментарием',
        description_html: '<p>Звездный рейтинг с комментарием</p>',
        comment_enabled: 1,
        comment_required: 1,
        starRatingOptions: {
          count: 5,
        },
      },
      // Rating scale with assessments single
      {
        type: RATING_QUESTION,
        id: 3,
        question_id: 103,
        description: 'Рейтинг с УВ одиночного выбора',
        description_html: '<p>Рейтинг с УВ одиночного выбора</p>',
        answerText: 'Что вам не понравилось?',
        assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
        variantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
        isHaveCustomField: 1,
        isHaveExtra: 1,
        extra_required: 1,
        starRatingOptions: {
          count: 5,
          color: '#000',
        },
        detail_answers: [
          { id: 1, variant: 'Вариант 1', is_deleted: 0 },
          { id: 2, variant: 'Вариант 2', is_deleted: 0 },
          { id: 3, variant: 'Вариант 3', is_deleted: 0 },
        ],
      },
      // Rating scale with assessments text and skip
      {
        type: RATING_QUESTION,
        id: 4,
        question_id: 104,
        description: 'Рейтинг с текстовым УВ и пропуском',
        description_html: '<p>Рейтинг с текстовым УВ и пропуском</p>',
        answerText: 'Что можно улучшить?',
        assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
        variantsType: ASSESMENT_VARIANT_TYPES.TEXT,
        isHaveCustomField: 1,
        isHaveExtra: 1,
        skip: 1,
        skip_text: 'Пропустить этот вопрос',
        placeholderText: 'Пожалуйста, уточните',
        detail_answers: [],
        starRatingOptions: {
          count: 5,
          color: '#000',
        },
      },
      // Media variants multiple with comment
      {
        type: MEDIA_VARIANTS_QUESTION,
        id: 5,
        question_id: 105,
        description: 'Медиа варианты множественного выбора',
        description_html: '<p>Медиа варианты множественного выбора</p>',
        variantsType: 1, // multiple choice
        isHaveComment: true,
        comment_required: 1,
        gallery: [
          {
            id: 1,
            src: '/uploads/clouds.jpg',
            url: '/uploads/clouds.jpg',
            poster: '/uploads/clouds.jpg',
            description: 'Изображение 1',
          },
          {
            id: 2,
            src: '/uploads/summer.jpg',
            url: '/uploads/summer.jpg',
            poster: '/uploads/summer.jpg',
            description: 'Изображение 2',
          },
        ],
      },
      // Text FIO
      {
        type: TEXT_QUESTION,
        id: 6,
        question_id: 106,
        description: 'ФИО',
        description_html: '<p>ФИО</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Name,
        isRequired: 1,
        maskConfig: {
          name: {
            visible: 'true',
            required: 'true',
            placeholderText: 'Имя',
            minLength: 0,
            maxLength: 50,
          },
          surname: {
            visible: 'true',
            required: 'true',
            placeholderText: 'Фамилия',
            minLength: 0,
            maxLength: 50,
          },
          patronymic: {
            visible: 'true',
            required: 'false',
            placeholderText: 'Отчество',
            minLength: 0,
            maxLength: 50,
          },
        },
      },
      // Text multiline
      {
        type: TEXT_QUESTION,
        id: 7,
        question_id: 107,
        maskType: MaskTypes.NoMask,
        description: 'Многострочное текстовое поле',
        description_html: '<p>Многострочное текстовое поле</p>',
        variantsType: TEXT_VARIANT_TYPES.TEXTAREA,
        isRequired: 1,
        textFieldParam: {
          min: 10,
          max: 500,
        },
        placeholderText: 'Введите ваш текст здесь',
      },
      // Text period date
      {
        type: TEXT_QUESTION,
        id: 8,
        question_id: 108,
        description: 'Period date',
        description_html: '<p>Period date</p>',
        variantsType: TEXT_VARIANT_TYPES.INPUT,
        maskType: MaskTypes.Period,
        isRequired: 1,
        placeholderText: 'DD.MM.YYYY',
      },
      // Date
      {
        type: DATE_QUESTION,
        id: 9,
        question_id: 109,
        description: 'Date',
        description_html: '<p>Date</p>',
        isRequired: 1,
        dateType: 0,
        only_date_month: 0,
      },
      // Date with time
      {
        type: DATE_QUESTION,
        id: 10,
        question_id: 110,
        description: 'Date with time',
        description_html: '<p>Date with time</p>',
        isRequired: 1,
        dateType: 2,
        only_date_month: 0,
      },
      // Quiz
      getQuizMockData({
        id: 11,
        question_id: 111,
      }),
      // Scale with skip
      scaleOptions({
        id: 12,
        question_id: 112,
        skip: 1,
      }),
      // Scale with variants and skip_variant
      scaleOptions({
        id: 13,
        question_id: 113,
        set_variants: 1,
        skip_variant: 1,
      }),
      // Gallery question
      galleryOptions({
        id: 14,
        question_id: 114,
      }),
      // Smile rating with 5 icons, comment and skip
      smileRatingOptions({
        id: 15,
        question_id: 115,
        smileType: 'face',
        count: 5,
        comment_enabled: 1,
        skip: 1,
        smiles: Array.from({ length: 5 }, (_, index) => ({
          id: index + 1,
          label: `mark ${index + 1}`,
          url: `/uploads/smiles/face/${index + 1}.svg`,
          smile_url: `/uploads/smiles/face/${index + 1}.svg`,
        })),
      }),
      // NPS rating with comment and skip
      ratingNpsOptions({
        id: 16,
        question_id: 116,
        comment_enabled: 1,
        skip: 1,
      }),
      // NPS rating with variants, comment and skip
      ratingNpsOptions({
        id: 17,
        question_id: 117,
        set_variants: 1,
        comment_enabled: 1,
        skip: 1,
        detail_answers: [
          {
            id: 1,
            variant: 'Скорость обслуживания',
            is_deleted: 0,
          },
          {
            id: 2,
            variant: 'Качество продукта',
            is_deleted: 0,
          },
          {
            id: 3,
            variant: 'Удобство использования',
            is_deleted: 0,
          },
        ],
      }),
      // Matrix with row skip
      {
        type: MATRIX_QUESTION,
        id: 18,
        question_id: 118,
        description: 'Матрица с пропуском строки',
        description_html: '<p>Матрица с пропуском строки</p>',
        isRequired: 1,
        skip_variant: 1,
        matrixSettings: {
          rows: ['Качество обслуживания', 'Время отклика', 'Удобство использования'],
          cols: ['Плохо', 'Удовлетворительно', 'Хорошо', 'Отлично'],
          minRowsReq: 2,
          multiple_choice: '0',
          type: 'standart',
        },
        detail_answers: [],
      },
      // 3D Matrix
      {
        type: MATRIX_3D_QUESTION,
        id: 19,
        question_id: 119,
        description: 'Трехмерная матрица',
        description_html: '<p>Трехмерная матрица</p>',
        isRequired: 1,
        skip: 1,
        skip_variant: 1,
        skip_row: 1,
        skip_column: 1,
        variantsType: 0, // 0 for single choice, 1 for multiple choice
        show_tooltips: 1,
        comment_enabled: 1,
        comment_required: 0,
        comment_label: 'Дополнительные комментарии',
        placeholderText: 'Введите ваши комментарии здесь',
        matrixSettings: {
          rowsAboveVariants: '0',
        },
        matrixElements: {
          rows: [
            { id: 1, name: 'Продукт А', description: 'Описание А', position: 1 },
            { id: 2, name: 'Продукт Б', description: 'Описание Б', position: 2 },
            { id: 3, name: 'Продукт В', description: 'Описание В', position: 3 },
          ],
          columns: [
            {
              id: 1,
              name: 'Качество',
              description: 'Качество продукта',
              position: 1,
              variants: [
                { id: 1, name: 'Плохо', position: 1 },
                { id: 2, name: 'Удовлетворительно', position: 2 },
                { id: 3, name: 'Хорошо', position: 3 },
                { id: 4, name: 'Отлично', position: 4 },
              ],
            },
            {
              id: 2,
              name: 'Цена',
              description: 'Стоимость',
              position: 2,
              variants: [
                { id: 5, name: 'Дорого', position: 1 },
                { id: 6, name: 'Умеренно', position: 2 },
                { id: 7, name: 'Доступно', position: 3 },
                { id: 8, name: 'Дешево', position: 4 },
              ],
            },
          ],
        },
      },
    ],
  }),
}

export default displayPagesMockData
