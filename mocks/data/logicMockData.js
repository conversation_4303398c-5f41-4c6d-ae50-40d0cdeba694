import { INTER_BLOCK } from '@entities/question/model/types'
import {
  B<PERSON><PERSON><PERSON>OR_ALWAYS,
  B<PERSON><PERSON>VIOR_MISS,
  BEHAVIOR_SELECT,
  BEHAVIOR_UNSELECT,
  JUMP_TO_DEFAULT_END_SCREEN,
  JUMP_TO_END_SCREEN,
  JUMP_TO_QUESTION,
} from '@shared/constants/logic'
import merge from 'lodash.merge'
import defaultQuestionsData from './defaultQuestionsData'
import { classifierQuestionOptions } from './questions/classifierMockData'
import { starRatingQuestionOptions } from './questions/starRatingMockData'

function cleanQuestionObject(question, additionalFieldsToKeep = []) {
  const defaultFieldsToKeep = [
    'id',
    'question_id',
    'intermediateBlock',
    'questionLogic',
    'isRequired',
    'type',
    'description',
    'description_html',
    'textFieldParam',
    'detail_answers',
    'dictionaryTree',
    'smiles',
    'images',
    'endScreenImages',
    'scaleRatingSetting',
    'values',
    'maskType',
    'variantsType',
    'dateType',
    'gallery',
    'set_variants',
    'skip_variant',
    'random_variants_order',
    'filials',
    'dictionary_list_type',
    'dictionary_sort',
    'isHaveCustomField',
  ]

  // Add fields ending with 'Options'
  const optionsFields = Object.keys(question).filter(key => key.endsWith('Options'))

  const fieldsToKeep = [
    ...defaultFieldsToKeep,
    ...optionsFields,
    ...additionalFieldsToKeep,
  ]

  const cleanedQuestion = {}
  fieldsToKeep.forEach((field) => {
    if (question[field] !== undefined) {
      cleanedQuestion[field] = question[field]
    }
  })

  return cleanedQuestion
}

function cleanQuestionsList(questions, additionalFieldsToKeep = []) {
  return questions.map(question => cleanQuestionObject(question, additionalFieldsToKeep))
}

function firstPartQuestions(additionalData) {
  return merge(
    [{
      id: 1,
      question_id: 166314,
      type: INTER_BLOCK,
      intermediateBlock: {
        id: 19392,
        question_id: 166314,
        screen_type: 2,
        show_question_number: 0,
        text: '<p>старт</p>',
        langs: [],
      },
      langs: [],
    }, {
      id: 2,
      question_id: 166232,
      answer: null,
      alias: '',
      name: '',
      description: 'ЗР',
      description_html: '<p>ЗР</p>',
      subdescription: '',
      type: 15,
      isRequired: 1,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      questionLogic: [
        {
          id: 4780,
          question_id: 166232,
          behavior: 1,
          variants: [
            4,
            5,
          ],
          jump: 2,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4781,
          question_id: 166232,
          behavior: 1,
          variants: [
            1,
            2,
          ],
          jump: 3,
          jump_question_id: 166233,
          jump_display_page_id: null,
          position: 2,
        },
        {
          id: 4782,
          question_id: 166232,
          behavior: 1,
          variants: [
            3,
          ],
          jump: 1,
          jump_question_id: 166243,
          jump_display_page_id: null,
          position: 3,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: {
        id: 35191,
        foquz_question_id: 166232,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      langs: [],
    }, starRatingQuestionOptions({
      id: 8,
      question_id: 166348,
      count: 2,
    }), starRatingQuestionOptions({
      id: 9,
      question_id: 165249,
      count: 7,
    }), starRatingQuestionOptions({
      id: 10,
      question_id: 165250,
      count: 5,
    }), {
      id: 3,
      question_id: 166233,
      answer: null,
      alias: '',
      name: '',
      description: '',
      description_html: '',
      subdescription: '',
      type: 16,
      mediaType: 0,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      intermediateBlock: {
        id: 19381,
        question_id: 166233,
        screen_type: 3,
        show_question_number: 0,
        text: '<p>конец 1</p>',
        langs: [],
      },
      langs: [],
    }, {
      id: 4,
      question_id: 166234,
      answer: null,
      alias: '',
      name: '',
      description: '',
      description_html: '',
      subdescription: '',
      type: 16,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      intermediateBlock: {
        id: 19382,
        question_id: 166234,
        screen_type: 3,
        show_question_number: 0,
        text: '<p>конец 2</p>',
        complaint_button: 0,
        unsubscribe_button: 0,
        complaint_button_text: '',
        unsubscribe_button_text: '',
        poll_button_text: '',
        code: null,
        pool_id: null,
        ready_button: 0,
        ready_button_text: '',
        close_widget_button: 0,
        close_widget_button_text: '',
        external_link: '',
        scores_button: 0,
        scores_button_text: '',
        start_over_button: 0,
        start_over_button_text: '',
        logos_backcolor: '#FFFFFF',
        agreement: 0,
        agreement_text: '',
        langs: [],
      },
      langs: [],
    }, {
      id: 5,
      question_id: 166242,
      answer: null,
      alias: '',
      name: '',
      description: 'ЗР - шкала 7',
      description_html: '<p>ЗР - шкала 7</p>',
      subdescription: '',
      type: 15,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      starRatingOptions: {
        id: 35193,
        foquz_question_id: 166242,
        color: 'rgb(248, 205, 28)',
        count: 7,
        size: 'md',
        labels: '["","","","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
          '',
          '',
        ],
      },
      langs: [],
    }, {
      id: 6,
      question_id: 166243,
      answer: null,
      alias: '',
      name: '',
      description: 'Рейтинг',
      description_html: '<p>Рейтинг</p>',
      subdescription: '',
      type: 18,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      questionLogic: [
        {
          id: 4783,
          question_id: 166243,
          behavior: BEHAVIOR_MISS,
          variants: [
          ],
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166234,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: {
        id: 35194,
        foquz_question_id: 166243,
        color: 'rgb(63, 101, 241)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      langs: [],
    }, {
      id: 7,
      question_id: 166244,
      answer: null,
      alias: '',
      name: '',
      description: 'Рейтинг - шкала 7',
      description_html: '<p>Рейтинг - шкала 7</p>',
      subdescription: '',
      type: 18,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      questionLogic: [
        {
          id: 4784,
          question_id: 166244,
          behavior: BEHAVIOR_SELECT,
          variants: [
            1,
            2,
            3,
            4,
            5,
          ],
          jump: 1,
          jump_question_id: 166245,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4784,
          question_id: 166244,
          behavior: 1,
          variants: [
            6,
            7,
          ],
          jump: 3,
          jump_question_id: 166233,
          jump_display_page_id: null,
          position: 2,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: {
        id: 35195166259,
        foquz_question_id: 166244,
        color: 'rgb(63, 101, 241)',
        count: 7,
        size: 'md',
        labels: '["","","","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
          '',
          '',
        ],
      },
      langs: [],
    }, starRatingQuestionOptions({
      count: 2,
      id: 943,
      question_id: 166248,
    }), {
      id: 9,
      question_id: 166245,
      answer: null,
      alias: '',
      name: '',
      description: 'Смайл-рейтинг',
      description_html: '<p>Смайл-рейтинг</p>',
      subdescription: '',
      type: 11,
      mediaType: 0,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      smiles: [
        {
          id: 39171,
          foquz_question_id: 166245,
          url: '/uploads/smiles/heart/break.svg',
          smile_url: '/uploads/smiles/heart/break.svg',
          label: '',
        },
        {
          id: 39172,
          foquz_question_id: 166245,
          url: '/uploads/smiles/heart/full.svg',
          smile_url: '/uploads/smiles/heart/full.svg',
          label: '',
        },
      ],
      questionLogic: [
        {
          id: 4785,
          question_id: 166245,
          behavior: BEHAVIOR_SELECT,
          variants: [
            2,
          ],
          jump: 1,
          jump_question_id: 166246,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      langs: [],
    }, starRatingQuestionOptions({
      id: 9,
      question_id: 166243998,
    }), {
      id: 10,
      question_id: 166246,
      answer: null,
      alias: '',
      name: '',
      description: 'Смайл-рейтинг / лицо',
      description_html: '<p>Смайл-рейтинг / лицо</p>',
      subdescription: '',
      type: 11,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      smiles: [
        {
          id: 39175,
          foquz_question_id: 166246,
          smile_url: '/img/smiles/face/1.svg',
          label: '',
        },
        {
          id: 39176,
          foquz_question_id: 166246,
          smile_url: '/img/smiles/face/2.svg',
          label: '',
        },
        {
          id: 39177,
          foquz_question_id: 166246,
          smile_url: '/img/smiles/face/3.svg',
          label: '',
        },
        {
          id: 39178,
          foquz_question_id: 166246,
          smile_url: '/img/smiles/face/4.svg',
          label: '',
        },
        {
          id: 39179,
          foquz_question_id: 166246,
          smile_url: '/img/smiles/face/5.svg',
          label: '',
        },
      ],
      chooseMedia: [],
      maskConfig: {},
      questionLogic: [
        {
          id: 4786,
          question_id: 166246,
          behavior: BEHAVIOR_SELECT,
          variants: [
            5,
          ],
          jump: JUMP_TO_QUESTION,
          jump_question_id: 166247,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4786,
          question_id: 166246,
          behavior: BEHAVIOR_MISS,
          variants: [],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 2,
        },
        {
          id: 4786,
          question_id: 166246,
          behavior: BEHAVIOR_UNSELECT,
          variants: [
            5,
          ],
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166234,
          jump_display_page_id: null,
          position: 3,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      langs: [],
    }, starRatingQuestionOptions({
      id: 33,
      question_id: 82820,
    }), starRatingQuestionOptions({
      id: 32,
      question_id: 82821,
    }), {
      id: 10,
      question_id: 166247,
      answer: null,
      alias: '',
      name: '',
      description: 'Рейтинг NPS / Стандартный ',
      description_html: '<p>Рейтинг NPS / Стандартный </p>',
      subdescription: '',
      type: 12,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      npsRatingSetting: {
        id: 6388,
        foquz_question_id: 166247,
        design: 1,
        start_point_color: '#F96261',
        end_point_color: '#00C968',
        start_label: '',
        end_label: '',
      },
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [
        {
          id: 4787,
          question_id: 166247,
          behavior: BEHAVIOR_SELECT,
          variants: [
            0,
            10,
          ],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      langs: [],
    }, {
      id: 11,
      question_id: 166253,
      answer: null,
      alias: '',
      name: '',
      description: 'Рейтинг NPS / Стандартный с 1',
      description_html: '<p>Рейтинг NPS / Стандартный с 1</p>',
      subdescription: '',
      type: 12,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      npsRatingSetting: {
        id: 6389,
        foquz_question_id: 166253,
        design: 1,
        start_point_color: '#F96261',
        end_point_color: '#00C968',
        start_label: '',
        end_label: '',
      },
      langs: [],
      questionLogic: [
        {
          id: 4787,
          question_id: 166247,
          behavior: BEHAVIOR_SELECT,
          variants: [
            1,
            10,
          ],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
      ],
    }, {
      id: 12,
      question_id: 166429,
      answer: null,
      alias: '',
      name: '',
      description: 'Шкала / Стандартная',
      description_html: '<p>Шкала / Стандартная</p>',
      subdescription: '',
      type: 20,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      scaleRatingSetting: {
        start: 0,
        end: 100,
        step: 2,
      },
      questionLogic: [
        {
          id: 4788,
          question_id: 1664297,
          behavior: BEHAVIOR_SELECT,
          variants: [
            62,
            100,
          ],
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166233,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4789,
          question_id: 166429,
          behavior: BEHAVIOR_UNSELECT,
          variants: [
            62,
            100,
          ],
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166234,
          jump_display_page_id: null,
          position: 2,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      langs: [],
    }],
    additionalData,
  )
}

function secondPartQuestions(additionalData) {
  return merge(
    [{
      id: 1,
      question_id: 166254,
      type: 2,
      description: 'Однострочное',
      description_html: '<p>Однострочное</p>',
      variantsType: 0,
      maskType: 0,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      questionLogic: [
        {
          id: 4796,
          question_id: 166254,
          behavior: BEHAVIOR_ALWAYS,
          variants: [],
          jump: JUMP_TO_QUESTION,
          jump_question_id: 168255,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      langs: [],
    }, starRatingQuestionOptions({
      id: 10,
      question_id: 168585,
      description: 'Звездный рейтинг',
      description_html: '<p>Звездный рейтинг</p>',
    }), {
      id: 2,
      question_id: 168255,
      answer: null,
      alias: '',
      name: '',
      description: 'Дата/время',
      description_html: '<p>Дата/время</p>',
      subdescription: '',
      type: 3,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: 0,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: null,
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [
        {
          id: 4797,
          question_id: 166255,
          behavior: BEHAVIOR_ALWAYS,
          variants: null,
          jump: JUMP_TO_QUESTION,
          jump_question_id: 166258,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: null,
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: null,
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    }, starRatingQuestionOptions({
      id: 2,
      question_id: 166555,
      description: 'Звездный рейтинг',
      description_html: '<p>Звездный рейтинг</p>',
    }), {
      id: 5,
      question_id: 166258,
      answer: null,
      alias: '',
      name: '',
      description: 'Анкета',
      description_html: '<p>Анкета</p>',
      subdescription: '',
      type: 6,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [
        {
          id: 14145,
          label: 'Телефон',
          value: '',
          isRequired: 1,
          maskType: 1,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '+7 (___) ___ - ____',
          maskConfig: {},
          langs: [],
        },
        {
          id: 14146,
          label: 'Дата',
          value: '',
          isRequired: 1,
          maskType: 6,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '00.00.0000',
          maskConfig: {},
          langs: [],
        },
      ],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: null,
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [
        {
          id: 4800,
          question_id: 222,
          behavior: BEHAVIOR_ALWAYS,
          variants: null,
          jump: JUMP_TO_QUESTION,
          jump_question_id: 226259,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      langs: [],
    }, starRatingQuestionOptions({
      id: 2,
      question_id: 126555,
      description: 'Звездный рейтинг',
      description_html: '<p>Звездный рейтинг</p>',
    }), {
      id: 6,
      question_id: 226259,
      answer: null,
      alias: '',
      name: '',
      description: 'Приоритет',
      description_html: '<p>Приоритет</p>',
      subdescription: '',
      type: 8,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      detail_answers: [
        {
          id: 118260,
          type: 0,
          variant: 'вар 1',
          description: '',
          position: 1,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118261,
          type: 0,
          variant: 'вар 2',
          description: '',
          position: 2,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118262,
          type: 0,
          variant: 'вар 3',
          description: '',
          position: 3,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
      ],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 0,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      values: [],
      questionLogic: [
        {
          id: 4801,
          question_id: 166259,
          behavior: 4,
          variants: null,
          jump: 2,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      langs: [],
    }],
    additionalData,
  )
}

function thirdPartQuestions(additionalData) {
  return merge([
    {
      id: 1,
      question_id: 166262,
      answer: null,
      alias: '',
      name: '',
      description: 'Рейтинг фото/видео галереи',
      description_html: '<p>Рейтинг фото/видео галереи</p>',
      subdescription: '',
      type: 10,
      mediaType: 1,
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [
        {
          id: 1,
          src: '/uploads/clouds.jpg',
          url: '/uploads/clouds.jpg',
          poster: '/uploads/clouds.jpg',
          description: 'Image 1',
        },
        {
          id: 2,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 2',
        },
        {
          id: 3,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 3',
        },
      ],
      enableGallery: true,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: '',
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: '',
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: null,
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 2,
      question_id: 166263,
      answer: null,
      alias: '',
      name: '',
      description: 'Шкала для вариантов',
      description_html: '<p>Шкала для вариантов</p>',
      subdescription: '',
      type: 20,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      detail_answers: [
        {
          id: 118263,
          type: 0,
          variant: 'вар 1',
          description: '',
          position: 1,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118264,
          type: 0,
          variant: 'вар 2',
          description: '',
          position: 2,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118265,
          type: 0,
          variant: 'вар 3',
          description: '',
          position: 3,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
      ],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 0,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [
        {
          id: 118263,
          type: 0,
          value: 'вар 1',
          description: '',
          position: 1,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
        {
          id: 118264,
          type: 0,
          value: 'вар 2',
          description: '',
          position: 2,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
        {
          id: 118265,
          type: 0,
          value: 'вар 3',
          description: '',
          position: 3,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
      ],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: '',
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [
        {
          id: 4805,
          question_id: 166263,
          behavior: 4,
          variants: null,
          jump: 3,
          jump_question_id: 166705,
          jump_display_page_id: null,
          position: 1,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: '',
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 1,
      scaleRatingSetting: {
        id: 1137,
        foquz_question_id: 166263,
        start: 0,
        end: 100,
        step: 10,
      },
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: null,
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 3,
      question_id: 166264,
      answer: null,
      alias: '',
      name: '',
      description: 'Рейтинг NPS / Для вариантов',
      description_html: '<p>Рейтинг NPS / Для вариантов</p>',
      subdescription: '',
      type: 12,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [
        {
          id: 118266,
          type: 0,
          value: 'вар 1',
          description: '',
          position: 1,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
        {
          id: 118267,
          type: 0,
          value: 'вар 2',
          description: '',
          position: 2,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
        {
          id: 118268,
          type: 0,
          value: 'вар 3',
          description: '',
          position: 3,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
      ],
      detail_answers: [
        {
          id: 118266,
          type: 0,
          variant: 'вар 1',
          description: '',
          position: 1,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118267,
          type: 0,
          variant: 'вар 2',
          description: '',
          position: 2,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118268,
          type: 0,
          variant: 'вар 3',
          description: '',
          position: 3,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
      ],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 0,
      forAllRates: 0,
      answerText: '',
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [
        {
          id: 118266,
          type: 0,
          value: 'вар 1',
          description: '',
          position: 1,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
        {
          id: 118267,
          type: 0,
          value: 'вар 2',
          description: '',
          position: 2,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
        {
          id: 118268,
          type: 0,
          value: 'вар 3',
          description: '',
          position: 3,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          isChecked: false,
        },
      ],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: '',
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: {
        id: 6390,
        foquz_question_id: 166264,
        design: 1,
        start_point_color: '#F96261',
        end_point_color: '#00C968',
        start_label: '',
        end_label: '',
      },
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: '',
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 1,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: '',
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 4,
      question_id: 166265,
      answer: null,
      alias: '',
      name: '',
      description: 'ЗР для вариантов',
      description_html: '<p>ЗР для вариантов</p>',
      subdescription: '',
      type: 7,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [
        {
          id: 118272,
          type: 0,
          value: 'вар 1',
          description: '',
          position: 1,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          stars: null,
          extra_question: 0,
        },
        {
          id: 118273,
          type: 0,
          value: 'вар 2',
          description: '',
          position: 2,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          stars: null,
          extra_question: 0,
        },
        {
          id: 118274,
          type: 0,
          value: 'вар 3',
          description: '',
          position: 3,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          stars: null,
          extra_question: 0,
        },
      ],
      detail_answers: [
        {
          id: 118272,
          type: 0,
          variant: 'вар 1',
          description: '',
          position: 1,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118273,
          type: 0,
          variant: 'вар 2',
          description: '',
          position: 2,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118274,
          type: 0,
          variant: 'вар 3',
          description: '',
          position: 3,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
      ],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 0,
      forAllRates: 0,
      answerText: '',
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [
        {
          id: 118272,
          type: 0,
          value: 'вар 1',
          description: '',
          position: 1,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          stars: null,
          extra_question: 0,
        },
        {
          id: 118273,
          type: 0,
          value: 'вар 2',
          description: '',
          position: 2,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          stars: null,
          extra_question: 0,
        },
        {
          id: 118274,
          type: 0,
          value: 'вар 3',
          description: '',
          position: 3,
          is_deleted: 0,
          file_id: null,
          file_url: null,
          preview_url: null,
          need_extra: 1,
          dictionary_element_id: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
          stars: null,
          extra_question: 0,
        },
      ],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: '',
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: {
        id: 35200,
        foquz_question_id: 166265,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: '',
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: '',
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 5,
      question_id: 166266,
      answer: null,
      alias: '',
      name: '',
      description: 'Простая матрица',
      description_html: '<p>Простая матрица</p>',
      subdescription: '',
      type: 13,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: '',
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: '',
      selectPlaceholderText: '',
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: {
        rows: [
          'вар 1',
          'вар 2',
          'вар 3',
        ],
        cols: [
          '1',
          '2',
          '3',
          '4',
          '5',
        ],
        points: [
          [
            '0',
            '0',
            '0',
            '0',
            '0',
          ],
          [
            '0',
            '0',
            '0',
            '0',
            '0',
          ],
          [
            '0',
            '0',
            '0',
            '0',
            '0',
          ],
        ],
        minRowsReq: '3',
        multiple_choice: '0',
        type: 'standart',
      },
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: '',
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: '',
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 6,
      question_id: 166267,
      answer: null,
      alias: '',
      name: '',
      description: '3Д матрица',
      description_html: '<p>3Д матрица</p>',
      subdescription: '',
      type: 21,
      mediaType: 0,
      questionContent: '',
      assessmentType: 1,
      assessmentVariantsType: 0,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 0,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: '',
      selectPlaceholderText: '',
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: {
        rows: [
          {
            id: 22830,
            type_id: 1,
            foquz_question_id: 166267,
            name: 'строка 1',
            description: '',
            position: 0,
            donor_variant_id: null,
            donor_dictionary_element_id: null,
            is_deleted: 0,
            variants: [],
            langs: [],
            isChecked: true,
          },
          {
            id: 22831,
            type_id: 1,
            foquz_question_id: 166267,
            name: 'строка 2',
            description: '',
            position: 1,
            donor_variant_id: null,
            donor_dictionary_element_id: null,
            is_deleted: 0,
            variants: [],
            langs: [],
            isChecked: true,
          },
        ],
        columns: [
          {
            id: 22832,
            type_id: 2,
            foquz_question_id: 166267,
            name: 'столбец 1',
            description: '',
            position: 0,
            donor_variant_id: null,
            donor_dictionary_element_id: null,
            is_deleted: 0,
            variants: [
              {
                id: 15414,
                matrix_element_id: 22832,
                name: 'плохо',
                position: 0,
                is_deleted: 0,
                langs: [],
              },
              {
                id: 15415,
                matrix_element_id: 22832,
                name: 'хорошо',
                position: 1,
                is_deleted: 0,
                langs: [],
              },
            ],
            langs: [],
            isChecked: true,
          },
          {
            id: 22833,
            type_id: 2,
            foquz_question_id: 166267,
            name: 'столбец 2',
            description: '',
            position: 1,
            donor_variant_id: null,
            donor_dictionary_element_id: null,
            is_deleted: 0,
            variants: [
              {
                id: 15416,
                matrix_element_id: 22833,
                name: 'плохо',
                position: 0,
                is_deleted: 0,
                langs: [],
              },
              {
                id: 15417,
                matrix_element_id: 22833,
                name: 'хорошо',
                position: 1,
                is_deleted: 0,
                langs: [],
              },
            ],
            langs: [],
            isChecked: true,
          },
        ],
      },
      reorder_required: false,
      matrixSettings: {
        rowsAboveVariants: '0',
      },
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: '',
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: '',
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 0,
      donorColumnsSelected: 0,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: null,
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 7,
      question_id: 166268,
      answer: null,
      alias: '',
      name: '',
      description: 'сем диф',
      description_html: '<p>сем диф</p>',
      subdescription: '',
      type: 14,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: '',
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [
        {
          id: 11554,
          question_id: 166268,
          start_label: '',
          end_label: '',
          position: 1,
        },
      ],
      semDifSetting: {
        id: 5231,
        foquz_question_id: 166268,
        form: 'rect',
        start_point_color: '#73808D',
        end_point_color: '#3F65F1',
      },
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: null,
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: '',
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: null,
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 8,
      question_id: 166269,
      answer: null,
      alias: '',
      name: '',
      description: '',
      description_html: '',
      subdescription: '',
      type: 16,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: null,
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: {
        id: 19383,
        question_id: 166269,
        screen_type: 1,
        show_question_number: 0,
        text: '<p>Текстовый блок</p>',
        complaint_button: 0,
        unsubscribe_button: 0,
        complaint_button_text: '',
        unsubscribe_button_text: '',
        poll_button_text: '',
        code: null,
        pool_id: null,
        ready_button: 0,
        ready_button_text: '',
        close_widget_button: 0,
        close_widget_button_text: '',
        external_link: '',
        scores_button: 0,
        scores_button_text: '',
        start_over_button: 0,
        start_over_button_text: '',
        logos_backcolor: '#FFFFFF',
        agreement: 0,
        agreement_text: '',
        socNetworks: {
          id: 19240,
          intermediate_block_id: 19383,
          social_networks_enabled: 0,
          social_networks: '{"vk":"1","ok":"1"}',
          form: 'round',
          style: 'style1',
          substrate: 0,
          total_counter: 0,
          location_for_total_counter: 'before',
          for_each_counter: 0,
          location_for_each_counter: null,
          size: '24',
          socialNetworks: {
            vk: '1',
            ok: '1',
          },
        },
        langs: [],
      },
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: null,
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: null,
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
    {
      id: 9,
      question_id: 166705,
      answer: null,
      alias: '',
      name: '',
      description: '',
      description_html: '',
      subdescription: '',
      type: 16,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 1,
      isValid: false,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: false,
      isHaveCustomField: 0,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      variants: [],
      values: [],
      arRegionsIDs: [],
      arDistrictsIDs: [],
      arCityIDs: [],
      arStreetsIDs: [],
      placeholderText: null,
      selectPlaceholderText: null,
      selfVariantPlaceholderText: null,
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [],
      questionViewLogic: [],
      starRatingOptions: [],
      min_choose_variants: null,
      max_choose_variants: null,
      self_variant_text: null,
      intermediateBlock: {
        id: 19420,
        question_id: 166705,
        screen_type: 3,
        show_question_number: 0,
        text: '<p>Конечный экран! (для теста логики перехода)</p>',
        complaint_button: 0,
        unsubscribe_button: 0,
        complaint_button_text: '',
        unsubscribe_button_text: '',
        poll_button_text: '',
        code: null,
        pool_id: null,
        ready_button: 0,
        ready_button_text: '',
        close_widget_button: 0,
        close_widget_button_text: '',
        external_link: '',
        scores_button: 0,
        scores_button_text: '',
        start_over_button: 0,
        start_over_button_text: '',
        logos_backcolor: '#FFFFFF',
        agreement: 0,
        agreement_text: '',
        langs: [],
      },
      dropdownVariants: 0,
      only_date_month: 0,
      random_variants_order: 0,
      wrongCondition: false,
      endScreenImages: [],
      skip: 0,
      skip_text: null,
      skip_variant: 0,
      skip_row: 0,
      skip_column: 0,
      show_tooltips: 0,
      skipped: 0,
      show_numbers: 0,
      show_labels: 0,
      fromOne: 0,
      donor: null,
      donor_rows: null,
      donor_columns: null,
      donorSelected: 1,
      donorColumnsSelected: 1,
      dictionary_id: null,
      dictionary_element_id: null,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      set_variants: 0,
      scaleRatingSetting: [],
      isHaveExtra: false,
      extra_required: 1,
      disable_select_category: 0,
      variants_with_files: 0,
      self_variant_comment_required: 0,
      self_variant_nothing: 0,
      self_variant_description: null,
      extra_question_type: 0,
      extra_question_rate_from: null,
      extra_question_rate_to: null,
      detail_question: null,
      self_variant_file: null,
      min_choose_extra_variants: null,
      max_choose_extra_variants: null,
      langs: [],
    },
  ], additionalData)
}

function fourthPartQuestions(additionalData) {
  return merge([
    {
      ...classifierQuestionOptions({
        id: 1,
        question_id: 166320,
        description: 'Выбор филиала',
        description_html: '<p>Выбор филиала</p>',
        dictionaryTree: {
          'Москва': {
            id: 100,
            title: 'Москва',
            description: '',
            position: 1,
            isCategory: true,
            children: {
              Москва: {
                id: 452,
                title: 'Москва',
                description: '',
                position: 1,
                isCategory: false,
              },
            },
          },
          'Самара': {
            id: 101,
            title: 'Самара',
            description: '',
            position: 2,
            isCategory: true,
            children: {
              Самара: {
                id: 453,
                title: 'Самара',
                description: '',
                position: 1,
                isCategory: false,
              },
            },
          },
          'Санкт-Петербург': {
            id: 102,
            title: 'Санкт-Петербург',
            description: '',
            position: 3,
            isCategory: true,
            children: {
              'Санкт-Петербург': {
                id: 454,
                title: 'Санкт-Петербург',
                description: '',
                position: 1,
                isCategory: false,
              },
            },
          },
        },
        dictionary_sort: 'default',
        dictionary_list_type: 'list',
        dropdownVariants: 0,
        max_choose_variants: 1,
        disable_select_category: 0,
        show_tooltips: 1,
        skip: 0,
        skip_text: 'Пропустить этот вопрос',
        comment_enabled: 0,
        comment_required: 0,
        comment_label: 'Дополнительные комментарии',
        placeholderText: 'Введите ваши комментарии здесь',
      }),
      answer: null,
      alias: '',
      name: '',
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      priorityAnswer: null,
      answerType: 2,
      textFieldParam: { min: 0, max: 250 },
      selfVariantParam: { min: 0, max: 250 },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      values: [],
      isRequired: 0,
      questionLogic: [
        {
          id: 4874,
          question_id: 166320,
          behavior: BEHAVIOR_SELECT,
          variants: [452, 453],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4875,
          question_id: 166320,
          behavior: BEHAVIOR_MISS,
          variants: null,
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166706,
          jump_display_page_id: null,
          position: 2,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      langs: [],
    },
    {
      id: 2,
      question_id: 166418,
      answer: null,
      alias: '',
      name: '',
      description: 'Классификатор / Простой список',
      description_html: '<p>Классификатор / Простой список</p>',
      subdescription: '',
      type: 19,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      filials: [],
      dictionaryTree: {
        'Категория 1': {
          id: 161,
          title: 'Категория 1',
          description: 'Проверка ',
          langs: null,
          position: 66,
          isCategory: true,
          children: {
            'Категория 11': {
              id: 162,
              title: 'Категория 11',
              description: '',
              langs: null,
              position: 0,
              isCategory: true,
              children: {
                '222': {
                  id: 1876,
                  title: '222',
                  description: 'Проверка',
                  langs: null,
                  position: 1,
                  isCategory: false,
                },
                'Элемент 1': {
                  id: 163,
                  title: 'Элемент 1',
                  description: 'Проверка 2',
                  langs: null,
                  position: 0,
                  isCategory: false,
                },
              },
            },
          },
        },
        'Категория 2': {
          id: 164,
          title: 'Категория 2',
          description: 'Проверка ',
          langs: null,
          position: 67,
          isCategory: true,
          children: {
            'Категория 21': {
              id: 165,
              title: 'Категория 21',
              description: '',
              langs: null,
              position: 0,
              isCategory: true,
              children: {
                'Элемент 2': {
                  id: 166,
                  title: 'Элемент 2',
                  description: '',
                  langs: null,
                  position: 0,
                  isCategory: false,
                },
              },
            },
          },
        },
        'Элемент 3': {
          id: 167,
          title: 'Элемент 3',
          description: 'Проверка 444 4444 444 444',
          langs: null,
          position: 181,
          isCategory: false,
        },
      },
      priorityAnswer: null,
      answerType: 2,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      questionLogic: [
        {
          id: 4876,
          question_id: 166418,
          behavior: BEHAVIOR_SELECT,
          variants: [
            163,
          ],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4877,
          question_id: 166418,
          behavior: BEHAVIOR_MISS,
          variants: null,
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166706,
          jump_display_page_id: null,
          position: 2,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      skip: 1,
      dictionary_list_type: 'list',
      dictionary_sort: 'default',
      langs: [],
    },
    {
      id: 3,
      question_id: 166426,
      answer: null,
      alias: '',
      name: '',
      description: 'Классификатор / Древовидный список',
      description_html: '<p>Классификатор / Древовидный список</p>',
      subdescription: '',
      type: 19,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      filials: [],
      dictionaryTree: {
        'Категория 1': {
          id: 161,
          title: 'Категория 1',
          description: 'Проверка ',
          langs: null,
          position: 66,
          isCategory: true,
          children: {
            'Категория 11': {
              id: 162,
              title: 'Категория 11',
              description: '',
              langs: null,
              position: 0,
              isCategory: true,
              children: {
                '222': {
                  id: 1876,
                  title: '222',
                  description: 'Проверка',
                  langs: null,
                  position: 1,
                  isCategory: false,
                },
                'Элемент 1': {
                  id: 163,
                  title: 'Элемент 1',
                  description: 'Проверка 2',
                  langs: null,
                  position: 0,
                  isCategory: false,
                },
              },
            },
          },
        },
        'Категория 2': {
          id: 164,
          title: 'Категория 2',
          description: 'Проверка ',
          langs: null,
          position: 67,
          isCategory: true,
          children: {
            'Категория 21': {
              id: 165,
              title: 'Категория 21',
              description: '',
              langs: null,
              position: 0,
              isCategory: true,
              children: {
                'Элемент 2': {
                  id: 166,
                  title: 'Элемент 2',
                  description: '',
                  langs: null,
                  position: 0,
                  isCategory: false,
                },
              },
            },
          },
        },
        'Элемент 3': {
          id: 167,
          title: 'Элемент 3',
          description: 'Проверка 444 4444 444 444',
          langs: null,
          position: 181,
          isCategory: false,
        },
      },
      priorityAnswer: null,
      answerType: 2,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      questionLogic: [
        {
          id: 4878,
          question_id: 166426,
          behavior: BEHAVIOR_SELECT,
          variants: [
            163,
            1876,
          ],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4879,
          question_id: 166426,
          behavior: BEHAVIOR_MISS,
          variants: null,
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166706,
          jump_display_page_id: null,
          position: 2,
        },
      ],
      questionViewLogic: [],
      skip: 1,
      dictionary_list_type: 'tree',
      dictionary_sort: 'default',
      set_variants: 0,
      langs: [],
    },
    {
      id: 4,
      question_id: 166427,
      answer: null,
      alias: '',
      name: '',
      description: 'Варианты ответов',
      description_html: '<p>Варианты ответов</p>',
      subdescription: '',
      type: 1,
      mediaType: 0,
      questionContent: '',
      assessmentType: 1,
      assessmentVariantsType: 1,
      detail_answers: [
        {
          id: 118410,
          type: 0,
          variant: 'вар 1',
          description: '',
          position: 1,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
        {
          id: 118411,
          type: 0,
          variant: 'вар 2',
          description: '',
          position: 2,
          points: null,
          without_points: 0,
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
          comment_required: 0,
          dictionary_element_id: null,
          file_id: null,
          file_url: null,
          preview_url: null,
          detail_question: '',
          question_detail_id: null,
          extra_question_rate_from: 0,
          extra_question_rate_to: 10,
          extra_required: 1,
          min_choose_extra_variants: null,
          max_choose_extra_variants: null,
          variants_with_files: 0,
          self_variant_text: '',
          self_variant_placeholder_text: '',
          variants_element_type: 0,
          for_all_rates: 1,
          placeholder_text: '',
          self_variant_minlength: 0,
          self_variant_maxlength: 250,
          text_variant_minlength: 0,
          text_variant_maxlength: 250,
          is_self_answer: 0,
        },
      ],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 0,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 0,
      isValid: true,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: true,
      isHaveCustomField: 1,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      placeholderText: '',
      selectPlaceholderText: null,
      selfVariantPlaceholderText: '',
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [
        {
          id: 4880,
          question_id: 166427,
          behavior: BEHAVIOR_SELECT,
          variants: [
            0,
          ],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4881,
          question_id: 166427,
          behavior: BEHAVIOR_MISS,
          variants: null,
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166706,
          jump_display_page_id: null,
          position: 2,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      skip: 1,
      skip_text: '',
      langs: [],
    },
    {
      id: 5,
      question_id: 166428,
      answer: null,
      alias: '',
      name: '',
      description: 'варианты ответов с медиа',
      description_html: '<p>варианты ответов с медиа</p>',
      subdescription: '',
      type: 1,
      mediaType: 0,
      questionContent: '',
      assessmentType: 1,
      assessmentVariantsType: 1,
      detail_answers: [
        { id: 1, variant: 'Вариант 1', is_deleted: 0, file_url: '/uploads/img-preview-1.jpeg', preview_url: '/uploads/img-preview-1.jpeg' },
        { id: 2, variant: 'Вариант 2', is_deleted: 0, file_url: '/uploads/img-preview-2.jpeg', preview_url: '/uploads/img-preview-2.jpeg' },
        { id: 3, variant: 'Вариант 3', is_deleted: 0, file_url: '/uploads/img-preview-3.jpeg', preview_url: '/uploads/img-preview-3.jpeg' },
      ],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 0,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      comment_enabled: 0,
      comment_required: 0,
      isRequired: 0,
      isValid: true,
      checkAll: 0,
      minDishPrice: 0,
      isHaveComment: true,
      isHaveCustomField: 1,
      filesLength: 4,
      fileTypes: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      isCustomFieldChecked: false,
      radioButtonCheckedValue: '',
      textFieldValue: '',
      value: '',
      dateValue: '',
      timeValue: '',
      files: [],
      placeholderText: '',
      selectPlaceholderText: null,
      selfVariantPlaceholderText: '',
      chooseType: null,
      smiles: [],
      chooseMedia: [],
      maskConfig: {},
      npsRatingSetting: null,
      differentialRows: [],
      semDifSetting: null,
      matrixElements: [],
      reorder_required: false,
      matrixSettings: null,
      showQuestion: true,
      questionLogic: [
        {
          id: 4882,
          question_id: 166428,
          behavior: BEHAVIOR_SELECT,
          variants: [
            0,
          ],
          jump: JUMP_TO_DEFAULT_END_SCREEN,
          jump_question_id: null,
          jump_display_page_id: null,
          position: 1,
        },
        {
          id: 4883,
          question_id: 166428,
          behavior: BEHAVIOR_MISS,
          jump: JUMP_TO_END_SCREEN,
          jump_question_id: 166706,
          jump_display_page_id: null,
          position: 2,
        },
      ],
      questionViewLogic: [],
      starRatingOptions: [],
      skip: 1,
      langs: [],
    },
    {
      id: 6,
      question_id: 166706,
      answer: null,
      alias: '',
      name: '',
      description: '',
      description_html: '',
      subdescription: '',
      type: 16,
      mediaType: 0,
      questionContent: '',
      assessmentType: 0,
      assessmentVariantsType: 1,
      assessmentVariants: [],
      detail_answers: [],
      filials: [],
      dictionaryTree: [],
      priorityAnswer: null,
      answerType: 2,
      forAllRates: 0,
      answerText: null,
      stars: null,
      maskType: 0,
      dateType: null,
      category: 0,
      b_name: 0,
      gallery: [],
      enableGallery: false,
      smileType: null,
      variantsType: 1,
      comment: '',
      comment_label: 'Ваш комментарий',
      endScreenImages: [],
      textFieldParam: {
        min: 0,
        max: 250,
      },
      selfVariantParam: {
        min: 0,
        max: 250,
      },
      intermediateBlock: {
        id: 19421,
        question_id: 166706,
        screen_type: 3,
        show_question_number: 0,
        text: '<p>Экран появится при пропуске вопроса</p>',
      },
      langs: [],
    },

  ], additionalData)
}

function multipleQuestionsPerPage(additionalData) {
  return merge([
    {
      id: 1,
      question_id: 166430,
      description: 'ЗР 1',
      description_html: '<p>ЗР 1</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35278,
        foquz_question_id: 166430,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [
        {
          id: 4883,
          question_id: 166430,
          behavior: 1,
          variants: [
            5,
          ],
          jump: 1,
          jump_question_id: null,
          jump_display_page_id: 2521,
          position: 1,
        },
      ],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 2,
      question_id: 166431,
      description: 'ЗР 2',
      description_html: '<p>ЗР 2</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35279,
        foquz_question_id: 166431,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [
        {
          id: 4884,
          question_id: 166431,
          behavior: 1,
          variants: [
            5,
          ],
          jump: 1,
          jump_question_id: null,
          jump_display_page_id: 2523,
          position: 1,
        },
      ],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 3,
      question_id: 166441,
      description: 'ЗР 3',
      description_html: '<p>ЗР 3</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35282,
        foquz_question_id: 166441,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 4,
      question_id: 166443,
      description: 'ЗР 4',
      description_html: '<p>ЗР 4</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35283,
        foquz_question_id: 166443,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 5,
      question_id: 166445,
      description: 'ЗР 5',
      description_html: '<p>ЗР 5</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35285,
        foquz_question_id: 166445,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 6,
      question_id: 166448,
      description: 'ЗР 6',
      description_html: '<p>ЗР 6</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35286,
        foquz_question_id: 166448,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 7,
      question_id: 166449,
      description: 'ЗР 7',
      description_html: '<p>ЗР 7</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35287,
        foquz_question_id: 166449,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 8,
      question_id: 166450,
      description: 'ЗР 8',
      description_html: '<p>ЗР 8</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35288,
        foquz_question_id: 166450,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [],
      ...starRatingQuestionOptions({}),
    },
    {
      id: 9,
      question_id: 166451,
      description: 'ЗР 9',
      description_html: '<p>ЗР 9</p>',
      type: 15,
      isRequired: 1,
      starRatingOptions: {
        id: 35289,
        foquz_question_id: 166451,
        color: 'rgb(248, 205, 28)',
        count: 5,
        size: 'md',
        labels: '["","","","",""]',
        extra_question_rate_from: null,
        extra_question_rate_to: null,
        labelsArray: [
          '',
          '',
          '',
          '',
          '',
        ],
      },
      questionLogic: [],
      ...starRatingQuestionOptions({}),
    },
  ], additionalData)
}

const logicMockData = {
  'logic-part-1': () => {
    const defaultData = defaultQuestionsData()

    defaultData.answer.status = 'open'
    defaultData.questions = cleanQuestionsList(firstPartQuestions())
    return defaultData
  },
  'logic-part-2': () => {
    const defaultData = defaultQuestionsData()
    defaultData.answer.status = 'open'
    defaultData.questions = cleanQuestionsList(secondPartQuestions())
    return defaultData
  },
  'logic-part-3': () => {
    const defaultData = defaultQuestionsData()
    defaultData.answer.status = 'open'
    defaultData.questions = cleanQuestionsList(thirdPartQuestions())
    return defaultData
  },
  'logic-part-4': () => {
    const defaultData = defaultQuestionsData()
    defaultData.answer.status = 'open'
    defaultData.questions = cleanQuestionsList(fourthPartQuestions())
    return defaultData
  },
  'logic-multiple-questions-per-page': () => {
    const defaultData = defaultQuestionsData()
    defaultData.answer.status = 'open'
    defaultData.questions = cleanQuestionsList(multipleQuestionsPerPage())
    defaultData.poll = {
      id: 51412,
      is_published: 0,
      is_answer_limited: 0,
      showUserConsentBanner: false,
      displaySetting: {
        id: 1677,
        poll_id: 51412,
        type: 2,
        random_order: 0,
        random_exclusion: null,
      },
      displayPages: [
        {
          id: 2520,
          foquz_poll_id: 51412,
          name: null,
          random_order: 0,
          order: 1,
          random_exclusion: 0,
          questions: [
            {
              id: 166430,
            },
            {
              id: 166431,
            },
            {
              id: 166441,
            },
          ],
        },
        {
          id: 2522,
          foquz_poll_id: 51412,
          name: null,
          random_order: 0,
          order: 2,
          random_exclusion: 0,
          questions: [
            {
              id: 166443,
            },
            {
              id: 166445,
            },
          ],
        },
        {
          id: 2521,
          foquz_poll_id: 51412,
          name: null,
          random_order: 0,
          order: 3,
          random_exclusion: 0,
          questions: [
            {
              id: 166448,
            },
            {
              id: 166449,
            },
          ],
        },
        {
          id: 2523,
          foquz_poll_id: 51412,
          name: null,
          random_order: 0,
          order: 4,
          random_exclusion: 0,
          questions: [
            {
              id: 166450,
            },
            {
              id: 166451,
            },
          ],
        },
      ],
      foquzPollLangs: [
        {
          default: 1,
          id: 19760,
          lang_id: 1,
          name: 'Русский',
          original_name: 'Русский',
          code: 'ru-RU',
          short_code: 'ru',
        },
        {
          default: 0,
          id: 19759,
          lang_id: 2,
          name: 'Английский',
          original_name: 'English',
          code: 'en-US',
          short_code: 'en',
        },
      ],
      answerLimitIsOver: false,
      testModeLimitIsOver: false,
    }
    defaultData.questions = cleanQuestionsList(multipleQuestionsPerPage())
    return defaultData
  },
}

export default logicMockData
