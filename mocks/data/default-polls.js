import { RATING_QUESTION, STARS_QUESTION } from '@entities/question/model/types'
import defaultQuestionsData from './defaultQuestionsData'

export function defaultPoll() {
  return defaultQuestionsData({
    questions: [
      {
        description: 'Звездный рейтинг',
        description_html: '<p>Звездный рейтинг<p>',
        type: STARS_QUESTION,
      },
    ],
  })
}

export function defaultTwoQuestions() {
  return defaultQuestionsData({
    questions: [
      {
        description: 'Звездный рейтинг',
        description_html: '<p>Звездный рейтинг<p>',
        id: 1,
        question_id: 34391,
        type: STARS_QUESTION,
      },
      {
        description: 'Второй вопрос',
        description_html: '<p>Второй вопрос<p>',
        type: RATING_QUESTION,
        id: 2,
        question_id: 34392,
        starRatingOptions: {
          count: 5,
          size: 'md',
          labelsArray: ['1', '2', '3', '4', '5'],
        },
      },
    ],
  })
}

export function defaultWithSubdescription() {
  return defaultQuestionsData({
    questions: [
      {
        description: 'Звездный рейтинг',
        description_html: '<p>Звездный рейтинг<p>',
        subdescription: '<p>Оцените наш сервис</p>',
        type: STARS_QUESTION,
      },
    ],
  })
}

export function defaultWithUnrequiredText() {
  return defaultQuestionsData({
    questions: [
      {
        description: 'Звездный рейтинг',
        description_html: '<p>Звездный рейтинг<p>',
        subdescription: '<p>Оцените наш сервис</p>',
        unrequired_text: 'Необязательный',
        isRequired: 0,
        type: STARS_QUESTION,
      },
    ],
  })
}

export function defaultWithUnrequiredCustomText() {
  return defaultQuestionsData({
    questions: [
      {
        description: 'Звездный рейтинг',
        description_html: '<p>Звездный рейтинг<p>',
        subdescription: 'Оцените наш сервис',
        isRequired: 0,
        type: STARS_QUESTION,
      },
    ],
    design: {
      unrequired_text: 'Необязательный (проходите, если хотите)',
    },
  })
}

export function defaultWithoutPrevButton() {
  return defaultQuestionsData({
    design: {
      show_prev_button: 0,
    },
  })
}

export function defaultWithoutPrevButtonTwoQuestions() {
  return defaultQuestionsData({
    design: {
      show_prev_button: 0,
    },
    questions: [
      {
        type: STARS_QUESTION,
        starRatingOptions: {
          count: 5,
        },
      },
      {
        type: RATING_QUESTION,
        starRatingOptions: {
          count: 5,
        },
      },
    ],
  })
}

export function defaultWithBgColor() {
  return defaultQuestionsData({
    design: {
      main_place_color: 'rgba(255, 0, 0, 0)',
      background_color: 'rgba(255, 0, 0, 1)',
    },
  })
}

export function defaultWithBgImages() {
  return defaultQuestionsData({
    design: {
      main_place_color: 'rgba(255, 0, 0, 0.6)',
      background_color: 'rgba(255, 0, 0, 0.5)',
      mobile_background_image: '/uploads/clouds.jpg',
      background_image: '/uploads/summer.jpg',
    },
  })
}

export function customDesign() {
  return defaultQuestionsData({
    questions: [
      {
        type: STARS_QUESTION,
        show_labels: 1,
        starRatingOptions: {
          count: 5,
          size: 'md',
          color: 'red',
          labelsArray: ['1', '2', '3', '4', '5'],
        },
      },
    ],
    poll: {
      is_published: true,
    },
    design: {
      main_color: 'rgba(255, 0, 0, 1)',
      background_color: 'rgba(0, 255, 0, 1)',
      header_color: '#0000FF',
      star_color: '#FF00FF',
      rating_color: '#00FFFF',
      nps_color_from: '#FFFF00',
      nps_color_to: '#000000',
      sem_diff_color_from: '#800080',
      sem_diff_color_to: '#FFA500',
      is_use_header: 1,
      font_family: 'Georgia, Times New Roman, Times, serif',
      title_font_size: '24',
      font_size: '12',
      text_on_bg: 'rgba(255, 255, 255, 1)',
      text_on_place: 'rgba(0, 0, 0, 1)',
      link_color: 'rgba(255, 0, 0, 1)',
      from_template: 0,
      logo_link: 'http://custom-logo.com',
      logo_type: 'text',
      logo_text: 'Custom Logo',
      logo_font_family: 'Courier, sans-serif',
      logo_color: '#FF0000',
      back_text: 'Назад идем',
      back_button_background_color: 'rgba(255, 255, 255, 1)',
      back_button_text_color: 'rgba(0, 0, 0, 1)',
      back_button_stroke_color: 'rgba(0, 0, 0, 1)',
      back_button_radius: 12,
      next_button_background_color: 'rgba(234, 100, 0, 1)',
      next_button_text_color: 'rgba(255, 255, 255, 1)',
      next_button_stroke_color: 'rgba(255, 255, 255, 1)',
      next_button_radius: 12,
      start_button_background_color: 'rgba(0, 255, 0, 1)',
      start_button_text_color: 'rgba(0, 0, 0, 1)',
      start_button_stroke_color: 'rgba(0, 0, 0, 1)',
      start_button_radius: 12,
      next_text: 'Дем дальш',
      finish_text: 'Финиш',
      finish_link: '',
      unrequired_text: 'Необязательный (проходите, если хотите)',
      show_process: 1,
      darkening_background: 1,
      place_under_buttons: 'light',
      show_prev_button: 1,
      main_place_color: 'rgba(255, 255, 0, 1)',
      choose_language: 1,
      full_width: 1,
      disable_question_autoscroll: 0,
      backgroundImage: '/img/custom-bg.jpg',
      logo: '/img/custom-logo.png',
      mainColor: '#FF0000',
      logoLink: 'http://custom-logo.com',
      bgColor: 'rgba(0, 255, 0, 1)',
      headerColor: '#0000FF',
      starColor: '#FF00FF',
      ratingColor: '#00FFFF',
      npsColorFrom: '#FFFF00',
      npsColorTo: '#000000',
      semDiffColorFrom: '#800080',
      semDiffColorTo: '#FFA500',
      textOnBg: '#FFFFFF',
      textOnPlace: '#000000',
      linkColor: '#FF0000',
      fontFamily: 'Arial, sans-serif',
      titleFontSize: '24',
      fontSize: '12',
      isUseHeader: true,
      templateId: 0,
      logoType: 'text',
      logoText: 'Custom Logo',
      logoFontFamily: 'Impact, Charcoal, sans-serif',
      logoColor: '#FF0000',
      backText: 'Go Back',
      nextText: 'Дем дальш',
      finishText: 'Финиш',
      finishLink: '',
      placeUnderButtons: 'light',
    },
  })
}

export const defaultPollsMap = {
  'default': defaultPoll,
  'default-2-questions': defaultTwoQuestions,
  'default-with-subdescription': defaultWithSubdescription,
  'default-with-unrequired-text': defaultWithUnrequiredText,
  'default-with-unrequired-custom-text': defaultWithUnrequiredCustomText,
  'default-without-prev-button': defaultWithoutPrevButton,
  'default-without-prev-button-2-questions': defaultWithoutPrevButtonTwoQuestions,
  'default-with-bg-color': defaultWithBgColor,
  'default-with-bg-images': defaultWithBgImages,
  'custom-design': customDesign,
}
