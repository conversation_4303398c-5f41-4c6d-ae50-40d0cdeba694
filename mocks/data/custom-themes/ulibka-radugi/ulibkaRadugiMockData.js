import { EXTRA_QUESTION_TYPE } from '@/shared/constants'
import defaultQuestionsData from '../../defaultQuestionsData'
import { classifierQuestionOptions } from '../../questions/classifierMockData'
import { dateOptions } from '../../questions/DateQuestionMockData'
import { diffQuestionOptions } from '../../questions/diffMockData'
import { galleryOptions } from '../../questions/galleryMockData'
import { intermediateBlockOptions } from '../../questions/interBlockMockData'
import { matrixQuestionOptions } from '../../questions/matrixMockData'
import { mediaVariantsOptions } from '../../questions/mediaVariantsMockData'
import { priorityQuestionOptions } from '../../questions/priorityMockData'
import { getQuizMockData } from '../../questions/quizMockData'
import { ratingNpsOptions } from '../../questions/ratingNpsData'
import { scaleOptions } from '../../questions/scaleMockData'
import { smileRatingOptions } from '../../questions/smileRatingMockData'
import { starRatingQuestionOptions } from '../../questions/starRatingMockData'
import { starRatingVariantsOptions } from '../../questions/starRatingVariantsMockData'
import { textOptions } from '../../questions/textQuestionMockData'
import { getDesignMockData } from './design'

function transformedQuizMockData(data) {
  const { values, ...rest } = data
  return {
    ...rest,
    values: values.map(value => ({
      ...value,
      isRequired: 0,
      maskConfig: value.maskConfig && {
        ...value.maskConfig,
        name: value.maskConfig.name && {
          ...value.maskConfig.name,
          required: 'false',
        },
        surname: value.maskConfig.surname && {
          ...value.maskConfig.surname,
          required: 'false',
        },
        patronymic: value.maskConfig.patronymic && {
          ...value.maskConfig.patronymic,
          required: 'false',
        },
      },
    })),
  }
}

export const ulibkaRadugiMockData = {
  'ulibka-radugi': () =>
    defaultQuestionsData({
      design: getDesignMockData(),
      showFoquzLink: false,
      showAdv: false,
      poll: {
        is_published: 1,
      },
      customThemes: {
        templateId: 1,
      },
      answer: {
        status: 'open',
      },
      questions: [
        // Question ID 177644: Intermediate Block (screen_type: 2)
        intermediateBlockOptions({
          id: 1,
          question_id: 177644,
          type: 16,
          description: '',
          description_html: '',
          intermediateBlock: {
            id: 20511,
            screen_type: 2,
            show_question_number: 0,
            text: '<p><span style="font-size:34px;">Пожалуйста, оцените нас!</span></p><p><br/><span style="font-size:16px;">Опрос займет не больше минуты. </span><br /><span style="font-size:16px;">И поможет сделать процесс покупок еще приятнее!</span></p>',
            agreement: 0,
            logos_backcolor: 'transparent',
          },
          endScreenImages: [
            {
              id: 2869,
              foquz_question_id: 177644,
              logo: '/uploads/custom-themes/ulibka-radugi/start-screen-img.svg',
              description: '',
              position: 1,
            },
          ],
          isRequired: 1,
        }),

        // Question ID 177643: NPS Rating (standard)
        ratingNpsOptions({
          id: 2,
          question_id: 177643,
          description:
            'Оцените, насколько вы удовлетворены последним посещением магазина',
          description_html:
            '<p>Оцените, насколько вы удовлетворены последним посещением магазина</p>',
          type: 12,
          fromOne: 1,
          npsRatingSetting: {
            design: 3,
            start_point_color: 'rgb(233, 233, 233)',
            end_point_color: 'rgb(233, 233, 233)',
            start_label: 'Совершенно не удовлетворен(-а)',
            end_label: 'Полностью удовлетворен(-а)',
          },
          isRequired: 1,
        }),

        // Question ID 177651: NPS Rating (with variants)
        ratingNpsOptions({
          id: 3,
          question_id: 177651,
          description: 'Оцените, насколько вы удовлетворены',
          description_html: '<p>Оцените, насколько вы удовлетворены</p>',
          type: 12,
          fromOne: 1,
          set_variants: 1,
          detail_answers: [
            { id: 128738, variant: 'Выбором товаров', is_deleted: 0 },
            { id: 128739, variant: 'Условиями акций', is_deleted: 0 },
            { id: 128740, variant: 'Сотрудниками', is_deleted: 0 },
            { id: 128741, variant: 'Выбором товаров (2)', is_deleted: 0 },
            { id: 128742, variant: 'Условиями акций (2)', is_deleted: 0 },
            { id: 128743, variant: 'Сотрудниками (2)', is_deleted: 0 },
            { id: 128744, variant: 'Выбором товаров (3)', is_deleted: 0 },
            { id: 128745, variant: 'Условиями акций (3)', is_deleted: 0 },
            { id: 128746, variant: 'Сотрудниками (3)', is_deleted: 0 },
            { id: 128747, variant: 'Выбором товаров (4)', is_deleted: 0 },
            { id: 128748, variant: 'Условиями акций (4)', is_deleted: 0 },
            { id: 128749, variant: 'Сотрудниками (4)', is_deleted: 0 },
          ],
          npsRatingSetting: {
            design: 3,
            start_point_color: 'rgb(233, 233, 233)',
            end_point_color: 'rgb(233, 233, 233)',
            start_label: 'Совершенно не удовлетворен(-а)',
            end_label: 'Полностью удовлетворен(-а)',
          },
          isRequired: 0,
        }),

        // Question ID 177657: NPS Rating (with variants)
        ratingNpsOptions({
          id: 5,
          question_id: 177657,
          description:
            'Оцените, насколько вы удовлетворены последним посещением магазина',
          description_html:
            '<p>Оцените, насколько вы удовлетворены последним посещением магазина</p>',
          type: 12,
          fromOne: 1,
          set_variants: 1,
          detail_answers: [
            { id: 128750, variant: 'Уровенем цен', is_deleted: 0 },
            { id: 128751, variant: 'Уровенем цен', is_deleted: 0 },
          ],
          npsRatingSetting: {
            design: 3,
            start_point_color: 'rgb(233, 233, 233)',
            end_point_color: 'rgb(233, 233, 233)',
            start_label: 'Совершенно не удовлетворен(-а)',
            end_label: 'Полностью удовлетворен(-а)',
          },
          isRequired: 0,
        }),

        // Question ID 177658: NPS Rating (with variants and extra question)
        ratingNpsOptions({
          id: 6,
          question_id: 177658,
          description:
            'Оцените, насколько вы удовлетворены последним посещением магазина',
          description_html:
            '<p>Оцените, насколько вы удовлетворены последним посещением магазина</p>',
          type: 12,
          fromOne: 1,
          set_variants: 1,
          detail_answers: [
            {
              id: 128752,
              variant: 'Комфортом в магазине',
              is_deleted: 0,
              extra_question: 0,
              need_extra: 1,
            },
            {
              id: 128753,
              variant: 'Уровенем цен',
              is_deleted: 0,
              extra_question: 0,
              need_extra: 1,
            },
            {
              id: 128754,
              variant: 'Сотрудники не знают товары',
              extra_question: 1,
              is_deleted: 0,
              file_id: 128754,
              preview_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
              file_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
            },
            {
              id: 128755,
              variant: 'Сотрудники не знают товары',
              extra_question: 1,
              is_deleted: 0,
              file_id: 128755,
              preview_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
              file_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
            },
            {
              id: 128756,
              variant: 'Сотрудники не знают товары',
              extra_question: 1,
              is_deleted: 0,
              file_id: 128756,
              preview_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
              file_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
            },
            {
              id: 128757,
              variant: 'Сотрудники не знают товары',
              extra_question: 1,
              is_deleted: 0,
              file_id: 128757,
              preview_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
              file_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
            },
            {
              id: 128758,
              variant: 'Сотрудники не знают товары',
              extra_question: 1,
              is_deleted: 0,
              file_id: 128758,
              preview_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
              file_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
            },
            {
              id: 128759,
              variant: 'Сотрудники не знают товары',
              extra_question: 1,
              is_deleted: 0,
              file_id: 128759,
              preview_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
              file_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
            },
            {
              id: 128760,
              variant: 'Сотрудники не знают товары',
              extra_question: 1,
              is_deleted: 0,
              file_id: 128760,
              preview_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
              file_url:
                '/uploads/custom-themes/ulibka-radugi/variants-gallery-item-img.svg',
            },
          ],
          npsRatingSetting: {
            design: 3,
            start_point_color: 'rgb(233, 233, 233)',
            end_point_color: 'rgb(233, 233, 233)',
            start_label: 'Совершенно не удовлетворен(-а)',
            end_label: 'Полностью удовлетворен(-а)',
          },
          isHaveExtra: true,
          detail_question: 'Что особенно не понравилось?',
          isRequired: 0,
          extra_question_type: EXTRA_QUESTION_TYPE.SINGLE,
          extra_question_rate_from: 1,
          extra_question_rate_to: 5,
          variantsType: 1,
          variants_with_files: true,
        }),

        // Question ID 178074: Star Rating
        starRatingQuestionOptions({
          id: 7,
          question_id: 178074,
          description: 'ЗР',
          description_html: '<p>ЗР</p>',
          type: 15,
          starRatingOptions: {
            count: 5,
            color: 'rgb(131, 39, 216)',
            size: 'md',
            labelsArray: ['', '', '', '', ''],
          },
          isRequired: 0,
        }),

        // Question ID 178075: Star Rating (Assuming type 18 is similar to star rating)
        starRatingQuestionOptions({
          id: 8,
          question_id: 178075,
          description: 'Рейтинг',
          description_html: '<p>Рейтинг</p>',
          type: 18,
          starRatingOptions: {
            count: 5,
            color: 'rgb(131, 39, 216)',
            size: 'md',
            labelsArray: ['', '', '', '', ''],
          },
          isRequired: 0,
        }),

        // Question ID 178076: Variants (Multiple Choice)
        {
          id: 9,
          question_id: 178076,
          description: 'Варианты ответов',
          description_html: '<p>Варианты ответов</p>',
          type: 1,
          variantsType: 1,
          detail_answers: [
            { id: 129493, variant: 'вар 1', is_deleted: 0 },
            { id: 129494, variant: 'вар 2', is_deleted: 0 },
            { id: 129495, variant: 'вар 3', is_deleted: 0 },
          ],
          comment_enabled: 1,
          isHaveCustomField: 1,
          isRequired: 0,
        },

        // Question ID 178077: Text Question
        textOptions({
          id: 10,
          question_id: 178077,
          description: 'Текстовый ответ',
          description_html: '<p>Текстовый ответ</p>',
          type: 2,
          variantsType: 0,
          maskType: 0,
          placeholderText: 'подсказка',
          isRequired: 0,
        }),

        // Question ID 178078: Date Question
        dateOptions({
          id: 11,
          question_id: 178078,
          description: 'Дата/время',
          description_html: '<p>Дата/время</p>',
          type: 3,
          dateType: 0,
          only_date_month: 0,
          isRequired: 0,
        }),

        // Question ID 178079: Address Question (Treated as text)
        textOptions({
          id: 12,
          question_id: 178079,
          description: 'Адрес',
          description_html: '<p>Адрес</p>',
          type: 4,
          variantsType: 1,
          maskType: 0,
          isRequired: 0,
        }),

        // Question ID 178080: File Upload Question
        {
          id: 13,
          question_id: 178080,
          description: 'Загрузка файла',
          description_html: '<p>Загрузка файла</p>',
          type: 5,
          filesLength: 4,
          isRequired: 0,
        },

        // Question ID 178081: Survey/Form Question
        transformedQuizMockData(
          getQuizMockData({
            id: 14,
            question_id: 178081,
            description: 'Анкета',
            description_html: '<p>Анкета</p>',
          }),
        ),
        // Question ID 178082: Priority Question
        priorityQuestionOptions({
          id: 15,
          question_id: 178082,
          description: 'Приоритет',
          description_html: '<p>Приоритет</p>',
          type: 8,
          detail_answers: [
            { id: 129496, variant: 'вар 1', is_deleted: 0 },
            { id: 129497, variant: 'вар 2', is_deleted: 0 },
            { id: 129498, variant: 'вар 3', is_deleted: 0 },
          ],
          comment_enabled: 1,
          comment_label: 'aaaa',
          isRequired: 1,
        }),

        // Question ID 178085: Scale Question
        scaleOptions({
          id: 16,
          question_id: 178085,
          description: 'Шкала / Стандартная',
          description_html: '<p>Шкала / Стандартная</p>',
          type: 20,
          scaleRatingSetting: {
            start: 0,
            end: 100,
            step: 10,
          },
          isRequired: 0,
        }),

        // Question ID 178086: Scale Question (with variants)
        scaleOptions({
          id: 17,
          question_id: 178086,
          description: 'Шкала / Для вариантов',
          description_html: '<p>Шкала / Для вариантов</p>',
          type: 20,
          set_variants: 1,
          detail_answers: [
            { id: 129499, variant: 'вар 1', is_deleted: 0 },
            { id: 129500, variant: 'вар 2', is_deleted: 0 },
          ],
          scaleRatingSetting: {
            start: 0,
            end: 100,
            step: 10,
          },
          isRequired: 0,
        }),

        // Question ID 178087: Media Choice Question
        mediaVariantsOptions({
          id: 18,
          question_id: 178087,
          description: 'выбор изображения/видео',
          description_html: '<p>выбор изображения/видео</p>',
          type: 9,
          chooseType: 'image',
          gallery: [
            {
              id: 67794,
              src: '/uploads/foquz/178087/67ac96dbea62b.jpeg',
              poster: '/uploads/foquz/178087/67ac96dbea62b.jpeg',
              description: '',
            },
            {
              id: 67795,
              src: '/uploads/foquz/178087/67ac96e2099a7.jpeg',
              poster: '/uploads/foquz/178087/67ac96e2099a7.jpeg',
              description: '',
            },
          ],
          isRequired: 0,
        }),

        // Question ID 178088: Gallery Rating Question
        galleryOptions({
          id: 19,
          question_id: 178088,
          description: 'Рейтинг галереи',
          description_html: '<p>Рейтинг галереи</p>',
          type: 10,
          starRatingOptions: {
            count: 5,
            color: 'rgb(131, 39, 216)',
            size: 'md',
            labelsArray: ['', '', '', '', ''],
          },
          gallery: [
            {
              id: 67796,
              src: '/uploads/foquz/178088/67ac976d223ac.jpeg',
              poster: '/uploads/foquz/178088/67ac976d223ac.jpeg',
              description: '',
            },
            {
              id: 67797,
              src: '/uploads/foquz/178088/67ac97714884f.jpg',
              poster: '/uploads/foquz/178088/67ac97714884f.jpg',
              description: '',
            },
            {
              id: 67798,
              src: '/uploads/foquz/178088/67ac977560eb8.jpeg',
              poster: '/uploads/foquz/178088/67ac977560eb8.jpeg',
              description: '',
            },
          ],
          isRequired: 0,
        }),

        // Question ID 178089: Smile Rating Question
        smileRatingOptions({
          id: 20,
          question_id: 178089,
          description: 'смайл',
          description_html: '<p>смайл</p>',
          type: 11,
          smileType: 'heart',
          smiles: [
            { id: 41633, smile_url: '/img/smiles/heart/break.svg', label: '' },
            { id: 41634, smile_url: '/img/smiles/heart/full.svg', label: '' },
          ],
          isRequired: 0,
        }),

        // Question ID 178090: Matrix Question
        matrixQuestionOptions({
          id: 21,
          question_id: 178090,
          description: 'простая матрица',
          description_html: '<p>простая матрица</p>',
          type: 13,
          matrixSettings: {
            rows: ['вар 1', 'вар 2', 'вар 3'],
            cols: ['1', '2', '3', '4', '5'],
            minRowsReq: '3',
            multiple_choice: '0',
          },
          isRequired: 0,
        }),

        // Question ID 178091: Matrix Question (with dropdown)
        matrixQuestionOptions({
          id: 22,
          question_id: 178091,
          description: 'простая матрица / выпадающий список',
          description_html: '<p>простая матрица / выпадающий список</p>',
          type: 13,
          dropdownVariants: 1,
          matrixSettings: {
            rows: ['вар 1', 'вар 2', 'вар 3'],
            cols: ['1', '2', '3', '4', '5'],
            minRowsReq: '3',
            multiple_choice: '0',
          },
          isRequired: 0,
        }),

        // Question ID 178092: 3D Matrix Question
        {
          id: 23,
          question_id: 178092,
          description: '3Д матрица',
          description_html: '<p>3Д матрица</p>',
          type: 21,
          matrixElements: {
            rows: [
              { id: 60142, name: 'строка 1', description: '', position: 0 },
              { id: 60143, name: 'строка 2', description: '', position: 1 },
            ],
            columns: [
              {
                id: 60144,
                name: 'столбец 1',
                description: '',
                position: 0,
                variants: [
                  { id: 30857, name: 'отв 1', position: 0 },
                  { id: 30858, name: 'отв 2', position: 1 },
                ],
              },
              {
                id: 60145,
                name: 'столбец 2',
                description: '',
                position: 1,
                variants: [
                  { id: 30859, name: 'отв 1', position: 0 },
                  { id: 30860, name: 'отв 2', position: 1 },
                ],
              },
            ],
          },
          matrixSettings: { rowsAboveVariants: '0' },
          isRequired: 0,
        },

        // Question ID 178093: Star Rating for Variants
        starRatingVariantsOptions({
          id: 24,
          question_id: 178093,
          description: 'ЗР для вариантов',
          description_html: '<p>ЗР для вариантов</p>',
          type: 7,
          detail_answers: [
            { id: 129501, variant: 'вар 1', is_deleted: 0 },
            { id: 129502, variant: 'вар 2', is_deleted: 0 },
          ],
          starRatingOptions: {
            count: 5,
            color: 'rgb(131, 39, 216)',
            size: 'md',
            labelsArray: ['', '', '', '', ''],
          },
          isRequired: 0,
        }),

        // Question ID 178094: Semantic Differential Question
        diffQuestionOptions({
          id: 25,
          question_id: 178094,
          description: 'Сем диф',
          description_html: '<p>Сем диф</p>',
          type: 14,
          differentialRows: [
            { id: 12099, start_label: '', end_label: '', position: 1 },
          ],
          semDifSetting: {
            form: 'rect',
            start_point_color: '#73808D',
            end_point_color: '#3F65F1',
          },
          isRequired: 0,
        }),

        // Question ID 178095: Intermediate Block (screen_type: 1)
        intermediateBlockOptions({
          id: 26,
          question_id: 178095,
          type: 16,
          description: 'Промежуточный блок',
          description_html: '<p>Промежуточный блок</p>',
          intermediateBlock: {
            id: 20563,
            screen_type: 1,
            show_question_number: 0,
            text: '<p>Текст</p><p>{Промокод}</p><p>{Промокод с копированием}</p><p>{Выбор языка}</p>',
            pool_id: 367,
            agreement: 0,
          },
          endScreenImages: [
            {
              id: 2870,
              foquz_question_id: 178095,
              logo: '/uploads/custom-themes/ulibka-radugi/end-screen-img.svg',
              description: '',
              position: 1,
            },
          ],
          isRequired: 0,
        }),

        // Question ID 178096: Classifier Question
        classifierQuestionOptions({
          id: 27,
          question_id: 178096,
          description: 'Классификатор',
          description_html: '<p>Классификатор</p>',
          type: 19,
          dictionaryTree: {
            'Категория 1': {
              id: 161,
              title: 'Категория 1',
              description: 'Проверка ',
              position: 66,
              isCategory: true,
              children: {
                'Категория 11': {
                  id: 162,
                  title: 'Категория 11',
                  description: '',
                  position: 0,
                  isCategory: true,
                  children: {
                    '222': {
                      id: 1876,
                      title: '222',
                      description: 'Проверка',
                      position: 1,
                      isCategory: false,
                    },
                    'Элемент 1': {
                      id: 163,
                      title: 'Элемент 1',
                      description: 'Проверка 2',
                      position: 0,
                      isCategory: false,
                    },
                  },
                },
              },
            },
            'Категория 2': {
              id: 164,
              title: 'Категория 2',
              description: 'Проверка ',
              position: 67,
              isCategory: true,
              children: {
                'Категория 21': {
                  id: 165,
                  title: 'Категория 21',
                  description: '',
                  position: 0,
                  isCategory: true,
                  children: {
                    'Элемент 2': {
                      id: 166,
                      title: 'Элемент 2',
                      description: '',
                      position: 0,
                      isCategory: false,
                    },
                  },
                },
              },
            },
            'Элемент 3': {
              id: 167,
              title: 'Элемент 3',
              description: 'Проверка 444 4444 444 444',
              position: 181,
              isCategory: false,
            },
          },
          dictionary_id: 41,
          dictionary_list_type: 'tree',
          show_tooltips: 1,
          isRequired: 0,
        }),

        // Question ID 177653: Intermediate Block (screen_type: 3)
        intermediateBlockOptions({
          id: 4,
          question_id: 177653,
          type: 16,
          description: '',
          description_html: '',
          intermediateBlock: {
            id: 20513,
            screen_type: 3,
            show_question_number: 0,
            text: '<p><span style="font-size:34px;">Большое спасибо за уделенное время!</span></p><p> </p><p><span style="font-size:24px;">Мы ценим ваше доверие и стараемся становиться лучше для вас</span></p><p> </p><p><span style="font-size:16px;">А еще мы всегда рады услышать ваше мнение, поэтому если будет чем поделиться - напишите нам в Телеграм </span><a href="@help_ulybka"><span style="font-size:16px;">@help_ulybka</span></a><span style="font-size:16px;"> или позвоните в контактный центр по номеру 8 800 505 66 00.</span></p><p></p><p><span style="font-size:16px;">До встречи в магазинах Улыбка радуги!</span></p>',
            external_link: 'https://devfoquz.ru/p/F679232204ed58',
            logos_backcolor: 'transparent',
            agreement: 0,
          },
          endScreenImages: [
            {
              id: 2865,
              foquz_question_id: 177653,
              logo: '/uploads/custom-themes/ulibka-radugi/end-screen-img.svg',
              description: '',
              position: 1,
            },
          ],
          isRequired: 0,
        }),
      ],
    }),
}
