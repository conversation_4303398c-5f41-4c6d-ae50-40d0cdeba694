import { QUESTION_TYPES } from "../../src/helpers";
import {
  ASSESMENT_VARIANT_TYPES,
  INTERMEDIATE_BLOCK_TYPES,
  MASK_TYPES,
  RATING_NPS_DESIGN,
  TEXT_VARIANT_TYPES,
  VARIANT_TYPES,
} from "../../src/helpers/question-helpers/questionTypes";
import { mockLangMessages } from "../mock-utils";
import defaultQuestionsData from "./questions/defaultQuestionData";
import { ratingOptions } from "./questions/ratingData";

const getQuestionsList = (additionalQuestions = []) => [
  {
    id: 0,
    question_id: 141449,
    type: QUESTION_TYPES.INTERMEDIATE_BLOCK,
    intermediateBlock: {
      screen_type: INTERMEDIATE_BLOCK_TYPES.START,
      text: '<p style="text-align:center;"><span style="font-size:24px;"><strong>Стартовый экран</strong></span></p><br/><p>Мы ценим ваше мнение и хотели бы узнать о вашем опыте. Пожалуйста, уделите несколько минут, чтобы ответить на следующие вопросы.</p><p>Ваши ответы помогут нам улучшить наши услуги.</p><p>Спасибо за участие!</p>',
      agreement: 1,
      langs: [
        {
          id: 11152,
          foquz_question_id: 141489,
          lang_id: 13688,
          text: '<p style="text-align:center;"><span style="font-size:24px;"><strong>Start screen</strong></span></p><br/><p>We value your opinion and would like to learn about your experience. Please take a few minutes to answer the following questions.</p><p>Your responses will help us improve our services.</p><p>Thank you for participating!</p>',
          agreement_text: "I agree to participate in this survey",
          poll_button_text: "Start Survey",
        },
        {
          id: 11233,
          foquz_question_id: 141489,
          lang_id: 13687,
        },
      ],
    },
    endScreenImages: [],
  },
  {
    comment_enabled: 0,
    comment_required: 0,
    type: QUESTION_TYPES.STAR_RATING,
    id: 1,
    isRequired: 1,
    isHaveCustomField: 1,
    isHaveExtra: true,
    show_labels: 1,
    assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
    extra_required: 1,
    starRatingOptions: {
      extra_question_rate_from: null,
      extra_question_rate_to: null,
    },
    textFieldParam: {
      min: 10,
      max: 50,
    },
    langs: [
      {
        id: 11149,
        foquz_question_id: 141487,
        foquz_poll_lang_id: 13688,
        description: "Star rating. (English)",
        description_html: "<p>Star rating. (English)</p>",
        sub_description: "subdescription (english)",
        placeholder_text: "Placeholder (English)",
        self_variant_placeholder_text: "Self Variant (english)",
        comment_label: "Comment text",
        skip_text: "Skip (English)",
        self_variant_text: "Self variant",
        labels: '["One","Two","Three","Four","Five"]',
      },
      {
        id: 11230,
        foquz_question_id: 141487,
        foquz_poll_lang_id: 13687,
      },
    ],
    detail_answers: [
      {
        id: 101758,
        variant: "Раз",
        detailLangs: [
          {
            foquz_poll_lang_id: 13688,
            question: "One",
          },
        ],
      },
      {
        id: 101759,
        variant: "Два",
        detailLangs: [
          {
            foquz_poll_lang_id: 13688,
            question: "Two",
          },
        ],
      },
      {
        id: 101760,
        show_labels: 1,
        variant: "Три",
        detailLangs: [
          {
            foquz_poll_lang_id: 13688,
            question: "Three",
          },
        ],
      },
    ],
  },
  {
    description: "Оцените наш сервис",
    description_html: "<p>Оцените наш сервис</p>",
    id: 2,
    type: QUESTION_TYPES.RATING,
    starRatingOptions: ratingOptions({
      labelsArray: ["Очень плохо", "Плохо", "Средне", "Хорошо", "Отлично"],
    }),
    comment_enabled: 1,
    comment_required: 1,
    skip: 1,
    skip_text: "Пропустить вопрос",
    isRequired: 1,
    textFieldParam: {
      min: 10,
      max: 50,
    },
    langs: [
      {
        id: 11149,
        foquz_question_id: 141487,
        foquz_poll_lang_id: 13688,
        description: "Rate our service",
        description_html: "<p>Rate our service</p>",
        sub_description: "Your feedback is important to us",
        placeholder_text: "Enter your comment here",
        comment_label: "Additional comments",
        skip_text: "Skip this question",
        labels: '["Very poor","Poor","Average","Good","Excellent"]',
      },
      {
        id: 11230,
        foquz_question_id: 141487,
        foquz_poll_lang_id: 13687,
      },
    ],
  },
  {
    description: "Насколько вероятно, что вы порекомендуете нас друзьям?",
    description_html:
      "<p>Насколько вероятно, что вы порекомендуете нас друзьям?</p>",
    type: QUESTION_TYPES.RATING_NPS,
    fromOne: 0,
    set_variants: 0,
    id: 3,
    question_id: 128281,
    npsRatingSetting: {
      id: 4216,
      foquz_question_id: 128281,
      design: RATING_NPS_DESIGN.COLORED,
      start_point_color: "#f96261",
      end_point_color: "#00c968",
      start_label: "Маловероятно",
      end_label: "Очень вероятно",
    },
    comment_enabled: 1,
    comment_required: 0,
    skip: 1,
    skip_text: "Пропустить вопрос",
    isRequired: 1,
    textFieldParam: {
      min: 10,
      max: 50,
    },
    langs: [
      {
        id: 11150,
        foquz_question_id: 128281,
        foquz_poll_lang_id: 13688,
        description: "How likely are you to recommend us to your friends?",
        description_html:
          "<p>How likely are you to recommend us to your friends?</p>",
        sub_description: "Your opinion matters to us",
        placeholder_text: "Tell us why you chose this rating",
        comment_label: "Your feedback",
        skip_text: "Skip this question",
        labels: '["Not likely","Very likely"]',
      },
      {
        id: 11231,
        foquz_question_id: 128281,
        foquz_poll_lang_id: 13687,
      },
    ],
  },
  {
    description: "Оцените ваше общее впечатление",
    description_html: "<p>Оцените ваше общее впечатление</p>",
    type: QUESTION_TYPES.SMILE_RATING,
    id: 4,
    starRatingOptions: {
      count: 5,
    },
    smileType: "face",
    show_labels: 1,
    skip: 1,
    skip_text: "Пропустить вопрос",
    comment_enabled: 1,
    comment_required: 0,
    isRequired: 1,
    textFieldParam: {
      min: 10,
      max: 50,
    },
    smiles: [
      {
        id: 1,
        label: "Ужасно",
        url: "/uploads/smiles/face/1.svg",
        smile_url: "/uploads/smiles/face/1.svg",
      },
      {
        id: 2,
        label: "Плохо",
        url: "/uploads/smiles/face/2.svg",
        smile_url: "/uploads/smiles/face/2.svg",
      },
      {
        id: 3,
        label: "Нормально",
        url: "/uploads/smiles/face/3.svg",
        smile_url: "/uploads/smiles/face/3.svg",
      },
      {
        id: 4,
        label: "Хорошо",
        url: "/uploads/smiles/face/4.svg",
        smile_url: "/uploads/smiles/face/4.svg",
      },
      {
        id: 5,
        label: "Отлично",
        url: "/uploads/smiles/face/5.svg",
        smile_url: "/uploads/smiles/face/5.svg",
      },
    ],
    langs: [
      {
        id: 11151,
        foquz_question_id: 141488,
        foquz_poll_lang_id: 13688,
        description: "Rate your overall experience",
        description_html: "<p>Rate your overall experience</p>",
        sub_description: "We value your feedback",
        placeholder_text: "Tell us more about your experience",
        comment_label: "Additional comments",
        skip_text: "Skip this question",
        labels:
          '{"1":"Very bad","2":"Bad","3":"Average","4":"Good","5":"Excellent"}',
      },
      {
        id: 11232,
        foquz_question_id: 141488,
        foquz_poll_lang_id: 13687,
      },
    ],
  },
  {
    id: 5,
    question_id: 141489,
    type: QUESTION_TYPES.INTERMEDIATE_BLOCK,
    intermediateBlock: {
      screen_type: INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE,
      text: '<p style="text-align:center;"><span style="font-size:24px;"><strong>Добро пожаловать в наш опрос!</strong></span></p><br/><p>Мы ценим ваше мнение и хотели бы узнать о вашем опыте. Пожалуйста, уделите несколько минут, чтобы ответить на следующие вопросы.</p><p>Ваши ответы помогут нам улучшить наши услуги.</p><p>Спасибо за участие!</p>',
      agreement: 1,
      langs: [
        {
          id: 11152,
          foquz_question_id: 141489,
          lang_id: 13688,
          text: '<p style="text-align:center;"><span style="font-size:24px;"><strong>Welcome to our survey!</strong></span></p><br/><p>We value your opinion and would like to learn about your experience. Please take a few minutes to answer the following questions.</p><p>Your responses will help us improve our services.</p><p>Thank you for participating!</p>',
          agreement_text: "I agree to participate in this survey",
          poll_button_text: "Start Survey",
        },
        {
          id: 11233,
          foquz_question_id: 141489,
          lang_id: 13687,
        },
      ],
    },
    endScreenImages: [],
    langs: [
      {
        id: 11152,
        foquz_question_id: 141489,
        foquz_poll_lang_id: 13688,
        text: '<p style="text-align:center;"><span style="font-size:24px;"><strong>Welcome to our survey!</strong></span></p><br/><p>We value your opinion and would like to learn about your experience. Please take a few minutes to answer the following questions.</p><p>Your responses will help us improve our services.</p><p>Thank you for participating!</p>',
        agreement_text: "I agree to participate in this survey",
        poll_button_text: "Start Survey",
      },
      {
        id: 11233,
        foquz_question_id: 141489,
        foquz_poll_lang_id: 13687,
      },
    ],
  },
  {
    description: "Укажите ваш номер телефона",
    description_html: "<p>Укажите ваш номер телефона</p>",
    id: 6,
    type: QUESTION_TYPES.TEXT,
    variantsType: TEXT_VARIANT_TYPES.INPUT,
    isRequired: 1,
    textFieldParam: {
      min: 10,
      max: 20,
    },
    placeholderText: "Введите номер телефона",
    maskType: MASK_TYPES.PHONE,
    langs: [
      {
        id: 11153,
        foquz_question_id: 141490,
        foquz_poll_lang_id: 13688,
        description: "Please provide your phone number",
        description_html: "<p>Please provide your phone number</p>",
        sub_description: "We may use this to contact you about your feedback",
        placeholder_text: "Enter your phone number",
      },
      {
        id: 11234,
        foquz_question_id: 141490,
        foquz_poll_lang_id: 13687,
      },
    ],
  },
  {
    description: "Введите ваше ФИО",
    id: 7,
    description_html: "<p>Введите ваше ФИО</p>",
    type: QUESTION_TYPES.TEXT,
    variantsType: TEXT_VARIANT_TYPES.INPUT,
    isRequired: 1,
    maskType: MASK_TYPES.FIO,
    maskConfig: {
      name: {
        visible: true,
        required: true,
        placeholderText: "Имя",
        minLength: 2,
        maxLength: 50,
        value: "",
      },
      surname: {
        visible: true,
        required: true,
        placeholderText: "Фамилия",
        minLength: 2,
        maxLength: 50,
        value: "",
      },
      patronymic: {
        visible: true,
        required: false,
        placeholderText: "Отчество",
        minLength: 0,
        maxLength: 50,
        value: "",
      },
    },
    langs: [
      {
        id: 11154,
        foquz_question_id: 141491,
        foquz_poll_lang_id: 13688,
        description: "Enter your full name",
        description_html: "<p>Enter your full name</p>",
        sub_description:
          "Please provide your first name, last name, and patronymic (if applicable)",
        labels:
          '{"name":{"name":"Name","placeholder":"Name placeholder (English)"},"surname":{"name":"Surname","placeholder":"Surname placeholder (English)"},"patronymic":{"name":"Patronymic","placeholder":"Patronymic placeholder (English)"}}',
      },
      {
        id: 11235,
        foquz_question_id: 141491,
        foquz_poll_lang_id: 13687,
      },
    ],
  },
  {
    id: 8,
    description: "Выберите один или несколько вариантов",
    description_html: "<p>Выберите один или несколько вариантов</p>",
    type: QUESTION_TYPES.VARIANTS,
    variantsType: VARIANT_TYPES.MULTIPLE,
    isRequired: 1,
    isHaveCustomField: 1,
    self_variant_text: "Свой вариант",
    selfVariantPlaceholderText: "Введите свой вариант",
    variants: [
      { id: 1, value: "Раз" },
      { id: 2, value: "Два" },
      { id: 3, value: "Три" },
    ],
    detail_answers: [
      {
        id: 1,
        variant: "Раз",
        detailLangs: [
          {
            foquz_poll_lang_id: 13688,
            question: "One",
          },
        ],
      },
      {
        id: 2,
        variant: "Два",
        detailLangs: [
          {
            foquz_poll_lang_id: 13688,
            question: "Two",
          },
        ],
      },
      {
        id: 3,
        variant: "Три",
        detailLangs: [
          {
            foquz_poll_lang_id: 13688,
            question: "Three",
          },
        ],
      },
    ],
    textFieldParam: {
      min: 0,
      max: 250,
    },
    selfVariantParam: {
      min: 0,
      max: 50,
    },
    langs: [
      {
        id: 11155,
        foquz_question_id: 141492,
        foquz_poll_lang_id: 13688,
        description: "Select one or more options",
        description_html: "<p>Select one or more options</p>",
        sub_description: "You can also add your own option if needed",
        self_variant_text: "Custom option",
        selfVariantPlaceholderText: "Enter your custom option",
        self_variant_placeholder_text: "Enter your custom option",
      },
      {
        id: 11236,
        foquz_question_id: 141492,
        foquz_poll_lang_id: 13687,
      },
    ],
  },
  ...additionalQuestions,
];

export const localizationMockData = {
  "localization-english": () =>
    defaultQuestionsData({
      lang: {
        id: 13688,
        foquz_poll_id: 43680,
        poll_lang_id: 2,
        checked: 1,
        default: 0,
        back_text: "Back",
        next_text: "Next",
        finish_text: "Success!",
        unrequired_text: "Optional",
        messages: mockLangMessages,
        questions: {
          141487: {
            id: 11149,
            foquz_question_id: 141487,
            foquz_poll_lang_id: 13688,
            name: "",
            description: "Star rating. (English)",
            description_html: "<p>Star rating. (English)</p>",
            sub_description: "subdescription (english)",
            placeholder_text: "Placeholder (English)",
            select_placeholder_text: null,
            self_variant_placeholder_text: "Self Variant (english)",
            self_variant_description: null,
            comment_label: "Comment text",
            skip_text: "Skip (English)",
            detail_question: "",
            self_variant_text: "Self variant",
            labels: '["One","Two","Three","Four","Five"]',
            text: null,
            card_column_text: null,
            category_column_text: null,
            detailLangs: {
              101758: {
                id: 1,
                foquz_question_id: 141487,
                foquz_question_detail_id: 101758,
                foquz_poll_lang_id: 13688,
                question: "One",
                description: null,
                detail_id: null,
                detail_type: null,
                detail_question: null,
                self_variant_text: null,
                self_variant_placeholder_text: null,
                langId: 2,
              },
              101759: {
                id: 2,
                foquz_question_id: 141487,
                foquz_question_detail_id: 101759,
                foquz_poll_lang_id: 13688,
                question: "Two",
                description: null,
                detail_id: null,
                detail_type: null,
                detail_question: null,
                self_variant_text: null,
                self_variant_placeholder_text: null,
                langId: 2,
              },
              101760: {
                id: 3,
                foquz_question_id: 141487,
                foquz_question_detail_id: 101760,
                foquz_poll_lang_id: 13688,
                question: "Three",
                description: null,
                detail_id: null,
                detail_type: null,
                detail_question: null,
                self_variant_text: null,
                self_variant_placeholder_text: null,
                langId: 2,
              },
            },
          },
          141488: {
            id: 11151,
            foquz_question_id: 141488,
            foquz_poll_lang_id: 13688,
            name: "",
            description: "Rate your overall experience",
            description_html: "<p>Rate your overall experience</p>",
            sub_description: "We value your feedback",
            placeholder_text: "Tell us more about your experience",
            comment_label: "Additional comments",
            skip_text: "Skip this question",
            labels:
              '{"1":"Very bad","2":"Bad","3":"Average","4":"Good","5":"Excellent"}',
            detailLangs: {},
          },
          128281: {
            id: 11150,
            foquz_question_id: 128281,
            foquz_poll_lang_id: 13688,
            name: "",
            description: "How likely are you to recommend us to your friends?",
            description_html:
              "<p>How likely are you to recommend us to your friends?</p>",
            sub_description: "Your opinion matters to us",
            placeholder_text: "Tell us why you chose this rating",
            comment_label: "Your feedback",
            skip_text: "Skip this question",
            labels: '["Not likely","Very likely"]',
            detailLangs: {},
          },
          141489: {
            id: 11152,
            foquz_question_id: 141489,
            foquz_poll_lang_id: 13688,
            name: "",
            description: "",
            description_html: "",
            sub_description: "",
            text: '<p style="text-align:center;"><span style="font-size:24px;"><strong>Welcome to our survey!</strong></span></p><br/><p>We value your opinion and would like to learn about your experience. Please take a few minutes to answer the following questions.</p><p>Your responses will help us improve our services.</p><p>Thank you for participating!</p>',
            agreement_text: "I agree to participate in this survey",
            poll_button_text: "Start Survey",
            detailLangs: {},
          },
          141490: {
            id: 11153,
            foquz_question_id: 141490,
            foquz_poll_lang_id: 13688,
            name: "",
            description: "Please provide your phone number",
            description_html: "<p>Please provide your phone number</p>",
            sub_description:
              "We may use this to contact you about your feedback",
            placeholder_text: "Enter your phone number",
            detailLangs: {},
          },
          141491: {
            id: 11154,
            foquz_question_id: 141491,
            foquz_poll_lang_id: 13688,
            name: "",
            description: "Enter your full name",
            description_html: "<p>Enter your full name</p>",
            sub_description:
              "Please provide your first name, last name, and patronymic (if applicable)",
            labels:
              '{"name":{"name":"Name","placeholder":"Name placeholder (English)"},"surname":{"name":"Surname","placeholder":"Surname placeholder (English)"},"patronymic":{"name":"Patronymic","placeholder":"Patronymic placeholder (English)"}}',
            detailLangs: {},
          },
          141492: {
            id: 11155,
            foquz_question_id: 141492,
            foquz_poll_lang_id: 13688,
            name: "",
            description: "Select one or more options",
            description_html: "<p>Select one or more options</p>",
            sub_description: "You can also add your own option if needed",
            self_variant_text: "Custom option",
            self_variant_placeholder_text: "Enter your custom option",
            detailLangs: {
              1: {
                id: 1,
                foquz_question_id: 141492,
                foquz_question_detail_id: 1,
                foquz_poll_lang_id: 13688,
                question: "One",
                description: null,
                detail_id: null,
                detail_type: null,
                detail_question: null,
                self_variant_text: null,
                self_variant_placeholder_text: null,
                langId: 2,
              },
              2: {
                id: 2,
                foquz_question_id: 141492,
                foquz_question_detail_id: 2,
                foquz_poll_lang_id: 13688,
                question: "Two",
                description: null,
                detail_id: null,
                detail_type: null,
                detail_question: null,
                self_variant_text: null,
                self_variant_placeholder_text: null,
                langId: 2,
              },
              3: {
                id: 3,
                foquz_question_id: 141492,
                foquz_question_detail_id: 3,
                foquz_poll_lang_id: 13688,
                question: "Three",
                description: null,
                detail_id: null,
                detail_type: null,
                detail_question: null,
                self_variant_text: null,
                self_variant_placeholder_text: null,
                langId: 2,
              },
            },
          },
        },
      },
      answer: {
        custom_fields: '{"lang": "en"}',
      },
      questions: getQuestionsList(),
      variables: {
        fio: "Иван Иванов",
        codes: {
          123: "PROMO123",
        },
      },
    }),
};

export default localizationMockData;
