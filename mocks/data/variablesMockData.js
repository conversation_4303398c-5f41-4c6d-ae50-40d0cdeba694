import defaultQuestionsData from './defaultQuestionsData'
// Import additional mock data functions at the top
import {
  DIFF_QUESTION,
  MATRIX_QUESTION,
  MEDIA_VARIANTS_QUESTION,
  RATING_QUESTION,
  STARS_QUESTION,
  TEXT_QUESTION,
  TEXT_VARIANT_TYPES,
  VARIANTS_QUESTION,
} from '@entities/question/model/types'

import MaskTypes from '@shared/constants/maskTypes'
import { classifierQuestionOptions } from './questions/classifierMockData'
import { galleryOptions } from './questions/galleryMockData'
import { intermediateBlockOptions } from './questions/interBlockMockData'
import { getQuizMockData } from './questions/quizMockData'
import { ratingNpsOptions } from './questions/ratingNpsData'
import { scaleOptions } from './questions/scaleMockData'
import { smileRatingOptions } from './questions/smileRatingMockData'

const variablesMockData = {
  variables: () => defaultQuestionsData({
    questions: [
      // NPS Rating
      ratingNpsOptions({
        id: 1,
        question_id: 101,
      }),
      // Star rating with answer reference
      {
        type: STARS_QUESTION,
        id: 2,
        question_id: 102,
        description: 'Ответ на первый вопрос: {ANSWER.1}',
        description_html: '<p>Ответ на первый вопрос: {ANSWER.1}</p>',
        subdescription: 'URL param test: {URL.test}',
        detail_answers: [],
        starRatingOptions: {
          count: 5,
        },
        textFieldParam: {
          min: 0,
          max: 50,
        },
      },
      // Variants with answer reference
      {
        type: STARS_QUESTION,
        id: 3,
        question_id: 103,
        description: 'Почему {ANSWER.2}?',
        description_html: '<p>Почему {ANSWER.2}?</p>',
        isHaveCustomField: 1,
        variantsType: 1, // multiple choice
        self_variant_text: 'Свой вариант',
        placeholderText: 'Введите свой вариант',
        textFieldParam: {
          min: 0,
          max: 50,
        },
        detail_answers: [
          { id: 1, variant: 'Вариант 1', is_deleted: 0 },
          { id: 2, variant: 'Вариант 2', is_deleted: 0 },
          { id: 3, variant: 'Вариант 3', is_deleted: 0 },
        ],
      },
      // Text with answer reference
      {
        type: STARS_QUESTION,
        id: 4,
        question_id: 104,
        description: 'Ответ на третий вопрос: {ANSWER.3}',
        description_html: '<p>Ответ на третий вопрос: {ANSWER.3}</p>',
        variantsType: 0,
        detail_answers: [],
        textFieldParam: {
          min: 0,
          max: 50,
        },
      },
      {
        // Rating scale
        type: RATING_QUESTION,
        id: 6,
        question_id: 106,
        description: 'Почему вы поставили {ANSWER.4}?',
        description_html: '<p>Почему вы поставили {ANSWER.4}?</p>',
        detail_answers: [],
        starRatingOptions: {
          count: 5,
          color: '#000',
        },
      },

      // Smile Rating
      smileRatingOptions({
        id: 7,
        question_id: 107,
        description: 'Насколько вы довольны, учитывая что {ANSWER.5}?',
        description_html: '<p>Насколько вы довольны, учитывая что {ANSWER.5}?</p>',
        smileType: 'face',
        count: 5,
      }),

      // Scale with variants
      scaleOptions({
        id: 8,
        question_id: 108,
        description: 'Оцените ваш предыдущий ответ {ANSWER.6}',
        description_html: '<p>Оцените ваш предыдущий ответ {ANSWER.6}</p>',
        set_variants: 1,
      }),

      // NPS with variants
      ratingNpsOptions({
        id: 9,
        question_id: 109,
        description: 'Учитывая ваш ответ {ANSWER.7}, насколько вы готовы рекомендовать?',
        description_html: '<p>Учитывая ваш ответ {ANSWER.7}, насколько вы готовы рекомендовать?</p>',
        set_variants: 1,
        detail_answers: [
          { id: 1, variant: 'Качество услуг', is_deleted: 0 },
          { id: 2, variant: 'Скорость обслуживания', is_deleted: 0 },
          { id: 3, variant: 'Цена', is_deleted: 0 },
        ],
      }),

      // Variants multiple choice with self variant
      {
        type: VARIANTS_QUESTION,
        id: 10,
        question_id: 110,
        description: 'Почему вы поставили {ANSWER.8}?',
        description_html: '<p>Почему вы поставили {ANSWER.8}?</p>',
        variantsType: 1,
        isHaveCustomField: 1,
        self_variant_text: 'Другое',
        detail_answers: [
          { id: 1, variant: 'Вариант 1', is_deleted: 0 },
          { id: 2, variant: 'Вариант 2', is_deleted: 0 },
        ],
      },

      // Media variants
      {
        type: MEDIA_VARIANTS_QUESTION,
        id: 11,
        question_id: 111,
        description: 'Выберите подходящее изображение к ответу {ANSWER.9}',
        description_html: '<p>Выберите подходящее изображение к ответу {ANSWER.9}</p>',
        variantsType: 1,
        gallery: [
          {
            id: 1,
            src: '/uploads/image1.jpg',
            url: '/uploads/image1.jpg',
            description: 'Изображение 1',
          },
          {
            id: 2,
            src: '/uploads/image2.jpg',
            url: '/uploads/image2.jpg',
            description: 'Изображение 2',
          },
        ],
      },

      // Text question
      {
        type: TEXT_QUESTION,
        id: 12,
        question_id: 112,
        description: 'Опишите подробнее ваш ответ {ANSWER.10}',
        description_html: '<p>Опишите подробнее ваш ответ {ANSWER.10}</p>',
        maskType: MaskTypes.NoMask,
        variantsType: TEXT_VARIANT_TYPES.TEXTAREA,
        placeholderText: 'Введите текст...',
      },

      // Quiz question
      getQuizMockData({
        id: 13,
        question_id: 113,
        description: 'Тестовый вопрос про {ANSWER.11}',
        description_html: '<p>Тестовый вопрос про {ANSWER.11}</p>',
      }),

      // Gallery
      galleryOptions({
        id: 14,
        question_id: 114,
        description: 'Выберите изображение, которое соответствует {ANSWER.12}',
        description_html: '<p>Выберите изображение, которое соответствует {ANSWER.12}</p>',
      }),

      // Matrix
      {
        type: MATRIX_QUESTION,
        id: 15,
        question_id: 115,
        description: 'Оцените следующие параметры с учетом {ANSWER.13}',
        description_html: '<p>Оцените следующие параметры с учетом {ANSWER.13}</p>',
        isRequired: 1,
        matrixSettings: {
          rows: ['Параметр 1', 'Параметр 2', 'Параметр 3'],
          cols: ['Плохо', 'Нормально', 'Хорошо'],
          multiple_choice: '0',
          type: 'standart',
        },
        detail_answers: [],
      },

      // Diff question
      {
        type: DIFF_QUESTION,
        id: 16,
        question_id: 116,
        description: 'Сравните варианты, учитывая {ANSWER.14}',
        description_html: '<p>Сравните варианты, учитывая {ANSWER.14}</p>',
        isRequired: 1,
        semDifSetting: {
          form: 'rect',
          start_point_color: '#73808D',
          end_point_color: '#3F65F1',
        },
        differentialRows: [
          {
            id: 1,
            start_label: 'Плохо',
            end_label: 'Отлично',
          },
          {
            id: 2,
            start_label: 'Медленно',
            end_label: 'Быстро',
          },
        ],
        comment_enabled: 0,
        comment_required: 0,
        comment_label: 'Ваш комментарий',
        placeholderText: 'Введите ваш комментарий здесь',
      },

      // Classifier
      classifierQuestionOptions({
        id: 19,
        question_id: 119,
        description_html: '<p>Классифицируйте ответ {ANSWER.15}</p>',
      }),
      // Intermediate block
      intermediateBlockOptions({
        id: 17,
        question_id: 117,
        intermediateBlock: {
          text: `
           <p>Промежуточный результат</p>
           <p>&nbsp;</p>
           <p>Ответ на первый вопрос {ANSWER.1}</p>
           <p>Ответ на второй вопрос {ANSWER.2}</p>
           <p>Ответ на третий вопрос {ANSWER.3}</p>
           <p>Ответ на четвертый вопрос {ANSWER.4}</p>
           <p>Ответ на пятый вопрос {ANSWER.5}</p>
           <p>Ответ на шестой вопрос {ANSWER.6}</p>
           <p>Ответ на седьмой вопрос {ANSWER.7}</p>
           <p>Ответ на восьмой вопрос {ANSWER.8}</p>
           <p>Ответ на девятый вопрос {ANSWER.9}</p>
           <p>Ответ на десятый вопрос {ANSWER.10}</p>
           <p>Ответ на одиннадцатый вопрос {ANSWER.11}</p>
           <p>Ответ на двенадцатый вопрос {ANSWER.12}</p>
           <p>Ответ на тринадцатый вопрос {ANSWER.13}</p>
           <p>Ответ на четырнадцатый вопрос {ANSWER.14}</p>
           <p>Ответ на пятнадцатый вопрос {ANSWER.15}</p>
           <p>&nbsp;</p>
           <p>Значение параметра test в url = {URL.test}</p>
           <p>Значение параметра test2 в url = {URL.test2}</p>
           <p>&nbsp;</p>
           <p>Характеристика 1 филиала = {FILIAL.param1}</p>
           <p>Характеристика 2 филиала = {FILIAL.param2}</p>
           <p>Характеристика 3 филиала = {FILIAL.param3}</p>
         `,
        },
      }),
    ],
    variables: {
      'fio': '',
      'order': null,
      'codes': {
        123: 'PROMO-123',
      },
      'scoresInterpretationRanges': [],
      'FILIAL.param1': 'https://wavehouse.ru/wp-content/uploads/2017/04/mini.jpeg',
      'FILIAL.param2': 'https://rg.ru/uploads/images/192/68/94/987654.jpg',
      'FILIAL.param3': 'https://naked-science.ru/wp-content/uploads/2017/08/field_image_1_624.jpg',
    },
  }),
}
export default variablesMockData
