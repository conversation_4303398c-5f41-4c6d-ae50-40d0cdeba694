import { PRIORITY_QUESTION } from '@entities/question/model/types'
import { merge } from 'lodash'
import defaultQuestionsData from '../defaultQuestionsData'

export function priorityQuestionOptions(options = {}) {
  return merge(
    {
      isRequired: 1,
      reorder_required: 0,
      type: PRIORITY_QUESTION,
      description: 'Расставьте приоритеты',
      comment_enabled: 1,
      comment_required: 0,
      comment_label: 'Ваш комментарий',
      placeholderText: 'Введите комментарий',
      textFieldParam: {
        min: 0,
        max: 1000,
      },
      detail_answers: [
        {
          id: 1,
          variant: 'Вариант 1',
          position: 0,
        },
        {
          id: 2,
          variant: 'Вариант 2',
          position: 1,
        },
        {
          id: 3,
          variant: 'Вариант 3',
          position: 2,
        },
        {
          id: 4,
          variant: 'Вариант 4',
          position: 3,
        },
      ],
    },
    options,
  )
}

/**
 * Моковые данные для вопроса "Приоритет"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {object}
 */
const priorityMockData = {
  'priority-question': () => defaultQuestionsData({
    questions: [
      priorityQuestionOptions(),
    ],
  }),
  'priority-question-reorder-required': () => defaultQuestionsData({
    questions: [
      priorityQuestionOptions({
        reorder_required: 1,
      }),
    ],
  }),
}

export default priorityMockData
