import { ASSESMENT_VARIANT_TYPES, RATING_QUESTION, STARS_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function ratingScaleOptions(data = {}) {
  return merge(
    {
      count: 5,
      color: 'rgb(63, 101, 241)',
    },
    data,
  )
}

/**
 * Моковые данные для вопроса "Рейтинг"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {object}
 */
const ratingScaleMockData = {
  'rating': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'rating-2-items': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions({ count: 2 }),
        },
      ],
    }),
  'rating-10-items': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions({ count: 10 }),
        },
      ],
    }),
  'rating-with-labels': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions({
            labelsArray: [
              'метка 1',
              'метка 2',
              'метка 3',
              'метка 4',
              'метка 5',
            ],
          }),
        },
      ],
    }),
  'rating-with-skip': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
          skip: 1,
          skip_text: 'Пропустить вопрос',
        },
      ],
    }),
  'rating-unrequired': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
          isRequired: 0,
        },
      ],
    }),
  'rating-without-prev-button-2-questions': () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
      questions: [
        {
          id: 1,
          question_id: 192,
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          detail_answers: [],
          starRatingOptions: ratingScaleOptions(),
        },
        {
          id: 2,
          question_id: 284893,
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: STARS_QUESTION,
          detail_answers: [],
          comment_enabled: 1,
          starRatingOptions: {
            count: 5,
          },
        },
      ],
    }),
  'rating-with-skip-required': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
          isRequired: 1,
          skip: 1,
          skip_text: 'Пропустить вопрос',
        },
      ],
    }),
  'rating-with-skip-and-comment': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
          comment_enabled: 1,
          skip: 1,
          skip_text: 'Пропустить вопрос',
        },
      ],
    }),
  'rating-with-comment': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
          comment_enabled: 1,
        },
      ],
    }),
  'rating-with-comment-required': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
          comment_enabled: 1,
          comment_required: 1,
        },
      ],
    }),
  'rating-with-comment-complex': () =>
    defaultQuestionsData({
      questions: [
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
          comment_enabled: 1,
          comment_required: 1,
          textFieldParam: {
            min: 10,
            max: 50,
          },
        },
      ],
    }),
  'rating-with-assessments-text': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          variantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'rating-with-assessments-text-complex': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          variantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 1,
          placeholderText: 'Тут подсказка',
          textFieldParam: {
            min: 10,
            max: 50,
          },
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions({
            extra_question_rate_from: 1,
            extra_question_rate_to: 3,
          }),
        },
      ],
    }),
  'rating-with-assessments-single': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          variantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'rating-with-assessments-single-required': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          variantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'rating-with-assessments-multiple': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'rating-with-assessments-multiple-required': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
}

export default ratingScaleMockData
