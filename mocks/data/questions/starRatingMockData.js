import { ASSESMENT_VARIANT_TYPES, RATING_QUESTION, STARS_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function starRatingQuestionOptions(options) {
  return merge({
    type: STARS_QUESTION,
    textFieldParam: {
      min: 0,
      max: 250,
    },
    starRatingOptions: {
      count: 5,
    },
  }, options)
}

/**
 * Моковые данные для вопроса "Звездный рейтинг"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {object}
 */
const starRatingMockData = {
  'star-rating': () => defaultQuestionsData(),
  'star-rating-2-stars': () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            count: 2,
          },
        },
      ],
    }),
  'star-rating-2-stars-big': () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            count: 2,
            size: 'lg',
          },
        },
      ],
    }),
  'star-rating-2-stars-big-always-show-labels': () =>
    defaultQuestionsData({
      questions: [
        {
          show_labels: 1,
          starRatingOptions: {
            count: 2,
            size: 'lg',
            labelsArray: ['метка 1', 'Номально — длинный вариант'],
          },
        },
      ],
    }),
  'star-rating-3-stars-big-always-show-labels': () =>
    defaultQuestionsData({
      questions: [
        {
          show_labels: 1,
          starRatingOptions: {
            count: 3,
            size: 'lg',
            labelsArray: ['метка 1', 'Номально — длинный вариант', 'метка 3'],
          },
        },
      ],
    }),
  'star-rating-10-stars': () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            count: 10,
          },
        },
      ],
    }),
  'star-rating-10-stars-with-numbers': () =>
    defaultQuestionsData({
      questions: [
        {
          show_numbers: 1,
          starRatingOptions: {
            count: 10,
          },
        },
      ],
    }),
  'star-rating-unrequired': () =>
    defaultQuestionsData({
      questions: [
        {
          isRequired: 0,
        },
      ],
    }),
  'star-rating-with-labels': () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            labelsArray: [
              'метка 1',
              'метка 2',
              'метка 3',
              'метка 4',
              'метка 5',
            ],
          },
        },
      ],
    }),
  'star-rating-always-show-labels': () =>
    defaultQuestionsData({
      questions: [
        {
          show_labels: 1,
          starRatingOptions: {
            labelsArray: [
              'метка 1',
              'метка 2',
              'метка 3',
              'метка 4',
              'метка 5',
            ],
          },
        },
      ],
    }),
  'star-rating-without-prev-button': () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
    }),
  'star-rating-without-prev-button-2-questions': () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
      questions: [
        {
          type: STARS_QUESTION,
          id: 1,
          question_id: 192,
          starRatingOptions: {
            count: 5,
          },
        },
        {
          type: RATING_QUESTION,
          id: 2,
          question_id: 193,
          description: 'Рейтинг',
          description_html: '<p>Рейтинг<p>',
          starRatingOptions: {
            count: 5,
          },
        },
      ],
    }),
  'star-rating-with-comment': () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
        },
      ],
    }),
  'star-rating-with-comment-required': () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
          comment_required: 1,
        },
      ],
    }),
  'star-rating-with-comment-complex': () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
          comment_required: 1,
          textFieldParam: {
            min: 10,
            max: 50,
          },
          langs: [
            {
              id: 11149,
              foquz_question_id: 141487,
              foquz_poll_lang_id: 13688,
              name: '',
              description: 'Star rating. (English)',
              description_html: '<p>Star rating. (English)</p>',
              sub_description: '',
              placeholder_text: null,
              select_placeholder_text: null,
              self_variant_placeholder_text: null,
              comment_label: '',
              skip_text: null,
              detail_question: null,
              self_variant_text: null,
              labels: '["One","Two","Three","Four","Five"]',
              text: null,
            },
            {
              id: 11230,
              foquz_question_id: 141487,
              foquz_poll_lang_id: 13687,
              name: '',
              description: '',
              description_html: '',
              sub_description: '',
              placeholder_text: null,
              select_placeholder_text: null,
              self_variant_placeholder_text: null,
              comment_label: '',
              skip_text: null,
              detail_question: null,
              self_variant_text: null,
              labels: '["","","","",""]',
              text: null,
            },
          ],
        },
      ],
    }),
  'star-rating-with-skip': () =>
    defaultQuestionsData({
      questions: [
        {
          skip: 1,
          skip_text: 'Skip this question',
        },
      ],
    }),
  'star-rating-with-skip-required': () =>
    defaultQuestionsData({
      questions: [
        {
          isRequired: 1,
          skip: 1,
          skip_text: 'Skip this question',
        },
      ],
    }),
  'star-rating-with-skip-and-comment': () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
          skip: 1,
          skip_text: 'Skip this question',
        },
      ],
    }),
  'star-rating-with-assessments-text': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          variantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 0,
          extra_question_rate_from: null,
          extra_question_rate_to: null,
          placeholderText: 'Пожалуйста, укажите',
          // textFieldParam: {
          //   min: 10,
          //   max: 50,
          // },
        },
      ],
    }),
  'star-rating-with-assessments-text-complex': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          variantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 1,
          extra_question_rate_from: 1,
          extra_question_rate_to: 3,
          starRatingOptions: {
            extra_question_rate_from: 1,
            extra_question_rate_to: 3,
          },
          placeholderText: 'Тут подсказка',
          textFieldParam: {
            min: 10,
            max: 50,
          },
        },
      ],
    }),
  'star-rating-with-assessments-single': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          variantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
        },
      ],
    }),
  'star-rating-with-assessments-single-required': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          variantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
        },
      ],
    }),
  'star-rating-with-assessments-multiple': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
        },
      ],
    }),
  'star-rating-with-assessments-multiple-required': () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: 'Что вам не понравилось?',
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: 'Пожалуйста, укажите',
          self_variant_text: 'Свой вариантик',
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: 'Вариант 1', is_deleted: 0 },
            { id: 2, variant: 'Вариант 2', is_deleted: 0 },
            { id: 3, variant: 'Вариант 3', is_deleted: 0 },
          ],
        },
      ],
    }),
}

export default starRatingMockData
