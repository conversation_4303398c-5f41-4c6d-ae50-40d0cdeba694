import { MATRIX_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function matrixQuestionOptions(options = {}) {
  return merge(
    {
      id: 1,
      question_id: 100001,
      type: MATRIX_QUESTION,
      isRequired: 1,
      description: 'Пожалуйста, оцените наши услуги',
      description_html: '<p>Пожалуйста, оцените наши услуги</p>',
      matrixSettings: {
        rows: ['Услуга 1', 'Услуга 2', 'Услуга 3'],
        cols: ['Плохо', 'Удовлетворительно', 'Хорошо', 'Отлично'],
        minRowsReq: 2,
        multiple_choice: '0',
        type: 'standart',
        extra_question: {
          rows: ['Услуга 1', 'Услуга 2'],
          cols: ['Плохо', 'Удовлетворительно'],
        },
      },
      random_variants_order: 0,
      skip: 0,
      skip_text: 'Пропустить этот вопрос',
      comment_enabled: 0,
      comment_required: 0,
      comment_label: 'Дополнительные комментарии',
      placeholderText: 'Введите ваши комментарии здесь',
      extra_required: 1,
      answerText: 'Почему вы поставили такую оценку?',
      self_variant_text: 'Другое',
      isHaveCustomField: true,
      detail_answers: [
        { id: 1, variant: 'Причина 1', is_deleted: 0, extra_question: 1 },
        { id: 2, variant: 'Причина 2', is_deleted: 0, extra_question: 1 },
        { id: 3, variant: 'Причина 3', is_deleted: 0, extra_question: 1 },
      ],
    },
    options,
  )
}

const matrixMockData = {
  'matrix-basic': () => defaultQuestionsData({
    questions: [matrixQuestionOptions()],
  }),
  'matrix-multiple-choice': () => defaultQuestionsData({
    questions: [matrixQuestionOptions({
      matrixSettings: { multiple_choice: '1' },
    })],
  }),
  'matrix-with-skip': () => defaultQuestionsData({
    questions: [matrixQuestionOptions({ skip: 1 })],
  }),
  'matrix-randomized': () => defaultQuestionsData({
    questions: [matrixQuestionOptions({ random_variants_order: 1 })],
  }),
  'matrix-with-required-comment': () => defaultQuestionsData({
    questions: [matrixQuestionOptions({ comment_required: 1, comment_enabled: 1 })],
  }),
  'matrix-without-extra-question': () => defaultQuestionsData({
    questions: [matrixQuestionOptions({
      matrixSettings: { extra_question: { rows: [], cols: [] } },
      answerText: null,
      extra_required: 0,
      detail_answers: [],
    })],
  }),
  'matrix-with-dropdown-variants': () => defaultQuestionsData({
    questions: [matrixQuestionOptions({
      dropdownVariants: 1,
    })],
  }),
  'matrix-english': () => defaultQuestionsData({
    questions: [matrixQuestionOptions()],
    lang: {
      id: 17343,
      foquz_poll_id: 49242,
      poll_lang_id: 2,
      checked: 0,
      default: 1,
      back_text: '',
      next_text: '',
      finish_text: '',
      questions: {
        100001: {
          description: 'Please rate our services',
          description_html: '<p>Please rate our services</p>',
          skip_text: 'Skip this question',
          comment_label: 'Additional comments',
          placeholderText: 'Enter your comments here',
          detail_question: 'Why did you give this rating?',
          self_variant_text: 'Other',
          labels: JSON.stringify({
            rows: ['Service 1', 'Service 2', 'Service 3'],
            cols: ['Poor', 'Fair', 'Good', 'Excellent'],
          }),
          detailLangs: {
            1: { question: 'Reason 1' },
            2: { question: 'Reason 2' },
            3: { question: 'Reason 3' },
          },
        },
      },
    },
  }),
}

export default matrixMockData
