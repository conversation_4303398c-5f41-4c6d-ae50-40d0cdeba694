import { SMILE_QUESTION, STARS_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function smileRatingOptions(data = {}) {
  return merge(
    {
      description: 'Оцените наш сервис',
      description_html: '<p>Оцените наш сервис<p>',
      type: SMILE_QUESTION,
      starRatingOptions: {
        count: 5,
      },
      labelsArray: [],
      smiles: [
        {
          id: 1,
          label: '',
          url: '/uploads/smiles/heart/break.svg',
          smile_url: '/uploads/smiles/heart/break.svg',
        },
        {
          id: 2,
          label: '',
          url: '/uploads/smiles/heart/full.svg',
          smile_url: '/uploads/smiles/heart/full.svg',
        },
      ],
      count: 2,
      // can be "heart" (2), "like" (2), "face" (2, 3, 5), "color_face" (2, 3, 5), "robot" (2, 3, 5), "weather", (2, 3, 5), "different" (2, 3, 5),
      smileType: 'heart',

      // whether to always show labels
      show_labels: 0,
      skip: 0,
      skip_text: 'Пропустить вопрос',
    },
    data,
  )
}

/**
 * Generates smile rating data based on the provided options.
 * @param {string} smileType - The type of smile.
 * @param {number} count - The count of smiles.
 * @param {boolean} withLabels - Whether to include labels.
 * @param {boolean} showLabels - Whether to always show labels.
 * @returns {object} - The generated smile rating data.
 */
function generateSmileRatingData(smileType, count, withLabels = false, showLabels = false) {
  const smiles = Array.from({ length: count }, (_, index) => {
    let url = `/uploads/smiles/${smileType}/${index + 1}.svg`
    let smile_url = `/uploads/smiles/${smileType}/${index + 1}.svg`

    if (smileType !== 'heart' && smileType !== 'like' && count !== 5) {
      // Если count равен 2, то нам нужно отобразить индексы 0 и 4 (первый и последний смайл)
      // Если count равен 3, то нам нужно отобразить индексы 0, 2 и 4 (первый, средний и последний смайл)
      index = index * 2
    }

    if (smileType === 'heart') {
      url
        = index === 0
          ? '/uploads/smiles/heart/break.svg'
          : '/uploads/smiles/heart/full.svg'
      smile_url
        = index === 0
          ? '/uploads/smiles/heart/break.svg'
          : '/uploads/smiles/heart/full.svg'
    }
    else if (smileType === 'like') {
      url
        = index === 0
          ? '/uploads/smiles/like/down.svg'
          : '/uploads/smiles/like/up.svg'
      smile_url
        = index === 0
          ? '/uploads/smiles/like/down.svg'
          : '/uploads/smiles/like/up.svg'
    }
    else if (smileType === 'color_face') {
      url = `/uploads/smiles/yellow/${index + 1}.svg`
      smile_url = `/uploads/smiles/yellow/${index + 1}.svg`
    }
    return {
      id: index + 1,
      label: withLabels ? `mark ${index + 1}` : '',
      url,
      smile_url,
    }
  })

  return smileRatingOptions({
    smileType,
    count,
    smiles,
    show_labels: showLabels ? 1 : 0,
  })
}

// Define the smile rating options
const smileRatingOptionsList = [
  { type: 'heart', counts: [2] },
  { type: 'like', counts: [2] },
  { type: 'face', counts: [2, 3, 5] },
  { type: 'color_face', counts: [2, 3, 5] },
  { type: 'robot', counts: [2, 3, 5] },
  { type: 'weather', counts: [2, 3, 5] },
  { type: 'emoji', counts: [2, 3, 5] },
]

// Generate the smileRatingMockData object
const smileRatingMockData = smileRatingOptionsList.reduce(
  (acc, { type, counts }) => {
    counts.forEach((count) => {
      // No labels
      const keyNoLabels = `rating-smile-${type}-${count}`
      acc[keyNoLabels] = () =>
        defaultQuestionsData({
          questions: [generateSmileRatingData(type, count)],
        })

      // With labels
      const keyWithLabels = `rating-smile-${type}-${count}-with-labels`
      acc[keyWithLabels] = () =>
        defaultQuestionsData({
          questions: [generateSmileRatingData(type, count, true)],
        })

      // With show_labels option
      const keyShowLabels = `rating-smile-${type}-${count}-show-labels`
      acc[keyShowLabels] = () =>
        defaultQuestionsData({
          questions: [generateSmileRatingData(type, count, true, true)],
        })
    })
    return acc
  },
  {},
)

const additionalSmileRatingData = {
  'rating-smile-with-skip': () =>
    defaultQuestionsData({
      questions: [
        smileRatingOptions({
          skip: 1,
          skip_text: 'Пропустить вопрос',
        }),
      ],
    }),
  'rating-smile-unrequired': () =>
    defaultQuestionsData({
      questions: [
        smileRatingOptions({
          isRequired: 0,
        }),
      ],
    }),
  'rating-smile-without-prev-button-2-questions': () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
      questions: [
        smileRatingOptions(),
        smileRatingOptions({
          type: STARS_QUESTION,
          starRatingOptions: {
            count: 5,
          },
        }),
      ],
    }),
  'rating-smile-with-skip-required': () =>
    defaultQuestionsData({
      questions: [
        smileRatingOptions({
          isRequired: 1,
          skip: 1,
          skip_text: 'Пропустить вопрос',
        }),
      ],
    }),
  'rating-smile-with-skip-and-comment': () =>
    defaultQuestionsData({
      questions: [
        smileRatingOptions({
          comment_enabled: 1,
          skip: 1,
          skip_text: 'Пропустить вопрос',
        }),
      ],
    }),
  'rating-smile-with-comment': () =>
    defaultQuestionsData({
      questions: [
        smileRatingOptions({
          comment_enabled: 1,
        }),
      ],
    }),
  'rating-smile-with-comment-required': () =>
    defaultQuestionsData({
      questions: [
        smileRatingOptions({
          comment_enabled: 1,
          comment_required: 1,
        }),
      ],
    }),
  'rating-smile-with-comment-complex': () =>
    defaultQuestionsData({
      questions: [
        smileRatingOptions({
          comment_enabled: 1,
          comment_required: 1,
          textFieldParam: {
            min: 10,
            max: 50,
          },
        }),
      ],
    }),
}

// Merge ratingNpsData into smileRatingData
Object.assign(smileRatingMockData, additionalSmileRatingData)

export default smileRatingMockData
