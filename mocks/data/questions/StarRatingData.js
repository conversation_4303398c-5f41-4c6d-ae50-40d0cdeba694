import { ASSESMENT_VARIANT_TYPES } from "../../../src/helpers/question-helpers/questionTypes";
import { QUESTION_TYPES } from "../../../src/helpers";
import defaultQuestionsData from "./defaultQuestionData";

/**
 * Моковые данные для вопроса "Звездный рейтинг"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {Object}
 */
const starRatingMockData = {
  "star-rating": () => defaultQuestionsData(),
  "star-rating-2-stars": () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            count: 2,
          },
        },
      ],
    }),
  "star-rating-2-stars-big": () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            count: 2,
            size: "lg",
          },
        },
      ],
    }),
  "star-rating-2-stars-big-always-show-labels": () =>
    defaultQuestionsData({
      questions: [
        {
          show_labels: 1,
          starRatingOptions: {
            count: 2,
            size: "lg",
            labelsArray: ["метка 1", "Номально — длинный вариант"],
          },
        },
      ],
    }),
  "star-rating-3-stars-big-always-show-labels": () =>
    defaultQuestionsData({
      questions: [
        {
          show_labels: 1,
          starRatingOptions: {
            count: 3,
            size: "lg",
            labelsArray: ["метка 1", "Номально — длинный вариант", "метка 3"],
          },
        },
      ],
    }),
  "star-rating-10-stars": () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            count: 10,
          },
        },
      ],
    }),
  "star-rating-10-stars-with-numbers": () =>
    defaultQuestionsData({
      questions: [
        {
          show_numbers: 1,
          starRatingOptions: {
            count: 10,
          },
        },
      ],
    }),
  "star-rating-unrequired": () =>
    defaultQuestionsData({
      questions: [
        {
          isRequired: 0,
        },
      ],
    }),
  "star-rating-with-labels": () =>
    defaultQuestionsData({
      questions: [
        {
          starRatingOptions: {
            labelsArray: [
              "метка 1",
              "метка 2",
              "метка 3",
              "метка 4",
              "метка 5",
            ],
          },
        },
      ],
    }),
  "star-rating-always-show-labels": () =>
    defaultQuestionsData({
      questions: [
        {
          show_labels: 1,
          starRatingOptions: {
            labelsArray: [
              "метка 1",
              "метка 2",
              "метка 3",
              "метка 4",
              "метка 5",
            ],
          },
        },
      ],
    }),
  "star-rating-without-prev-button": () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
    }),
  "star-rating-without-prev-button-2-questions": () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
      questions: [
        {
          type: QUESTION_TYPES.STAR_RATING,
          id: 1,
          question_id: 192,
          starRatingOptions: {
            count: 5,
          },
        },
        {
          type: QUESTION_TYPES.RATING,
          id: 2,
          question_id: 193,
          description: "Рейтинг",
          description_html: "<p>Рейтинг<p>",
          starRatingOptions: {
            count: 5,
          },
        },
      ],
    }),
  "star-rating-with-comment": () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
        },
      ],
    }),
  "star-rating-with-comment-required": () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
          comment_required: 1,
        },
      ],
    }),
  "star-rating-with-comment-complex": () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
          comment_required: 1,
          textFieldParam: {
            min: 10,
            max: 50,
          },
          langs: [
            {
              id: 11149,
              foquz_question_id: 141487,
              foquz_poll_lang_id: 13688,
              name: "",
              description: "Star rating. (English)",
              description_html: "<p>Star rating. (English)</p>",
              sub_description: "",
              placeholder_text: null,
              select_placeholder_text: null,
              self_variant_placeholder_text: null,
              comment_label: "",
              skip_text: null,
              detail_question: null,
              self_variant_text: null,
              labels: '["One","Two","Three","Four","Five"]',
              text: null,
            },
            {
              id: 11230,
              foquz_question_id: 141487,
              foquz_poll_lang_id: 13687,
              name: "",
              description: "",
              description_html: "",
              sub_description: "",
              placeholder_text: null,
              select_placeholder_text: null,
              self_variant_placeholder_text: null,
              comment_label: "",
              skip_text: null,
              detail_question: null,
              self_variant_text: null,
              labels: '["","","","",""]',
              text: null,
            },
          ],
        },
      ],
    }),
  "star-rating-with-skip": () =>
    defaultQuestionsData({
      questions: [
        {
          skip: 1,
          skip_text: "Skip this question",
        },
      ],
    }),
  "star-rating-with-skip-required": () =>
    defaultQuestionsData({
      questions: [
        {
          isRequired: 1,
          skip: 1,
          skip_text: "Skip this question",
        },
      ],
    }),
  "star-rating-with-skip-and-comment": () =>
    defaultQuestionsData({
      questions: [
        {
          comment_enabled: 1,
          skip: 1,
          skip_text: "Skip this question",
        },
      ],
    }),
  "star-rating-with-assessments-text": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 0,
          extra_question_rate_from: null,
          extra_question_rate_to: null,
          placeholderText: "Пожалуйста, укажите",
          // textFieldParam: {
          //   min: 10,
          //   max: 50,
          // },
        },
      ],
    }),
  "star-rating-with-assessments-text-complex": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 1,
          extra_question_rate_from: 1,
          extra_question_rate_to: 3,
          starRatingOptions: {
            extra_question_rate_from: 1,
            extra_question_rate_to: 3,
          },
          placeholderText: "Тут подсказка",
          textFieldParam: {
            min: 10,
            max: 50,
          },
        },
      ],
    }),
  "star-rating-with-assessments-single": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
        },
      ],
    }),
  "star-rating-with-assessments-single-required": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
        },
      ],
    }),
  "star-rating-with-assessments-multiple": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
        },
      ],
    }),
  "star-rating-with-assessments-multiple-required": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
        },
      ],
    }),
  "star-rating-with-assessments-multiple-screenshot-default": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что можно улучшить?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          forAllRates: 1,
          placeholderText: "Дополнительные пожелания",
          self_variant_text: "Другое",
          detail_answers: [
            { id: 1, variant: "Скорость работы", is_deleted: 0 },
            { id: 2, variant: "Интерфейс", is_deleted: 0 },
          ],
          questionScreenshot: {
            make_screenshot_enabled: 0,
            upload_enabled: 1,
            max_files: 3,
          },
        },
      ],
    }),
  "star-rating-with-assessments-multiple-screenshot-custom": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что можно улучшить?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          forAllRates: 1,
          placeholderText: "Дополнительные пожелания",
          self_variant_text: "Другое",
          detail_answers: [
            { id: 1, variant: "Скорость работы", is_deleted: 0 },
            { id: 2, variant: "Интерфейс", is_deleted: 0 },
          ],
          questionScreenshot: {
            make_screenshot_enabled: 1,
            upload_enabled: 1,
            max_files: 10,
            button_text: "Прикрепить файлы (свой текст)",
            screenshot_button_text: "Добавить скриншот",
            description: "Прикрепите скриншоты или файлы, которые помогут нам лучше понять вашу проблему (максимум 10 файлов)",
          },
        },
      ],
    }),
};

export default starRatingMockData;
