import merge from "lodash.merge";
import { RATING_NPS_DESIGN } from "../../../src/helpers/question-helpers/questionTypes";
import defaultQuestionsData from "./defaultQuestionData";
import { QUESTION_TYPES } from "@/helpers/question-helpers/questionTypes";

export const ratingNpsOptions = (data = {}) =>
  merge(
    {
      description: "Оцените наш сервис",
      description_html: "<p>Оцените наш сервис<p>",
      type: QUESTION_TYPES.RATING_NPS,
      fromOne: 0,
      set_variants: 0,
      id: 1,
      question_id: 128281,
      npsRatingSetting: {
        id: 4216,
        foquz_question_id: 128281,
        design: RATING_NPS_DESIGN.COLORED,
        start_point_color: "#f96261",
        end_point_color: "#00c968",
        start_label: null,
        end_label: null,
      },
    },
    data,
  );

/**
 * Моковые данные для вопроса "Рейтинг NPS"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {Object}
 */
const ratingNpsData = {
  "rating-nps": () =>
    defaultQuestionsData({
      questions: [ratingNpsOptions()],
    }),
  "rating-nps-from-1": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          fromOne: 1,
        }),
      ],
    }),
  "rating-nps-custom-gradient": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          npsRatingSetting: {
            design: RATING_NPS_DESIGN.CUSTOM,
            start_point_color: "#0032fd",
            end_point_color: "#c400a3",
          },
        }),
      ],
    }),
  "rating-nps-black-and-white": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          npsRatingSetting: {
            design: RATING_NPS_DESIGN.BLACK_AND_WHITE,
          },
        }),
      ],
    }),
  "rating-nps-with-labels": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          npsRatingSetting: {
            start_label: "Начало",
            end_label: "Конец",
          },
        }),
      ],
    }),
  "rating-nps-with-skip": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          skip: 1,
          skip_text: "Пропустить вопрос",
        }),
      ],
    }),
  "rating-nps-unrequired": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          isRequired: 0,
        }),
      ],
    }),
  "rating-nps-without-prev-button-2-questions": () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
      questions: [
        ratingNpsOptions(),
        ratingNpsOptions({
          type: QUESTION_TYPES.STAR_RATING,
          id: 2,
          question_id: 128288,
          starRatingOptions: {
            count: 5,
          },
        }),
      ],
    }),
  "rating-nps-with-skip-required": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          isRequired: 1,
          skip: 1,
          skip_text: "Пропустить вопрос",
        }),
      ],
    }),
  "rating-nps-with-skip-and-comment": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          comment_enabled: 1,
          skip: 1,
          skip_text: "Пропустить вопрос",
        }),
      ],
    }),
  "rating-nps-with-comment": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          comment_enabled: 1,
        }),
      ],
    }),
  "rating-nps-with-comment-required": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          comment_enabled: 1,
          comment_required: 1,
        }),
      ],
    }),
  "rating-nps-with-comment-complex": () =>
    defaultQuestionsData({
      questions: [
        ratingNpsOptions({
          comment_enabled: 1,
          comment_required: 1,
          textFieldParam: {
            min: 10,
            max: 50,
          },
        }),
      ],
    }),
};

export default ratingNpsData;
