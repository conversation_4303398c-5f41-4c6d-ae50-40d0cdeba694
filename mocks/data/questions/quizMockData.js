import { QUIZ_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function getQuizMockData(data = {}) {
  return merge(
    {
      id: 1,
      description: 'Как вы оцениваете свою подготовку к экзамену?',
      description_html: '<p>Как вы оцениваете свою подготовку к экзамену?</p>',
      type: QUIZ_QUESTION,
      values: [
        {
          id: 12762,
          label: 'Однострочное / Без маски',
          value: '',
          isRequired: 1,
          maskType: 0,
          isTextarea: 0,
          textFieldParam: {
            min: 2,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12763,
          label: 'Многострочное - необязательный',
          value: '',
          isRequired: 0,
          maskType: 0,
          isTextarea: 1,
          textFieldParam: {
            min: 12,
            max: 500,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12764,
          label: 'Телефон',
          value: '',
          isRequired: 1,
          maskType: 1,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '+7 (___) ___ - ____',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12765,
          label: 'Почта',
          value: '',
          isRequired: 1,
          maskType: 2,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '<EMAIL>',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12766,
          label: 'Число',
          value: '',
          isRequired: 1,
          maskType: 3,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '0',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12767,
          label: 'Сайт',
          value: '',
          isRequired: 1,
          maskType: 4,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: 'http://example.com',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12768,
          label: 'ФИО',
          value: '',
          isRequired: 1,
          maskType: 5,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '',
          maskConfig: {
            name: {
              visible: 'true',
              required: 'true',
              placeholderText: '',
              minLength: '0',
              maxLength: '250',
              value: '',
            },
            surname: {
              visible: 'true',
              required: 'true',
              placeholderText: '',
              minLength: '0',
              maxLength: '250',
              value: '',
            },
            patronymic: {
              visible: 'true',
              required: 'true',
              placeholderText: '',
              minLength: '0',
              maxLength: '250',
              value: '',
            },
          },
          langs: [],
        },
        {
          id: 12769,
          label: 'Дата',
          value: '',
          isRequired: 1,
          maskType: 6,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '00.00.0000',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12770,
          label: 'Период',
          value: '',
          isRequired: 1,
          maskType: 7,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '00.00.0000',
          maskConfig: {},
          langs: [],
        },
        {
          id: 12771,
          label: 'Дата (день и месяц)',
          value: '',
          isRequired: 1,
          maskType: 8,
          isTextarea: 0,
          textFieldParam: {
            min: 0,
            max: 250,
          },
          linkWithClientField: 0,
          linkedClientField: 'email',
          rewriteLinkedField: 0,
          placeholderText: '',
          maskConfig: {},
          langs: [],
        },
      ],
    },
    data,
  )
}

export const quizMockData = {
  'quiz-default': () => defaultQuestionsData({
    questions: [
      getQuizMockData({}),
    ],
  }),
}

export default quizMockData
