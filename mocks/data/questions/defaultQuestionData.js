import merge from "lodash.merge";

/**
 * Стандартные данные, которые возвращаются при получении опроса
 * @param {Object} data - Дополнительные данные, которые можно добавить к стандартным
 * @returns {Object} - Данные опроса
 */
export default function defaultQuestionsData(data = {}) {
  return merge(
    {
      answer: {
        id: 720331,
        created_at: "2024-05-25 16:46:18",
        updated_at: "2024-05-25 16:46:34",
        client_id: null,
        user_id: null,
        auth_key: "72e2aea779a249979e2c934ed215985e",
        foquz_poll_id: 40013,
        status: "open",
        ip_address: "",
        order_id: 0,
        delivery_id: null,
        order_sum: 0,
        contact_id: null,
        os: null,
        useragent: null,
        custom_fields: "[]",
        points: null,
        max_points: 0,
        answer_filial_id: null,
        device: null,
        first_question_showed_at: null,
        answer_time: 16,
        processing_time_in_minutes: 0,
        language: null,
        feedback_id: 176901,
        answer_dictionary_element_id: null,
        user_agreement: 0,
        points_job_id: null,
        foquzAnswer: [
          {
            id: 361977,
            created_at: "2024-05-25 16:46:34",
            foquz_poll_answer_id: 720331,
            foquz_question_id: 126922,
            answer: null,
            rating: null,
            detail_item: null,
            is_self_variant: 0,
            self_variant: null,
            question_name: "",
            points: null,
            max_points: 0,
            skipped: 0,
            avg_score: null,
            points_job_id: null,
          },
        ],
      },
      poll: {
        id: 40013,
        created_at: 1715938115,
        updated_at: 1716896975,
        created_by: 1233,
        updated_by: 1233,
        name: "Виджет. Звёздный рейтинг",
        image: null,
        description: null,
        is_tmp: 0,
        status: 0,
        key: "F6647243001495",
        company_id: 753,
        is_qr_code: 1,
        is_auto: 0,
        date_start: "2024-05-17",
        date_end: "2024-05-17",
        goals_count: null,
        limit_count: null,
        end_of_question: null,
        trigger: null,
        trigger_time: null,
        goal_text: "",
        is_folder: 0,
        folder_id: null,
        trigger_days: null,
        notification_script_id: null,
        kiosk_mode: 1,
        is_template: 0,
        send_time: null,
        title: "",
        point_system: 0,
        max_points: null,
        dont_send_if_passed: 0,
        dont_send_if_passed_link: 0,
        dont_send_if_promocode_used: 0,
        deleted: 0,
        deleted_at: null,
        deleted_by: null,
        personal_data: 0,
        is_published: 1,
        time_to_pass: null,
        datetime_start: null,
        datetime_end: null,
        show_foquz_link: 0,
        time_to_restart: 15,
        trigger_orders: null,
        expiration_in_minutes: 0,
        utm_source: null,
        utm_medium: null,
        utm_campaign: null,
        processing_time_in_minutes: 0,
        processing_time_by_link_in_minutes: 0,
        processing_time_for_client_in_minutes: 0,
        code: null,
        is_short_link: 1,
        time_to_restart_screen_type_end: null,
        stop_sending: null,
        stop_sending_link: null,
        is_active: 1,
        mailing_limit: 0,
        mailing_frequency: 0,
        answers_count: 1,
        sent_answers_count: 12132,
        opened_answers_count: 11,
        in_progress_answers_count: 0,
        filled_answers_count: 1,
        processing_new_answers_count: 18,
        processing_inprocess_answers_count: 0,
        processing_work_answers_count: 0,
        processing_delayed_answers_count: 0,
        processing_done_answers_count: 0,
        default_executor_id: null,
        default_moderator_id: null,
        first_answer_at: "2024-05-22 15:38:40",
        last_answer_at: "2024-06-05 16:06:50",
        dictionary_id: null,
        editing_duration: null,
        need_auth: 0,
        foquzPollLangs: [
          {
            id: 13687,
            foquz_poll_id: 43680,
            lang_id: 1,
            checked: 1,
            default: 1,
            back_text: "",
            next_text: "",
            finish_text: "",
            unrequired_text: "",
          },
          {
            id: 13688,
            foquz_poll_id: 43680,
            lang_id: 2,
            checked: 1,
            default: 0,
            back_text: "Back",
            next_text: "Next",
            finish_text: "Success!",
            unrequired_text: "Unreqiured",
            messages: {
              "Обязательное поле": "Required field",
              "Поле обязательно для заполнения": "The field must be filled in",
              "Неверный формат": "Invalid format",
              "Некорректный формат": "Incorrect format",
              "Некорректный формат интервала": "Incorrect interval format",
              "Некорректное значение": "Incorrect value",
              "Некорректный диапазон": "Incorrect range",
              "Неверный формат ссылки": "Incorrect link format",
              "Некорректный период": "Incorrect period",
              "{count} символ": "{count} symbol",
              "{count} символа": "{count} symbols",
              "{count} символов": "{count} symbols",
              "{count} вариант": "{count} variant",
              "{count} варианта": "{count} variants",
              "{count} вариантов": "{count} variants",
              "Должно быть введено хотя бы {characters}":
                "At least {characters} must be entered",
              "Должно быть введено не более {characters}":
                "No more than {characters} must be entered",
              "Должно быть заполнено хотя бы одно поле":
                "At least one field must be filled in",
              "Нужно добавить хотя бы {options}":
                "At least {options} must be added",
              "Название повторяется": "This name already exists",
              "Нужно выбрать хотя бы один вариант ответа":
                "You must select at least one answer",
              "Можно загружать файлы форматов:":
                "You can upload files in the formats:",
              "Размер файла не должен превышать {size} Мб":
                "File must not exceed {size} MB",
              "Необходимо добавить хотя бы одно изображение":
                "You must add at least one picture",
              "Необходимо добавить хотя бы одно видео":
                "You must add at least one video",
              "Максимальное количество файлов:": "Maximum number of files",
              "Нужно поставить оценку": "You need to evaluate",
              "Нужно поставить все оценки": "You need to evaluate everything",
              "Не все оценки поставлены": "Not all grades are set",
              "Необходимо ответить хотя бы на {number}":
                "Please answer at least {number}",
              "Введите свой вариант": "Enter your answer",
              "Выберите значение в списке и/или введите свой вариант":
                "Select a value from the list and/or enter your answer",
              "Выберите значение в списке": "Select a value from the list",
              "Нужно выбрать один из вариантов":
                "You need to select one of the options",
              "Нужно изменить порядок": "You need to change the order",
              "Ваш комментарий": "Your comment",
              Выбрать: "Select",
              "Ваш выбор": "Your choice",
              "Нет результатов, удовлетворяющих условию поиска":
                "No results matching your search",
              "Загрузите файл": "Upload file",
              "Загрузить файл": "Upload file",
              "Загрузить фото": "Upload photo",
              "Загрузить видео": "Upload video",
              "Загрузить фото, видео": "Upload photo, video",
              "Загрузить аудио": "Upload audio",
              "Загрузить видео, аудио": "Upload video, audio",
              "Загрузить фото, аудио": "Upload photo, audio",
              "Загрузить фото, видео, аудио": "Upload photo, video, audio",
              Ужасно: "Very bad",
              Плохо: "Bad",
              Нормально: "Normal",
              Хорошо: "Good",
              Отлично: "Excellent",
              "Всё понравилось": "I liked everything",
              "Написать жалобу": "Submit a complaint",
              "Текст жалобы": "Complaint text",
              "Введите текст жалобы или загрузите фотографию":
                "Enter the text of your complaint or upload a photo",
              Отменить: "Cancel",
              Отправить: "Send",
              "Спасибо за обратную связь!": "Thank you for your feedback!",
              "Мы свяжемся с вами в течение 24 часов":
                "We'll contact you within 24 hours",
              "Отправлена жалоба № {number}": "Complaint № {number} was sent",
              "Ваш адрес {email} будет отписан от рассылки":
                "Your email address {email} will be unsubscribed",
              "Вы будете отписаны от рассылки": "You will be unsubscribed",
              "Создано в Foquz": "Created in Foquz",
              Скопировать: "Copy",
              Скопировано: "Copied",
              "Оставшееся время": "Remaining time",
              "Тестовый режим": "Test mode",
              Необязательный: "Optional",
              Назад: "Back",
              "Опрос успешно пройден!":
                "The survey has been successfully completed!",
              "К сожалению, время для прохождения опроса истекло":
                "Unfortunately, the time to complete the survey has expired",
              "К сожалению, ссылка устарела<br>и опрос больше не актуален":
                "Unfortunately, the link has expired<br> and the survey is no longer available",
              "К сожалению, опрос ещё не доступен для прохождения. Попробуйте позднее.":
                "Unfortunately, the survey is not yet available. Try again later.",
              "К сожалению, опрос больше не доступен для прохождения.":
                "Unfortunately, the survey is no longer available.",
              "Здесь был опрос, но его уже, к сожалению, нет.":
                "There was a survey here, but unfortunately it's no longer available.",
              "Опрос по техническим причинам недоступен.<br>В ближайшее время проблемы будут устранены.<br>Приносим извинения за доставленные неудобства.":
                "The survey is not available for technical reasons.<br> The issue will be resolved soon.<br> We apologize for the inconvenience.",
              "К сожалению, лимит для прохождения опроса в тестовом режиме исчерпан.<br>Для дальнейшего прохождения опрос необходимо опубликовать.":
                "Unfortunately, the limit for taking the survey in test mode has been reached.<br> To continue taking the survey, it must be published.",
              "Опрос недоступен.<br>Опрос был отключен, удален или время приема ответов для него истекло.":
                "Survey is unavailable.<br> The survey has been disabled, deleted, or the response time for the survey has expired.",
              "Создайте свой опрос в FOQUZ": "Create your request in Foquz",
              "Демо-доступ на 1 месяц и до 50 собранных анкет":
                "Demo access for 1 month and up to 50 collected surveys",
              Создать: "Create",
              Отписаться: "Unsubscribe",
              "Я хочу остаться": "I want to stay",
              "Вы отписаны от рассылки": "You are unsubscribed",
              "Мы заботимся о&nbsp;конфиденциальности ваших данных. Используя сервис Foquz, вы&nbsp;соглашаетесь с&nbsp;<a href='https://foquz.ru/agreement.pdf' target='_blank'>пользовательским соглашением</a> и&nbsp;<a href='https://foquz.ru/confidential.pdf' target='_blank'>политикой конфиденциальности</a>":
                "We care about your data privacy. Using the Foquz service, you are agree with the&nbsp;<a href='https://foquz.ru/agreement.pdf' target='_blank'>User agreement</a>&nbsp;and&nbsp;<a href='https://foquz.ru/confidential.pdf' target='_blank'>Privacy&nbsp;Policy</a>",
              Закрыть: "Close",
              "Копировать промокод": "Copy promo code",
              "Промокод скопирован": "Promo code copied",
              "Добавить вариант": "Add variant",
              Удалить: "Delete",
              Логин: "Login",
              Пароль: "Password",
              "Неверный логин или пароль": "Incorrect login or password",
              Войти: "Sign in",
              Авторизация: "Sign in to the survey",
              "Этот опрос доступен только авторизованным пользователям":
                "This survey is available to authorized users only",
              "Нужно поставить оценку всем блюдам":
                "You need to assess all the dishes",
              Фамилия: "Surname",
              Имя: "Name",
              Отчество: "Patronymic",
            },
          },
        ],
        startPage: {
          id: 67915,
          created_at: "2024-05-17 12:28:35",
          updated_at: "2024-05-17 12:28:35",
          foquz_poll_id: 40013,
          type: "start",
          name: "<strong>Заголовок</strong>",
          description: "Текст раздела",
          description_html: "<p>Текст раздела<p>",
          image_url: null,
          is_show_complain: 0,
          is_show_unsubscribe: 0,
          is_promocode: 0,
          promocode: null,
          enabled: 0,
          poll_text: null,
          unsubscribe_text: null,
          complaint_text: null,
          socNetworksOptions: null,
        },
        endPage: {
          id: 67916,
          created_at: "2024-05-17 12:28:35",
          updated_at: "2024-05-17 12:28:35",
          foquz_poll_id: 40013,
          type: "end",
          name: "<strong>Заголовок</strong>",
          description: "Текст раздела",
          description_html: "<p>Текст раздела<p>",
          image_url: null,
          is_show_complain: 0,
          is_show_unsubscribe: 0,
          is_promocode: 0,
          promocode: null,
          enabled: 0,
          poll_text: null,
          unsubscribe_text: null,
          complaint_text: null,
          socNetworksOptions: null,
        },
      },
      questions: [
        {
          id: 1,
          question_id: 129364,
          alias: "",
          name: "",
          description: "Foo",
          description_html: "<p>Foo</p>",
          subdescription: null,
          type: 15,
          mediaType: 0,
          questionContent: "",
          assessmentType: 0,
          assessmentVariantsType: 1,
          assessmentVariants: [],
          detail_answers: [],
          priorityAnswer: null,
          answerType: 2,
          forAllRates: 0,
          answerText: "",
          stars: null,
          maskType: 0,
          dateType: null,
          category: 0,
          b_name: 0,
          gallery: [],
          enableGallery: false,
          smileType: null,
          variantsType: 1,
          comment: "",
          comment_label: "Ваш комментарий",
          comment_enabled: 0,
          comment_required: 0,
          isRequired: 1,
          isValid: false,
          checkAll: 0,
          minDishPrice: 0,
          isHaveComment: false,
          isHaveCustomField: 0,
          filesLength: 4,
          fileTypes: [],
          textFieldParam: {
            min: 0,
            max: 250,
          },
          selfVariantParam: {
            min: 0,
            max: 250,
          },
          isCustomFieldChecked: false,
          radioButtonCheckedValue: "",
          textFieldValue: "",
          value: "",
          dateValue: "",
          timeValue: "",
          files: [],
          variants: [],
          values: [],
          arRegionsIDs: [],
          arDistrictsIDs: [],
          arCityIDs: [],
          arStreetsIDs: [],
          placeholderText: null,
          selectPlaceholderText: null,
          selfVariantPlaceholderText: null,
          chooseType: null,
          smiles: [],
          chooseMedia: [],
          maskConfig: {},
          npsRatingSetting: null,
          differentialRows: [],
          semDifSetting: null,
          matrixElements: [],
          reorder_required: false,
          matrixSettings: null,
          showQuestion: true,
          questionLogic: [],
          questionViewLogic: [],
          starRatingOptions: {
            id: 25233,
            foquz_question_id: 129364,
            color: "#f8cd1c",
            count: 5,
            size: "md",
            labels: '["","","","",""]',
            extra_question_rate_from: null,
            extra_question_rate_to: null,
            labelsArray: ["", "", "", "", ""],
          },
          max_choose_variants: null,
          self_variant_text: null,
          intermediateBlock: null,
          dropdownVariants: 0,
          only_date_month: 0,
          random_variants_order: 0,
          wrongCondition: false,
          endScreenImages: [],
          skip: 0,
          skip_text: "",
          skip_variant: 0,
          skip_row: 0,
          skip_column: 0,
          show_tooltips: 0,
          skipped: 0,
          show_numbers: 0,
          show_labels: 0,
          fromOne: 0,
          donor: null,
          donor_rows: null,
          donor_columns: null,
          donorSelected: 1,
          donorColumnsSelected: 1,
          langs: [],
          dictionary_id: null,
          dictionary_element_id: null,
          dictionary_list_type: "list",
          dictionary_sort: "default",
          set_variants: 0,
          scaleRatingSetting: [],
          isHaveExtra: false,
          extra_required: 1,
          disable_select_category: 0,
          variants_with_files: 0,
        },
      ],
      design: {
        id: 10323,
        foquz_poll_id: 40013,
        background_image: "",
        mobile_background_image: "",
        logo_image: "/img/poll-design__custom-logo.png",
        main_color: "rgba(63, 101, 241, 1)",
        background_color: "rgba(207, 216, 220, 1)",
        header_color: "#000000",
        star_color: "#F8CD1C",
        rating_color: "#3F65F1",
        nps_color_from: "#F96261",
        nps_color_to: "#00C968",
        sem_diff_color_from: "#73808D",
        sem_diff_color_to: "#73808D",
        is_use_header: 1,
        font_family: "sans-serif",
        title_font_size: "30",
        font_size: "14",
        text_on_bg: "rgba(193, 129, 160, 1)",
        text_on_place: "rgba(37, 17, 234, 1)",
        link_color: "rgba(0, 255, 255, 1)",
        from_template: 1,
        logo_link: "http://foquz.ru",
        logo_type: "image",
        logo_text: null,
        logo_font_family: null,
        logo_color: null,
        back_text: "",
        back_button_background_color: "rgba(255, 255, 255, 0)",
        back_button_text_color: "var(--fqz-widget-text-on-place)",
        back_button_stroke_color: "var(--fqz-widget-text-on-place)",
        back_button_radius: 24,
        next_button_background_color: "rgba(63, 101, 241, 0)",
        next_button_text_color: "var(--fqz-widget-text-on-place)",
        next_button_stroke_color: "var(--fqz-widget-main-color)",
        next_button_radius: 24,
        start_button_background_color: "var(--fqz-widget-main-color)",
        start_button_text_color: "rgba(255, 255, 255, 1)",
        start_button_stroke_color: "var(--fqz-widget-main-color)",
        start_button_radius: 24,
        next_text: "",
        finish_text: "",
        finish_link: "",
        unrequired_text: "Необязательный",
        show_process: 1,
        darkening_background: 0,
        place_under_buttons: "dark",
        show_prev_button: 1,
        main_place_color: "rgba(232, 255, 154, 1)",
        choose_language: 1,
        full_width: 0,
        disable_question_autoscroll: 0,
        backgroundImage: "/img/themes/background4.jpg",
        logo: "/img/poll-design__custom-logo.png",
        mainColor: "#3f65f1",
        logoLink: "http://foquz.ru",
        bgColor: "rgba(207, 216, 220, 1)",
        headerColor: "#000000",
        starColor: "#F8CD1C",
        ratingColor: "#3F65F1",
        npsColorFrom: "#F96261",
        npsColorTo: "#00C968",
        semDiffColorFrom: "#73808D",
        semDiffColorTo: "#73808D",
        textOnBg: "#c181a0",
        textOnPlace: "#2511ea",
        linkColor: "#ffffff",
        fontFamily: "Roboto, sans-serif",
        titleFontSize: "30",
        fontSize: "14",
        isUseHeader: true,
        templateId: 1,
        logoType: "image",
        logoText: null,
        logoFontFamily: null,
        logoColor: null,
        backText: "Назадд",
        nextText: "",
        finishText: "",
        finishLink: "",
        placeUnderButtons: "dark",
      },
      isActive: true,
      isAnswersLimitsOver: false,
      showFoquzLink: false,
      allowEditAfterDone: false,
      variables: {
        fio: "",
        codes: [],
        scoresInterpretationRanges: [],
      },
    },
    data,
  );
}
