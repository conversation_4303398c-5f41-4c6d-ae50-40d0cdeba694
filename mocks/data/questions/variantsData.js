import merge from "lodash.merge";
import {
  QUESTION_TYPES,
  VARIANT_ITEM_TYPES,
  VARIANT_TYPES,
} from "../../../src/helpers/question-helpers/questionTypes";
import defaultQuestionsData from "./defaultQuestionData";

const variants = [
  {
    id: 93561,
    type: 0,
    variant: "Раз",
    position: 2,
    is_deleted: 0,
    file_id: 221,
    file_url: "/uploads/1/09f9/05c70efe0206407a2eb46c4a4995.mp4",
    preview_url: "/uploads/video-preview.jpg",
    isChecked: false,
  },
  {
    id: 93562,
    type: 0,
    variant: "Интерьер 2. Страховая сумма субъективно защищает договор.",
    position: 2,
    is_deleted: 0,
    file_id: 221,
    file_url: "/uploads/1/09f9/05c70efe0206407a2eb46c4a4995.mp4",
    preview_url: "/uploads/video-preview.jpg",
    isChecked: false,
    comment_required: 0,
  },
  {
    id: 93563,
    type: 0,
    variant: "Три",
    position: 3,
    is_deleted: 0,
    file_id: 221,
    file_url: "/uploads/1/09f9/05c70efe0206407a2eb46c4a4995.mp4",
    preview_url: "/uploads/video-preview.jpg",
    isChecked: false,
    comment_required: 0,
  },
  {
    id: 93564,
    type: VARIANT_ITEM_TYPES.REMOVE_OTHERS,
    variant: "Четыре",
    position: 4,
    is_deleted: 0,
    file_id: 221,
    file_url: "/uploads/1/09f9/05c70efe0206407a2eb46c4a4995.mp4",
    preview_url: "/uploads/video-preview.jpg",
    isChecked: false,
    comment_required: 0,
  },
];

export const variantsOptions = (data = {}) =>
  merge(
    {
      description: "Выберите один или несколько вариантов",
      description_html: "<p>Выберите один или несколько вариантов<p>",
      type: QUESTION_TYPES.VARIANTS,
      textFieldParam: {
        min: 0,
        max: 250,
      },
      placeholderText: "Введите текст",
      variantsType: VARIANT_TYPES.SINGLE,
      variants,
      detail_answers: variants,
      variants_with_files: 0,
      randomVariantsOrder: 0,
      max_choose_variants: 2,
      isHaveCustomField: 1,
      self_variant_text: "Свой вариант",
      selfVariantPlaceholderText: "Введите свой вариант",
      self_variant_comment_required: 0,
      selfVariantParam: {
        min: 0,
        max: 50,
      },
    },
    data,
  );

const variantsMockData = {
  "variants-single": () =>
    defaultQuestionsData({ questions: [variantsOptions()] }),
  "varians-single-not-required": () =>
    defaultQuestionsData({
      questions: [variantsOptions({ isRequired: 0 })],
    }),
  "variants-multiple": () =>
    defaultQuestionsData({
      questions: [variantsOptions({ variantsType: VARIANT_TYPES.MULTIPLE })],
    }),
  "variants-multiple-min-choose-variants": () =>
    defaultQuestionsData({
      questions: [
        variantsOptions({
          variantsType: VARIANT_TYPES.MULTIPLE,
          min_choose_variants: 2,
        }),
      ],
    }),
  "variants-multiple-with-files": () =>
    defaultQuestionsData({
      questions: [
        variantsOptions({
          variants_with_files: 1,
          variantsType: VARIANT_TYPES.MULTIPLE,
        }),
      ],
    }),
  "variants-multiple-skip": () =>
    defaultQuestionsData({
      questions: [
        variantsOptions({
          variantsType: VARIANT_TYPES.MULTIPLE,
          skip: 1,
          skip_text: "",
        }),
      ],
    }),
  "variants-multiple-skip-comment": () =>
    defaultQuestionsData({
      questions: [
        variantsOptions({
          variantsType: VARIANT_TYPES.MULTIPLE,
          skip: 1,
          skip_text: "",
          comment_required: 1,
          comment_enabled: 1,
        }),
      ],
    }),
};

export default variantsMockData;
