import merge from "lodash.merge";
import { ASSESMENT_VARIANT_TYPES } from "../../../src/helpers/question-helpers/questionTypes";
import defaultQuestionsData from "./defaultQuestionData";
import { QUESTION_TYPES } from "@/helpers/question-helpers/questionTypes";

export const ratingOptions = (data = {}) =>
  merge(
    {
      count: 5,
      color: "rgb(63, 101, 241)",
    },
    data,
  );

/**
 * Моковые данные для вопроса "Рейтинг"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {Object}
 */
const ratingMockData = {
  rating: () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
        },
      ],
    }),
  "rating-2-items": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions({ count: 2 }),
        },
      ],
    }),
  "rating-10-items": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions({ count: 10 }),
        },
      ],
    }),
  "rating-with-labels": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions({
            labelsArray: [
              "метка 1",
              "метка 2",
              "метка 3",
              "метка 4",
              "метка 5",
            ],
          }),
        },
      ],
    }),
  "rating-with-skip": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          skip: 1,
          skip_text: "Пропустить вопрос",
        },
      ],
    }),
  "rating-unrequired": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          isRequired: 0,
        },
      ],
    }),
  "rating-without-prev-button-2-questions": () =>
    defaultQuestionsData({
      design: {
        show_prev_button: 0,
      },
      questions: [
        {
          id: 1,
          question_id: 192,
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          detail_answers: [],
          starRatingOptions: ratingOptions(),
        },
        {
          id: 2,
          question_id: 284893,
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.STAR_RATING,
          detail_answers: [],
          comment_enabled: 1,
          starRatingOptions: {
            count: 5,
          },
        },
      ],
    }),
  "rating-with-skip-required": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          isRequired: 1,
          skip: 1,
          skip_text: "Пропустить вопрос",
        },
      ],
    }),
  "rating-with-skip-and-comment": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          comment_enabled: 1,
          skip: 1,
          skip_text: "Пропустить вопрос",
        },
      ],
    }),
  "rating-with-comment": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          comment_enabled: 1,
        },
      ],
    }),
  "rating-with-comment-required": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          comment_enabled: 1,
          comment_required: 1,
        },
      ],
    }),
  "rating-with-comment-complex": () =>
    defaultQuestionsData({
      questions: [
        {
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          comment_enabled: 1,
          comment_required: 1,
          textFieldParam: {
            min: 10,
            max: 50,
          },
        },
      ],
    }),
  "rating-with-assessments-text": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
        },
      ],
    }),
  "rating-with-assessments-text-complex": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          comment_required: 1,
          placeholderText: "Тут подсказка",
          textFieldParam: {
            min: 10,
            max: 50,
          },
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions({
            extra_question_rate_from: 1,
            extra_question_rate_to: 3,
          }),
        },
      ],
    }),
  "rating-with-assessments-single": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
        },
      ],
    }),
  "rating-with-assessments-single-required": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
        },
      ],
    }),
  "rating-with-assessments-multiple": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          comment_required: 0,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
        },
      ],
    }),
  "rating-with-assessments-multiple-required": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что вам не понравилось?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 1,
          extraQuestionRateFrom: null,
          extraQuestionRateTo: null,
          placeholderText: "Пожалуйста, укажите",
          self_variant_text: "Свой вариантик",
          textFieldParam: {
            min: 10,
            max: 50,
          },
          detail_answers: [
            { id: 1, variant: "Вариант 1" },
            { id: 2, variant: "Вариант 2" },
            { id: 3, variant: "Вариант 3" },
          ],
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
        },
      ],
    }),
  "rating-with-assessments-multiple-screenshot-default": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что можно улучшить?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          forAllRates: 1,
          placeholderText: "Дополнительные пожелания",
          self_variant_text: "Другое",
          detail_answers: [
            { id: 1, variant: "Скорость работы", is_deleted: 0, extra_question: 1 },
            { id: 2, variant: "Интерфейс", is_deleted: 0, extra_question: 1 },
          ],
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          questionScreenshot: {
            make_screenshot_enabled: 0,
            upload_enabled: 1,
            max_files: 3,
          },
        },
      ],
    }),
  "rating-with-assessments-multiple-screenshot-custom": () =>
    defaultQuestionsData({
      questions: [
        {
          answerText: "Что можно улучшить?",
          assessmentVariantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
          isHaveCustomField: 1,
          isHaveExtra: 1,
          extra_required: 0,
          forAllRates: 1,
          placeholderText: "Дополнительные пожелания",
          self_variant_text: "Другое",
          detail_answers: [
            { id: 1, variant: "Скорость работы", is_deleted: 0, extra_question: 1 },
            { id: 2, variant: "Интерфейс", is_deleted: 0, extra_question: 1 },
          ],
          description: "Оцените наш сервис",
          description_html: "<p>Оцените наш сервис<p>",
          type: QUESTION_TYPES.RATING,
          starRatingOptions: ratingOptions(),
          questionScreenshot: {
            make_screenshot_enabled: 1,
            upload_enabled: 1,
            max_files: 10,
            button_text: "Прикрепить файлы (свой текст)",
            screenshot_button_text: "Добавить скриншот",
            description: "Прикрепите скриншоты или файлы, которые помогут нам лучше понять вашу проблему (максимум 10 файлов)",
          },
        },
      ],
    }),
};

export default ratingMockData;
