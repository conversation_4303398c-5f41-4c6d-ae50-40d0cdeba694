import { DATE_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

/**
 *     this.maskType = data.maskType
 
    if (this.dateType === 0 && !this.onlyDateMonth) {
      this.maskType = MaskTypes.Date
    }
    else if (this.dateType === 0 && this.onlyDateMonth) {
      this.maskType = MaskTypes.DateMonth
    }
    else if (this.dateType === 1) {
      this.maskType = MaskTypes.Time
    }
    else if (this.dateType === 2 && !this.onlyDateMonth) {
      this.maskType = MaskTypes.DateTime
    }
    else if (this.dateType === 2 && this.onlyDateMonth) {
      this.maskType = MaskTypes.DateMonthTime
    }
 */

export function dateOptions(data = {}) {
  return merge(
    {
      description: 'Дата',
      description_html: '<p>Дата<p>',
      type: DATE_QUESTION,
      isRequired: 1,
      dateType: 0,
      only_date_month: 0,
      ...data,
    },
  )
}

const dateQuestionMockData = {
  'date-default': () => defaultQuestionsData({
    questions: [dateOptions()],
  }),
  'date-default-unrequired': () => defaultQuestionsData({
    questions: [dateOptions({ isRequired: 0 })],
  }),
  'date-date-month': () => defaultQuestionsData({
    questions: [dateOptions({ only_date_month: 1 })],
  }),
  'date-date-month-unrequired': () => defaultQuestionsData({
    questions: [dateOptions({ only_date_month: 1, isRequired: 0 })],
  }),
  'date-time': () => defaultQuestionsData({
    questions: [dateOptions({ dateType: 1 })],
  }),
  'date-time-unrequired': () => defaultQuestionsData({
    questions: [dateOptions({ dateType: 1, isRequired: 0 })],
  }),
  'date-date-time': () => defaultQuestionsData({
    questions: [dateOptions({ dateType: 2 })],
  }),
  'date-date-time-unrequired': () => defaultQuestionsData({
    questions: [dateOptions({ dateType: 2, isRequired: 0 })],
  }),
  'date-date-month-time': () => defaultQuestionsData({
    questions: [dateOptions({ dateType: 2, only_date_month: 1 })],
  }),
  'date-date-month-time-unrequired': () => defaultQuestionsData({
    questions: [dateOptions({ dateType: 2, only_date_month: 1, isRequired: 0 })],
  }),
}

export default dateQuestionMockData
