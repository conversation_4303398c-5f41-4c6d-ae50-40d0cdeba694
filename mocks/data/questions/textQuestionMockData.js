import { TEXT_QUESTION, TEXT_VARIANT_TYPES } from '@entities/question/model/types'
import MaskTypes from '@shared/constants/maskTypes'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function textOptions(data = {}) {
  return merge(
    {
      description: 'Текстовый ответ',
      description_html: '<p>Текстовый ответ<p>',
      type: TEXT_QUESTION,
      variantsType: TEXT_VARIANT_TYPES.TEXTAREA,
      isRequired: 1,
      textFieldParam: {
        min: 0,
        max: 700,
      },
      placeholderText: 'Введите текст',
      maskType: MaskTypes.NoMask,
    },
    data,
  )
}

const textMockData = {
  'textarea-default': () =>
    defaultQuestionsData({ questions: [textOptions()] }),
  'textarea-unrequired': () =>
    defaultQuestionsData({
      questions: [textOptions({ isRequired: 0 })],
    }),
  'textarea-min-100': () =>
    defaultQuestionsData({
      questions: [textOptions({ textFieldParam: { min: 100 } })],
    }),
  'input-default': () =>
    defaultQuestionsData({
      questions: [textOptions({ variantsType: TEXT_VARIANT_TYPES.INPUT })],
    }),
  'input-unrequired': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          isRequired: 0,
        }),
      ],
    }),
  'input-min-100': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          textFieldParam: { min: 100 },
        }),
      ],
    }),
  'input-mask-phone': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Phone,
        }),
      ],
    }),
  'input-mask-email': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Email,
        }),
      ],
    }),
  'input-mask-number': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Number,
        }),
      ],
    }),
  'input-mask-website': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Site,
        }),
      ],
    }),
  'input-mask-fio': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Name,
          maskConfig: {
            name: {
              visible: true,
              required: true,
              placeholderText: 'Имя',
              minLength: 0,
              maxLength: 50,
              value: '',
            },
            surname: {
              visible: true,
              required: true,
              placeholderText: 'Фамилия',
              minLength: 0,
              maxLength: 50,
              value: '',
            },
            patronymic: {
              visible: true,
              required: true,
              placeholderText: 'Отчество',
              minLength: 0,
              maxLength: 50,
              value: '',
            },
          },
        }),
      ],
    }),
  'input-mask-fio-complex': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Name,
          maskConfig: {
            name: {
              visible: false,
              required: true,
              placeholderText: 'Имя',
              minLength: 0,
              maxLength: 50,
              value: '',
            },
            surname: {
              visible: true,
              required: true,
              placeholderText: 'Фамилия',
              minLength: 5,
              maxLength: 100,
              value: '',
            },
            patronymic: {
              visible: true,
              required: false,
              placeholderText: 'Отчество',
              minLength: 2,
              maxLength: 30,
              value: '',
            },
          },
        }),
      ],
    }),
  'input-mask-date-required': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          placeholderText: '00.00.0000',
          maskType: MaskTypes.Date,
          isRequired: 1,
        }),
      ],
    }),

  'input-mask-date-not-required': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Date,
          placeholderText: '00.00.0000',
          isRequired: 0,
        }),
      ],
    }),

  'input-mask-range-required': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Period,
          placeholderText: '00.00.0000',
          isRequired: 1,
        }),
      ],
    }),

  'input-mask-range-not-required': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.Period,
          placeholderText: '00.00.0000',
          isRequired: 0,
        }),
      ],
    }),

  'input-mask-date-month-required': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.DateMonth,
          isRequired: 1,
        }),
      ],
    }),

  'input-mask-date-month-not-required': () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MaskTypes.DateMonth,
          isRequired: 0,
        }),
      ],
    }),
}

export default textMockData
