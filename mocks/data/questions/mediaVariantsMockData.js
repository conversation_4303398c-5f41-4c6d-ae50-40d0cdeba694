import { MEDIA_VARIANTS_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function mediaVariantsOptions(options = {}) {
  return merge(
    {
      type: MEDIA_VARIANTS_QUESTION,
      isRequired: 1,
      chooseType: 'image',
      variantsType: 0, // 0 - single, 1 - multiple
      max_choose_variants: 0,
      random_variants_order: 0,
      isHaveComment: false,
      comment_required: 0,
      gallery: [
        {
          id: 1,
          src: '/uploads/clouds.jpg',
          url: '/uploads/clouds.jpg',
          poster: '/uploads/clouds.jpg',
          description: 'Image 1',
        },
        {
          id: 2,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 2',
        },
        {
          id: 3,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 3',
        },
      ],
    },
    options,
  )
}

/**
 * Моковые данные для вопроса "Медиа варианты"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {object}
 */
const mediaVariantsMockData = {
  'media-variants-default': () => defaultQuestionsData({
    questions: [
      mediaVariantsOptions(),
    ],
  }),
  'media-variants-multiple': () => defaultQuestionsData({
    questions: [
      mediaVariantsOptions({
        variantsType: 1,
      }),
    ],
  }),
  'media-variants-max-choose-2': () => defaultQuestionsData({
    questions: [
      mediaVariantsOptions({
        variantsType: 1,
        max_choose_variants: 2,
      }),
    ],
  }),
  'media-variants-skip': () => defaultQuestionsData({
    questions: [
      mediaVariantsOptions({
        skip: 1,
      }),
    ],
  }),
}

export default mediaVariantsMockData
