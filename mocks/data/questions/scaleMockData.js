import { SCALE_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function scaleOptions(options = {}) {
  return merge(
    {
      description: 'Оцените по шкале',
      description_html: '<p>Оцените по шкале</p>',
      type: SCALE_QUESTION,
      id: 1,
      question_id: 128281,
      isRequired: 1,
      skip: 0,
      skip_text: 'Пропустить вопрос',
      set_variants: 0,
      skip_variant: 0,
      random_variants_order: 0,
      comment_enabled: 0,
      comment_required: 0,
      scaleRatingSetting: {
        start: 0,
        end: 10,
        step: 1,
      },
      textFieldParam: {
        min: 0,
        max: 1000,
      },
      placeholderText: 'Введите комментарий',
      comment_label: 'Ваш комментарий',
      detail_answers: [
        {
          id: 1,
          variant: 'Вариант 1',
          position: 1,
          is_deleted: 0,
        },
        {
          id: 2,
          variant: 'Вариант 2',
          position: 2,
          is_deleted: 0,
        },
        {
          id: 3,
          variant: 'Вариант 3',
          position: 3,
          is_deleted: 0,
        },
      ],
    },
    options,
  )
}

/**
 * Моковые данные для вопроса "Шкала"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {object}
 */
const scaleMockData = {
  'scale': () => defaultQuestionsData({
    questions: [
      scaleOptions(),
    ],
  }),
  'scale-with-skip': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        skip: 1,
      }),
    ],
  }),
  'scale-with-variants': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        set_variants: 1,
      }),
    ],
  }),
  'scale-with-skip-variant': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        skip: 1,
        skip_variant: 1,
      }),
    ],
  }),
  'scale-with-random-order': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        set_variants: 1,
        random_variants_order: 1,
      }),
    ],
  }),
  'scale-with-comment': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        comment_enabled: 1,
      }),
    ],
  }),
  'scale-with-required-comment': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        comment_enabled: 1,
        comment_required: 1,
      }),
    ],
  }),
  'scale-custom-range': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        scaleRatingSetting: {
          start: 10,
          end: 1000,
          step: 10,
        },
      }),
    ],
  }),
  'scale-with-complex-comment': () => defaultQuestionsData({
    questions: [
      scaleOptions({
        comment_enabled: 1,
        comment_required: 1,
        skip: 1,
        textFieldParam: {
          min: 10,
          max: 100,
        },
        placeholderText: 'Пожалуйста, оставьте комментарий',
      }),
    ],
  }),
}

export default scaleMockData
