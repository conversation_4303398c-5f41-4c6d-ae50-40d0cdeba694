import { DIFF_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function diffQuestionOptions(options = {}) {
  return merge(
    {
      id: 1,
      question_id: 100001,
      type: DIFF_QUESTION,
      isRequired: 1,
      description: 'Оцените следующие параметры',
      description_html: '<p>Оцените следующие параметры</p>',
      skip: 0,
      skip_text: 'Пропустить этот вопрос',
      skip_variant: 0,
      enableGallery: false,
      semDifSetting: {
        form: 'rect',
        start_point_color: '#73808D',
        end_point_color: '#3F65F1',
      },
      differentialRows: [
        {
          id: 1,
          start_label: 'Плохо',
          end_label: 'Отлично',
        },
        {
          id: 2,
          start_label: 'Медленно',
          end_label: 'Быстро',
        },
      ],
      comment_enabled: 0,
      comment_required: 0,
      comment_label: 'Ваш комментарий',
      placeholderText: 'Введите ваш комментарий здесь',
    },
    options,
  )
}

const diffMockData = {
  'diff-basic': () => defaultQuestionsData({
    questions: [diffQuestionOptions()],
  }),
  'diff-with-skip': () => defaultQuestionsData({
    questions: [diffQuestionOptions({ skip: 1 })],
  }),
  'diff-with-gallery': () => defaultQuestionsData({
    questions: [diffQuestionOptions({
      enableGallery: true,
      gallery: [
        {
          id: 1,
          src: '/uploads/clouds.jpg',
          url: '/uploads/clouds.jpg',
          poster: '/uploads/clouds.jpg',
          description: 'Image 1',
        },
        {
          id: 2,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 2',
        },
        {
          id: 3,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 3',
        },
      ],
    })],
  }),
  'diff-with-comment': () => defaultQuestionsData({
    questions: [diffQuestionOptions({ comment_enabled: 1, comment_required: 1 })],
  }),
  'diff-circle-form': () => defaultQuestionsData({
    questions: [diffQuestionOptions({
      semDifSetting: {
        form: 'circle',
        start_point_color: '#FF0000',
        end_point_color: '#00FF00',
      },
    })],
  }),
  'diff-english': () => defaultQuestionsData({
    questions: [diffQuestionOptions()],
    lang: {
      id: 17343,
      foquz_poll_id: 49242,
      poll_lang_id: 2,
      checked: 0,
      default: 1,
      back_text: '',
      next_text: '',
      finish_text: '',
      questions: {
        100001: {
          description: 'Rate the following parameters',
          description_html: '<p>Rate the following parameters</p>',
          skip_text: 'Skip this question',
          comment_label: 'Your comment',
          placeholderText: 'Enter your comment here',
          labels: JSON.stringify({
            1: ['Poor', 'Excellent'],
            2: ['Slow', 'Fast'],
          }),
        },
      },
    },
  }),
}

export default diffMockData
