import { CLASSIFIER_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function classifierQuestionOptions(options = {}) {
  return merge(
    {
      id: 1,
      question_id: 158668,
      type: CLASSIFIER_QUESTION,
      isRequired: 1,
      description: 'Выберите категорию и элемент',
      description_html: '<p>Выберите категорию и элемент</p>',
      dictionaryTree: {
        'Категория 1': {
          id: 161,
          title: 'Категория 1',
          description: 'Проверка ',
          position: 66,
          isCategory: true,
          children: {
            'Категория 11': {
              id: 162,
              title: 'Категория 11',
              description: '',
              position: 0,
              isCategory: true,
              children: {
                'Элемент 1': {
                  id: 163,
                  title: 'Элемент 1',
                  description: 'Проверка 2',
                  position: 0,
                  isCategory: false,
                },
                '222': {
                  id: 1876,
                  title: '222',
                  description: 'Проверка',
                  position: 1,
                  isCategory: false,
                },
              },
            },
          },
        },
        'Категория 2': {
          id: 164,
          title: 'Категория 2',
          description: 'Проверка ',
          position: 67,
          isCategory: true,
          children: {
            'Категория 21': {
              id: 165,
              title: 'Категория 21',
              description: '',
              position: 0,
              isCategory: true,
              children: {
                'Элемент 2': {
                  id: 166,
                  title: 'Элемент 2',
                  description: '',
                  position: 0,
                  isCategory: false,
                },
              },
            },
          },
        },
        'Элемент 3': {
          id: 167,
          title: 'Элемент 3',
          description: 'Проверка 444 4444 444 444',
          position: 181,
          isCategory: false,
        },
      },
      dictionary_sort: 'default',
      dictionary_list_type: 'tree',
      dropdownVariants: 0,
      assessmentVariantsType: 0,
      max_choose_variants: 1,
      disable_select_category: 0,
      show_tooltips: 1,
      skip: 0,
      skip_text: 'Пропустить этот вопрос',
      comment_enabled: 0,
      comment_required: 0,
      comment_label: 'Дополнительные комментарии',
      placeholderText: 'Введите ваши комментарии здесь',
    },
    options,
  )
}

const classifierMockData = {
  'classifier-basic': () => defaultQuestionsData({
    questions: [classifierQuestionOptions()],
  }),
  'classifier-multiple-choice': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({
      assessmentVariantsType: 1,
      max_choose_variants: 3,
    })],
  }),
  'classifier-with-skip': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ skip: 1 })],
  }),
  'classifier-list-type': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ dictionary_list_type: 'list' })],
  }),
  'classifier-list-type-multiple': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ dictionary_list_type: 'list', max_choose_variants: 3, assessmentVariantsType: 1 })],
  }),
  'classifier-with-dropdown': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ dropdownVariants: 1 })],
  }),
  'classifier-with-required-comment': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ comment_required: 1, comment_enabled: 1 })],
  }),
  'classifier-alphabetical-sort': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ dictionary_sort: 'alphabetter' })],
  }),
  'classifier-random-sort': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ dictionary_sort: 'random' })],
  }),
  'classifier-disable-select-category': () => defaultQuestionsData({
    questions: [classifierQuestionOptions({ disable_select_category: 1 })],
  }),
  'classifier-english': () => defaultQuestionsData({
    questions: [classifierQuestionOptions()],
    lang: {
      id: 17343,
      foquz_poll_id: 49242,
      poll_lang_id: 2,
      checked: 0,
      default: 1,
      back_text: 'Back',
      next_text: 'Next',
      finish_text: 'Finish',
      questions: {
        158668: {
          id: 13849,
          foquz_question_id: 158668,
          foquz_poll_lang_id: 17339,
          name: 'Classifier',
          description: '<p>Classifier / Tree</p>',
          description_html: '<p>Classifier / Tree</p>',
          sub_description: '',
          placeholder_text: '',
          select_placeholder_text: null,
          self_variant_placeholder_text: null,
          self_variant_description: null,
          comment_label: 'Your comment',
          skip_text: '',
          detail_question: null,
          self_variant_text: null,
          labels: null,
          text: null,
          detailLangs: {
            162: {
              id: 9310,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'Category 11',
              description: '',
              detail_id: 162,
              detail_type: 'category',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
            161: {
              id: 9309,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'Category 1',
              description: 'Examination',
              detail_id: 161,
              detail_type: 'category',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
            163: {
              id: 9313,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'element 1',
              description: 'Examination 2',
              detail_id: 163,
              detail_type: 'element',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
            165: {
              id: 9312,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'Category 21',
              description: '',
              detail_id: 165,
              detail_type: 'category',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
            164: {
              id: 9311,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'Category 2',
              description: 'Examination',
              detail_id: 164,
              detail_type: 'category',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
            166: {
              id: 9314,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'element 2',
              description: '',
              detail_id: 166,
              detail_type: 'element',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
            167: {
              id: 9315,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'element 3',
              description: 'Examination 444 4444 444 444',
              detail_id: 167,
              detail_type: 'element',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
            1876: {
              id: 9316,
              foquz_question_id: 158668,
              foquz_question_detail_id: null,
              foquz_poll_lang_id: 17339,
              question: 'element 222',
              description: 'Examination',
              detail_id: 1876,
              detail_type: 'element',
              detail_question: null,
              self_variant_text: null,
              self_variant_placeholder_text: null,
              langId: 2,
            },
          },
        },
      },
    },
  }),
}

export default classifierMockData
