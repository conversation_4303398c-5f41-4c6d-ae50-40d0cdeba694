import { ASSESMENT_VARIANT_TYPES, STAR_VARIANTS_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function starRatingVariantsOptions(options = {}) {
  return merge(
    {
      starRatingOptions: {
        count: 10,
        extra_question_rate_from: 1,
        extra_question_rate_to: 4,
      },
      textFieldParam: {
        min: 10,
        max: 50,
      },
      type: STAR_VARIANTS_QUESTION,
      answerText: '',
      extra_required: 0,
      variantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
      assessmentVariantsType: ASSESMENT_VARIANT_TYPES.SINGLE,
      isHaveCustomField: true,
      detail_answers: [
        // ряд обычных вариантов
        {
          id: 1,
          variant: 'Вариант 1',
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
        },
        {
          id: 2,
          variant: 'Вариант 2',
          is_deleted: 0,
          extra_question: 0,
          need_extra: 1,
        },
        {
          id: 3,
          variant: 'Вариант 3',
          is_deleted: 0,
          extra_question: 0,
          need_extra: 0,
        },
        // ряд дополнительных вариантов
        {
          id: 2,
          variant: 'УВ: вариант 1',
          extra_question: 1,
          is_deleted: 0,
        },
        {
          id: 3,
          variant: 'УВ: вариант 2',
          extra_question: 1,
          is_deleted: 0,
        },
        {
          id: 4,
          variant: 'УВ: вариант 3',
          extra_question: 1,
          is_deleted: 0,
        },
      ],
    },
    options,
  )
}

/**
 * Моковые данные для вопроса "Звездный рейтинг для вариантов"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {object}
 */
const starRatingVariantsMockData = {
  'star-rating-variants': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions(),
    ],
  }),
  'star-rating-variants-unrequired': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions({
        isRequired: 0,
      }),
    ],
  }),
  'star-rating-variants-skipped': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions({
        skip: 1,
      }),
    ],
  }),
  'star-rating-variants-skip-variant': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions({
        skip: 1,
        skip_variant: 1,
      }),
    ],
  }),
  'star-rating-variants-assessments': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions({
        extra_required: 1,
        answerText: 'Почему такая низкая оценка?',
      }),
    ],
  }),
  'star-rating-variants-assessments-text': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions({
        extra_required: 1,
        answerText: 'Почему такая низкая оценка?',
        assessmentVariantsType: ASSESMENT_VARIANT_TYPES.TEXT,
        variantsType: ASSESMENT_VARIANT_TYPES.TEXT,
      }),
    ],
  }),
  'star-rating-variants-assessments-multiple': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions({
        extra_required: 1,
        isHaveCustomField: 1,
        answerText: 'Почему такая низкая оценка?',
        variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
      }),
    ],
  }),
  'star-rating-variants-assessments-skip-variant': () => defaultQuestionsData({
    questions: [
      starRatingVariantsOptions({
        extra_required: 1,
        isHaveCustomField: 1,
        answerText: 'Почему такая низкая оценка?',
        variantsType: ASSESMENT_VARIANT_TYPES.MULTIPLE,
        skip: 1,
        skip_variant: 1,
      }),
    ],
  }),
}

export default starRatingVariantsMockData
