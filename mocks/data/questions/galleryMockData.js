import { GALLERY_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'

export function galleryOptions(options = {}) {
  return merge(
    {
      type: GALLERY_QUESTION,
      isRequired: 1,
      chooseType: 'image',
      isHaveComment: false,
      comment_required: 0,
      gallery: [
        {
          id: 1,
          src: '/uploads/clouds.jpg',
          url: '/uploads/clouds.jpg',
          poster: '/uploads/clouds.jpg',
          description: 'Image 1',
        },
        {
          id: 2,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 2',
        },
        {
          id: 3,
          src: '/uploads/summer.jpg',
          url: '/uploads/summer.jpg',
          poster: '/uploads/summer.jpg',
          description: 'Image 3',
        },
      ],
    },
    options,
  )
}

/**
 * Моковые данные для вопроса "Галерея с рейтингом"
 * ключ – название ключа виджета, значение – функция, возвращающая данные для вопроса
 * @type {object}
 */
const galleryMockData = {
  'gallery-rating-default': () => defaultQuestionsData({
    questions: [
      galleryOptions(),
    ],
  }),
  'gallery-rating-skip': () => defaultQuestionsData({
    questions: [
      galleryOptions({
        skip: 1,
      }),
    ],
  }),
  'gallery-rating-random-order': () => defaultQuestionsData({
    questions: [
      galleryOptions({
        random_variants_order: 1,
      }),
    ],
  }),
  'gallery-rating-with-comment': () => defaultQuestionsData({
    questions: [
      galleryOptions({
        type: 'rating',
        comment_enabled: 1,
        comment_required: 1,
      }),
    ],
  }),
}

export default galleryMockData
