import { NPS_QUESTION } from '@entities/question/model/types'
import { EXTRA_QUESTION_TYPE } from '@shared/constants'
import defaultQuestionsData from '../defaultQuestionsData'

// Test Case 2: extra_question_type = SINGLE (Multi-variant NPS, single extra question set)
export const dataSingle = {
  id: 1,
  description_html: '<p>Вопрос NPS</p>',
  type: NPS_QUESTION,
  set_variants: 1,
  isRequired: 1,
  random_variants_order: 0,
  extra_question_type: EXTRA_QUESTION_TYPE.SINGLE,
  detail_answers: [
    { id: 1, variant: 'Вариант 1', position: 0, is_deleted: 0, extra_question: 0, need_extra: 1 },
    { id: 2, variant: 'Вариант 2', position: 1, is_deleted: 0, extra_question: 0, need_extra: 1 },
    { id: 3, variant: 'Дополнительный вариант А', position: 0, is_deleted: 0, extra_question: 1, need_extra: 0 },
    { id: 4, variant: 'Дополнительный вариант Б', position: 1, is_deleted: 0, extra_question: 1, need_extra: 0 },
  ],
  variantsType: 0, // Single choice
  textFieldParam: { min: 0, max: 100 },
  placeholderText: 'Введите ваш ответ',
  extra_question_rate_from: 6,
  extra_question_rate_to: 10,
  extra_required: 1,
  npsRatingSetting: {
    design: 1,
    start_point_color: '#FF0000',
    end_point_color: '#00FF00',
    start_label: 'Маловероятно',
    end_label: 'Очень вероятно',
  },
  comment_enabled: 0,
  enableGallery: 0,
}

// Test Case 3: extra_question_type = COMMON_PER_VARIANT (Multi-variant NPS, common extra variants)
export const dataCommonPerVariant = {
  id: 1,
  description_html: '<p>Вопрос NPS</p>',
  type: NPS_QUESTION,
  set_variants: 1,
  isRequired: 1,
  random_variants_order: 0,
  extra_question_type: EXTRA_QUESTION_TYPE.COMMON_PER_VARIANT,
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 5,
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 6,
      extra_question_rate_to: 10,
    },
    { id: 3, variant: 'Дополнительный вариант А', position: 0, is_deleted: 0, extra_question: 1 },
    { id: 4, variant: 'Дополнительный вариант Б', position: 1, is_deleted: 0, extra_question: 1 },
  ],
  variantsType: 1, // Multiple choice
  npsRatingSetting: {
    design: 1,
    start_point_color: '#FF0000',
    end_point_color: '#00FF00',
    start_label: 'Маловероятно',
    end_label: 'Очень вероятно',
  },
  comment_enabled: 0,
  enableGallery: 0,
  extra_question_rate_from: 6,
  extra_question_rate_to: 10,
}

// Test Case 2: Single Choice NPS with Single Extra Question Set
export const dataSingleChoice = {
  id: 1,
  description_html: '<p>Вопрос NPS</p>',
  type: NPS_QUESTION,
  set_variants: 1,
  isRequired: 1,
  random_variants_order: 0,
  extra_question_type: EXTRA_QUESTION_TYPE.SINGLE,
  answerText: 'Пожалуйста, объясните вашу оценку',
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 5,
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 6,
      extra_question_rate_to: 10,
    },
    {
      id: 3,
      variant: 'Дополнительный вариант А',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      need_extra: 0,
      self_variant_text: 'Другое (пожалуйста, уточните)',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 10,
      self_variant_maxlength: 500,
      comment_required: 1,
    },
    {
      id: 4,
      variant: 'Дополнительный вариант Б',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      need_extra: 0,
      self_variant_text: 'Свой ответ',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 5,
      self_variant_maxlength: 250,
      comment_required: 1,
    },
  ],
  variantsType: 0, // Single choice
  textFieldParam: { min: 0, max: 100 },
  placeholderText: 'Введите ваш ответ',
  extra_question_rate_from: 6,
  extra_question_rate_to: 10,
  extra_required: 1,
  self_variant_text: 'Другая причина',
  self_variant_nothing: 0,
  self_variant_comment_required: 1,
  isHaveCustomField: 1,
  self_variant_file: {
    file_id: 401,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
  npsRatingSetting: {
    design: 1,
    start_point_color: '#FF0000',
    end_point_color: '#00FF00',
    start_label: 'Маловероятно',
    end_label: 'Очень вероятно',
  },
  comment_enabled: 0,
  enableGallery: 0,
}

// Multiple Choice variant
export const dataSingleMultipleChoice = {
  ...dataSingleChoice,
  variantsType: 1, // Multiple choice
}

// Text Input variant
export const dataSingleText = {
  ...dataSingleChoice,
  variantsType: 2, // Text input
}

// Single Choice with Files
export const dataSingleChoiceWithFiles = {
  ...dataSingleChoice,
  variants_with_files: 1,
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      file_id: 101,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
    {
      id: 3,
      variant: 'Дополнительный вариант А',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      need_extra: 0,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 4,
      variant: 'Дополнительный вариант Б',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      need_extra: 0,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
  ],
  self_variant_file: {
    file_id: 105,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
}

// Multiple Choice with Files
export const dataSingleMultipleChoiceWithFiles = {
  ...dataSingleChoiceWithFiles,
  variantsType: 1, // Multiple choice
}

// Text Input with Files
export const dataSingleTextWithFiles = {
  ...dataSingleChoiceWithFiles,
  variantsType: 2, // Text input
  self_variant_file: {
    file_id: 106,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
}

// Test Case 3: Common Per Variant - Single Choice
export const dataCommonPerVariantSingleChoice = {
  id: 1,
  description_html: '<p>Вопрос NPS</p>',
  type: NPS_QUESTION,
  set_variants: 1,
  isRequired: 1,
  random_variants_order: 0,
  extra_question_type: EXTRA_QUESTION_TYPE.COMMON_PER_VARIANT,
  answerText: 'Пожалуйста, объясните вашу оценку',
  isHaveCustomField: 1,
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 10,
      self_variant_text: 'Другое (Вариант 1)',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 10,
      self_variant_maxlength: 300,
      comment_required: 1,
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 10,
      self_variant_text: 'Другое (Вариант 2)',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 15,
      self_variant_maxlength: 400,
      comment_required: 1,
    },
    {
      id: 3,
      variant: 'Дополнительный вариант А',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      self_variant_text: 'Другая причина А',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 5,
      self_variant_maxlength: 200,
      comment_required: 1,
    },
    {
      id: 4,
      variant: 'Дополнительный вариант Б',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      self_variant_text: 'Другая причина Б',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 8,
      self_variant_maxlength: 250,
      comment_required: 1,
    },
  ],
  variantsType: 0, // Single choice
  self_variant_text: 'Другой отзыв',
  self_variant_nothing: 0,
  self_variant_comment_required: 1,
  extra_question_rate_from: 0,
  extra_question_rate_to: 10,
  self_variant_file: {
    id: 402,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
  npsRatingSetting: {
    design: 1,
    start_point_color: '#FF0000',
    end_point_color: '#00FF00',
    start_label: 'Маловероятно',
    end_label: 'Очень вероятно',
  },
  comment_enabled: 0,
  enableGallery: 0,
}

// Multiple Choice variant
export const dataCommonPerVariantMultipleChoice = {
  ...dataCommonPerVariantSingleChoice,
  variantsType: 1, // Multiple choice
}

// Text Input variant
export const dataCommonPerVariantText = {
  ...dataCommonPerVariantSingleChoice,
  variantsType: 2, // Text input
}

// Single Choice with Files
export const dataCommonPerVariantSingleChoiceWithFiles = {
  ...dataCommonPerVariantSingleChoice,
  variants_with_files: 1,
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 5,
      file_id: 201,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 6,
      extra_question_rate_to: 10,
      file_id: 202,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
    {
      id: 3,
      variant: 'Дополнительный вариант А',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      file_id: 203,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 4,
      variant: 'Дополнительный вариант Б',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      file_id: 204,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
  ],
  self_variant_file: {
    id: 205,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
}

// Multiple Choice with Files
export const dataCommonPerVariantMultipleChoiceWithFiles = {
  ...dataCommonPerVariantSingleChoiceWithFiles,
  variantsType: 1, // Multiple choice
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 0, 
      extra_question_rate_to: 10, // Expanded range to include 5
      file_id: 201,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_question_rate_from: 6,
      extra_question_rate_to: 10,
      file_id: 202,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
    {
      id: 3,
      variant: 'Дополнительный вариант А',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      file_id: 203,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 4,
      variant: 'Дополнительный вариант Б',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      file_id: 204,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
  ],
}

// Text Input with Files
export const dataCommonPerVariantTextWithFiles = {
  ...dataCommonPerVariantSingleChoiceWithFiles,
  variantsType: 2, // Text input
  self_variant_file: {
    id: 206,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
}

// Test Case 4: Different Per Variant
export const dataDifferentPerVariant = {
  id: 1,
  description_html: '<p>Вопрос NPS</p>',
  type: NPS_QUESTION,
  set_variants: 1,
  isRequired: 1,
  random_variants_order: 0,
  extra_question_type: EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT,
  answerText: 'Пожалуйста, объясните вашу оценку',
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_required: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 6,
      variants_element_type: 0, // Single choice
      self_variant_text: 'Другое (Вариант 1)',
      is_self_answer: 1,
      detail_question: 'Пожалуйста, объясните вашу оценку для Варианта 1',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 20,
      self_variant_maxlength: 600,
      comment_required: 1,
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_required: 1,
      extra_question_rate_from: 6,
      extra_question_rate_to: 10,
      variants_element_type: 1, // Multiple choice
      self_variant_text: 'Другое (Вариант 2)',
      is_self_answer: 1,
      detail_question: 'Пожалуйста, объясните вашу оценку для Варианта 2',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 25,
      self_variant_maxlength: 700,
      comment_required: 1,
    },
    {
      id: 3,
      variant: 'Вариант 3',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      extra_required: 1,
      question_detail_id: 1,
      detail_question: 'Пожалуйста, объясните вашу оценку для Варианта 3',
      is_self_answer: 1,
      self_variant_text: 'Другая причина А1',
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 10,
      self_variant_maxlength: 300,
      extra_question_rate_from: 0,
      extra_question_rate_to: 5,
      variants_element_type: 2, // Text input
      clarifyingQuestion: 'Пожалуйста, уточните ваш выбор для Варианта 1',
      comment_required: 1,
    },
    {
      id: 4,
      variant: 'Дополнительный вариант Б для Варианта 1',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 1,
      self_variant_text: 'Другая причина Б1',
      is_self_answer: 1,
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 15,
      self_variant_maxlength: 350,
      comment_required: 1,
      clarifyingQuestion: 'Не могли бы вы предоставить более подробную информацию о вашем выборе?',
    },
    {
      id: 5,
      variant: 'Дополнительный вариант В для Варианта 2',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 2,
      self_variant_text: 'Другая причина В2',
      is_self_answer: 1,
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 12,
      self_variant_maxlength: 400,
      comment_required: 1,
      clarifyingQuestion: 'Пожалуйста, объясните ваш выбор для Варианта 2',
    },
    {
      id: 6,
      variant: 'Дополнительный вариант Г для Варианта 2',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 2,
      self_variant_text: 'Другая причина Г2',
      is_self_answer: 1,
      self_variant_nothing: 0,
      self_variant_comment_required: 1,
      self_variant_minlength: 18,
      self_variant_maxlength: 450,
      comment_required: 1,
      clarifyingQuestion: 'Какую дополнительную информацию вы можете предоставить о своем выборе?',
    },
  ],
  variantsType: 0, // Single choice
  self_variant_text: 'Другой подробный отзыв',
  self_variant_nothing: 0,
  self_variant_comment_required: 1,
  self_variant_file: {
    id: 403,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
  npsRatingSetting: {
    design: 1,
    start_point_color: '#FF0000',
    end_point_color: '#00FF00',
    start_label: 'Маловероятно',
    end_label: 'Очень вероятно',
  },
  comment_enabled: 0,
  enableGallery: 0,
}

// Single Choice with Files
export const dataDifferentPerVariantWithFiles = {
  ...dataDifferentPerVariant,
  variants_with_files: 1,
  detail_answers: [
    {
      id: 1,
      variant: 'Вариант 1',
      position: 0,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      is_self_answer: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 10,
      variants_element_type: 0, // Single choice
      variants_with_files: 1,
    },
    {
      id: 2,
      variant: 'Вариант 2',
      position: 1,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      is_self_answer: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 10,
      variants_element_type: 1,
      variants_with_files: 1,
    },
    {
      id: 3,
      variant: 'Вариант 3',
      position: 2,
      is_deleted: 0,
      extra_question: 0,
      need_extra: 1,
      is_self_answer: 1,
      extra_required: 1,
      extra_question_rate_from: 0,
      extra_question_rate_to: 10, // Expanded range to include 2
      variants_element_type: 2,
      variants_with_files: 1,
    },
    {
      id: 4,
      variant: 'Дополнительный вариант А для Варианта 1',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 1,
      file_id: 303,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 5,
      variant: 'Дополнительный вариант Б для Варианта 1',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 1,
      file_id: 304,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
    {
      id: 6,
      variant: 'Дополнительный вариант В для Варианта 2',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 2,
      file_id: 305,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
    {
      id: 7,
      variant: 'Дополнительный вариант Г для Варианта 2',
      position: 1,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 2,
      file_id: 306,
      file_url: '/uploads/summer.jpg',
      preview_url: '/uploads/summer.jpg',
    },
    {
      id: 8,
      variant: 'Дополнительный вариант Д для Варианта 3',
      position: 0,
      is_deleted: 0,
      extra_question: 1,
      question_detail_id: 3,
      file_id: 307,
      file_url: '/uploads/clouds.jpg',
      preview_url: '/uploads/clouds.jpg',
    },
  ],
  self_variant_file: {
    id: 307,
    file_url: '/uploads/clouds.jpg',
    preview_url: '/uploads/clouds.jpg',
  },
}

export default {
  // Single Extra Question Set
  'nps-extra-questions-single-choice': () => defaultQuestionsData({ questions: [dataSingleChoice] }),
  'nps-extra-questions-single-multiple-choice': () => defaultQuestionsData({ questions: [dataSingleMultipleChoice] }),
  'nps-extra-questions-single-text': () => defaultQuestionsData({ questions: [dataSingleText] }),
  'nps-extra-questions-single-choice-with-files': () => defaultQuestionsData({ questions: [dataSingleChoiceWithFiles] }),
  'nps-extra-questions-single-multiple-choice-with-files': () => defaultQuestionsData({ questions: [dataSingleMultipleChoiceWithFiles] }),

  // Common Per Variant
  'nps-extra-questions-common-per-variant-single-choice': () => defaultQuestionsData({ questions: [dataCommonPerVariantSingleChoice] }),
  'nps-extra-questions-common-per-variant-multiple-choice': () => defaultQuestionsData({ questions: [dataCommonPerVariantMultipleChoice] }),
  'nps-extra-questions-common-per-variant-text': () => defaultQuestionsData({ questions: [dataCommonPerVariantText] }),
  'nps-extra-questions-common-per-variant-single-choice-with-files': () => defaultQuestionsData({ questions: [dataCommonPerVariantSingleChoiceWithFiles] }),
  'nps-extra-questions-common-per-variant-multiple-choice-with-files': () => defaultQuestionsData({ questions: [dataCommonPerVariantMultipleChoiceWithFiles] }),

  // Different Per Variant
  'nps-extra-questions-different-per-variant': () => defaultQuestionsData({ questions: [dataDifferentPerVariant] }),
  'nps-extra-questions-different-per-variant-with-files': () => defaultQuestionsData({ questions: [dataDifferentPerVariantWithFiles] }),
}
