import merge from "lodash.merge";
import { QUESTION_TYPES } from "../../../src/helpers";
import {
  MASK_TYPES,
  TEXT_VARIANT_TYPES,
} from "../../../src/helpers/question-helpers/questionTypes";
import defaultQuestionsData from "./defaultQuestionData";

export const textOptions = (data = {}) =>
  merge(
    {
      description: "Текстовый ответ",
      description_html: "<p>Текстовый ответ<p>",
      type: QUESTION_TYPES.TEXT,
      variantsType: TEXT_VARIANT_TYPES.TEXTAREA,
      isRequired: 1,
      textFieldParam: {
        min: 0,
        max: 700,
      },
      placeholderText: "Введите текст",
      maskType: MASK_TYPES.NONE,
      //   maskConfig: {
      //     name: {
      //       visible: false,
      //       required: false,
      //       placeholderText: "Имя",
      //       minLength: 0,
      //       maxLength: 50,
      //       value: "",
      //     },
      //     surname: {
      //       visible: false,
      //       required: false,
      //       placeholderText: "Фамилия",
      //       minLength: 0,
      //       maxLength: 50,
      //       value: "",
      //     },
      //     patronymic: {
      //       visible: false,
      //       required: false,
      //       placeholderText: "Отчество",
      //       minLength: 0,
      //       maxLength: 50,
      //       value: "",
      //     },
      //   },
    },
    data,
  );

const textMockData = {
  "textarea-default": () =>
    defaultQuestionsData({ questions: [textOptions()] }),
  "textarea-unrequired": () =>
    defaultQuestionsData({
      questions: [textOptions({ isRequired: 0 })],
    }),
  "textarea-min-100": () =>
    defaultQuestionsData({
      questions: [textOptions({ textFieldParam: { min: 100 } })],
    }),
  "input-default": () =>
    defaultQuestionsData({
      questions: [textOptions({ variantsType: TEXT_VARIANT_TYPES.INPUT })],
    }),
  "input-unrequired": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          isRequired: 0,
        }),
      ],
    }),
  "input-min-100": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          textFieldParam: { min: 100 },
        }),
      ],
    }),
  "input-mask-phone": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MASK_TYPES.PHONE,
        }),
      ],
    }),
  "input-mask-email": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MASK_TYPES.EMAIL,
        }),
      ],
    }),
  "input-mask-number": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MASK_TYPES.NUMBER,
        }),
      ],
    }),
  "input-mask-website": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MASK_TYPES.WEBSITE,
        }),
      ],
    }),
  "input-mask-fio": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MASK_TYPES.FIO,
          maskConfig: {
            name: {
              visible: true,
              required: true,
              placeholderText: "Имя",
              minLength: 0,
              maxLength: 50,
              value: "",
            },
            surname: {
              visible: true,
              required: true,
              placeholderText: "Фамилия",
              minLength: 0,
              maxLength: 50,
              value: "",
            },
            patronymic: {
              visible: true,
              required: true,
              placeholderText: "Отчество",
              minLength: 0,
              maxLength: 50,
              value: "",
            },
          },
        }),
      ],
    }),
  "input-mask-fio-complex": () =>
    defaultQuestionsData({
      questions: [
        textOptions({
          variantsType: TEXT_VARIANT_TYPES.INPUT,
          maskType: MASK_TYPES.FIO,
          maskConfig: {
            name: {
              visible: false,
              required: true,
              placeholderText: "Имя",
              minLength: 0,
              maxLength: 50,
              value: "",
            },
            surname: {
              visible: true,
              required: true,
              placeholderText: "Фамилия",
              minLength: 5,
              maxLength: 100,
              value: "",
            },
            patronymic: {
              visible: true,
              required: false,
              placeholderText: "Отчество",
              minLength: 2,
              maxLength: 30,
              value: "",
            },
          },
        }),
      ],
    }),
};

export default textMockData;
