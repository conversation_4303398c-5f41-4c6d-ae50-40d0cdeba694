import { INTER_BLOCK, INTERMEDIATE_BLOCK_TYPES, RATING_QUESTION, STARS_QUESTION } from '@entities/question/model/types'
import merge from 'lodash.merge'
import defaultQuestionsData from '../defaultQuestionsData'
import { ratingScaleOptions } from './ratingScaleMockData'

const intrmediateScreenText = '<p style="text-align:center;"><span style="font-size:36px;"><strong>Море</strong></span></p><p style="text-align:center;">&nbsp;</p><ul><li><span style="font-size:18px;">Мое ФИО: {ФИО}</span></li><li><span style="font-size:18px;">Мой промокод: </span>{Промокод}</li></ul><p>&nbsp;</p><p style="text-align:center;">Держите промокод: {Промокод с копированием}</p><p style="text-align:center;"><br><span style="font-size:18px;"><i><s><strong><u>Большинство курортных отелей острова расположены на&nbsp;его северном побережье, выходящем на&nbsp;Атлантический океан.</u></strong></s></i>&nbsp;</span></p><p>&nbsp;</p><p style="text-align:right;"><a href="https://travelata.ru/blog/wp-content/uploads/2019/07/photo-1549039288-e126fc6ad126.jpg"><span style="font-size:18px;">Южное побережье Кубы</span></a><span style="font-size:18px;">, омываемое Карибским морем, не&nbsp;столь популярно, да&nbsp;и&nbsp;добираться до&nbsp;него сложнее. Но&nbsp;именно здесь находятся лучшие пляжи Острова Свободы, лучшие места для&nbsp;рыбалки и&nbsp;самые интересные дайв-сайты.</span></p><p><br>{Промокод}<br>&nbsp;</p><h3>10 интересных фактов о&nbsp;Кубе</h3><ol><li>Куба – самый большой по&nbsp;площади остров Карибского бассейна и&nbsp;входит в&nbsp;20-ку самых крупных островов мира.</li><li>Несмотря на&nbsp;многочисленные ограничения свобод, например, по&nbsp;выходу в&nbsp;интернет, на&nbsp;Кубе живут одни из&nbsp;самых грамотных людей в&nbsp;мире – 99,8% кубинцев признаны грамотными. И, похоже, одни из&nbsp;самых счастливых, ведь живут на&nbsp;острове Свободы в&nbsp;среднем до&nbsp;77,4 лет.</li><li>На Кубе живет и&nbsp;самая маленькая птица мира – пчелиный колибри. Рассмотреть ее в&nbsp;зарослях будет очень сложно, ведь она размером со&nbsp;спичечный коробок и&nbsp;весит всего 1,6 грамм.</li><li>Самая низкая среднемесячная температура в&nbsp;январе на&nbsp;Кубе +23 градуса. А&nbsp;самая высокая – в&nbsp;июле, +27. так что&nbsp;климат на&nbsp;Кубе превосходный: вечное мягкое лето.</li><li>Два самых известных здания кубинской столицы</li></ol>'
const startScreenText = '<p>Количество пирокластического материала имеет тенденцию морской аллит, что увязывается со структурно-тектонической обстановкой, гидродинамическими условиями и литолого-минералогическим составом пород. </p> <br><p>Промокод с копированием: {Промокод с копированием}</p><p>Выбор языка: {Выбор языка}</p>'

export function intermediateBlockOptions(data = {}) {
  return merge(
    {
      description: 'Промежуточный блок',
      description_html: '<p>Промежуточный блок<p>',
      type: INTER_BLOCK,
      id: 1,
      question_id: 123,
      answer: null,
      intermediateBlock: {
        screen_type: INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE,
        text: intrmediateScreenText,
        agreement: 1,
        agreement_text: 'Я согласен/согласна на обработку персональных данных',
        logos_backcolor: '#f0f0f0',
      },
      endScreenImages: [],
    },
    data,
  )
}

const defaultAnswerStartBlockData = {
  status: 'open',
}

const defaultAnswerEndBlockData = {
  status: 'done',
}

const intermediateBlockMockData = {
  'intermediate-block-default': () =>
    defaultQuestionsData({
      answer: defaultAnswerStartBlockData,
      questions: [
        intermediateBlockOptions({
          endScreenImages: [
            {
              logo: '/uploads/summer.jpg',
              description: 'Logo 1',
              description_html: '<p>Logo 1<p>',
              position: 1,
              width: 133,
              height: 200,
              link: 'https://example.com/link1',
            },
            {
              logo: '/uploads/summer.jpg',
              description: 'Logo 2',
              description_html: '<p>Logo 2<p>',
              position: 2,
              width: 133,
              height: 200,
            },
            {
              logo: '/uploads/summer.jpg',
              external_logo: null,
              description: 'Logo 3',
              description_html: '<p>Logo 3<p>',
              position: 0,
              width: 133,
              height: 200,
            },
          ],
        }),
      ],
      variables: {
        fio: 'Иван Иванов',
        codes: {
          123: 'PROMO123',
        },
      },
    }),
  'intermediate-block-start': () =>
    defaultQuestionsData({
      answer: defaultAnswerStartBlockData,
      variables: {
        fio: 'Иван Иванов',
        codes: {
          123: 'PROMO123',
        },
      },
      questions: [
        intermediateBlockOptions({
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.START,
            text: startScreenText,
            agreement: 1,
          },
          endScreenImages: [
            {
              logo: '/uploads/city.jpg',
              description: 'Logo 1',
              description_html: '<p>Logo 1<p>',
              position: 1,
              width: 302,
              height: 218,
              link: 'https://example.com/link1',
            },
            {
              logo: '/uploads/city.jpg',
              description: 'Logo 2',
              description_html: '<p>Logo 2<p>',
              position: 2,
              width: 174,
              height: 120,
            },
          ],
        }),
        {
          id: 2,
          question_id: 3939,
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'intermediate-block-start-small-text': () =>
    defaultQuestionsData({
      answer: defaultAnswerStartBlockData,
      questions: [
        intermediateBlockOptions({
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.START,
            text: '<p>Маленький текст для начала.</p>',
            agreement: 0,
          },
          endScreenImages: [],
        }),
        {
          description: 'Оцените наш сервис',
          description_html: '<p>Оцените наш сервис<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'intermediate-block-end': () =>
    defaultQuestionsData({
      question_id: '123',
      answer: defaultAnswerEndBlockData,
      questions: [
        intermediateBlockOptions({
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.END,
            text: '<p style="text-align:center;"><span style="font-size:36px;"><strong>Конечный блок</strong></span></p><p style="text-align:center;">&nbsp;</p><ul><li><span style="font-size:18px;">Мое ФИО: {ФИО}</span></li><li><span style="font-size:18px;">Мой промокод: </span>{Промокод}</li></ul><p>&nbsp;</p><p style="text-align:center;">Держите промокод:</p><p style="text-align:center;">&nbsp;</p><p style="text-align:center;">{Промокод с копированием}</p><p style="text-align:center;"><br><span style="font-size:18px;"><i><s><strong><u>Большинство курортных отелей острова расположены на&nbsp;его северном побережье, выходящем на&nbsp;Атлантический океан.</u></strong></s></i>&nbsp;</span></p><p>&nbsp;</p><p style="text-align:right;"><a href="https://travelata.ru/blog/wp-content/uploads/2019/07/photo-1549039288-e126fc6ad126.jpg"><span style="font-size:18px;">Южное побережье Кубы</span></a><span style="font-size:18px;">, омываемое Карибским морем, не&nbsp;столь популярно, да&nbsp;и&nbsp;добираться до&nbsp;него сложнее. Но&nbsp;именно здесь находятся лучшие пляжи Острова Свободы, лучшие места для&nbsp;рыбалки и&nbsp;самые интересные дайв-сайты.</span></p><p><br>{Промокод}<br>&nbsp;</p><h3>10 интересных фактов о&nbsp;Кубе</h3><ol><li>Куба – самый большой по&nbsp;площади остров Карибского бассейна и&nbsp;входит в&nbsp;20-ку самых крупных островов мира.</li><li>Несмотря на&nbsp;многочисленные ограничения свобод, например, по&nbsp;выходу в&nbsp;интернет, на&nbsp;Кубе живут одни из&nbsp;самых грамотных людей в&nbsp;мире – 99,8% кубинцев признаны грамотными. И, похоже, одни из&nbsp;самых счастливых, ведь живут на&nbsp;острове Свободы в&nbsp;среднем до&nbsp;77,4 лет.</li><li>На Кубе живет и&nbsp;самая маленькая птица мира – пчелиный колибри. Рассмотреть ее в&nbsp;зарослях будет очень сложно, ведь она размером со&nbsp;спичечный коробок и&nbsp;весит всего 1,6 грамм.</li><li>Самая низкая среднемесячная температура в&nbsp;январе на&nbsp;Кубе +23 градуса. А&nbsp;самая высокая – в&nbsp;июле, +27. так что&nbsp;климат на&nbsp;Кубе превосходный: вечное мягкое лето.</li><li>Два самых известных здания кубинской столицы</li></ol>',
            agreement: 0,
            ready_button: 1,
            external_link: 'https://example.com/external-link',
          },
        }),
      ],
      variables: {
        fio: 'Петр Петров',
        codes: {
          123: 'ENDPROMO456',
        },
      },
    }),
  'intermediate-block-end-small-text': () =>
    defaultQuestionsData({
      answer: defaultAnswerEndBlockData,
      questions: [
        intermediateBlockOptions({
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.END,
            text: '<p>Маленький текст для конца.</p>',
            agreement: 0,
            ready_button: 1,
            external_link: 'https://example.com/external-link',
          },
        }),
      ],
      variables: {
        fio: 'Петр Петров',
        codes: {
          [intermediateBlockOptions().id]: 'ENDPROMO456',
        },
      },
    }),
  'intermediate-block-end-close-widget-button': () =>
    defaultQuestionsData({
      answer: defaultAnswerEndBlockData,
      questions: [
        intermediateBlockOptions({
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.END,
            text: '<p>Маленький текст для конца.</p>',
            agreement: 0,
            close_widget_button: 1,
          },
        }),
      ],
    }),
  'intermediate-block-end-close-widget-button-custom-text': () =>
    defaultQuestionsData({
      answer: defaultAnswerEndBlockData,
      questions: [
        intermediateBlockOptions({
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.END,
            text: '<p>Маленький текст для конца.</p>',
            agreement: 0,
            close_widget_button: 1,
            close_widget_button_text: 'Закроем?',
          },
        }),
      ],
    }),
  'intermediate-block-in-the-middle': () =>
    defaultQuestionsData({
      answer: defaultAnswerStartBlockData,
      questions: [
        {
          description: 'Оцените наш сервис (звезды)',
          description_html: '<p>Оцените наш сервис (звезды)<p>',
          id: 1,
          type: STARS_QUESTION,
          starRatingOptions: {
            count: 5,
          },
        },
        intermediateBlockOptions({
          id: 2,
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.END,
            text: '<p>Промежуточный блок в конце.</p>',
            agreement: 0,
            ready_button: 1,
            external_link: 'https://example.com/external-link',
          },
        }),
        {
          id: 3,
          description: 'Оцените наш сервис (рейтинг)',
          description_html: '<p>Оцените наш сервис (рейтинг)<p>',
          type: RATING_QUESTION,
          starRatingOptions: ratingScaleOptions(),
        },
      ],
    }),
  'intermediate-block-agreement-html': () =>
    defaultQuestionsData({
      answer: defaultAnswerStartBlockData,
      questions: [
        intermediateBlockOptions({
          intermediateBlock: {
            screen_type: INTERMEDIATE_BLOCK_TYPES.END,
            text: '<p>Промежуточный блок в конце.</p>',
            agreement: 1,
            external_link: 'https://example.com/external-link',
            agreement_text:
              '<p>Я согласен/согласна на <a href=\'http://google.com\'>обработку персональных данных</a></p>',
          },
        }),
      ],
    }),
}

export default intermediateBlockMockData
