import {
  FILIALS_QUESTION,
  INTERMEDIATE_BLOCK_TYPES,
  NPS_QUESTION,
  RATING_QUESTION,
  SCALE_QUESTION,
  SMILE_QUESTION,
  STAR_VARIANTS_QUESTION,
  STARS_QUESTION,
} from '@entities/question/model/types'
import { VIEW_LOGIC } from '@shared/constants/logic'
import defaultQuestionsData from '../defaultQuestionsData'
import { classifierQuestionOptions } from './classifierMockData'
import { intermediateBlockOptions } from './interBlockMockData'
import { ratingNpsOptions } from './ratingNpsData'
import { scaleOptions } from './scaleMockData'
import { smileRatingOptions } from './smileRatingMockData'
import { starRatingQuestionOptions } from './starRatingMockData'
import { createVariantsData } from './variantsData'

/**
 * Creates a view logic rule
 * @param {object} options - View logic options
 * @param {number} options.questionId - Question ID
 * @param {string} options.displayType - Display type
 * @param {string} options.conditionType - Condition type
 * @param {number} options.conditionQuestionId - Condition question ID
 * @param {Array} options.variants - Variants
 * @param {string} options.parameter - Parameter
 * @param {string} options.parameterCondition - Parameter condition
 * @param {string} options.parameterValue - Parameter value
 * @returns {object} View logic rule
 */
function createViewLogicRule({
  questionId,
  displayType = VIEW_LOGIC.DISPLAY_TYPE.SHOW_IF,
  conditionType = VIEW_LOGIC.CONDITION_TYPE.ANSWER,
  conditionQuestionId = null,
  variants = [],
  parameter = null,
  parameterCondition = null,
  parameterValue = null,
}) {
  return {
    id: Math.floor(Math.random() * 10000),
    question_id: questionId,
    visibility: displayType,
    condition_type: conditionType,
    condition_question_id: conditionQuestionId,
    variants,
    skipped: [],
    parameter,
    parameter_condition: parameterCondition,
    parameter_value: parameterValue,
    sort: 0,
  }
}

const viewLogicMockData = {
  'view-logic-scales': (additionalParams = {}) => {
    // Define question IDs
    const q1Id = 1001 // Star Rating
    const q2Id = 1002 // Rating Scale
    const q3Id = 1003 // Smile Rating
    const q4Id = 1004 // NPS (from 1)
    const q5Id = 1005 // NPS (from 0)
    const q6Id = 1006 // Scale

    return defaultQuestionsData({
      questions: [
        // Question 1: Star Rating with parameter logic
        {
          ...starRatingQuestionOptions(),
          id: q1Id,
          question_id: q1Id,
          description: 'Оцените наш сервис',
          type: STARS_QUESTION,
          questionViewLogic: [
            createViewLogicRule({
              questionId: q1Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.PARAMETER,
              parameter: 'user_type',
              parameterCondition: VIEW_LOGIC.PARAMETER_CONDITION_TYPE.EQUAL,
              parameterValue: 'premium',
            }),
          ],
        },

        // Question 2: Rating Scale with mixed logic
        {
          id: q2Id,
          question_id: q2Id,
          type: RATING_QUESTION,
          starRatingOptions: {
            count: 10,
          },
          description_html: '<p>Оцените по шкале</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q2Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q1Id,
              variants: [4, 5], // Show if Q1 was rated 4 or 5
            }),
            createViewLogicRule({
              questionId: q2Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.PARAMETER,
              parameter: 'region',
              parameterCondition: VIEW_LOGIC.PARAMETER_CONDITION_TYPE.CONTAINS,
              parameterValue: 'EU',
            }),
          ],
        },

        // Question 3: Smile Rating with mixed logic
        {
          ...smileRatingOptions(),
          id: q3Id,
          question_id: q3Id,
          type: SMILE_QUESTION,
          description_html: '<p>Оцените наш сервис (смайлы)</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q3Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q2Id,
              variants: [7, 8, 9, 10], // Show if Q2 was rated 7-10
            }),
            createViewLogicRule({
              questionId: q3Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.PARAMETER,
              parameter: 'language',
              parameterCondition: VIEW_LOGIC.PARAMETER_CONDITION_TYPE.STARTS_WITH,
              parameterValue: 'en',
            }),
          ],
        },

        // Question 4: NPS (from 1)
        {
          ...ratingNpsOptions({ fromOne: 1 }),
          id: q4Id,
          question_id: q4Id,
          type: NPS_QUESTION,
          description_html: '<p>Рейтинг NPS (от 1)</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q4Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q3Id,
              variants: [1], // Show if Q3 was rated with first smile
            }),
          ],
        },

        // Question 5: NPS (from 0)
        {
          ...ratingNpsOptions({ fromOne: 0 }),
          id: q5Id,
          question_id: q5Id,
          type: NPS_QUESTION,
          description_html: '<p>Рейтинг NPS (от 0)</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q5Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q4Id,
              variants: [8, 9, 10], // Show if Q4 was rated 8-10
            }),
          ],
        },

        // Question 6: Scale with complex logic
        {
          ...scaleOptions(),
          id: q6Id,
          question_id: q6Id,
          type: SCALE_QUESTION,
          description_html: '<p>Оцените по шкале</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q6Id,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q5Id,
              variants: [0, 1, 2, 3], // Hide if Q5 was rated 0-3
            }),
            createViewLogicRule({
              questionId: q6Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.PARAMETER,
              parameter: 'device',
              displayType: VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF,
              parameterCondition: VIEW_LOGIC.PARAMETER_CONDITION_TYPE.EQUAL,
              parameterValue: 'mobile', // Hide if device is mobile
            }),
          ],
        },
      ],
      variables: {
        ...additionalParams,
      },
      //   variables: {
      //     user_type: 'premium',
      //     region: 'EU-West',
      //     language: 'en-US',
      //     device: 'desktop',
      //   },
    })
  },

  'view-logic-variants': (additionalParams = {}) => {
    // Define question IDs
    const q1Id = 2001 // Variants
    const q2Id = 2002 // Variants with media
    const q3Id = 2003 // Filials
    const q4Id = 2004 // Filials list
    const q5Id = 2005 // Classifier simple
    const q6Id = 2006 // Classifier tree

    return defaultQuestionsData({
      questions: [
        // Question 1: Basic variants with parameter logic
        {
          ...createVariantsData(),
          id: q1Id,
          question_id: q1Id,
          description: 'Варианты ответов',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q1Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.PARAMETER,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF,
              parameter: 'user_type',
              parameterCondition: VIEW_LOGIC.PARAMETER_CONDITION_TYPE.EQUAL,
              parameterValue: 'premium',
            }),
          ],
        },

        // Question 2: Variants with media and answer logic
        {
          ...createVariantsData({
            variants_with_files: 1,
            detail_answers: [
              { id: 1, variant: 'Вариант 1', file_url: '/uploads/img-preview-1.jpeg', preview_url: '/uploads/img-preview-1.jpeg' },
              { id: 2, variant: 'Вариант 2', file_url: '/uploads/img-preview-2.jpeg', preview_url: '/uploads/img-preview-2.jpeg' },
              { id: 3, variant: 'Вариант 3', file_url: '/uploads/img-preview-3.jpeg', preview_url: '/uploads/img-preview-3.jpeg' },
            ],
          }),
          id: q2Id,
          question_id: q2Id,
          description_html: '<p>Варианты ответов с медиа</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q2Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q1Id,
              variants: [1, 2], // Show if first question variants 1 or 2 selected
            }),
          ],
        },

        // Question 3: Filials basic -> replaced with classifier question
        {
          ...classifierQuestionOptions({
            dictionary_list_type: 'list',
            description_html: '<p>Выбор филиала</p>',
          }),
          type: FILIALS_QUESTION,
          id: q3Id,
          question_id: q3Id,
          questionViewLogic: [
            createViewLogicRule({
              questionId: q3Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q2Id,
              variants: [1, 0], // Show if second question variant 1 or self variant selected
            }),
          ],
        },

        // Question 4: Filials list -> replaced with classifier question
        {
          ...classifierQuestionOptions({
            dictionary_list_type: 'list',
            description_html: '<p>Выбор филиала / Списком</p>',
          }),
          type: FILIALS_QUESTION,
          id: q4Id,
          question_id: q4Id,
          questionViewLogic: [
            createViewLogicRule({
              questionId: q4Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q3Id,
              variants: [1876],
            }),
          ],
        },

        // Question 5: Classifier simple list
        {
          ...classifierQuestionOptions({
            dictionary_list_type: 'list',
          }),
          id: q5Id,
          question_id: q5Id,
          description_html: '<p>Классификатор / Простой список</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q5Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q4Id,
              variants: [163],
            }),
          ],
        },

        // Question 6: Classifier tree
        {
          ...classifierQuestionOptions({
            dictionary_list_type: 'tree',
          }),
          id: q6Id,
          question_id: q6Id,
          description_html: '<p>Классификатор / Древовидный список</p>',
          questionViewLogic: [
            createViewLogicRule({
              questionId: q6Id,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q5Id,
              variants: [163, 166], // Show if fifth question elements selected
            }),
          ],
        },
        {
          ...intermediateBlockOptions({
            intermediateBlock: {
              text: '<p>Конечный блок</p>',
              screen_type: INTERMEDIATE_BLOCK_TYPES.END,
            },
          }),
          id: 1007,
          question_id: 1007,
          questionViewLogic: [
            createViewLogicRule({
              questionId: 1007,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: q6Id,
              variants: [163, 1876], // Show if sixth question elements selected
            }),
          ],
        },

      ],
      variables: {
        ...additionalParams,
      },
    })
  },
  'view-logic-variant-scales': () => {
    const defaultData = defaultQuestionsData()
    defaultData.answer.status = 'open'
    defaultData.questions = [
      // Star Rating Variants question with view logic
      {
        id: 1,
        question_id: 166430,
        description: 'Star Rating Variants',
        description_html: '<p>Star Rating Variants</p>',
        type: STAR_VARIANTS_QUESTION,
        isRequired: 1,
        detail_answers: [
          { id: 1, variant: 'Variant 1', position: 1, is_deleted: 0, extra_question: 0, need_extra: 1 },
          { id: 2, variant: 'Variant 2', position: 2, is_deleted: 0, extra_question: 0, need_extra: 1 },
          { id: 3, variant: 'Variant 3', position: 3, is_deleted: 0, extra_question: 0, need_extra: 0 },
        ],
        textFieldParam: {},
        starRatingOptions: {
          color: 'rgb(248, 205, 28)',
          count: 5,
        },
        // questionViewLogic: [
        //   {
        //     id: 8864,
        //     question_id: 166430,
        //     visibility: 0,
        //     condition_type: 0,
        //     condition_question_id: 166431,
        //     variants: [
        //       { row: '1', col: 4 },
        //       { row: '2', col: 5 },
        //       { row: '3', col: 3 },
        //     ],
        //     skipped: ['1', '2'],
        //   },
        // ],
      },
      // NPS Rating question with view logic
      {
        id: 2,
        question_id: 166431,
        description: 'NPS Rating',
        description_html: '<p>NPS Rating</p>',
        type: 12,
        isRequired: 1,
        set_variants: 1,
        detail_answers: [
          { id: 1, variant: 'NPS Variant 1', position: 1, is_deleted: 0, extra_question: 0, need_extra: 1 },
          { id: 2, variant: 'NPS Variant 2', position: 2, is_deleted: 0, extra_question: 0, need_extra: 1 },
        ],
        npsRatingSetting: {
          design: 1,
          start_point_color: '#F96261',
          end_point_color: '#00C968',
        },
        questionViewLogic: [
          {
            id: 8865,
            question_id: 166431,
            visibility: 0,
            condition_type: 0,
            condition_question_id: 166430,
            variants: [
              { row: 1, col: 4 },
              { row: 2, col: 5 },
              { row: 3, col: 3 },
            ],
            skipped: [1, 2],
          },
        ],
      },
      // Scale question with view logic
      {
        id: 3,
        question_id: 166432,
        description: 'Scale',
        description_html: '<p>Scale</p>',
        type: 20,
        isRequired: 1,
        set_variants: 1,
        detail_answers: [
          { id: 1, variant: 'Scale Variant 1', position: 1, is_deleted: 0, extra_question: 0, need_extra: 1 },
          { id: 2, variant: 'Scale Variant 2', position: 2, is_deleted: 0, extra_question: 0, need_extra: 1 },
        ],
        scaleRatingSetting: {
          start: 0,
          end: 100,
          step: 10,
        },
        questionViewLogic: [
          {
            id: 8866,
            question_id: 166432,
            visibility: 0,
            condition_type: 0,
            condition_question_id: 166431,
            variants: [
              { row: 1, col: 1 },
              { row: 1, col: 2 },
              { row: 1, col: 3 },
              { row: 1, col: 4 },
              { row: 1, col: 5 },
              { row: 2, col: 6 },
              { row: 2, col: 7 },
            ],
            skipped: [2],
          },
        ],
      },
      // Matrix question with view logic
      {
        id: 4,
        question_id: 166433,
        description: 'Matrix',
        description_html: '<p>Matrix</p>',
        type: 13,
        isRequired: 1,
        detail_answers: [
          { id: 1, variant: 'Variant 1', position: 1, is_deleted: 0, extra_question: 0, need_extra: 1 },
          { id: 2, variant: 'Variant 2', position: 2, is_deleted: 0, extra_question: 0, need_extra: 1 },
          { id: 3, variant: 'Variant 3', position: 3, is_deleted: 0, extra_question: 0, need_extra: 0 },
        ],
        matrixSettings: {
          rows: ['row 1', 'row 2', 'row 3'],
          cols: ['1', '2', '3', '4', '5'],
          type: 'standart',
        },
        questionViewLogic: [
          {
            id: 8867,
            question_id: 166433,
            visibility: 0,
            condition_type: 0,
            condition_question_id: 166432,
            variants: [
              { col: [70, 100], row: 1 },
              { col: [30, 70], row: 2 },
            ],
            skipped: [],
          },
        ],
      },
      {
        ...intermediateBlockOptions({
          intermediateBlock: {
            text: '<p>Конечный блок</p>',
            screen_type: INTERMEDIATE_BLOCK_TYPES.END,
          },
          questionViewLogic: [
            {
              id: 8867,
              question_id: 166433,
              visibility: VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF,
              condition_type: 0,
              condition_question_id: 166433,
              variants: [
                { col: '2', row: 'row 1' },
                { col: '3', row: 'row 2' },
                { col: '4', row: 'row 3' },
              ],
              skipped: ['row 1', 'row 2'],
            },
          ],
        }),
      },
    ]
    return defaultData
  },
  'view-logic-multiple-questions': () => {
    const defaultData = defaultQuestionsData()
    defaultData.answer.status = 'open'

    defaultData.poll.displaySetting = {
      id: 1,
      foquz_poll_id: 1,
      type: 2,
      random_order: 0,
      random_exclusion: null,
    }

    // displayPages - 3 questions per page
    defaultData.poll.displayPages = [
      {
        id: 1,
        page: 1,
        questions: [{
          id: 1,
          question_id: 1,
        }, {
          id: 2,
          question_id: 2,
        }, {
          id: 3,
          question_id: 3,
        }],
      },
      {
        id: 2,
        page: 2,
        questions: [{
          id: 4,
          question_id: 4,

        }, {
          id: 5,
          question_id: 5,
        }, {
          id: 6,
          question_id: 6,
        }],
      },
      {
        id: 3,
        page: 3,
        questions: [{
          id: 7,
          question_id: 7,
        }, {
          id: 8,
          question_id: 8,
        }, {
          id: 9,
          question_id: 9,
        }],
      },
      {
        id: 4,
        page: 4,
        questions: [{
          id: 10,
          question_id: 10,
        }],
      },
    ]
    defaultData.questions = [
      // Question 1: Star Rating
      {
        ...starRatingQuestionOptions({
          id: 1,
          question_id: 1,
          description_html: 'ЗР 1',
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 2,
          question_id: 2,
          description_html: 'ЗР 2',
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 3,
          question_id: 3,
          description_html: 'ЗР 3',
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 4,
          question_id: 4,
          description_html: 'ЗР 4',
          questionViewLogic: [
            createViewLogicRule({
              questionId: 4,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.SHOW_IF,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: 1,
              variants: [1],
            }),
          ],
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 5,
          question_id: 5,
          description_html: 'ЗР 5',
          questionViewLogic: [
            createViewLogicRule({
              questionId: 5,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.SHOW_IF,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: 2,
              variants: [2],
            }),
          ],
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 6,
          question_id: 6,
          description_html: 'ЗР 6',
          questionViewLogic: [
            createViewLogicRule({
              questionId: 6,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.SHOW_IF,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: 3,
              variants: [3],
            }),
          ],
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 7,
          question_id: 7,
          description_html: 'ЗР 7',
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 8,
          question_id: 8,
          description_html: 'ЗР 8',
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 9,
          question_id: 9,
          description_html: 'ЗР 9',
        }),
      },
      {
        ...starRatingQuestionOptions({
          id: 10,
          question_id: 10,
          description_html: 'ЗР 10',
          questionViewLogic: [
            createViewLogicRule({
              questionId: 10,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: 7,
              variants: [1],
            }),
            createViewLogicRule({
              questionId: 10,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: 8,
              variants: [2],
            }),
            createViewLogicRule({
              questionId: 10,
              displayType: VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF,
              conditionType: VIEW_LOGIC.CONDITION_TYPE.ANSWER,
              conditionQuestionId: 9,
              variants: [3],
            }),
          ],
        }),
      },
    ]
    return defaultData
  },
}

export default viewLogicMockData
