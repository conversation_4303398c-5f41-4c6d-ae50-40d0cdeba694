# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# FOQUZ Widget DOM Agent Guide

## Commands
- **Dev**: `npm run dev` (localhost:5173), `npm run dev:test` (localhost:3002, with test mode)
- **Build**: `npm run build` (produces dist/widget.js and dist/styles.css)
- **Size**: `npm run size` (checks bundle size against 63KB gzipped limit)
- **Lint**: `npm run lint:js` (ESLint). NOTE: NEVER run `npm run lint` as it includes stylelint for CSS, which is not configured and will produce errors.
- **Test**: `npx playwright test` (E2E tests), `npx playwright test --ui` (interactive), `npx playwright test tests/e2e/specific.test.js` (single test)

## Architecture
- **Entry point**: `widget.js` (imports from src/components/core/widget/)
- **Framework**: VanJS (reactive UI, <1kb) with CSS modules for styling
- **Structure**: `src/components/` (core/, questions/, ui/), `src/api.js`, `src/validation/`, `src/helpers/`
- **Aliases**: `@` → `./src/`, `@components` → `./src/components/`
- **Testing**: Playwright E2E tests in `tests/e2e/`, MSW for API mocking
- **Build**: Vite with custom CSS scoped naming, ESM output format

## Code Style
- **ES6+ modules**, no TypeScript
- **ESLint** + Prettier (spaces, semicolons), Stylelint for CSS
- **Naming**: camelCase for JS, kebab-case for CSS classes (CSS modules generate `_fc-widget_hash` format)
- **Imports**: Use aliases `@/` and `@components/`, relative paths for same directory
- **VanJS**: Use `van.tags` destructuring, reactive `van.state()` for data binding
- **No comments** unless complex logic requires explanation
