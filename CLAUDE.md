# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Development
npm run dev              # Standard development server
npm run dev:local        # Development with local mode  
npm run dev:test         # Test environment on port 5178

# Building
npm run build            # Build with TypeScript checking
npm run build:production # Production build

# Testing
npm run test             # Run Playwright e2e tests
npm run type-check       # TypeScript validation

# Code Quality
npm run lint             # ESLint checking
npm run lint:fix         # Auto-fix linting issues
```

## Architecture Overview

This Vue.js poll application follows **Feature Sliced Design (FSD)** architecture:

```
src/
├── app/          # Core application setup
├── entities/     # Business entities (poll, question)
├── features/     # Self-contained features (points, themes)
├── widgets/      # Complex composite UI components
└── shared/       # Reusable utilities and basic UI
```

### Key Architectural Decisions

- **Component Framework**: Vue 3 with Composition API (`<script setup>`)
- **State Management**: Pinia stores (prefix with `use`, e.g., `usePollStore`)
- **TypeScript**: Gradual migration, use for new files, strict mode enabled
- **Path Aliases**: `@/` → `src/`, `@shared/`, `@entities/`, `@features/`, `@widgets/`

### Component Placement Rules

- Pure UI components → `shared/ui/`
- Components requiring stores/state → `widgets/`
- Business logic components → `entities/{entity}/ui/`
- Feature-specific components → `features/{feature}/ui/`

## Testing Approach

Use MSW for API mocking during development:
- Set `VITE_ENABLE_API_MOCK=true` in environment
- Or use URL parameter `?_fz_enable_mocks=true`
- Mock handlers are in `src/app/api/mock/handlers/`

E2E tests use Playwright:
```bash
npm run test                    # Run all tests
npm run test -- test-name       # Run specific test
```

## Question Types

The app supports 20+ question types, each with specific implementations:
- **Variants**: Single/multiple choice with file attachments
- **Matrix**: Grid-based questions
- **Rating/NPS**: Numeric rating scales
- **Input Types**: Text, number, email, phone
- **Special**: Geolocation, signature, media capture

Question type implementations are in `src/entities/question/types/`

## Important Patterns

### Store Usage
```javascript
import { usePollStore } from '@entities/poll/model/store'
const pollStore = usePollStore()
```

### Component Props with TypeScript
```vue
<script setup lang="ts">
interface Props {
  questionId: number
  isRequired?: boolean
}
const props = defineProps<Props>()
</script>
```

### API Integration
- API client: `src/shared/api/api-client.js`
- Environment-based URL configuration in `vite.config.ts`
- Production uses `/p/` base path, development uses `/`

## File Attachments System

Recent refactoring in `refactor/variants-file-upload` branch:
- Handlers in `src/entities/question/handlers/`
- Components: `VariantsFileAttachment.vue`, `VariantsFileUpload.vue`
- Supports multiple file types with validation
- Progress tracking and error handling

## Development Tips

1. Check existing implementations before creating new components
2. Follow established naming conventions (kebab-case files, PascalCase components)
3. Use existing CSS modules and global styles for styling
4. Leverage existing shared components from `@shared/ui/`
5. Test with MSW mocks before backend integration
6. Use TypeScript for new files and gradually migrate existing ones