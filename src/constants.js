import commonStyles from "./styles/common.module.css";

export const CSS_VARIABLE_PREFIX = "--fqz-widget-";

/**
 * Transition names for the widget
 * Used as a property for Transition component to add a transition effect
 */
export const TRANSITION_NAMES = {
  FADE: commonStyles["fc-widget-fade"],
  FADE_IN: commonStyles["fc-widget-fade-in"],
  SLIDE: commonStyles["fc-widget-slide"],
};

/**
 * File size constants
 */
export const FILE_SIZE = {
  BYTES_IN_MB: 1024 * 1024,
  DEFAULT_LIMIT_MB: 5,
};

/**
 * Attachment error kinds
 */
export const ATTACHMENT_ERROR_KIND = {
  DEFAULT: "default",
  FILE_TOO_LARGE: "too-large",
  INVALID_TYPE: "invalid-type",
  MAX_FILES_REACHED: "max-files",
  UPLOAD_FAILED: "upload-failed",
};

/**
 * Attachment types
 */
export const ATTACHMENT_TYPE = {
  SCREENSHOT: "screenshot",
  IMAGE: "image",
  VIDEO: "video",
  AUDIO: "audio",
  FILE: "file",
};
