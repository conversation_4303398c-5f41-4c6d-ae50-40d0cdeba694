<script setup>
import { usePollStore } from '@/entities/poll/model/store'
import { usePreviewStore } from '@shared/store/previewStore'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import ArrowButton from '@shared/ui/ArrowButton.vue'
import PollTimer from '@shared/ui/PollTimer.vue'
import { useResizeObserver, useScreenOrientation, useScroll } from '@vueuse/core'
import debounce from 'lodash.debounce'
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch, watchEffect } from 'vue'

const props = defineProps({
  isTablet: Boolean,
  pageIndex: Number,
  hasPrev: Boolean,
  isFirst: Boolean,
  shouldShowFinishButton: Boolean,
  finishLink: String,
  backButtonText: String,
  nextButtonText: String,
  timeToPass: {
    type: Number,
    default: 0,
  },
  showTimer: {
    type: Boolean,
    default: false,
  },
  timeLeft: {
    type: Number,
    default: 0,
  },
  isSubmitted: Boolean,
  additionalMarginBottom: Number,
})

const store = usePollStore()
const previewStore = usePreviewStore()
const translationsStore = useTranslationsStore()
const simplifiedStore = useSimplifiedStore()
const tabletStore = useTabletStore()
const t = translationsStore.t

const {
  activePage,
  activePageContainsMultipleQuestions,
  isPagesModeEnabled,
  pageController,
  isSavingAnswer,
  forceNextButtonToBeDisabled,
  shouldHideBackButton,
} = storeToRefs(store)

const isPreviewMode = computed(() => previewStore.isPreviewMode)
const isFullPreview = computed(() => previewStore.isFullPreview)

const actionsRef = ref(null)
const mainButtonsRef = ref(null)
const prevButtonRef = ref(null)
const nextButtonRef = ref(null)
const { y: scrollY } = useScroll(window, {
  throttle: 100,
  idle: 0,
})

const bodyEl = document.body
const bodyElRef = ref(bodyEl)

/**
 * Включаем дополнительные кнопки навигации, если режим страниц включен и активная страница содержит несколько вопросов
 * Дополнительные кнопки навигации включаются для того,
 * чтобы переходить от вопроса к вопросу на одной странице
 */
const enableQuestionsNavigation = computed(
  () => isPagesModeEnabled.value
    && activePageContainsMultipleQuestions.value
    && !pageController.value?.allQuestionsAreWithinViewport.value
    && !simplifiedStore.isSimplifiedMode,
)

const isActionSticky = ref(false)

const updateStickyState = debounce(() => {
  if (!actionsRef.value || enableQuestionsNavigation.value) {
    isActionSticky.value = false
    return
  }

  const windowHeight = window.innerHeight
  const elementBottom = actionsRef.value.getBoundingClientRect().bottom

  isActionSticky.value = windowHeight - elementBottom < 5
}, 10)

useResizeObserver(bodyElRef, () => {
  updateStickyState()
})

const { orientation } = useScreenOrientation()

watch([scrollY], updateStickyState)

watchEffect(() => {
  // Trigger update when orientation changes

  // eslint-disable-next-line ts/no-unused-expressions
  orientation.value
  updateStickyState()
})

const scrollElement = ref(null)

onMounted(() => {
  const nonFullPreview = isPreviewMode.value && !isFullPreview.value

  const simplebarContentWrapper = document.querySelector('.app-root-container .simplebar-content-wrapper')
  scrollElement.value = nonFullPreview ? simplebarContentWrapper : window

  if (scrollElement.value) {
    scrollElement.value.addEventListener('resize', updateStickyState)
    scrollElement.value.addEventListener('scroll', updateStickyState)
  }
})

onUnmounted(() => {
  if (scrollElement.value) {
    scrollElement.value.removeEventListener('resize', updateStickyState)
    scrollElement.value.removeEventListener('scroll', updateStickyState)
  }
})

const showPrevButton = computed(() => {
  if (simplifiedStore.isSimplifiedMode) {
    return false
  }
  // Скрываем кнопку "Назад", если в предыдущем переходе были вопросы с ограничениями по квотам
  if (shouldHideBackButton.value) {
    return false
  }
  return !props.isFirst && props.hasPrev
})

const isPrevButtonDisabled = computed(() => {
  // Access the ref directly from the store, not storeToRefs
  return store.forcePrevButtonToBeDisabled
})

const isNextButtonDisabled = computed(() => {
  return activePage.value?.blocked
    || forceNextButtonToBeDisabled.value
    || isSavingAnswer.value
})

function handlePrev() {
  store.goPrev()
}

const pollActionClasses = computed(() => {
  const baseClasses = {
    'survey-questions__question-actions': true,
    'survey-actions': true,
    'survey-actions--questions-navigation-enabled': enableQuestionsNavigation.value,
    'survey-actions--simplified': simplifiedStore.isSimplifiedMode,
    'survey-actions--simplified-iframe': simplifiedStore.isInIframe,
    'survey-actions--tablet': tabletStore.isTabletMode,
  }

  if (!simplifiedStore.isInIframe) {
    baseClasses.sticky = isActionSticky.value
  }

  return baseClasses
})

const additionalActionsRef = ref(null)
const isAdditionalActionsFixed = ref(false)

function updateQuestionsNavigationState() {
  if (!additionalActionsRef.value || !actionsRef.value)
    return

  const additionalActionsEl = additionalActionsRef.value
  const containerEl = actionsRef.value
  const containerRect = containerEl.getBoundingClientRect()
  const additionalActionsRect = additionalActionsEl.getBoundingClientRect()
  const viewportHeight = window.innerHeight

  if (!isAdditionalActionsFixed.value) {
    // Check if the bottom of the element is close to or above the viewport bottom
    if (additionalActionsRect.bottom >= viewportHeight) {
      additionalActionsEl.classList.add('survey-actions__additional-actions--fixed')
      isAdditionalActionsFixed.value = true
    }
  }
  else {
    // Check if the top of additionalActions is close to or below the top of containerEl
    if (additionalActionsRect.top >= containerRect.top + 10) {
      additionalActionsEl.classList.remove('survey-actions__additional-actions--fixed')
      isAdditionalActionsFixed.value = false
    }
  }
}

function setupQuestionsNavigation() {
  updateQuestionsNavigationState()
  const nonFullPreview = isPreviewMode.value && !isFullPreview.value
  const scrollElement = nonFullPreview
    ? document.querySelector('.app-root-container .simplebar-content-wrapper')
    : window

  scrollElement.addEventListener('scroll', updateQuestionsNavigationState)
  scrollElement.addEventListener('resize', updateQuestionsNavigationState)
}

function destroyQuestionsNavigation() {
  const nonFullPreview = isPreviewMode.value && !isFullPreview.value
  const scrollElement = nonFullPreview
    ? document.querySelector('.app-root-container .simplebar-content-wrapper')
    : window

  if (!scrollElement)
    return

  scrollElement.removeEventListener('scroll', updateQuestionsNavigationState)
  scrollElement.removeEventListener('resize', updateQuestionsNavigationState)
}

watch(enableQuestionsNavigation, (newVal) => {
  if (newVal) {
    setupQuestionsNavigation()
    setTimeout(() => {
      // trigger scrollEvent
      window.dispatchEvent(new Event('scroll'))
    }, 100)
  }
  else {
    destroyQuestionsNavigation()
  }
}, { immediate: true })

const handleFirstQuestionDisabled = computed(() => {
  const isFirstQuestionInViewPort = pageController.value?.firstQuestionInViewPort.value
  return isFirstQuestionInViewPort
})

const pollActionsStyle = computed(() => {
  const mb = props.additionalMarginBottom || 0
  return {
    '--additional-margin-bottom': `${mb}px`,
  }
})
</script>

<template>
  <div ref="actionsRef" :class="pollActionClasses" :style="pollActionsStyle">
    <div class="survey-actions__container">
      <div v-if="showTimer && !enableQuestionsNavigation && !isTablet" class="survey-actions__timer">
        <PollTimer :label="t('Оставшееся время')" :elapsed-time="timeLeft" />
      </div>
      <div v-if="isAdditionalActionsFixed" class="survey-actions__phantom" />
      <div
        v-if="enableQuestionsNavigation"
        ref="additionalActionsRef"
        class="survey-actions__additional-actions"
      >
        <div class="survey-actions__additional-actions-container">
          <div v-if="showTimer && !isTablet" class="survey-actions__timer">
            <PollTimer :label="t('Оставшееся время')" :elapsed-time="timeLeft" />
          </div>
          <div class="survey-actions__questions-navigation">
            <button
              type="button"
              data-testid="poll-action-scroll-prev"
              class="survey-actions__questions-navigation-btn survey-actions__questions-navigation-btn--prev"
              :disabled="handleFirstQuestionDisabled"
              @click.prevent="pageController.scrollToPrevQuestionInViewport"
            >
              <svg class="survey-actions__questions-navigation-icon" width="14" height="9" viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 7.98511L7 1.98511L0.999995 7.98511" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </button>
            <button type="button" class="survey-actions__questions-navigation-btn" data-testid="poll-action-scroll-next" :disabled="pageController?.lastQuestionInViewPort.value" @click.prevent="pageController?.scrollToNextQuestionInViewport">
              <svg width="14" height="9" viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1.98511L7 7.98511L13 1.98511" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <div ref="mainButtonsRef" class="survey-actions__main-buttons" :data-ind="pageIndex">
        <!-- Standard buttons for non-tablet mode -->
        <template v-if="!isTablet">
          <button
            v-if="showPrevButton"
            ref="prevButtonRef"
            data-testid="poll-action-prev"
            type="button"
            class="btn survey-questions__question-actions-btn survey-questions__question-actions-btn--focus-prev survey-questions__question-actions-btn--prev"
            :disabled="isPrevButtonDisabled"
            @click.prevent="handlePrev"
          >
            {{ backButtonText }}
          </button>
          <button
            ref="nextButtonRef"
            data-testid="poll-action-next"
            type="submit"
            class="btn survey-questions__question-actions-btn survey-questions__question-actions-btn--focus-next survey-questions__question-actions-btn--next"
            :disabled="isNextButtonDisabled"
          >
            {{ nextButtonText }}
          </button>
        </template>

        <!-- Arrow buttons for tablet mode -->
        <template v-else>
          <div v-if="showPrevButton" class="tablet-button-container tablet-button-container--prev">
            <ArrowButton
              direction="back"
              :disabled="isPrevButtonDisabled"
              data-testid="poll-action-prev"
              @click="handlePrev"
            />
          </div>
          <div class="tablet-button-container tablet-button-container--next">
            <ArrowButton
              direction="next"
              type="submit"
              data-testid="poll-action-next"
              :disabled="isNextButtonDisabled"
            />
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.survey-questions__question-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 18px;
  width: 100%;
  padding-left: 12px;
  padding-right: 12px;
  padding-top: 12px;
  padding-bottom: 12px;
  position: sticky;
  bottom: 0;
  min-height: 72px;
}

.survey-actions--simplified {
  padding-top: 0;
  margin-top: -6px;
}

.poll-actions--simplified-iframe {
  margin-top: 0;
}

.survey-questions__question-actions:not(.survey-actions--questions-navigation-enabled) {
  margin-bottom: var(--additional-margin-bottom);
}

.survey-actions__container {
  max-width: 650px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.survey-actions__main-buttons {
  display: flex;
  align-items: center;
  z-index: 100;
  justify-content: center;
  position: relative;
  z-index: 1;
  max-width: 450px;
  flex-grow: 1;
  width: 100%;
}

.survey-questions__question-actions-btn {
  font-size: 16px;
  width: 100%;
  max-width: 155px;
  min-height: 48px;
  background: none;
  border-width: 2px !important;
  border-style: solid !important;
  border-color: currentColor;
  border-radius: 100px;
  line-height: 1;
  padding-top: 2px;
  padding-bottom: 2px;
  transition: opacity 0.3s ease-in-out;
  font-weight: 700;
  cursor: pointer;
}

.survey-questions__question-actions-btn:not(:disabled):hover {
  opacity: 0.8;
}

.survey-questions__question-actions-btn:not(:first-child) {
  margin-left: 20px;
}

.survey-questions__question-actions-btn.focus,
.survey-questions__question-actions-btn:focus {
  outline: 0;
  box-shadow: none;
}

.survey-questions__question-actions-btn--auth {
  background-color: var(--fqz-poll-main-color);
  border-color: var(--fqz-poll-main-color);
  max-width: 97px;
  font-size: 15px !important;
  color: white !important;
  border: none !important;
  white-space: nowrap;
  transition:
    background-color 0.3s ease-in-out,
    border-color 0.3s ease-in-out;
}

.survey-questions__question-actions-btn--next {
  color: var(--fqz-poll-next-button-text-color);
  border-color: var(--fqz-poll-next-button-stroke-color);
  background: var(--fqz-poll-next-button-background-color);
  border-radius: var(--fqz-poll-next-button-radius);
}

.survey-questions__question-actions-btn--prev {
  color: var(--fqz-poll-back-button-text-color);
  border-color: var(--fqz-poll-back-button-stroke-color);
  background: var(--fqz-poll-back-button-background-color);
  border-radius: var(--fqz-poll-back-button-radius);
}

.sticky {
  z-index: 100;
  bottom: 0;
  transition:
    background-color 0.2s ease,
    border-color 0.2s ease;
  flex-grow: 0;
  flex-shrink: 0;
  position: sticky !important;
}

.sticky:not(.survey-actions--simplified) {
  background-color: var(--fqz-poll-buttons-bg);
  padding-top: 12px;
  padding-bottom: 12px;
}

.survey-actions--simplified.sticky {
  background-color: var(--fqz-poll-main-place-color);
}

.survey-actions--simplified.sticky .survey-questions__question-actions-btn {
  color: var(--fqz-poll-text-on-bg);
}

.survey-actions--questions-navigation-enabled {
  margin-bottom: 230px;
  position: relative !important;
}

.survey-actions__additional-actions {
  width: 100%;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  /* z-index: 3; */
  margin-left: auto;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  justify-content: space-between;
}

.survey-actions__additional-actions-container {
  width: 100%;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.survey-actions__additional-actions-container .survey-actions__timer {
  margin-bottom: 0;
}

.survey-actions__additional-actions--fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: auto;
  height: 72px;
  background-color: var(--fqz-poll-buttons-bg);
  padding: 12px;
  z-index: 100;
}

.survey-actions__phantom {
  height: 36px;
}

.survey-actions__additional-actions--fixed + .survey-actions__phantom {
  display: block;
}

.survey-actions__additional-actions--fixed + .survey-actions__phantom {
  display: block;
}

.survey-actions:not(.survey-actions--questions-navigation-enabled) .survey-actions__phantom {
  display: none;
}

.survey-actions__questions-navigation {
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.survey-actions__questions-navigation-btn {
  all: unset;
  color: var(--fqz-poll-text-on-bg);
  border: 2px solid var(--fqz-poll-text-on-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  width: 36px;
  border-radius: 100%;
  background-color: transparent;
  transition: opacity 0.3s ease;
  cursor: pointer;
  box-sizing: border-box;
}

.survey-actions__questions-navigation-btn--prev {
  padding-bottom: 2px;
}

.survey-actions__questions-navigation-btn:not(:disabled):hover {
  opacity: 0.8;
}

.survey-actions__questions-navigation-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.survey-actions__questions-navigation-icon {
  display: block;
}

.survey-questions__question-actions-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.survey-actions__timer {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.survey-actions--simplified {
  position: sticky !important;
  z-index: 100;
  flex: 0 0 auto;
  margin-top: 0;
  margin-bottom: 0 !important;
  padding-top: 0;
}

.survey-actions--simplified-iframe {
  min-height: 0;
  margin: 0;
  position: relative !important;
}

.survey-actions--simplified,
.poll-actions--simplified-iframe {
  padding-top: 30px;
  padding-bottom: 50px;
}

@media screen and (max-width: 679px) {
  .survey-actions__timer {
    position: static;
    transform: none;
    align-self: flex-start;
    min-height: 36px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .survey-questions__question-actions {
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 8px;
    padding-top: 12px;
    min-height: 0;
  }

  .survey-questions__question-actions-btn {
    min-height: 36px;
    padding-bottom: 2px;
    flex-grow: 1;
    max-width: 100%;
  }
  .survey-questions__question-actions-btn:not(:first-child) {
    margin-left: 10px;
  }

  .survey-actions--questions-navigation-enabled .survey-actions__container {
    flex-direction: column;
  }
  .survey-actions__additional-actions {
    position: relative;
    height: 60px;
  }
  .survey-actions__additional-actions--fixed {
    position: fixed;
    padding-left: 15px;
    padding-right: 15px;
  }
  .survey-actions__container {
    flex-direction: column;
    gap: 5px;
  }

  .survey-actions__main-buttons {
    max-width: 100%;
  }
  .survey-actions__phantom {
    height: 60px;
  }

  .survey-questions__question-actions-btn:not(:disabled):hover {
    opacity: 0.8;
  }

  .survey-actions--simplified {
    padding-top: 30px;
    padding-bottom: 30px;
    margin-top: 0;
    padding-left: 20px;
    padding-right: 20px;
  }

  .survey-actions--simplified .survey-actions__timer {
    position: static;
    transform: none;
    min-height: 0;
    margin-bottom: 5px;
    width: 100%;
    justify-content: center;
  }

  .survey-actions--simplified .poll-timer {
    flex-direction: row;
    height: auto;
    align-items: center;
  }

  .survey-actions--simplified :deep(.poll-timer__time) {
    font-size: 12px;
  }
}

/* Tablet mode styles */
.tablet-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 11px;
  background-color: var(--fqz-poll-buttons-bg);
  border-radius: 50%;
}

.tablet-button-container .arrow-button {
  position: relative;
}

.tablet-button-container .arrow-button:after {
  content: '';
  position: absolute;
  top: -11px;
  left: -11px;
  width: calc(100% + 22px);
  height: calc(100% + 22px);
  background-color: var(--fqz-poll-buttons-bg);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tablet-button-container--prev {
  margin-right: auto;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.tablet-button-container--next {
  margin-left: auto;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.tablet-button-container--prev .arrow-button {
  color: var(--fqz-poll-back-button-text-color);
  border-color: var(--fqz-poll-back-button-stroke-color);
  background: var(--fqz-poll-back-button-background-color);
}

.tablet-button-container--next .arrow-button {
  color: var(--fqz-poll-next-button-text-color);
  border-color: var(--fqz-poll-next-button-stroke-color);
  background: var(--fqz-poll-next-button-background-color);
}
</style>
