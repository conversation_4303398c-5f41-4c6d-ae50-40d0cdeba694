<script setup>
import { usePollStore } from '@entities/poll/model/store'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import CountrySelect from '@shared/ui/CountrySelect.vue'
import { computed, toValue } from 'vue'
import TestModeLabel from './TestModeLabel.vue'

const props = defineProps({
  showTestmodeLabel: { type: Boolean, default: true },
  logoLink: { type: String, default: '' },
  logoType: { type: String, default: 'text' },
  logoText: { type: String, default: '' },
  logoFontFamily: { type: String, default: '' },
  logoColor: { type: String, default: '' },
  logoPosition: { type: Number, default: 1 },
  logoMargins: { type: Number, default: 0 },
  smallHeaderMobile: { type: Number, default: 0 },
  logoHeight: { type: [String, Number], default: '' },
  logoTextSize: { type: [String, Number], default: '' },
  logoTextBold: { type: [Boolean, Number], default: false },
  logoTextItalic: { type: [Boolean, Number], default: false },
  logoImage: { type: String, default: '' },
  disabled: { type: Boolean, default: false },
})

const store = usePollStore()
const translationsStore = useTranslationsStore()
const tabletStore = useTabletStore()
const selectedLang = computed(() => {
  return translationsStore.selectedLang
})
const allLangs = computed(() => {
  return translationsStore.allLangs
})

const logoPositionNumber = computed(() => Number.parseInt(props.logoPosition))

const headerStyle = computed(() => ({
  'minHeight': '45px',
  'position': 'relative',
  'display': 'flex',
  'alignItems': 'center',
  'backgroundColor': props.disabled ? 'transparent' : 'var(--fqz-poll-header-bg-color, #000)',
  '--logo-margin-y': `${props.logoMargins || 0}px`,
  '--logo-mobile-margin-y': `${props.smallHeaderMobile && props.logoMargins ? 2 : 0}px`,
}))

const headerClasses = computed(() => ({
  'survey-header': true,
  'survey-header--disabled': props.disabled,
  'survey-header--has-test-mode-label': props.showTestmodeLabel,
  'survey-header--has-lang-select': translationsStore.multipleLangsEnabled,
  'survey-header--logo-centered': logoPositionNumber.value === 2,
  'survey-header--has-logo-image': props.logoType === 'image',
  'survey-header--has-logo-text': props.logoType === 'text',
  'survey-header--small-mobile': props.smallHeaderMobile,
}))

const logoStyle = computed(() => {
  let justifyContentValue = 'center'
  if (logoPositionNumber.value === 3) {
    justifyContentValue = 'flex-end'
  }
  else if (logoPositionNumber.value === 1 || props.logoType === 'text') {
    justifyContentValue = 'flex-start'
  }

  const style = {
    'justifyContent': justifyContentValue,
    'width': 'auto',
    'display': 'flex',
    'alignItems': 'center',
    'padding': '0',
    'textDecoration': 'none',
    'fontSize': `${props.logoTextSize}px`,
    'lineHeight': '1',
    '--logo-color': props.logoColor || '#fff',
    'color': `var(--logo-color)`,
    'fontFamily': props.logoFontFamily || 'inherit',
  }
  return style
})

const logoTextStyle = computed(() => ({
  fontWeight: props.logoTextBold ? 'bold' : 'normal',
  fontStyle: props.logoTextItalic ? 'italic' : 'normal',
  fontSize: `${props.logoTextSize || 20}px`,
  margin: `var(--logo-margin-y) 0`,
}))

const logoImageWrapperStyle = computed(() => ({
  margin: `var(--logo-margin-y) 0`,
}))

const logoImageStyle = computed(() => {
  const heightString = `${props.logoHeight}px`
  return {
    height: props.logoHeight ? heightString : '',
    width: props.logoHeight ? 'auto' : '90px',
  }
})

function onUpdateLang(lang) {
  translationsStore.setLanguage(lang.shortCode)
}

const testModeText = computed(() => toValue(translationsStore.t('Тестовый режим')))
</script>

<template>
  <header class="survey__header" :style="headerStyle">
    <div :class="headerClasses">
      <component
        :is="logoLink ? 'a' : 'div'"
        v-if="!disabled && (logoImage || logoText)"
        :href="logoLink || '#'"
        class="navbar-brand survey__header-logo"
        target="_blank"
        :style="logoStyle"
      >
        <template v-if="logoType === 'text'">
          <span v-if="logoText" class="logo-text" :style="logoTextStyle">{{
            logoText
          }}</span>
        </template>
        <template v-else-if="logoType === 'image'">
          <div v-if="logoImage" class="img" :style="logoImageWrapperStyle">
            <img
              :src="logoImage"
              :alt="logoText || ''"
              :style="logoImageStyle"
            >
          </div>
        </template>
      </component>
      <div class="survey-header__aside">
        <div v-if="translationsStore.multipleLangsEnabled && store.languageSelectEnabled && !disabled" class="survey-header__lang-select">
          <CountrySelect
            view="compact"
            trigger-class="country-select-trigger--in-header"
            :countries="allLangs"
            :selected="selectedLang"
            :model-value="selectedLang"
            :tablet-view="tabletStore.isTabletMode"
            @update:model-value="onUpdateLang"
          />
        </div>
        <TestModeLabel v-if="showTestmodeLabel" :text="testModeText" />
      </div>
    </div>
  </header>
</template>

<style scoped>
.survey-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-self: stretch;
  width: 100%;
  padding-left: 20px;
}

.survey-header--has-test-mode-label {
  padding-right: 0;
}

.survey-header:not(.survey-header--has-test-mode-label) .survey-header__aside {
  padding-right: 20px;
}

.survey-header__aside {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 0 0 auto;
  margin-left: auto;
  align-self: stretch;
}

.survey-header--logo-centered {
  display: flex;
  flex-grow: 1;
  justify-content: center;
  padding-left: 0;
  padding-right: 0;
}

.survey-header--disabled :deep(.survey__test-mode) {
  margin-left: auto;
  margin-right: 0;
}

.survey__header-logo {
  max-width: 560px;
  overflow: hidden;
  transition: opacity 0.3s;
}

.survey__header-logo:hover,
.survey__header-logo:focus {
  opacity: 0.8;
}

.survey__header-logo:active {
  opacity: 0.6;
}

.survey__header-logo:visited {
  color: var(--logo-color);
}

.logo-text {
  white-space: nowrap;
}

.survey-header--logo-centered .survey__header-logo {
  /* margin-left: auto; */
}

.survey-header--logo-centered .survey-header__aside {
  position: absolute;
  right: 0;
  height: 100%;
  top: 0;
}

@media (max-width: 679px) {
  .survey-header {
    padding-left: 15px;
  }
  .survey-header--has-test-mode-label {
    padding-right: 0;
  }

  .survey-header--logo-centered {
    padding-left: 0;
    padding-right: 0;
  }

  .survey-header__aside {
    gap: 10px;
  }

  .survey-header:not(.survey-header--has-test-mode-label) .survey-header__aside {
    padding-right: 15px;
  }

  .survey-header .survey-header__lang-select {
    margin-right: 0;
  }

  .survey__header-logo {
    max-width: 180px;
  }

  .survey-header--small-mobile .survey__header-logo .img {
    margin: var(--logo-mobile-margin-y) 0 !important;
  }

  .survey-header--small-mobile .survey__header-logo img {
    height: 40px !important;
  }

  .survey-header--small-mobile .logo-text {
    font-size: 14px !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
}
</style>
