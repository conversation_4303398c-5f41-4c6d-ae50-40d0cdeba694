<script setup>
defineProps({
  isDisableHead: { type: <PERSON><PERSON><PERSON>, default: false },
  text: { type: String, required: true },
})
</script>

<template>
  <div class="survey__test-mode" :class="{ 'disable-head': isDisableHead }">
    <div class="test-mode-label">
      <svg class="survey__test-mode-letter-desktop" width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 21C16.299 21 21 16.299 21 10.5C21 4.70101 16.299 0 10.5 0C4.70101 0 0 4.70101 0 10.5C0 16.299 4.70101 21 10.5 21ZM7.5 6C6.67157 6 6 6.67157 6 7.5C6 8.32843 6.67157 9 7.5 9H9V15C9 15.8284 9.67157 16.5 10.5 16.5C11.3284 16.5 12 15.8284 12 15V9H13.5C14.3284 9 15 8.32843 15 7.5C15 6.67157 14.3284 6 13.5 6H7.5Z" fill="white" />
      </svg>
      <span class="survey__test-mode-text">{{ text }}</span>
      <svg class="survey__test-mode-letter-mobile" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.5 4.5C4.94772 4.5 4.5 4.94772 4.5 5.5C4.5 6.05228 4.94772 6.5 5.5 6.5H6.5V10.5C6.5 11.0523 6.94772 11.5 7.5 11.5C8.05228 11.5 8.5 11.0523 8.5 10.5V6.5H9.5C10.0523 6.5 10.5 6.05228 10.5 5.5C10.5 4.94772 10.0523 4.5 9.5 4.5H5.5Z" fill="#DADFE3" />
      </svg>
    </div>
  </div>
</template>

<style scoped>
.survey__test-mode {
  top: 0;
  right: 0;
  z-index: 2;
  display: flex;
  justify-content: flex-end;
  pointer-events: none;
  align-items: center;
  align-self: stretch;
}

.survey__test-mode.disable-head {
  position: static;
  height: auto !important;
}

.survey__test-mode.disable-head .test-mode-label {
  padding-top: 11px;
  padding-bottom: 13px;
}

.test-mode-label {
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #f96261;
  color: white;
  font-size: 14px !important;
  font-family: 'Roboto', 'Arial', sans-serif !important;
  font-weight: bold;
  padding: 0 20px;
}

.test-mode-label svg {
  width: 21px;
  height: 21px;
  margin-right: 10px;
}

.survey__test-mode-text {
  display: inline;
}

.survey__test-mode-letter-mobile {
  display: none;
}

.survey__test-mode-letter-desktop {
  display: inline;
}

@media screen and (max-width: 1023px) {
  .survey__test-mode-letter-mobile {
    display: flex;
  }

  .survey__test-mode-letter-desktop {
    display: none;
  }

  .survey__test-mode-text {
    display: none;
  }

  .survey__test-mode.disable-head .test-mode-label {
    padding-top: 16px;
    padding-bottom: 15px;
    padding-left: 0;
    padding-right: 0;
  }

  .test-mode-label {
    padding: 0;
    font-size: 12px !important;
    font-weight: 700;
    justify-content: center;
  }

  .test-mode-label svg {
    width: 14px;
    height: 14px;
    margin: 0;
  }
}
</style>
