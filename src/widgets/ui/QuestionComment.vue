<script setup>
import { computed, ref, watch } from 'vue'
import SlideTransition from './SlideTransition.vue'

const props = defineProps({
  controller: Object,
  texts: Object,
  formControlErrorStateMatcher: Function,
})

const value = ref(props.controller.value)
const isInvalid = computed(() => props.formControlErrorStateMatcher(props.controller.value))

watch(value, (newValue) => {
  props.controller.setValue(newValue)
})
</script>

<template>
  <SlideTransition v-if="controller.enabled">
    <div class="poll-question-comment">
      <label class="form-label">
        {{ texts?.commentLabel || controller.title }}
      </label>
      <textarea
        v-model="value"
        name="comment"
        class="form-control my-0"
        :data-min="controller.textFieldParam.min"
        :data-max="controller.textFieldParam.max"
        :maxlength="controller.textFieldParam.max"
        :placeholder="texts?.placeholderText || controller.placeholderText"
        :class="{ 'is-invalid': isInvalid }"
      />
      <SlideTransition>
        <div v-if="isInvalid" class="form-error">
          {{ controller.value.error }}
        </div>
      </SlideTransition>
    </div>
  </SlideTransition>
</template>

<style scoped>
.poll-question-comment {
  margin-top: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.form-error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
</style>
