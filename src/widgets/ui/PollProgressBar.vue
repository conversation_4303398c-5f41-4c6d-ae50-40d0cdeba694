<script setup>
import { computed } from 'vue'

const props = defineProps({
  interactedQuestionsCount: {
    type: Number,
    default: 0,
  },
  totalQuestionsCount: {
    type: Number,
    required: true,
  },
  percentage: {
    type: Number,
    default: 0,
  },
})

const touchedPercent = computed(() => {
  return props.percentage || Math.round((props.interactedQuestionsCount / props.totalQuestionsCount) * 100)
})
</script>

<template>
  <div class="poll-progress-bar">
    <div class="poll-progress-bar__inner">
      <span class="poll-progress-bar__text poll-progress-bar__text--left">
        <span>{{ interactedQuestionsCount > 0 ? interactedQuestionsCount : 0 }}</span>/<span>{{ totalQuestionsCount }}</span>
      </span>

      <div class="progress-line">
        <div
          class="progress-line__indicator"
          :style="{ width: `${touchedPercent}%` }"
          :data-progress="touchedPercent"
        />
      </div>

      <span class="poll-progress-bar__text poll-progress-bar__text--right">{{ touchedPercent }}%</span>
    </div>
  </div>
</template>

<style scoped>
.poll-progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 36px;
  padding-top: 4px;
  padding-bottom: 4px;
  color: var(--fqz-poll-text-on-bg);
}

.poll-progress-bar__inner {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 680px;
  width: 100%;
}

.poll-progress-bar__text {
  line-height: 1.1;
  font-weight: 700;
  font-size: 16px;
  color: var(--fqz-poll-text-on-bg);
  flex: 0 0 auto;
  margin-top: 1px;
}

.progress-line {
  flex-grow: 1;
  margin-left: 10px;
  margin-right: 10px;
  height: 10px;
  background-color: transparent;
  border-radius: 15px;
  border: 1px solid var(--fqz-poll-main-color);
}

.progress-line__indicator {
  background: var(--fqz-poll-main-color);
  height: 100%;
  transition: width 0.3s ease;
  will-change: width;
  border-radius: 15px;
}

.poll-progress-bar__text--left {
  width: 62px;
  text-align: right;
}

.poll-progress-bar__text--right {
  width: 40px;
}

@media (max-width: 679px) {
  .poll-progress-bar__text {
    font-size: 12px;
    line-height: 1.1;
  }
  .poll-progress-bar__text--left {
    width: 46px;
  }
  .poll-progress-bar__text--right {
    width: 30px;
  }
  .poll-progress-bar__inner {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
  }
}

@media (max-width: 321px) {
  .poll-progress-bar__text--left,
  .poll-progress-bar__text--right {
    width: 46px;
  }
}
</style>
