<script setup>
import { usePollStore } from '@/entities/poll/model/store'

const store = usePollStore()
function onSubmit(e) {
  e.preventDefault()
  setTimeout(() => {
    store.goNext()
  }, 10)
}
</script>

<template>
  <div class="poll-content">
    <form class="poll-content__form" novalidate @submit.prevent="onSubmit">
      <main class="poll-content__main">
        <slot v-if="$slots.startPage" name="startPage" />
        <template v-if="$slots.default">
          <slot />
        </template>
        <slot v-if="$slots.endPage" name="endPage" />
      </main>
      <footer v-if="$slots.footer" class="poll-content__footer">
        <slot name="footer" />
      </footer>
    </form>
  </div>
</template>

<style scoped>
.poll-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.poll-content__form {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.poll-content__main {
  flex-grow: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.poll-content__footer {
  /* Footer styles can be added here if needed */
}

/* Additional styles from survey.less */
.poll-content__background-covering {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #000000;
  opacity: 0.6;
}

.poll-content__container {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

@media (min-width: 661px) {
  .poll-content__unsubscribe-text {
    font-size: 19px;
    line-height: 22px;
  }
}
</style>
