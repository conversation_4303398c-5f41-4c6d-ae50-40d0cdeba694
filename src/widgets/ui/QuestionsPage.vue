<script setup>
import { CARD_SORTING_QUESTION, INTER_BLOCK, PRIORITY_QUESTION } from '@/entities/question/model/types'
import { usePollStore } from '@entities/poll/model/store'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { computed, toValue } from 'vue'
import PageHeader from './PageHeader.vue'
import QuestionItem from './QuestionItem.vue'

const props = defineProps({
  page: {
    type: Object,
    required: true,
  },
  unrequiredText: {
    type: String,
    required: false,
  },
})

const store = usePollStore()
const simplifiedStore = useSimplifiedStore()

const pageController = computed(() => store.pageController)

const isPriorityQuestion = question => (question.type === PRIORITY_QUESTION) || (question.type === CARD_SORTING_QUESTION)

const visibleQuestions = computed(() => {
  return toValue(props.page.visibleQuestions)
})

function isInterBlock(question) {
  return question.type === INTER_BLOCK
}

/**
 * Должа ли отображаться шапка вопроса
 * Мы не отображаем шапку для промежуточных блоков и блока "5 секунд"
 * @param {Question} question
 * @returns {boolean} true - если шапка должна отображаться, false - если нет
 */
function shouldShowPageHeader(question) {
  return !isInterBlock(question)
}

function setupQuestionRefs(ref, question) {
  question.domNodeElement.value = ref
}

function getPageItemClasses(question) {
  const classes = {
    'questions-page__item': true,
  }

  if (!pageController.value)
    return classes

  const isInitialized = pageController.value.initialized.value
  const disableAutoscroll = pageController.value.disableQuestionAutoscroll.value
  const multipleQuestionsOnPage = pageController.value.multipleQuestionsOnPage.value
  const allQuestionsAreWithinViewport = pageController.value.allQuestionsAreWithinViewport.value
  const activeQuestions = pageController.value.activeQuestions.value

  if (!multipleQuestionsOnPage || allQuestionsAreWithinViewport)
    return classes

  if (isInitialized && !disableAutoscroll)
    classes['questions-page__item--autoscroll-enabled'] = true

  if (isInitialized && activeQuestions.some(q => q.questionId === question.questionId))
    classes['questions-page__item--active-in-viewport'] = true

  return classes
}

function handleQuestionClick(question) {
  if (!pageController.value)
    return

  const isInitialized = pageController.value.initialized.value
  const disableAutoscroll = pageController.value.disableQuestionAutoscroll.value
  const allQuestionsAreWithinViewport = pageController.value.allQuestionsAreWithinViewport.value
  if (isInitialized && !disableAutoscroll && !allQuestionsAreWithinViewport) {
    const questionIndex = visibleQuestions.value.findIndex(q => q.questionId === question.questionId)
    pageController.value.scrollToQuestion(questionIndex)
  }
}

const questionsPageClasses = computed(() => {
  const classes = {
    'questions-page': true,
  }

  if (simplifiedStore.isSimplifiedMode) {
    classes['questions-page--simplified'] = true
  }

  if (simplifiedStore.isHelloBoardWidget) {
    classes['questions-page--hello-board-widget'] = true
  }

  const visible = toValue(visibleQuestions)
  const questionsLength = visible?.length

  if (questionsLength === 1) {
    classes['questions-page--single-question'] = true
  }

  if (questionsLength === 1 && isInterBlock(visible[0])) {
    classes['questions-page--single-question-inter-block'] = true
  }
  else if (questionsLength === 1 && !isInterBlock(visible[0])) {
    classes['questions-page--single-question-non-inter-block'] = true
  }

  if (props.page?.id === 'question-end-default') {
    classes['questions-page--end-default'] = true
  }

  return classes
})
</script>

<template>
  <div :class="questionsPageClasses">
    <TransitionGroup
      name="fade"
      tag="div"
      class="questions-page__items-container"
    >
      <div
        v-for="question in visibleQuestions"
        :key="question.questionId"
        :ref="(ref) => setupQuestionRefs(ref, question)"
        :data-question-id="question.questionId"
        :class="getPageItemClasses(question)"
        @click="handleQuestionClick(question)"
      >
        <PageHeader
          v-if="shouldShowPageHeader(question)"
          :description-html="question.description_html.value"
          :subdescription-html="question.subdescription.value"
          :unrequired-text="toValue(question.isRequired) ? '' : toValue(props.unrequiredText)"
        />
        <div
          class="questions-page__item-content"
          :class="[{ 'questions-page__item-content--inter-block': isInterBlock(question) },
                   { 'questions-page__item-content--priority-question': isPriorityQuestion(question) }]"
        >
          <div class="questions-page__item-content-inner">
            <QuestionItem
              :question="question"
            />
          </div>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<style scoped>
.questions-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  flex-grow: 1;
  justify-content: center;
  gap: 35px;
}

.questions-page__item {
  width: 100%;
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.questions-page__item--autoscroll-enabled {
  opacity: 0.5;
}

.questions-page__item--active-in-viewport {
  opacity: 1;
}

.questions-page__item-content {
  width: 100%;
  max-width: 680px;
  margin: 25px auto 0;
  background-color: var(--fqz-poll-main-place-color);
  color: var(--fqz-poll-text-on-place);
  border-radius: 8px;
  padding: 40px 50px;
  display: flex;
  flex-wrap: wrap;
}

.questions-page--simplified .questions-page__item-content {
  background-color: transparent;
  color: var(--fqz-poll-text-on-place);
  margin-top: 0;
  padding-top: 30px;
  padding-bottom: 1px;
  padding-left: 0;
  padding-right: 0;
}

.questions-page--in-iframe .questions-page__item-content {
  padding-bottom: 1px;
}

.questions-page__item-content-inner {
  width: 100%;
  min-width: 100%;
  max-width: 100%;
}

.questions-page--simplified :deep(.page-header) {
  margin-left: 0;
  margin-right: 0;
}

.questions-page--simplified :deep(.page-header__description) {
  line-height: 1.1;
}

.questions-page--hello-board-widget :deep(.page-header) {
  margin-bottom: 10px;
}

.questions-page__item-content--inter-block {
  max-width: none;
  background-color: transparent;
  padding: 0;
  overflow: visible;
  flex-grow: 1;
}

.questions-page__item-content--inter-block .questions-page__item-content-inner {
  padding: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.questions-page--simplified .questions-page__item-content--inter-block {
  padding-top: 0;
}

/* .questions-page--simplified.questions-page--single-question:not(.questions-page--end-default)
  .questions-page__item-content--inter-block {
  padding-bottom: 50px !important;
} */

.questions-page--simplified .questions-page__item-content--inter-block:has(.points-report-screen) {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.questions-page__item-content--priority-question {
  padding: 0;
  background-color: transparent;
  color: var(--fqz-poll-text-on-bg);
}

.questions-page__items-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 35px;
}

/* Add transition styles */
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@media (max-width: 679px) {
  .questions-page {
    gap: 25px;
  }

  .questions-page__item-content {
    margin-top: 25px;
    padding: 40px 15px;
  }

  .questions-page__item-content--inter-block {
    padding: 0;
    margin: 0;
  }

  .questions-page__item-content--priority-question {
    padding: 0;
    margin: 25px 0 0;
  }

  .questions-page__item-content-inner {
    padding: 0;
  }

  .questions-page__items-container {
    gap: 25px;
  }

  .questions-page--simplified .questions-page__item-content {
    padding: 20px 0 20px;
  }

  .questions-page--simplified .questions-page__item-content--inter-block .questions-page__item-content-inner {
    padding-bottom: 0px;
  }

  .questions-page--simplified.questions-page--single-question:not(.questions-page--end-default)
    .questions-page__item-content--inter-block:has(.points-report-screen) {
    padding-bottom: 0 !important;
  }
}
</style>
