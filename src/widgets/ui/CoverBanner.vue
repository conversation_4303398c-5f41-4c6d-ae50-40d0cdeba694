<script setup>
import { usePollStore } from '@entities/poll/model/store'
import { getBaseAssetsUrl } from '@shared/api'
import { computed } from 'vue'

const store = usePollStore()
const baseUrl = getBaseAssetsUrl()
const image = computed(() => {
  return store.design?.cover_image?.startsWith?.('/')
    ? `${baseUrl}${store.design?.cover_image}`
    : store.design?.cover_image
})

const isFullWidth = computed(() => !!store.design?.cover_full_width)
const imageStyle = computed(() => ({
  'object-position': ['top', 'center', 'bottom'][store.design?.cover_position || 0],
}))
const containerClass = computed(() => ({
  'cover-banner': true,
  'cover-banner_fullscreen': isFullWidth.value,
  'cover-banner_with-margin': !isFullWidth.value,
}))
</script>

<template>
  <div
    :class="containerClass"
  >
    <img
      :src="image"
      class="cover-banner__img"
      :style="imageStyle"
    >
  </div>
</template>

<style scoped>
.cover-banner {
  height: 160px;
  width: 100%;
  max-width: 680px;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
}
.cover-banner_fullscreen {
  max-width: 100%;
  border-radius: 0;
}
.cover-banner__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.cover-banner_with-margin {
  margin-top: 10px;
}
@media (max-width: 679px) {
  .cover-banner {
    position: relative;
    height: auto;
    padding-top: 23.5%;
    width: 100%;
    max-width: 100%;
    border-radius: 0;
  }

  .cover-banner_with-margin {
    margin-top: 0;
  }

  .cover-banner__img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
@media (max-width: 365px) {
  .cover-banner {
    padding-top: 84px;
  }
}
</style>
