<script setup>
import { usePollStore } from '@/entities/poll/model/store'
import { useScrollShadows } from '@shared/composables/useScrollShadows'
import { dispatchGlobalEvent } from '@shared/helpers/dom'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { createReusableTemplate, useResizeObserver } from '@vueuse/core'
import debounce from 'lodash.debounce'
import Simplebar from 'simplebar-vue'
import { computed, onMounted, ref, watch } from 'vue'
import QuestionsPage from './QuestionsPage.vue'

const store = usePollStore()
const simplifiedStore = useSimplifiedStore()

const filteredPages = computed(() => store.pages && store.pages.filter(page => page.id === store.activePageId))
const containerRef = ref(null)
const simplebarRef = ref(null)
const simplebarScrollElementRef = computed(() => simplebarRef.value?.scrollElement)
const debouncedResize = debounce(() => {
  dispatchGlobalEvent('resize')
  dispatchGlobalEvent('scroll')
}, 100)

const { showShadowTop, showShadowBottom } = useScrollShadows(simplebarScrollElementRef, true)

onMounted(() => {
  if (containerRef.value) {
    useResizeObserver(containerRef, debouncedResize)
  }
})

const pagesContainerClasses = computed(() => ({
  'question-pages-container': true,
  'question-pages-container--end-screen': false,
  'question-pages-container--simplified': simplifiedStore.isSimplifiedMode,
  'question-pages-container--in-iframe': simplifiedStore.isInIframe,
  'question-pages-container--with-shadows': simplifiedStore.isSimplifiedMode && !simplifiedStore.isInIframe,
  'question-pages-container--shadow-top': showShadowTop.value,
  'question-pages-container--shadow-bottom': showShadowBottom.value,
}))

const isStartScreenPage = computed(() => {
  return filteredPages.value?.length === 1 && filteredPages.value[0]?.type === 'start'
})

const isEndScreenPage = computed(() => {
  return filteredPages.value?.length === 1 && filteredPages.value[0]?.type === 'end'
})

const pageQuestionsCustomScrollbarWrapperClasses = computed(() => ({
  'page-questions-container-custom-scrollbar-wrapper': true,
  'page-questions-container-custom-scrollbar-wrapper--start-screen': isStartScreenPage.value,
  'page-questions-container-custom-scrollbar-wrapper--end-screen': isEndScreenPage.value,
}))

watch(filteredPages, () => {
  setTimeout(() => {
    pagesContainerClasses.value['question-pages-container--end-screen'] = isEndScreenPage.value
  }, 30)
}, { immediate: true })

const simplebarClasses = computed(() => ({
  'simplebar-custom simplebar-themed page-questions-scrollbar-wrapper__simplebar': true,
  'page-questions-scrollbar-wrapper__simplebar--shadow-top': showShadowTop.value,
  'page-questions-scrollbar-wrapper__simplebar--shadow-bottom': showShadowBottom.value,
}))

const [DefineTemplate, ReuseTemplate] = createReusableTemplate()
</script>

<template>
  <DefineTemplate>
    <div v-if="store.pages.length > 0" ref="containerRef" :class="pagesContainerClasses">
      <TransitionGroup :name="store.navigationDirection === 'next' ? 'slide-next' : 'slide-prev'">
        <div v-for="(page) in filteredPages" :key="page.id">
          <QuestionsPage
            :page="page"
            :unrequired-text="store.unrequiredText"
          />
        </div>
      </TransitionGroup>
    </div>
  </DefineTemplate>

  <!-- When in iframe mode, wrap the container in a custom scrollbar -->
  <div v-if="simplifiedStore.isInIframe" :class="pageQuestionsCustomScrollbarWrapperClasses">
    <Simplebar
      ref="simplebarRef"
      :class="simplebarClasses"
      data-simplebar-auto-hide="false"
      :class-names="{
        track: 'simplebar-track fz-simplebar-track page-questions-scrollbar-wrapper__track',
        content: 'simplebar-content fz-simplebar-content page-questions-scrollbar-wrapper__content',
        contentWrapper: 'simplebar-content-wrapper page-questions-scrollbar-wrapper__content-wrapper',
        scrollbar: 'simplebar-scrollbar page-questions-scrollbar-wrapper__scrollbar',
      }"
    >
      <ReuseTemplate />
    </Simplebar>
  </div>
  <!-- default rendering when not in iframe -->
  <template v-else>
    <ReuseTemplate />
  </template>
</template>

<style scoped>
.question-pages-container {
  position: relative;
  overflow: hidden;
}

.question-pages-container--simplified {
  max-width: var(--fqz-poll-simplified-max-width);
  padding-left: 50px;
  padding-right: 50px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.question-pages-container--end-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

.question-pages-container--end-screen > div {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
}

.question-pages-container--end-screen :deep(.questions-page) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
}

.question-pages-container--end-screen :deep(.questions-page__items-container) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
}

.slide-next-leave-active,
.slide-prev-leave-active {
  display: none;
}

.slide-next-move,
.slide-prev-move {
  position: absolute;
}

.slide-next-enter-active,
.slide-prev-enter-active {
  transition: all 0.3s;
}

.slide-next-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-prev-enter-from {
  transform: translateX(-30px);
  opacity: 0;
}

.page-questions-container-custom-scrollbar-wrapper {
  flex: 1 1 auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.page-questions-container-custom-scrollbar-wrapper--start-screen,
.page-questions-container-custom-scrollbar-wrapper--end-screen {
  padding-bottom: 50px;
}

/* Gradient shadows for scrollbar */
:global(.page-questions-scrollbar-wrapper__simplebar::before),
:global(.page-questions-scrollbar-wrapper__simplebar::after) {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 15px;
  background: linear-gradient(360deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s;
  display: var(--show-gradient-shadow);
}

:global(.page-questions-scrollbar-wrapper__simplebar::before) {
  background: linear-gradient(180deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
  top: 0;
  bottom: auto;
}

:global(.page-questions-scrollbar-wrapper__simplebar--shadow-top::before),
:global(.page-questions-scrollbar-wrapper__simplebar--shadow-bottom::after) {
  opacity: 1;
}

:global(.page-questions-scrollbar-wrapper__simplebar) {
  flex: 0 1 auto !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
}

:global(.page-questions-scrollbar-wrapper__content) {
  height: 100%;
}

:global(.simplebar-custom .simplebar-vertical.simplebar-track.page-questions-scrollbar-wrapper__track) {
  width: 20px;
  z-index: 4;
}

:global(.simplebar-custom .simplebar-vertical .simplebar-scrollbar.page-questions-scrollbar-wrapper__scrollbar) {
  width: 16px;
}

.question-pages-container--with-shadows {
  position: relative;
}

.question-pages-container--with-shadows::before,
.question-pages-container--with-shadows::after {
  content: '';
  position: absolute;
  left: 0;
  width: 100%;
  height: 15px;
  pointer-events: none;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s;
}

.question-pages-container--with-shadows::before {
  top: 0;
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.question-pages-container--with-shadows::after {
  bottom: 0;
  background: linear-gradient(360deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.question-pages-container--shadow-top::before {
  opacity: 1;
}

.question-pages-container--shadow-bottom::after {
  opacity: 1;
}

.question-pages-container--simplified :deep(.questions-page__item:last-child .questions-page__item-content) {
  margin-bottom: 0;
  padding-bottom: 0;
}

.question-pages-container--in-iframe {
  padding-bottom: 1px;
}

.question-pages-container--simplified :deep(.questions-page__items-container) {
  gap: 0;
}

.question-pages-container--simplified :deep(.questions-page__item) {
  padding-top: 30px;
  padding-bottom: 30px;
  position: relative;
}

.question-pages-container--simplified :deep(.questions-page__item:first-child) {
  margin-top: 0;
  padding-top: 0;
}

.question-pages-container--simplified :deep(.questions-page__item::before) {
  content: '';
  border-bottom: 0;
  margin-bottom: 0;
  padding-bottom: 0;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

.question-pages-container--simplified :deep(.questions-page__item:last-child) {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: 0;
}

.question-pages-container--simplified :deep(.questions-page__item:last-child::before) {
  display: none;
}

@media (max-width: 679px) {
  .question-pages-container--simplified {
    padding-left: 20px;
    padding-right: 20px;
  }

  :global(.simplebar-custom .simplebar-vertical.simplebar-track.page-questions-scrollbar-wrapper__track) {
    width: 10px;
  }

  :global(.simplebar-custom .simplebar-vertical .simplebar-scrollbar.page-questions-scrollbar-wrapper__scrollbar) {
    width: 16px;
  }

  .page-questions-container-custom-scrollbar-wrapper--start-screen {
    padding-bottom: 30px;
  }

  .page-questions-container-custom-scrollbar-wrapper--end-screen {
    padding-top: 30px;
  }

  .question-pages-container--simplified :deep(.questions-page__item) {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .question-pages-container--simplified :deep(.questions-page__item .questions-page__item-content) {
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .question-pages-container--simplified :deep(.questions-page__item:last-child .questions-page__item-content) {
    margin-bottom: 0;
    padding-bottom: 0;
  }
}
</style>
