<script setup>
import { POLL_NAVIGATION_TYPE } from '@/shared/constants'
import { usePreviewStore } from '@shared/store/previewStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import FcPaginator from '@shared/ui/FcPaginator.vue'
import throttle from 'lodash.throttle'
import { computed, onMounted, onUnmounted, ref, toValue } from 'vue'
import PollProgressBar from './PollProgressBar.vue'

const props = defineProps({
  type: {
    type: Number,
    default: POLL_NAVIGATION_TYPE.NUMBERS,
  },
  questions: {
    type: Array,
    required: true,
  },
  translation: Object,
  isFinishScreen: Boolean,
  nextQuestionIndex: Number,
  activeId: [String, Number],
  pagesMode: Boolean,
  pages: Array,
  activePageId: [String, Number],
  activePageIndex: Number,
  isHeaderEnabled: Boolean,
  progressPercentage: {
    type: Number,
    default: 0,
  },
  interactedQuestionsCount: {
    type: Number,
    default: 0,
  },
  visibleQuestionsCount: {
    type: Number,
    default: 0,
  },
})
// Add previewStore
const previewStore = usePreviewStore()
const isPreviewMode = computed(() => previewStore.isPreviewMode)
const isFullPreview = computed(() => previewStore.isFullPreview)

// Add simplified store
const simplifiedStore = useSimplifiedStore()
const showPaginator = computed(() => !simplifiedStore.isSimplifiedMode)

/**
 * Список отфильтрованных страниц
 * Без начальных и конечных экранов
 */
const filteredPages = computed(() => {
  let lastVisibleInNavigationIndex = 0
  let i = 0
  return props.pages
    .map((p) => {
      if (!toValue(p.visibleInNavigation)) {
        p.realIndex = lastVisibleInNavigationIndex
      }
      else {
        lastVisibleInNavigationIndex = i
        p.realIndex = i
        i++
      }
      return p
    })
    .filter(p => p.type !== 'start' && p.type !== 'end')
})

/**
 * Список страниц, которые должны отображаться в навигации
 * Без начальных и конечных экранов
 * И без промежуточных блоков, у которых выключена опция "Отображать номера страниц"
 */
const visibleInNavigationPages = computed(() => {
  return filteredPages.value
    .filter(p => toValue(p.visibleInNavigation))
})

const realPageIndex = computed(() => {
  const currentIndex = visibleInNavigationPages.value.findIndex(p => p.id === props.activePageId)
  if (currentIndex !== -1) {
    return currentIndex
  }

  const page = filteredPages.value.find(p => p.id === props.activePageId)

  return page?.realIndex === undefined ? -1 : page.realIndex
})

// Количество пройденных страниц
// используется для отображения прогресса в прогрессбаре
// const touchedCount = computed(() => {
//   const index = realPageIndex.value
//   const isLastIndex = index === visibleInNavigationPagesCount.value - 1
//   const page = filteredPages.value.find(p => props.activePageId === p.id)

//   if (isLastIndex && page?.type === 'intermediate') {
//     return index + 1
//   }

//   return index
// })

const paginatorItems = computed(() => {
  return visibleInNavigationPages.value.map((p) => {
    return {
      id: p.id,
      label: toValue(p.name),
      blocked: toValue(p.blocked),
      visible: toValue(p.isVisible),
      realIndex: p.realIndex,
    }
  })
})

const paginatorActive = computed(() => {
  return props.pagesMode ? props.activePageId : props.activeId
})

const pageScrollY = ref(0)

const paginatorWrapperClasses = computed(() => {
  return {
    'paginator-wrapper': true,
    'paginator-wrapper--progressbar': props.type === POLL_NAVIGATION_TYPE.PROGRESSBAR,
  }
})

const paginatorClasses = computed(() => {
  return {
    'poll-paginator': true,
    'poll-paginator--progressbar': props.type === POLL_NAVIGATION_TYPE.PROGRESSBAR,
    'poll-paginator--header-enabled': props.isHeaderEnabled,
    'poll-paginator--sticky': pageScrollY.value > 0,
  }
})

const paginatorWrapperRef = ref(null)

const scrollElement = ref(null)

const handleScrollAndResize = throttle(() => {
  // Update to use scrollElement's scrollTop if available
  pageScrollY.value = scrollElement.value instanceof Window
    ? window.scrollY
    : scrollElement.value?.scrollTop || 0
}, 300)

onMounted(() => {
  // Initialize scroll element based on preview mode
  const nonFullPreview = isPreviewMode.value && !isFullPreview.value
  const simplebarContentWrapper = document.querySelector('.app-root-container .simplebar-content-wrapper')
  scrollElement.value = nonFullPreview ? simplebarContentWrapper : window

  // Initialize sticky progressbar
  if (props.type === POLL_NAVIGATION_TYPE.PROGRESSBAR && scrollElement.value) {
    handleScrollAndResize()
    scrollElement.value.addEventListener('scroll', handleScrollAndResize)
    scrollElement.value.addEventListener('resize', handleScrollAndResize)
  }
})

onUnmounted(() => {
  if (scrollElement.value) {
    scrollElement.value.removeEventListener('scroll', handleScrollAndResize)
    scrollElement.value.removeEventListener('resize', handleScrollAndResize)
  }
})
</script>

<template>
  <div v-if="showPaginator" ref="paginatorWrapperRef" :class="paginatorWrapperClasses" :style="{ '--paginator-wrapper-distance-to-top': `${paginatorWrapperDistanceToTop || 45}px` }">
    <div :class="paginatorClasses">
      <FcPaginator
        v-if="type === POLL_NAVIGATION_TYPE.NUMBERS" :items="paginatorItems" :active="paginatorActive" :mode="mode"
        :real-index="realPageIndex"
        :active-index="activePageIndex"
        class="poll-paginator__paginator"
      />
      <PollProgressBar
        v-else-if="type === POLL_NAVIGATION_TYPE.PROGRESSBAR"
        :interacted-questions-count="interactedQuestionsCount"
        :total-questions-count="visibleQuestionsCount"
        :percentage="progressPercentage"
      />
    </div>
  </div>
</template>

<style scoped>
.paginator-wrapper {
  width: 100%;
  position: relative;
}

.poll-paginator {
  transition: background-color 1s ease;
}

.poll-paginator--sticky {
  background-color: var(--fqz-poll-buttons-bg);
}

.poll-paginator--sticky.poll-paginator--header-enabled {
  background-color: var(--fqz-poll-progressbar-bg);
}

.poll-paginator :deep(.fc-paginator) {
  color: white;
  width: 100%;
}

.poll-paginator :deep(.fc-paginator-item__label) {
  color: var(--fqz-poll-text-on-bg);
}
</style>
