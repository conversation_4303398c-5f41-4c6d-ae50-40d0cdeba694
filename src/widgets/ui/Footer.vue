<script setup>
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { computed } from 'vue'

const props = defineProps({
  showCopyright: {
    type: Boolean,
    default: true,
  },
})

const translationsStore = useTranslationsStore()
const simplifiedStore = useSimplifiedStore()

const showFooter = computed(() => !simplifiedStore.isSimplifiedMode && props.showCopyright)

const footerClasses = computed(() => ({
  'survey-footer': true,
}))
</script>

<template>
  <div v-if="showFooter" :class="footerClasses">
    <a target="_blank" class="survey-footer__copyright" href="https://foquz.ru" @click="handleCopyrightClick">{{ translationsStore.t("Создано в Foquz") }}</a>
  </div>
</template>

<style scoped>
.survey-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 15px;
  font-size: inherit;
  z-index: 10;
  position: relative;
  width: 100%;
  gap: 15px;
}

.survey-footer__copyright {
  color: var(--fqz-poll-link-color);
  text-decoration: underline;
  font-size: 12px;
  padding-bottom: 15px;
  line-height: 1.2;
}

.survey-footer__copyright::visited {
  color: var(--fqz-poll-link-color);
}
</style>
