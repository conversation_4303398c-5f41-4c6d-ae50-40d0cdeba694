<script setup>
import {
  ADDRESS_QUESTION,
  CARD_SORTING_QUESTION,
  CLASSIFIER_QUESTION,
  DATE_QUESTION,
  DIFF_QUESTION,
  DISTRIBUTION_SCALE_QUESTION,
  FILE_QUESTION,
  F<PERSON><PERSON>LS_QUESTION,
  FIRST_CLICK_TEST_QUESTION,
  GALLERY_QUESTION,
  INTER_BLOCK,
  MATRIX_3D_QUESTION,
  MATRIX_QUESTION,
  MEDIA_VARIANTS_QUESTION,
  NPS_QUESTION,
  PRIORITY_QUESTION,
  QUIZ_QUESTION,
  RATING_QUESTION,
  SCALE_QUESTION,
  SMILE_QUESTION,
  STAR_VARIANTS_QUESTION,
  STARS_QUESTION,
  TEXT_QUESTION,
  VARIANTS_QUESTION,
} from '@/entities/question/model/types'

import AddressQuestion from '@/entities/question/ui/AddressQuestion.vue'
import CardSortingQuestion from '@/entities/question/ui/CardSortingQuestion.vue'
import ClassifierQuestion from '@/entities/question/ui/ClassifierQuestion.vue'
import DateQuestion from '@/entities/question/ui/DateQuestion.vue'
import DiffQuestion from '@/entities/question/ui/DiffQuestion.vue'
import DistributionScaleQuestion from '@/entities/question/ui/DistributionScaleQuestion.vue'
import FileQuestion from '@/entities/question/ui/FileQuestion.vue'
import GalleryQuestion from '@/entities/question/ui/GalleryQuestion.vue'
import InterBlockQuestion from '@/entities/question/ui/InterBlockQuestion.vue'
import MediaVariantsQuestion from '@/entities/question/ui/MediaVariantsQuestion.vue'
import NpsRatingQuestion from '@/entities/question/ui/NpsRatingQuestion.vue'
import PriorityQuestion from '@/entities/question/ui/PriorityQuestion.vue'
import QuizQuestion from '@/entities/question/ui/QuizQuestion.vue'
import RatingScaleQuestion from '@/entities/question/ui/RatingScaleQuestion.vue'
import ScaleQuestion from '@/entities/question/ui/ScaleQuestion.vue'
import SmileRatingQuestion from '@/entities/question/ui/SmileRatingQuestion.vue'
import StarRatingQuestion from '@/entities/question/ui/StarRatingQuestion.vue'
import StarRatingVariantsQuestion from '@/entities/question/ui/StarRatingVariantsQuestion.vue'
import TextQuestion from '@/entities/question/ui/TextQuestion.vue'
import VariantsQuestion from '@/entities/question/ui/VariantsQuestion.vue'
import FirstClickTestQuestion from '@entities/question/ui/FirstClickTestQuestion.vue'
import Matrix3dQuestion from '@entities/question/ui/matrix/Matrix3dQuestion.vue'
import MatrixQuestion from '@entities/question/ui/matrix/MatrixQuestion.vue'
import { computed } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const QuestionComponent = computed(() => {
  switch (props.question.type) {
    case INTER_BLOCK:
      return InterBlockQuestion
    case STAR_VARIANTS_QUESTION:
      return StarRatingVariantsQuestion
    case STARS_QUESTION:
      return StarRatingQuestion
    case SMILE_QUESTION:
      return SmileRatingQuestion
    case RATING_QUESTION:
      return RatingScaleQuestion
    case TEXT_QUESTION:
      return TextQuestion
    case NPS_QUESTION:
      return NpsRatingQuestion
    case ADDRESS_QUESTION:
      return AddressQuestion
    case FILIALS_QUESTION:
      return ClassifierQuestion
    case DATE_QUESTION:
      return DateQuestion
    case VARIANTS_QUESTION:
      return VariantsQuestion
    case QUIZ_QUESTION:
      return QuizQuestion
    case PRIORITY_QUESTION:
      return PriorityQuestion
    case SCALE_QUESTION:
      return ScaleQuestion
    case DISTRIBUTION_SCALE_QUESTION:
      return DistributionScaleQuestion
    case DIFF_QUESTION:
      return DiffQuestion
    case CLASSIFIER_QUESTION:
      return ClassifierQuestion
    case MATRIX_QUESTION:
      return MatrixQuestion
    case FILE_QUESTION:
      return FileQuestion
    case MEDIA_VARIANTS_QUESTION:
      return MediaVariantsQuestion
    case GALLERY_QUESTION:
      return GalleryQuestion
    case MATRIX_3D_QUESTION:
      return Matrix3dQuestion
    case CARD_SORTING_QUESTION:
      return CardSortingQuestion
    case FIRST_CLICK_TEST_QUESTION:
      return FirstClickTestQuestion
    default:
      return null
  }
})
</script>

<template>
  <component
    :is="QuestionComponent"
    v-if="QuestionComponent"
    :question="question"
  />
  <div v-else>
    Unsupported question type: {{ question.type }}
  </div>
</template>
