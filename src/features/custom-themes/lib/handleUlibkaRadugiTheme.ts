interface UlibkaRadugiThemeConfig {
  templateId: number
  specialConditions?: {
    urForceDesignTheme?: boolean
  }
}

/**
 * Applies the Ulibka Radugi theme styling based on the provided configuration
 * @param config - Configuration object for the Ulibka Radugi theme
 */
export function handleUlibkaRadugiTheme(config: UlibkaRadugiThemeConfig): void {
  // Only apply if templateId is 1 (ulibka-radugi theme)
  if (config.templateId !== 1)
    return

  // Add the base theme class
  document.body.classList.add('template-1-theme')

  // If urForceDesignTheme is true, add the force-theme-design class
  if (config.specialConditions?.urForceDesignTheme) {
    document.body.classList.add('template-1-theme--force-theme-design')
  }
}
