interface ApplyCustomStylesConfig {
  type: 0 | 1
  style?: string
}

export function applyCustomStylesCss(config: ApplyCustomStylesConfig): void {
  if (!config.style) {
    return
  }
  const element = config.type === 0
    ? createLinkElement(config.style)
    : createStyleElement(config.style)

  document.head.appendChild(element)
}

function createLinkElement(href: string): HTMLLinkElement {
  const link = document.createElement('link')
  link.rel = 'stylesheet'
  link.href = href
  return link
}

function createStyleElement(css: string): HTMLStyleElement {
  const style = document.createElement('style')
  style.type = 'text/css'
  style.textContent = css
  return style
}
