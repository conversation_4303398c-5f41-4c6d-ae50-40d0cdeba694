import type { CustomStyleConfig, CustomThemeConfig, ThemeName } from './types'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { applyCustomStylesCss } from '../lib/applyCustomStylesCss'
import { handleUlibkaRadugiTheme } from '../lib/handleUlibkaRadugiTheme'

export const useThemeStore = defineStore('theme', () => {
  const currentTheme = ref<ThemeName | null>(null)

  function initializeTheme(config: CustomThemeConfig) {
    // If templateId is 0, no custom theme is enabled
    if (!config || config.templateId === 0) {
      return
    }

    // Handle ulibka-radugi theme (templateId === 1)
    if (config.templateId === 1) {
      handleUlibkaRadugiTheme(config)
      currentTheme.value = 'template-1-theme'
    }

    // Future themes can be added here with additional templateId checks
  }

  function applyCustomStyles(config: CustomStyleConfig) {
    if (!config.style) {
      return
    }
    applyCustomStylesCss(config)
  }

  return {
    currentTheme,
    initializeTheme,
    applyCustomStyles,
  }
})
