@import './setup.css';

.template-1-theme {
  /* Primary colors */

  --template-1-primary-active: #8327d8;
  --template-1-primary: #9b51e0;
  --template-1-primary-hover: #b86bd9;
  --template-1-primary-active-sub: #dab4fe;
  --template-1-primary-hover-sub: #ebd5ff;
  --template-1-primary-bg: #f4e9ff;

  /* Secondary colors */
  --template-1-secondary-active: #379564;
  --template-1-secondary: rgba(69, 185, 124, 1);
  --template-1-secondary-hover: #50db92;
  --template-1-secondary-active-sub: #86efac;
  --template-1-secondary-hover-sub: #ecfef7;
  --template-1-secondary-bg: #bbf7d0;

  /* Base colors */
  --template-1-black-default: #353a41;
  --template-1-black-main: #6e8098;
  --template-1-gray-default: #a7aeb7;
  --template-1-gray-main: #e9e9e9;
  --template-1-gray-bg-01: #f4f4f4;
  --template-1-white-main: #ffffff;
  --template-1-white-text-50: #fafafa;

  /* Typography */
  --template-1-text-h5-font-size: 24px;
  --template-1-text-h5-line-height: 1.33;
  --template-1-text-h6-font-size: 20px;
  --template-1-text-h6-line-height: 1.2;
  --template-1-text-subtitle-1-16-regular-font-size: 16px;
  --template-1-text-subtitle-1-16-regular-line-height: 1.5;
  --template-1-text-subtitle-2-14-regular-font-size: 14px;
  --template-1-text-subtitle-2-14-regular-line-height: 1.715;
  --template-1-text-caption-12-regular-font-size: 12px;
  --template-1-text-caption-12-regular-line-height: 1.33;
  --template-1-text-overline-10-regular-font-size: 10px;
  --template-1-text-overline-10-regular-line-height: 1.2;

  /* Scrollbar colors */
  --fqz-poll-scrollbar-track: var(--template-1-gray-main);
  --fqz-poll-scrollbar-thumb: var(--template-1-gray-default);

  /* Ulibka Radugi button styles */
  --template-1-button-transition: opacity 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  --template-1-button-padding-x: 24px;
  --template-1-button-radius: 8px;

  /* Previous button specific variables */
  --template-1-prev-button-bg: transparent;
  --template-1-prev-button-border: var(--template-1-primary);
  --template-1-prev-button-color: var(--template-1-primary);
  --template-1-prev-button-hover-bg: var(--template-1-primary-bg);
  --template-1-prev-button-active-bg: var(--template-1-primary-hover-sub);

  /* Map to application variables */
  --fqz-poll-main-color: var(--template-1-primary-active);
  --fqz-poll-main-color-selected: var(--template-1-primary-active);
  --fqz-poll-bg-color: var(--template-1-white-main);
  --fqz-poll-text-on-bg: var(--template-1-black-default);
  --fqz-poll-text-on-place: var(--template-1-black-default);
  --fqz-poll-main-place-color: var(--template-1-white-main);
  --fqz-poll-button-text: var(--template-1-white-main);
  --fqz-poll-title-font-size: var(--template-1-text-h6-font-size);
  --show-gradient-shadow: none;

  /* Main buttons styles */
  --fqz-poll-next-button-radius: 8px;

  --fqz-poll-back-button-radius: 8px;
  --fqz-poll-start-button-radius: 8px;

  --fqz-poll-buttons-bg: var(--template-1-white-main);

  /* Layout */

  --template-1-content-padding-desktop-x: 24px;

  /* Размер контента. Основной размер плюс паддинги: 830 + 24px + 24px */
  --template-1-content-max-width: 878px;

  --template-1-content-padding-mobile-x: 16px;
  --template-1-intermediate-block-content-max-width: 840px;
  --template-1-main-border-color: #cfd8dc;
  --template-1-header-height: 80px;

  /* Question Types */
  --template-1-input-border-focus: var(--template-1-secondary);
  --template-1-input-max-width: 580px;
  --template-1-input-max-width-mobile: 335px;
  --template-1-nps-selected: var(--template-1-primary);
  --template-1-star-rating-color: var(--template-1-primary);
  --template-1-gallery-rating-color: var(--template-1-primary);
  --template-1-icon-color: var(--template-1-gray-default);
  --template-1-border-color: var(--template-1-gray-main);
  --template-1-selected-bg: var(--template-1-secondary-bg);
  --template-1-selected-border: var(--template-1-secondary);

  /* Additional mappings */
  --fqz-poll-simplified-max-width: var(--template-1-content-max-width);
  --fqz-poll-header-height: var(--template-1-header-height);
  --fqz-poll-header-shadow: var(--template-1-header-shadow);
  --fqz-poll-input-border-focus: var(--template-1-input-border-focus);
  --fqz-poll-input-max-width: var(--template-1-input-max-width);
  --fqz-poll-input-max-width-mobile: var(--template-1-input-max-width-mobile);
}

/* Override scrollbar styles for the theme */
.template-1-theme .simplebar-themed .simplebar-track:before {
  background: var(--fqz-poll-scrollbar-track);
}

.template-1-theme .simplebar-themed .simplebar-scrollbar:before {
  background: var(--fqz-poll-scrollbar-thumb);
}

.template-1-theme .app-root {
  background-color: var(--template-1-white-main);
}

.template-1-theme .survey-header {
  padding-left: 24px;
  padding-right: 4px;
  border-bottom: 1px solid var(--template-1-gray-main);
  transition: border-color 0.3s ease;
}

.template-1-theme .poll-paginator--progressbar {
  border-bottom: 1px solid transparent;
  position: relative;
}

.template-1-theme .survey-header:after,
.template-1-theme .poll-paginator--progressbar:after {
  content: '';
  position: absolute;
  top: auto;
  left: 0;
  width: 100%;
  height: 80px;
  background: linear-gradient(360deg, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.04) 100%);
  bottom: -80px;
  z-index: 1;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.template-1-theme .poll-paginator--progressbar:after {
  opacity: 0;
}

.template-1-theme .poll-progress-bar {
  min-height: 80px;
}

.template-1-theme .poll-paginator--progressbar {
  transition: border-color 0.3s ease;
}

.template-1-theme .app-root__top:has(.poll-paginator--sticky) .survey-header {
  border-bottom-color: transparent !important;
}

.template-1-theme .app-root__top:has(.poll-paginator--sticky) .poll-paginator--progressbar:after {
  opacity: 1;
}

.template-1-theme .app-root__top:has(.poll-paginator--sticky) .survey-header:after {
  opacity: 0;
}

.template-1-theme .app-root__top:has(.poll-paginator--sticky) .poll-paginator--progressbar {
  border-bottom-color: var(--template-1-gray-main) !important;
}

/* Interscreen */

.template-1-theme .survey-interscreen .text a {
  text-decoration: none;
}

.template-1-theme .survey-interscreen .text strong {
  font-weight: 500 !important;
}

.template-1-theme .survey-interscreen--start-screen .interscreen-images,
.template-1-theme .survey-interscreen--end-screen .interscreen-images {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 380px;
  margin: 0;
  padding: 0;
}

.template-1-theme .survey-interscreen--start-screen .interscreen-image,
.template-1-theme .survey-interscreen--end-screen .interscreen-image {
  padding: 0;
  margin: 0;
}

.template-1-theme .survey-interscreen .interscreen-image {
  border-radius: 0;
}

.template-1-theme .survey-interscreen--start-screen,
.template-1-theme .survey-interscreen--end-screen {
  padding-bottom: 30px;
}

.template-1-theme .survey-interscreen--start-screen .survey-interscreen__wrapper,
.template-1-theme .survey-interscreen--end-screen .survey-interscreen__wrapper {
  max-width: 100%;
  padding-left: 410px;
  padding-right: 0;
  min-height: 379px;
  position: relative;
  justify-content: center;
  align-items: flex-start;
}

.template-1-theme .survey-interscreen--default .survey-interscreen__wrapper {
  padding-left: 0;
  padding-right: 0;
  min-height: 0;
}

.template-1-theme .survey-interscreen--start-screen .text > p > span,
.template-1-theme .survey-interscreen--end-screen .text > p > span {
  line-height: 1.5;
}

.template-1-theme .survey-interscreen--start-screen .survey-interscreen__button-container,
.template-1-theme .survey-interscreen--end-screen .survey-interscreen__button-container {
  justify-content: flex-start;
}

.template-1-theme
  .survey-interscreen__wrapper:not(.survey-interscreen__wrapper--has-images)
  .survey-interscreen__button-container {
  justify-content: center !important;
}

.template-1-theme .survey-interscreen--start-screen .survey-interscreen__button-container .btn,
.template-1-theme .survey-interscreen--end-screen .survey-interscreen__button-container .btn {
  font-weight: 400 !important;
  border: none;
  transition: var(--template-1-button-transition);
  border-radius: var(--fqz-poll-next-button-radius);
  opacity: 0.8;
}

.template-1-theme .survey-interscreen--start-screen .survey-interscreen__button-container .btn:hover,
.template-1-theme .survey-interscreen--end-screen .survey-interscreen__button-container .btn:hover {
  opacity: 0.6;
}

.template-1-theme .survey-interscreen--start-screen .survey-interscreen__button-container .btn:active,
.template-1-theme .survey-interscreen--end-screen .survey-interscreen__button-container .btn:active {
  opacity: 1 !important;
}

/* .template-1-theme .survey-interscreen--end-screen p:nth-of-type(3) {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
} */

/* .template-1-theme .survey-interscreen--end-screen p:nth-of-type(3) > span {
  font-size: var(--template-1-text-h5-font-size) !important;
  line-height: var(--template-1-text-h5-line-height) !important;
}

.template-1-theme .survey-interscreen--end-screen p > span {
  font-size: var(--template-1-text-subtitle-1-16-regular-font-size);
  line-height: var(--template-1-text-subtitle-1-16-regular-line-height);
} */

/* .template-1-theme .survey-interscreen--end-screen p:last-of-type {
  margin-top: 7px;
} */

/* Make unsubscribe button centered on end screen */
.template-1-theme .survey-interscreen--end-screen .survey-start__additional-actions {
  justify-content: center;
  width: 100%;
}

/* Interscreen end */

/* Progressbar */

.template-1-theme .poll-progress-bar__inner {
  max-width: 396px;
}

.template-1-theme .poll-progress-bar__text {
  font-size: 14px;
  line-height: 1.715;
  color: var(--template-1-black-main);
  font-weight: 400 !important;
  margin-top: 0 !important;
}

.template-1-theme .progress-line {
  border-color: var(--template-1-secondary) !important;
  overflow: hidden;
  margin-left: 22px;
  margin-right: 22px;
  background-color: white;
}

.template-1-theme .progress-line__indicator {
  background-color: var(--template-1-secondary) !important;
  border-radius: 0;
}

/* Progressbar end */

/* Page header */

.template-1-theme .page-header {
  text-align: left;
  font-size: var(--template-1-text-h6-font-size);
  line-height: var(--template-1-text-h6-line-height);
  max-width: var(--template-1-content-max-width);
  padding-left: var(--template-1-content-padding-desktop-x);
  padding-right: var(--template-1-content-padding-desktop-x);
  width: 100%;
}

.template-1-theme .page-header__description p {
  text-align: left !important;
}

.template-1-theme .page-header__description p strong,
.template-1-theme .page-header__subdescription p strong {
  font-weight: 500 !important;
}

.template-1-theme .page-header__subdescription {
  margin-top: 24px;
  font-size: var(--template-1-text-subtitle-1-16-regular-font-size);
  line-height: var(--template-1-text-subtitle-1-16-regular-line-height);
}

.template-1-theme .page-header__unrequired {
  margin-top: 24px;
  font-size: var(--template-1-text-subtitle-2-14-regular-font-size);
  line-height: var(--template-1-text-subtitle-2-14-regular-line-height);
  color: var(--template-1-black-main);
  text-align: left !important;
}

.template-1-theme .page-header_old .page-header__unrequired {
  text-align: left !important;
}

/* Page header end */

/* Page item content */

.template-1-theme .questions-page__item-content {
  max-width: var(--template-1-content-max-width);
  padding-left: var(--template-1-content-padding-desktop-x);
  padding-right: var(--template-1-content-padding-desktop-x);
  width: 100%;
  margin-top: 0;
}

.template-1-theme .questions-page__item-content--inter-block {
  max-width: var(--template-1-intermediate-block-content-max-width);
  padding-left: 0;
  padding-right: 0;
}

.template-1-theme .survey-interscreen__wrapper:not(.survey-interscreen__wrapper--has-images) {
  padding-left: 0 !important;
  padding-right: 0 !important;
  min-height: 0 !important;
  max-width: 704px !important;
  margin: 0 auto;
}

.template-1-theme .poll-body__content:has(.questions-page--single-question-non-inter-block) {
  justify-content: flex-start;
}

/* Page item content end */

/* Poll actions */

.template-1-theme .survey-actions {
  padding-top: 40px !important;
  padding-bottom: 46px !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  position: static !important;
  max-width: var(--template-1-content-max-width);
  padding-left: var(--template-1-content-padding-desktop-x);
  padding-right: var(--template-1-content-padding-desktop-x);
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.template-1-theme .survey-actions.sticky {
  position: static !important;
}

.template-1-theme .survey-actions__container {
  max-width: 100%;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.template-1-theme .survey-actions__main-buttons {
  justify-content: space-between !important;
}

/* When there's only one button (next), position it on the right */
.template-1-theme .survey-actions__main-buttons .survey-questions__question-actions-btn--next:only-child {
  margin-left: auto;
}

.template-1-theme .survey-questions__question-actions-btn {
  font-weight: 400 !important;
  min-height: 48px !important;
  padding-left: var(--template-1-button-padding-x) !important;
  padding-right: var(--template-1-button-padding-x) !important;
  min-width: auto !important;
  width: auto !important;
  transition: var(--template-1-button-transition);
}

.template-1-theme .survey-actions__main-buttons {
  max-width: 100%;
}

.template-1-theme .survey-questions__question-actions-btn--next {
  opacity: 0.8 !important;
  transition: var(--template-1-button-transition);
}

.template-1-theme .survey-questions__question-actions-btn--next:hover {
  opacity: 0.6 !important;
}

.template-1-theme .survey-questions__question-actions-btn--next:active {
  opacity: 1 !important;
}

/* Disabled state for next button */
.template-1-theme .survey-questions__question-actions-btn--next:disabled {
  background-color: var(--template-1-gray-bg-01) !important;
  border-color: var(--template-1-gray-bg-01) !important;
  color: var(--template-1-gray-default) !important;
  opacity: 1 !important;
  cursor: not-allowed;
}

.template-1-theme .survey-questions__question-actions-btn--prev {
  position: relative;
  border: none !important;
  transition: var(--template-1-button-transition);
  overflow: hidden;
}

.template-1-theme .survey-questions__question-actions-btn--prev:after {
  content: '';
  position: absolute;
  inset: 0;
  border: 1px solid var(--fqz-poll-back-button-stroke-color);
  border-radius: var(--fqz-poll-back-button-radius);
  background-color: transparent;
}

.template-1-theme .survey-questions__question-actions-btn--prev:before {
  content: '';
  position: absolute;
  inset: 0;
  background-color: var(--fqz-poll-back-button-stroke-color);
  transition: var(--template-1-button-transition);
  opacity: 0;
}

.template-1-theme .survey-questions__question-actions-btn--prev:hover {
  opacity: 1 !important;
}

.template-1-theme .survey-questions__question-actions-btn--prev:hover::before {
  opacity: 0.1;
}

.template-1-theme .survey-questions__question-actions-btn--prev:active:before {
  opacity: 0.2;
}

/* Poll actions end */

/* form control styles */
.template-1-theme .fc-check__box {
  border-color: var(--template-1-main-border-color);
}

.template-1-theme .fc-check--checked .fc-check__box {
  background-color: var(--template-1-secondary) !important;
  border-color: var(--template-1-secondary) !important;
}

.template-1-theme .fc-input__wrapper,
.template-1-theme .form-control,
.template-1-theme .select-trigger {
  border-color: var(--template-1-main-border-color);
  max-width: var(--template-1-input-max-width);
  margin: 0;
}

.template-1-theme .fc-input__wrapper:focus-within,
.template-1-theme .form-control:focus,
.template-1-theme .select-trigger:focus {
  border-color: var(--template-1-secondary) !important;
}

/* Select styles */
.template-1-theme .select-trigger .select-trigger__icon svg path {
  stroke: var(--template-1-gray-default) !important;
  stroke-opacity: 1 !important;
}

.template-1-theme .select-trigger:hover .select-trigger__icon {
  color: var(--template-1-black-main);
}

.template-1-theme .select-trigger[data-state='open'] .select-trigger__icon {
  color: var(--template-1-primary);
}

.template-1-theme .select-trigger {
  border-color: var(--template-1-main-border-color);
}

.template-1-theme .command-content__header .fc-input__wrapper {
  max-width: 100% !important;
}
/* form control styles end */

/* Form group */
.template-1-theme .form-group--plain-text,
.template-1-theme .form-group:has(.autocomplete) {
  max-width: var(--template-1-input-max-width);
  margin: 0;
}

.template-1-theme .form-group__label {
  font-weight: 400 !important;
}

.template-1-theme .questions-page__item-content--priority-question {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.template-1-theme .survey-questions__comment-form-group,
.question-variants__self-variant-comment {
  max-width: var(--template-1-input-max-width);
  margin: 0;
}

/* Date Question */
.template-1-theme .date-question__date-form-group,
.template-1-theme .date-question__time-form-group,
.template-1-theme .date-question__date-month-form-group,
.template-1-theme .date-datetime-mask-container__date-form-group,
.template-1-theme .date-datetime-mask-container__time-form-group,
.template-1-theme .date-datemonthtime-mask-container__day-form-group,
.template-1-theme .date-datemonthtime-mask-container__time-form-group {
  margin: 0;
}

.template-1-theme .date-month-mask-container,
.template-1-theme .date-datemonthtime-mask-container__day-form-group-inner,
.template-1-theme .date-datetime-mask-container,
.template-1-theme .date-datemonthtime-mask-container {
  justify-content: flex-start;
}

/* Text Question */
.template-1-theme .text-question-field-container,
.template-1-theme .name-mask-container,
.template-1-theme .period-mask-container__form-group,
.template-1-theme .date-month-mask-container__form-group {
  margin: 0;
}

.template-1-theme .survey-questions__comment-form-group textarea::placeholder,
.question-variants__self-variant-comment textarea::placeholder {
  color: var(--template-1-black-main) !important;
}

.template-1-theme .survey-questions__variants-form-group {
  padding-top: 24px !important;
}

.template-1-theme .question-variants__label {
  font-size: var(--template-1-text-subtitle-1-16-regular-font-size) !important;
  line-height: var(--template-1-text-subtitle-1-16-regular-line-height) !important;
  margin-bottom: 24px !important;
}
/* Form group end */

/* Nps Question */
.template-1-theme .question-nps-rating-error {
  text-align: left !important;
}

.template-1-theme .nps-rating-container {
  gap: 40px;
}

.template-1-theme .nps-scale__list {
  gap: 16px;
}

.template-1-theme .nps-scale__item {
  height: 44px;
  background-color: var(--template-1-gray-main) !important;
  color: var(--template-1-black-default) !important;
  border-radius: 8px;
  font-size: 14px;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.template-1-theme .nps-scale__item span {
  font-weight: 400;
}

.template-1-theme .nps-scale__item:hover {
  background-color: var(--template-1-primary-hover) !important;
  color: var(--template-1-white-text-50) !important;
}

.template-1-theme .nps-scale__item .nps-scale__item-bg,
.template-1-theme .nps-scale__item:before {
  display: none !important;
}

.template-1-theme .nps-scale--inited .nps-scale__item.active:before,
.template-1-theme .nps-scale--inited .nps-scale__item.active .nps-scale__item-bg {
  transform: none !important;
  display: none !important;
}

.template-1-theme .nps-scale__item.active {
  background-color: var(--fqz-poll-main-color-selected) !important;
  color: var(--template-1-white-text-50) !important;
}

.template-1-theme .nps-scale--inited .nps-scale__item:not(.active) {
  opacity: 1;
}

.template-1-theme .nps-scale__labels {
  font-size: var(--template-1-text-caption-12-regular-font-size);
  line-height: var(--template-1-text-caption-12-regular-line-height);
  color: var(--template-1-black-main);
  margin-top: 12px;
  font-weight: 400;
  opacity: 1;
}

.template-1-theme .nps-rating-item {
  padding-bottom: 0;
  width: 100%;
  position: relative;
  margin: 0;
}

.template-1-theme .nps-rating-item:before {
  display: none;
}

.template-1-theme .nps-rating-item__label {
  font-size: var(--template-1-text-h6-font-size) !important;
  margin-bottom: 24px !important;
  text-align: left !important;
  line-height: var(--template-1-text-h6-line-height);
  overflow: hidden;
  transition: opacity 0.3s;
}
/* NPS Question end */

/* File Question */
.template-1-theme .file-button {
  border-color: var(--template-1-main-border-color);
}
/* File Question end */

/* Scale Question */

.template-1-theme .scale-container .slider-range:before {
  background-color: var(--template-1-primary-hover) !important;
  border-color: var(--template-1-primary-hover) !important;
  opacity: 1;
}

.template-1-theme .scale-container .slider-value {
  font-weight: 400 !important;
}

.template-1-theme .scale-item:before {
  display: none;
}

.template-1-theme .scale-item__label {
  text-align: left !important;
}

/* Scale Question end */

/* Priority Question */
.template-1-theme .priority-item .priority-item__content::before {
  opacity: 0.2;
}

.template-1-theme .priority-question__comment {
  padding: 0 !important;
}
/* Priority Question end */

/* Matrix Question */
.template-1-theme .matrix-question__table {
  padding-bottom: 1px;
}

.template-1-theme .matrix-question__row::after {
  display: none !important;
}
/* Matrix Question end */

/* 3d Matrix Question */
.template-1-theme .matrix-question__table {
  padding-bottom: 1px;
}
/* 3d Matrix Question end */

/* Star Rating Question */
.template-1-theme .fc-star-rating__label {
  font-weight: 500 !important;
}
/* Star Rating Question end */

/* Smile Rating Question */
.template-1-theme .smile-rating__message {
  font-weight: 500 !important;
}
/* Smile Rating Question end */

/* Star Variants Question */
.template-1-theme .star-rating-variant-item__label {
  text-align: left;
}

.template-1-theme .star-rating-variant-item:before {
  display: none;
}

/* Star Variants Question end */

/* Force theme colors */
.template-1-theme.template-1-theme--force-colors .star-container {
  color: var(--template-1-primary-active) !important;
}

.template-1-theme.template-1-theme--force-colors .rating-scale__item {
  border-color: var(--template-1-primary-active) !important;
}

.template-1-theme.template-1-theme--force-colors .rating-scale__item.active {
  background-color: var(--template-1-primary-active) !important;
  color: var(--template-1-white-text-50) !important;
}
/* Force theme colors end */

/* Gallery Question */
.template-1-theme .question-gallery-error {
  text-align: left !important;
}
/* Fix star colors in gallery question */
.template-1-theme .gallery-item__rating .star-container {
  color: var(--template-1-gallery-rating-color) !important;
}
/* Gallery Question end */

/* Diff Question */
.template-1-theme .diff-question-button--rectangle:not(.diff-question-button--selected)::before {
  background-color: var(--template-1-gray-main) !important;
  opacity: 1 !important;
}

/* Variants Gallery */
.template-1-theme .variants-gallery-item {
  background-color: var(--template-1-gray-bg-01) !important;
  border-radius: 8px !important;
  border: 1px solid transparent !important;
  padding: 7px !important;
  transition: border-color 0.3s ease;
}

.template-1-theme .variants-gallery-item__preview {
  background-color: transparent !important;
  border-color: transparent !important;
  padding-top: 0 !important;
  position: static !important;
}

.template-1-theme .variants-gallery-item__preview-trigger,
.template-1-theme .variants-gallery-item__content {
  position: relative !important;
}

.template-1-theme .variants-gallery-item__content {
  height: 90px !important;
  width: 90px !important;
}

.template-1-theme .variants-gallery-item__preview-trigger:after {
  display: none !important;
}

.template-1-theme .variants-gallery-item--checked {
  border-color: var(--template-1-secondary) !important;
}

.template-1-theme .variants-gallery-item--checked .variants-gallery-item__check {
  display: block !important;
}

.template-1-theme .variants-gallery-item .fc-check__box {
  width: 20px !important;
  height: 20px !important;
  border-width: 2px !important;
  border-radius: 50% !important;
  position: absolute !important;
  margin: 0 !important;
  top: 8px;
  left: auto;
  right: 8px;
}

.template-1-theme .variants-gallery-item .fc-check:not(.fc-check--checked) .fc-check__box {
  border-color: #a3a3a3 !important;
}

.template-1-theme .variants-gallery-item__label {
  padding-top: 8px;
  font-size: var(--template-1-text-subtitle-1-16-regular-font-size) !important;
  line-height: var(--template-1-text-subtitle-1-16-regular-line-height) !important;
}

.template-1-theme .variants-gallery__slide {
  max-width: 134px !important;
  margin-right: 10px !important;
  align-self: stretch !important;
  display: flex !important;
}

.template-1-theme .variants-gallery__slide:last-child {
  margin-right: 0 !important;
}

.template-1-theme .variants-gallery__button-prev,
.template-1-theme .variants-gallery__button-next {
  top: 50% !important;
}

.template-1-theme .variants-gallery-item__image {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
}

/* Variants Gallery end */

@media screen and (max-width: 1023px) {
  .template-1-theme .survey-interscreen--start-screen .survey-interscreen__wrapper,
  .template-1-theme .survey-interscreen--end-screen .survey-interscreen__wrapper {
    padding-left: 300px;
    max-width: 652px;
  }

  .template-1-theme .survey-interscreen--start-screen .interscreen-images,
  .template-1-theme .survey-interscreen--end-screen .interscreen-images {
    width: 250px;
  }

  .template-1-theme .survey-interscreen--default .survey-interscreen__wrapper {
    padding-left: 0;
    padding-right: 0;
    min-height: 0;
  }
}

@media screen and (max-width: 679px) {
  .template-1-theme .survey-header {
    padding-left: 16px;
    padding-right: 1px;
    min-height: 72px;
  }

  .template-1-theme .survey__header-logo img {
    max-width: 142px;
    max-height: 34px;
  }

  .template-1-theme .survey-interscreen--end-screen,
  .template-1-theme .survey-interscreen--start-screen {
    padding-left: 0;
  }

  .template-1-theme .survey-interscreen--start-screen,
  .template-1-theme .survey-interscreen--end-screen {
    padding-bottom: 0;
  }

  .template-1-theme .survey-interscreen--start-screen .interscreen-images,
  .template-1-theme .survey-interscreen--end-screen .interscreen-images {
    margin-bottom: 15px;
  }

  .template-1-theme .survey-interscreen--start-screen .text > p:first-of-type,
  .template-1-theme .survey-interscreen--end-screen .text > p:first-of-type {
    margin-bottom: 0;
  }

  .template-1-theme .survey-interscreen--start-screen .survey-interscreen__wrapper,
  .template-1-theme .survey-interscreen--end-screen .survey-interscreen__wrapper {
    margin-top: 0;
    padding-top: 40px;
    padding-left: 0;
    padding-right: 0;
  }

  .template-1-theme .survey-interscreen--default .survey-interscreen__wrapper {
    padding-top: 0;
  }

  .template-1-theme .survey-interscreen--start-screen .text,
  .template-1-theme .survey-interscreen--end-screen .text {
    text-align: center !important;
  }

  .template-1-theme .survey-interscreen--start-screen .interscreen-images,
  .template-1-theme .survey-interscreen--end-screen .interscreen-images {
    position: static;
    transform: none;
    width: 100%;
    padding: 0;
    order: -1;
    margin-bottom: 15px;
  }

  .template-1-theme .survey-interscreen--start-screen .interscreen-images img,
  .template-1-theme .survey-interscreen--end-screen .interscreen-images img {
    max-width: 290px;
    width: 100%;
  }

  /* Center unsubscribe button on mobile end screen */
  .template-1-theme .survey-start__additional-actions,
  .template-1-theme .survey-interscreen--end-screen .survey-start__additional-actions {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .template-1-theme .survey-start__additional-actions-social {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .template-1-theme .survey-interscreen--start-screen .survey-interscreen__button-container,
  .template-1-theme .survey-interscreen--end-screen .survey-interscreen__button-container {
    justify-content: center;
    margin-top: 14px;
  }

  .template-1-theme .survey-interscreen--start-screen .survey-interscreen__button-container .btn,
  .template-1-theme .survey-interscreen--end-screen .survey-interscreen__button-container .btn {
    font-size: var(--template-1-text-subtitle-1-16-regular-font-size) !important;
    line-height: var(--template-1-text-subtitle-1-16-regular-line-height) !important;
    min-height: 48px !important;
  }

  .template-1-theme .survey-start__additional-actions-social {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .template-1-theme .poll-progress-bar__text {
    width: auto !important;
  }

  .template-1-theme .app-root__top {
    margin-bottom: 15px;
  }

  .template-1-theme .poll-progress-bar {
    min-height: 70px;
  }

  .template-1-theme .poll-progress-bar__inner {
    width: 376px;
  }

  .template-1-theme .page-header__description {
    font-size: var(--template-1-text-h6-font-size) !important;
    line-height: var(--template-1-text-h6-line-height) !important;
  }

  .template-1-theme .page-header__subdescription,
  .template-1-theme .page-header__unrequired {
    margin-top: 16px;
  }

  .template-1-theme .questions-page__item-content,
  .template-1-theme .page-header {
    padding-left: var(--template-1-content-padding-mobile-x);
    padding-right: var(--template-1-content-padding-mobile-x);
  }

  .template-1-theme .questions-page__item-content {
    padding-top: 32px;
  }

  /* Mobile NPS scale adjustments */
  .template-1-theme .nps-rating-container {
    gap: 24px;
  }

  .template-1-theme .nps-scale__list {
    gap: 6px;
  }

  .template-1-theme .nps-scale__labels {
    margin-top: 8px;
    font-size: var(--template-1-text-overline-10-regular-font-size) !important;
    line-height: var(--template-1-text-overline-10-regular-line-height) !important;
  }

  .template-1-theme .nps-scale__item {
    height: 32px;
    font-size: 12px;
  }

  .template-1-theme .nps-rating-item {
    padding-bottom: 0;
    width: 100%;
    position: relative;
    margin: 0;
  }

  .template-1-theme .nps-rating-item:before {
    display: none;
  }

  .template-1-theme .nps-rating-item__label {
    font-size: var(--template-1-text-subtitle-1-16-regular-font-size) !important;
    margin-bottom: 16px !important;
    text-align: left !important;
  }

  /* Scale Question adjustments */
  .template-1-theme .scale-container .fc-input__wrapper {
    max-width: 100% !important;
  }

  .template-1-theme .scale-item:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Mobile survey actions adjustments */
  .template-1-theme .survey-actions {
    padding-top: 16px !important;
    padding-left: var(--template-1-content-padding-mobile-x);
    padding-right: var(--template-1-content-padding-mobile-x);
  }

  /* Mobile button adjustments */
  .template-1-theme .survey-questions__question-actions-btn {
    min-height: 44px !important;
    padding-left: 20px !important;
    padding-right: 20px !important;
    font-size: 14px !important;
    flex-grow: 0 !important;
    width: auto !important;
  }

  .template-1-theme .survey-actions__main-buttons {
    display: flex;
    justify-content: space-between !important;
    width: 100%;
  }

  /* Form group adjustments */
  .template-1-theme .survey-questions__variants-form-group {
    padding-top: 16px !important;
  }

  .template-1-theme .question-variants__label {
    margin-bottom: 16px !important;
  }

  .template-1-theme .variants-gallery-item__label {
    padding-top: 8px !important;
    font-size: var(--template-1-text-subtitle-1-16-regular-font-size) !important;
    line-height: var(--template-1-text-subtitle-1-16-regular-line-height) !important;
  }

  /* Variants Gallery adjustments */
  .template-1-theme .variants-gallery swiper-slide {
    max-width: 120px !important;
    width: 120px !important;
  }

  .template-1-theme .variants-gallery-item__content {
    width: 78px !important;
    height: 80px !important;
  }

  .template-1-theme .variants-gallery-item__label {
    font-size: var(--template-1-text-subtitle-2-14-regular-font-size) !important;
    line-height: var(--template-1-text-subtitle-2-14-regular-line-height) !important;
  }

  .template-1-theme .fc-input__wrapper,
  .template-1-theme .form-control,
  .template-1-theme .select-trigger {
    max-width: var(--fqz-poll-input-max-width-mobile);
  }

  .template-1-theme .form-group--plain-text,
  .template-1-theme .form-group:has(.autocomplete) {
    max-width: var(--fqz-poll-input-max-width-mobile);
  }

  .template-1-theme .survey-questions__comment-form-group,
  .question-variants__self-variant-comment {
    max-width: var(--fqz-poll-input-max-width-mobile);
  }

  /* Override all span styles inside .text for start/end screens on mobile */
  .template-1-theme .survey-interscreen--start-screen .text span,
  .template-1-theme .survey-interscreen--end-screen .text span {
    font-size: 16px !important;
    line-height: 1.3 !important;
  }
}
