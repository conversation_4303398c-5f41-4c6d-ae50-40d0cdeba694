import { getBaseAssetsUrl } from '@/shared/api'
import { POLL_NAVIGATION_TYPE } from '@/shared/constants'

/**
 * Design settings for the 'theme-1' (<PERSON>libka <PERSON>dugi) preset.
 */
export function getTheme1DefaultDesignSettings() {
  const baseUrl = getBaseAssetsUrl()
  return {
    id: 16687,
    foquz_poll_id: 56401,
    background_image: '',
    mobile_background_image: '',
    logo_image: `${baseUrl}/img/ur-logo.png`,
    main_color: 'rgba(131, 39, 216, 1)',
    background_color: 'rgba(255, 255, 255, 1)',
    header_color: 'rgba(255, 255, 255, 1)',
    star_color: '#F8CD1C',
    rating_color: '#3F65F1',
    nps_color_from: '#F96261',
    nps_color_to: '#00C968',
    sem_diff_color_from: '#73808D',
    sem_diff_color_to: '#73808D',
    is_use_header: 1,
    font_family: 'Arial, Helvetica, sans-serif',
    title_font_size: '20',
    font_size: '14',
    text_on_bg: 'rgba(53, 58, 65, 1)',
    text_on_place: 'rgba(0, 0, 0, 1)',
    link_color: 'rgba(51, 172, 220, 1)',
    from_template: 2506,
    logo_link: 'https://www.r-ulybka.ru/',
    logo_type: 'image',
    logo_text: null,
    logo_font_family: null,
    logo_color: null,
    logo_position: 1,
    logo_margins: 16,
    small_header_mobile: 0,
    logo_height: 40,
    logo_text_size: 14,
    logo_text_bold: 0,
    logo_text_italic: 0,
    back_text: '',
    back_button_background_color: 'rgba(255, 255, 255, 1)',
    back_button_text_color: 'rgba(155, 81, 224, 1)',
    back_button_stroke_color: 'rgba(155, 81, 224, 1)',
    back_button_radius: 8,
    next_text: '',
    next_button_background_color: 'rgba(131, 39, 216, 1)',
    next_button_text_color: 'rgba(255, 255, 255, 1)',
    next_button_stroke_color: 'rgba(131, 39, 216, 1)',
    next_button_radius: 8,
    start_button_background_color: 'rgba(131, 39, 216, 1)',
    start_button_text_color: 'rgba(255, 255, 255, 1)',
    start_button_stroke_color: 'rgba(131, 39, 216, 1)',
    start_button_radius: 8,
    finish_text: '',
    finish_link: '',
    unrequired_text: 'Необязательный',
    show_process: POLL_NAVIGATION_TYPE.PROGRESSBAR,
    darkening_background: 0,
    place_under_buttons: 'light',
    show_prev_button: 1,
    main_place_color: 'rgba(255, 255, 255, 1)',
    choose_language: 0,
    full_width: 0,
    disable_question_autoscroll: 0,
    in_use_cover: 0,
    cover_image: '',
    cover_position: 0,
    cover_only_first_page: 0,
    cover_full_width: 0,
    backgroundImage: '',
    logo: `${baseUrl}/img/ur-logo.png`,
    mainColor: 'rgba(131, 39, 216, 1)',
    logoLink: 'https://www.r-ulybka.ru/',
    bgColor: 'rgba(255, 255, 255, 1)',
    headerColor: 'rgba(255, 255, 255, 1)',
    starColor: '#F8CD1C',
    ratingColor: '#3F65F1',
    npsColorFrom: '#F96261',
    npsColorTo: '#00C968',
    semDiffColorFrom: '#73808D',
    semDiffColorTo: '#73808D',
    textOnBg: 'rgba(53, 58, 65, 1)',
    textOnPlace: 'rgba(0, 0, 0, 1)',
    linkColor: 'rgba(51, 172, 220, 1)',
    fontFamily: 'Arial, Helvetica, sans-serif',
    titleFontSize: '20',
    fontSize: '14',
    isUseHeader: true,
    templateId: 2506,
    logoType: 'image',
    logoText: null,
    logoFontFamily: null,
    logoColor: null,
    backText: '',
    nextText: '',
    finishText: '',
    finishLink: '',
    placeUnderButtons: 'light',
  }
}
