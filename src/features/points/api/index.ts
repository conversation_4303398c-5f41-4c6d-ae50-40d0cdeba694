import type { GetResultsResponse } from '../schemas/getResults'
import type { PointsData } from '../types'
import { getBaseApiUrl } from '@shared/api'
import { pointsDataSchema } from '../schemas'
import { getResultsSchema } from '../schemas/getResults'

/**
 * Fetches points data from server
 * @param {string} authKey - User's auth key
 * @returns {Promise<PointsData>} Points data
 * @throws {Error} If failed to fetch points or validation fails
 */
export async function fetchPoints(authKey: string): Promise<PointsData> {
  const url = new URL(`${getBaseApiUrl()}/foquz/api/p/get-points`)
  url.searchParams.append('key', authKey)

  const response = await fetch(url.toString())

  if (!response.ok) {
    throw new Error('Failed to fetch points data')
  }

  const data = await response.json()
  return pointsDataSchema.parse(data)
}

/**
 * Fetches detailed results data from server
 * @param {string} authKey - User's auth key
 * @returns {Promise<GetResultsResponse>} Detailed results data
 * @throws {Error} If failed to fetch results or validation fails
 */
export async function getResults(authKey: string): Promise<GetResultsResponse> {
  const url = new URL(`${getBaseApiUrl()}/foquz/api/p/get-results`)
  url.searchParams.append('key', authKey)

  const response = await fetch(url.toString())

  if (!response.ok) {
    throw new Error('Failed to fetch results data')
  }

  const data = await response.json()
  const parsedData = getResultsSchema.parse(data)
  return parsedData
}
