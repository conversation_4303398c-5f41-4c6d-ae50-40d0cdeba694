// @TODO: remove eslint-disable
import { MaskTypes } from '@shared/constants/maskTypes'
import { z } from 'zod'

const baseQuestionSchema = z.object({
  description: z.string(),
  commentEnabled: z.union([z.literal(0), z.literal(1)]),
  commentLabel: z.string().optional().nullable(),
  rightAnswer: z.any().optional(),
  id: z.number(),
  skipText: z.string().optional().nullable(),
  isDeleted: z.number().optional().nullable(),
  maxPoints: z.number().optional().nullable(),
})

// Base answer schema that's common across all question types
const baseAnswerSchema = z.object({
  skipped: z.union([z.literal(0), z.literal(1)]),
  points: z.number().optional().nullable(),
  max_points: z.number().optional().nullable(),
  comment: z.string().optional().nullable(),
  rightAnswer: z.boolean().optional().nullable(),
  answer: z.any().optional().nullable(),
  correct_answer: z.any().optional().nullable(),
  selfVariant: z.string().nullable().optional(),
})

// Text question answer schemas based on mask type
const textBaseAnswerSchema = baseAnswerSchema.extend({
  answer: z.string().nullable(),
})

const nameAnswerSchema = baseAnswerSchema.extend({
  answer: z.object({
    name: z.string().optional(),
    surname: z.string().optional(),
    patronymic: z.string().optional(),
  }),
})

// Text question schema
const textQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(2),
  mask_type: z.nativeEnum(MaskTypes),
  answer: z.nullable(z.union([
    textBaseAnswerSchema,
    nameAnswerSchema,
  ])),
})

// Variants question schema
const variantSchema = z.object({
  variant: z.string(),
  id: z.union([z.string(), z.number()]),
  question_detail_id: z.union([z.string(), z.number()]).optional().nullable(),
  dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
  file_url: z.string().nullable(),
  preview_url: z.string().nullable(),
  file_id: z.number().nullable(),
})

const variantsAnswerSchema = baseAnswerSchema.extend({
  selectedIds: z.array(z.string()).optional(),
  selfVariant: z.string().nullable().optional(),
  comment: z.string().nullable().optional(),
  points: z.number().nullable().optional(),
  max_points: z.number().nullable().optional(),
  correct_answer: z.array(z.object({
    id: z.number(),
    points: z.number(),
    text: z.string().optional().nullable(),
  })).nullable().optional(),
})

const variantsQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(1),
  variants: z.array(variantSchema),
  answer: z.nullable(variantsAnswerSchema),
  rightAnswer: z.object({
    id: z.array(z.union([z.string(), z.number()])),
  }).nullable(),
  self_variant_text: z.string().nullable().optional(),
})

// Card Sorting question schema
const cardSortingCategorySchema = z.object({
  name: z.string(),
  id: z.union([z.string(), z.number()]),
})

const cardSortingAnswerSchema = baseAnswerSchema.extend({
  answer: z.record(z.string(), z.union([
    z.array(z.string()),
    z.literal('null'),
  ])).nullable(),
  comment: z.string().optional().nullable(),
  skipped: z.union([z.literal(0), z.literal(1)]),
})

const cardSortingQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(22),
  answer: z.nullable(cardSortingAnswerSchema),
  variants: z.array(variantSchema),
  cardSortingCategories: z.array(cardSortingCategorySchema),
  self_variant_text: z.string().nullable().optional(),
})

// Stars question schema
const starRatingOptionsSchema = z.object({
  color: z.string(),
  count: z.number(),
  size: z.string(),
  labelsArray: z.array(z.string()),
})

const starsAnswerSchema = baseAnswerSchema.extend({
  rating: z.number().optional().nullable(),
  comment: z.string().nullable().optional(),
  selectedIds: z.array(z.string()).optional(),
  selfVariant: z.string().nullable().optional(),
})

const starsQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(15),
  starRatingOptions: starRatingOptionsSchema,
  answer: z.nullable(starsAnswerSchema),
  variants: z.array(variantSchema).optional(),
  clarifyingQuestion: z.string().nullable(),
  self_variant_text: z.string().optional().nullable(),
})

// Date question schema
const dateAnswerSchema = baseAnswerSchema.extend({
  answer: z.string().nullable(),
  correct_answer: z.object({
    text: z.string().optional().nullable(),
  }).nullable().optional(),
})

const dateQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(3),
  answer: z.nullable(dateAnswerSchema),
  rightAnswer: z.object({
    decodedAnswer: z.object({
      date: z.string().nullable(),
      time: z.string().nullable(),
      dateFormat: z.string().optional().nullable(),
    }).nullable(),
  }).nullable(),
})

// Address question schema
const addressAnswerSchema = baseAnswerSchema.extend({
  answer: z.string().nullable(),
})

const addressQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(4),
  answer: z.nullable(addressAnswerSchema),
})

// Scale question schema
const scaleAnswerSchema = baseAnswerSchema.extend({
  answer: z.record(z.string(), z.any()).nullable(),
  selfVariant: z.string().nullable().optional(),
})

const scaleQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(20),
  answer: z.nullable(scaleAnswerSchema),
  set_variants: z.union([z.literal(0), z.literal(1)]),
  scaleRatingSetting: z.object({
    start: z.number(),
    end: z.number(),
    step: z.number(),
  }),
  variants: z.array(z.object({
    id: z.union([z.string(), z.number()]),
    dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
    question_detail_id: z.union([z.string(), z.number()]).optional().nullable(),
    variant: z.string(),
  })).optional(),
})

const distributionScaleQuestionSchema = scaleQuestionSchema.extend({
  type: z.literal(23),
})

// Matrix question schema

const matrixCorrectAnswerItemSchema = z.object({
  row: z.string(),
  col: z.string(),
  text: z.string().optional().nullable(),
})

const matrixQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(13),
  clarifyingQuestion: z.string().nullable(),
  self_variant_text: z.string().nullable(),
  variants: z.array(z.object({
    id: z.number().or(z.string()),
    variant: z.string().optional(),
    question: z.string().optional(),
  })).optional(),
  answer: z.nullable(z.object({
    comment: z.string().nullable().optional(),
    rightAnswer: z.boolean().optional(),
    skipped: z.union([z.literal(0), z.literal(1)]),
    answer: z.object({
      answer: z.record(z.string(), z.union([
        z.array(z.string()),
        z.literal('null'),
      ])).nullable(),
      comment: z.string().nullable(),
      extra: z.record(z.string(), z.union([
        z.array(z.string()),
        z.record(z.string(), z.string()),
      ])).nullable(),
    }).nullable(),
    correct_answer: z.union([
      z.array(matrixCorrectAnswerItemSchema),
      z.array(z.array(matrixCorrectAnswerItemSchema)).optional(),
    ]).optional().nullable(),
    points: z.number().nullable(),
    max_points: z.number().nullable(),
    without_points: z.boolean().optional(),
    selfVariant: z.string().nullable().optional(),
  })),
})

const matrix3dAnswerSchema = baseAnswerSchema.extend({
  answer: z.object({
    answer: z.record(z.string(), z.record(z.string(), z.array(z.string()))).nullable(),
    comment: z.string().nullable(),
  }).nullable(),
  selfVariant: z.string().nullable().optional(),
})

const matrix3dQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(21),
  matrixElements: z.object({
    columns: z.array(z.object({
      id: z.number(),
      donor_variant_id: z.union([z.string(), z.number()]).optional().nullable(),
      donor_dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
      name: z.string(),
      variants: z.array(z.object({
        id: z.number(),
        name: z.string(),
      })),
    })),
    rows: z.array(z.object({
      id: z.number(),
      donor_variant_id: z.union([z.string(), z.number()]).optional().nullable(),
      donor_dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
      name: z.string(),
    })),
  }),
  answer: z.nullable(matrix3dAnswerSchema),
})

const selfVariantFileSchema = z.object({
  file_id: z.number(),
  file_url: z.string().nullable(),
  preview_url: z.string().nullable(),
}).nullable().optional()

// NPS question schema
const npsVariantSchema = z.object({
  id: z.union([z.string(), z.number()]),
  question_detail_id: z.union([z.string(), z.number()]).optional().nullable(),
  dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
  variant: z.string(),
  extra_question: z.union([z.literal(0), z.literal(1)]),
  file_id: z.number().nullable(),
  file_url: z.string().nullable(),
  preview_url: z.string().nullable(),
  detail_question: z.string().nullable(),
  self_variant_text: z.string().nullable().optional(),
  is_self_answer: z.union([z.literal(0), z.literal(1), z.boolean()]),
  variants_with_files: z.union([z.literal(0), z.literal(1)]),
  selfVariantFile: selfVariantFileSchema,
  text_answer: z.string().nullable().optional(),
})

const npsAnswerSchema = baseAnswerSchema.extend({
  skipped: z.literal(0).or(z.literal(1)),
  answer: z.record(z.string(), z.string()).nullable().optional(),
  detail_item: z.union([
    z.array(z.string()),
    z.record(z.string(), z.union([
      z.array(z.string()),
      z.record(z.string(), z.string()),
    ])),
  ]).optional().nullable(),
  self_variant: z.string().nullable().optional(),

  // @NOTE: Значение своего варианта-реципиента
  // Может приходить в ответе, если вопрос является реципиентом от "Варианты ответов"
  // и пользователь выбрал свой вариант
  selfVariant: z.string().nullable().optional(),
  text_answer: z.string().nullable().optional(),
})

const npsQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(12),
  variants: z.array(npsVariantSchema),
  answer: z.nullable(npsAnswerSchema),
  set_variants: z.union([z.literal(0), z.literal(1)]),
  fromOne: z.union([z.literal(0), z.literal(1)]),
  clarifyingQuestion: z.string().nullable(),
  variants_with_files: z.union([z.literal(0), z.literal(1)]).optional(),
  self_variant_text: z.string().nullable(),
  self_variant_file: selfVariantFileSchema,
  extra_question_type: z.union([
    z.literal(0), // DISABLED
    z.literal(1), // SINGLE
    z.literal(2), // COMMON_PER_VARIANT
    z.literal(3), // DIFFERENT_PER_VARIANT
  ]),
})

// File question schema
const fileAnswerSchema = z.object({
  id: z.number(),
  name: z.string(),
  url: z.string().nullable(),
  preview_url: z.string().nullable(),
})

const fileQuestionAnswerSchema = baseAnswerSchema.extend({
  answer: z.object({
    files: z.array(fileAnswerSchema).optional(),
    comment: z.string().nullable().optional(),
  }).nullable(),
})

const fileQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(5),
  answer: z.nullable(fileQuestionAnswerSchema),
})

// Smile question schema
const smileAnswerSchema = baseAnswerSchema.extend({
  skipped: z.union([z.literal(0), z.literal(1)]),
  comment: z.string().optional(),
  self_variant: z.string().nullable().optional(),
  text_answer: z.string().nullable().optional(),
  selectedIds: z.union([
    z.array(z.string()),
    z.record(z.string(), z.union([
      z.array(z.string()),
      z.record(z.string(), z.string()),
    ])),
  ]).optional(),
  selfVariant: z.string().nullable().optional(),
  answer: z.object({
    extra: z.union([
      z.array(z.string()),
      z.record(z.string(), z.union([
        z.array(z.string()),
        z.record(z.string(), z.string()),
      ])),
    ]).optional(),
    smile: z.object({
      id: z.number(),
      smile_url: z.string(),
      label: z.string(),
    }).nullable(),
    comment: z.string().nullable(),
  }).optional(),
})

const smileQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(11),
  answer: z.nullable(smileAnswerSchema),
  self_variant_text: z.string().nullable().optional(),
  self_variant_file: selfVariantFileSchema,
  variants: z.array(z.object({
    extra_question: z.union([z.literal(0), z.literal(1)]),
    file_url: z.string().nullable(),
    file_id: z.number().nullable(),
    preview_url: z.string().nullable(),
    id: z.union([z.string(), z.number()]),
    dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
    question_detail_id: z.union([z.string(), z.number()]).optional().nullable(),
    variant: z.string(),
  })).optional(),
  clarifyingQuestion: z.string().nullable().optional(),
})

// Differential question schema
const diffAnswerSchema = baseAnswerSchema.extend({
  answer: z.object({
    answer: z.record(z.string(), z.string()).nullable(),
    comment: z.string().nullable(),
  }).nullable(),
})

const diffQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(14),
  differentialRows: z.array(z.object({
    id: z.union([z.string(), z.number()]),
    question_id: z.number(),
    start_label: z.string(),
    end_label: z.string(),
    position: z.number(),
  })),
  semDifSetting: z.object({
    id: z.number(),
    foquz_question_id: z.number(),
    form: z.string(),
    start_point_color: z.string(),
    end_point_color: z.string(),
  }),
  answer: z.nullable(diffAnswerSchema),
})

// Quiz question schema
const quizAnswerSchema = baseAnswerSchema.extend({
  skipped: z.union([z.literal(0), z.literal(1)]),
  answer: z.object({
    values: z.array(z.object({
      id: z.number(),
      label: z.string(),
      value: z.union([
        z.string(),
        z.object({
          name: z.string(),
          surname: z.string(),
          patronymic: z.string().optional(),
        }),
      ]),
    })),
  }).optional(),
})

const quizQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(6),
  answer: z.nullable(quizAnswerSchema),
  formFields: z.array(z.object({
    id: z.union([z.string(), z.number()]),
    maskType: z.nativeEnum(MaskTypes),
  })).optional(),
})
// Priority question schema

const priorityQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(8),
  variants: z.array(z.object({
    id: z.union([z.string(), z.number()]),
    question_detail_id: z.union([z.string(), z.number()]).optional().nullable(),
    dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
    variant: z.string(),
    position: z.number(),
  })),
  answer: z.nullable(baseAnswerSchema.extend({
    answer: z.object({
      variants: z.array(z.string()).nullable(),
      comment: z.string().nullable(),
    }).nullable(),
    rightAnswer: z.boolean(),
    selfVariant: z.string().nullable().optional(),
  })),
  rightAnswer: z.nullable(z.object({
    decodedAnswer: z.array(z.string()).nullable(),
  })),
})

// Media variants question schema

const mediaVariantsAnswerSchema = baseAnswerSchema.extend({
  skipped: z.union([z.literal(0), z.literal(1)]),
  answer: z.object({
    answer: z.array(z.string()).nullable(),
    comment: z.string().nullable(),
  }),
  correct_answer: z.array(z.object({
    id: z.number(),
    file_url: z.string().nullable(),
    preview_url: z.string().nullable(),
  })).nullable().optional(),
  selfVariant: z.string().nullable().optional(),
})

const mediaVariantsQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(9),
  chooseMedia: z.array(z.object({
    id: z.number(),
    url: z.string(),
    src: z.string(),
    poster: z.string(),
    description: z.string(),
    points: z.number().nullable(),
  })),
  answer: mediaVariantsAnswerSchema.nullable(),
  rightAnswer: z.nullable(z.any()),
})

const classifierVariantSchema = z.object({
  id: z.string(),
  value: z.string(),
  description: z.string().optional(),
  path: z.string().optional(),
})

const classifierAnswerSchema = baseAnswerSchema.extend({
  selectedIds: z.array(z.string()).optional(),
  selfVariant: z.string().nullable(),
  comment: z.string().nullable(),
})

// Classifier question schema
const classifierQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(19),
  variants: z.array(classifierVariantSchema),
  answer: z.nullable(classifierAnswerSchema),
  dictionary_list_type: z.union([z.literal('list'), z.literal('tree')]),
})

const filialsQuestionSchema = classifierQuestionSchema.extend({
  type: z.literal(17),
})

// Rating question schema
const ratingScaleAnswerSchema = baseAnswerSchema.extend({
  rating: z.number().optional().nullable(),
  comment: z.string().nullable().optional(),
  selectedIds: z.array(z.string()).optional(),
  selfVariant: z.string().nullable().optional(),
})

const ratingScaleQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(18),
  starRatingOptions: starRatingOptionsSchema,
  answer: z.nullable(ratingScaleAnswerSchema),
  variants: z.array(variantSchema).optional(),
  clarifyingQuestion: z.string().nullable(),
  self_variant_text: z.string().nullable(),
})

const galleryAnswerSchema = baseAnswerSchema.extend({
  answer: z.object({
    answer: z.union([
      z.record(z.string(), z.string()),
      z.array(z.string()),
    ]).nullable(),
    comment: z.string().nullable(),
  }).nullable(),
})

// Gallery question schema
const galleryQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(10),
  chooseMedia: z.array(z.object({
    id: z.number(),
    url: z.string(),
    src: z.string(),
    poster: z.string(),
    description: z.string(),
  })),
  answer: z.nullable(galleryAnswerSchema),
})

/**
 * @NOTE: тут без примера не обойтись.
 * Обратите внимание, что в ответе с УВ в виде текста, есть поле 'answer' внутри значения
 * А в ответе с УВ в виде выбора одного/нескольких вариантов приходит просто строка
 *
 * Пример ответа с двумя вариантами и рейтингом (4 и 5) и УВ в виде текста
 * @example
 * {
 *   answer: {
 *     '1': '4',
 *     '2': '5',
 *     'extra': {
 *       '1': {
 *         'answer': 'Уточняющий вопрос для варианта 1',
 *       },
 *       '2': {
 *         'answer': 'Уточняющий вопрос для варианта 2',
 *       },
 *     },
 *   },
 * }
 *
 * Другой пример ответа с с двумя вариантами и рейтингом (4 и 5) и УВ в виде нескольких вариантов + свой вариант
 * @example
 * {
 *   answer: {
 *     '1': '4',
 *     '2': '5',
 *     'extra': {
 *       '1': "id123",
 *       '2': "id124",
 *       'self_variant': 'Значение своего варианта',
 *     },
 *   },
 * }
 * А еще может приходить в extra['id'] массив строк:
 * @example
 * {
 *   '1': '4',
 *   '2': '5',
 *   'extra': {
 *     '1': ['id123', 'id124'],
 *     '2': ['id125', 'id126'],
 *   },
 * }
 */
const starVariantsExtraSchema = z.record(
  z.string(),
  z.union([
    z.record(z.string(), z.string()),
    z.array(z.string()),
  ]),
)
const starVariantsAnswerSchema = baseAnswerSchema.extend({
  comment: z.string().nullable().optional(),
  answer: z.object({
    comment: z.string().nullable().optional(),
    answer: z.record(
      z.string(),
      z.union([
        starVariantsExtraSchema,
        z.string(),
      ]),
    ).nullable(),
  }).nullable(),
  selfVariant: z.string().nullable().optional(),
})

// Star variants question schema
const starVariantsQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(7),
  variants: z.array(z.object({
    id: z.union([z.string(), z.number()]),
    dictionary_element_id: z.union([z.string(), z.number()]).optional().nullable(),
    question_detail_id: z.union([z.string(), z.number()]).optional().nullable(),
    variant: z.string(),
    extra_question: z.union([z.literal(0), z.literal(1)]),
  })),
  starRatingOptions: starRatingOptionsSchema,
  answer: z.nullable(starVariantsAnswerSchema),
  clarifyingQuestion: z.string().nullable(),
  self_variant_text: z.string().nullable(),
})

// Test first click
const firstClickTestQuestionSchema = baseQuestionSchema.extend({
  type: z.literal(24),
  answer: z.nullable(baseAnswerSchema.extend({
    points: z.array(z.string()).optional().nullable(), // Changed from number to array of strings
    time_expired: z.number().optional().nullable(),
  })),
})

// Combined question schema
const questionSchema = z.discriminatedUnion('type', [
  variantsQuestionSchema, // 1 - Варианты ответов
  textQuestionSchema, // 2 - Текстовый ответ
  dateQuestionSchema, // 3 - Дата
  addressQuestionSchema, // 4 - Адрес
  fileQuestionSchema, // 5 - Выбор файла
  quizQuestionSchema, // 6 - Анкета
  starVariantsQuestionSchema, // 7 - Звездный рейтинг с вариантами
  priorityQuestionSchema, // 8 - Приоритет
  mediaVariantsQuestionSchema, // 9 - Медиа варианты
  galleryQuestionSchema, // 10 - Галерея
  smileQuestionSchema, // 11 - Смайлик
  npsQuestionSchema, // 12 - NPS
  diffQuestionSchema, // 14 - Семантический дифференциал
  starsQuestionSchema, // 15 - Звездный рейтинг
  filialsQuestionSchema, // 17 - Филиалы
  ratingScaleQuestionSchema, // 18 - Рейтинг
  classifierQuestionSchema, // 19 - Классификатор
  scaleQuestionSchema, // 20 - Шкала
  matrixQuestionSchema, // 13 - Матрица
  matrix3dQuestionSchema, // 21 - 3D матрица
  cardSortingQuestionSchema, // 22 - какрытая карточная сортировка
  distributionScaleQuestionSchema, // 23 - распределительная шкала
  firstClickTestQuestionSchema, // 24 - тест 1 клика
])

// Points schema
const pointsSchema = z.object({
  answer_points: z.number(),
  points_max: z.number(),
  percent: z.number(),
})

// Root response schema
export const getResultsSchema = z.object({
  id: z.number(),
  displaySettings: z.any().nullable().optional(),
  displayPages: z.array(z.any()),
  questions: z.array(questionSchema),
  points: pointsSchema,
  poll: z.object({
    name: z.string(),
  }).nullable(),
  contact: z.object({
    name: z.string().nullable().optional(),
    phone: z.string().nullable().optional(),
    email: z.string().nullable().optional(),
  }).nullable(),
})

export type GetResultsResponse = z.infer<typeof getResultsSchema>
export type GetResultsQuestions = z.infer<typeof questionSchema>

export type GetResultsVariantsQuestion = z.infer<typeof variantsQuestionSchema> // 1
export type GetResultsTextQuestion = z.infer<typeof textQuestionSchema> // 2
export type GetResultsDateQuestion = z.infer<typeof dateQuestionSchema> // 3
export type GetResultsAddressQuestion = z.infer<typeof addressQuestionSchema> // 4
export type GetResultsFileQuestion = z.infer<typeof fileQuestionSchema> // 5
export type GetResultsQuizQuestion = z.infer<typeof quizQuestionSchema> // 6
export type GetResultsStarVariantsQuestion = z.infer<typeof starVariantsQuestionSchema> // 7
export type GetResultsPriorityQuestion = z.infer<typeof priorityQuestionSchema> // 8
export type GetResultsMediaVariantsQuestion = z.infer<typeof mediaVariantsQuestionSchema> // 9
export type GetResultsGalleryQuestion = z.infer<typeof galleryQuestionSchema> // 10
export type GetResultsSmileQuestion = z.infer<typeof smileQuestionSchema> // 11
export type GetResultsNpsQuestion = z.infer<typeof npsQuestionSchema> // 12
export type GetResultsMatrixQuestion = z.infer<typeof matrixQuestionSchema> // 13
export type GetResultsDiffQuestion = z.infer<typeof diffQuestionSchema> // 14
export type GetResultsStarsQuestion = z.infer<typeof starsQuestionSchema> // 15
export type GetResultsRatingScaleQuestion = z.infer<typeof ratingScaleQuestionSchema> // 18
export type GetResultsClassifierQuestion = z.infer<typeof classifierQuestionSchema | typeof filialsQuestionSchema> // 19
export type GetResultsScaleQuestion = z.infer<typeof scaleQuestionSchema> // 20
export type GetResultsMatrix3dQuestion = z.infer<typeof matrix3dQuestionSchema> // 21
export type GetResultsCardSortingQuestion = z.infer<typeof cardSortingQuestionSchema> // 22
export type GetResultsDistributionScaleQuestion = z.infer<typeof distributionScaleQuestionSchema> // 23
export type GetResultsFirstClickTestQuestion = z.infer<typeof firstClickTestQuestionSchema> // 24

export type AssessmentVariant = z.infer<typeof variantSchema>
export type NameAnswer = z.infer<typeof nameAnswerSchema>
export type StarVariantsExtra = z.infer<typeof starVariantsExtraSchema>
// Points data type
export interface PointsData {
  questions: Array<{
    questionId: number
    points: number
    maxPoints: number
    correctAnswer?: any
  }>
  points: number
  maxPoints: number
}
