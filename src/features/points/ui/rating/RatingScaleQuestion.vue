<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { RatingScaleQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'
import PointsReportVariants from '../PointsReportVariants.vue'

interface RatingScaleQuestionProps extends BasePointsProps {
  question: RatingScaleQuestionReport
  number: number
}

defineProps<RatingScaleQuestionProps>()
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="rating-scale">
      <div class="rating-scale__rating">
        <div
          class="rating-scale__selected"
          :style="{ backgroundColor: '#3F65F1' }"
        >
          {{ question.rating }}
        </div>
        <div v-if="question.label" class="rating-scale__label">
          {{ question.label }}
        </div>
      </div>
      <PointsReportVariants
        v-if="question.clarifyingQuestion && !question.commentEnabled"
        :variants="question.assessments || []"
        :label="question.clarifyingQuestion"
        :text-value="question.comment"
        :self-variant="question.selfVariantText"
        :self-variant-label="question.selfVariantLabel"
      />
    </div>
  </PointsReportItem>
</template>

<style scoped>
.rating-scale {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.rating-scale__rating {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.rating-scale__selected {
  width: 28px;
  height: 24px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  line-height: 1.1;
  color: white;
}

.rating-scale__label {
  font-size: 14px;
  line-height: 1.1;
  margin-top: -1px;
}
</style>
