<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { ScaleQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'

interface ScaleQuestionProps extends BasePointsProps {
  question: ScaleQuestionReport
  number: number
}

defineProps<ScaleQuestionProps>()
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="scale-question scale-question__variants">
      <div class="scale-question__variants">
        <div
          v-for="variant in question.formattedVariants"
          :key="variant.id"
          class="scale-question__variant"
        >
          <div v-if="variant.text" class="scale-question__variant-text">
            {{ variant.text }}
          </div>
          <div class="scale-question__item">
            <div class="scale-question__bar">
              <div
                class="scale-question__progress"
                :style="{
                  width: `${(variant.rating / question.maxRating) * 100}%`,
                }"
              />
            </div>
            <div class="scale-question__value">
              {{ variant.rating }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.scale-question,
.scale-question__variants {
  flex-direction: column;
}

.scale-question__value {
  font-weight: 700;
  margin-top: -1px;
}

.scale-question__variant {
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.scale-question__variant:first-child {
  padding-top: 0;
}

.scale-question__variant:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.scale-question__variant-text {
  font-size: 14px;
  line-height: 1.1;
}

.scale-question__variant-rating {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.scale-question__item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.scale-question__bar {
  height: 6px;
  background: rgba(217, 217, 217, 1);
  border-radius: 4px;
  overflow: hidden;
  width: 100px;
  flex: 0 0 100px;
}

.scale-question__progress {
  height: 100%;
  background: var(--fqz-brand-color-1);
  border-radius: 4px;
  width: 0;
}
</style>
