<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { StarsQuestionReport } from '../../types/formatter'
import { computed } from 'vue'
import PointsReportItem from '../PointsReportItem.vue'
import PointsReportStars from '../PointsReportStars.vue'
import PointsReportVariants from '../PointsReportVariants.vue'

interface StarsQuestionProps extends BasePointsProps {
  question: StarsQuestionReport
  number: number
}

const props = defineProps<StarsQuestionProps>()

const rating = computed(() => props.question.rating || 0)
const maxRating = computed(() => {
  if (!props.question.starRatingOptions)
    return 5
  return props.question.starRatingOptions.count
})
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="stars-question">
      <PointsReportStars
        :rating="rating"
        :max-rating="maxRating"
        :label="question.label || ''"
      />
      <PointsReportVariants
        v-if="question.clarifyingQuestion && !question.commentEnabled"
        :label="question.clarifyingQuestion || ''"
        :variants="question.assessments || []"
        :text-value="question.comment"
        :self-variant="question.selfVariantText"
        :self-variant-label="question.selfVariantLabel"
      />
    </div>
  </PointsReportItem>
</template>

<style scoped>
.stars-question {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
</style>
