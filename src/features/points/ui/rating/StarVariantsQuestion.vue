<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { StarVariantsQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'
import PointsReportStars from '../PointsReportStars.vue'
import PointsReportVariants from '../PointsReportVariants.vue'

interface StarVariantsQuestionProps extends BasePointsProps {
  question: StarVariantsQuestionReport
  number: number
}

defineProps<StarVariantsQuestionProps>()
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="star-variants-question">
      <div
        v-for="variantData in question.formattedVariants"
        :key="variantData.id"
        class="star-variants-question__variant"
      >
        <div class="star-variants-question__variant-text">
          {{ variantData.variant }}
        </div>
        <div v-if="variantData.skipped" class="points-report-skipped-text">
          {{ question.skipText ?? 'Не готов(а) оценить' }}
        </div>
        <div v-else-if="variantData.unrequiredSkipped" class="points-report-skipped-unrequired-text">
          –
        </div>
        <div v-else class="star-variants-question__rating">
          <PointsReportStars
            :rating="variantData.rating"
            :max-rating="question.starRatingOptions?.count ?? 0"
            :label="question.starRatingOptions?.labelsArray[variantData.rating - 1] ?? ''"
          />
          <PointsReportVariants
            v-if="variantData.extra"
            :label="question.clarifyingQuestion ?? ''"
            :text-value="variantData.extra.textValue"
            :variants="variantData.extra.variants"
            :self-variant="variantData.extra.selfVariant"
            :self-variant-label="question.selfVariantLabel"
          />
        </div>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.star-variants-question {
  flex-direction: column;
}

.star-variants-question__assessments-variants {
  padding-left: 15px;
}

.star-variants-question__variant {
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.star-variants-question__variant:first-child {
  padding-top: 0;
}

.star-variants-question__variant:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.star-variants-question__variant-text {
  font-size: 14px;
  line-height: 1.1;
  margin-bottom: 10px;
}

.star-variants-question__rating {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.star-variants-question__skipped {
  font-size: 14px;
  color: #666;
  font-style: italic;
}
</style>
