<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { SmileQuestionReport } from '../../types/formatter'
import { computed } from 'vue'
import PointsReportItem from '../PointsReportItem.vue'
import PointsReportVariants from '../PointsReportVariants.vue'

interface SmileQuestionProps extends BasePointsProps {
  question: SmileQuestionReport
  number: number
}

const props = defineProps<SmileQuestionProps>()

const smileAnswer = computed(() => ({
  url: props.question.smileUrl,
  label: props.question.smileLabel,
  comment: props.question.comment,
}))
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="smile-question">
      <div v-if="smileAnswer" class="smile-question__answer">
        <div class="smile-question__smile">
          <img
            v-if="smileAnswer.url"
            :src="smileAnswer.url"
            :alt="smileAnswer.label || ''"
            class="smile-question__image"
          >
          <div v-if="smileAnswer.label" class="smile-question__label">
            {{ smileAnswer.label }}
          </div>
        </div>
        <PointsReportVariants
          v-if="(question.extra || question.comment) && !question.commentEnabled"
          :label="question.clarifyingQuestion || ''"
          :text-value="question.extra?.textValue || question.comment"
          :variants="question.extra?.variants"
          :self-variant="question.extra?.selfVariant"
          :self-variant-label="question.extra?.selfVariantLabel"
          :self-variant-file="question.extra?.selfVariantFile"
        />
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.smile-question__answer {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.smile-question__smile {
  display: flex;
  align-items: center;
}

.smile-question__image {
  width: 34px;
  height: 34px;
  object-fit: contain;
}
</style>
