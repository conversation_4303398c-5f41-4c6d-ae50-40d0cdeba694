<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { DiffQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'

interface DiffQuestionProps extends BasePointsProps {
  question: DiffQuestionReport
  number: number
}

defineProps<DiffQuestionProps>()

function getCircleButtonStyle(index: number) {
  const size = index === 0 || index === 4 ? 'large' : index === 1 || index === 3 ? 'medium' : 'small'
  const colors = [
    'rgba(116, 129, 142, 1)',
    'rgba(166, 176, 185, 1)',
    'rgba(207, 216, 220, 1)',
    'rgba(140, 163, 231, 1)',
    'rgba(63, 101, 241, 1)',
  ] // Default gradient

  return {
    'borderRadius': '50%',
    'border': `2px solid var(--color)`,
    'backgroundColor': '#fff',
    'width': size === 'large' ? '24px' : size === 'medium' ? '18px' : '14px',
    'height': size === 'large' ? '24px' : size === 'medium' ? '18px' : '14px',
    '--color': colors[index],
  }
}

function diffQuestionClasses(rows: DiffQuestionReport['differentialRows']) {
  return {
    'diff-question': true,
    'diff-question--only-end-labels': rows.every(row => row.endLabel && !row.startLabel),
    'diff-question--only-start-labels': rows.every(row => row.startLabel && !row.endLabel),
    'diff-question--no-labels': rows.every(row => !row.startLabel && !row.endLabel),
  }
}
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div :class="diffQuestionClasses(question.differentialRows)">
      <div
        v-for="row in question.differentialRows"
        :key="row.id"
        class="diff-question-row"
      >
        <div
          class="diff-question-scale"
          :class="{
            'diff-question-scale--circle': question.semDifSetting.form === 'circle',
            'diff-question-scale--has-start-label': row.startLabel,
            'diff-question-scale--has-end-label': row.endLabel,
            'diff-question-scale--no-labels': !row.startLabel && !row.endLabel,
          }"
        >
          <div v-if="row.startLabel" class="diff-question-label diff-question-label--start">
            {{ row.startLabel }}
          </div>
          <div v-else class="diff-question-label diff-question-label--start diff-question-label--placeholder" />
          <div class="diff-question-buttons">
            <div
              v-for="index in 5"
              :key="index"
              class="diff-question-button"
              :class="{
                'diff-question-button--selected': row.rating === index,
                'diff-question-button--circle': question.semDifSetting.form === 'circle',
                'diff-question-button--rectangle': question.semDifSetting.form === 'rect',
              }"
              :style="question.semDifSetting.form === 'circle' ? getCircleButtonStyle(index - 1) : null"
            >
              <template v-if="question.semDifSetting.form === 'rect'">
                {{ index }}
              </template>
              <svg v-else class="diff-question-button__icon" width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 3.82353L5.94118 8.76471L13 1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
          </div>
          <div v-if="row.endLabel" class="diff-question-label diff-question-label--end">
            {{ row.endLabel }}
          </div>
          <span v-else class="diff-question-label diff-question-label--end diff-question-label--placeholder" />
        </div>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.diff-question-row {
  width: 100%;
  margin-bottom: 10px;
}

.diff-question-row:last-child {
  margin-bottom: 0;
}

.diff-question-scale {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 15px;
  justify-content: center;
}

.diff-question-scale--circle .diff-question-buttons {
  flex: 0 0 118px;
  justify-content: space-between;
  gap: 0;
  align-items: center;
}

.diff-question-scale--circle .diff-question-label {
  width: 197px;
}

.diff-question-scale--has-start-label {
  justify-content: flex-start;
}

.diff-question-scale--has-end-label {
  justify-content: flex-end;
}

.diff-question-scale--has-start-label.diff-question-scale--has-end-label {
  justify-content: space-between;
}

.diff-question-scale--no-labels .diff-question-scale--no-labels {
  justify-content: flex-start;
}

.diff-question--only-end-labels .diff-question-scale {
  justify-content: flex-start !important;
}

.diff-question--only-end-labels .diff-question-label--start {
  display: none;
}

.diff-question--no-labels .diff-question-scale {
  justify-content: start !important;
}

.diff-question--no-labels .diff-question-label--placeholder {
  display: none;
}

.diff-question-label {
  font-size: 12px;
  line-height: 1.1;
  color: rgba(0, 0, 0, 0.5);
  flex: 0 1 368px;
}

.diff-question-label--start {
  text-align: right;
}

.diff-question-label--end {
  text-align: left;
}

.diff-question-buttons {
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  width: 148px;
  gap: 2px;
  align-items: center;
}

.diff-question-button {
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  line-height: 1.2;
  flex: 1;
}

.diff-question-button--rectangle {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  color: black;
  font-size: 13px;
  line-height: 1.2;
}

.diff-question-button--circle {
  flex: 0 0 auto;
  min-height: 0;
  align-self: center;
  background-color: transparent;
}

.diff-question-button--selected.diff-question-button--rectangle {
  background-color: var(--fqz-poll-main-color, #3f65f1);
  color: white;
}

.diff-question-button--selected.diff-question-button--circle {
  background-color: var(--color) !important;
}

.diff-question-button--selected.diff-question-button--circle .diff-question-button__icon {
  opacity: 1;
  width: 6px;
  height: 6px;
}

.diff-question-button__icon {
  opacity: 0;
}

.diff-question__comment {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.diff-question__comment-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.diff-question__comment-value {
  font-size: 14px;
  color: #333;
}

@media screen and (max-width: 679px) {
  .diff-question-row {
    margin-bottom: 15px;
  }

  .diff-question-scale {
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between !important;
  }

  .diff-question-buttons {
    width: 100%;
    max-width: none;
  }

  .diff-question-scale--circle .diff-question-buttons {
    flex: 0 0 auto;
  }

  .diff-question-label {
    font-size: 13px;
    flex: 1 0 auto;
    align-self: center;
  }

  .diff-question-label--start {
    text-align: left !important;
    order: -2;
    align-self: flex-start;
    margin-right: auto;
  }

  .diff-question-label--end {
    align-self: flex-end;
    order: -1;
    text-align: right !important;
    margin-left: auto;
  }

  .diff-question-scale--has-start-label .diff-question-label--placeholder {
    display: none;
  }

  .diff-question-scale--has-end-label .diff-question-label--placeholder {
    display: none;
  }

  .diff-question-scale--no-labels .diff-question-label--placeholder {
    display: none;
  }

  .diff-question-button--circle:first-child,
  .diff-question-button--circle:last-child {
    width: 48px !important;
    height: 48px !important;
  }

  .diff-question-button--circle:nth-child(2),
  .diff-question-button--circle:nth-child(4) {
    width: 36px !important;
    height: 36px !important;
  }

  .diff-question-button--circle:nth-child(3) {
    width: 30px !important;
    height: 30px !important;
  }
}

@media screen and (max-width: 450px) {
  .diff-question-label {
    width: 134px;
  }
}

@media screen and (max-width: 321px) {
  .diff-question-label {
    width: 120px;
  }
}
</style>
