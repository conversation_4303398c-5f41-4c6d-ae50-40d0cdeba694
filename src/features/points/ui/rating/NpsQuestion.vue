<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { NpsQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'
import PointsReportVariants from '../PointsReportVariants.vue'

interface NpsQuestionProps extends BasePointsProps {
  question: NpsQuestionReport
  number: number
}

defineProps<NpsQuestionProps>()
</script>

<template>
  <PointsReportItem :number="number" :question="question" :view="view">
    <template v-if="question.isVariants">
      <div class="nps-question__variants">
        <div v-for="(variantData, index) in question.formattedVariants" :key="index" class="nps-question__item">
          <div v-if="variantData.variant" class="nps-question__variant-name">
            {{ variantData.variant }}
          </div>
          <div v-if="variantData.unrequiredSkipped" class="points-report-skipped-unrequired-text">
            —
          </div>
          <div v-else-if="variantData.skipped" class="points-report-skipped-text">
            {{ question.skipText }}
          </div>
          <div v-else class="nps-question__rating">
            <div class="nps-question__score" :style="{ backgroundColor: variantData.color }">
              {{ variantData.rating }}
            </div>
            <div v-if="variantData.extra" class="nps-question__extra-variants">
              <PointsReportVariants
                :label="question.clarifyingQuestion ?? ''"
                :text-value="variantData.extra.textValue"
                :variants="variantData.extra.variants"
                :self-variant="variantData.extra.selfVariant"
                :self-variant-label="variantData.extra.selfVariantLabel"
                :self-variant-file="variantData.extra.selfVariant ? variantData.extra.selfVariantFile : null"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-if="question.extra" class="nps-question__extra-variants">
        <PointsReportVariants
          :label="question.clarifyingQuestion ?? ''"
          :text-value="question.extra.textValue"
          :variants="question.extra.variants"
          :self-variant="question.extra.selfVariant"
          :self-variant-label="question.extra.selfVariantLabel"
          :self-variant-file="question.extra.selfVariant ? question.extra.selfVariantFile : null"
        />
      </div>
    </template>
    <template v-else>
      <div class="nps-question__score" :style="{ backgroundColor: question.color }">
        {{ question.rating }}
      </div>
    </template>
  </PointsReportItem>
</template>

<style scoped>
.nps-question,
.nps-question__variants {
  flex-direction: column;
}

.nps-question__item {
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.nps-question__item:first-child {
  padding-top: 0;
}

.nps-question__item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.nps-question__score {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 24px;
  border-radius: 3px;
  text-align: center;
  color: white;
  font-size: 13px;
  line-height: 1.1;
}

.nps-question__rating {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
