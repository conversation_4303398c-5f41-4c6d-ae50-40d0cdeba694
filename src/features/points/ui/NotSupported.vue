<script setup lang="ts">
import type { BasePointsProps } from '../types'
import { computed } from 'vue'

const props = defineProps<PointsReportItemProps>()

interface PointsReportItemProps extends BasePointsProps {}

const numberString = computed(() => {
  return `${props.number}.`
})
</script>

<template>
  <div class="points-report-item">
    <div class="points-report-item__number not-support">
      {{ numberString }}
    </div>
    <div class="points-report-item__main">
      <div class="points-report-item__header not-support">
        <h3 class="points-report-item__description" v-html="question.description" />
      </div>
      <div class="not-supported-question">
        Данный тип вопроса не поддерживается в отчёте о тестировании.
      </div>
    </div>
  </div>
</template>

<style scoped>
.not-supported-question {
  font-size: 12px;
  font-weight: 400;
  line-height: 120%;
  color: #000000;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 10px 15px;
  opacity: 70%;
}

.points-report-item {
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  line-height: 1.1;
  font-size: 14px;
  gap: 13px;
}

.points-report-item:first-child {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.points-report-item:last-child {
  border-bottom: none;
}

.points-report-item__main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.points-report-item__number {
  flex-shrink: 0;
  flex: 0 0 24px;
  font-size: 16px;
  font-weight: 700;
}

.points-report-item__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.points-report-item__description {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
}

.points-report-item__description :deep(br) {
  display: none;
}

.points-report-item__description :deep(p) {
  margin: 0;
  line-height: 1;
  padding: 0;
}

.not-support {
  opacity: 50%;
}

@media screen and (max-width: 679px) {
  .points-report-item__number,
  .points-report-item__description {
    font-size: 14px;
    line-height: 1.1;
  }

  .points-report-item__description {
    text-wrap: initial;
  }
}
</style>
