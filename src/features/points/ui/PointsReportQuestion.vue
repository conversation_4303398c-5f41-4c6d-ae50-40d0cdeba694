<script lang="ts">
import type { BasePointsProps } from '../types'
import type { QuestionReport } from '../types/formatter'
import { computed, defineComponent, toValue } from 'vue'
import {
  AddressQuestion,
  CardSortingQuestion,
  ClassifierQuestion,
  DateQuestion,
  DiffQuestion,
  DistributionScaleQuestion,
  FileQuestion,
  FilialsQuestion,
  GalleryQuestion,
  Matrix3dQuestion,
  MatrixQuestion,
  MediaVariantsQuestion,
  NotSupported,
  NpsQuestion,
  PriorityQuestion,
  QuizQuestion,
  RatingScaleQuestion,
  ScaleQuestion,
  SmileQuestion,
  StarsQuestion,
  StarVariantsQuestion,
  TextQuestion,
  VariantsQuestion,
} from './index'

interface PointsReportQuestionProps extends BasePointsProps {
  question: QuestionReport
}

export default defineComponent({
  name: 'PointsReportQuestion',

  components: {
    TextQuestion,
    AddressQuestion,
    CardSortingQuestion,
    StarsQuestion,
    StarVariantsQuestion,
    DateQuestion,
    QuizQuestion,
    GalleryQuestion,
    SmileQuestion,
    NpsQuestion,
    RatingScaleQuestion,
    ScaleQuestion,
    FileQuestion,
    ClassifierQuestion,
    MediaVariantsQuestion,
    Matrix3dQuestion,
    MatrixQuestion,
    VariantsQuestion,
    PriorityQuestion,
    NotSupported,
  },

  props: {
    question: {
      type: Object,
      required: true,
    },
    number: {
      type: Number,
      required: true,
    },
  },

  setup(props: PointsReportQuestionProps) {
    const COMPONENT_MAP = {
      1: VariantsQuestion, // VARIANTS_QUESTION
      2: TextQuestion, // TEXT_QUESTION
      3: DateQuestion, // DATE_QUESTION
      4: AddressQuestion, // ADDRESS_QUESTION
      5: FileQuestion, // FILE_QUESTION
      6: QuizQuestion, // QUIZ_QUESTION
      7: StarVariantsQuestion, // STAR_VARIANTS_QUESTION
      8: PriorityQuestion, // PRIORITY_QUESTION
      9: MediaVariantsQuestion, // MEDIA_VARIANTS_QUESTION
      10: GalleryQuestion, // GALLERY_QUESTION
      11: SmileQuestion, // SMILE_QUESTION
      13: MatrixQuestion, // MATRIX_QUESTION
      12: NpsQuestion, // NPS_QUESTION
      14: DiffQuestion, // DIFF_QUESTION
      15: StarsQuestion, // STARS_QUESTION
      17: FilialsQuestion, // FILIALS_QUESTION
      18: RatingScaleQuestion, // RATING_SCALE_QUESTION
      19: ClassifierQuestion, // CLASSIFIER_QUESTION
      20: ScaleQuestion, // SCALE_QUESTION
      22: CardSortingQuestion, // CARD_SORTING_QUESTION
      21: Matrix3dQuestion, // MATRIX_3D_QUESTION
      23: DistributionScaleQuestion, // DISTRIBUTION_SCALE_QUESTION
      24: NotSupported, // NOT SUPPORTED QUESTION
    }

    const questionComponent = computed(() => {
      const component = COMPONENT_MAP[toValue(props.question).type as keyof typeof COMPONENT_MAP]
      if (!component) {
        console.error(`Component for question type ${toValue(props.question).type} not found`)
      }
      return component ?? null
    })

    const unwrappedQuestion = computed(() => {
      return toValue(props.question)
    }) as any

    return {
      questionComponent,
      unwrappedQuestion,
    }
  },
})
</script>

<template>
  <component
    :is="questionComponent"
    v-if="questionComponent"
    :question="unwrappedQuestion"
    :number="number"
    :view="view"
  />
</template>
