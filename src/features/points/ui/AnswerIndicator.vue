<script setup lang="ts">
import type { AnswerStatus } from '../types'

defineProps<{
  type: AnswerStatus
}>()
</script>

<template>
  <div class="answer-indicator" :class="type">
    <svg v-if="type === 'wrong'" width="10" height="3" viewBox="0 0 10 3" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 1.5H9" stroke="#FF0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
    <svg v-else-if="type === 'correct'" width="10" height="11" viewBox="0 0 10 11" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 5.5H9M5 9.5V1.5" stroke="#00C968" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
    <svg v-else-if="type === 'intermediate'" width="10" height="13" viewBox="0 0 10 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 5H9M1 12H9M5 9V1" stroke="#F4A224" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  </div>
</template>

<style scoped>
.answer-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.answer-indicator > svg {
  display: block;
}
</style>
