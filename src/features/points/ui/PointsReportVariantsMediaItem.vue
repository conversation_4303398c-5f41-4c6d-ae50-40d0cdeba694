<script setup lang="ts">
import FilePreview from '@shared/ui/FilePreview.vue'

interface Props {
  variant: string
  fileUrl: string | null
  previewUrl: string | null
  id: string | number
  type: 'image' | 'video' | 'audio' | null
  label: string | null
}

defineProps<Props>()
</script>

<template>
  <div class="points-report-variants-media-item">
    <div v-if="fileUrl" class="points-report-variants-media-item__preview">
      <FilePreview
        :id="String(id)"
        view="compact"
        :file="undefined"
        :preview-url="previewUrl || ''"
        :full-url="fileUrl"
        :type="type || 'image'"
        group="points-report-variants"
      />
    </div>
    <div class="points-report-variants-media-item__content">
      <div v-if="label" class="points-report-variants-media-item__label">
        {{ label }}
      </div>
      <div class="points-report-variants-media-item__text">
        {{ variant }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.points-report-variants-media-item {
  display: flex;
  gap: 15px;
  align-items: center;
}

.points-report-variants-media-item__preview {
  flex: 0 0 auto;
}

.points-report-variants-media-item__preview :deep(.file-preview--compact) {
  width: 35px;
  height: 35px;
}

.points-report-variants-media-item__preview :deep(.file-preview__icon svg) {
  width: 22px;
  height: 22px;
}

.points-report-variants-media-item__content {
  flex: 1;
  min-width: 0;
  display: flex;
  gap: 2px;
  flex-direction: column;
}

.points-report-variants-media-item__label {
  font-size: 12px;
  line-height: 1.3;
  color: rgba(115, 128, 141, 1);
}

.points-report-variants-media-item__text {
  font-size: 12px;
  flex-grow: 1;
  line-height: 1.3;
}

.points-report-variants-media-item__preview :deep(.file-preview__close) {
  display: none !important;
}

.points-report-variants-media-item__preview :deep(.file-preview) {
  margin: 0;
}

@media screen and (max-width: 768px) {
  .points-report-variants-media-item {
    flex-wrap: wrap;
    gap: 10px 15px;
  }
}
</style>
