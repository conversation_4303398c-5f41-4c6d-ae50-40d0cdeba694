<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { Matrix3dQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'

interface Matrix3dQuestionProps extends BasePointsProps {
  question: Matrix3dQuestionReport
  number: number
}

defineProps<Matrix3dQuestionProps>()

function columnHasNoAnswers(answers: Record<string, any[]>, columnId: number) {
  return answers[columnId.toString()]?.length === 0
}

function columnHasSkipped(answers: Record<string, any[]>, columnId: number) {
  return answers[columnId.toString()]?.length > 0 && answers[columnId.toString()][0]?.id === '-1'
}
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="matrix-3d">
      <div v-for="row in question.rows" :key="row.id" class="matrix-3d__row-group">
        <div class="matrix-3d__row-title">
          {{ row.name }}
        </div>

        <div v-if="row.skipped" class="points-report-skipped-text">
          {{ question.skipText }}
        </div>
        <div v-else-if="row.unrequiredSkipped" class="points-report-skipped-unrequired-text">
          –
        </div>
        <div v-else class="matrix-3d__columns">
          <div v-for="column in question.columns" :key="column.id" class="matrix-3d__column">
            <div class="matrix-3d__column-title">
              {{ column.name }}
            </div>
            <div v-if="columnHasSkipped(row.answers, column.id)" class="points-report-skipped-text">
              {{ question.skipText }}
            </div>
            <div v-else-if="columnHasNoAnswers(row.answers, column.id)" class="points-report-skipped-unrequired-text">
              –
            </div>
            <div v-else class="matrix-3d__variants">
              <div
                v-for="variant in (row.answers[column.id.toString()] || [])"
                :key="variant.id"
                class="matrix-3d__variant"
              >
                <div>
                  {{ variant.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.matrix-3d__row-group {
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.matrix-3d__row-group:first-child {
  padding-top: 0;
}

.matrix-3d__row-group:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.matrix-3d__row-title {
  font-size: 12px;
  line-height: 1.1;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 10px;
}

.matrix-3d__columns {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-left: 15px;
}

.matrix-3d__column {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.matrix-3d__column-title {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  line-height: 1.1;
}

.matrix-3d__column-skip {
  font-style: italic;
  margin-left: 4px;
}

.matrix-3d__variants {
  font-size: 14px;
  line-height: 1.1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.points-report-skipped-unrequired-text {
  font-size: 14px;
  line-height: 1.1;
}
</style>
