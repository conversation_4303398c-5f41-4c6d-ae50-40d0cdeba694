<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { MatrixQuestionReport } from '../../types/formatter'
import LabeledField from '../LabeledField.vue'
import PointsReportItem from '../PointsReportItem.vue'
import PointsReportVariants from '../PointsReportVariants.vue'

interface MatrixQuestionProps extends BasePointsProps {
  question: MatrixQuestionReport
  number: number
}

defineProps<MatrixQuestionProps>()

function getColumnValueClasses(row: { skipped?: boolean }) {
  return row.skipped ? 'points-report-skipped-text' : ''
}

function formatVariants(
  variants: Array<{ id: string, text: string }> | null | undefined,
) {
  return variants?.map(v => ({
    variant: v.text,
    id: v.id,
    file_url: null,
    preview_url: null,
    description: null,
    file_id: null,
  }))
}
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="matrix-question">
      <div
        v-for="row in question.rows"
        :key="row.rawTitle"
        class="matrix-question__row"
        :class="{ 'matrix-question__row_divided': question.rowsDivider }"
      >
        <div class="matrix-question__row-title">
          {{ row.rawTitle }}
        </div>
        <div class="matrix-question__row-content">
          <div
            v-if="row.unrequiredSkipped"
            class="points-report-skipped-unrequired-text"
          >
            –
          </div>
          <div v-else-if="row.skipped" class="points-report-skipped-text">
            {{ question.skipText }}
          </div>
          <div v-else class="matrix-question__row-cols">
            <div
              v-for="col in row.selectedCols"
              :key="col"
              class="matrix-question__row-col"
            >
              <span :class="getColumnValueClasses(row)">{{ col }}</span>
            </div>
          </div>
          <PointsReportVariants
            v-if="row.extra"
            :label="question.clarifyingQuestion ?? ''"
            :variants="formatVariants(row.extra.variants)"
            :text-value="row.extra.text"
            :self-variant-label="question.selfVariantLabel ?? ''"
            :self-variant="row.extra.selfVariant"
            value-font-size="small"
            class="matrix-question__assessment-variants"
          />
        </div>
      </div>
    </div>
    <template #correctAnswer>
      <LabeledField
        v-if="question.formattedCorrectAnswers?.length"
        label="Правильный ответ"
        class="matrix-question__correct-answers"
      >
        <div v-for="answer in question.formattedCorrectAnswers" :key="answer" class="matrix-question__correct-answers-list-item">
          {{ answer }}
        </div>
      </LabeledField>
    </template>
  </PointsReportItem>
</template>

<style scoped>
.matrix-question {
  padding: 0;
}

.matrix-question__row-group {
  margin-bottom: 24px;
}

.matrix-question__row-group:last-child {
  margin-bottom: 0;
}

.matrix-question__row {
  padding-top: 10px;
  padding-bottom: 10px;
}

.matrix-question__row_divided {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.matrix-question__row:first-child {
  padding-top: 0;
}

.matrix-question__row:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}

.matrix-question__row-title {
  font-weight: 700;
  opacity: 0.5;
  font-size: 12px;
  line-height: 1.1;
  margin-bottom: 10px;
}

.matrix-question__row-content {
  padding-left: 15px;
}

.matrix-question__row-cols {
  font-size: 14px;
  line-height: 1.1;
  display: flex;
  gap: 5px;
  flex-direction: column;
}

.matrix-question__assessment-variants {
  margin-top: 10px;
}

.matrix-question__correct-answer-row {
  font-size: 12px;
  line-height: 1.1;
  margin-bottom: 5px;
}

.matrix-question__correct-answer-row:last-child {
  margin-bottom: 0;
}

.matrix-question__correct-answers-list:last-child {
  margin-bottom: 0;
}

.matrix-question__correct-answers-list-item {
  margin-bottom: 5px;
  font-size: 12px;
  line-height: 1.1;
}

.matrix-question__correct-answers-list-item:last-child {
  margin-bottom: 0;
}
</style>
