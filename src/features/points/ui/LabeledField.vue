<script setup lang="ts">
import type { PropType } from 'vue'
import { computed } from 'vue'

const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  gap: {
    type: String,
    // small, medium, large
    default: 'medium',
  },
  textSize: {
    type: String as PropType<'small' | 'medium' | 'large'>,
    default: 'medium',
  },
})

const labeledFieldClasses = computed(() => {
  return {
    'labeled-field': true,
    [`labeled-field--gap-${props.gap}`]: true,
    [`labeled-field--text-size-${props.textSize}`]: true,
  }
})
</script>

<template>
  <div :class="labeledFieldClasses">
    <div class="labeled-field__label">
      {{ label }}
    </div>
    <div v-if="$slots.default" class="labeled-field__value">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.labeled-field {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  line-height: 1.2;
}

.labeled-field--gap-small {
  gap: 2px;
}

.labeled-field--gap-medium {
  gap: 5px;
}

.labeled-field--gap-large {
  gap: 8px;
}

.labeled-field__label {
  opacity: 0.5;
}

.labeled-field--text-size-small .labeled-field__value {
  font-size: 12px;
  line-height: 1.1;
}

.labeled-field--text-size-medium .labeled-field__value {
  font-size: 14px;
  line-height: 1.1;
}

.labeled-field--text-size-large .labeled-field__value {
  font-size: 16px;
}
</style>
