<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { GalleryQuestionReport } from '../../types/formatter'
import FilePreview from '@/shared/ui/FilePreview.vue'
import { computed } from 'vue'
import PointsReportItem from '../PointsReportItem.vue'
import PointsReportStars from '../PointsReportStars.vue'

interface Props extends BasePointsProps {
  question: GalleryQuestionReport
  number: number
}

const props = defineProps<Props>()

const hasAnswers = computed(() =>
  !props.question.unrequiredSkipped && props.question.media.length > 0,
)
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="gallery-question">
      <div v-if="hasAnswers" class="gallery-question__answers">
        <div
          v-for="item in question.media"
          :key="item.id"
          class="gallery-question__answer"
        >
          <div class="gallery-question__preview">
            <FilePreview
              :id="String(item.id)"
              view="compact"
              :file="undefined"
              :preview-url="item.poster"
              :full-url="item.url"
              :type="item.type"
              :group="`points-report-gallery-question-${number}`"
              :open-in-new-tab="view === 'print'"
            />
          </div>

          <div class="gallery-question__content">
            <div class="gallery-question__description">
              {{ item.description }}
            </div>
          </div>

          <div class="gallery-question__rating">
            <div v-if="item.unrequiredSkipped" class="points-report-unrequired-skipped-text">
              –
            </div>
            <PointsReportStars
              v-else
              :rating="item.rating"
              :max-rating="5"
              :show-rating-number="false"
            />
          </div>
        </div>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.gallery-question__answers {
  display: flex;
  flex-direction: column;
}

.gallery-question__answer {
  display: flex;
  gap: 15px;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.gallery-question__answer:first-child {
  padding-top: 0;
}

.gallery-question__answer:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}

.gallery-question__preview {
  flex: 0 0 auto;
}

.gallery-question__preview :deep(.file-preview--compact) {
  width: 35px;
  height: 35px;
}

.gallery-question__preview :deep(.file-preview__icon svg) {
  width: 22px;
  height: 22px;
}

.gallery-question__content {
  flex: 1;
  min-width: 0; /* Prevent flex item from overflowing */
  display: flex;
  gap: 15px;
  align-items: center;
}

.gallery-question__description {
  font-size: 12px;
  flex-grow: 1;
  line-height: 1.3;
}

.gallery-question__comment {
  margin-top: 16px;
  font-style: italic;
  color: var(--fqz-poll-text-secondary);
}

.gallery-question__rating {
  flex: 0 0 130px;
}

.gallery-question__preview :deep(.file-preview__close) {
  display: none !important;
}

.gallery-question__preview :deep(.file-preview) {
  margin: 0;
}

@media screen and (max-width: 768px) {
  .gallery-question__answer {
    flex-wrap: wrap;
    gap: 10px 15px;
  }
  .gallery-question__rating {
    flex: 0 0 100%;
  }
}
</style>
