<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { PriorityQuestionReport } from '../../types/formatter'
import LabeledField from '../LabeledField.vue'
import PointsReportItem from '../PointsReportItem.vue'

interface PriorityQuestionProps extends BasePointsProps {
  question: PriorityQuestionReport
  number: number
}

defineProps<PriorityQuestionProps>()
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="priority-question">
      <div v-if="question.variants?.length" class="priority-question__answers">
        <div
          v-for="(variant, index) in question.variants"
          :key="variant.id"
          class="priority-question__answer"
        >
          <span class="priority-question__answer-index">{{ index + 1 }}.</span>
          <span class="priority-question__answer-text">
            {{ variant.text }}
          </span>
        </div>
      </div>
    </div>
    <template #correctAnswer>
      <LabeledField
        v-if="question.correctFormattedAnswers?.length"
        label="Правильный ответ"
        class="priority-question__correct-answers"
      >
        <div
          v-for="(answer, index) in question.correctFormattedAnswers"
          :key="index"
          class="priority-question__correct-answer"
        >
          <span class="priority-question__correct-answer-index">{{ index + 1 }}.</span>
          <span class="priority-question__correct-answer-text">{{ answer }}</span>
        </div>
      </LabeledField>
    </template>
  </PointsReportItem>
</template>

<style scoped>
.priority-question__answers {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.priority-question__answer {
  display: flex;
}

.priority-question__answer-index {
  min-width: 20px;
  flex: 0 0 auto;
}

.priority-question__correct-answer {
  margin-bottom: 5px;
  font-size: 12px;
  line-height: 1.1;
  display: flex;
}

.priority-question__correct-answer:last-child {
  margin-bottom: 0;
}

.priority-question__correct-answer .priority-question__correct-answer-index {
  flex: 0 0 auto;
  min-width: 17px;
  text-align: right;
  margin-right: 5px;
}
</style>
