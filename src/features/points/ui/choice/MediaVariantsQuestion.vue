<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { MediaVariantsQuestionReport } from '../../types/formatter'
import FilePreview from '@/shared/ui/FilePreview.vue'
import { computed } from 'vue'
import LabeledField from '../LabeledField.vue'
import PointsReportItem from '../PointsReportItem.vue'

interface Props extends BasePointsProps {
  question: MediaVariantsQuestionReport
  number: number
}

const props = defineProps<Props>()

const selectedVariants = computed(() => {
  if (!props.question.selectedMedia.length)
    return []
  return props.question.chooseMedia.filter(variant =>
    props.question.selectedMedia.includes(String(variant.id)),
  )
})
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="media-variants">
      <!-- Selected answers -->
      <div v-if="selectedVariants.length" class="media-variants__section">
        <div class="media-variants__files">
          <FilePreview
            v-for="variant in selectedVariants"
            :id="String(variant.id)"
            :key="variant.id"
            :file="undefined"
            :preview-url="variant.poster"
            :full-url="variant.url"
            :name="variant.description"
            :type="variant.type"
            view="default"
            :group="`points-report-item__media-variants-${number}`"
            :open-in-new-tab="view === 'print'"
          />
        </div>
      </div>
    </div>
    <template v-if="question.correctAnswerVariants.length" #correctAnswer>
      <!-- Correct answers -->
      <LabeledField
        v-if="question.correctAnswerVariants.length"
        label="Правильный ответ"
        class="media-variants__section media-variants__section--correct"
      >
        <div class="media-variants__files">
          <FilePreview
            v-for="variant in question.correctAnswerVariants"
            :id="String(variant.id)"
            :key="variant.id"
            :file="undefined"
            :preview-url="variant.poster"
            :full-url="variant.url"
            :name="variant.description"
            :type="variant.type"
            view="default"
            :group="`points-report-item__media-variants-${number}-correct`"
            :open-in-new-tab="view === 'print'"
          />
        </div>
      </LabeledField>
    </template>
  </PointsReportItem>
</template>

<style scoped>
.media-variants__section:last-child {
  margin-bottom: 0;
}

.media-variants__files {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.media-variants__files :deep(.file-preview__close) {
  display: none;
}

.media-variants__files :deep(.file-preview__name) {
  margin-top: 5px;
  font-size: 12px;
}
</style>
