<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { ClassifierQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'

interface ClassifierQuestionProps extends BasePointsProps {
  question: ClassifierQuestionReport
  number: number
}

defineProps<ClassifierQuestionProps>()

function splitPath(path: string | null) {
  if (!path)
    return []
  return path.split(' / ')
}
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="classifier-question">
      <div class="classifier-question__answers">
        <template v-if="question.selectedVariants.length">
          <div
            v-for="variant in question.selectedVariants"
            :key="variant.id"
            class="classifier-question__answer"
          >
            <div v-if="question.isTreeType && variant.path">
              <span
                v-for="(pathItem, pathIndex) in splitPath(variant.path)"
                :key="pathIndex"
                class="classifier-question__answer-path-item"
              >
                {{ pathItem }}
                <span v-if="pathIndex !== splitPath(variant.path).length - 1">
                  /
                </span>
              </span>
            </div>
            <div v-else>
              {{ variant.text }}
            </div>
          </div>
        </template>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.classifier-question__answers {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.classifier-question__answer-path-item {
  opacity: 0.5;
}

.classifier-question__answer-path-item:not(:last-child) {
  margin-right: 2px;
}

.classifier-question__answer-path-item:last-child {
  opacity: 1;
}
</style>
