<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { VariantsQuestionReport } from '../../types/formatter'
import FilePreview from '@/shared/ui/FilePreview.vue'
import { computed } from 'vue'
import LabeledField from '../LabeledField.vue'
import PointsReportItem from '../PointsReportItem.vue'

interface VariantsQuestionProps extends BasePointsProps {
  question: VariantsQuestionReport
  number: number
}

const props = defineProps<VariantsQuestionProps>()

const correctAnswersText = computed(() => {
  if (!props.question.rightAnswerVariants.length)
    return ''

  return props.question.rightAnswerVariants
    .map(variant => variant.variant)
    .join(', ')
})
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="variants-question">
      <div class="variants-question__items">
        <div
          v-for="variant in question.selectedVariants"
          :key="variant.id"
          class="variants-question__item"
          :class="{
            'variants-question__item--file': variant.file_url || variant.preview_url,
          }"
        >
          <div class="variants-question__content">
            <div v-if="variant.file_url || variant.preview_url" class="variants-question__preview">
              <FilePreview
                :id="String(variant.id)"
                view="compact"
                :file="undefined"
                :preview-url="(variant.preview_url as string)"
                :full-url="(variant.file_url as string)"
                :type="variant.type || 'image'"
                :group="`points-report-variants-preview-${number}`"
                :open-in-new-tab="view === 'print'"
              />
            </div>
            <div class="variants-question__answer">
              {{ variant.variant }}
            </div>
          </div>
        </div>
        <div
          v-if="question.selfVariant"
          class="variants-question__item"
        >
          <div class="variants-question__content">
            <div class="variants-question__answer variants-question__answer--self-variant">
              <div class="variants-question__label">
                {{ question.selfVariantLabel || 'Свой вариант' }}
              </div>
              <div class="variants-question__text">
                {{ question.selfVariant }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #correctAnswer>
      <LabeledField
        v-if="question.rightAnswerVariants.length"
        label="Правильный ответ"
        class="variants-question__correct-answers"
      >
        <div v-if="question.variantsHaveMedia" class="variants-question__correct-answers-list">
          <div
            v-for="variant in question.rightAnswerVariants"
            :key="variant.id"
            class="variants-question__item"
            :class="{
              'variants-question__item--file': variant.file_url || variant.preview_url,
            }"
          >
            <div class="variants-question__content">
              <div v-if="variant.file_url || variant.preview_url" class="variants-question__preview">
                <FilePreview
                  :id="String(variant.id)"
                  view="compact"
                  :file="undefined"
                  :preview-url="(variant.preview_url as string)"
                  :full-url="(variant.file_url as string)"
                  :type="variant.type || 'image'"
                  :group="`points-report-variants-correct-preview-${number}`"
                  :open-in-new-tab="view === 'print'"
                />
              </div>
              <div class="variants-question__answer">
                {{ variant.variant }}
              </div>
            </div>
          </div>
        </div>
        <div v-else class="variants-question__correct-answer">
          {{ correctAnswersText }}
        </div>
      </LabeledField>
    </template>
  </PointsReportItem>
</template>

<style scoped>
.variants-question__items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.variants-question__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.variants-question__item--file .variants-question__answer {
  font-size: 12px;
  line-height: 1.2;
}

.variants-question__content {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.variants-question__preview {
  flex: 0 0 auto;
}

.variants-question__preview :deep(.file-preview--compact) {
  width: 35px;
  height: 35px;
}

.variants-question__answer {
  font-size: 14px;
}

.variants-question__correct-answer {
  font-size: 12px;
  line-height: 1.2;
}

.variants-question__correct-answers-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.variants-question__label {
  font-size: 12px;
  line-height: 1.2;
  margin-bottom: 5px;
  opacity: 0.5;
}

.variants-question__text {
  font-size: 14px;
  line-height: 1.2;
}

.variants-question__answer--self-variant .variants-question__label {
  margin-bottom: 2px;
  line-height: 1.2;
}

.variants-question__answer--self-variant .variants-question__text {
  line-height: 1.2;
}

:deep(.file-preview__close) {
  display: none !important;
}

:deep(.file-preview) {
  margin: 0;
}
</style>
