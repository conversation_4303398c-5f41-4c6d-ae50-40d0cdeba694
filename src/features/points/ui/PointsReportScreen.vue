<script setup lang="ts">
import type { FormattedResultsData } from '../types/formatter'
import PointsReport from './PointsReport.vue'

interface PointsReportScreenProps {
  results?: FormattedResultsData
  loading: boolean
  backButtonText: string
  showButtons?: boolean
  view?: 'default' | 'print'
}

withDefaults(defineProps<PointsReportScreenProps>(), {
  showButtons: true,
  view: 'default',
})

defineEmits<{
  (e: 'back'): void
  (e: 'print'): void
}>()
</script>

<template>
  <div class="points-report-screen" :class="view === 'print' ? 'points-report-screen--print' : ''">
    <PointsReport
      :results="results"
      :loading="loading"
      :view="view"
    />
    <div v-if="showButtons" class="points-report-screen__footer">
      <div class="points-report-screen__footer-buttons">
        <button class="points-report-screen__button" @click.prevent="$emit('back')">
          {{ backButtonText }}
        </button>
        <button class="points-report-screen__button points-report-screen__button--print" @click.prevent="$emit('print')">
          <svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 6.5V1.5H13V6.5M4 13.5H2.5C2.10218 13.5 1.72064 13.3361 1.43934 13.0444C1.15804 12.7527 1 12.357 1 11.9444V8.05556C1 7.643 1.15804 7.24734 1.43934 6.95561C1.72064 6.66389 2.10218 6.5 2.5 6.5H14.5C14.8978 6.5 15.2794 6.66389 15.5607 6.95561C15.842 7.24734 16 7.643 16 8.05556V11.9444C16 12.357 15.842 12.7527 15.5607 13.0444C15.2794 13.3361 14.8978 13.5 14.5 13.5H13M4 10.5H13V16.5H4V10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.points-report-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  max-width: 680px;
  width: 100%;
  margin: 0 auto;
  padding-bottom: 35px;
}

.points-report-screen--print {
  padding-bottom: 0;
  max-width: 1030px;
}

.points-report-screen__footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  flex-shrink: 1;
  gap: 10px;
}

.points-report-screen__footer-buttons {
  display: flex;
  gap: 10px;
}

.points-report-screen__button {
  padding: 10px 20px 10px;
  border-radius: 5px;
  height: 48px;
  cursor: pointer;
  color: var(--fqz-poll-back-button-text-color) !important;
  border: 2px solid var(--fqz-poll-back-button-stroke-color) !important;
  background: var(--fqz-poll-back-button-background-color) !important;
  border-radius: var(--fqz-poll-back-button-radius) !important;
  font-size: 16px;
  line-height: 1.1;
  font-weight: 500;
  align-self: center;
  font-family: inherit;
  transition: opacity 0.3s;
  text-decoration: none;
}

.points-report-screen__button--print {
  width: 48px;
  height: 48px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.points-report-screen__button--print svg {
  display: block;
}

@media screen and (max-width: 679px) {
  .points-report-screen {
    padding-top: 5px;
    padding-bottom: 15px;
  }

  .points-report-screen__footer {
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 20px;
  }

  .points-report-screen__footer,
  .points-report-screen__footer-buttons {
    width: 100%;
  }

  .points-report-screen__button,
  .points-report-screen__button--print {
    flex-grow: 1;
    height: 36px;
    padding: 0;
    flex-basis: 100%;
  }

  .points-report-screen__button--print {
    width: auto;
  }
}
</style>
