<script setup lang="ts">
import type { AnswerStatus, BasePointsProps } from '../types'
import { computed } from 'vue'
import AnswerIndicator from './AnswerIndicator.vue'
import LabeledField from './LabeledField.vue'

interface PointsReportItemProps extends BasePointsProps {
  notSupport?: boolean
}

const props = withDefaults(defineProps<PointsReportItemProps>(), {
  notSupport: false,
})

const numberString = computed(() => {
  return `${props.number}.`
})

const hasComment = computed(() => {
  return props.question.comment && props.question.commentEnabled === 1
})

const itemClasses = computed(() => {
  const classes = {
    'points-report-item': true,
  } as Record<string, boolean>

  if (props.question.skipped) {
    classes['points-report-item--skipped'] = true
  }
  else if (props.question.unrequiredSkipped) {
    classes['points-report-item--unrequired-skipped'] = true
  }

  if (hasComment.value) {
    classes['points-report-item--has-comment'] = true
  }

  return classes
})

const contentClasses = computed(() => {
  return {
    'points-report-item__content': true,
  }
})

const hasPoints = computed(() => {
  const points = props.question.points || 0
  const maxPoints = props.question.maxPoints || 0

  const hasAnswerPoints = points !== null && points !== undefined
  const hasAnswerMaxPoints = maxPoints !== null && maxPoints !== undefined && maxPoints !== 0

  return hasAnswerPoints && hasAnswerMaxPoints
})

const hasRightAnswer = computed(() => {
  const decodedAnswer = props.question.rightAnswer?.decodedAnswer || props.question.answer?.correct_answer

  if (decodedAnswer && typeof decodedAnswer === 'object') {
    return Object.values(decodedAnswer).some(Boolean)
  }

  if (Array.isArray(decodedAnswer)) {
    return decodedAnswer.length
  }

  return false
})

const shouldShowRightAnswer = computed(() => {
  if (props.question.isAnswerCorrect)
    return false

  const show = hasRightAnswer.value && hasPoints.value
  return show
})

const answerStatus = computed<AnswerStatus | null>(() => {
  if (props.question.answerStatus)
    return props.question.answerStatus

  if (!hasPoints.value || !hasRightAnswer.value)
    return null

  const unrequiredSkipped = props.question.unrequiredSkipped
  const skipped = props.question.skipped

  if (unrequiredSkipped && !skipped)
    return null

  const isCorrect = props.question.isAnswerCorrect

  if (isCorrect)
    return 'correct'

  const points = props.question.points
  const pointsValue = Array.isArray(points) ? points.length : (points || 0)
  const isWrong = pointsValue === 0
  const isIntermediate = pointsValue
    && props.question.maxPoints
    && pointsValue < props.question.maxPoints

  if (isWrong)
    return 'wrong'
  if (isIntermediate)
    return 'intermediate'

  return null
})
</script>

<template>
  <div :class="itemClasses">
    <div class="points-report-item__number" :class="{ 'not-support': notSupport }">
      {{ numberString }}
    </div>
    <div class="points-report-item__main">
      <div class="points-report-item__header" :class="{ 'not-support': notSupport }">
        <h3 class="points-report-item__description" v-html="question.description" />
        <div v-if="hasPoints" class="points-report-item__points">
          <span class="points-report-item__points-value">{{ question.points }}</span> /
          <span class="points-report-item__points-max">{{ question.maxPoints }}</span>
        </div>
      </div>

      <div :class="contentClasses">
        <AnswerIndicator v-if="answerStatus" :type="answerStatus" />
        <div v-if="question.skipped" class="points-report-skipped-text">
          {{ question.skipText || 'Затрудняюсь ответить' }}
        </div>
        <div v-else-if="question.unrequiredSkipped">
          <div class="points-report-skipped-unrequired-text">
            –
          </div>
        </div>
        <slot v-else />
        <LabeledField
          v-if="hasComment"
          :label="question.commentLabel || 'Комментарий'"
          class="points-report-item__comment"
        >
          {{ question.comment }}
        </LabeledField>
        <slot v-if="shouldShowRightAnswer" name="correctAnswer" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.points-report-item {
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  line-height: 1.1;
  font-size: 14px;
  gap: 13px;
}

.points-report-item:first-child {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.points-report-item:last-child {
  border-bottom: none;
}

.points-report-item__main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.points-report-item--unrequired-skipped:not(.points-report-item--has-comment) .points-report-item__header,
.points-report-item--unrequired-skipped:not(.points-report-item--has-comment) .points-report-item__number {
  opacity: 0.5;
}

.points-report-item__skipped-unrequired {
  font-size: 12px;
  line-height: 1.2;
}

.points-report-item__number {
  flex-shrink: 0;
  flex: 0 0 24px;
  font-size: 16px;
  font-weight: 700;
}

.points-report-item__content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.points-report-item__content > :deep(div) {
  display: flex;
  flex-direction: column;
}

.points-report-item__content :deep(.answer-indicator) {
  position: absolute;
  top: 0px;
  left: -6px;
  transform: translateX(-100%);
}

.points-report-item__content :deep(.answer-indicator.intermediate) {
  top: -1px;
}

.points-report-item__content :deep(.answer-indicator.correct) {
  top: -1px;
}

.points-report-item__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.points-report-item__description {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
}

.points-report-item__description :deep(br) {
  display: none;
}

.points-report-item__description :deep(p) {
  margin: 0;
  line-height: 1;
  padding: 0;
}

.points-report-item__points {
  font-size: 12px;
  line-height: 1.2;
  white-space: nowrap;
}

.points-report-item__points-value {
  font-weight: 700;
  font-size: 14px;
  line-height: 1.2;
}

.points-report-item__skipped {
  font-size: 14px;
  line-height: 1.1;
}

:global(.points-report-skipped-text) {
  font-size: 14px;
  line-height: 1.1;
}

:global(.points-report-skipped-unrequired-text) {
  color: black;
  font-size: 12px;
  line-height: 1.2;
}

.not-support {
  opacity: 50%;
}

@media screen and (max-width: 679px) {
  .points-report-item__number,
  .points-report-item__description {
    font-size: 14px;
    line-height: 1.1;
  }

  .points-report-item__description {
    text-wrap: initial;
  }

  .points-report-item__points {
    margin-left: 5px;
  }
}
</style>
