<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { AddressQuestionReport } from '../../types/formatter'
import PointsReportItem from '../PointsReportItem.vue'

interface AddressQuestionProps extends BasePointsProps {
  question: AddressQuestionReport
}

defineProps<AddressQuestionProps>()
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="address-question__answer">
      {{ question.formattedAnswer }}
    </div>
  </PointsReportItem>
</template>
