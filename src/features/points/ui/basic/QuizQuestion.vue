<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { QuizQuestionReport, QuizValue } from '../../types/formatter'
import MaskTypes from '@shared/constants/maskTypes'
import { computed } from 'vue'
import LabeledField from '../LabeledField.vue'
import PointsReportItem from '../PointsReportItem.vue'

interface QuizQuestionProps extends BasePointsProps {
  question: QuizQuestionReport
  number: number
}

const props = defineProps<QuizQuestionProps>()

function getAnswerComponent(field: QuizValue) {
  const hasMaskType = field.maskType !== null
  if (hasMaskType && field.maskType === MaskTypes.Email)
    return 'a'
  if (hasMaskType && field.maskType === MaskTypes.Site)
    return 'a'
  return 'div'
}

function getAnswerHref(field: QuizValue) {
  const hasMaskType = field.maskType !== null
  if (hasMaskType && field.maskType === MaskTypes.Email)
    return `mailto:${field.value}`
  if (hasMaskType && field.maskType === MaskTypes.Site)
    return field.value
  return null
}

function getAnswerClasses(field: QuizValue) {
  const hasMaskType = field.maskType !== null
  return {
    'quiz-question__value': true,
    'quiz-question__value--link': hasMaskType && [MaskTypes.Email, MaskTypes.Site].includes(field.maskType as MaskTypes),
    'quiz-question__value--name': field.maskType === MaskTypes.Name,
    'quiz-question__value--email': field.maskType === MaskTypes.Email,
    'quiz-question__value--site': field.maskType === MaskTypes.Site,
    'quiz-question__value--number': field.maskType === MaskTypes.Number,
  }
}

function formatValue(value: string | { name: string, surname: string, patronymic?: string }) {
  if (typeof value === 'string')
    return value
  return `${value.surname} ${value.name}${value.patronymic ? ` ${value.patronymic}` : ''}`
}

const answerValues = computed(() => props.question.values || [])

function getAnswerTarget(field: QuizValue) {
  return field.maskType === MaskTypes.Site ? '_blank' : null
}
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="quiz-question">
      <div class="quiz-question__fields">
        <LabeledField
          v-for="field in answerValues"
          :key="field.label"
          :label="field.label"
          gap="medium"
        >
          <component
            :is="getAnswerComponent(field)"
            :class="getAnswerClasses(field)"
            :href="getAnswerHref(field)"
            :target="getAnswerTarget(field)"
          >
            {{ formatValue(field.value) }}
          </component>
        </LabeledField>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.quiz-question {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.quiz-question__fields {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.quiz-question__value {
  font-size: 14px;
  line-height: 1.2;
  text-decoration: none;
}

.quiz-question__value--link {
  color: var(--fqz-brand-color-1);
  text-decoration: none;
  transition: color 0.3s ease;
}

.quiz-question__value--link:hover {
  text-decoration: none;
}

.quiz-question__value--link:active {
  color: rgba(63, 101, 241, 0.8);
}

.quiz-question :deep(.labeled-field__label) {
  line-height: 1.1;
}

.quiz-question__value--name,
.quiz-question__value--email,
.quiz-question__value--site,
.quiz-question__value--number {
  overflow-wrap: anywhere;
}
</style>
