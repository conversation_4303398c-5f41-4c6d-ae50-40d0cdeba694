<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { TextQuestionReport } from '../../types/formatter'
import { MaskTypes } from '@shared/constants/maskTypes'
import { computed } from 'vue'
import PointsReportItem from '../PointsReportItem.vue'

interface TextQuestionProps extends BasePointsProps {
  question: TextQuestionReport
  number: number
}

const props = defineProps<TextQuestionProps>()

const answerClasses = computed(() => {
  return {
    'text-question__answer': true,
    'text-question__answer--link': [
      MaskTypes.Email,
      MaskTypes.Site,
    ].includes(props.question.mask_type),
    'text-question__answer--name': props.question.mask_type === MaskTypes.Name,
    'text-question__answer--email': props.question.mask_type === MaskTypes.Email,
    'text-question__answer--phone': props.question.mask_type === MaskTypes.Phone,
    'text-question__answer--number': props.question.mask_type === MaskTypes.Number,
    'text-question__answer--site': props.question.mask_type === MaskTypes.Site,
  }
})

const answerComponent = computed(() => {
  if ([MaskTypes.Email, MaskTypes.Phone, MaskTypes.Site].includes(props.question.mask_type)) {
    return 'a'
  }
  return 'div'
})

const answerHref = computed(() => {
  if (!props.question.formattedAnswer)
    return null

  switch (props.question.mask_type) {
    case MaskTypes.Email:
      return `mailto:${props.question.formattedAnswer}`
    case MaskTypes.Site:
      return props.question.formattedAnswer
    default:
      return null
  }
})
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <component :is="answerComponent" :class="answerClasses" :href="answerHref" :target="answerHref ? '_blank' : undefined">
      {{ question.formattedAnswer }}
    </component>
  </PointsReportItem>
</template>

<style scoped>
.text-question__answer--link {
  color: var(--fqz-brand-color-1);
  text-decoration: none;
  transition: color 0.3s ease;
}

.text-question__answer--link:hover {
  text-decoration: none;
}

.text-question__answer--name,
.text-question__answer--email,
.text-question__answer--site,
.text-question__answer--number {
  overflow-wrap: anywhere;
}
</style>
