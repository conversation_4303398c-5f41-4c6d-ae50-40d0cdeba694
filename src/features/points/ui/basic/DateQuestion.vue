<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { DateQuestionReport } from '../../types/formatter'
import { computed } from 'vue'
import LabeledField from '../LabeledField.vue'
import PointsReportItem from '../PointsReportItem.vue'

interface DateQuestionProps extends BasePointsProps {
  question: DateQuestionReport
  number: number
}

const props = defineProps<DateQuestionProps>()

const hasRightAnswer = computed(() =>
  props.question.rightAnswerText,
)
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="date-question">
      <div class="date-question__answer">
        {{ question.answerText }}
      </div>
    </div>
    <template #correctAnswer>
      <LabeledField
        v-if="hasRightAnswer"
        class="date-question__correct-answer"
        text-size="small"
        label="Правильный ответ"
      >
        {{ question.rightAnswerText }}
      </LabeledField>
    </template>
  </PointsReportItem>
</template>

<style scoped>
</style>
