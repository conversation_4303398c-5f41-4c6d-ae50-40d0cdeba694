<script setup lang="ts">
import type { BasePointsProps } from '../../types'
import type { FileQuestionReport } from '../../types/formatter'
import FilePreview from '@/shared/ui/FilePreview.vue'
import { computed } from 'vue'
import PointsReportItem from '../PointsReportItem.vue'

interface FileQuestionProps extends BasePointsProps {
  question: FileQuestionReport
  number: number
}

const props = defineProps<FileQuestionProps>()

const mediaFiles = computed(() => {
  return props.question.files.filter(file =>
    file.type === 'image' || file.type === 'video',
  )
})

const audioFiles = computed(() => {
  return props.question.files.filter(file =>
    file.type === 'audio',
  )
})

const filesOfTheSameTypeOnly = computed(() => {
  let type: 'image' | 'video' | 'audio' | null = null
  return props.question.files.every((file) => {
    if (type === null) {
      type = file.type
      return true
    }
    return file.type === type
  })
})

const mediaSectionTitle = computed(() => {
  if (filesOfTheSameTypeOnly.value) {
    return null
  }
  const imagesOnly = mediaFiles.value.every(file => file.type === 'image')
  const videosOnly = mediaFiles.value.every(file => file.type === 'video')
  const imagesAndVideos = mediaFiles.value.some(file => file.type === 'image') && mediaFiles.value.some(file => file.type === 'video')

  if (imagesOnly)
    return 'Фото'
  if (videosOnly)
    return 'Видео'
  if (imagesAndVideos)
    return 'Фото/видео'
  return 'Файлы'
})

function handleAudioClick(e: MouseEvent, file: typeof props.question.files[number]) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  if (props.view === 'print') {
    window.open(file.url, '_blank')
  }
}
</script>

<template>
  <PointsReportItem :question="question" :number="number" :view="view">
    <div class="file-question">
      <!-- Photo/Video Section -->
      <div v-if="mediaFiles.length" class="file-question__section">
        <div v-if="mediaSectionTitle" class="file-question__title">
          {{ mediaSectionTitle }}
        </div>
        <div class="file-question__media-files">
          <FilePreview
            v-for="(file, index) in mediaFiles"
            :id="String(index)"
            :key="index"
            :file="undefined"
            :preview-url="file.preview_url || file.url || undefined"
            :full-url="file.url"
            :type="(file.type as string)"
            :name="file.name"
            :group="`points-report-file-question-${number}-media`"
            :is-uploading="false"
            :open-in-new-tab="view === 'print'"
            view="default"
          />
        </div>
      </div>

      <!-- Audio Section -->
      <div v-if="audioFiles.length" class="file-question__section">
        <div v-if="!filesOfTheSameTypeOnly" class="file-question__title">
          Аудио
        </div>
        <div class="file-question__audio-files">
          <div v-for="file in audioFiles" :key="file.name" class="file-question__audio-item" @click="(e) => handleAudioClick(e, file)">
            <audio controls :style="{ pointerEvents: view === 'print' ? 'none' : 'auto' }" class="file-question__audio-player" :src="file.url" />
            <div class="file-question__audio-name">
              {{ file.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </PointsReportItem>
</template>

<style scoped>
.file-question__section {
  margin-bottom: 15px;
}

.file-question__section:last-child {
  margin-bottom: 0;
}

.file-question__title {
  font-size: 12px;
  line-height: 1.1;
  opacity: 0.5;
  margin-bottom: 10px;
}

.file-question__media-files {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.file-question__audio-files {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-question__audio-item {
  max-width: 300px;
}

.file-question__audio-player {
  width: 100%;
  margin-bottom: 5px;
}

.file-question__audio-name {
  padding-left: 15px;
  font-size: 12px;
  line-height: 1.1;
}

.file-question__media-files :deep(.file-preview__close) {
  display: none;
}

.file-question__media-files :deep(.file-preview__name) {
  margin-top: 5px;
  font-size: 12px;
}
</style>
