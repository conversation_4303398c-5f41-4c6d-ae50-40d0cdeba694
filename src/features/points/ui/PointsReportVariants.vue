<script setup lang="ts">
import { computed } from 'vue'
import LabeledField from './LabeledField.vue'
import PointsReportVariantsMediaItem from './PointsReportVariantsMediaItem.vue'

interface VariantWithFiles {
  id: string | number
  variant: string
  file_url: string | null
  preview_url: string | null
  type?: 'image' | 'video' | 'audio' | null
}

interface PointsReportVariantsProps {
  label?: string
  selfVariantLabel?: string | null
  variants?: Array<VariantWithFiles>
  selfVariant?: string | null
  selfVariantFile?: {
    file_id: number
    file_url: string | null
    preview_url: string | null
    type: 'image' | 'video' | 'audio' | null
  } | null
  textValue?: string | null
  valueFontSize?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<PointsReportVariantsProps>(), {
  label: '',
  variants: () => [],
  selfVariant: null,
  textValue: null,
  valueFontSize: 'medium',
})

const hasContent = computed(() => {
  return props.variants.length > 0 || props.textValue || props.selfVariant
})

const classes = computed<any>(() => ({
  'points-report-variants': true,
  [`points-report-variants--value-font-size-${props.valueFontSize}`]: true,
}))
</script>

<template>
  <div v-if="hasContent" :class="classes">
    <LabeledField v-if="label" :label="label" gap="small" />

    <template v-if="variants.length">
      <div class="points-report-variants__list">
        <div
          v-for="(variant, index) in variants"
          :key="index"
          class="points-report-variants__item"
        >
          <div v-if="!variant.file_url" class="points-report-variants__item-text">
            {{ variant.variant }}
          </div>
          <PointsReportVariantsMediaItem
            v-else
            :id="index"
            :label="null"
            :variant="variant.variant"
            :file-url="variant.file_url"
            :preview-url="variant.preview_url"
            :type="variant.type || 'image'"
          />
        </div>
      </div>
    </template>
    <div v-else-if="textValue" class="points-report-variants__text-value">
      {{ textValue }}
    </div>
    <LabeledField
      v-if="selfVariant && !selfVariantFile"
      :label="selfVariantLabel || 'Свой вариант'"
      gap="small"
      class="points-report-variants__self-variant"
    >
      <div v-if="!selfVariantFile" class="points-report-variants__self-variant-text">
        {{ selfVariant }}
      </div>
    </LabeledField>
    <PointsReportVariantsMediaItem
      v-else-if="selfVariant && selfVariantFile"
      :id="selfVariantFile.file_id"
      :variant="selfVariant || ''"
      :file-url="selfVariantFile.file_url || ''"
      :preview-url="selfVariantFile.preview_url || ''"
      :type="selfVariantFile.type"
      :label="selfVariantLabel || null"
    />
  </div>
</template>

<style scoped>
.points-report-variants {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.points-report-variants__list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.points-report-variants__text-value {
  line-height: 1.1;
}
</style>
