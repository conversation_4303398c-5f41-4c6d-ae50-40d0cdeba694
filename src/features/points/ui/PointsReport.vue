<script lang="ts" setup>
import type { FormattedResultsData } from '../types/formatter'
import Spinner from '@shared/ui/Spinner.vue'
import { computed, toValue } from 'vue'
import PointsReportQuestion from './PointsReportQuestion.vue'

const props = defineProps<{
  results?: FormattedResultsData
  loading: boolean
  view?: 'default' | 'print'
}>()

const questions = computed(() => {
  return toValue(props.results?.questions)
})

const currentYear = computed(() => new Date().getFullYear())
const hasContact = computed(() => {
  return props.results?.contact?.name || props.results?.contact?.phone || props.results?.contact?.email
})
</script>

<template>
  <div class="points-report" :class="view === 'print' ? 'points-report--print' : ''">
    <Transition name="points-report-fade">
      <div v-if="loading" key="loader" class="points-report__loader">
        <Spinner size="44px" />
      </div>
    </Transition>
    <Transition name="points-report-fade">
      <div v-if="!loading" key="content">
        <!-- Print mode header -->
        <header v-if="view === 'print'" class="points-report__print-header">
          <h1 v-if="results?.poll" class="points-report__poll-title">
            {{ results?.poll.name }}
          </h1>
          <div v-if="hasContact" class="points-report__contact">
            <div v-if="results?.contact?.name" class="points-report__contact-name">
              {{ results.contact.name }}
            </div>
            <div v-if="results?.contact?.phone || results?.contact?.email" class="points-report__contact-info">
              <span v-if="results?.contact?.phone" class="points-report__contact-info--phone">
                {{ results.contact.phone }}
              </span>
              <span v-if="results?.contact?.phone && results?.contact?.email" class="points-report__contact-info--separator">, </span>
              <span v-if="results?.contact?.email" class="points-report__contact-info--email">
                {{ results.contact.email }}
              </span>
            </div>
          </div>
        </header>
        <header v-if="results" class="points-report__header">
          <div class="points-report__header-top">
            <h2 class="points-report__title">
              Результаты тестирования
            </h2>
            <div class="points-report__results">
              <span class="points-report__results-title">
                Набрано баллов:
              </span>
              <div class="points-report__total">
                <span class="points-report__score">
                  {{ results?.points.answer_points }}
                </span>
                <span class="points-report__results-word-separator">
                  из
                </span>
                <span class="points-report__max">
                  {{ results?.points.points_max }}
                </span>
                <span class="points-report__percentage">
                  ({{ results?.points.percent }}%)
                </span>
              </div>
            </div>
          </div>
        </header>
        <div class="points-report__questions">
          <PointsReportQuestion
            v-for="(question, index) in questions"
            :key="index"
            :question="question"
            :number="index + 1"
            :view="view ?? 'default'"
          />
        </div>
        <!-- Print mode footer -->
        <footer v-if="view === 'print'" class="points-report__print-footer">
          <a href="https://foquz.ru" class="points-report__logo" target="_blank">
            <svg width="98" height="21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M48.98 19.9a.65.65 0 0 1-.48-.2.65.65 0 0 1-.19-.48V1.78c0-.2.06-.36.19-.49a.65.65 0 0 1 .48-.19h2.92c.2 0 .**********.***********.19.48v6.7h1.98l4.15-6.87c.1-.14.22-.26.38-.34.16-.11.36-.17.61-.17h2.98c.18 0 .*********.**********.17.37 0 .1-.04.21-.11.32l-5 8.16 5.5 8.83c.***********.06.3 0 .17-.06.32-.19.45-.1.1-.24.16-.4.16h-3.09c-.26 0-.46-.05-.58-.16a4.1 4.1 0 0 1-.3-.32l-4.42-7.11h-2.14v6.92c0 .2-.06.36-.19.48a.65.65 0 0 1-.48.2h-2.92Z" fill="#000" fill-opacity=".5" /><path fill-rule="evenodd" clip-rule="evenodd" d="M35.65 20.38c5.63 0 9.8-4.02 9.8-9.94 0-6-4.17-9.79-9.8-9.79-5.6 0-9.76 3.78-9.76 9.79 0 5.92 4.16 9.94 9.76 9.94Zm0-15.89c3.06 0 5.17 2.54 5.17 5.97 0 3.54-2.14 6.08-5.17 6.08-3.02 0-5.14-2.54-5.14-6.08 0-3.43 2.09-5.97 5.14-5.97Z" fill="#000" fill-opacity=".5" /><path d="M90.22 20.34c-1.29 0-2.4-.14-3.35-.43a6.92 6.92 0 0 1-2.32-1.15 5.2 5.2 0 0 1-1.98-********** 0 0 1 .16-.4.62.62 0 0 1 .43-.16h2.88c.2 0 .***********.**********.***********.57.92 1.1 ********* 1.26.45 ********* 0 1.3-.09 1.82-.26.51-.2.91-.48 1.2-.84.3-.35.45-.78.45-1.44 0-.78-.29-1.45-.88-1.86a4.01 4.01 0 0 0-2.38-.61H88.4a.65.65 0 0 1-.48-.2.75.75 0 0 1-.19-.5V9.24c0-.2.06-.36.19-.5a.61.61 0 0 1 .48-.22h1.87a3.7 3.7 0 0 0 2.03-.5c.54-.37.8-.93.8-1.7 0-.43-.12-.8-.37-1.12-.25-.34-.59-.6-1.02-.78a3.94 3.94 0 0 0-1.52-.27c-.87 0-1.58.15-2.11.43-.54.29-.88.72-1.02 1.29a.78.78 0 0 1-.********* 0 0 1-.45.11h-3a.64.64 0 0 1-.42-.16.47.47 0 0 1-.14-.4 4.96 4.96 0 0 1 1.9-3.51 6.63 6.63 0 0 1 2.27-1.16C87.88.84 89 .7 90.27.7c1.52 0 2.8.25 3.85.75a5.7 5.7 0 0 1 2.44 1.93c.57.78.85 1.64.85 2.57 0 .46-.06.95-.19 1.45-.12.48-.33.93-.64 1.36-.28.41-.71.91-1.28 1.23a4.4 4.4 0 0 1 2.43 2.76 6 6 0 0 1 .27 1.78 5.2 5.2 0 0 1-.94 3.1 6.18 6.18 0 0 1-2.7 1.99 10.8 10.8 0 0 1-4.14.72Z" fill="#000" fill-opacity=".5" /><path fill-rule="evenodd" clip-rule="evenodd" d="M23.36 10.37c0 5.23-3.63 8.78-8.51 8.78H13.7v1.2c0 .35-.28.65-.64.65h-2.79a.64.64 0 0 1-.64-.64v-1.2H8.51C3.63 19.15 0 15.6 0 10.36c0-5.3 3.63-8.64 8.51-8.64h1.13V.64c0-.31.26-.64.64-.64h2.8c.33 0 .63.28.63.64v1.1h1.14c4.88 0 8.5 3.33 8.5 8.63Zm-19.07.02c0-2.93 1.81-5.16 4.48-5.16h.87v10.42h-.87c-2.64 0-4.48-2.23-4.48-5.26Zm14.78 0c0-2.93-1.82-5.16-4.48-5.16h-.88v10.42h.88c2.64 0 4.48-2.23 4.48-5.26Z" fill="#000" fill-opacity=".5" /><path
                d="m67.2 17.68 1.24 1.97c.**********.66.37h1.37c3.3 0 4.82-3.55 5.65-5.48.84-1.92 5.36-12.42 5.36-12.42l.08-.21a.66.66 0 0 0 .05-.24.48.48 0 0 0-.18-.38.5.5 0 0 0-.4-.19h-2.85a.89.89 0 0 0-.*********** 0 0 0-.3.4l-3.6 8.34-4.3-8.34a1.1 1.1 0 0 0-.32-.4.65.65 0 0 0-.43-.14h-3c-.16 0-.3.06-.43.19a.56.56 0 0 0-.19.4c0 .**********.24l6.68 12.51-.05.1c-.43.94-1.08 2.3-2.76 2.24-1.69-.06-.8 0-1.37 0-.46 0-.71.53-.5.9Z"
                fill="#000"
                fill-opacity=".5"
              />
            </svg>
          </a>
          <div class="points-report__copyright">
            © FOQUZ {{ currentYear }}
          </div>
        </footer>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.points-report {
  width: 100%;
  margin: 0 auto;
  flex-grow: 1;
  padding: 40px 50px;
  background-color: white;
  border-radius: 8px;
  font-family: var(--fqz-poll-font-family);
  color: black;
  position: relative;
}

.points-report--print .points-report__questions {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.points-report__header {
  padding-bottom: 20px;
}

.points-report__header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.points-report__title {
  font-size: 19px;
  font-weight: 700;
  line-height: 1.1;
  color: black;
}

.points-report__results-title:after {
  content: ' ';
  display: inline-block;
}

.points-report__results {
  display: flex;
  align-items: baseline;
  line-height: 1.1;
}

.points-report__results-title {
  font-size: 14px;
  font-weight: 400;
  opacity: 0.5;
}

.points-report__score,
.points-report__max,
.points-report__results-word-separator,
.points-report__percentage {
  font-size: 14px;
  line-height: 1.1;
}

.points-report__score,
.points-report__max {
  font-weight: 700;
}

.points-report__questions {
  display: flex;
  flex-direction: column;
}

.points-report__loader {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -52%);
}

/* Print header styles */
.points-report__print-header {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.points-report__poll-title {
  font-size: 22px;
  font-weight: 900;
  line-height: 1.1;
  color: black;
}

.points-report__contact {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.points-report__contact-name {
  font-weight: 700;
  font-size: 16px;
  line-height: 1.1;
  margin-bottom: 5px;
}

.points-report__contact-info {
  font-size: 14px;
  line-height: 1.15;
}

.points-report__contact-info--phone {
  font-weight: 700;
}

/* Print footer styles */
.points-report__print-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 50px;
  padding-top: 40px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.points-report__logo {
  flex-shrink: 0;
  width: 98px;
  height: 21px;
  display: block;
  text-decoration: none;
  color: black;
  transition: opacity 0.3s ease;
}

.points-report__logo:hover {
  opacity: 0.7;
}

.points-report__logo svg {
  width: 100%;
  height: 100%;
  display: block;
}

.points-report__copyright {
  font-size: 13px;
  line-height: 18px;
  color: rgba(0, 0, 0, 0.5);
}

.points-report--print {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
}

.points-report--print > div {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.points-report--print .points-report__print-footer {
  margin-top: auto;
  padding-bottom: 40px;
}

.points-report--print .points-report__questions {
  margin-bottom: 50px;
}

@media screen and (max-width: 679px) {
  .points-report {
    padding-left: 15px;
    padding-right: 15px;
  }

  .points-report__header-top {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .points-report__title {
    font-size: 16px;
    line-height: 1.1;
  }

  .points-report__results {
    font-size: 14px;
    line-height: 1.1;
  }

  .points-report__poll-title {
    font-size: 20px;
    margin-bottom: 15px;
  }

  .points-report__print-footer {
    margin-top: 30px;
    padding-top: 15px;
  }
}

@media print {
  .points-report__print-header {
    margin-bottom: 25px;
  }

  .points-report__print-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0;
    background: white;
    padding-bottom: 50px;
    max-width: calc(100% - 100px);
    margin-left: auto;
    margin-right: auto;
  }

  .points-report--print {
    padding-top: 40px;
    padding-bottom: 100px;
  }
}
</style>

<style>
.points-report-fade-enter-active,
.points-report-fade-leave-active {
  transition: opacity 0.3s ease;
}

.points-report-fade-enter-from,
.points-report-fade-leave-to {
  opacity: 0;
}
</style>
