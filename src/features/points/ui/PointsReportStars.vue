<script setup lang="ts">
import { computed } from 'vue'

interface PointsReportStarsProps {
  label?: string
  rating: number
  maxRating?: number
  showRatingNumber?: boolean
}

const props = withDefaults(defineProps<PointsReportStarsProps>(), {
  label: '',
  maxRating: 5,
  showRatingNumber: true,
})

const starsArray = computed(() => {
  return Array.from({ length: props.maxRating }, (_, i) => i < props.rating)
})
</script>

<template>
  <div class="points-report-stars">
    <div class="points-report-stars__container">
      <div class="points-report-stars__stars">
        <div
          v-for="(isFilled, index) in starsArray"
          :key="index"
          class="points-report-stars__star"
          :class="{ 'points-report-stars__star--filled': isFilled }"
        >
          <svg v-if="!isFilled" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M13.2351 15.8211L9.31853 13.7206C9.11901 13.613 8.88105 13.613 8.68153 13.7206L4.76495 15.8211L5.483 11.3112C5.51937 11.082 5.44594 10.8489 5.2866 10.6857L2.14904 7.46835L6.50623 6.78696C6.72826 6.75275 6.9205 6.60842 7.0213 6.40065L8.99986 2.31195L10.9788 6.40065C11.0796 6.60842 11.2715 6.75275 11.4938 6.78696L15.8507 7.46835L12.7135 10.6857C12.5538 10.8489 12.4807 11.082 12.5167 11.3112L13.2351 15.8211ZM14.2244 17.8232C14.6026 17.7751 14.8718 17.418 14.825 17.0256C14.824 17.0153 14.8226 17.0046 14.8209 16.9943L13.94 11.4444L17.7956 7.48938C18.0665 7.21141 18.0682 6.75845 17.8001 6.47763C17.6962 6.36893 17.5618 6.29766 17.416 6.27414L12.0494 5.42418L9.61677 0.394293C9.44531 0.0411244 9.03034 -0.10107 8.68984 0.0764056C8.55752 0.145543 8.45014 0.256732 8.38329 0.394293L5.95062 5.42418L0.584073 6.27414C0.207206 6.33508 -0.0505051 6.70072 0.00838048 7.09131C0.0308955 7.24241 0.0998263 7.38175 0.204435 7.48938L4.06005 11.4444L3.17919 16.9943C3.11581 17.3842 3.36936 17.753 3.74519 17.8189C3.89448 17.8449 4.04793 17.8193 4.18198 17.7459L8.99986 15.1607L13.8181 17.7459C13.9428 17.8143 14.0844 17.841 14.2244 17.8232Z" fill="#F8CD1C" />
          </svg>
          <svg v-else width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M14.2244 17.8232C14.6026 17.7751 14.8718 17.418 14.825 17.0256C14.824 17.0153 13.94 11.4444 13.94 11.4444L17.7956 7.48938C18.0665 7.21141 18.0682 6.75845 17.8001 6.47763C17.6962 6.36893 17.5618 6.29766 17.416 6.27414L12.0494 5.42418L9.61677 0.394293C9.44531 0.0411244 9.03034 -0.10107 8.68984 0.0764056C8.55752 0.145543 8.45014 0.256732 8.38329 0.394293L5.95062 5.42418L0.584073 6.27414C0.207206 6.33508 -0.0505051 6.70072 0.00838048 7.09131C0.0308955 7.24241 0.0998263 7.38175 0.204435 7.48938L4.06005 11.4444L3.17919 16.9943C3.11581 17.3842 3.36936 17.753 3.74519 17.8189C3.89448 17.8449 4.04793 17.8193 4.18198 17.7459L8.99986 15.1607L13.8181 17.7459C13.9428 17.8143 14.0844 17.841 14.2244 17.8232Z" fill="#F8CD1C" />
          </svg>
        </div>
      </div>
      <div v-if="showRatingNumber" class="points-report-stars__rating">
        {{ rating }}
      </div>
      <div v-if="label" class="points-report-stars__label">
        {{ label }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.points-report-stars {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.points-report-stars__container {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.points-report-stars__stars {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.points-report-stars__rating {
  flex-shrink: 0;
  font-weight: 700;
}

.points-report-stars__star svg {
  display: block;
}

@media screen and (max-width: 679px) {
  .points-report-stars__container {
    gap: 5px 10px;
  }
}
</style>
