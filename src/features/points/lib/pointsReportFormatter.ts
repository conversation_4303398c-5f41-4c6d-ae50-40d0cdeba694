import type MaskTypes from '@/shared/constants/maskTypes'
import type {
  AssessmentVariant,
  GetResultsAddressQuestion,
  GetResultsCardSortingQuestion,
  GetResultsClassifierQuestion,
  GetResultsDateQuestion,
  GetResultsDiffQuestion,
  GetResultsDistributionScaleQuestion,
  GetResultsFileQuestion,
  GetResultsFirstClickTestQuestion,
  GetResultsGalleryQuestion,
  GetResultsMatrix3dQuestion,
  GetResultsMatrixQuestion,
  GetResultsMediaVariantsQuestion,
  GetResultsNpsQuestion,
  GetResultsPriorityQuestion,
  GetResultsQuestions,
  GetResultsQuizQuestion,
  GetResultsRatingScaleQuestion,
  GetResultsScaleQuestion,
  GetResultsSmileQuestion,
  GetResultsStarsQuestion,
  GetResultsStarVariantsQuestion,
  GetResultsTextQuestion,
  GetResultsVariantsQuestion,
  NameAnswer,
  StarVariantsExtra,
} from '../schemas/getResults'
import type {
  AddressQuestionReport,
  BaseQuestionReport,
  ClassifierQuestionReport,
  ClassifierVariant,
  DateQuestionReport,
  DiffQuestionReport,
  DistributionScaleQuestionReport,
  Extra,
  FileQuestionReport,
  FirstClickTestReport,
  GalleryQuestionReport,
  Matrix3dColumnData,
  Matrix3dQuestionReport,
  Matrix3dRowData,
  MatrixQuestionReport,
  MediaVariantsItem,
  MediaVariantsQuestionReport,
  NpsQuestionReport,
  NpsVariantReport,
  PriorityQuestionReport,
  QuizQuestionReport,
  QuizValue,
  RatingScaleQuestionReport,
  ScaleQuestionReport,
  ScaleVariantData,
  SmileQuestionReport,
  StarsQuestionReport,
  StarVariantData,
  StarVariantsQuestionReport,
  TextQuestionReport,
  VariantQuestionItem,
  VariantsQuestionReport,
} from '../types/formatter'
import { EXTRA_QUESTION_TYPE } from '@/shared/constants'
import { ALLOWED_FILE_TYPES } from '@/shared/constants/files'
import { NPSGradient } from '@/shared/helpers/color'
import { getFileTypeFromFilename } from '@/shared/helpers/files'
import { withRootUrl } from '@shared/helpers/general'
import { computed, toValue } from 'vue'

function formatBaseQuestion(
  question: GetResultsQuestions,
  _translations: any,
): BaseQuestionReport {
  const comment = question.answer?.comment || question.answer?.answer?.comment || null

  const pointsValue = Array.isArray(question.answer?.points)
    ? question.answer.points.length > 0 ? 1 : 0 // Or some other logic to convert array to number
    : question.answer?.points

  const reachedMaxPoints = question.answer?.max_points
    && pointsValue
    && pointsValue >= question.answer.max_points

  return {
    id: question.id,
    type: question.type,
    description: question.description,
    commentLabel: question.commentLabel || null,
    commentEnabled: question.commentEnabled,
    comment,
    points: question.answer?.points || 0,
    maxPoints: question.answer?.max_points || question.maxPoints || null,
    assessments: [],
    skipped: question.answer?.skipped === 1,
    skipText: question.skipText || 'Затрудняюсь ответить',
    unrequiredSkipped: !question.answer,
    isAnswerCorrect: question.answer?.rightAnswer || reachedMaxPoints || false,
    rightAnswer: question.rightAnswer,
    answer: question.answer,
    answerStatus: null,
  }
}

function formatRatingScaleQuestion(
  question: GetResultsRatingScaleQuestion,
  _translations: any,
): RatingScaleQuestionReport {
  const base = formatBaseQuestion(question, _translations)

  let selectedAssessments = null
  const selectedIds = question.answer?.selectedIds?.filter?.(Boolean)

  if (selectedIds && question.variants) {
    selectedAssessments = selectedIds.map((id) => {
      const variant = question.variants?.find(v => String(v.id) === String(id))
      if (!variant) {
        return null
      }
      return variant
    }).filter(Boolean) as AssessmentVariant[]
  }

  const rating = question.answer?.rating || -1
  const selectedLabel = question.starRatingOptions?.labelsArray?.[rating - 1] || ''

  return {
    ...base,
    rating: question.answer?.rating || 0,
    clarifyingQuestion: question.clarifyingQuestion || null,
    starRatingOptions: question.starRatingOptions,
    assessments: selectedAssessments || null,
    selfVariantLabel: question.self_variant_text || null,
    selfVariantText: question.answer?.selfVariant || null,
    label: selectedLabel,
    unrequiredSkipped: !question.answer?.rating,
    skipText: question.skipText || 'Не готов(а) оценить',
  }
}

function formatStarsQuestion(
  question: GetResultsStarsQuestion,
  translations: any,
): StarsQuestionReport {
  const base = formatBaseQuestion(question, translations)

  let selectedAssessments = null
  const selectedIds = question.answer?.selectedIds?.filter?.(Boolean)

  if (selectedIds && question.variants) {
    selectedAssessments = selectedIds.map((id) => {
      const variant = question.variants?.find(v => String(v.id) === String(id))
      if (!variant) {
        return null
      }
      return variant
    }).filter(Boolean) as AssessmentVariant[]
  }

  const rating = question.answer?.rating || -1
  const selectedLabel = question.starRatingOptions?.labelsArray?.[rating - 1] || ''

  const formattedQuestion: StarsQuestionReport = {
    ...base,
    rating: question.answer?.rating || 0,
    clarifyingQuestion: translations?.answerText || question.clarifyingQuestion || null,
    starRatingOptions: question.starRatingOptions,
    assessments: selectedAssessments,
    selfVariantLabel: translations?.self_variant_text || question.self_variant_text || null,
    selfVariantText: question.answer?.selfVariant || null,
    label: selectedLabel,
    unrequiredSkipped: !question.answer?.rating,
    skipText: question.skipText || 'Не готов(а) оценить',
  }

  return formattedQuestion
}

function formatStarVariantsQuestion(
  question: GetResultsStarVariantsQuestion,
  translations: any,
): StarVariantsQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const mainVariants = question.variants.filter(v => v.extra_question === 0)
  const extraVariantsById = question.variants
    .filter(v => v.extra_question === 1)
    .reduce((acc, variant) => {
      acc[String(variant.id)] = variant
      return acc
    }, {} as Record<string, typeof question.variants[number]>)

  const formattedVariants = mainVariants.map((variant) => {
    const id = variant.question_detail_id || variant.dictionary_element_id || variant.id
    const data = {
      id: String(id),
      variant: variant.variant,
      rating: 0,
      skipped: false,
      unrequiredSkipped: false,
      extra: undefined as {
        variants: AssessmentVariant[]
        selfVariant: string | null
        textValue: string | null
      } | undefined,
    }

    const outerAnswer = question.answer?.answer
    if (!outerAnswer) {
      return data
    }

    const answer = outerAnswer.answer
    const rating = answer?.[String(id)] as string | undefined

    if (rating === undefined) {
      return null
    }

    if (rating === 'null') {
      data.skipped = true
    }
    else if (rating === '-1') {
      data.unrequiredSkipped = true
    }
    else {
      data.rating = Number(rating)
    }

    const extra = answer?.extra as unknown as StarVariantsExtra | undefined
    const variantExtra = extra?.[String(variant.id)]

    if (variantExtra) {
      const variantsMapper = (id: string) => ({
        id,
        variant: extraVariantsById[id]?.variant,
        description: null,
        file_url: null,
        preview_url: null,
        file_id: null,
      })

      if (Array.isArray(variantExtra)) {
        const selfVariant = variantExtra.find(id => id === variant.id)
        data.extra = {
          variants: variantExtra.map(id => variantsMapper(id)),
          selfVariant: selfVariant?.[1] || null,
          textValue: outerAnswer.comment ?? null,
        }
      }
      else if (typeof variantExtra === 'object' && 'answer' in variantExtra) {
        data.extra = {
          variants: [],
          selfVariant: null,
          textValue: (variantExtra as { answer?: string }).answer || null,
        }
      }
      else {
        const variantExtraEntries = Object.entries(variantExtra as Record<string, string>)
        const selfVariant = variantExtraEntries.find(([key]) => key === 'self_variant')
        const extraVariants = variantExtraEntries.map(([_, id]) => variantsMapper(id))

        data.extra = {
          variants: extraVariants,
          selfVariant: selfVariant?.[1] || null,
          textValue: outerAnswer.comment ?? null,
        }
      }
    }

    return data
  }).filter(Boolean) as StarVariantData[]

  const unrequiredSkipped = !question.answer?.answer || formattedVariants.every(v => v.unrequiredSkipped)

  /**
   * @NOTE: Обработка своего варианта-реципиента
   * Если донором является вопрос "Варианты ответов", то в ответе свой вариант приходит в виде "-1"
   * В таком случае, мы получаем его значение через поле "selfVariant" и обрабатываем как обычный вариан
   */
  const selfVariant = question.answer?.selfVariant
  const selfVariantAnswer = question.answer?.answer?.answer?.['-1']

  if (selfVariant && selfVariantAnswer) {
    const ratingNumber = Number(selfVariantAnswer)
    formattedVariants.push({
      id: '-1',
      variant: selfVariant,
      rating: ratingNumber,
      skipped: false,
      unrequiredSkipped: false,
    })
  }

  return {
    ...base,
    type: 7,
    clarifyingQuestion: question.clarifyingQuestion || null,
    starRatingOptions: question.starRatingOptions,
    formattedVariants,
    selfVariantLabel: question.self_variant_text || null,
    unrequiredSkipped,
    skipText: question.skipText || 'Не готов(а) оценить',
  }
}

function formatTextQuestion(
  question: GetResultsTextQuestion,
  translations: any,
): TextQuestionReport {
  const base = formatBaseQuestion(question, translations)

  let formattedAnswer: string | null = null
  let nameAnswer = null
  let unrequiredSkipped = true // Default to true, set to false if we find valid content

  if (question.answer?.answer) {
    if (typeof question.answer.answer === 'object') {
      const answer = question.answer as NameAnswer
      nameAnswer = {
        name: answer.answer.name,
        surname: answer.answer.surname,
        patronymic: answer.answer.patronymic,
      }
      formattedAnswer = [answer.answer.surname, answer.answer.name, answer.answer.patronymic]
        .filter(Boolean)
        .join(' ')
      // Check if any name field has content
      unrequiredSkipped = !answer.answer.name?.trim()
      && !answer.answer.surname?.trim()
      && !answer.answer.patronymic?.trim()
    }
    else {
      formattedAnswer = question.answer.answer
      // Check if text answer has content
      unrequiredSkipped = !formattedAnswer?.toString().trim()
    }
  }

  return {
    ...base,
    mask_type: question.mask_type,
    formattedAnswer: formattedAnswer || null,
    nameAnswer,
    unrequiredSkipped,
  }
}

function formatDateQuestion(
  question: GetResultsDateQuestion,
  translations: any,
): DateQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const answerText = question.answer?.answer || null
  const rightAnswerText = question.answer?.correct_answer?.text || null

  return {
    ...base,
    answerText,
    rightAnswerText,
  }
}

function formatNpsQuestion(
  question: GetResultsNpsQuestion,
  translations: any,
): NpsQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const npsConfig = {
    design: 1,
    startColor: '#F96261',
    endColor: '#00C968',
    startLabel: '',
    endLabel: '',
  }

  const colors = NPSGradient(npsConfig.startColor, npsConfig.endColor)
  // const selfVariantRecipientText = question.answer?.selfVariant || null

  const mainVariants = question.variants.filter(v => v.extra_question === 0)
  const extraVariantsById = question.variants
    .filter(v => v.extra_question === 1)
    .reduce((acc, variant) => {
      acc[String(variant.id)] = variant
      return acc
    }, {} as Record<string, typeof question.variants[number]>)

  const getSelfVariantFile = (variant?: typeof question.variants[number]) => {
    const singleOrCommonPerVariantType = question.extra_question_type === EXTRA_QUESTION_TYPE.SINGLE
      || question.extra_question_type === EXTRA_QUESTION_TYPE.COMMON_PER_VARIANT

    if (singleOrCommonPerVariantType && question.self_variant_file) {
      return {
        file_id: question.self_variant_file?.file_id,
        file_url: question.self_variant_file?.file_url ? withRootUrl(question.self_variant_file?.file_url) : null,
        preview_url: question.self_variant_file?.preview_url ? withRootUrl(question.self_variant_file?.preview_url) : null,
        type: getFileTypeFromFilename(question.self_variant_file?.file_url),
      }
    }
    else if (variant?.selfVariantFile) {
      return {
        file_id: variant.selfVariantFile.file_id,
        file_url: variant.selfVariantFile.file_url ? withRootUrl(variant.selfVariantFile.file_url) : null,
        preview_url: variant.selfVariantFile.preview_url ? withRootUrl(variant.selfVariantFile.preview_url) : null,
        type: getFileTypeFromFilename(variant.selfVariantFile.file_url),
      }
    }
    return null
  }

  const formattedVariants = mainVariants.map((variant) => {
    const id = variant.question_detail_id || variant.dictionary_element_id || variant.id

    const answer = question.answer?.answer || {}
    const rating = answer[String(id)]
    const ratingNumber = rating ? Number(rating) : 0

    const data = {
      id,
      variant: variant.variant,
      rating: ratingNumber,
      skipped: rating === 'null',
      unrequiredSkipped: !rating || rating === '-1',
      color: colors[ratingNumber],
      extra: null,
    } as NpsVariantReport

    const detailItem = question.answer?.detail_item || []
    if (!detailItem)
      return data

    const variantExtra = Array.isArray(detailItem)
      ? null
      : (detailItem as Record<string, Record<string, string> | string[]>)[String(id)]

    if (variantExtra) {
      if (Array.isArray(variantExtra)) {
        data.extra = {
          variants: variantExtra.map(id => ({
            id,
            variant: extraVariantsById[id]?.variant || '',
            file_url: extraVariantsById[id]?.file_url ? withRootUrl(extraVariantsById[id]?.file_url) : null,
            preview_url: extraVariantsById[id]?.preview_url ? withRootUrl(extraVariantsById[id]?.preview_url) : null,
            file_id: extraVariantsById[id]?.file_id || null,
            type: getFileTypeFromFilename(extraVariantsById[id]?.file_url),
          })),
          selfVariant: null,
          textValue: variant.text_answer || null,
          selfVariantLabel: variant.self_variant_text || question.self_variant_text || null,
          selfVariantFile: variantExtra.includes('is_self_answer') ? getSelfVariantFile(variant) : null,
        }
      }
      else if ('answer' in variantExtra) {
        data.extra = {
          variants: [],
          selfVariant: null,
          textValue: variantExtra.answer || variant.text_answer || null,
          selfVariantLabel: variant.self_variant_text || question.self_variant_text || null,
          selfVariantFile: null,
        }
      }
      else if (typeof variantExtra === 'object') {
        const selfVariant = variantExtra.self_variant
        const variants = Object.entries(variantExtra)
          .filter(([key]) => key !== 'self_variant')
          .map(([_, id]) => {
            const extraVariant = extraVariantsById[String(id)]

            return {
              id: String(id),
              variant: extraVariant?.variant || '',
              file_url: extraVariant?.file_url ? withRootUrl(extraVariant?.file_url) : null,
              preview_url: extraVariant?.preview_url ? withRootUrl(extraVariant?.preview_url) : null,
              file_id: extraVariant?.file_id,
              type: extraVariant?.file_url ? getFileTypeFromFilename(extraVariant?.file_url) : null,
            }
          })

        data.extra = {
          variants,
          selfVariant,
          textValue: variantExtra.answer || variant.text_answer || null,
          selfVariantLabel: variant.self_variant_text || question.self_variant_text || null,
          selfVariantFile: selfVariant ? getSelfVariantFile(variant) : null,
        }
      }
    }

    return data
  })

  /**
   * @NOTE: Обработка своего варианта-реципиента
   * Если донором является вопрос "Варианты ответов", то в ответе свой вариант приходит в виде "-1"
   * В таком случае, мы получаем его значение через поле "selfVariant" и обрабатываем как обычный вариан
   */
  const selfVariant = question.answer?.selfVariant
  const selfVariantAnswer = question.answer?.answer?.['-1']
  if (selfVariant && selfVariantAnswer) {
    const ratingNumber = Number(selfVariantAnswer)
    formattedVariants.push({
      id: '-1',
      variant: selfVariant,
      rating: ratingNumber,
      skipped: false,
      unrequiredSkipped: false,
      color: colors[ratingNumber],

      // @NOTE: Добавляем пустой extra, но свой-вариант реципиент тоже может иметь уточняющие вопросы
      // поправить в будущем
      extra: null,
    })
  }

  let unrequiredSkipped = false

  if (question.set_variants === 1) {
    unrequiredSkipped = formattedVariants.every(v => v.unrequiredSkipped)
  }
  else {
    const rating = Number(question.answer?.answer?.rating || -1)
    unrequiredSkipped = rating === -1 || Number.isNaN(rating)
  }

  let questionExtra: Extra | null = null
  if (question.extra_question_type === EXTRA_QUESTION_TYPE.SINGLE) {
    const detailItem = question.answer?.detail_item || []
    if (Array.isArray(detailItem)) {
      questionExtra = {
        variants: detailItem.map(id => ({
          id,
          variant: extraVariantsById[id]?.variant || '',
          file_url: extraVariantsById[id]?.file_url ? withRootUrl(extraVariantsById[id]?.file_url) : null,
          preview_url: extraVariantsById[id]?.preview_url ? withRootUrl(extraVariantsById[id]?.preview_url) : null,
          file_id: extraVariantsById[id]?.file_id || null,
          type: extraVariantsById[id]?.file_url ? getFileTypeFromFilename(extraVariantsById[id]?.file_url) : null,
        })),
        selfVariant: question.answer?.self_variant || null,
        textValue: question.answer?.text_answer || null,
        selfVariantLabel: question.self_variant_text || null,
        selfVariantFile: getSelfVariantFile(),
      }
    }
    else if (typeof detailItem === 'object') {
      questionExtra = {
        variants: Object.entries(detailItem)
          .filter(([key]) => key !== 'self_variant')
          .map(([_, id]) => ({
            id: String(id),
            variant: extraVariantsById[String(id)]?.variant || '',
            file_url: extraVariantsById[String(id)]?.file_url ? withRootUrl(extraVariantsById[String(id)]?.file_url) : null,
            preview_url: extraVariantsById[String(id)]?.preview_url ? withRootUrl(extraVariantsById[String(id)]?.preview_url) : null,
            file_id: extraVariantsById[String(id)]?.file_id || null,
            type: extraVariantsById[String(id)]?.file_url ? getFileTypeFromFilename(extraVariantsById[String(id)]?.file_url) : null,
          })),
        selfVariant: (detailItem as any).self_variant || null,
        textValue: question.answer?.text_answer || null,
        selfVariantLabel: question.self_variant_text || null,
        selfVariantFile: getSelfVariantFile(),
      }
    }
  }

  const formattedData = {
    ...base,
    rating: 0,
    clarifyingQuestion: question.clarifyingQuestion || null,
    label: null,
    npsStartLabel: npsConfig.startLabel,
    npsEndLabel: npsConfig.endLabel,
    fromOne: question.fromOne === 1,
    npsDesign: npsConfig.design,
    color: colors[0],
    formattedVariants,
    isVariants: question.set_variants === 1,
    unrequiredSkipped,
    skipText: question.skipText || 'Не готов(а) оценить',
    extraQuestionType: question.extra_question_type,
    extra: questionExtra,
  }

  return formattedData
}

function formatAddressQuestion(
  question: GetResultsAddressQuestion,
  translations: any,
): AddressQuestionReport {
  const base = formatBaseQuestion(question, translations)

  return {
    ...base,
    formattedAnswer: question.answer?.answer || null,
  }
}

function formatScaleQuestion(
  question: GetResultsScaleQuestion,
  translations: any,
): ScaleQuestionReport {
  const base = formatBaseQuestion(question, translations)
  const answer = question.answer?.answer

  const formattedVariants = question.variants?.map((variant) => {
    const id = variant.question_detail_id || variant.dictionary_element_id || variant.id
    const rating = answer?.[String(id)]
    let skipped = false

    if (!rating) {
      return null
    }
    if (rating === 'null') {
      skipped = true
    }
    const finalRating = skipped ? question.scaleRatingSetting.start : Number(rating)

    return {
      id: String(variant.id),
      text: variant.variant,
      rating: finalRating,
      skipped,
      unrequiredSkipped: false,
      skipText: question.skipText || 'Не готов(а) оценить',
    }
  }).filter(Boolean) as ScaleVariantData[]

  const selfVariant = question.answer?.selfVariant
  if (selfVariant && answer?.['-1']) {
    formattedVariants.push({
      id: '-1',
      text: question.answer?.selfVariant || '',
      rating: 0,
      skipped: false,
      unrequiredSkipped: false,
      skipText: question.skipText || 'Не готов(а) оценить',
    })
  }

  const rating = Number(question.answer?.answer?.rating) || question.scaleRatingSetting.start

  return {
    ...base,
    rating,
    minRating: question.scaleRatingSetting.start,
    maxRating: question.scaleRatingSetting.end,
    step: question.scaleRatingSetting.step,
    isVariants: question.set_variants === 1,
    formattedVariants,
    skipText: question.skipText || 'Не готов(а) оценить',
  }
}

function formatDistributionScaleQuestion(
  question: GetResultsDistributionScaleQuestion,
  translations: any,
): DistributionScaleQuestionReport {
  const base = formatBaseQuestion(question, translations)
  const answer = question.answer?.answer

  const formattedVariants = question.variants?.map((variant) => {
    const id = variant.question_detail_id || variant.dictionary_element_id || variant.id
    const rating = answer?.[String(id)]
    let skipped = false

    if (!rating) {
      return null
    }
    if (rating === 'null') {
      skipped = true
    }
    const finalRating = skipped ? 0 : Number(rating)

    return {
      id: String(variant.id),
      text: variant.variant,
      rating: finalRating,
      skipped,
      unrequiredSkipped: false,
      skipText: question.skipText || 'Не готов(а) оценить',
    }
  }).filter(Boolean) as ScaleVariantData[]

  const selfVariant = question.answer?.selfVariant
  if (selfVariant && answer?.['-1']) {
    formattedVariants.push({
      id: '-1',
      text: question.answer?.selfVariant || '',
      rating: 0,
      skipped: false,
      unrequiredSkipped: false,
      skipText: question.skipText || 'Не готов(а) оценить',
    })
  }

  const rating = Number(question.answer?.answer?.rating) || question.scaleRatingSetting.start

  return {
    ...base,
    rating,
    minRating: question.scaleRatingSetting.start,
    maxRating: question.scaleRatingSetting.end,
    step: question.scaleRatingSetting.step,
    isVariants: question.set_variants === 1,
    formattedVariants,
    skipText: question.skipText || 'Не готов(а) оценить',
  }
}

function formatMediaVariantsQuestion(
  question: GetResultsMediaVariantsQuestion,
  translations: any,
): MediaVariantsQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const detectType = (url: string) => {
    if (!url) {
      return 'image'
    }

    const extension = url.split('.').pop() || ''

    return ALLOWED_FILE_TYPES.video.includes(extension) ? 'video' : 'image'
  }

  const selectedMedia = question.answer?.answer?.answer || []

  const correctAnswerVariants = question.answer?.correct_answer?.map((answer) => {
    const variant = question.chooseMedia.find(v => v.id === answer.id)
    return variant
      ? {
          ...variant,
          type: detectType(variant.url),
          url: withRootUrl(variant.url),
          poster: withRootUrl(variant.poster),
        }
      : null
  }).filter(Boolean) as Array<MediaVariantsItem> || []

  return {
    ...base,
    chooseMedia: question.chooseMedia.map(variant => ({
      ...variant,
      type: detectType(variant.url),
      url: withRootUrl(variant.url),
      poster: withRootUrl(variant.poster),
    })),
    selectedMedia,
    correctAnswerVariants,
    comment: question.answer?.answer?.comment || null,
    unrequiredSkipped: !selectedMedia.length,
  }
}

function formatSmileQuestion(
  question: GetResultsSmileQuestion,
  _translations: any,
): SmileQuestionReport {
  const base = formatBaseQuestion(question, _translations)

  const smileAnswer = question.answer?.answer?.smile

  const data: SmileQuestionReport = {
    ...base,
    smileUrl: smileAnswer ? withRootUrl(smileAnswer.smile_url) : null,
    smileLabel: smileAnswer?.label || null,
    extra: null,
    comment: question.answer?.comment || '',
    clarifyingQuestion: question.clarifyingQuestion || null,
    unrequiredSkipped: !smileAnswer,
    skipText: question.skipText || 'Не готов(а) оценить',
  }

  const selfVariant = question.answer?.selfVariant

  if (question.variants || selfVariant) {
    const detailItem = question.answer?.selectedIds || []
    data.extra = {
      variants: [],
      selfVariant: question.answer?.selfVariant || null,
      textValue: question.answer?.text_answer || null,
      selfVariantLabel: question.self_variant_text || null,
      selfVariantFile: question.self_variant_file?.file_id
        ? {
            file_id: question.self_variant_file?.file_id,
            file_url: question.self_variant_file?.file_url ? withRootUrl(question.self_variant_file?.file_url) : null,
            preview_url: question.self_variant_file?.preview_url ? withRootUrl(question.self_variant_file?.preview_url) : null,
            type: getFileTypeFromFilename(question.self_variant_file?.file_url),
          }
        : null,
    }

    if (question.variants && Array.isArray(detailItem)) {
      const extraVariantsById = question.variants
        .filter(v => v.extra_question === 1)
        .reduce((acc, variant) => {
          acc[String(variant.id)] = variant
          return acc
        }, {} as Record<string, typeof question.variants[number]>)

      data.extra.variants = detailItem.map(id => ({
        id,
        variant: extraVariantsById[id]?.variant || '',
        file_url: extraVariantsById[id]?.file_url ? withRootUrl(extraVariantsById[id]?.file_url) : null,
        preview_url: extraVariantsById[id]?.preview_url ? withRootUrl(extraVariantsById[id]?.preview_url) : null,
        file_id: extraVariantsById[id]?.file_id || null,
        type: extraVariantsById[id]?.file_url ? getFileTypeFromFilename(extraVariantsById[id]?.file_url) : null,
      }))
    }
  }
  return data
}

function formatPriorityQuestion(
  question: GetResultsPriorityQuestion,
  translations: any,
): PriorityQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const variants = question.variants || []
  const selectedVariants = question.answer?.answer?.variants || []

  const formattedVariants = selectedVariants.map((id) => {
    if (id === '-1') {
      const selfVariant = variants.find(v => v.question_detail_id === null && v.dictionary_element_id === null)

      return {
        id: String(selfVariant?.id || '-1'),
        text: question.answer?.selfVariant || '',
      }
    }

    const variant = variants.find((v) => {
      const variantId = v.question_detail_id || v.dictionary_element_id || v.id
      return String(variantId) === String(id)
    })
    return {
      id: String(variant?.id || id),
      text: variant?.variant || '',
    }
  })

  const correctFormattedAnswers = question.rightAnswer?.decodedAnswer || []

  return {
    ...base,
    variants: formattedVariants,
    unrequiredSkipped: !formattedVariants.length,
    correctFormattedAnswers,
  }
}

function formatFileQuestion(
  question: GetResultsFileQuestion,
  translations: any,
): FileQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const files = question.answer?.answer?.files || []

  const formattedData = {
    ...base,
    files: files.map(file => ({
      ...file,
      url: withRootUrl(file.url),
      preview_url: withRootUrl(file.preview_url),
      type: getFileTypeFromFilename(file.url || null),
    })),
    unrequiredSkipped: files.length === 0,
  }

  return formattedData
}

function formatGalleryQuestion(
  question: GetResultsGalleryQuestion,
  translations: any,
): GalleryQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const detectType = (url: string) => {
    if (!url) {
      return 'image' as const
    }

    const extension = url.split('.').pop() || ''

    return ALLOWED_FILE_TYPES.video.includes(extension) ? 'video' as const : 'image' as const
  }

  const formattedMedia = question.chooseMedia.map((media) => {
    const answer = question.answer?.answer?.answer as Record<string, string>
    const rating = answer?.[media.id.toString()] || '0'

    return {
      id: media.id,
      url: withRootUrl(media.url),
      src: withRootUrl(media.src),
      poster: withRootUrl(media.poster),
      description: media.description,
      type: detectType(media.url),
      rating: Number(rating),
      skipped: false,
      unrequiredSkipped: !rating || rating === '0',
    }
  })

  return {
    ...base,
    media: formattedMedia,
    comment: question.answer?.answer?.comment || null,
    unrequiredSkipped: !question.answer || formattedMedia.every(m => m.unrequiredSkipped),
    skipText: question.skipText || 'Не готов(а) оценить',
  }
}

function formatQuizQuestion(
  question: GetResultsQuizQuestion,
  translations: any,
): QuizQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const values = question.answer?.answer?.values || []
  const formFields = question.formFields || []
  const formFieldsById = formFields.reduce((acc, field) => {
    acc[field.id] = field
    return acc
  }, {} as Record<string | number, typeof formFields[number]>)

  return {
    ...base,
    values: values.map((v) => {
      const maskType = formFieldsById[String(v.id)]?.maskType || null

      return {
        ...v,
        maskType: maskType as MaskTypes,
      }
    }) as QuizValue[],
    unrequiredSkipped: !question.answer?.answer?.values?.length,
  }
}

function formatDiffQuestion(
  question: GetResultsDiffQuestion,
  translations: any,
): DiffQuestionReport {
  const base = formatBaseQuestion(question, translations)
  const answer = question.answer?.answer?.answer || {}

  const formattedRows = question.differentialRows.map(row => ({
    id: row.id,
    rating: Number(answer[row.id.toString()] || 0),
    startLabel: row.start_label,
    endLabel: row.end_label,
    position: row.position,
  }))

  const unrequiredSkipped = formattedRows.every(row => !row.rating || row.rating === -1)

  return {
    ...base,
    differentialRows: formattedRows,
    semDifSetting: {
      form: question.semDifSetting.form,
      startPointColor: question.semDifSetting.start_point_color,
      endPointColor: question.semDifSetting.end_point_color,
    },
    unrequiredSkipped,
    skipText: question.skipText || 'Не готов(а) оценить',
  }
}

function formatMatrix3dQuestion(
  question: GetResultsMatrix3dQuestion,
  translations: any,
): Matrix3dQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const answer = question.answer?.answer?.answer || {}

  const rows = question.matrixElements.rows.map((row) => {
    const id = row.id

    const rowAnswers = answer[String(id)] || {}

    if (Object.keys(rowAnswers).length === 0) {
      return null
    }

    const allAnswers = Object.values(rowAnswers).flat()

    const isSkipped = allAnswers.length > 0 && allAnswers.every(v => v === '-1')
    const isUnrequiredSkipped = allAnswers.length === 0 || allAnswers.every(v => v === '')

    type RowAnswers = Record<string, Array<{ id: number, name: string }>>

    return {
      id: row.id,
      name: row.name,
      skipped: isSkipped,
      unrequiredSkipped: isUnrequiredSkipped,
      answers: Object.entries(rowAnswers).reduce<RowAnswers>((acc, [columnId, selectedVariantIds]) => {
        const column = question.matrixElements.columns.find(col => col.id.toString() === columnId)
        if (!column)
          return acc

        const selectedVariants = selectedVariantIds
          .filter(id => id !== '')
          .map((variantId) => {
            if (variantId === '-1')
              return { id: '-1', name: question.skipText }

            const variant = column.variants.find(v => v.id.toString() === variantId)
            return variant ? { id: variant.id, name: variant.name } : undefined
          })
          .filter((v): v is { id: number, name: string } => v !== undefined)

        acc[columnId] = selectedVariants
        return acc
      }, {}),
    }
  }).filter(Boolean) as Matrix3dRowData[]

  const columns = question.matrixElements.columns.map((column) => {
    const id = column.id
    const columnAnswers = Object.values(answer).map(rowAnswer => rowAnswer[String(id)] || []).flat()

    if (Object.keys(columnAnswers).length === 0) {
      return null
    }

    return {
      id: column.id,
      name: column.name,
      variants: column.variants.map((variant, index) => ({
        id: variant.id,
        name: variant.name,
        skipped: columnAnswers[index] === '-1',
        unrequiredSkipped: !columnAnswers[index],
      })),
    }
  }).filter(Boolean) as Matrix3dColumnData[]

  return {
    ...base,
    rows,
    columns,
    unrequiredSkipped: rows.every(row => row.unrequiredSkipped),
  }
}

function formatClassifierQuestion(
  question: GetResultsClassifierQuestion,
  translations: any,
): ClassifierQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const selectedVariants = question.answer?.selectedIds?.map((id) => {
    const variant = question.variants.find(v => v.id === id)
    if (!variant)
      return null

    return {
      id: variant.id,
      text: question.dictionary_list_type === 'list' ? variant.value : variant.path || variant.value,
      description: variant.description,
      path: variant.path,
    }
  }).filter((v): v is ClassifierVariant => v !== null) || []

  let comment = null

  if (question.commentEnabled) {
    comment = question.answer?.selfVariant || null
  }

  return {
    ...base,
    selectedVariants,
    isTreeType: question.dictionary_list_type === 'tree',
    unrequiredSkipped: !question.answer || (!question.answer.selectedIds?.length),
    comment,
  }
}

function formatMatrixQuestion(
  question: GetResultsMatrixQuestion,
  translations: any,
): MatrixQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const answer = question.answer?.answer?.answer || {}
  const extra = question.answer?.answer?.extra || {}
  const variants = question.variants || []
  const selfVariant = question.answer?.selfVariant

  const rows = Object.entries(answer).map(([rowTitle, selectedCols]) => {
    let isSelfVariant = false
    if (rowTitle === '' && selfVariant) {
      rowTitle = selfVariant
      isSelfVariant = true
    }

    const isUnrequiredSkipped = Array.isArray(selectedCols) && selectedCols.includes('-1')
    const isSkipped = !selectedCols || selectedCols === 'null' || (Array.isArray(selectedCols) && selectedCols.length === 0)

    const titleField = isSelfVariant ? 'Свой вариант' : rowTitle
    const rowExtra = extra[titleField]

    let extraData = null
    if (rowExtra) {
      if ('answer' in rowExtra) {
        extraData = { text: rowExtra.answer }
      }
      else if (Array.isArray(rowExtra)) {
        // Filter out nulls before assigning to variants
        const validVariants = rowExtra
          .map((id) => {
            const variant = variants.find(v => String(v.id) === id)
            return variant ? { id: String(variant.id), text: variant.variant || variant.question || '' } : null
          })
          .filter((v): v is { id: string, text: string } => v !== null)

        extraData = { variants: validVariants }
      }
      else if (typeof rowExtra === 'object' && rowExtra !== null) {
        const selectedVariants = Object.values(rowExtra)
          .filter(v => v !== rowExtra.self_variant)
          .map((id) => {
            const variant = variants.find(v => String(v.id) === id)
            return variant ? { id: String(variant.id), text: variant.variant || variant.question || '' } : null
          })
          .filter((v): v is { id: string, text: string } => v !== null)

        extraData = {
          variants: selectedVariants,
          selfVariant: rowExtra.self_variant,
        }
      }
    }

    return {
      rawTitle: rowTitle,
      selectedCols: Array.isArray(selectedCols) ? selectedCols : [],
      skipped: isSkipped,
      unrequiredSkipped: isUnrequiredSkipped,
      extra: extraData,
    }
  })

  let formattedCorrectAnswers: Array<string> = []

  if (question.answer?.correct_answer) {
    const correctAnswer = question.answer.correct_answer
    if (Array.isArray(correctAnswer) && correctAnswer.length) {
      const firstItem = correctAnswer[0]
      if (Array.isArray(firstItem)) {
        const answers = correctAnswer as Array<Array<{ row: string, col: string }>>
        formattedCorrectAnswers = answers.map(rowItem => `${rowItem[0].row} – ${rowItem.map(item => item.col).join(' / ')}`)
      }
      else {
        const answers = correctAnswer as Array<{ row: string, col: string }>
        formattedCorrectAnswers = answers.map(item => `${item.row} – ${item.col}`)
      }
    }
  }

  return {
    ...base,
    rows,
    rowsDivider: true,
    comment: question.answer?.answer?.comment || null,
    unrequiredSkipped: rows.every(row => row.unrequiredSkipped),
    clarifyingQuestion: question.clarifyingQuestion || null,
    selfVariantLabel: question.self_variant_text || null,
    formattedCorrectAnswers,
  }
}

function formatVariantsQuestion(
  question: GetResultsVariantsQuestion,
  translations: any,
): VariantsQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const detectType = (variant: typeof question.variants[number]) => {
    if (!variant.file_id || !variant.file_url)
      return 'image'

    const extension = variant.file_url.split('.').pop()?.toLowerCase() || ''

    if (ALLOWED_FILE_TYPES.video.includes(extension))
      return 'video'

    if (ALLOWED_FILE_TYPES.audio.includes(extension))
      return 'audio'

    return 'image'
  }

  const processedVariants = question.variants.map(variant => ({
    ...variant,
    type: detectType(variant) as 'image' | 'video' | 'audio',
    file_url: variant.file_url ? withRootUrl(variant.file_url) : null,
    preview_url: variant.preview_url ? withRootUrl(variant.preview_url) : null,
  }))

  const selectedVariants = question.answer?.selectedIds
    ? processedVariants.filter((variant) => {
      const id = variant.question_detail_id || variant.dictionary_element_id || variant.id

      return question.answer?.selectedIds?.includes(String(id))
    })
    : []

  const rightAnswerVariants = question.answer?.correct_answer?.map?.((answer) => {
    const variant = processedVariants.find(v => v.id === answer.id || v.dictionary_element_id === answer.id)
    if (!variant)
      return undefined

    return variant
  }).filter(Boolean) as Array<VariantQuestionItem> || []

  const variantsHaveMedia = rightAnswerVariants.some(variant => variant.file_url)

  let answerStatus = null as 'correct' | 'wrong' | 'intermediate' | null

  const hasCorrectAnswer = question.answer?.correct_answer?.length

  const everyVariantIsCorrect = hasCorrectAnswer && rightAnswerVariants.every(variant => selectedVariants.some(v => v.id === variant.id))
  const someVariantsAreCorrect = hasCorrectAnswer && rightAnswerVariants.some(variant => selectedVariants.some(v => v.id === variant.id))
  const unrequiredSkipped = !question.answer?.selectedIds?.length && !question.answer?.selfVariant
  const skippedOrUnrequiredSkipped = question.answer?.skipped || unrequiredSkipped

  if (everyVariantIsCorrect)
    answerStatus = 'correct'
  else if (someVariantsAreCorrect)
    answerStatus = 'intermediate'
  else if (skippedOrUnrequiredSkipped || !hasCorrectAnswer)
    answerStatus = null
  else
    answerStatus = 'wrong'

  return {
    ...base,
    variants: processedVariants,
    selectedVariants,
    rightAnswerVariants,
    selfVariant: question.answer?.selfVariant || null,
    selfVariantLabel: question.self_variant_text || null,
    variantsHaveMedia,
    answerStatus,
    unrequiredSkipped,
  }
}
function formatCardSortingQuestion(
  question: GetResultsCardSortingQuestion,
  translations: any,
): MatrixQuestionReport {
  const base = formatBaseQuestion(question, translations)

  const rows = []
  const answer = question.answer?.answer
  const hasAnswer = !!answer

  if (hasAnswer) {
    const cardSortingCategories = question.cardSortingCategories
    let variants = [...question.variants]
    Object.entries(answer).forEach(([rowId, selectedIds]) => {
      const rawTitle = cardSortingCategories.find(({ id }) => String(id) === rowId)?.name
      const selectedCols: Array<string> = []
      const ids = Array.isArray(selectedIds) ? selectedIds : []
      ids.forEach((selectedId) => {
        const name = variants.find(({ id }) => String(id) === selectedId)?.variant
        if (name) {
          variants = variants.filter(({ id }) => String(id) !== selectedId)
          selectedCols.push(name)
        }
      })
      const row = { rawTitle, selectedCols }
      rows.push(row)
    })
    if (variants.length) {
      rows.push({
        rawTitle: 'Без категории',
        selectedCols: variants.map(({ variant }) => variant),
      })
    }
  }

  return {
    ...base,
    rows,
    rowsDivider: false,
    unrequiredSkipped: !hasAnswer,
    comment: question.answer?.comment || null,
    selfVariantLabel: question.self_variant_text || null,
  }
}

function formatFirstClickTestQuestion(
  question: GetResultsFirstClickTestQuestion,
  translations: any,
): FirstClickTestReport {
  const base = formatBaseQuestion(question, translations)

  const points = question.answer?.points?.length || 0

  return {
    ...base,
    points,
  }
}

export function formatQuestionForPointsReport(
  question: GetResultsQuestions,
  translations: any,
) {
  return computed(() => {
    const translationsValue = toValue(translations)
    switch (question.type) {
      case 1:
        return formatVariantsQuestion(question, translationsValue)
      case 2:
        return formatTextQuestion(question, translationsValue)
      case 14:
        return formatDiffQuestion(question, translationsValue)
      case 3:
        return formatDateQuestion(question, translationsValue)
      case 4:
        return formatAddressQuestion(question, translationsValue)
      case 5:
        return formatFileQuestion(question, translationsValue)
      case 6:
        return formatQuizQuestion(question, translationsValue)
      case 7:
        return formatStarVariantsQuestion(question, translationsValue)
      case 8:
        return formatPriorityQuestion(question, translationsValue)
      case 11:
        return formatSmileQuestion(question, translationsValue)
      case 12:
        return formatNpsQuestion(question, translationsValue)
      case 15:
        return formatStarsQuestion(question, translationsValue)
      case 18:
        return formatRatingScaleQuestion(question, translationsValue)
      case 20:
        return formatScaleQuestion(question, translationsValue)
      case 9:
        return formatMediaVariantsQuestion(question, translationsValue)
      case 10:
        return formatGalleryQuestion(question, translationsValue)
      case 21:
        return formatMatrix3dQuestion(question, translationsValue)
      case 17:
        return formatClassifierQuestion(question, translationsValue)
      case 19:
        return formatClassifierQuestion(question, translationsValue)
      case 13:
        return formatMatrixQuestion(question, translationsValue)
      case 22:
        return formatCardSortingQuestion(question, translationsValue)
      case 23:
        return formatDistributionScaleQuestion(question, translationsValue)
      case 24:
        return formatFirstClickTestQuestion(question, translationsValue)
      default:
        throw new Error(`Formatting for question type ${(question as any)?.type} is not implemented.`)
    }
  })
}
