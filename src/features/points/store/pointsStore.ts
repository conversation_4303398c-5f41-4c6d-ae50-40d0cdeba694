import type { GetResultsResponse } from '../schemas/getResults'
import type { PointsData, PointsInterpretationRange } from '../types'
import type { FormattedResultsData, QuestionReport } from '../types/formatter'
// #if VITE_USE_SENTRY
import * as Sentry from '@sentry/vue'
// #endif
import { useTranslationsStore } from '@shared/store/translationsStore'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { fetchPoints, getResults } from '../api'
import { formatQuestionForPointsReport } from '../lib/pointsReportFormatter'

interface PollData {
  poll: {
    point_system: number
  }
  variables?: {
    scoresInterpretationRanges?: PointsInterpretationRange[]
  }
}

interface PrintModeState {
  enabled: boolean
  ready: boolean
  loading: boolean
}

export const usePointsStore = defineStore('points', () => {
  const isEnabled = ref(false)
  const pointsData = ref<PointsData | null>(null)
  const resultsData = ref<GetResultsResponse | null>(null)
  const formattedResultsData = computed<FormattedResultsData>(() => {
    if (!resultsData.value) {
      return {
        points: {
          answer_points: 0,
          points_max: 0,
          percent: 0,
        },
        questions: [],
        poll: null,
        contact: null,
      }
    }

    const translationsStore = useTranslationsStore()
    const formattedQuestions = resultsData.value.questions.map((question) => {
      if (question.isDeleted)
        return null

      const translations = translationsStore.getQuestionTranslationById(question.id)
      const formatted = formatQuestionForPointsReport(question, translations)
      return formatted.value || formatted
    }).filter(Boolean) as QuestionReport[]

    return {
      points: resultsData.value.points,
      questions: formattedQuestions,
      poll: resultsData.value.poll,
      contact: resultsData.value.contact,
    }
  })

  const pointsInterpretationRanges = ref<PointsInterpretationRange[]>([])
  const isLoading = ref(false)
  const isGetResultsLoading = ref(false)
  const authKey = ref<string | null>(null)

  const totalPoints = computed(() => pointsData.value?.points || 0)
  const maxPoints = computed(() => pointsData.value?.maxPoints || 0)
  const pointsPercentage = computed(() => {
    if (!maxPoints.value)
      return 0
    return Math.round((totalPoints.value / maxPoints.value) * 100)
  })

  const currentPointsInterpretation = computed(() => {
    if (!pointsData.value || !pointsInterpretationRanges.value.length)
      return null

    const points = pointsData.value.points

    return pointsInterpretationRanges.value.find(range =>
      points >= range.min && points <= range.max,
    )
  })

  const printMode = ref<PrintModeState>({
    enabled: false,
    ready: false,
    loading: false,
  })

  // Computed helpers for print mode
  const isPrintMode = computed(() => printMode.value.enabled)
  const isPrintReady = computed(() => printMode.value.ready)
  const isPrintLoading = computed(() => printMode.value.loading)

  async function fetchPointsData(key: string) {
    if (!isEnabled.value)
      return

    isLoading.value = true
    try {
      const fetchedPointsData = await fetchPoints(key)
      pointsData.value = fetchedPointsData
    }
    catch (error) {
      console.error('error fetching points data:', error)
      // #if VITE_USE_SENTRY
      Sentry.captureException(error)
      // #endif
    }
    finally {
      isLoading.value = false
    }
  }

  /**
   * Инициализация печатной версии отчета на основе параметров URL
   */
  function initializePrintMode() {
    const params = new URLSearchParams(window.location.search)
    const isPrintReport = params.get('print-report') === '1'

    if (isPrintReport) {
      printMode.value.enabled = true
      const authKeyParam = params.get('auth-key')
      if (authKeyParam) {
        authKey.value = authKeyParam
      }
    }
  }

  /**
   * Загрузка печатной версии отчета
   */
  async function loadPrintReport() {
    if (!isPrintMode.value || !authKey.value || !isEnabled.value)
      return

    printMode.value.loading = true

    try {
      await fetchResultsData(authKey.value)
      printMode.value.ready = true
    }
    catch (error) {
      console.error('Failed to load print report:', error)
      // #if VITE_USE_SENTRY
      Sentry.captureException(error)
      // #endif
    }
    finally {
      printMode.value.loading = false

      setTimeout(() => {
        triggerNativePrint()
      }, 1000)
    }
  }

  function triggerNativePrint() {
    window.print()
  }

  function initialize(pollData: PollData) {
    isEnabled.value = pollData.poll.point_system === 1
    pointsInterpretationRanges.value = pollData.variables?.scoresInterpretationRanges || []

    initializePrintMode()

    // Load print report data if in print mode
    if (isPrintMode.value) {
      loadPrintReport()
    }
  }

  async function fetchResultsData(key: string) {
    if (!isEnabled.value)
      return

    try {
      isGetResultsLoading.value = true
      const fetchedResultsData = await getResults(key)
      resultsData.value = fetchedResultsData
    }
    catch (error) {
      console.error('error fetching results data:', error)
      // #if VITE_USE_SENTRY
      Sentry.captureException(error)
      // #endif
    }
    finally {
      isGetResultsLoading.value = false
    }
  }

  return {
    isEnabled,
    pointsData,
    pointsInterpretationRanges,
    isLoading,
    isGetResultsLoading,
    totalPoints,
    maxPoints,
    pointsPercentage,
    currentPointsInterpretation,
    authKey,
    fetchPointsData,
    initialize,
    resultsData,
    formattedResultsData,
    fetchResultsData,
    isPrintMode,
    isPrintReady,
    isPrintLoading,
  }
})
