import type MaskTypes from '@/shared/constants/maskTypes'
import type { FileType } from '@/shared/helpers/files'
import type {
  AssessmentVariant,
  GetResultsAddressQuestion,
  GetResultsCardSortingQuestion,
  GetResultsClassifierQuestion,
  GetResultsDateQuestion,
  GetResultsDiffQuestion,
  GetResultsDistributionScaleQuestion,
  GetResultsFileQuestion,
  GetResultsFirstClickTestQuestion,
  GetResultsGalleryQuestion,
  GetResultsMatrix3dQuestion,
  GetResultsMatrixQuestion,
  GetResultsMediaVariantsQuestion,
  GetResultsNpsQuestion,
  GetResultsPriorityQuestion,
  GetResultsQuestions,
  GetResultsQuizQuestion,
  GetResultsRatingScaleQuestion,
  GetResultsResponse,
  GetResultsScaleQuestion,
  GetResultsSmileQuestion,
  GetResultsStarsQuestion,
  GetResultsStarVariantsQuestion,
  GetResultsTextQuestion,
  GetResultsVariantsQuestion,
} from '../schemas/getResults'

export interface VariantQuestionItem {
  id: string | number
  variant: string
  file_url: string | null
  preview_url: string | null
  type: 'image' | 'video' | 'audio'
  file_id: number | null
}

export interface VariantsQuestionReport extends BaseQuestionReport {
  variants: Array<VariantQuestionItem>
  selectedVariants: Array<VariantQuestionItem>
  rightAnswerVariants: Array<VariantQuestionItem>
  selfVariant: string | null
  selfVariantLabel: string | null
  variantsHaveMedia: boolean
}

export interface MediaVariantsItem {
  id: number
  url: string
  src: string
  poster: string
  description: string
  points: number | null
  type: string
}

export interface MediaVariantsQuestionReport extends BaseQuestionReport {
  chooseMedia: Array<MediaVariantsItem>
  selectedMedia: string[]
  comment: string | null
  correctAnswerVariants: Array<MediaVariantsItem>
}

export interface BaseQuestionReport {
  id: number
  type: number
  description: string
  commentEnabled: 0 | 1
  commentLabel: string | null
  comment: string | null
  points: number | string[] | null
  maxPoints: number | null
  assessments: AssessmentVariant[] | null
  skipped: boolean
  unrequiredSkipped: boolean
  skipText: string | null
  rightAnswer: GetResultsQuestions['rightAnswer'] | null
  answer: GetResultsQuestions['answer'] | null
  isAnswerCorrect: boolean
  answerStatus: 'correct' | 'wrong' | 'intermediate' | null
}

export type RatingScaleQuestionReport = BaseQuestionReport & {
  rating: number | null
  clarifyingQuestion: string | null
  label: string | null
  starRatingOptions: GetResultsRatingScaleQuestion['starRatingOptions'] | null
  selfVariantText: string | null
  selfVariantLabel: string | null
}

export type TextQuestionReport = BaseQuestionReport & {
  mask_type: MaskTypes
  formattedAnswer: string | null
  nameAnswer?: {
    name?: string
    surname?: string
    patronymic?: string
  } | null
}

export interface StarVariantData {
  id: string | number
  variant: string
  rating: number
  skipped: boolean
  unrequiredSkipped: boolean
  extra?: {
    variants: AssessmentVariant[]
    selfVariant: string | null
    textValue: string | null
  }
}

export type StarsQuestionReport = BaseQuestionReport & {
  rating: number | null
  clarifyingQuestion: string | null
  label: string | null
  starRatingOptions: GetResultsStarsQuestion['starRatingOptions'] | null
  selfVariantText: string | null
  selfVariantLabel: string | null
}

export type StarVariantsQuestionReport = BaseQuestionReport & {
  clarifyingQuestion: string | null
  starRatingOptions: GetResultsStarVariantsQuestion['starRatingOptions'] | null
  formattedVariants: StarVariantData[]
  selfVariantLabel: string | null
}

export type DateQuestionReport = BaseQuestionReport & {
  answerText: string | null
  rightAnswerText: string | null
}

export type AddressQuestionReport = BaseQuestionReport & {
  formattedAnswer: string | null
}

export interface Extra {
  variants: Array<{
    id: string
    variant: string
    file_url: string | null
    file_id: number | null
    preview_url: string | null
    type: 'image' | 'video' | 'audio' | null
  }>
  selfVariant: string | null
  selfVariantLabel: string | null
  textValue: string | null
  selfVariantFile: {
    file_id: number
    file_url: string | null
    preview_url: string | null
    type: 'image' | 'video' | 'audio' | null
  } | null
}

export interface NpsVariantReport {
  id: string | number
  variant: string
  rating: number
  skipped: boolean
  unrequiredSkipped: boolean
  color: string
  extra: Extra | null
}

export interface NpsQuestionReport extends BaseQuestionReport {
  rating: number | null
  clarifyingQuestion: string | null
  label: string | null
  npsStartLabel: string | null
  npsEndLabel: string | null
  fromOne: boolean
  npsDesign: number
  color: string
  formattedVariants: NpsVariantReport[]
  isVariants: boolean
  extraQuestionType: number
  extra: Extra | null
}

export interface ScaleVariantData {
  id: string | number
  text: string
  rating: number
  skipped: boolean
  unrequiredSkipped: boolean
  skipText: string | null
}

export interface QuizValue {
  label: string
  id: number
  maskType: MaskTypes | null
  value: string | {
    name: string
    surname: string
    patronymic?: string
  }
}

export type QuizQuestionReport = BaseQuestionReport & {
  values: QuizValue[]
}

export type ScaleQuestionReport = BaseQuestionReport & {
  rating: number | null
  minRating: number
  maxRating: number
  step: number
  isVariants: boolean
  formattedVariants: ScaleVariantData[]
}

export type DistributionScaleQuestionReport = BaseQuestionReport & {
  rating: number | null
  minRating: number
  maxRating: number
  step: number
  isVariants: boolean
  formattedVariants: ScaleVariantData[]
}

export interface SmileQuestionReport extends BaseQuestionReport {
  smileUrl: string | null
  smileLabel: string | null
  comment: string | null
  extra: Extra | null
  clarifyingQuestion: string | null
}

export interface GalleryMediaItem {
  id: number
  url: string
  src: string
  poster: string
  description: string
  type: 'video' | 'image'
  rating: number
  skipped: boolean
  unrequiredSkipped: boolean
}

export interface GalleryQuestionReport extends BaseQuestionReport {
  media: GalleryMediaItem[]
  comment: string | null
}

export interface PriorityQuestionReport extends BaseQuestionReport {
  variants: Array<{
    id: string
    text: string
  }>
  correctFormattedAnswers: string[]
}

export type FileQuestionReport = BaseQuestionReport & {
  files: Array<{
    id: number
    name: string
    url: string
    preview_url: string | null
    type: FileType
  }>
}

export interface DiffQuestionReport extends BaseQuestionReport {
  differentialRows: Array<{
    id: string | number
    rating: number
    startLabel: string
    endLabel: string
    position: number
  }>
  semDifSetting: {
    form: string
    startPointColor: string
    endPointColor: string
  }
}

export interface MatrixQuestionReport extends BaseQuestionReport {
  rows: Array<{
    rawTitle: string
    selectedCols: Array<string>
    skipped?: boolean
    unrequiredSkipped?: boolean
    extra?: {
      text?: string
      variants?: Array<{
        id: string
        text: string
      }> | null
      selfVariant?: string
    } | null
  }>
  rowsDivider: boolean
  comment: string | null
  clarifyingQuestion?: string | null
  selfVariantLabel?: string | null
  formattedCorrectAnswers?: Array<string>
}

export interface Matrix3dRowData {
  id: number
  name: string
  skipped: boolean
  unrequiredSkipped: boolean
  answers: Record<string, Array<{ id: number, name: string }>>
}

export interface Matrix3dColumnData {
  id: number
  name: string
  variants: Array<{ id: number, name: string, skipped: boolean, unrequiredSkipped: boolean }> | null
}

export interface Matrix3dQuestionReport extends BaseQuestionReport {
  rows: Array<Matrix3dRowData>
  columns: Array<Matrix3dColumnData>
}

export interface ClassifierVariant {
  id: string
  text: string
  description: string | undefined
  path: string | undefined
}

export interface ClassifierQuestionReport extends BaseQuestionReport {
  selectedVariants: ClassifierVariant[]
  isTreeType: boolean
}

interface CardSortingCategory {
  id: string | number
  name: string
}

export interface CardSortingReport extends BaseQuestionReport {
  cardSortingCategories: Array<CardSortingCategory>
  variants: Array<VariantQuestionItem>
}

export interface FirstClickTestReport extends BaseQuestionReport {}

export type QuestionReport =
  | VariantsQuestionReport
  | FileQuestionReport
  | StarsQuestionReport
  | RatingScaleQuestionReport
  | TextQuestionReport
  | QuizQuestionReport
  | StarVariantsQuestionReport
  | DateQuestionReport
  | PriorityQuestionReport
  | NpsQuestionReport
  | GalleryQuestionReport
  | MediaVariantsQuestionReport
  | AddressQuestionReport
  | ScaleQuestionReport
  | DistributionScaleQuestionReport
  | SmileQuestionReport
  | DiffQuestionReport
  | Matrix3dQuestionReport
  | MatrixQuestionReport
  | ClassifierQuestionReport
  | CardSortingReport
  | FirstClickTestReport

export type GetResultsQuestion =
  | GetResultsVariantsQuestion
  | GetResultsTextQuestion
  | GetResultsDateQuestion
  | GetResultsAddressQuestion
  | GetResultsFileQuestion
  | GetResultsQuizQuestion
  | GetResultsStarVariantsQuestion
  | GetResultsPriorityQuestion
  | GetResultsMediaVariantsQuestion
  | GetResultsGalleryQuestion
  | GetResultsSmileQuestion
  | GetResultsNpsQuestion
  | GetResultsMatrixQuestion
  | GetResultsDiffQuestion
  | GetResultsStarsQuestion
  | GetResultsRatingScaleQuestion
  | GetResultsClassifierQuestion
  | GetResultsScaleQuestion
  | GetResultsDistributionScaleQuestion
  | GetResultsMatrix3dQuestion
  | GetResultsCardSortingQuestion
  | GetResultsFirstClickTestQuestion

export interface FormattedResultsData {
  points: GetResultsResponse['points']
  questions: QuestionReport[]
  poll: GetResultsResponse['poll']
  contact: GetResultsResponse['contact']
}
