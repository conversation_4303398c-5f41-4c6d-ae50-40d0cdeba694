import type { z } from 'zod'
import type { pointsDataSchema } from '../schemas'
import type { QuestionReport } from './formatter'

export interface PointsInterpretationRange {
  min: number
  max: number
  result: string
  description: string
  position: number
}

export type PointsData = z.infer<typeof pointsDataSchema>

export type AnswerStatus = 'correct' | 'wrong' | 'intermediate'

export interface BasePointsProps {
  number: number
  question: QuestionReport
  view?: 'default' | 'print'
}
