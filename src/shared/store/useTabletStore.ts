import { INTER_BLOCK } from '@/entities/question/model/types'

import { usePollStore } from '@entities/poll/model/store'
// #if VITE_USE_SENTRY
import * as Sentry from '@sentry/vue'
// #endif
import { defineStore } from 'pinia'
import { ref, toValue, watch } from 'vue'

interface TimerConfig {
  questionTimeout: number
  finalScreenTimeout: number
  firstPage: any
}

export const useTabletStore = defineStore('tablet', () => {
  const isTabletMode = ref(false)

  const store = usePollStore()

  const workerInstance = ref<Worker | null>(null)
  const questionTimeout = ref(0)
  const finalScreenTimeout = ref(0)
  const firstPage = ref<any>(null)
  let pollResetFunction: (() => void) | null = null
  const currentTimeoutDuration = ref(0)
  const GLOBAL_CLICK_HANDLER_EVENTS = ['click', 'droppable-drag-start', 'droppable-drag-end'] as const

  let globalClickHandler: ((e: any) => void) | null = null

  function initialize() {
    const params = new URLSearchParams(window.location.search)
    const hasTabletParam = params.get('tablet') === '1'

    const pathSegments = window.location.pathname.split('/')
    const hasTabletPrefix = pathSegments.includes('t')

    if (hasTabletParam || hasTabletPrefix) {
      isTabletMode.value = true
      document.body.classList.add('tablet-mode')
    }
  }

  function initializeInactivityTimer(config: TimerConfig, pollStoreResetFn: () => void) {
    if (!isTabletMode.value)
      return

    questionTimeout.value = config.questionTimeout
    finalScreenTimeout.value = config.finalScreenTimeout
    firstPage.value = config.firstPage || null
    pollResetFunction = pollStoreResetFn

    stopInactivityTimer()

    setupInteractionWatcher(firstPage.value)

    try {
      workerInstance.value = new Worker(
        new URL('@shared/workers/pollTimer.worker.js', import.meta.url),
        { type: 'module' },
      )

      workerInstance.value.onmessage = (e) => {
        const { type } = e.data
        switch (type) {
          case 'WORKER_READY':
            break
          case 'TICK':
            break
          case 'FINISHED':
            if (pollResetFunction) {
              pollResetFunction()
            }
            break
        }
      }

      workerInstance.value.onerror = (error) => {
        console.error('Inactivity timer worker error:', error)
        // #if VITE_USE_SENTRY
        Sentry.captureException(error)
        // #endif
        stopInactivityTimer()
      }

      window.addEventListener('beforeunload', stopInactivityTimer)

      if (!globalClickHandler) {
        globalClickHandler = () => {
          if (isTabletMode.value && workerInstance.value && currentTimeoutDuration.value > 0) {
            startOrResetInactivityTimer(currentTimeoutDuration.value)
          }
        }

        GLOBAL_CLICK_HANDLER_EVENTS.forEach((event) => {
          if (globalClickHandler) {
            document.addEventListener(event as string, globalClickHandler)
          }
        })
      }
    }
    catch (error) {
      console.error('Failed to create inactivity timer worker:', error)
      // #if VITE_USE_SENTRY
      Sentry.captureException(error)
      // #endif
      workerInstance.value = null
    }
  }

  function startOrResetInactivityTimer(duration: number) {
    if (!isTabletMode.value || !workerInstance.value)
      return

    if (duration > 0) {
      currentTimeoutDuration.value = duration
    }

    workerInstance.value.postMessage({
      type: 'RESET',
      payload: { timeLeft: duration },
    })
  }

  function stopInactivityTimer() {
    window.removeEventListener('beforeunload', stopInactivityTimer)

    if (workerInstance.value) {
      workerInstance.value.postMessage({ type: 'STOP' })
      workerInstance.value.terminate()
      workerInstance.value = null
    }

    if (globalClickHandler) {
      document.removeEventListener('click', globalClickHandler)
      globalClickHandler = null
    }
  }

  function setupInteractionWatcher(firstPage: any) {
    if (!firstPage && !toValue(firstPage?.questions))
      return

    toValue(firstPage?.questions).forEach((question: any) => {
      if (question.type === INTER_BLOCK && toValue(question.isStartScreen)) {
        watch(question.agreementValue, (value) => {
          if (value) {
            startOrResetInactivityTimer(questionTimeout.value)
          }
        })
      }
      else {
        watch(question.hasInteracted, (value) => {
          if (value) {
            startOrResetInactivityTimer(questionTimeout.value)
          }
        })
      }
    })
  }

  let cleanupPreviousWatchers: (() => void) | null = null

  watch(() => store.activePage, (page) => {
    // Clean up previous watchers if they exist
    if (cleanupPreviousWatchers) {
      cleanupPreviousWatchers()
      cleanupPreviousWatchers = null
    }

    const cleanupFunctions: (() => void)[] = []
    page.questions.forEach((q: any) => {
      const stop = watch(q.canMoveToNextQuestion, (canMove) => {
        if (canMove && isTabletMode.value) {
          store.goNext()
        }
      })
      cleanupFunctions.push(stop)
    })

    cleanupPreviousWatchers = () => {
      cleanupFunctions.forEach(stop => stop())
    }
  })

  return {
    isTabletMode,
    questionTimeout,
    finalScreenTimeout,
    initialize,
    initializeInactivityTimer,
    startOrResetInactivityTimer,
    stopInactivityTimer,
    currentTimeoutDuration,
  }
})
