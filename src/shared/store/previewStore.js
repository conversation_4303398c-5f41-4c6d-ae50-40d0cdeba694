import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const usePreviewStore = defineStore('preview', () => {
  const isPreviewMode = ref(false)
  const isDesignPreview = ref(false)
  const isFullPreview = ref(false)
  const isWidgetPreviewMode = ref(false)
  const previewData = ref(null)

  /**
   * Флаг, который указывает, является ли превью функциональным
   * В данном режиме превью работает в штатном режиме, но НЕ сохраняются ответы
   */
  const isFunctionalPreview = computed(() => isFullPreview.value || isDesignPreview.value)

  /**
   * Флаг, который указывает, является ли превью нефункциональным
   * В данном режиме не работает логика отображения, логика переходов, донор-реципиент
   */
  const isNonFunctionalPreview = computed(() => isPreviewMode.value && !isFunctionalPreview.value)

  function updatePreviewData(data) {
    previewData.value = data
  }

  function setPreviewMode(value) {
    isPreviewMode.value = value
  }

  function setIsWidgetPreviewMode(value) {
    isWidgetPreviewMode.value = value
  }

  function setIsDesignPreview(value) {
    isDesignPreview.value = value
  }

  function setIsFullPreview(value) {
    isFullPreview.value = value
  }

  /**
   * Возвращает тип превью в виде строки
   * @returns {string | null} тип превью или null, если превью не активно
   */
  function getPreviewType() {
    if (!isPreviewMode.value)
      return null
    if (isDesignPreview.value)
      return 'preview-design'
    if (isFullPreview.value)
      return 'preview-full'
    return 'preview-default'
  }

  function sendLinkToParent(link) {
    window.parent.postMessage({
      type: 'PREVIEW_LINK',
      data: { link },
    }, '*')
  }

  function sendAppReadyMessage() {
    if (isPreviewMode.value) {
      window.parent.postMessage({
        type: 'fz:app_ready',
      }, '*')
    }
  }

  return {
    isPreviewMode,
    isDesignPreview,
    isFullPreview,
    isFunctionalPreview,
    isWidgetPreviewMode,
    previewData,
    isNonFunctionalPreview,
    updatePreviewData,
    setPreviewMode,
    setIsDesignPreview,
    setIsFullPreview,
    setIsWidgetPreviewMode,
    sendLinkToParent,
    getPreviewType,
    sendAppReadyMessage,
  }
})
