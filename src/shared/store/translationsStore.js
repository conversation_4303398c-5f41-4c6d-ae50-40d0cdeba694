import { useUrlSearchParams } from '@vueuse/core'
import { defineStore } from 'pinia'
import { computed, ref, shallowRef } from 'vue'
import { getBaseApiUrl, getBaseAssetsUrl } from '../api'
import { LANGS_DATA } from '../constants/langs'
import { usePreviewStore } from './previewStore'

export const useTranslationsStore = defineStore('translations', () => {
  const params = useUrlSearchParams('history')
  const langParam = computed(() => params.lang)
  const baseAssetsUrl = getBaseAssetsUrl()
  const pollId = ref(null)
  const isLoading = ref(false)
  const pollKey = ref(null)

  const previewStore = usePreviewStore()

  /**
   * Переводы, которые приходят из pollData.lang
   * Обычно, если при получении опроса есть параметр lang (например, lang=en), то он приходит в pollDataLang
   * Если нет, то pollDataLang пустой
   * @type {import('vue').Ref<object>}
   */
  const pollDataLang = ref({})

  /**
   * Проверка на наличие нескольких языков в опросе
   * @type {import('vue').Ref<boolean>}
   */
  const multipleLangsEnabled = ref(false)

  const foquzPollLang = shallowRef({})

  const allLangs = shallowRef([])
  const defaultLang = computed(() => allLangs.value.find(l => l.default === 1))
  const selectedLangShortCode = ref(null)

  const selectedLang = computed(() => allLangs.value.find(l => l.shortCode === selectedLangShortCode.value) || defaultLang.value)

  const translations = computed(() => {
    const lang = selectedLang.value
    return lang
  })

  /**
   * Устанавливает язык опроса
   * @param {string} shortCode - Короткий код языка (например, 'ru', 'en', 'de' и т.д.)
   */
  function setLanguage(shortCode) {
    const lang = allLangs.value.find(l => l.shortCode === shortCode)
    if (lang) {
      handlePollTranslation(pollKey.value, lang.lang_id, pollId.value, previewStore.isPreviewMode)
    }
  }

  /**
   * Инициализирует языки для опроса
   * @param {object} pollData - Данные опроса
   */
  function initializeLanguages(pollData) {
    pollId.value = pollData.poll.id
    foquzPollLang.value = pollData.poll.foquzPollLang
    pollDataLang.value = pollData.lang
    allLangs.value = pollData.poll.foquzPollLangs?.map((lang) => {
      const langData = {
        ...lang,
        shortCode: LANGS_DATA[lang.lang_id].shortCode,
        name: LANGS_DATA[lang.lang_id].name,
        code: LANGS_DATA[lang.lang_id].code,
        img: baseAssetsUrl + LANGS_DATA[lang.lang_id].img,
      }

      if (pollDataLang.value && pollDataLang.value.poll_lang_id === lang.lang_id) {
        return {
          ...langData,
          ...pollDataLang.value,
        }
      }
      return langData
    }) || []

    if (allLangs.value.length > 1) {
      multipleLangsEnabled.value = true
    }

    // Если в URL есть параметр lang, то устанавливаем язык опроса из него
    if (langParam.value) {
      const searchLangItem = allLangs.value.find(l => l.shortCode === langParam.value)
      if (searchLangItem) {
        selectedLangShortCode.value = langParam.value
      }
    }

    const lang = defaultLang.value?.shortCode || 'ru'
    document.documentElement.lang = lang
  }

  /**
   * Возвращает перевод для заданного ключа
   * @param {string} key - Ключ перевода
   * @param {object} [params] - Параметры для подстановки в перевод
   * @returns {import('vue').ComputedRef<string>} Вычисляемое значение перевода
   * @NOTE:
   * Данная функция возвращает перевод в виде computed значения,
   * чтобы можно было использовать актуальный перевод при смене языка
   * Лучше использовать с toValue, чтобы получить значение перевода
   * @see https://vuejs.org/api/reactivity-utilities#tovalue
   * @example
   * const translation = t('Привет, {name}!', { name: 'Мир' })
   * console.log(toValue(translation)) // "Привет, Мир!"
   * console.log(translation.value) // Можно еще вот так. Тоже вернется "Привет, Мир!"
   */
  function t(key, params = {}) {
    let messages = {}
    return computed(() => {
      const isInitialLang = selectedLang.value?.id === foquzPollLang.value?.id
      if (isInitialLang) {
        messages = foquzPollLang.value?.messages || {}
      }
      else {
        messages = translations.value.messages || {}
      }
      let translation = key

      if (messages[key]) {
        translation = messages[key]
      }

      return translation.replace(/\{(\w+)\}/g, (_, placeholder) => {
        if (placeholder === 'count' && params.count !== undefined) {
          return params.count
        }
        return params[placeholder] !== undefined ? params[placeholder] : `{${placeholder}}`
      })
    })
  }

  /**
   * Получает перевод для вопроса
   * @param {object} question - Объект вопроса
   * @returns {object} Перевод вопроса
   */
  function getQuestionTranslation(question) {
    const lang = selectedLang.value
    const translationsFromRemote = lang?.questions?.[question.question_id]
    const translationsFromLocal = question.lang

    if (translationsFromRemote) {
      return translationsFromRemote
    }
    else if (lang?.id === question.lang?.foquz_poll_lang_id) {
      return translationsFromLocal
    }
    return {}
  }

  /**
   * Получает перевод для вопроса по его id
   * @param {number | null} questionId - Идентификатор вопроса
   * @returns {object} Перевод вопроса
   */
  function getQuestionTranslationById(questionId) {
    if (!questionId) {
      return {}
    }
    return selectedLang.value?.questions?.[questionId]
  }

  /**
   * Получает перевод для промежуточного блока
   * @param {object} questionData - Данные вопроса
   * @returns {object} Перевод промежуточного блока
   */
  function getInterBlockTranslation(questionData) {
    const intermediateBlockId = questionData.intermediateBlock?.id
    const translationsFromRemote = selectedLang.value?.questions?.[questionData.question_id]?.settingLangs[intermediateBlockId]
    const translationsFromLocal = questionData?.intermediateBlock?.langs || []

    const localLangTranslation = translationsFromLocal.find(lang => lang.lang_id === selectedLang.value?.id)
    return translationsFromRemote || localLangTranslation || {}
  }

  /**
   * Обрабатывает перевод опроса
   * @param {string} key - Ключ опроса
   * @param {string|number} langId - Идентификатор языка
   * @param {string|number} pollId - Идентификатор опроса
   * @param {boolean} isPreview - Флаг, указывающий, запрос идет из preview
   */
  async function handlePollTranslation(key, langId, pollId = null, isPreview = false) {
    const index = allLangs.value.findIndex(l => l.lang_id === langId)
    const lang = allLangs.value[index]

    const preventLoading = ref(false)

    // При смене языка показываем индикатор загрузки через 1 секунду после вызова функции
    setTimeout(() => {
      if (!preventLoading.value) {
        isLoading.value = true
      }
    }, 1000)

    try {
      // Если уже есть переводы, то не нужно их загружать
      if (lang.messages || lang.default === 1) {
        preventLoading.value = true
        return
      }

      const data = await getPollTranslation(key, langId, pollId, isPreview)
      if (data) {
        if (index !== -1) {
          allLangs.value = allLangs.value.map((lang, idx) => {
            if (idx === index) {
              return {
                ...allLangs.value[index],
                ...data,
              }
            }
            return lang
          })

          params.lang = allLangs.value[index].shortCode
        }
      }
    }
    catch (error) {
      console.error('Error fetching poll translation:', error)
    }
    finally {
      preventLoading.value = true
      isLoading.value = false
      const shortCode = allLangs.value[index].shortCode
      params.lang = shortCode
      selectedLangShortCode.value = shortCode
    }
  }

  /**
   * Получает перевод опроса с сервера
   * @param {string} key - Ключ опроса
   * @param {string|number} langId - Идентификатор языка
   * @param {string|number} pollId - Идентификатор опроса
   * @param {boolean} isPreview - Флаг, указывающий, запрос идет из preview
   * @returns {Promise<object>} Данные перевода
   */
  async function getPollTranslation(key, langId, pollId = null, isPreview = false) {
    let pollTranslationsUrl = `${getBaseApiUrl()}/foquz/api/p/get-translate?langId=${langId}`
    if (pollId) {
      pollTranslationsUrl += `&pollId=${pollId}`
    }
    if (key) {
      pollTranslationsUrl += `&key=${key}`
    }

    if (isPreview) {
      pollTranslationsUrl += `&preview=1`
    }

    const response = await fetch(pollTranslationsUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    const data = await response.json()
    return data
  }

  return {
    allLangs,
    selectedLang,
    translations,
    isLoading,
    setLanguage,
    initializeLanguages,
    multipleLangsEnabled,
    pollKey,
    pollId,
    t,
    getQuestionTranslation,
    getInterBlockTranslation,
    getQuestionTranslationById,
  }
})
