import fetchJsonp from 'fetch-jsonp'

/**
 * Get the base URL for the assets
 * @returns {string} The base URL for the assets
 */
export function getBaseAssetsUrl() {
  const isDev = import.meta.env.MODE === 'development' || import.meta.env.MODE === 'test' || import.meta.env.MODE === 'development-local'
  const protocol = window.location.protocol
  let host = window.location.host

  if (host.endsWith('/')) {
    host = host.slice(0, -1)
  }

  return isDev ? import.meta.env.VITE_ROOT_URL : `${protocol}//${host}`
}

/**
 * Get the base URL for the API
 * @returns {string} The base URL for the API
 */
export function getBaseApiUrl() {
  const isDev = import.meta.env.MODE === 'development' || import.meta.env.MODE === 'test' || import.meta.env.MODE === 'development-local'

  if (isDev) {
    return import.meta.env.VITE_ROOT_URL
  }

  const protocol = window.location.protocol
  const host = window.location.host

  return `${protocol}//${host}`
}

/**
 *  Получение адресов по запросу из API КЛАДР
 * @param {string} query - Запрос для поиска адресов
 * @param {object} options - Опции для запроса
 * @param {string} options.regionId - Идентификатор региона
 * @param {string} options.cityId - Идентификатор города
 * @param {string} options.districtId - Идентификатор района
 * @param {string} options.streetId - Идентификатор улицы
 * @param {number} options.limit - Максимальное количество результатов
 * @param {number} options.oneString - Флаг, указывающий, нужно ли возвращать одну строку
 * @returns {Promise<Array>} - Массив адресов
 */
export async function fetchAddress(query, options = {}) {
  const {
    regionId = '',
    cityId = '',
    districtId = '',
    streetId = '',
    limit = 30,
    oneString = 1,
  } = options

  const params = new URLSearchParams({
    query,
    oneString: oneString.toString(),
    regionId,
    cityId,
    districtId,
    streetId,
    limit: limit.toString(),
  })

  try {
    const response = await fetchJsonp(`https://kladr-api.ru/api.php?${params.toString()}`)
    const data = await response.json()

    if (data && data.result) {
      return data.result.map(item => ({
        type: 0,
        id: item.id,
        name: item.fullName,
      }))
    }

    return []
  }
  catch (error) {
    console.error('Error fetching address suggestions:', error)
    return []
  }
}
