body.tablet-mode {
  /* Check Component Styles */
  .fc-check__label-text {
    font-size: var(--tablet-checkbox-radio-text-size);
    line-height: 1.2;
  }

  .fc-check__box,
  .fc-check--radio .fc-check__box {
    width: 34px;
    height: 34px;
    margin-right: 20px;
  }

  .fc-check--radio .fc-check__marker {
    width: 12px;
    height: 12px;
  }

  .fc-check--checkbox .fc-check__box {
    margin-right: 15px;
  }

  .fc-check--checkbox .fc-check__marker {
    width: 17px;
    height: 12px;
  }

  .fc-check--intermediate .fc-check__marker {
    width: 12px;
    height: 12px;
    border-radius: 3px;
  }

  .fc-check__label {
    padding-top: 4px;
  }

  .fc-check--with-hint .fc-check__label {
    flex-grow: 1;
    padding-top: 4px;
    position: static;
    &-text {
      overflow: visible;
      position: static;
      padding-right: 40px;
    }
    .fc-check__hint {
      margin-left: auto;
      margin-top: 0;
      padding-top: 0;
      position: absolute;
      z-index: 2;
      top: 0;
      width: 60px;
      right: -19px;
      height: calc(100%);
      text-align: center;
      margin-top: -2px;
      .hint-trigger {
        width: 100% !important;
        height: 100% !important;
        display: flex;
        align-items: center;
        justify-content: center;
        svg {
          width: 21px !important;
          height: 23px !important;
        }
        &:before {
          content: '';
          position: absolute;
          z-index: 2;
          top: -10px;
          height: calc(100% + 22px);
          width: 100%;
        }
      }
    }
  }

  /* File check styles */
  .file-check {
    border-width: 2px;
    .fc-check {
      padding-top: 13px;
      padding-left: 13px;
    }

    &__label {
      padding: 15px 10px;
      font-size: 22px;
      line-height: 1.1;
    }

    &__hint {
      vertical-align: middle;
      top: -2px;
    }

    &--no-label .file-check__hint {
      width: 21px !important;
      height: 23px !important;
    }

    &__play-icon {
      width: 50% !important;
      height: 50% !important;
      svg {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  /* Dialog Hint Content styles */
  .dialog-hint-content {
    max-width: 460px;
    padding: 20px;

    &__inner {
      gap: 10px;
    }

    &__text {
      font-size: 19px;
      line-height: 1.3;
    }

    &__close {
      font-size: 22px;
      line-height: 1.1;
    }
  }

  /* Hint trigger styles */
  .hint-trigger {
    width: 21px !important;
    height: 23px !important;
    svg {
      width: 100% !important;
      height: auto !important;
    }
  }

  /* Textarea Styles */
  .form-control {
    font-size: var(--tablet-label-font-size);
    padding: 18px 20px;
    border-radius: 6px;
    min-height: 100px;
    line-height: 1.1;

    &::placeholder {
      font-size: var(--tablet-label-font-size);
    }

    &.adaptive-height {
      max-height: 300px;
    }
  }

  /* Form Group Styles */
  .form-group__error,
  .validationMessage {
    font-size: var(--tablet-error-text-size);
  }

  .validationMessage--filled {
    padding: 14px 20px;
    font-size: 19px;
    line-height: 1.3;
  }

  .form-group__label {
    font-size: var(--tablet-label-font-size);
  }

  /* User Consent Banner Styles */
  .user-consent {
    &__text {
      font-size: 19px;
      line-height: 1.2;
    }

    &__inner {
      padding: 12px 20px;
    }

    &__close {
      width: 60px;
    }

    &__close-icon svg {
      width: 14px;
      height: 14px;
    }
  }

  /* Input Styles */
  .fc-input {
    &__wrapper {
      height: 60px;
      border-radius: 6px;
      padding: 0 15px 0 20px;
    }

    &__field {
      font-size: var(--tablet-label-font-size);
      height: 60px;

      &::placeholder {
        font-size: var(--tablet-label-font-size);
      }
    }

    &__icon {
      margin-right: 18px;
    }

    &__icon-button {
      width: 50px;
      .fc-icon {
        width: 28px !important;
        height: 29px !important;
      }

      svg {
        width: 100% !important;
        height: auto !important;
      }
    }

    &__count {
      font-size: 14px;
    }
  }

  /* Dialog Styles */
  .DialogContent.date-picker-dialog-content {
    padding: 0;
    max-width: 620px;
    height: auto;
    width: 100%;
    min-height: 672px;
    height: auto;
    display: flex;
    flex-direction: column;
  }

  /* Calendar Styles */
  .Calendar {
    height: 100%;
    width: 100%;
    flex-grow: 1;
    padding: 30px 40px;
    gap: 25px;
    display: flex;
    flex-direction: column;
    .select-trigger__label {
      font-size: 30px;
    }
    &__MonthSelect {
      flex-grow: 1;
      max-width: 100%;
    }
    &__YearSelect {
      flex-grow: 1;
      width: 150px;
      margin-left: 20px;
    }
  }

  .CalendarGridRow {
    height: 70px;
  }

  .CalendarCellTrigger {
    height: 100%;
  }

  .CalendarWrapper {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .date-picker__calendar-footer {
    margin-top: auto;
  }

  .CalendarCellTrigger {
    font-size: 30px;
  }

  /* Select Component Styles */
  .select-trigger {
    &:not(.country-select-trigger--in-header) {
      min-height: 55px;
      font-size: 22px;
      padding-left: 20px;
      padding-right: 20px;
      .select-trigger__icon {
        width: 21px !important;
        height: 12px !important;
        svg {
          width: 100% !important;
          height: auto !important;
        }
      }

      .select-trigger__clear {
        margin-top: -1px;
        margin-right: 6px;
      }
      .select-trigger__clear .select-trigger__icon svg {
        width: 15px !important;
        height: 15px !important;
      }
    }

    &__label {
      font-size: 22px;
    }

    &__label-description {
      font-size: 19px;
    }

    &--multiple {
      padding-top: 18px;
      padding-bottom: 18px;
      .select-trigger__label,
      .select-trigger__label--placeholder {
        font-size: 22px;
        line-height: 1.1;
      }

      .select-trigger__labels {
        gap: 10px;
      }
    }
  }

  .select-item {
    &__label-text {
      font-size: 22px !important;
    }

    &__label-description {
      font-size: 19px !important;
    }
  }

  .command-content {
    padding-top: 25px;

    .simplebar-content {
      margin-top: -15px;
    }

    .country-option {
      gap: 15px;

      &__name {
        font-size: 22px !important;
      }
    }

    &__header {
      margin-top: 5px;
      margin-bottom: 25px !important;
    }

    &__empty {
      padding-left: 20px;
      font-size: 22px;
      margin-top: 0;
    }
  }

  .command-item {
    padding-left: 30px;
    padding-right: 30px;
    padding-top: 15px;
    padding-bottom: 15px;

    &__label-text {
      font-size: 22px;
    }
    .fc-check__label {
      padding-top: 5px;
    }
  }

  /* Country select specific styles */
  .country-select-trigger--in-header {
    /* Override the min-height to keep it small */
    min-height: 30px;
  }

  .country-select-trigger:not(.country-select-trigger--in-header) {
    min-height: 55px;
    max-width: 400px;
    min-width: 200px;
    font-size: 22px;
    line-height: 1.1;

    .country-selected-item__name {
      /* Keep the original compact size for the trigger */
      font-size: 22px !important;
      line-height: 1.1;
    }

    .select-trigger__label {
      padding-bottom: 11px;
    }
  }

  /* Dialog close buttons */
  .close-button {
    height: 58px;

    svg {
      width: 20px;
      height: 20px;
    }

    &--close svg {
      width: 24px;
    }
  }

  /* Gallery Styles */
  .gallery-container {
    margin-top: -20px;
    margin-bottom: 40px;
  }

  .gallery {
    /* When there's only one item */
    &--single swiper-slide {
      width: 580px !important;
      margin: 0 auto !important;
    }

    /* Override breakpoints for tablet view */
    swiper-container {
      --swiper-breakpoints-679-slides-per-view: 3 !important;
    }

    &__button-prev,
    &__button-next {
      width: 47px;
      height: 47px;

      svg {
        width: 24px;
        height: 17px;
      }
    }

    &__button-prev {
      left: -62px;
    }

    &__button-next {
      right: -62px;
    }

    &-item__label {
      font-size: 19px;
      line-height: 1.1;
      margin-top: 15px;
    }

    &-item__play-icon {
      width: 50% !important;
      height: 50% !important;
      svg {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  /* VariantsGallery Styles */
  .variants-gallery {
    --variants-gallery-item-width: 150px !important;
    --variants-gallery-item-height: 150px !important;
    --variants-gallery-button-width: 47px !important;
    --variants-gallery-button-height: 47px !important;
    &__slide {
      max-width: var(--variants-gallery-item-width) !important;
    }

    &-item__check {
      z-index: 11;
    }

    &-item__check label {
      padding-top: 10px !important;
      padding-left: 10px !important;
    }

    .swiper-container {
      --swiper-space-between: 25px !important;
    }

    &-item__label {
      font-size: 16px;
      line-height: 1.1;
      padding-top: 10px;
      text-align: center;
      width: 100%;
    }

    &-item__icon svg {
      width: 80px;
      height: 80px;
    }

    &__button-prev,
    &__button-next {
      width: var(--variants-gallery-button-width);
      height: var(--variants-gallery-button-height);

      svg {
        width: 24px;
        height: 17px;
      }
    }

    &__button-prev {
      left: -62px;
    }

    &__button-next {
      right: -62px;
    }
  }

  /* Extra Variants Styles */
  .question-variants {
    &__with-files {
      gap: 20px;
    }

    &__with-files--gallery {
      gap: 0;
      .question-variants__self-variant-comment {
        padding-top: 20px;
      }
    }

    &__label {
      font-size: 22px;
      margin-bottom: 20px;
    }

    .check-group {
      gap: 5px;
    }

    /* Regular variants (non-file) */
    .check-group__item {
      position: relative;
      padding: 13px;
    }

    .check-group__item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 2px solid var(--fqz-poll-text-on-place);
      border-radius: 8px;
      opacity: 0.15;
      pointer-events: none;
      transition:
        border-color 0.3s,
        opacity 0.3s;
    }

    .check-group__item label {
      position: relative;
    }

    .check-group__item label::before {
      content: '';
      position: absolute;
      top: -13px;
      left: -13px;
      width: calc(100% + 26px);
      height: calc(100% + 26px);
      z-index: 2;
    }

    .check-group__item:has(.fc-check--checked) {
      &::before {
        border-color: var(--fqz-poll-main-color);
        opacity: 1;
      }
    }

    &__self-variant-comment {
      padding-top: 5px;
    }

    &__self-variant-wrapper .question-variants__self-variant-comment {
      padding-top: 10px;
    }
  }

  /* Variant attachments block */
  .question-variants__file-attachment {
    padding-top: 30px;
  }

  .file-attachment__description {
    font-size: 22px;
    line-height: 1.3;
    margin-bottom: 24px;
  }

  /* Comment field top margin */
  .survey-questions__comment-form-group {
    padding-top: 40px;
    margin-top: 0;
    /* Comment variant error styles */
    .comment-required-variants-error-item {
      font-size: 19px;
      line-height: 1.3;
      margin-top: 5px;
    }
  }

  /* Extra variants in NPS questions */
  .survey-questions__variants-form-group {
    padding-top: 40px;
    .question-variants__label {
      font-size: 22px;
      margin-bottom: 20px;
    }
  }

  .survey-questions__variants-form-group-container {
    padding-top: 0;
  }

  .skip-container {
    margin-top: 40px;
  }

  /* Copy Promocode Styles */
  .copy-promocode {
    min-height: 55px;
    max-height: none;
    height: auto;
    font-size: 22px;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 6px;
    gap: 15px;

    &__text {
      font-size: 22px !important;
      line-height: 1.1 !important;
      padding-bottom: 3px;
    }

    &__button {
      padding-top: 12px;
      padding-bottom: 12px;
    }

    &__button-icon svg {
      width: 22px;
      height: 24px;
    }

    &__button:hover .copy-promocode__button-icon {
      /* Disable hover effect on tablet */
      opacity: 0.4;
      transform: scale(1);
    }
  }

  /* Cover Banner Styles */
  .cover-banner {
    width: 100%;
    max-width: 100%; /* Ensure full width */
    margin-top: 0 !important; /* Override any margin */
    height: 240px;
    border-radius: 0; /* Remove any border radius */
    overflow: hidden; /* Keep overflow hidden */

    &__img {
      width: 100%;
      height: 100%;
      object-fit: cover; /* Ensure image covers the area */
    }
  }

  @media (max-width: 1023px) {
    .gallery .gallery__button-prev {
      left: -20px !important;
    }
    .gallery .gallery__button-next {
      right: -20px !important;
    }
  }
}
