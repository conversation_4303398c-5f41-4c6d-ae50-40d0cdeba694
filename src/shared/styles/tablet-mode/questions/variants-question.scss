/* Tablet Mode Styles for Variants Question */
body.tablet-mode {
  /* Variants container styles */
  .question-variants {
    &__error {
      margin-top: 30px;
    }

    &__file-check-list {
      gap: 20px;
    }

    &__file-check-list-item {
      flex: 0 1 calc(50% - 10px);
    }

    .check-group__item {
      position: relative;
      padding: 13px;
    }

    .check-group__item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 2px solid var(--fqz-poll-text-on-place);
      border-radius: 8px;
      opacity: 0.15;
      pointer-events: none;
    }

    .check-group__item label {
      position: relative;
    }

    .check-group__item label::before {
      content: '';
      position: absolute;
      top: -13px;
      left: -13px;
      width: calc(100% + 26px);
      height: calc(100% + 26px);
      z-index: 2;
    }
  }
}
