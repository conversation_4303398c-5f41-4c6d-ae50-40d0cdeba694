body.tablet-mode {
  .media-variants-question {
    /* Styles for the gallery used within MediaVariantsQuestion */
    .gallery {
      .gallery-item {
        &__select {
          top: 15px;
          right: 15px;
        }
        &__select-button {
          padding: 4px 25px;
          min-height: 48px;
          font-size: 19px;
          line-height: 1;
        }

        &__label {
          margin-top: 15px;
          font-size: 19px;
          line-height: 1.1;
        }

        &__selected-check {
          top: 15px;
          right: 15px;
          width: 48px;
          height: 48px;
          border-width: 3px;
          svg {
            width: 20px;
            height: 13px;
          }
        }
      }
    }

    /* Adjust error message margin if needed */
    .question-gallery-error {
      margin-top: 30px; /* Match gallery-question style */
    }
  }
}
