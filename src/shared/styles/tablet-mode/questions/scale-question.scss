body.tablet-mode {
  /* Scale Question Styles */
  .scale-container {
    gap: 30px; /* Gap between scale and input */
    padding-top: 26px;
  }

  .scale-slider {
    .slider-track {
      height: 12px; /* Scale height (slider bar) */
    }

    .slider-thumb {
      width: 48px;
      height: 48px;
    }

    .slider-value {
      font-size: 22px;
      line-height: 1.2;
      top: auto;
      bottom: calc(100% + 10px);
    }
  }

  .scale-input {
    width: 100px;
    height: 60px;
  }

  /* Scale Variants Styles */
  .scale-items-container {
    gap: 20px; /* Gap between items */
  }

  .scale-item {
    padding-bottom: 20px;

    &__label {
      font-size: 22px;
      line-height: 1.1;
      margin-bottom: 20px;
    }
  }

  .variant-skip-container {
    margin-top: 30px;
  }
}
