body.tablet-mode {
  .matrix-3d-question {
    --variant-cell-padding-y: 30px;

    /* Override select trigger font size specifically for 3D matrix */
    .select-trigger__label,
    .select-trigger__label--placeholder {
      font-size: 16px;
      line-height: 1.1;
    }

    &__row-title,
    &__column-title {
      font-size: 16px;
      line-height: 1.2;
    }

    &__row-title {
      width: 229px;
      padding-top: var(--variant-cell-padding-y);
      padding-bottom: var(--variant-cell-padding-y);
      padding-right: 20px;
    }

    &__variant-cell-row-name {
      font-size: 19px;
      line-height: 1.1;
      margin-bottom: 30px;
    }

    &__column-names {
      gap: 15px;
    }

    &__variant-cell-list {
      gap: 20px;
      width: 100% !important;
    }

    &__variants-row {
      padding-top: var(--variant-cell-padding-y);
      padding-bottom: var(--variant-cell-padding-y);
    }

    &__skip-row {
      margin-top: 30px;
    }

    &__comment-form-group {
      margin-top: 39px;
    }

    &__error {
      margin-top: 30px !important;
    }

    &--forbid-scroll + div > .matrix-3d-question__error {
      margin-top: 19px !important;
    }

    &__row-placeholder-text {
      font-size: 16px;
      line-height: 1.2;
    }

    &__skip-column,
    &__skip-row {
      .fc-check__label-text {
        font-size: 16px;
        line-height: 1.2;
      }

      .fc-check__label {
        padding-top: 7px;
      }
    }
  }
}
