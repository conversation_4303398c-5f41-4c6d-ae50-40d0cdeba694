body.tablet-mode {
  /* Diff Question Container */
  .diff-question-form-group {
    gap: 30px;
  }

  .diff-question-container--circle .diff-question-form-group {
    gap: 30px;
  }

  .question-diff-error {
    margin-top: 0px;
  }

  /* Diff Question Scale */
  .diff-question-scale {
    flex-wrap: wrap;
    gap: 15px;
    justify-content: space-between;

    &:before {
      top: -19px;
      height: calc(100% + 29px);
    }
  }

  /* Diff Question Buttons */
  .diff-question-buttons {
    width: 100%;
    gap: 10px;
  }

  /* Diff Question Button */
  .diff-question-button {
    height: 58px;
    flex-grow: 1;
    font-size: 22px;
  }

  /* Diff Question Labels */
  .diff-question-label {
    font-size: 22px;
    line-height: 1.1;
    width: 412px;
  }

  .diff-question-label--start,
  .diff-question-label--end {
    order: -1;
  }

  .diff-question-label--start {
    text-align: left !important;
    align-self: flex-start;
    margin-right: auto;
  }

  .diff-question-label--end {
    align-self: flex-end;
    text-align: right !important;
    margin-left: auto;
  }

  /* Circle View Specific Styles */
  .diff-question-scale--circle {
    .diff-question-buttons {
      justify-content: space-between !important;
      flex-basis: 100% !important;
    }

    .diff-question-button {
      flex: 0 0 auto;
      min-height: 0;
      align-self: center;
      border-width: 6px !important;

      &:nth-child(1),
      &:nth-child(5) {
        width: 144px !important;
        height: 144px !important;
      }

      &:nth-child(2),
      &:nth-child(4) {
        width: 108px !important;
        height: 108px !important;
      }

      &:nth-child(3) {
        width: 90px !important;
        height: 90px !important;
      }

      .diff-question-button__icon {
        width: 38px !important;
        height: 25px !important;
      }
    }
  }

  @media (max-width: 1024px) {
    .diff-question-label {
      width: 48% !important;
    }
  }
}
