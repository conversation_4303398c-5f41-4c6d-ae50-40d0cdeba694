body.tablet-mode {
  .file-previews {
    gap: 25px;
  }

  .file-button {
    width: 150px;
    height: 150px;
    padding-left: 15px;
    padding-right: 15px;
    gap: 10px;
    border-radius: 16px;

    svg {
      width: 26px;
      height: 27px;
    }

    &__text {
      font-size: 16px;
      line-height: 1;
      margin-top: 0;
    }
  }

  .file-preview {
    width: 150px;

    &__uploading {
      border-radius: 16px;
    }

    &__trigger {
      width: 150px;
      height: 150px;
      border-radius: 16px;
    }

    &__icon {
      svg {
        width: 69px;
        height: 69px;
      }
    }

    &__close {
      width: 30px;
      height: 30px;

      svg {
        width: 11px;
        height: 11px;
      }
    }

    &__name {
      font-size: 16px;
      line-height: 1.1;
      margin-top: 10px;
    }
  }

  .file-preview__uploading {
    width: 150px;
    height: 150px;

    .spinner {
      width: 36px !important;
      height: 36px !important;
    }
  }

  .file-preview__placeholder,
  .file-preview__placeholder-not-supported {
    svg {
      width: 69px;
      height: 69px;
    }
  }

  .file-dropzone {
    .file-dropzone__error {
      margin-top: -5px !important;
    }

    .file-dropzone__error--too-large {
      margin-top: 30px !important;
    }
  }
}
