/* Date Question Styles */
body.tablet-mode {
  /* Common date question styles */
  .date-question__date-form-group,
  .date-question__time-form-group {
    max-width: var(--tablet-input-container-max-width);
  }

  .date-question__date-month-form-group {
    max-width: 520px;
  }

  /* Date Month container styles */
  .date-month-mask-container {
    gap: 20px;
    max-width: 100%;

    .date-month-mask-container__day {
      width: 100px;
    }

    .select-trigger {
      max-width: 100%;
    }
  }

  /* DateTime container styles */
  .date-datetime-mask-container {
    gap: 30px;

    .masked-field__input {
      max-width: 100%;
    }

    .form-group {
      flex-grow: 1;
    }
  }

  /* DateMonthTime container styles */
  .date-datemonthtime-mask-container {
    gap: 30px;

    &__date-month {
      gap: 20px;
      flex-grow: 1;
    }

    &__day-form-group-inner {
      gap: 20px;
      .select-trigger {
        max-width: 100%;
      }
    }

    &__time-form-group {
      flex-grow: 1;
    }
    .masked-field__input {
      max-width: 100%;
    }
  }

  /* Adjust day input for all date types */
  .date-month-mask-container__day {
    width: 100px;
    text-align: center;
  }

  /* Time input adjustments */
  .date-question__time-input {
    width: 100%;
  }
}
