body.tablet-mode {
  /* Smile Rating Styles */
  .smile-rating {
    /* 5 smile items configuration */
    &[data-icons='5'] {
      --fc-rating-item-size: 155px;
      --fc-rating-icon-size: 120px;
      --fc-rating-item-gap: 10px;
    }

    /* 3 and 2 smile items configuration */
    &[data-icons='3'],
    &[data-icons='2'] {
      --fc-rating-item-size: 180px;
      --fc-rating-icon-size: 180px;
      --fc-rating-item-gap: 30px;
    }

    /* Common styles for all smile ratings */
    .smile-rating-item__label {
      font-size: 19px;
      line-height: 1.1;
      margin-top: 30px;
    }

    /* Remove scaling for active items to match NPS behavior */
    &--inited .smile-rating-item.active .smile-rating-item__icon {
      transform: none;
    }
  }

  /* Smile Rating Container Styles */
  .smile-rating-container {
    display: flex;
    justify-content: center;
  }

  /* Smile Rating Message Styles */
  .smile-rating__message-container {
    margin-top: 30px;
  }

  .smile-rating__message {
    font-size: 22px;
    line-height: 1.1;
  }

  .smile-rating[data-type='like'] .smile-rating-item__icon {
    margin-bottom: -22px;
  }

  @media (max-width: 1023px) {
    .smile-rating {
      /* 5 smile items configuration */
      &[data-icons='5'] {
        --fc-rating-item-size: 121px;
        --fc-rating-icon-size: 120px;
        --fc-rating-item-gap: 10px;
      }
    }
  }
}
