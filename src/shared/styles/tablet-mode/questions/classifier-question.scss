/* Tablet Mode Styles for Classifier Question */
body.tablet-mode {
  .classifier-question {
    &--list,
    &--multiple {
      display: flex;
      flex-direction: column;
    }
    /* Styles for tree/list nodes */
    .classifier-tree-node {
      position: relative;
      width: 100%; /* Ensure node takes full width */
      box-sizing: border-box;
      padding-bottom: 5px;

      &:last-child {
        padding-bottom: 0;
      }

      /* Adjust content alignment and spacing */
      &__content {
        align-items: center;
        position: relative; /* Needed for absolute positioning of children */
        padding: 13px;
        min-height: 60px;
        padding-right: 60px;
        /* Border similar to variants */
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border: 2px solid var(--fqz-poll-text-on-place);
          border-radius: 8px;
          opacity: 0.15;
          pointer-events: none;
          transition:
            border-color 0.3s,
            opacity 0.3s;
        }

        &:has(.fc-check--checked)::before {
          border-color: var(--fqz-poll-main-color);
          opacity: 1;
        }

        &:has(.hint-trigger) {
          padding-right: 100px;
        }
      }

      /* Text adjustments */
      &__text {
        font-size: var(--tablet-checkbox-radio-text-size);
        line-height: 1.2;
        flex-grow: 0;
      }

      &:not(.classifier-tree-node--selectable-categories) {
        .fc-check {
          width: 100%;
        }
      }

      /* Hint trigger positioning */
      &__hint-trigger {
        position: absolute;
        right: 38px; /* Position near the toggle button */
        width: 56px !important;
        height: 100% !important;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 0;
        z-index: 2; /* Ensure it's clickable */
        margin-top: -1px;
        svg {
          width: 22px !important;
          height: 22px !important;
        }
      }

      /* Toggle button positioning */
      &__toggle {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 0;
        width: 62px; /* Keep original width */
        height: 100%; /* Make it cover the height */
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 4; /* Ensure it's clickable */

        &--open {
          opacity: 0.4;
        }

        .fc-icon {
          width: 20px !important; /* Adjust icon size if needed */
          height: 11px !important;
          svg {
            width: 100%;
            height: auto;
            display: block;
          }
        }
      }

      /* Selected indicator (single choice, non-multiple) */
      &__indicator {
        position: static; /* Reset absolute positioning */
        margin-left: 10px; /* Space from text */
        order: 1; /* Place it after the text */
        width: 24px;
        height: 24px;
        flex-shrink: 0;

        svg {
          width: 11px;
          height: 8px;
        }
      }

      /* Count indicator (multiple choice) */
      &__count {
        position: static; /* Reset absolute positioning */
        margin-left: 10px; /* Space from text */
        order: 1; /* Place it after the text */
        font-size: var(--tablet-checkbox-radio-text-size);
        line-height: 1.2;
        flex-shrink: 0;
      }

      /* Adjust children indentation */
      &__children {
        margin-left: 0; /* Remove default indentation */
        padding-left: 20px; /* Add padding for visual hierarchy */
      }

      &__children-inner {
        padding-top: 5px;
      }

      .fc-check label {
        z-index: 1;
        position: relative;
      }

      /* Ensure label covers the area for clicking */
      .fc-check label::before {
        content: '';
        position: absolute;
        top: -13px;
        left: -13px;
        width: calc(100% + 26px);
        height: calc(100% + 26px);
      }

      /* Adjustments for list type */
      &--list {
        padding-bottom: 10px; /* Adjust spacing */
        &:last-of-type {
          padding-bottom: 0;
        }
      }

      &--leaf {
        .fc-check {
          width: 100%;
        }
        .classifier-tree-node__content {
          padding-right: 13px;
        }

        &:has(.hint-trigger) {
          .classifier-tree-node__content {
            padding-right: 13px;
          }
        }

        .classifier-tree-node__hint-trigger {
          right: 0 !important;
        }
      }

      &:not(.classifier-tree-node--leaf) > .classifier-tree-node__content {
        .hint-trigger {
          width: 23px !important;
          right: 54px !important;
          z-index: 6;
        }
      }

      &--disabled {
        .classifier-tree-node__content::before {
          opacity: 0.1;
        }
      }
    }

    /* Dropdown styles (ClassifierSelectTree) */
    .classifier-select-tree-list {
      gap: 20px; /* Increase gap between dropdowns */

      &__item-content {
        padding-top: 0; /* Remove default padding */
      }

      &__buttons {
        margin-top: 20px; /* Increase spacing */
        gap: 15px;
      }

      &__button {
        height: 48px; /* Increase button height */
        padding: 0 20px;
        font-size: 16px; /* Adjust font size */
        line-height: 1.3;
        gap: 14px;

        &-icon {
          margin-top: 0;
        }
        &-icon svg {
          width: 15px; /* Adjust icon size */
          height: 15px;
        }
      }

      .child-select {
        padding-left: 20px;
      }
    }

    /* Error message */
    &__error {
      margin-top: 30px;
    }
  }
}
