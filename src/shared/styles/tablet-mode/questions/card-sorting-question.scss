/* Tablet Mode Styles for Card Sorting Question */
body.tablet-mode {
  .card-sorting-question {
    /* 1. Main headers */
    &__header {
      font-size: 22px;
      line-height: 1.1;
      margin-bottom: 20px;
    }

    /* 2. Category headers */
    &__category-header {
      font-size: 22px;
      font-weight: 400;
      line-height: 1.1;
      margin-bottom: 15px;
    }

    /* 3. Category placeholders (dotted borders) */
    &__col .card-sorting-question__items {
      min-height: 64px; /* Ensure minimum height for placeholder */
    }

    /* 4. Gap between categories */
    &__category + .card-sorting-question__category {
      margin-top: 20px !important;
    }

    /* Additional adjustments for tablet */
    &__grid {
      gap: 30px; /* Increase gap between columns */
    }

    &__items {
      gap: 5px; /* Increase gap between draggable items */
      .priority-item__content {
        padding-left: 20px;
      }
      .priority-item__card-sorting-icon {
        margin-right: 8px !important;
      }
    }

    &__comment {
      margin-top: 25px; /* Increase top margin for comment/skip section */
      padding: 30px 40px; /* Adjust padding */

      .skip-container {
        margin-top: 0;
      }
    }

    &__comment-form-group {
      padding-bottom: 40px;
    }
  }
}
