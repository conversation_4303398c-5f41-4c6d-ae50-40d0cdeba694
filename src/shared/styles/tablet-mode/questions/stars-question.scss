body.tablet-mode {
  .fc-star-rating {
    /* Override default width calculation as spacing is variable now */
    width: auto;

    /* Default styles for numbers above stars */
    &__index {
      font-size: 22px;
      line-height: 1.2;
      margin-bottom: 30px;
    }

    /* Default styles for labels below each star */
    &__label {
      font-size: 19px !important;
      line-height: 1.1;
      margin-top: 30px;
    }

    /* Size specific overrides */
    &[data-size='sm'],
    &--small-on-mobile {
      --fc-star-rating-star-size: 64px !important;
      --fc-star-rating-spacing: 20px !important;
    }

    &[data-size='md']:not(.fc-star-rating--small-on-mobile) {
      --fc-star-rating-star-size: 96px;
      --fc-star-rating-spacing: 30px;

      /* With labels specific styles */
      &.fc-star-rating--labels {
        .fc-star-rating__item {
          width: 155px; /* Adjust container width */
        }
      }
    }

    &[data-size='lg'] {
      --fc-star-rating-star-size: 128px;
      --fc-star-rating-spacing: 30px;

      /* With labels specific styles */
      &.fc-star-rating--labels {
        .fc-star-rating__item {
          width: 155px; /* Adjust container width */
        }
      }
    }

    &[data-size='lg'][data-count='2'],
    &[data-size='lg'][data-count='3'] {
      --fc-star-rating-star-size: 160px !important;
      --fc-star-rating-spacing: 30px !important;
      &.fc-star-rating--labels {
        .fc-star-rating__item {
          width: 160px; /* Adjust container width */
        }
      }
    }

    /* Adjust spacing between items based on variable */
    .fc-star-rating__item:not(:last-child) {
      margin-right: var(--fc-star-rating-spacing);
    }

    .fc-star-rating__wrapper {
      justify-content: center; /* Center items if width is auto */
    }
  }

  .fc-star-rating__message-container {
    margin-top: 30px;
  }

  .fc-star-rating__message {
    font-size: 22px;
    line-height: 1;
  }

  .fc-star-rating__item .star-outline path {
    stroke-width: 3px;
  }

  .fc-star-rating__item .star-fill path {
    stroke-width: 0px !important;
  }

  @media screen and (max-width: 910px) {
    .fc-star-rating--small-on-mobile {
      --fc-star-rating-spacing: 0 !important;
      justify-content: space-between;
      width: 100%;
    }
  }
}
