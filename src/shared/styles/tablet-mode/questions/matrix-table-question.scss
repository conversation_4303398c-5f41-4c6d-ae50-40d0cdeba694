body.tablet-mode {
  .matrix-table-question {
    --variant-cell-padding-y: 30px;

    &__row-title,
    &__column-title {
      font-size: 16px;
      line-height: 1.2;
    }

    &__row-title {
      width: 209px;
      padding-top: var(--variant-cell-padding-y);
      padding-bottom: var(--variant-cell-padding-y);
      padding-right: 20px;
    }

    &__column-names {
      gap: 20px;
    }

    &__variant-cell-list {
      gap: 20px;
      width: 100% !important;
    }

    &__variant-cell-input {
      .fc-check__box {
        margin-right: 0;
      }
    }

    &__variants-row {
      padding-top: var(--variant-cell-padding-y);
      padding-bottom: var(--variant-cell-padding-y);
    }

    &__skip-row {
      margin-top: 30px;

      label {
        align-items: center;
      }

      .fc-check__label {
        padding-top: 0;
      }

      .fc-check__label-text {
        font-size: 16px !important;
      }
    }

    .question-variants__label {
      font-size: 16px !important;
    }
    .fc-check__label-text {
      font-size: 16px !important;
    }

    .fc-check__box {
      margin-right: 15px !important;
    }
    .fc-check label {
      align-items: center;
    }
    .fc-check__label {
      padding-top: 0 !important;
    }

    &--skip--tablemod {
      .fc-check__label {
        padding-top: 0 !important;
      }

      .fc-check__label-text {
        font-size: 16px !important;
      }
      label {
        align-items: center;
      }
    }
    &__row--padding-min {
      margin-bottom: 13px;
    }

    &__variants-input {
      margin-top: 30px;
    }
  }
}
