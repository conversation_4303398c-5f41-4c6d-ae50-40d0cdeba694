/* Base styles for tablet mode */
body.tablet-mode {
  --tablet-fixed-width: 768px;
  --tablet-input-container-max-width: 400px;

  /* Font sizes as specified in requirements */
  --tablet-label-font-size: 22px;
  --tablet-checkbox-radio-text-size: 22px;
  --tablet-smiley-label-size: 22px;
  --tablet-nps-number-size: 22px;
  --tablet-rating-number-size: 22px;
  --tablet-semantic-diff-size: 22px;
  --tablet-scale-slider-number-size: 22px;
  --tablet-stars-number-size: 22px;
  --tablet-stars-smiley-label-size: 19px;
  --tablet-matrix-cell-label-size: 16px;
  --tablet-hint-size: 19px;
  --tablet-error-text-size: 19px;
  --tablet-photo-caption-size: 19px;
  --tablet-question-text-size: 32px;
  --tablet-additional-description-size: 16px;
  --tablet-optional-label-size: 12px;
  --tablet-dropdown-text-size: 22px;
  --tablet-intermediate-screen-text-size: 22px;

  /* Apply tablet mode styles to specific elements */
  .app-root {
    margin: 0 auto;
    /* Ensure background stretches full width */
    background-size: cover;
    background-position: center;
  }

  /* Questions page main place block - full width with specific padding */
  .questions-page {
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 50px;

    &__item-content {
      max-width: 100%;
      padding: 50px 85px;
    }
    &__item-content--priority-question {
      margin-top: 0;
      padding-top: 25px;
    }
    &__item-content--inter-block {
      padding-left: 85px;
      padding-right: 85px;
    }

    &--single-question-inter-block {
      padding-bottom: 0;
    }

    &__item-content--inter-block {
      padding-bottom: 0;
      .survey-interscreen__wrapper {
        padding-bottom: 15px;
      }
    }
  }

  /* Page Footer styles */
  .survey-footer {
    z-index: initial;
    &__copyright {
      position: relative;
      z-index: 20;
    }
  }

  /* Page Header Styles */
  .page-header {
    max-width: 100%;
    padding-left: 85px;
    padding-right: 85px;

    &__description {
      font-size: var(--tablet-question-text-size);
      line-height: 1.2;

      p > * {
        font-size: var(--tablet-question-text-size) !important;
      }
    }

    &__subdescription {
      font-size: var(--tablet-additional-description-size);
      margin-top: 15px;

      p > * {
        font-size: var(--tablet-additional-description-size) !important;
      }
    }

    &__unrequired {
      font-size: var(--tablet-optional-label-size);
      margin-top: 15px;

      & > * {
        font-size: var(--tablet-optional-label-size) !important;
      }
    }
  }

  /* Header Styles */
  .survey-header__aside {
    gap: 10px;
  }

  /* Pagination adjustments when used with cover banner */
  .app-root:has(.app-root__cover-banner) .app-root-container__paginator {
    margin-top: 13px;
  }

  /* Next/Back buttons - fixed at bottom */
  .survey-actions {
    position: fixed !important;
    bottom: 0;
    z-index: 100;
    padding: 0;
    display: flex;
    justify-content: space-between;
    margin-bottom: 0 !important;

    &.sticky {
      padding: 0;
    }

    &--tablet {
      background-color: transparent !important;

      &.sticky {
        background-color: transparent !important;
      }
    }

    &__container {
      width: 100%;
      max-width: 100%;
      margin-left: 0;
      margin-right: 0;
      padding: 0;
    }

    &__main-buttons {
      max-width: 100%;

      .tablet-button-container--next {
        margin-left: auto;
      }
    }
  }

  @media (max-width: 1023px) {
    .questions-page {
      &__item-content {
        padding-left: 40px;
        padding-right: 40px;
      }
    }
  }
}
