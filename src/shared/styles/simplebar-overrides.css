.simplebar-custom-fullpage .simplebar-vertical.fz-simplebar-track {
  z-index: 33;
}

.simplebar-custom-fullpage .fz-simplebar-content,
.simplebar-custom-fullpage .app-root {
  min-height: 100vh;
}

.simplebar-custom .simplebar-content-wrapper:focus {
  outline: none;
}

.simplebar-custom .simplebar-vertical.simplebar-track {
  width: 16px;
  overflow: visible;
  z-index: 4;
}

.simplebar-custom .simplebar-vertical.simplebar-track:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  right: 12px;
  height: 100%;
  background: rgba(231, 235, 237, 1);
  border-radius: 7px;
}

.simplebar-custom .simplebar-vertical.simplebar-track .simplebar-scrollbar:before {
  overflow: hidden;
}

.simplebar-custom .simplebar-vertical.simplebar-track .simplebar-scrollbar:before {
  width: 4px;
  transition:
    transform 0.3s,
    opacity 0.3s,
    width 0.3s;
  background: rgba(142, 153, 163, 1);
  left: auto;
  top: 0;
  right: 12px;
  opacity: 1;
  height: 100%;
}

.simplebar-custom .simplebar-dragging .simplebar-vertical.simplebar-track .simplebar-scrollbar:before {
  width: 8px;
  transform: translateX(4px);
}

.simplebar-custom .simplebar-vertical.simplebar-track.simplebar-hover .simplebar-scrollbar:before {
  width: 8px;
  transform: translateX(4px);
}

.simplebar-custom .simplebar-horizontal.simplebar-track {
  height: 16px;
  overflow: visible;
}

.simplebar-custom .simplebar-horizontal.simplebar-track:before {
  content: '';
  position: absolute;
  top: auto;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: rgba(231, 235, 237, 1);
}

.simplebar-custom .simplebar-horizontal.simplebar-track .simplebar-scrollbar:before {
  width: 100%;
  height: 4px;
  top: auto;
  bottom: 0;
  left: 0;
  transform: none;
  transition:
    transform 0.3s,
    opacity 0.3s,
    height 0.3s;
}

.simplebar-custom .simplebar-horizontal.simplebar-track.simplebar-hover .simplebar-scrollbar:before {
  height: 10px;
  transform: translateY(3px);
}

.simplebar-themed .simplebar-horizontal.simplebar-track:before,
.simplebar-themed .simplebar-vertical.simplebar-track:before {
  background: var(--fqz-poll-text-on-place);
  opacity: 0.2;
}

.simplebar-themed .simplebar-horizontal.simplebar-track .simplebar-scrollbar:before,
.simplebar-themed .simplebar-vertical.simplebar-track .simplebar-scrollbar:before {
  background: var(--fqz-poll-text-on-place);
  opacity: 0.3;
}

.simplebar-themed .simplebar-horizontal.simplebar-track.simplebar-hover .simplebar-scrollbar:before {
  opacity: 0.6;
}
