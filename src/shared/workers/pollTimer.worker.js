/**
 * Web Worker для управления таймером опроса.
 *
 * Используется для точного отсчета времени даже когда вкладка браузера неактивна.
 * В обычной ситуации, когда таймер работает в основном потоке через setInterval,
 * браузер может замедлять или приостанавливать его работу в неактивных вкладках.
 * Web Worker позволяет обойти это ограничение, так как работает в отдельном потоке.
 *
 * @module pollTimer.worker
 */

let timerId = null
let timeLeft = 0

/**
 * Обработчик сообщений от основного потока
 * @param {MessageEvent} e - Событие сообщения
 * @param {object} e.data - Данные сообщения
 * @param {string} e.data.type - Тип сообщения ('START'|'STOP'|'RESET')
 * @param {object} [e.data.payload] - Дополнительные данные
 * @param {number} [e.data.payload.timeLeft] - Оставшееся время в секундах
 */
globalThis.onmessage = function (e) {
  const { type, payload } = e.data

  switch (type) {
    case 'START':
      timeLeft = payload.timeLeft
      startTimer()
      break
    case 'RESET':
      stopTimer()
      timeLeft = payload.timeLeft
      if (timeLeft > 0) {
        startTimer()
      }
      break
    case 'STOP':
      stopTimer()
      break
    default:
      break
  }
}

/**
 * Запускает таймер обратного отсчета
 * Отправляет сообщения в основной поток:
 * - 'TICK' каждую секунду с оставшимся временем
 * - 'FINISHED' когда время истекло
 */
function startTimer() {
  stopTimer() // Clear any existing timer before starting a new one

  if (timeLeft <= 0) {
    // Don't start if time is already zero or less
    return
  }

  timerId = setInterval(() => {
    timeLeft--
    globalThis.postMessage({ type: 'TICK', timeLeft })

    if (timeLeft <= 0) {
      globalThis.postMessage({ type: 'FINISHED' })
      stopTimer()
    }
  }, 1000)
}

/**
 * Останавливает таймер и очищает интервал
 */
function stopTimer() {
  if (timerId) {
    clearInterval(timerId)
    timerId = null
  }
}

/**
 * Отправляем сообщение о готовности воркера к работе
 */
globalThis.postMessage({ type: 'WORKER_READY' })
