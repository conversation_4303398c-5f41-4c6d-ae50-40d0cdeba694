import type { Ref } from 'vue'
import { isRef, onMounted, onUnmounted, ref, toValue, watch } from 'vue'

/**
 * Состояние теней при прокрутке
 */
interface ScrollShadowState {
  showShadowTop: Ref<boolean>
  showShadowBottom: Ref<boolean>
}

/**
 * Хук для отслеживания состояния прокрутки и отображения теней
 * @description Отслеживает положение прокрутки элемента и возвращает состояние видимости теней сверху и снизу
 * @returns {object} Объект с реактивными значениями для отображения теней
 * @example
 * const { showShadowTop, showShadowBottom } = useScrollShadows(document.body, enabledRef)
 */
export function useScrollShadows(
  scrollElement: Ref<Window | HTMLElement | null> | Window | HTMLElement | null,
  enabled: Ref<boolean> = ref(true),
): ScrollShadowState {
  const showShadowTop = ref(false)
  const showShadowBottom = ref(false)

  const getScrollInfo = (element: Window | HTMLElement | null) => {
    if (!element)
      return null

    if (element instanceof Window) {
      return {
        scrollHeight: document.documentElement.scrollHeight,
        clientHeight: document.documentElement.clientHeight,
        scrollTop: window.scrollY,
      }
    }

    return {
      scrollHeight: element.scrollHeight,
      clientHeight: element.clientHeight,
      scrollTop: element.scrollTop,
    }
  }

  const handleScroll = () => {
    const element = scrollElement instanceof Window ? scrollElement : toValue(scrollElement)
    if (!element || !toValue(enabled)) {
      showShadowTop.value = false
      showShadowBottom.value = false
      return
    }

    const scrollInfo = getScrollInfo(element)
    if (!scrollInfo)
      return

    const { scrollHeight, clientHeight, scrollTop } = scrollInfo

    const isScrolledToTop = scrollTop === 0
    const isScrolledToBottom = scrollTop + clientHeight >= scrollHeight - 1

    showShadowTop.value = !isScrolledToTop
    showShadowBottom.value = !isScrolledToBottom
  }

  const cleanup = () => {
    const element = scrollElement instanceof Window ? scrollElement : toValue(scrollElement)
    if (!element)
      return

    element.removeEventListener('scroll', handleScroll)
  }

  onMounted(() => {
    const element = scrollElement instanceof Window ? scrollElement : toValue(scrollElement)
    if (!element)
      return

    element.addEventListener('scroll', handleScroll)
    // Initial check
    handleScroll()
  })

  onUnmounted(() => {
    cleanup()
  })

  // Watch for changes if scrollElement is a ref
  if (isRef(scrollElement)) {
    watch([scrollElement, enabled], () => {
      cleanup()
      const element = toValue(scrollElement)
      if (!element)
        return

      element.addEventListener('scroll', handleScroll)
      handleScroll()
    }, { immediate: true })
  }
  else {
    // Just watch enabled if scrollElement is direct Window
    watch(enabled, handleScroll, { immediate: true })
  }

  return {
    showShadowTop,
    showShadowBottom,
  }
}
