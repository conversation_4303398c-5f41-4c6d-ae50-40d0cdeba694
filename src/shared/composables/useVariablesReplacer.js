import CopyableVariable from '@shared/ui/CopyableVariable.vue'
import CopyPromocode from '@shared/ui/CopyPromocode.vue'
import LoadableText from '@shared/ui/LoadableText.vue'
import { h, nextTick, render, toValue } from 'vue'
import { useTranslationsStore } from '../store/translationsStore'
import { useTabletStore } from '../store/useTabletStore' // Already imported, good.
import CountrySelect from '../ui/CountrySelect.vue'

// Функция для генерации уникального идентификатора
const getUniqueId = () => Math.random().toString(36).substring(2, 15)

/**
 * Композабл для замены переменных в тексте
 * @returns {object} Объект с функцией replace
 */
export default function useVariablesReplacer() {
  const translationsStore = useTranslationsStore()
  const tabletStore = useTabletStore()
  const t = translationsStore.t
  const multipleLangsEnabled = translationsStore.multipleLangsEnabled

  // Общая переменная для хранения активного текстового узла после разделения
  let activeTextNodeAfterSplit = null

  const appendComponent = (component, props, node, placeholderToReplace) => {
    let targetNode = node

    // Если активная нода не имеет родителя, то используем активную ноду после разделения
    if (!targetNode.parentNode && activeTextNodeAfterSplit && activeTextNodeAfterSplit.parentNode) {
      if (activeTextNodeAfterSplit.textContent.includes(placeholderToReplace)) {
        targetNode = activeTextNodeAfterSplit
      }
      else {
        activeTextNodeAfterSplit = null
        return ''
      }
    }
    else if (!targetNode.parentNode) {
      activeTextNodeAfterSplit = null
      return ''
    }

    const wrapper = document.createElement('span')
    const uniqueId = getUniqueId()
    wrapper.classList.add('component-wrapper')
    wrapper.id = `component-wrapper-${uniqueId}`

    const nodeText = targetNode.textContent
    let leadingText = ''
    let trailingText = ''

    const placeholderIndex = nodeText.indexOf(placeholderToReplace)

    if (placeholderIndex !== -1) {
      leadingText = nodeText.substring(0, placeholderIndex)
      trailingText = nodeText.substring(placeholderIndex + placeholderToReplace.length)
    }
    else {
      activeTextNodeAfterSplit = null
      return ''
    }

    const parent = targetNode.parentNode
    if (!parent) {
      activeTextNodeAfterSplit = null
      return ''
    }
    const fragment = document.createDocumentFragment()
    let newTrailingTextNode = null

    if (leadingText) {
      fragment.appendChild(document.createTextNode(leadingText))
    }
    fragment.appendChild(wrapper)
    if (trailingText) {
      newTrailingTextNode = document.createTextNode(trailingText)
      fragment.appendChild(newTrailingTextNode)
    }

    parent.replaceChild(fragment, targetNode)

    // Нода была заменена, поэтому обновляем активную ноду
    activeTextNodeAfterSplit = newTrailingTextNode

    nextTick(() => {
      const container = document.getElementById(`component-wrapper-${uniqueId}`)
      if (container) {
        render(h(component, props), container)
      }
    })

    return ''
  }

  /**
   * Функция для замены переменных в тексте
   * @param {object} options - Объект с опциями
   * @returns {string} Результат замены переменных
   * @example
   * const variablesReplacer = useVariablesReplacer()
   * const result = variablesReplacer.replace({
   *   text: 'Hello, {URL.name}!',
   *   variables: { 'URL.name': 'World' },
   * })
   * console.log(result) // 'Hello, World!'
   */
  const replace = (options = {}) => {
    const {
      text,
      variables = {},
      answers = {},
      ignoreInteractiveElements = false,
      // Режим превью: preview-default, preview-design, preview-full
      // В разных режимах переменные могут работать по-разному
      previewMode = null,
    } = options

    if (!text)
      return ''

    if (previewMode === 'preview-default') {
      return text
    }

    const isFunctionalPreview = previewMode === 'preview-design' || previewMode === 'preview-full'

    // Объект с правилами замены
    const replacements = {
      // Замена переменных баллов
      '{Общее кол-во баллов}': (args, { node }) => {
        if (isFunctionalPreview)
          return '0'

        return appendComponent(LoadableText, {
          isLoading: toValue(variables.points).isLoading,
          text: toValue(variables.points).maxPoints?.toString() || '0',
        }, node, args[0])
      },
      '{Набранное кол-во баллов}': (args, { node }) => {
        if (isFunctionalPreview)
          return '0'

        return appendComponent(LoadableText, {
          isLoading: toValue(variables.points).isLoading,
          text: toValue(variables.points).totalPoints?.toString() || '0',
        }, node, args[0])
      },
      '{Набранное кол-во баллов в %}': (args, { node }) => {
        if (isFunctionalPreview)
          return '0'

        return appendComponent(LoadableText, {
          isLoading: toValue(variables.points).isLoading,
          text: toValue(variables.points).pointsPercentage?.toString() || '0',
        }, node, args[0])
      },
      '{Интерпретация баллов: Результат}': (args, { node }) => {
        if (isFunctionalPreview)
          return ''

        return appendComponent(LoadableText, {
          isLoading: toValue(variables.points).isLoading,
          text: toValue(variables.points).currentPointsInterpretation?.result || '',
        }, node, args[0])
      },
      '{Интерпретация баллов: Описание результата}': (args, { node }) => {
        if (isFunctionalPreview)
          return ''

        return appendComponent(LoadableText, {
          isLoading: toValue(variables.points).isLoading,
          text: toValue(variables.points).currentPointsInterpretation?.description || '',
        }, node, args[0])
      },
      // Замена ФИО.
      // @NOTE: Для превью добавляем фейковый ФИО. Для демонстрации
      '{ФИО}': isFunctionalPreview ? 'Иванов Иван Иванович' : (variables.fio || ''),

      // Замена промокода
      '{Промокод}': (args, { node }) => {
        let promocode = variables?.promocode
        if (isFunctionalPreview) {
          // @NOTE: Для превью добавляем фейковый промокод. Для демонстрации
          promocode = '111111'
        }

        if (promocode) {
          const wrapper = document.createElement('span')
          wrapper.classList.add('inter-block-promocode')
          wrapper.innerHTML = promocode
          const textParts = node.textContent.split('{Промокод}')
          const fragment = document.createDocumentFragment()

          textParts.forEach((part, index) => {
            if (index > 0) {
              fragment.appendChild(wrapper)
            }
            if (part) {
              fragment.appendChild(document.createTextNode(part))
            }
          })
          node.parentNode.replaceChild(fragment, node)
          return () => wrapper
        }
        return ''
      },

      // Замена промокода с копированием - добавляем фейковое значение для превью
      '{Промокод с копированием}': () => {
        let promocodeValue = variables?.promocode

        // @NOTE: Для превью добавляем фейковый промокод. Для демонстрации копирования
        if (isFunctionalPreview) {
          promocodeValue = '111111'
        }

        return promocodeValue
          ? h(CopyPromocode, {
            promocode: promocodeValue,
            copyButtonText: t('Копировать промокод'),
            copySuccessText: t('Скопировано'),
            tabletView: tabletStore.isTabletMode,
          })
          : ''
      },

      // Добавление выбора языка
      '{Выбор языка}': () => h(CountrySelect, {
        'countries': translationsStore.allLangs,
        'modelValue': () => translationsStore.selectedLang,
        'disableOutAnimation': true,
        'tabletView': tabletStore.isTabletMode,
        'onUpdate:modelValue': (lang) => {
          translationsStore.setLanguage(lang.shortCode)
        },
      }),

      // Замена URL-параметров
      '{URL.([^}]+)}': (args, { node }) => {
        if (isFunctionalPreview)
          return args?.[0] || ''

        const fullPlaceholder = args[0]
        // Параметр URL-параметра из переменной
        // {URL.name} -> name
        const param = args[1]?.split('.')[0]
        const value = variables.urls?.[`URL.${param}`] || ''
        if (value === null || value === undefined) {
          return ''
        }

        return handleCopyableVariable({
          fullPlaceholder,
          param,
          value,
          node,
        })
      },

      /**
       * Переменная, для получения ответа предыдущего вопроса
       * по номеру.
       * @param {Array} args - Массив с аргументами
       * @param {object} data - Дополнительные данные
       * @param {object} data.node - Узел DOM
       * @returns {string} Результат замены ответа
       * @example
       * const variablesReplacer = useVariablesReplacer()
       * // В данном примере предположим, что первый вопрос - это звездный рейтинг
       * // и пользователь выбрал 2 звезды
       * const result = variablesReplacer.replace({
       *   text: 'Почему вы ответили {ANSWER.1}?',
       *   answers: {
       *     1: '2 звезды',
       *   },
       * })
       * console.log(result) // 'Почему вы ответили 2 звезды?'
       */
      '{ANSWER.([^}]+)}': (args, { node }) => {
        if (isFunctionalPreview)
          return ''

        const fullPlaceholder = args[0]
        const param = args[1]?.split('.')[0]
        const parsedNum = Number.parseInt(param)
        if (Number.isNaN(parsedNum)) {
          return ''
        }
        let answer = toValue(answers[parsedNum])

        if (answer === null || answer === undefined) {
          return ''
        }
        if (typeof answer === 'number') {
          answer = answer.toString()
        }

        return handleCopyableVariable({
          fullPlaceholder,
          param,
          value: answer || '',
          node,
        })
      },

      // Замена данных филиала
      '{FILIAL.([^}]+)}': (args, { node }) => {
        if (isFunctionalPreview)
          return args?.[0] || ''

        const fullPlaceholder = args[0]

        // Параметр филиала из переменной
        // {FILIAL.name} -> name
        const param = args[1]?.split('.')[0]

        const value = variables.filials?.[`FILIAL.${param}`] || ''
        if (!value) {
          return ''
        }

        return handleCopyableVariable({
          fullPlaceholder,
          param,
          value,
          node,
        })
      },
    }

    // Функция для обработки копируемых переменных
    function handleCopyableVariable({ fullPlaceholder, value, node }) {
      const copySuffixRegex = /\.COPY\}$/
      const shouldInsertCopyableVariable
      = copySuffixRegex.test(fullPlaceholder)
      && !ignoreInteractiveElements
      && node
      && value
      if (shouldInsertCopyableVariable) {
        const parentNode = node.parentNode
        const comp = h(CopyableVariable, { text: value })
        const wrapper = document.createElement('span')
        const uniqueId = getUniqueId()
        wrapper.classList.add('copyable-variable-wrapper')
        wrapper.id = `copyable-variable-wrapper-${uniqueId}`

        const textParts = node.textContent.split(fullPlaceholder)
        const fragment = document.createDocumentFragment()

        textParts.forEach((part, index) => {
          if (index > 0) {
            fragment.appendChild(wrapper)
          }
          if (part) {
            fragment.appendChild(document.createTextNode(part))
          }
        })

        parentNode.replaceChild(fragment, node)

        nextTick(() => {
          const container = document.getElementById(`copyable-variable-wrapper-${uniqueId}`)
          if (container) {
            render(comp, container)
          }
        })

        return () => wrapper
      }

      return value
    }

    let result = toValue(text)

    // Обработка HTML-контента
    if (/<[a-z][\s\S]*>/i.test(result)) {
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = result

      // Рекурсивная функция для замены в узлах DOM
      const replaceInNode = (node) => {
        if (node.nodeType === Node.TEXT_NODE) {
          let content = node.textContent

          // Первый проход: оборачиваем все переменные в span
          const variablePatterns = Object.keys(replacements)
          let hasVariables = false

          variablePatterns.forEach((pattern) => {
            const regex = new RegExp(pattern, 'g')
            if (regex.test(content)) {
              hasVariables = true
              content = content.replace(regex, (match) => {
                const uniqueId = getUniqueId()
                return `<span data-variable="${match}" data-variable-id="${uniqueId}">${match}</span>`
              })
            }
          })

          if (hasVariables && node.parentNode) {
            const parentNode = node.parentNode
            const tempDiv = document.createElement('div')
            tempDiv.innerHTML = content

            const fragment = document.createDocumentFragment()
            Array.from(tempDiv.childNodes).forEach((child) => {
              fragment.appendChild(child)
            })

            parentNode.replaceChild(fragment, node)

            // Второй проход: обрабатываем каждый span в компоненты переменных
            parentNode.querySelectorAll('[data-variable]').forEach((span) => {
              const variableText = span.getAttribute('data-variable')
              const variableId = span.getAttribute('data-variable-id')

              Object.entries(replacements).forEach(([key, value]) => {
                const regex = new RegExp(key)
                if (regex.test(variableText)) {
                  if (key === '{Промокод с копированием}') {
                    if ((!variables.promocode || ignoreInteractiveElements) && !isFunctionalPreview) {
                      span.textContent = ''
                    }
                    else {
                      const containerEl = document.createElement('div')
                      containerEl.classList.add('copy-promocode-container')
                      containerEl.id = `copy-promocode-container-${variableId}`
                      span.parentNode.replaceChild(containerEl, span)

                      nextTick(() => {
                        const container = document.getElementById(`copy-promocode-container-${variableId}`)
                        if (container) {
                          render(value(), container)
                        }
                      })
                    }
                  }
                  else if (key === '{Выбор языка}') {
                    if (!multipleLangsEnabled || ignoreInteractiveElements) {
                      span.textContent = ''
                    }
                    else {
                      const containerEl = document.createElement('div')
                      containerEl.classList.add('country-select-container')
                      containerEl.id = `country-select-container-${variableId}`
                      span.parentNode.replaceChild(containerEl, span)

                      nextTick(() => {
                        const container = document.getElementById(`country-select-container-${variableId}`)
                        if (container) {
                          render(h(CountrySelect, {
                            'countries': translationsStore.allLangs,
                            'modelValue': () => translationsStore.selectedLang,
                            'disableOutAnimation': true,
                            'tabletView': tabletStore.isTabletMode,
                            'onUpdate:modelValue': (lang) => {
                              translationsStore.setLanguage(lang.shortCode)
                            },
                          }), container)
                        }
                      })
                    }
                  }
                  else if (typeof value === 'function') {
                    const result = variableText.replace(regex, (...args) => {
                      return value(args, { node: span })
                    })
                    if (typeof result === 'string') {
                      span.textContent = result
                    }
                  }
                  else if (typeof value === 'string') {
                    span.textContent = variableText.replace(regex, value)
                  }
                }
              })
            })
          }
        }
        else if (node.nodeType === Node.ELEMENT_NODE) {
          Array.from(node.childNodes).forEach(replaceInNode)
        }
      }

      replaceInNode(tempDiv)
      result = tempDiv.innerHTML
    }
    else if (result) {
      // Замена переменных в обычном тексте
      Object.entries(replacements).forEach(([key, value]) => {
        result = result.replace(new RegExp(key, 'g'), (...args) => {
          if (typeof value === 'function') {
            return value(args, { node: null })
          }
          return value
        })
      })
    }

    activeTextNodeAfterSplit = null
    return result
  }

  // Возвращаем объект с функцией replace
  return {
    replace,
  }
}
