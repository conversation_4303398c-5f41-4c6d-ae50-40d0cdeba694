/* eslint-disable regexp/no-unused-capturing-group  */
/* eslint-disable regexp/no-super-linear-backtracking */
export const EMAIL_REGEX = /^\S+@\S+\.\S+$/
export const EMAIL_WITH_BOUNDARIES_REGEX = /^\b[\w.%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i

export const PHONE_REGEX = /^\+7 \(\d{3}\) \d{3} \d{4}$/
export const SITE_REGEX = /^(https?:\/\/)?(www\.)?[a-z0-9]+([\-.][a-z0-9]+)*\.[a-z]{2,5}(?::\d{1,5})?\/?$/

export const TIME_REGEX = /^([01]\d|2[0-3]):([0-5]\d)$/

export function isValidHttpUrl(str) {
  const pattern = new RegExp(
    '^(https?:\\/\\/)?' // protocol
    + '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' // domain name
    + '((\\d{1,3}\\.){3}\\d{1,3}))' // OR ip (v4) address
    + '(:\\d+)?(\\/[-\\w%.~+]*)*' // port and path
    + '(\\?[;&\\w%.~+=-]*)?' // query string
    + '(#[-\\w]*)?$', // fragment locator
    'i',
  )
  return pattern.test(str)
}
