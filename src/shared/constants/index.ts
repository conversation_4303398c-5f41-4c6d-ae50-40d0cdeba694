export const TEMP_POLL_KEY = '2611bef92b815306c389c698e290ad94'

/**
 * Тип выбора вариантов из донора
 * Если значение равно 1, то берем выбранные варианты из донора
 * Если значение равно 0, то берем невыбранные варианты из донора
 * Данный объект используется при включенной опции "Донор-Реципиент"
 * @type {object}
 * @property {number} SELECTED - выбранные
 * @property {number} NOT_SELECTED - невыбранные
 */
export const DONOR_SELECT_TYPE = {
  SELECTED: 1,
  NOT_SELECTED: 0,
}

/**
 * Режим отображения навигации (Опция "Отображение процесса" в настройках опроса)
 * null - нет навигации
 * 1 - постраничная навигация
 * 2 - постраничная навигация с прогресс-баром
 */
export const POLL_NAVIGATION_TYPE = {
  DISABLED: null,
  NUMBERS: 1,
  PROGRESSBAR: 2,
}

/**
 * Тип уточняющего вопроса для вариантов
 * @type {object}
 * @property {number} DISABLED - уточняющий вопрос отключен
 * @property {number} SINGLE - один общий уточняющий вопрос для всех вариантов
 * @property {number} COMMON_PER_VARIANT - одинаковый уточняющий вопрос для каждого варианта
 * @property {number} DIFFERENT_PER_VARIANT - разные уточняющие вопросы для каждого варианта
 */
export const EXTRA_QUESTION_TYPE = {
  DISABLED: 0,
  SINGLE: 1,
  COMMON_PER_VARIANT: 2,
  DIFFERENT_PER_VARIANT: 3,
}
