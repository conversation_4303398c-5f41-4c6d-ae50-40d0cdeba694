export const BEHAVI<PERSON>_SELECT = 1
export const BEHAVIOR_UNSELECT = 2
export const BEHAVIOR_MISS = 3
export const BEHAVIOR_ALWAYS = 4

export const JUMP_TO_QUESTION = 1
export const JUMP_TO_DEFAULT_END_SCREEN = 2
export const JUMP_TO_END_SCREEN = 3

/**
 * Условие отображения вопроса
 * @type {object}
 */
export const VIEW_LOGIC = {
  /**
   * Тип отображения вопроса
   * @type {object}
   * @property {number} SHOW_IF - Отображать вопрос, если условие истинно
   * @property {number} HIDE_IF - Скрывать вопрос, если условие истинно
   */
  DISPLAY_TYPE: {
    SHOW_IF: 0,
    HIDE_IF: 1,
  },
  /**
   * Тип условия
   * @type {object}
   * @property {number} ANSWER - Условие по ответу на вопрос
   * @property {number} PARAMETER - Условие по параметру
   * @see https://foquz.ru/foquz/user-wiki/logicheskie-usloviya-dlya-voprosov
   */
  CONDITION_TYPE: {
    ANSWER: 0,
    PARAMETER: 1,
  },
  /**
   * Тип условия для параметра
   * @type {object}
   * @property {number} EQUAL - Равно
   * @property {number} GREATER_THAN - Больше
   * @property {number} LESS_THAN - Меньше
   * @property {number} CONTAINS - Содержит
   * @property {number} STARTS_WITH - Начинается с
   * @property {number} ENDS_WITH - Заканчивается на
   */
  PARAMETER_CONDITION_TYPE: {
    EQUAL: 0,
    GREATER_THAN: 1,
    LESS_THAN: 2,
    CONTAINS: 3,
    STARTS_WITH: 4,
    ENDS_WITH: 5,
  },
}
