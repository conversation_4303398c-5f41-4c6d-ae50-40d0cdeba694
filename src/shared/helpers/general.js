export function withRootUrl(url) {
  if (!url)
    return ''

  if (url.startsWith('http'))
    return url

  const isDev = import.meta.env.MODE === 'development'
    || import.meta.env.MODE === 'test'
    || import.meta.env.MODE === 'development-local'

  const protocol = window.location.protocol
  const host = window.location.host
  const rootUrl = isDev ? import.meta.env.VITE_ROOT_URL : `${protocol}//${host}`
  const normalizedUrl = url.startsWith('/') ? url : `/${url}`
  return `${rootUrl}${normalizedUrl}`
}

/**
 * Сериализует объект в строку, поддерживая глубоко вложенные объекты и массивы
 * @param {object} obj - Объект для сериализации
 * @param {string} [prefix] - Префикс для ключей (используется для рекурсии)
 * @returns {string} Строка, представляющая сериализованный объект
 */
export function serialize(obj, prefix = '') {
  const str = []

  for (const p in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, p)) {
      const k = prefix ? `${prefix}[${encodeURIComponent(p)}]` : encodeURIComponent(p)
      const v = obj[p]

      if (v === null || v === undefined) {
        str.push(`${k}=`)
      }
      else if (typeof v === 'object') {
        str.push(serialize(v, k))
      }
      else {
        str.push(`${k}=${encodeURIComponent(v)}`)
      }
    }
  }

  return str.join('&')
}

/**
 * Возвращает уникальный идентификатор для элемента
 * @param {string} prefix - Префикс для идентификатора
 * @returns {string} Уникальный идентификатор
 */
export function getUniqueId(prefix = '') {
  return `${prefix}-${Math.random().toString(36).substring(2, 15)}`
}
