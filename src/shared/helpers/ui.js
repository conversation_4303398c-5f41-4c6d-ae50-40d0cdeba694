import kebabCase from 'lodash.kebabcase'
import pick from 'lodash.pick'
import { withRootUrl } from './general'

/**
 * Обновляет CSS-переменные на основе данных дизайна.
 *
 * @param {HTMLElement} root - Корневой элемент, к которому будут применены CSS-переменные.
 * @param {object} designData - Объект с данными дизайна.
 *
 * @description
 * Эта функция принимает корневой элемент и объект с данными дизайна.
 * Она обрабатывает различные свойства дизайна и устанавливает соответствующие
 * CSS-переменные на корневом элементе. Функция обрабатывает такие аспекты, как
 * фоновые изображения, цвета, шрифты, размеры кнопок и другие стилевые параметры.
 *
 * Если root или designData не предоставлены, функция завершается без выполнения действий.
 */
export function updateCSSVariables(root, designData) {
  if (!root)
    return

  if (!designData)
    return

  const pickedFields = pick(designData, [
    'background_image',
    'mobile_background_image',
    'main_color',
    'font_family',
    'font_size',
    'title_font_size',
    'main_place_color',
    'background_color',
    'text_on_bg',
    'text_on_place',
    'star_color',
    'link_color',
    'darkening_background',
    'place_under_buttons',
    'start_bg_url',
    'end_bg_url',
    'next_button_radius',
    'next_button_background_color',
    'next_button_text_color',
    'next_button_stroke_color',
    'back_button_radius',
    'back_button_background_color',
    'back_button_text_color',
    'back_button_stroke_color',
    'start_button_radius',
    'start_button_background_color',
    'start_button_text_color',
    'start_button_stroke_color',
    'header_color',
    'logo_font_family',
    'logo_color',
    'logo_type',
    'is_use_header',
  ])

  Object.entries(pickedFields).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (key === 'background_image') {
        root.style.setProperty(`--fqz-poll-bg-image`, `url("${withRootUrl(value)}")`)
        root.style.setProperty(`--fqz-poll-mobile-bg-image`, `url("${withRootUrl(value)}")`)
        root.style.setProperty(`--poll-finish-bg-image`, `url("${withRootUrl(value)}")`)
        root.style.setProperty(`--fqz-poll-simplified-bg-image`, `url("${withRootUrl(value)}")`)
      }
      else if (key === 'mobile_background_image' && !!value) {
        root.style.setProperty(`--fqz-poll-mobile-bg-image`, `url("${withRootUrl(value)}")`)
        root.style.setProperty(`--poll-finish-bg-image`, `url("${withRootUrl(value)}")`)
      }
      else if (key === 'font_size' || key === 'title_font_size'
        || key.endsWith('_button_radius')) {
        root.style.setProperty(`--fqz-poll-${kebabCase(key)}`, `${value}px`)
      }
      else if (key === 'main_place_color') {
        root.style.setProperty(`--fqz-poll-${kebabCase(key)}`, value)
        const rgbaRegex = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*(0|0?\.\d+|1(\.0)?)\s*\)$/
        const match = value.match(rgbaRegex)
        if (match) {
          const opacity = match[1]
          root.style.setProperty(`--fqz-poll-main-place-opacity`, opacity)
          root.style.setProperty(`--show-gradient-shadow`, opacity === '1' ? 'block' : 'none')
        }
      }
      else if (key === 'background_color') {
        root.style.setProperty(`--fqz-poll-bg-color`, value)
      }
      else if (key === 'darkening_background') {
        root.style.setProperty(`--fqz-poll-bg-cover`, value ? 'block' : 'none')
      }
      else if (key === 'place_under_buttons') {
        const buttonsBg = value === 'dark' ? 'rgba(0, 0, 0, 0.85)' : 'rgba(255, 255, 255, 0.85)'
        root.style.setProperty(`--fqz-poll-buttons-bg`, buttonsBg)
      }
      else if (key === 'start_bg_url') {
        root.style.setProperty(`--fqz-poll-start-screen-bg`, value)
      }
      else if (key === 'end_bg_url') {
        root.style.setProperty(`--fqz-poll-end-screen-bg`, value)
      }
      else if (key === 'header_color') {
        root.style.setProperty(`--fqz-poll-header-bg-color`, value)
      }
      else if (key === 'logo_font_family') {
        root.style.setProperty(`--fqz-poll-logo-font-family`, value)
      }
      else if (key === 'logo_color') {
        root.style.setProperty(`--fqz-poll-logo-color`, value)
      }
      else if (key === 'logo_type') {
        root.style.setProperty(`--fqz-poll-logo-display`, value === 'text' ? 'block' : 'none')
      }
      else {
        root.style.setProperty(`--fqz-poll-${kebabCase(key)}`, value)
      }
    }
  })

  // Add progress bar background handling
  const headerEnabled = designData.is_use_header !== 0 && designData.is_use_header !== undefined
  const defaultBgColor = pickedFields.place_under_buttons === 'dark' ? '0, 0, 0' : '255, 255, 255'

  let opacity = '0.85' // default opacity from place_under_buttons
  if (headerEnabled && pickedFields.header_color) {
    const rgbaMatch = pickedFields.header_color.match(/^rgba?\(([^)]+)\)/)
    if (rgbaMatch) {
      const values = rgbaMatch[1].split(',')
      opacity = values?.[3] ? values?.[3].trim?.() : '0.85'
    }
  }

  root.style.setProperty(
    '--fqz-poll-progressbar-bg',
    `rgba(${defaultBgColor}, ${opacity})`,
  )
}
