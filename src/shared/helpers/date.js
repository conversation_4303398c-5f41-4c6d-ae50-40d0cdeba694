import { toValue } from 'vue'

/**
 * Получаем массив с месяцами для выбора в календаре
 * @param {Function} t - Функция для получения перевода
 * @returns {Array} - Массив с переведенными месяцами
 * @example
 * const months = getMonths(t); // Предположим, что в опросе выбран английский язык
 * // months будет содержать массив с месяцами
 * console.log(months); // [{ id: 1, label: 'January' }, { id: 2, label: 'February' }, ...]
 */
export function getMonths(t) {
  return [
    {
      id: 1,
      label: toValue(t('Январь')),
    },
    {
      id: 2,
      label: toValue(t('Февраль')),
    },
    {
      id: 3,
      label: toValue(t('Март')),
    },
    {
      id: 4,
      label: toValue(t('Апрель')),
    },
    {
      id: 5,
      label: toValue(t('Май')),
    },
    {
      id: 6,
      label: toValue(t('Июнь')),
    },
    {
      id: 7,
      label: toValue(t('Июль')),
    },
    {
      id: 8,
      label: toValue(t('Август')),
    },
    {
      id: 9,
      label: toValue(t('Сентябрь')),
    },
    {
      id: 10,
      label: toValue(t('Октябрь')),
    },
    {
      id: 11,
      label: toValue(t('Ноябрь')),
    },
    {
      id: 12,
      label: toValue(t('Декабрь')),
    },
  ]
}

// generate years from 1923 to 2123
export function getYears() {
  return Array.from({ length: 203 }, (_, index) => {
    return {
      id: 1923 + index,
      label: 1923 + index,
    }
  })
}
