/**
 * Возвращает правильную форму слова в зависимости от числа.
 *
 * @param {number} number - Число, для которого нужно подобрать форму слова.
 * @param {string[]} titles - Массив из трех форм слова (для 1, 2-4, 5-20).
 * @returns {string} Правильная форма слова.
 *
 * @example
 * // Возвращает "яблоко"
 * declOfNum(1, ['яблоко', 'яблока', 'яблок']);
 *
 * @example
 * // Возвращает "яблока"
 * declOfNum(3, ['яблоко', 'яблока', 'яблок']);
 *
 * @example
 * // Возвращает "яблок"
 * declOfNum(11, ['яблоко', 'яблока', 'яблок']);
 */
export function declOfNum(number, titles) {
  const _number = Math.abs(number)
  const _titles = [...titles]
  if (_titles.length < 3)
    _titles[2] = _titles[1]

  const cases = [2, 0, 1, 1, 1, 2]
  return _titles[
    _number % 100 > 4 && _number % 100 < 20
      ? 2
      : cases[_number % 10 < 5 ? _number % 10 : 5]
  ]
}
