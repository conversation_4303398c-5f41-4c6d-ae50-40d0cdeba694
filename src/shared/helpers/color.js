export const NEUTRAL_COLOR = '#ACACB6'

/**
 * Преобразует HEX-код цвета в массив RGB значений.
 * @param {string} hex - HEX-код цвета (например, '#FF0000' или 'FF0000').
 * @returns {number[]|null} Массив из трех чисел, представляющих RGB значения, или null, если входные данные некорректны.
 * @example
 * // Возвращает [255, 0, 0]
 * hexToRgbArray('#FF0000');
 *
 * // Возвращает [0, 255, 0]
 * hexToRgbArray('00FF00');
 *
 * // Возвращает null
 * hexToRgbArray('Invalid');
 */
function hexToRgbArray(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  if (!result)
    return null
  return [
    Number.parseInt(result[1], 16),
    Number.parseInt(result[2], 16),
    Number.parseInt(result[3], 16),
  ]
}

/**
 * Преобразует HEX-код цвета в строку RGB.
 * @param {string} hex - HEX-код цвета (например, '#FF0000' или 'FF0000').
 * @returns {string|null} Строка RGB в формате "r,g,b" или null, если входные данные некорректны.
 * @example
 * // Возвращает "255,0,0"
 * hexToRgb('#FF0000');
 *
 * // Возвращает "0,255,0"
 * hexToRgb('00FF00');
 *
 * // Возвращает null
 * hexToRgb('Invalid');
 */
export function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? `${Number.parseInt(result[1], 16)
    },${
      Number.parseInt(result[2], 16)
    },${
      Number.parseInt(result[3], 16)}`
    : null
}

/**
 * Преобразует строку RGB в HEX-код цвета.
 * @param {string} rgbString - Строка RGB в формате "r,g,b" (например, "255,0,0").
 * @returns {string} HEX-код цвета (например, '#FF0000').
 * @throws {Error} Если входная строка не соответствует формату RGB.
 * @example
 * // Возвращает '#FF0000'
 * rgbStringToHex('255,0,0');
 *
 * // Возвращает '#00FF00'
 * rgbStringToHex('0,255,0');
 *
 * // Выбрасывает ошибку
 * rgbStringToHex('Invalid');
 */
function rgbStringToHex(rgbString) {
  if (/^#[0-9A-F]{6}$/i.test(rgbString)) {
    return rgbString
  }
  const match = rgbString.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/)

  if (!match) {
    throw new Error('Неверный формат строки RGB.')
  }
  const r = Number.parseInt(match[1])
  const g = Number.parseInt(match[2])
  const b = Number.parseInt(match[3])

  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
}

/**
 * Генерирует градиент цветов между двумя заданными цветами.
 *
 * @param {object} config - Конфигурация градиента.
 * @param {string} config.startColor - Начальный цвет градиента (в формате HEX или RGB).
 * @param {string} config.endColor - Конечный цвет градиента (в формате HEX или RGB).
 * @param {number} config.count - Количество цветов в градиенте.
 * @returns {Array} Массив RGB-значений цветов градиента.
 *
 * @example
 * // Возвращает градиент из 5 цветов от красного к синему
 * const redToBlue = getGradient({
 *   startColor: '#FF0000',
 *   endColor: '#0000FF',
 *   count: 5
 * });
 * console.log(redToBlue);
 * // Примерный вывод: [[255, 0, 0], [191, 0, 64], [128, 0, 128], [64, 0, 191], [0, 0, 255]]
 *
 * @example
 * // Возвращает градиент из 3 цветов от зеленого к желтому
 * const greenToYellow = getGradient({
 *   startColor: 'rgb(0,255,0)',
 *   endColor: 'rgb(255,255,0)',
 *   count: 3
 * });
 * console.log(greenToYellow);
 * // Примерный вывод: [[0, 255, 0], [128, 255, 0], [255, 255, 0]]
 */
export function getGradient(config) {
  const start = hexToRgbArray(rgbStringToHex(config.startColor))
  const finish = hexToRgbArray(rgbStringToHex(config.endColor))
  const count = config.count - 1

  const getPoint = (point) => {
    let x = start[0]
    let y = start[1]
    let z = start[2]

    if (point === count) {
      x = finish[0]
      y = finish[1]
      z = finish[2]
    }
    else if (point > 0) {
      const k = point / count / ((count - point) / point)
      x = (start[0] + k * finish[0]) / (1 + k)
      y = (start[1] + k * finish[1]) / (1 + k)
      z = (start[2] + k * finish[2]) / (1 + k)
    }

    return [x, y, z]
  }

  const gradient = []
  for (let i = 0; i < config.count; i++) {
    gradient.push(getPoint(i))
  }

  return gradient
}

export function doubleGradient(config) {
  const startColor = config.start
  const endColor = config.end

  const count = config.count
  const center = config.center || Math.ceil(count / 2)

  const neutralColor = config.neutral || NEUTRAL_COLOR

  const left = getGradient({
    startColor,
    endColor: neutralColor,
    count: center,
  })
  const right = getGradient({
    startColor: neutralColor,
    endColor,
    count: count - center + 1,
  })

  return [...left, ...right.slice(1)].map((c) => {
    return [Math.round(c[0]), Math.round(c[1]), Math.round(c[2])]
  })
}

export function doubleGradientCss(config) {
  return doubleGradient(config).map((c) => {
    return `rgb(${c[0]}, ${c[1]}, ${c[2]})`
  })
}

export function NPSGradient(startColor, endColor) {
  const NPSNeutralColor = '#ACACB6' // Assuming a neutral gray color
  const left = getGradient({
    startColor,
    endColor: NPSNeutralColor,
    count: 7,
  })
  const right = getGradient({
    startColor: NPSNeutralColor,
    endColor,
    count: 5,
  })
  return [...left, ...right.slice(1)].map(c => `rgb(${Math.round(c[0])}, ${Math.round(c[1])}, ${Math.round(c[2])})`)
}
