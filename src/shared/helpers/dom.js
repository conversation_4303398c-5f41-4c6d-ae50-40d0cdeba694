const CUSTOM_SCROLL_CONTAINER_CLASS = '.app-root-container .fz-simplebar-content-wrapper'
const QUESTIONS_SCROLL_CONTAINER_CLASS = '.page-questions-scrollbar-wrapper__content-wrapper'

/**
 * Возвращает элемент скролла, который используется для триггера событий
 * В разных режимах используются разные элементы скролла:
 * - в режиме превью используется кастомный скроллбар
 * - в упрощенном виде при использовании прохождения в качстве виджета используется скроллбар для контейнера с вопросами
 * @returns {HTMLElement} Элемент скролла
 */
export function getScrollElement() {
  const customScrollElement = document.querySelector(CUSTOM_SCROLL_CONTAINER_CLASS)
  const questionsScrollElement = document.querySelector(QUESTIONS_SCROLL_CONTAINER_CLASS)

  if (customScrollElement) { // режим превью
    return customScrollElement
  }
  else if (questionsScrollElement) { // упрощенный режим с виджето
    return questionsScrollElement
  }

  return window
}

/**
 * Вызывает глобальное событие. Обычно мы вызываем его на window, но
 * в режиме превью мы используем кастомный скроллбар, поэтому в этих случаях мы триггерим событие на этом элементе
 * Обычно данная фунция используется для триггера resize и scroll событий
 * @param {string} eventName - Имя события
 * @returns {void}
 */
export function dispatchGlobalEvent(eventName) {
  const scrollElement = getScrollElement()

  if (scrollElement) {
    scrollElement.dispatchEvent(new Event(eventName))
  }
}
