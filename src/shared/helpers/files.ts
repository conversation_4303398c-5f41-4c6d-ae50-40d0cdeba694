import { ALLOWED_BUT_NOT_SUPPORTED, ALLOWED_FILE_TYPES } from '../constants/files'

/**
 * Проверяет, является ли файл видео по URL
 * @param {string | null} url - URL файла или видео (может быть YouTube)
 * @returns {boolean} true если это видео, false если нет
 */
export function isVideo(url?: string | null): boolean {
  if (!url)
    return false

  // Проверяем, является ли URL YouTube
  if (url.includes('youtube.'))
    return true

  // Check file extension
  const urlArr = url.split('.')
  const extension = urlArr[urlArr.length - 1]?.toLowerCase?.()

  if (!extension) {
    return false
  }

  const allowedVideos = [
    ...ALLOWED_FILE_TYPES.video,
    ...ALLOWED_BUT_NOT_SUPPORTED.video,
  ]

  return allowedVideos.includes(extension)
}

export type FileType = 'image' | 'video' | 'audio' | null

/**
 * Получаем тип файла по имени файла
 * @param {string | null} filename - Имя файла
 * @returns {'image' | 'video' | 'audio' | null} Тип файла
 */
export function getFileTypeFromFilename(filename?: string | null): FileType | null {
  if (!filename) {
    return null
  }

  const extension = filename.split('.').pop()?.toLowerCase?.()

  if (!extension) {
    return null
  }

  const allowedImageFormats = [
    ...ALLOWED_FILE_TYPES.image,
    ...ALLOWED_BUT_NOT_SUPPORTED.image,
  ]
  const allowedVideoFormats = [
    ...ALLOWED_FILE_TYPES.video,
  ]

  const allowedAudioFormats = [
    ...ALLOWED_FILE_TYPES.audio,
    ...ALLOWED_BUT_NOT_SUPPORTED.audio,
  ]

  if (allowedImageFormats.includes(extension)) {
    return 'image'
  }

  if (allowedVideoFormats.includes(extension)) {
    return 'video'
  }

  if (allowedAudioFormats.includes(extension)) {
    return 'audio'
  }

  return null
}
