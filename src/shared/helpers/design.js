import { getTheme1DefaultDesignSettings } from '@/features/custom-themes/config/designPresets'
import { getBaseAssetsUrl } from '../api'

/**
 * Возвращает объект с настройками дизайна по умолчанию для опроса.
 * В некоторых случаях, бэкенд может возвращать ошибку,
 * поэтому нам нужно возвращать какие-то настройки по умолчанию.
 * @param {string} [presetName] - Имя пресета (опционально)
 * @returns {object} Объект с настройками дизайна
 */
export function getDefaultDesignSettings(presetName = null) {
  // Default settings if no preset or unknown preset is provided
  const baseUrl = getBaseAssetsUrl()
  const defaultSettings = {
    // Логотип FOQUZ
    logo_image: `${baseUrl}/img/poll-design__custom-logo.svg`,
    logo_height: 13,
    logo_link: 'https://foquz.ru/',
    font_family: 'Arial, Helvetica, sans-serif',
    background_image: `/img/themes/background4.jpg`,
    in_use_cover: 0,
    disabled: false,
    logo_type: 'image',
    mobile_background_image: null,
    main_color: 'rgba(63, 101, 241, 1)',
    background_color: 'rgba(207, 216, 220, 1)',
    header_color: 'rgba(0, 0, 0, 0.5)',
    is_use_header: 1,
    text_on_bg: 'rgba(255, 255, 255, 1)',
    text_on_place: 'rgba(0, 0, 0, 1)',
    link_color: 'rgba(255, 255, 255, 1)',
    logo_text: null,
    choose_language: 1,
    show_prev_button: true,
    unrequired_text: 'Необязательный',
  }

  if (presetName === 'theme-1') {
    // Ensure all necessary default properties exist even in the preset
    return { ...defaultSettings, ...getTheme1DefaultDesignSettings() }
  }

  return defaultSettings
}
