/**
 * Извлекает переменные дизайна, необходимые для интерфейса скриншота
 * @param {object} designData - Данные дизайна из хранилища опроса
 * @returns {object} Переменные дизайна, отформатированные для интерфейса скриншота
 */
export function extractScreenshotDesignVariables(designData) {
  if (!designData)
    return {}

  const variables = {}

  // Стили кнопок для "Далее/Вперед"
  if (designData.next_button_radius !== undefined) {
    variables['--primary-button-radius'] = `${designData.next_button_radius}px`
  }
  if (designData.next_button_background_color) {
    variables['--primary-button-bg'] = designData.next_button_background_color
  }
  if (designData.next_button_text_color) {
    variables['--primary-button-text'] = designData.next_button_text_color
  }
  if (designData.next_button_stroke_color) {
    variables['--primary-button-border'] = designData.next_button_stroke_color
  }

  // Стили кнопки "Назад" (вторичная кнопка)
  if (designData.back_button_radius !== undefined) {
    variables['--secondary-button-radius'] = `${designData.back_button_radius}px`
  }
  if (designData.back_button_background_color) {
    variables['--secondary-button-bg'] = designData.back_button_background_color
  }
  if (designData.back_button_text_color) {
    variables['--secondary-button-text'] = designData.back_button_text_color
  }
  if (designData.back_button_stroke_color) {
    variables['--secondary-button-border'] = designData.back_button_stroke_color
  }

  // Цвет текста на подложке
  if (designData.text_on_place) {
    variables['--text-color'] = designData.text_on_place
  }

  // Основной цвет подложки (primary color)
  if (designData.main_place_color) {
    variables['--background-color'] = designData.main_place_color
    variables['--main-place-color'] = designData.main_place_color
  }

  // Шрифты
  if (designData.font_family) {
    variables['--font-family'] = designData.font_family
  }
  if (designData.font_size) {
    variables['--font-size-base'] = `${designData.font_size}px`
  }

  return variables
}

/**
 * Преобразует переменные дизайна в формат CSS-переменных для интерфейса скриншота
 * @param {object} variables - Объект с переменными дизайна
 * @returns {object} CSS-переменные с префиксом --fz-screenshot-
 */
export function formatScreenshotCSSVariables(variables) {
  const cssVariables = {}

  Object.entries(variables).forEach(([key, value]) => {
    // Удаляем существующий префикс, если есть, и добавляем префикс для скриншота
    const cleanKey = key.replace(/^--/, '')
    cssVariables[`--fz-screenshot-${cleanKey}`] = value
  })

  return cssVariables
}
