import { toValue } from 'vue'

/**
 * Извлекает переводы, необходимые для интерфейса скриншота
 * @param {Function} t - Функция перевода из translationsStore (возвращает ComputedRef<string>)
 * @returns {object} Переводы для интерфейса скриншота
 */
export function extractScreenshotTranslations(t) {
  const translations = {
    // Текст инструкции
    instructionText: toValue(t('Для создания скриншота выделите нужную область и нажмите «Готово»')),
    // Кнопка отмены (первый вариант)
    cancelButton: toValue(t('Отменить')),
    // Кнопка отмены (второй вариант)
    cancelButtonSecond: toValue(t('Отмена')),
    // Кнопка завершения для мобильных
    doneButtonMobile: toValue(t('Сделать скриншот экрана')),
    // Кнопка завершения для десктопа
    doneButtonDesktop: toValue(t('Готово')),
  }

  return translations
}
