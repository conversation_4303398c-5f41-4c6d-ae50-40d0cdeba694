<script setup>
import { CalendarDate, getLocalTimeZone, today } from '@internationalized/date'
import { getYears } from '@shared/helpers/date'
import FcSelect from '@shared/ui/Select/FcSelect.vue'
import { useMediaQuery } from '@vueuse/core'
import { CalendarCell, CalendarCellTrigger, CalendarGrid, CalendarGridBody, CalendarGridRow, CalendarHeader, CalendarRoot, useForwardPropsEmits } from 'radix-vue'
import { computed, ref } from 'vue'

const props = defineProps({
  as: {
    type: [String, Object],
    default: 'div',
  },
  selectProps: {
    type: Object,
    default: () => ({}),
  },
  asChild: Boolean,
  calendarLabel: String,
  defaultPlaceholder: [Object, Date],
  monthOptions: {
    type: Array,
    default: () => [],
  },
  defaultValue: [Object, Date],
  initialFocus: {
    type: Boolean,
    default: false,
  },
  dir: {
    type: String,
    default: 'ltr',
  },
  disabled: Boolean,
  fixedWeeks: Boolean,
  initialFocus: Boolean,
  isDateDisabled: Function,
  isDateUnavailable: Function,
  locale: {
    type: String,
    default: 'en',
  },
  maxValue: [Object, Date],
  minValue: [Object, Date],
  multiple: Boolean,
  nextPage: Function,
  numberOfMonths: {
    type: Number,
    default: 1,
  },
  pagedNavigation: Boolean,
  placeholder: [Object, Date],
  preventDeselect: Boolean,
  prevPage: Function,
  readonly: Boolean,
  weekdayFormat: {
    type: String,
    default: 'narrow',
  },
  weekStartsOn: {
    type: Number,
    default: 0,
  },
  compact: {
    type: Boolean,
    default: false,
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  'update:modelValue',
  'update:placeholder',
  'cell-click',
])

const forwarded = useForwardPropsEmits(props, emit)

const model = defineModel()

const yearOptions = getYears()
const todayDate = today(getLocalTimeZone())
const defaultPlaceholderDate = ref(todayDate.add({ day: 1 }))
const selectedMonth = computed({
  get() {
    const val = model.value
    const placeholderVal = props.defaultPlaceholder
    const defaultVal = props.defaultValue
    if (val) {
      const month = val.month
      return props.monthOptions.find(m => m.id === month)
    }
    if (placeholderVal) {
      const month = placeholderVal.month
      return props.monthOptions.find(m => m.id === month)
    }
    if (defaultPlaceholderDate.value) {
      const month = defaultPlaceholderDate.value.month
      return props.monthOptions.find(m => m.id === month)
    }
    if (defaultVal) {
      const month = defaultVal.month
      return props.monthOptions.find(m => m.id === month)
    }
    return props.monthOptions.find(m => m.id === todayDate.month)
  },
  set(v) {
    if (!v) {
      return
    }
    const val = model.value
    if (val) {
      emit('update:modelValue', val.set({ month: v.id }))
    }
    else {
      defaultPlaceholderDate.value = defaultPlaceholderDate.value.set({ month: v.id })
    }
  },
})

const selectedYear = computed({
  get() {
    const val = model.value
    const placeholderVal = props.defaultPlaceholder
    const defaultVal = props.defaultValue
    if (val) {
      return yearOptions.find(m => m.id === val.year)
    }
    if (placeholderVal) {
      return yearOptions.find(m => m.id === placeholderVal.year)
    }
    if (defaultPlaceholderDate.value) {
      return yearOptions.find(m => m.id === defaultPlaceholderDate.value.year)
    }
    if (defaultVal) {
      return yearOptions.find(m => m.id === defaultVal.year)
    }
    return yearOptions.find(m => m.id === todayDate.year)
  },
  set(v) {
    if (!v) {
      return
    }
    const val = model.value
    if (val) {
      emit('update:modelValue', val.set({ year: v.id }))
    }
    else {
      defaultPlaceholderDate.value = defaultPlaceholderDate.value.set({ year: v.id })
    }
  },
})

const placeholder = computed({
  get() {
    return model.value || defaultPlaceholderDate.value
  },
  set(v) {
    defaultPlaceholderDate.value = v
  },
})
const isMobile = useMediaQuery('(max-width: 679px)')

const selectItemView = computed(() => {
  return isMobile.value ? 'radio' : 'default'
})
/**
 *
 * @param {import('@internationalized/date').CalendarDate} date
 */
function isDateUnavailable(date) {
  // check if  date is in the range of
  // min: new Date(1923, 0, 1),
  // max: new Date(2123, 11, 31),
  return date.compare(new CalendarDate(1923, 0, 1)) < 0 || date.compare(new CalendarDate(2123, 12, 31)) > 0
}
</script>

<template>
  <CalendarRoot
    v-slot="{ grid }"
    v-bind="forwarded"
    v-model:placeholder="placeholder"
    v-model="model"
    :initial-focus="false"
    class="Calendar"
    :class="[{ 'Calendar--compact': compact }]"
    :week-starts-on="1"
    :is-date-unavailable="isDateUnavailable"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <CalendarHeader class="CalendarHeader">
      <FcSelect
        v-model="selectedMonth"
        :options="monthOptions"
        variant="plain"
        size="small"
        searchable
        :fit-trigger-width="false"
        trigger-class="Calendar__MonthSelect"
        :item-view="selectItemView"
        v-bind="selectProps"
        :tablet-view="tabletView"
      />
      <FcSelect
        v-model="selectedYear"
        :options="yearOptions"
        variant="plain"
        size="small"
        searchable
        :fit-trigger-width="false"
        trigger-class="Calendar__YearSelect"
        :item-view="selectItemView"
        v-bind="selectProps"
        :tablet-view="tabletView"
      />
    </CalendarHeader>
    <div
      class="CalendarWrapper"
    >
      <CalendarGrid
        v-for="month in grid"
        :key="month.value.toString()"
        class="CalendarGrid"
      >
        <CalendarGridBody class="CalendarGridWrapper">
          <CalendarGridRow
            v-for="(weekDates, index) in month.rows"
            :key="`weekDate-${index}`"
            class="CalendarGridRow"
          >
            <CalendarCell
              v-for="weekDate in weekDates"
              :key="weekDate.toString()"
              :date="weekDate"
              class="CalendarCell"
            >
              <CalendarCellTrigger
                :day="weekDate"
                :month="month.value"
                class="CalendarCellTrigger"
                @click="emit('cell-click', weekDate)"
              />
            </CalendarCell>
          </CalendarGridRow>
        </CalendarGridBody>
      </CalendarGrid>
      <slot name="footer" />
    </div>
  </CalendarRoot>
</template>

<style>
.Calendar {
  width: 310px;
  border: none;
  border-radius: 0;
  background-color: #ffffff;
  padding: 15px 20px 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.CalendarHeader {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(231, 235, 237, 1);
}

.Calendar__YearSelect {
  width: 85px;
  flex: 0 0 auto;
}

.CalendarNavButton {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 2.5rem;
  height: 2.5rem;
  color: #000000;
  background-color: transparent;
  cursor: pointer;
}

.CalendarNavButton:hover {
  color: #ffffff;
  background-color: #000000;
}

.CalendarHeading {
  font-weight: 500;
  color: #000000;
  color: 15px;
}

.CalendarWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
}

.CalendarGrid {
  width: 100%;
  user-select: none;
  border-collapse: collapse;
}

.CalendarGridRow {
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  width: 100%;
  height: 36px;
}

.CalendarHeadCell {
  border-radius: 0.375rem;
  font-size: 16px;
  line-height: 1.1;
  color: #000000;
  font-weight: 400;
}

.CalendarCell {
  position: relative;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-align: center;
}

.CalendarCellTrigger {
  display: flex;
  position: relative;
  padding: 0.5rem;
  justify-content: center;
  align-items: center;
  outline-style: none;
  font-size: 16px;
  line-height: 1.1;
  font-weight: 400;
  color: #000000;
  white-space: nowrap;
  background-color: transparent;
  transition:
    opacity 0.3s,
    background-color 0.3s,
    color 0.3s;
  border-radius: 8px;
  background-color: transparent;
  cursor: pointer;
}

.CalendarCellTrigger:not([data-selected]):hover {
  background-color: #eee;
}

.CalendarCellTrigger:focus {
  outline: none;
}

.CalendarCellTrigger[data-disabled] {
  color: rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 0, 0, 0.4);
}

.CalendarCellTrigger[data-selected] {
  background-color: var(--fqz-poll-main-color) !important;
  color: #ffffff;
}

.CalendarCellTrigger[data-today] {
  background-color: #eee;
}

.CalendarCellTrigger[data-unavailable] {
  color: rgba(0, 0, 0, 0.2) !important;
  pointer-events: none;
}

.Calendar--compact {
  padding: 10px;
  width: 280px;
}

.Calendar--compact .CalendarCellTrigger {
  padding: 6px;
}

.Calendar--compact .CalendarHeader {
  padding-bottom: 6px;
}

.Calendar--compact .CalendarGridRow {
  height: auto;
}

.Calendar--compact .CalendarCellTrigger {
  padding: 6px;
}
</style>
