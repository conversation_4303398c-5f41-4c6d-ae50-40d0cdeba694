<script setup>
import { DialogContent, DialogOverlay, DialogPortal, useEmitAsProps } from 'radix-vue'
import { computed } from 'vue'

const props = defineProps({
  asChild: <PERSON><PERSON><PERSON>,
  disableOutsidePointerEvents: <PERSON><PERSON><PERSON>,
  forceMount: <PERSON><PERSON><PERSON>,
  trapFocus: <PERSON><PERSON><PERSON>,
  fullScreen: <PERSON><PERSON><PERSON>,
})
const emits = defineEmits(['close'])

const emitsAsProps = useEmitAsProps(emits)
const dialogContentClasses = computed(() => {
  return {
    'dialog-content': true,
    'dialog-content--full-screen': props.fullScreen,
  }
})
</script>

<template>
  <DialogPortal>
    <DialogOverlay />
    <DialogContent v-bind="{ ...props, ...emitsAsProps }" :class="dialogContentClasses">
      <slot />
    </DialogContent>
  </DialogPortal>
</template>

<style scoped>
.dialog-content {
  width: 500px;
  height: 500px;
  display: flex;
  background-color: #fff;
  border-radius: 10px;
}

.dialog-content--full-screen {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}
.dialog-content[data-state='open'] {
  z-index: var(--z-index-modal);
}
</style>
