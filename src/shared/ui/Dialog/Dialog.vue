<script setup>
import { DialogContent, DialogOverlay, DialogPortal, DialogRoot, DialogTrigger, useForwardPropsEmits } from 'radix-vue'
import { computed, onMounted, ref } from 'vue'

const props = defineProps({
  open: Boolean,
  defaultOpen: Boolean,
  showClose: {
    type: Boolean,
    default: true,
  },
  modal: {
    type: Boolean,
    default: true,
  },
  contentClass: {
    type: String,
    default: '',
  },
  showOverlay: {
    type: Boolean,
    default: true,
  },
  fullScreen: {
    type: Boolean,
    default: false,
  },
  disableOutsidePointerEvents: {
    type: Boolean,
    default: false,
  },
  portalTo: {
    type: Object,
    default: null,
  },
  contentStyle: {
    type: Object,
    default: () => {},
  },
})

const emit = defineEmits(['update:open', 'closeAutoFocus', 'focusOutside', 'interactOutside'])

const forwarded = useForwardPropsEmits(props, emit)
const documentBody = ref(document?.body || null)

const safePortalTo = computed(() => {
  return props.portalTo || documentBody.value
})

onMounted(() => {
  documentBody.value = document?.body || null
})

const classes = computed(() => {
  return {
    'DialogRoot': true,
    'DialogRoot--modal': props.modal,
    'DialogRoot--full-screen': props.fullScreen,
  }
})
const dialogContentClasses = computed(() => {
  let classes = {
    'DialogContent': true,
    'DialogContent--full-screen': props.fullScreen,
  }

  if (!props.contentClass) {
    return classes
  }

  const propsContentClass = typeof props.contentClass === 'string' ? props.contentClass.split(' ') : props.contentClass

  if (Array.isArray(propsContentClass)) {
    const transformedClasses = propsContentClass.reduce((acc, cls) => {
      acc[cls] = true
      return acc
    }, {})
    classes = { ...classes, ...transformedClasses }
  }
  else if (typeof propsContentClass === 'object') {
    classes = { ...classes, ...propsContentClass }
  }
  else if (typeof propsContentClass === 'string') {
    classes = { ...classes, [propsContentClass]: true }
  }

  return classes
})
</script>

<template>
  <DialogRoot v-bind="forwarded" :class="classes" :modal="props.modal">
    <DialogTrigger v-if="$slots.trigger" as-child>
      <slot name="trigger" />
    </DialogTrigger>
    <DialogPortal :to="safePortalTo">
      <DialogOverlay v-if="!props.fullScreen && props.showOverlay" class="DialogOverlay" />
      <DialogContent :style="contentStyle" :class="dialogContentClasses" @open-auto-focus.prevent @close-auto-focus="$emit('closeAutoFocus', $event)" @focus-outside="$emit('focusOutside', $event)" @interact-outside="$emit('interactOutside', $event)">
        <slot />
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<style>
.DialogOverlay {
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  inset: 0;
  z-index: var(--z-index-modal-backdrop);
  /* animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1); */
}

@keyframes contentHide {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes contentShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.DialogContent {
  background-color: white;
  border-radius: 6px;
  box-shadow:
    hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
    hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 450px;
  max-height: 85vh;
  padding: 25px;
  display: flex;
  flex-direction: column;
  border-radius: 9px;
  overflow: hidden;
  transition: none;
  z-index: var(--z-index-modal);
  opacity: 1;
  animation-duration: 0.3s;
  animation-timing-function: ease-in-out;
  pointer-events: auto;
}

.DialogContent[data-state='open'] {
  animation-name: contentShow;
  animation-fill-mode: forwards;
}

.DialogContent[data-state='closed'] {
  animation-duration: 0.3s;
  animation-name: contentHide;
}

.DialogContent:focus {
  outline: none;
}

.DialogTitle {
  margin: 0;
  font-weight: 500;
  color: var(--mauve-12);
  font-size: 17px;
}

.DialogDescription {
  margin: 10px 0 20px;
  color: var(--mauve-11);
  font-size: 15px;
  line-height: 1.5;
}

.DialogContent--full-screen {
  width: 100%;
  height: 100%;
  max-height: none;
  max-width: none;
  border-radius: 0;
  top: 0;
  left: 0;
  transform: none;
  padding: 0;
  gap: 15px;
}

@keyframes overlayShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
