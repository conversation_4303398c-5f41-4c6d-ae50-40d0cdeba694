<script setup>
import { DialogClose } from 'radix-vue'

defineProps({
  type: {
    type: String,
    // can be 'reset' or 'close'
    default: 'reset',
  },
})

defineEmits(['click'])
</script>

<template>
  <DialogClose as-child @click.prevent="$emit('click')">
    <button v-if="type === 'reset'" class="close-button close-button--reset">
      <svg width="14" height="14" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 2L16 16M16 2L2 16" stroke="#F96261" stroke-width="3" stroke-linecap="round" />
      </svg>
    </button>
    <button v-else class="close-button close-button--close">
      <svg width="21" height="14" viewBox="0 0 24 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1.5 7.96607L9.71971 16.0728L22.5 1.92676" stroke="#00C968" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </button>
  </DialogClose>
</template>

<style scoped>
.close-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  width: 100%;
  height: 36px;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
}

.close-button--reset {
  border: 2px solid rgba(249, 98, 97, 1);
}

.close-button--close {
  border: 2px solid rgba(0, 201, 104, 1);
}
</style>
