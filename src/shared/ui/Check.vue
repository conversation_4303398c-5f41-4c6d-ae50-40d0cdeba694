<script setup>
import { computed, ref } from 'vue'
import Hint from './Hint.vue'

const props = defineProps({
  type: {
    type: String,
    default: 'checkbox',
    validator: value => ['radio', 'checkbox'].includes(value),
  },
  checked: {
    // can be 'intermediate' | true | false
    type: [Boolean, String],
    default: false,
  },
  value: {
    type: [String, Number, Boolean],
    default: true,
  },
  hint: {
    type: String,
    default: null,
  },
  label: {
    type: String,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  onChange: {
    type: Function,
    default: null,
  },
  partial: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: null,
  },
  modelValue: {
    type: [String, Number, Boolean, Array],
    default: undefined,
  },
  invalid: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'click'])

const inputRef = ref(null)

const checkComputedValue = computed(() => {
  if (props.modelValue === undefined) {
    return props.checked
  }

  if (Array.isArray(props.modelValue)) {
    return props.modelValue.includes(props.value)
  }

  return props.modelValue === props.value
})

const isChecked = computed(() => {
  return checkComputedValue.value
})

const isIntermediate = computed(() => {
  return props.checked === 'intermediate'
})

const inputAttrs = computed(() => ({
  type: props.type,
  ...props.$attrs,
}))

function focus() {
  inputRef.value?.focus()
}

defineExpose({
  inputRef,
  inputAttrs,
  focus,
})

function onChange(event) {
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()
  if (props.onChange) {
    props.onChange(event)
  }
  if (props.disabled || props.modelValue === undefined) {
    return
  }

  if (Array.isArray(props.modelValue)) {
    const hasValue = props.modelValue.includes(props.value)
    const isRadio = props.type === 'radio'
    if (!hasValue) {
      emit('update:modelValue', isRadio ? [props.value] : [...props.modelValue, props.value])
    }
    else {
      emit('update:modelValue', isRadio ? [props.value] : props.modelValue.filter(v => v !== props.value))
    }
  }
  else {
    if (props.type === 'radio') {
      emit('update:modelValue', props.value)
    }
    else {
      emit('update:modelValue', props.modelValue !== props.value)
    }
  }
}
</script>

<template>
  <div
    class="fc-check"
    :class="{
      'fc-check--checked': isChecked,
      'fc-check--intermediate': isIntermediate,
      'fc-check--disabled': disabled,
      'fc-check--inactive': inactive,
      'fc-check--sm': size === 'sm',
      'fc-check--radio': type === 'radio',
      'fc-check--checkbox': type === 'checkbox',
      'fc-check--with-hint': hint,
      'fc-check--invalid': invalid,
    }"
    @click="$emit('click', $event)"
  >
    <label :data-checked="isChecked" :data-value="value">
      <input v-bind="inputAttrs" :value="value" :checked="isChecked" name="check" class="fc-check__input" @change="onChange">

      <div class="fc-check__box">
        <span class="fc-check__marker">
          <svg
            v-if="type === 'checkbox' && !isIntermediate" width="10" height="8" viewBox="0 0 10 8" fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.49373 0.758435C8.83831 0.413855 9.39698 0.413855 9.74156 0.758435C10.0861 1.10302 10.0861 1.66169 9.74156 2.00627L4.44745 7.30039C4.10287 7.64497 3.54419 7.64497 3.19961 7.30039L0.258435 4.35921C-0.0861451 4.01463 -0.0861451 3.45596 0.258435 3.11138C0.603015 2.7668 1.16169 2.7668 1.50627 3.11138L3.82353 5.42864L8.49373 0.758435Z"
              fill="#3F65F1"
            />
          </svg>
        </span>
      </div>

      <div class="fc-check__label">
        <div v-if="label" class="fc-check__label-text">
          {{ label }} <span v-if="hint" class="fc-check__hint">
            <Hint :text="hint" />
          </span>
        </div>
        <slot v-else />
      </div>
    </label>
  </div>
</template>

<style scoped>
.fc-check {
  display: block;
  cursor: pointer;
  transition: opacity 0.3s;
}

.fc-check label {
  display: flex;
  cursor: pointer;
}

.fc-check__input {
  display: block !important;
  opacity: 0;
  position: absolute;
  clip: rect(0, 0, 0, 0);
}

.fc-check__box {
  display: block;
  width: 22px;
  height: 22px;
  border: 1px solid rgba(207, 216, 220, 1);
  background-color: white;
  margin-right: 15px;
  border-radius: 4px;
  position: relative;
  flex-shrink: 0;
  transition:
    border-color 0.3s,
    background-color 0.3s,
    opacity 0.3s;
}

.fc-check__marker {
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.fc-check__label {
  color: var(--f-color-service);
  min-height: 0;
  padding-top: 4.2px;
  text-align: left;
  line-height: 1.1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  gap: 4px;
}

.fc-check__label-text {
  display: block;
  gap: 10px;
  align-items: center;
}

.fc-check__label-after {
  display: inline-flex;
  vertical-align: middle;
  flex: 0 0 auto;
}

.fc-check--checkbox .fc-check__marker {
  width: 10px;
  height: 8px;
}

.fc-check--checkbox .fc-check__marker svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.fc-check--checkbox .fc-check__marker svg path {
  fill: currentColor;
}

.fc-check--radio .fc-check__box {
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

.fc-check--radio .fc-check__marker {
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

.fc-check--invalid .fc-check__box {
  border-color: #ff0000;
}

.fc-check--checked .fc-check__box .fc-check__marker {
  opacity: 1;
}

.fc-check--intermediate .fc-check__box .fc-check__marker {
  border-radius: 1px !important;
}

.fc-check--intermediate .fc-check__marker {
  border-radius: 1px !important;
  width: 8px;
  height: 8px;
  background-color: white;
}

.fc-check--intermediate .fc-check__box .fc-check__marker {
  opacity: 1;
}

.fc-check--checked .fc-check__label {
  color: var(--f-color-text);
}

.fc-check--checked .fc-check__box .fc-check__marker {
  opacity: 1;
}

.fc-check--checked .fc-check__label {
  color: var(--f-color-text);
}

.fc-check__input:focus ~ .fc-check__box {
  box-shadow: none;
}

.fc-check--checked .fc-check__box {
  border-color: var(--fqz-poll-main-color);
  background-color: var(--fqz-poll-main-color);
  color: white;
}

.fc-check--disabled label {
  cursor: not-allowed;
}
.fc-check--disabled,
.fc-check--inactive {
  opacity: 0.7;
}

.fc-check--sm .fc-check__box {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}

.fc-check--sm .fc-check__label {
  min-height: 19px;
  font-size: 12px;
}

.fc-check--sm.fc-check--radio .fc-check__marker {
  width: 6px;
  height: 6px;
}

.fc-check--checkbox.fc-check--partial .fc-check__marker {
  opacity: 1;
  width: 8px;
  height: 8px;
  background: #3f65f1;
  box-shadow: 0px 7px 64px rgba(0, 0, 0, 0.07);
  border-radius: 2px;
}

.fc-check--with-hint :deep(.hint-trigger) {
  width: 17px;
  height: 15px;
  top: 1px;
  position: relative;
}

.fc-check--with-hint .fc-check__label {
  display: inline-block;
  padding-top: 2px;
}

.fc-check--with-hint .fc-check__label-text {
  display: inline-block;
}

.fc-check--with-hint .fc-check__hint {
  margin-left: 5px;
  vertical-align: middle;
  display: inline-block;
  position: relative;
}

@media (max-width: 679px) {
  .fc-check__label {
    font-size: 14px;
    padding-top: 4.5px;
  }
}
</style>
