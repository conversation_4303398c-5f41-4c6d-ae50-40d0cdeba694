<script setup>
import { dispatchGlobalEvent } from '@shared/helpers/dom'
import { computed, ref } from 'vue'

const props = defineProps({
  fade: {
    type: Boolean,
    default: false,
  },
  skipInitialAnimation: {
    type: Boolean,
    default: false,
  },
})

const el = ref(null)

function beforeEnter(element) {
  if (props.skipInitialAnimation) {
    return
  }

  el.value = element
  const height = getElementHeight(element)
  element.style.overflow = 'hidden'
  element.style.setProperty('--height', `${height}px`)
}

function enter(element) {
  if (props.skipInitialAnimation) {
    return
  }

  const height = getElementHeight(element)
  element.style.setProperty('--height', `${height}px`)
}

function afterEnter(element) {
  element.style.height = ''
  element.style.overflow = ''
  element.style.transition = ''

  dispatchGlobalEvent('resize')
}

function beforeLeave(element) {
  el.value = element
  element.style.overflow = 'hidden'
  element.style.setProperty('--height', `${element.scrollHeight}px`)
}

function afterLeave(element) {
  element.style.height = ''
  element.style.overflow = ''
  element.style.transition = ''

  dispatchGlobalEvent('resize')
}

function getElementHeight(element) {
  const prevHeight = element.style.height
  element.style.height = 'auto'
  const height = element.scrollHeight
  element.style.height = prevHeight
  return height
}

const transitionName = computed(() => {
  return props.fade ? 'slide-fade-transition' : 'slide-transition'
})
</script>

<template>
  <Transition
    :name="transitionName"
    @before-enter="beforeEnter"
    @enter="enter"
    @after-enter="afterEnter"
    @before-leave="beforeLeave"
    @after-leave="afterLeave"
  >
    <slot />
  </Transition>
</template>

<style scoped>
.slide-transition-enter-active,
.slide-transition-leave-active {
  transition: height 300ms ease-out;
}

.slide-transition-enter-from {
  height: 0;
}
.slide-transition-enter-to {
  height: var(--height);
}

.slide-transition-leave-from {
  height: var(--height);
}
.slide-transition-leave-to {
  height: 0;
}

.slide-fade-transition-enter-active,
.slide-fade-transition-leave-active {
  transition:
    opacity 0.3s ease,
    height 0.3s ease;
}

.slide-fade-transition-enter-from {
  opacity: 0;
  height: 0;
}
.slide-fade-transition-enter-to {
  opacity: 1;
  height: var(--height);
}
.slide-fade-transition-leave-from {
  opacity: 1;
  height: var(--height);
}
.slide-fade-transition-leave-to {
  opacity: 0;
  height: 0;
}
</style>
