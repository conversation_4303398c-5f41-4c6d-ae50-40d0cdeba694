<script setup>
defineProps({
  color: {
    type: String,
    default: 'rgba(0, 0, 0, 0.5)',
  },
  size: {
    type: String,
    default: '1em',
  },
})
</script>

<template>
  <div class="spinner" :style="{ width: size, height: size, borderColor: color }">
    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.0625 2.0625C13.0625 1.50391 12.8477 1.03125 12.4609 0.601562C12.0312 0.214844 11.5586 0 11 0C10.3984 0 9.92578 0.214844 9.53906 0.601562C9.10938 1.03125 8.9375 1.50391 8.9375 2.0625C8.9375 2.66406 9.10938 3.13672 9.53906 3.52344C9.92578 3.95312 10.3984 4.125 11 4.125C11.5586 4.125 12.0312 3.95312 12.4609 3.52344C12.8477 3.13672 13.0625 2.66406 13.0625 2.0625ZM11 17.875C11.5586 17.875 12.0312 18.0898 12.4609 18.4766C12.8477 18.9062 13.0625 19.3789 13.0625 19.9375C13.0625 20.5391 12.8477 21.0117 12.4609 21.3984C12.0312 21.8281 11.5586 22 11 22C10.3984 22 9.92578 21.8281 9.53906 21.3984C9.10938 21.0117 8.9375 20.5391 8.9375 19.9375C8.9375 19.3789 9.10938 18.9062 9.53906 18.4766C9.92578 18.0898 10.3984 17.875 11 17.875ZM19.9375 8.9375C20.4961 8.9375 20.9688 9.15234 21.3984 9.53906C21.7852 9.96875 22 10.4414 22 11C22 11.6016 21.7852 12.0742 21.3984 12.4609C20.9688 12.8906 20.4961 13.0625 19.9375 13.0625C19.3359 13.0625 18.8633 12.8906 18.4766 12.4609C18.0469 12.0742 17.875 11.6016 17.875 11C17.875 10.4414 18.0469 9.96875 18.4766 9.53906C18.8633 9.15234 19.3359 8.9375 19.9375 8.9375ZM4.125 11C4.125 11.6016 3.91016 12.0742 3.52344 12.4609C3.09375 12.8906 2.62109 13.0625 2.0625 13.0625C1.46094 13.0625 0.988281 12.8906 0.601562 12.4609C0.171875 12.0742 0 11.6016 0 11C0 10.4414 0.171875 9.96875 0.601562 9.53906C0.988281 9.15234 1.46094 8.9375 2.0625 8.9375C2.62109 8.9375 3.09375 9.15234 3.52344 9.53906C3.91016 9.96875 4.125 10.4414 4.125 11ZM4.68359 15.2539C5.24219 15.2539 5.71484 15.4688 6.14453 15.8555C6.53125 16.2852 6.74609 16.7578 6.74609 17.3164C6.74609 17.918 6.53125 18.3906 6.14453 18.7773C5.71484 19.207 5.24219 19.3789 4.68359 19.3789C4.08203 19.3789 3.60938 19.207 3.22266 18.7773C2.79297 18.3906 2.62109 17.918 2.62109 17.3164C2.62109 16.7578 2.79297 16.2852 3.22266 15.8555C3.60938 15.4688 4.08203 15.2539 4.68359 15.2539ZM17.3164 15.2539C17.875 15.2539 18.3477 15.4688 18.7773 15.8555C19.1641 16.2852 19.3789 16.7578 19.3789 17.3164C19.3789 17.918 19.1641 18.3906 18.7773 18.7773C18.3477 19.207 17.875 19.3789 17.3164 19.3789C16.7148 19.3789 16.2422 19.207 15.8555 18.7773C15.4258 18.3906 15.2539 17.918 15.2539 17.3164C15.2539 16.7578 15.4258 16.2852 15.8555 15.8555C16.2422 15.4688 16.7148 15.2539 17.3164 15.2539ZM4.68359 2.62109C5.24219 2.62109 5.71484 2.83594 6.14453 3.22266C6.53125 3.65234 6.74609 4.125 6.74609 4.68359C6.74609 5.28516 6.53125 5.75781 6.14453 6.14453C5.71484 6.57422 5.24219 6.74609 4.68359 6.74609C4.08203 6.74609 3.60938 6.57422 3.22266 6.14453C2.79297 5.75781 2.62109 5.28516 2.62109 4.68359C2.62109 4.125 2.79297 3.65234 3.22266 3.22266C3.60938 2.83594 4.08203 2.62109 4.68359 2.62109Z" fill="currentColor" />
    </svg>
  </div>
</template>

<style scoped>
.spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: v-bind(size);
  height: v-bind(size);
  color: v-bind(color);
}

.spinner svg {
  width: 100%;
  height: 100%;
  animation-name: fc-spinner-rotation;
  animation-duration: 2000ms;
  animation-iteration-count: infinite;
  animation-timing-function: steps(8);
}

@keyframes fc-spinner-rotation {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
