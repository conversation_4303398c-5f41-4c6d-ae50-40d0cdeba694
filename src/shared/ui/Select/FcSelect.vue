<script setup>
import Command from '@/shared/ui/Command/Command.vue'
import CommandContent from '@/shared/ui/Command/CommandContent.vue'
import CommandItem from '@/shared/ui/Command/CommandItem.vue'
import Icon from '@/shared/ui/Icon.vue'
import { useCompactMode } from '@shared/composables/useCompactMode'
import { Dialog, DialogMobileClose } from '@shared/ui/Dialog'
import Popover from '@shared/ui/Popover/Popover.vue'
import { createReusableTemplate, useMediaQuery } from '@vueuse/core'
import { useForwardPropsEmits } from 'radix-vue'

import { computed, ref, toValue, watch } from 'vue'

const props = defineProps({
  options: Array,
  placeholder: {
    type: String,
    default: '',
  },
  modelValue: [String, Array],
  itemView: {
    type: String,
  },
  fullWidth: {
    type: Boolean,
    default: false,
  },
  variant: {
    // can be 'outline' or 'plain'
    type: String,
    default: 'outline',
  },
  size: {
    // can be 'small', 'medium', 'large'
    type: String,
    default: 'medium',
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  searchable: {
    type: Boolean,
    default: false,
  },
  searchAutofocus: {
    type: Boolean,
    default: false,
  },
  triggerClass: {
    type: String,
    default: '',
  },
  defaultValue: [String, Array],
  modelValue: Array,
  searchPlaceholderText: {
    type: String,
    default: 'Поиск',
  },
  itemClass: {
    type: Function,
    default: () => '',
  },
  triggerItemClass: {
    type: Function,
    default: () => '',
  },
  popoverContentClass: {
    type: String,
    default: '',
  },
  nothingFoundText: {
    type: String,
    default: 'Совпадений не найдено',
  },
  invalid: {
    type: Boolean,
    default: false,
  },
  success: {
    type: Boolean,
    default: false,
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'update:open'])
const computedOptions = computed(() => {
  return props.options.map(option => ({
    ...option,
    label: toValue(option.label),
  }))
})

const forward = useForwardPropsEmits(props, emit)

const searchQuery = ref('')
const chosenValue = ref(null)

function filterFn(items, query) {
  const queryLower = query.toLowerCase()
  if (!queryLower || !items || items.length === 0) {
    return items
  }
  const isLabelString = typeof toValue(items[0].label) === 'string'
  return items.filter((item) => {
    if (isLabelString) {
      return toValue(item.label).toLowerCase().includes(queryLower)
    }
    return toValue(item.label).toString().includes(queryLower)
  })
}

const hasSelectedValue = computed(() => {
  const value = toValue(forward).modelValue
  if (Array.isArray(value)) {
    return value.length > 0
  }
  return !!value
})

const selectTriggerOption = computed(() => {
  const value = toValue(props.modelValue)
  if (!hasSelectedValue.value) {
    return {
      label: props.placeholder || '',
      description: '',
    }
  }

  return Array.isArray(value) ? value[0] : value
})

const selectTriggerClasses = computed(() => {
  return {
    'select-trigger--full-width': props.fullWidth,
    'select-trigger--plain': props.variant === 'plain',
    'select-trigger--small': props.size === 'small',
    'select-trigger--medium': props.size === 'medium',
    'select-trigger--large': props.size === 'large',
    'select-trigger--invalid': props.invalid,
    'select-trigger--success': props.success && hasSelectedValue.value,
    'select-trigger--single': !props.multiple,
    'select-trigger--multiple': props.multiple,
    [props.triggerClass]: !!props.triggerClass,
  }
})

const selectTriggerLabelClasses = computed(() => {
  return {
    'select-trigger__label--placeholder': !hasSelectedValue.value,
    'select-trigger__label--has-description': !!selectTriggerOption.value?.description,
    [props.triggerItemClass(selectTriggerOption.value)]: !!props.triggerItemClass,
  }
})

const isOpen = ref(false)
const selectTriggerRef = ref(null)
const popoverRef = ref(null)
const isMobile = useMediaQuery('(max-width: 679px)')
const useDialogView = computed(() => isMobile.value || props.tabletView)

const { isCompactMode } = useCompactMode({
  minHeight: 380,
})

watch(isOpen, (newVal) => {
  emit('update:open', newVal)
  if (newVal) {
    window.dispatchEvent(new CustomEvent('dialog:open'))
  }
  else {
    window.dispatchEvent(new CustomEvent('dialog:close'))
  }
})

function onSelect(option, event) {
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()
  if (props.multiple) {
    const currentValue = toValue(props.modelValue) || []
    const currentValueCopy = [...currentValue]
    const index = currentValueCopy.findIndex(item => item.id === option.id)
    if (index > -1) {
      currentValueCopy.splice(index, 1)
    }
    else {
      // Add the new option and sort the array based on the original props.options order
      currentValueCopy.push(option)
      currentValueCopy.sort((a, b) => {
        const indexA = props.options.findIndex(opt => opt.id === a.id)
        const indexB = props.options.findIndex(opt => opt.id === b.id)
        return indexA - indexB
      })
    }

    emit('update:modelValue', currentValueCopy)
  }
  else {
    emit('update:modelValue', option)
    isOpen.value = false
  }
}

function isSelected(option) {
  const value = toValue(forward).modelValue

  if (!value) {
    return false
  }
  if (Array.isArray(value)) {
    return value.some(item => item.id === option.id)
  }
  return value.id === option.id
}

const chevronSize = computed(() => {
  return props.size === 'small' ? { width: '13px', height: '7px' } : { width: '14px', height: '9px' }
})

function onResetClick() {
  isOpen.value = false
  if (props.multiple) {
    const resetValue = chosenValue.value || props.defaultValue || []
    emit('update:modelValue', resetValue)
  }
}

function onCloseClick() {
  isOpen.value = false
  chosenValue.value = props.modelValue
}

function onClear(e) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  emit('update:modelValue', props.defaultValue)
}

const selectedOptions = computed(() => {
  const value = toValue(forward).modelValue
  if (Array.isArray(value)) {
    return value
  }
  return value ? [value] : []
})

const [DefineTriggerTemplate, ReuseTriggerTemplate] = createReusableTemplate()

const dialogCommandItemView = computed(() => {
  const singleChoiceView = props.itemView || 'radio'

  if (props.tabletView && props.multiple) {
    return 'checkbox'
  }
  else if (props.tabletView) {
    return 'radio'
  }

  return props.multiple ? 'checkbox' : singleChoiceView
})

function onDialogInteractOutside(e) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
}

/**
 * Пропсы для позиционирования выпадающего списка
 */
const popoverContentProps = computed(() => ({
  align: isCompactMode.value ? 'end' : 'start',
  side: isCompactMode.value ? 'top' : 'bottom',
  collisionPadding: isCompactMode.value ? 10 : 0,
  prioritizePosition: true,
}))

const popoverComputedClasses = computed(() => ({
  'select-popover-content': true,
  'select-popover-content--compact': isCompactMode.value,
  [props.popoverContentClass]: !!props.popoverContentClass,
}))
</script>

<template>
  <DefineTriggerTemplate>
    <button ref="selectTriggerRef" class="select-trigger" :class="selectTriggerClasses">
      <div v-if="!multiple" class="select-trigger__label" :class="selectTriggerLabelClasses">
        <template v-if="$slots.selectedItem && hasSelectedValue">
          <slot name="selectedItem" :option="selectTriggerOption" />
        </template>
        <template v-else-if="!hasSelectedValue && placeholder">
          {{ placeholder }}
        </template>
        <template v-else>
          <div class="select-trigger__label-text">
            {{ selectTriggerOption?.label }}
          </div>
          <div v-if="selectTriggerOption?.description" class="select-trigger__label-description">
            {{ selectTriggerOption?.description }}
          </div>
        </template>
      </div>
      <div v-else class="select-trigger__labels">
        <div v-if="modelValue.length === 0" class="select-trigger__label select-trigger__label--placeholder">
          {{ placeholder }}
        </div>
        <div
          v-for="option in selectedOptions"
          :key="option.id"
          class="select-trigger__label"
          :class="{
            'select-trigger__label--has-description': !!option.description,
            [props.triggerItemClass(option)]: !!props.triggerItemClass,
          }"
        >
          <template v-if="$slots.selectedItem">
            <slot name="selectedItem" :option="option" />
          </template>
          <template v-else>
            <div class="select-trigger__label-text">
              {{ option.label }}
            </div>
            <div v-if="option.description" class="select-trigger__label-description">
              {{ option.description }}
            </div>
          </template>
        </div>
      </div>
      <span v-if="clearable && hasSelectedValue" class="select-trigger__clear" @click="onClear">
        <Icon name="times-small" class="select-trigger__icon" size="12px" />
      </span>
      <Icon name="chevron-bottom" class="select-trigger__icon" :width="chevronSize.width" :height="chevronSize.height" />
    </button>
  </DefineTriggerTemplate>

  <Popover
    v-if="!useDialogView"
    v-bind="forward"
    ref="popoverRef"
    v-model:open="isOpen"
    fit-trigger-width
    :class="popoverComputedClasses"
    :modal="false"
    :content-props="popoverContentProps"
  >
    <template #trigger>
      <ReuseTriggerTemplate />
    </template>
    <template v-if="$slots.anchor" #anchor>
      <slot name="anchor" />
    </template>
    <Command v-bind="forward" v-model:search-term="searchQuery" :default-value="props.defaultValue" :filter-function="filterFn" :default-open="true" :multiple="props.multiple">
      <CommandContent :searchable="props.searchable" size="small" aria-hidden="false" data-aria-hidden="false" :placeholder-text="toValue(searchPlaceholderText)" :empty-text="toValue(nothingFoundText)" :compact="isCompactMode">
        <CommandItem v-for="option in computedOptions" :key="option.id" :value="option" :view="props.multiple ? 'checkbox' : 'default'" :selected="isSelected(option)" :class="itemClass(option)" :disabled="option.disabled" @select.prevent="onSelect(option, $event)">
          <template v-if="$slots.option">
            <slot name="option" :option="option" :selected="isSelected(option)" />
          </template>
          <template v-else>
            <div class="select-item__label-text">
              {{ option.label }}
            </div>
            <div v-if="option.description" class="select-item__label-description">
              {{ option.description }}
            </div>
          </template>
        </CommandItem>
      </CommandContent>
    </Command>
  </Popover>
  <Dialog v-else v-model:open="isOpen" :modal="false" :full-screen="true" :show-overlay="false" @interact-outside="onDialogInteractOutside" @focus-outside="onDialogInteractOutside">
    <template #trigger>
      <ReuseTriggerTemplate />
    </template>
    <Command v-bind="forward" v-model:search-term="searchQuery" view="modal" class="command-dialog" :placeholder-text="toValue(searchPlaceholderText)" :empty-text="nothingFoundText" :filter-function="filterFn">
      <CommandContent :searchable="searchable" :search-autofocus="searchAutofocus" view="modal" :placeholder-text="toValue(searchPlaceholderText)" :empty-text="nothingFoundText" :compact="isCompactMode">
        <CommandItem v-for="option in computedOptions" :key="option.id" :value="option" :view="dialogCommandItemView" :selected="isSelected(option)" :class="itemClass(option)" :disabled="option.disabled" @select.prevent="onSelect(option, $event)">
          <template v-if="$slots.option">
            <slot name="option" :option="option" :selected="isSelected(option)" />
          </template>
          <template v-else>
            <div class="select-item__label-text">
              {{ option.label }}
            </div>
            <div v-if="option.description" class="select-item__label-description select-item__label-description--dialog">
              {{ option.description }}
            </div>
          </template>
        </CommandItem>
        <template #footer>
          <div class="select-mobile-footer">
            <DialogMobileClose type="reset" @click="onResetClick" />
            <DialogMobileClose v-if="multiple" type="close" @click="onCloseClick" />
          </div>
        </template>
      </CommandContent>
    </Command>
  </Dialog>
</template>

<style>
.select-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 16px;
  line-height: 1;
  gap: 5px;
  position: relative;
  z-index: 4;
  display: flex;
  background-color: white;
  color: #000;
  outline: none;
  border: 1px solid rgba(207, 216, 220, 1);
  cursor: pointer;
  min-height: 45px;
  width: 100%;
  min-width: 0;
  max-width: 220px;
  transition: border-color 0.2s ease-in-out;
  --success-color: rgba(0, 201, 104, 1);
}

.select-trigger:before {
  content: '';
  position: absolute;
  top: -7px;
  right: -7px;
  width: 100%;
  height: 100%;
  background-color: var(--success-color);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-image: url('data:image/svg+xml,%3Csvg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath fill-rule="evenodd" clip-rule="evenodd" d="M7.68008 1.67039C8.05032 1.26058 8.01825 0.62823 7.60844 0.25799C7.19864 -0.112251 6.56628 -0.0801771 6.19604 0.329628L3.2826 3.55441L1.7022 1.99573C1.30898 1.60792 0.675829 1.6123 0.288017 2.00552C-0.0997954 2.39873 -0.095414 3.03188 0.297804 3.41969L2.62205 5.71199C2.81628 5.90355 3.08035 6.00745 3.35304 5.99959C3.62573 5.99174 3.88338 5.87281 4.06626 5.67039L7.68008 1.67039Z" fill="white"/%3E%3C/svg%3E');
  background-repeat: no-repeat;
  background-position: center;
  transition:
    opacity 0.3s,
    transform 0.3s;
  opacity: 0;
  transform: scale(0);
  pointer-events: none;
}

.select-trigger[data-state='open'],
.select-trigger:focus-visible {
  border-color: var(--fqz-poll-main-color);
  z-index: 100;
}

.select-trigger:not([data-state='open']):hover {
  border: 1px solid rgb(193, 193, 193);
}

.select-trigger--plain {
  border: none !important;
}

.select-trigger--invalid {
  border-color: red !important;
}

.select-trigger--small {
  min-height: 35px;
  padding: 0 10px 0 10px;
}

.select-trigger--success {
  border-color: rgba(0, 201, 104, 1) !important;
}

.select-trigger--success:before {
  opacity: 1;
  transform: scale(1);
}

.select-trigger--multiple {
  padding: 10px 15px;
}

.select-trigger--single:not(.select-trigger--placeholder) .select-trigger__label {
  padding-top: 10px;
  padding-bottom: 10px;
}

.select-trigger--small .select-trigger__label {
  font-size: 16px;
}

.select-trigger--full-width {
  width: 100%;
  max-width: none;
}

.select-trigger:focus {
  box-shadow: none;
  outline: none;
}

.select-trigger[data-placeholder] {
  color: rgba(0, 0, 0, 0.6);
}

.select-trigger__label {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.select-trigger__label-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.select-trigger__label-description {
  font-size: 12px;
  line-height: 1.1;
  margin-top: 1px;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.select-trigger__labels {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 5px;
  height: 100%;
  text-align: left;
}

.select-trigger__labels .select-trigger__label {
  line-height: 1.1;
  flex: 0 0 auto;
}

.select-trigger__label--placeholder {
  color: rgba(0, 0, 0, 0.6);
}

.select-trigger__icon {
  flex-shrink: 0;
  flex-grow: 0;
  margin-right: -1px;
  margin-top: 2px;
}

.select-trigger__clear {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  margin-right: -5px;
  margin-top: -1px;
  height: 100%;
  transition: opacity 0.3s;
  flex-shrink: 0;
  flex-grow: 0;
  cursor: pointer;
}

.select-trigger__clear:hover {
  opacity: 0.7;
}

.select-popover-content {
  max-height: 320px;
  min-width: 220px;
}

.select-popover-content--compact {
  max-height: var(--radix-popover-content-available-height);
}

.select-viewport {
  padding: 5px;
}

.select-item {
  font-size: 13px;
  line-height: 1;
  color: #000;
  border-radius: 3px;
  display: flex;
  align-items: center;
  height: 25px;
  padding: 0 35px 0 25px;
  position: relative;
  user-select: none;
}

.select-item[data-disabled] {
  color: #999;
  pointer-events: none;
}

.select-item[data-highlighted] {
  outline: none;
  background-color: #f4f4f4;
  color: #000;
}

.select-item__label-text,
.select-item__label-description {
  position: relative;
  z-index: 1;
}

.select-label {
  padding: 0 25px;
  font-size: 12px;
  line-height: 25px;
  color: #999;
}

.select-item-indicator {
  position: absolute;
  left: 0;
  width: 25px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.select-scroll-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 25px;
  background-color: white;
  color: #999;
  cursor: default;
}

.search-container {
  padding: 5px;
}

.search-input {
  width: 100%;
  padding: 5px;
  font-size: 13px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.select-mobile-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.select-item__label-description {
  font-size: 12px;
  line-height: 1.1;
  color: rgba(0, 0, 0, 0.6);
}

.select-item__label-description--dialog {
  margin-top: 0;
}

@media (max-width: 679px) {
  .select-trigger__label {
    line-height: 1.1;
  }
  .select-item__label-description {
    line-height: 1.2;
  }
}
</style>
