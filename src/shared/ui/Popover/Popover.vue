<script setup>
import { <PERSON>overAnchor, PopoverContent, PopoverPortal, PopoverRoot, PopoverTrigger, useForwardPropsEmits } from 'radix-vue'
import { computed, ref } from 'vue'

const props = defineProps({
  defaultOpen: {
    type: Boolean,
    default: false,
  },
  side: {
    type: String,
    default: 'bottom',
  },
  modal: {
    type: Boolean,
    default: false,
  },
  open: {
    type: Boolean,
    default: false,
  },
  class: {
    type: String,
    default: '',
  },
  fitTriggerWidth: {
    type: Boolean,
    default: false,
  },
  trapFocus: {
    type: Boolean,
    default: true,
  },
  contentProps: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:open', 'focus-outside', 'interact-outside', 'open-auto-focus'])

const popoverContentRef = ref(null)

const delegatedProps = computed(() => {
  const { class: _, contentRef, ...delegated } = props
  return delegated
})

const forward = useForwardPropsEmits(delegatedProps, emit)
const popoverContentClasses = computed(() => {
  return {
    'popover__content': true,
    'popover__content--fit-trigger-width': props.fitTriggerWidth,
    [props.class]: true,
  }
})

defineExpose({
  contentRef: popoverContentRef,
})
</script>

<template>
  <PopoverRoot v-bind="forward" class="popover" @enter-key-down.prevent @enter.prevent>
    <PopoverTrigger v-if="$slots.trigger" as-child>
      <slot name="trigger" />
    </PopoverTrigger>
    <PopoverAnchor v-if="$slots.anchor" as-child>
      <slot name="anchor" />
    </PopoverAnchor>
    <PopoverPortal>
      <PopoverContent
        v-bind="contentProps"
        ref="popoverContentRef"
        :class="popoverContentClasses"
        :trap-focus="trapFocus"
        update-position-strategy="always"
        @focus-outside="emit('focus-outside', $event)"
        @interact-outside="emit('interact-outside', $event)"
        @open-auto-focus="emit('open-auto-focus', $event)"
        @enter-key-down.prevent
        @enter.prevent
      >
        <slot />
      </PopoverContent>
    </PopoverPortal>
  </PopoverRoot>
</template>

<style>
[data-radix-popper-content-wrapper] {
  z-index: var(--z-index-popover);
}
.popover {
  z-index: var(--z-index-popover);
}
.popover__content {
  overflow: hidden;
  display: flex;
  background-color: white;
  border-radius: 6px;
  box-shadow:
    0px 10px 38px -10px rgba(22, 23, 24, 0.35),
    0px 10px 20px -15px rgba(22, 23, 24, 0.2);
}

.popover__content--fit-trigger-width {
  width: var(--radix-popover-trigger-width);
}

.popover__content[data-state='open'] {
  z-index: var(--z-index-popover);
  animation: contentAnimationUp 400ms cubic-bezier(0.16, 1, 0.3, 1);
}
.popover__content[data-state='closed'] {
  animation: contentAnimationDown 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes contentAnimationDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(4px);
  }
}

@keyframes contentAnimationUp {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
