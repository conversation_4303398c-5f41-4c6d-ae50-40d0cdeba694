<script setup>
defineProps({
  error: {
    type: String,
    default: null,
  },
  success: {
    type: Boolean,
    default: false,
  },
  view: {
    type: String,
    // filled | default
    default: 'default',
  },
  style: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<template>
  <span
    v-if="error"
    data-validation-error
    class="validationMessage"
    :class="[
      { 'validationMessage--filled': view === 'filled' },
      { 'validationMessage--success': success },
    ]"
    :style="style"
  >
    {{ error }}
  </span>
</template>

<style scoped>
.validationMessage {
  font-size: 12px;
  line-height: 1.2;
  color: #ea1d27;
}

.validationMessage--filled {
  font-size: 12px;
  line-height: 1.2;
  background-color: rgba(249, 98, 97, 1);
  color: #fff;
  padding: 10px 15px;
  min-height: 34px;
  border-radius: 8px;
}
.validationMessage--success.validationMessage--filled {
  background-color: #fff;
  color: #000000;
}
</style>
