<script setup>
import DialogAlert from '@/shared/ui/DialogAlert.vue'
import { ref, watch } from 'vue'
import { useTranslationsStore } from '../store/translationsStore'

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
})

const translationsStore = useTranslationsStore()
const t = translationsStore.t

const isOpen = ref(false)
const isHighlighted = ref(false)

function copyText() {
  navigator.clipboard.writeText(props.text)
}

function onClick() {
  copyText()
  isOpen.value = true
  highlightText()
}

function highlightText() {
  isHighlighted.value = true
  let count = 0
  const interval = setInterval(() => {
    isHighlighted.value = !isHighlighted.value
    count++
    if (count >= 3) {
      clearInterval(interval)
      isHighlighted.value = false
    }
  }, 500)
}

watch(isOpen, (v) => {
  if (!v)
    isHighlighted.value = false
})
</script>

<template>
  <DialogAlert v-model:open="isOpen" :text="t('Скопировано')" :dismiss-after="2000" variant="success">
    <template #anchor>
      <span class="copyable-variable">
        <span class="copyable-variable__text" :class="{ 'copyable-variable__text--highlighted': isHighlighted, 'copyable-variable__text--copied': isOpen }">
          {{ text }}
        </span>
        <button type="button" class="copyable-variable__button" @click.prevent="onClick">
          <span class="copyable-variable__button-icon">
            <svg width="19" height="21" viewBox="0 0 19 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 13H16C17.1046 13 18 12.1046 18 11V3C18 1.89543 17.1046 1 16 1H10C8.89543 1 8 1.89543 8 3V5M3 20H9C10.1046 20 11 19.1046 11 18V10C11 8.89543 10.1046 8 9 8H3C1.89543 8 1 8.89543 1 10V18C1 19.1046 1.89543 20 3 20Z" stroke="currentColor" stroke-width="2" />
            </svg>
          </span>
        </button>
      </span>
    </template>
    <template #content>
      <span class="copyable-variable__alert-text">{{ t('Скопировано') }}</span>
    </template>
  </DialogAlert>
</template>

<style scoped>
.copyable-variable {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: inherit !important;
}

.copyable-variable__text {
  transition: background-color 0.6s ease;
}

.copyable-variable__text--highlighted.copyable-variable__text--copied {
  transition: background-color 0.6s ease;
  background-color: var(--fqz-poll-main-color);
}

.copyable-variable__text--highlighted:not(.copyable-variable__text--copied) {
  transition: none !important;
  background-color: transparent;
}

.copyable-variable__button {
  all: unset;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
  transition: opacity 0.3s;
}

.copyable-variable__button:hover {
  opacity: 0.8;
}

.copyable-variable__button-icon {
  transition:
    transform 0.3s,
    opacity 0.3s;
  display: block;
}

.copyable-variable__button-icon svg {
  width: 1em;
  height: unset;
  overflow: hidden;
  display: block;
}
</style>
