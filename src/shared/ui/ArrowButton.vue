<script setup>
defineProps({
  direction: {
    type: String,
    required: true,
    validator: value => ['back', 'next'].includes(value),
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <button
    type="button"
    class="arrow-button"
    :class="[`arrow-button--${direction}`]"
    :disabled="disabled"
  >
    <svg class="arrow-button__icon" width="32" height="22" viewBox="0 0 32 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M30.5 11H1.5M30.5 11L21 1.5M30.5 11L21 20.5" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  </button>
</template>

<style scoped>
.arrow-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  width: 58px;
  height: 58px;
  background-color: transparent;
  border-radius: 50%;
  border: 2px solid currentColor;
  cursor: pointer;
  transition: opacity 0.3s ease;
  padding: 0;
  margin: 0;
}

.arrow-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.arrow-button:not(:disabled):hover {
  opacity: 0.7;
}

.arrow-button__icon {
  width: 24px;
  height: 24px;
  fill: none;
  stroke: white;
  stroke-width: 2;
}

.arrow-button--back .arrow-button__icon {
  transform: rotate(180deg);
}
</style>
