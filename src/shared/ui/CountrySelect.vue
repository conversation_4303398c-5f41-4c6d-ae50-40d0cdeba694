<script setup>
import FcSelect from '@shared/ui/Select/FcSelect.vue'
import { computed, toValue } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  placeholder: {
    type: String,
    default: 'Select a country',
  },
  countries: {
    type: Array,
    default: () => [],
  },
  view: {
    type: String,
    // 'default' | 'compact'
    default: 'default',
  },
  disableOutAnimation: {
    type: Boolean,
    default: false,
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
  triggerClass: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue'])

const selectedCountry = computed({
  get: () => {
    const mv = toValue(props.modelValue)
    if (!mv) {
      return null
    }
    if (mv.value) {
      return mv
    }
    return {
      id: mv.code,
      value: mv.code,
      label: mv.name,
      img: mv.img,
    }
  },
  set: value => emit('update:modelValue', value),
})

const countryOptions = computed(() =>
  props.countries.map(country => ({
    ...country,
    id: country.code,
    value: country.code,
    label: country.name,
    img: country.img,
  })),
)
const triggerClasses = computed(() => {
  const cls = 'country-select-trigger'
  let result = cls

  if (props.view === 'compact') {
    result = `${result} ${cls}--compact`
  }

  if (props.triggerClass) {
    result = `${result} ${props.triggerClass}`
  }

  return result
})

function getItemClass() {
  const cls = 'command-item--country'
  if (props.view === 'compact') {
    return `${cls} command-item--compact`
  }
  return cls
}

const triggerItemClass = computed(() => {
  const cls = 'country-selected-item'
  if (props.view === 'compact') {
    return `${cls} ${cls}--compact`
  }
  return cls
})

const optionClass = computed(() => {
  return {
    'country-option': true,
    'country-option--compact': props.view === 'compact',
  }
})

const popoverContentClass = computed(() => {
  return {
    'country-select-popover-content--disable-out-animation': props.disableOutAnimation,
  }
})
</script>

<template>
  <FcSelect
    v-model="selectedCountry"
    :options="countryOptions"
    :placeholder="placeholder"
    :searchable="false"
    :clearable="false"
    :item-class="getItemClass"
    :trigger-class="triggerClasses"
    :popover-content-class="popoverContentClass"
    :tablet-view="tabletView"
    item-view="default"
  >
    <template #selectedItem="{ option }">
      <div :class="triggerItemClass">
        <img :src="option.img" :alt="option.label" class="country-selected-item__img">
        <span class="country-selected-item__name">{{ option.label }}</span>
      </div>
    </template>
    <template #option="{ option }">
      <div :class="optionClass">
        <img :src="option.img" :alt="option.label" class="country-img">
        <span class="country-option__name">{{ option.label }}</span>
      </div>
    </template>
  </FcSelect>
</template>

<style scoped>
:global(.country-select-trigger) {
  min-width: 140px;
  max-width: 290px;
  width: auto;
  gap: 10px;
}

:global(.country-select-trigger .select-trigger__label) {
  overflow: visible;
}

.country-option {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.country-img {
  width: 18px;
  height: 12px;
  object-fit: cover;
}

.country-img {
  display: block;
}

.country-selected-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.country-selected-item__img {
  width: 18px;
  height: 12px;
  object-fit: cover;
}

.country-selected-item__name {
  font-size: 16px !important;
  line-height: 1 !important;
}

.country-option__name {
  font-size: 16px !important;
  line-height: 1 !important;
  transition: opacity 0.3s;
}

:global(.country-select-popover-content--disable-out-animation[data-state='closed']) {
  animation: none !important;
}

:global(
    .command-item--country[data-state='checked'],
    .command-item--country:not(.command-item--checkbox):not(.command-item--radio)
  ) {
  opacity: 1 !important;
}

:global(.command-item--country[data-state='checked'] .country-option__name) {
  opacity: 0.5;
}

:global(.country-select-trigger--compact) {
  padding: 0 10px;
  min-height: 30px;
}

:global(.country-select-trigger--compact .select-trigger__label) {
  padding: 0 !important;
  font-size: 12px !important;
}

:global(.country-select-trigger--compact .country-selected-item__name) {
  font-size: 13px !important;
  line-height: 1.3;
}

:global(.country-select-trigger--compact .fc-icon) {
  width: 13px !important;
  height: 7px !important;
  margin-top: 1px;
}

@media (max-width: 1023px) {
  :global(.country-select-trigger--compact .country-selected-item__name) {
    display: none;
  }
  :global(.country-select-trigger--compact) {
    min-width: 0;
    max-width: none;
    gap: 12px;
  }
}
</style>
