<script setup>
import Spinner from './Spinner.vue'

defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
  text: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <span class="loadable-text" :class="{ 'loadable-text--loading': isLoading }">
    <Spinner v-if="isLoading" color="currentColor" />
    <span v-else>{{ text }}</span>
  </span>
</template>

<style scoped>
.loadable-text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loadable-text--loading {
  position: relative;
  top: 0.15em;
}

.loadable-text > :deep(.spinner) {
  width: 1em;
  height: 1em;
  vertical-align: middle;
}
</style>
