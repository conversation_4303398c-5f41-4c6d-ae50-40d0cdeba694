<script setup>
import { useTranslationsStore } from '@/shared/store/translationsStore'
import { useVModel } from '@vueuse/core'
import { computed, ref, watch } from 'vue'
import Input from './Input.vue'
import ModalSuggestionsList from './ModalSuggestionsList.vue'
import SuggestionsList from './SuggestionsList.vue'

const props = defineProps({
  modelValue: String,
  placeholder: String,
  fetchSuggestions: {
    type: Function,
    required: true,
  },
  debounce: {
    type: Number,
    default: 500,
  },
  minLength: {
    type: Number,
    default: 1,
  },
  isInvalid: {
    type: Boolean,
    default: false,
  },
  view: {
    type: String,
    default: 'desktop',
  },
  disablePopup: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'select', 'update:hasSuggestions'])

const inputValue = useVModel(props, 'modelValue', emit)
const suggestions = ref([])
const isLoading = ref(false)
const showSuggestions = ref(false)
const highlightedIndex = ref(-1)
const selectedIndex = ref(-1)
const inputWrapperRef = ref('')
const clicked = ref(false)

let debounceTimer = null

const hasSuggestions = computed(() => inputValue.value && suggestions.value.length > 0)

watch(hasSuggestions, (newValue) => {
  emit('update:hasSuggestions', newValue)
})

watch(inputValue, (newValue) => {
  if (newValue.length >= props.minLength && !clicked.value) {
    debounceFetch(newValue)
  }
  else {
    // suggestions.value = []
    if (props.view !== 'mobile') {
      showSuggestions.value = false
    }
  }
})

function debounceFetch(query) {
  clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    fetchSuggestions(query)
  }, props.debounce)
}

async function fetchSuggestions(query) {
  isLoading.value = true
  showSuggestions.value = true
  try {
    suggestions.value = await props.fetchSuggestions(query)
  }
  catch (error) {
    console.error('Error fetching suggestions:', error)
    suggestions.value = []
  }
  finally {
    isLoading.value = false
  }
}

function selectSuggestion(suggestion) {
  if (suggestion) {
    inputValue.value = suggestion.name
    emit('select', suggestion)
  }
  selectedIndex.value = highlightedIndex.value
  clicked.value = true
  showSuggestions.value = false
  highlightedIndex.value = -1
  setTimeout(() => {
    clicked.value = false
  }, 100)
}

function onKeyDown(event) {
  if (showSuggestions.value) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        highlightedIndex.value = (highlightedIndex.value + 1) % suggestions.value.length
        break
      case 'ArrowUp':
        event.preventDefault()
        highlightedIndex.value = (highlightedIndex.value - 1 + suggestions.value.length) % suggestions.value.length
        break
      case 'Enter':
        event.preventDefault()
        if (highlightedIndex.value !== -1) {
          selectSuggestion(suggestions.value[highlightedIndex.value])
        }
        break
      case 'Escape':
        showSuggestions.value = false
        break
    }
  }
}

function inputWrapperRefFn(el) {
  inputWrapperRef.value = el

  if (inputWrapperRef.value) {
    const $input = inputWrapperRef.value.querySelector('input')
    $input.addEventListener('focus', () => {
      showSuggestions.value = true
    })
  }
}
const translationsStore = useTranslationsStore()
const notFoundText = computed(() => translationsStore.t('Совпадений не найдено'))

const showDesktopSuggestions = computed(() => props.view === 'desktop' && showSuggestions.value && suggestions.value.length > 0)
const showMobileSuggestions = computed(() => props.view === 'mobile' && showSuggestions.value)
</script>

<template>
  <div class="autocomplete">
    <Input
      v-model="inputValue"
      :is-invalid="isInvalid"
      :wrapper-ref="inputWrapperRefFn"
      :placeholder="placeholder"
      :maxlength="999"
      @keydown="onKeyDown"
      @focus="showSuggestions = true"
    />
    <Teleport to="body">
      <Transition name="fade-up">
        <SuggestionsList
          v-if="showDesktopSuggestions && !disablePopup"
          :suggestions="suggestions"
          :is-loading="isLoading"
          :highlighted-index="highlightedIndex"
          :selected-index="selectedIndex"
          :input-ref="inputWrapperRef"
          :not-found-text="notFoundText"
          @select="selectSuggestion"
          @update:selected-index="highlightedIndex = $event"
        />
      </Transition>
      <ModalSuggestionsList
        v-if="showMobileSuggestions && !disablePopup"
        :suggestions="suggestions"
        :is-loading="isLoading"
        :highlighted-index="highlightedIndex"
        :selected-index="selectedIndex"
        :input-value="inputValue"
        :placeholder="placeholder"
        :not-found-text="notFoundText"
        @select="selectSuggestion"
        @update:selected-index="highlightedIndex = $event"
        @update:input-value="inputValue = $event"
        @close="showSuggestions = false"
      />
    </Teleport>
  </div>
</template>

<style scoped>
.autocomplete {
  position: relative;
}

.fade-up-enter-active,
.fade-up-leave-active {
  transition:
    opacity 0.2s,
    transform 0.2s;
}

.fade-up-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.fade-up-enter-to {
  opacity: 1;
  transform: translateY(0);
}

.fade-up-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.fade-up-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
