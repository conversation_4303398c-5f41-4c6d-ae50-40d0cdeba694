<script setup>
import { useCompactMode } from '@shared/composables/useCompactMode'
import Calendar from '@shared/ui/Calendar/Calendar.vue'
import { Dialog, DialogMobileClose } from '@shared/ui/Dialog'
import MaskedField from '@shared/ui/MaskedField.vue'

import Popover from '@shared/ui/Popover/Popover.vue'
import { useMediaQuery } from '@vueuse/core'
import { useForwardPropsEmits } from 'radix-vue'
import { computed, ref, watch } from 'vue'

const props = defineProps({
  placeholder: {
    type: String,
    default: () => '00.00.0000',
  },
  selectProps: {
    type: Object,
    default: () => ({}),
  },
  monthOptions: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Date,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  open: {
    type: Boolean,
    default: false,
  },
  calendarProps: {
    type: Object,
    default: () => ({}),
  },
  invalid: {
    type: Boolean,
    default: false,
  },
  inputAttrs: {
    type: Object,
    default: () => ({}),
  },
  align: {
    type: String,
    default: undefined,
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'update:open', 'opened'])

const textModel = defineModel('text', {
  type: String,
  required: true,
})

const isOpen = ref(false)
const forceClose = ref(false)
const forwarded = useForwardPropsEmits(props, emit)
const isMobile = useMediaQuery('(max-width: 679px)')
const useDialogView = computed(() => isMobile.value || props.tabletView)
const chosenDate = ref(props.modelValue)
const inputRef = ref(null)

const { isCompactMode } = useCompactMode({
  minHeight: 340, // POPUP_DATE_PICKER_MAX_HEIGHT
})

/**
 * Пропсы для позиционирования выпадающего списка
 */
const popoverContentProps = computed(() => ({
  align: props.align || 'end',
  side: isCompactMode.value ? 'top' : 'bottom',
  collisionPadding: 10,
  prioritizePosition: true,
}))

function onOpenChange() {
  isOpen.value = true
  emit('opened')
}

function onResetClick() {
  isOpen.value = false

  if (!props.modelValue) {
    textModel.value = ''
  }
  if (chosenDate.value) {
    emit('update:modelValue', chosenDate.value)
  }
  else {
    emit('update:modelValue', null)
    textModel.value = ''
  }
}

function onCloseClick() {
  isOpen.value = false
  chosenDate.value = props.modelValue
}

function onFocusOutside(e) {
  if (forceClose.value) {
    return
  }
  const input = inputRef.value
  if (input && input instanceof HTMLElement && input.contains(e.target)) {
    e.preventDefault()
  }
}

function onInteractOutside(e) {
  const input = inputRef.value
  if (input && input instanceof HTMLElement && input.contains(e.target)) {
    e.preventDefault()
  }
}

function onDialogInteractOutside() {
  if (useDialogView.value && chosenDate.value) {
    emit('update:modelValue', chosenDate.value)
  }
}

function onCellClick(_e) {
  isOpen.value = false
}

watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    textModel.value = ''
  }
}, { deep: true })

function onEnter(e) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  forceClose.value = true

  setTimeout(() => {
    const form = document.querySelector('form')
    if (form) {
      form.requestSubmit()
    }
  }, 10)
}

function onWrapperRef(wrapperRef) {
  inputRef.value = wrapperRef
}

function onIconButtonClick(e) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    emit('opened')
  }
}

watch(() => forceClose.value, (newVal) => {
  if (newVal) {
    isOpen.value = false

    setTimeout(() => {
      forceClose.value = false
    }, 100)
  }
})

watch(isOpen, (newVal) => {
  if (newVal) {
    window.dispatchEvent(new CustomEvent('dialog:open'))
  }
  else {
    window.dispatchEvent(new CustomEvent('dialog:close'))
  }
})

const popoverComputedClasses = computed(() => ({
  'date-picker-content': true,
  'date-picker-content--compact': isCompactMode.value,
}))

const iconButtonName = computed(() => {
  if (props.tabletView) {
    return 'calendar-detailed'
  }
  return 'calendar'
})
</script>

<template>
  <Popover v-if="!useDialogView && !forceClose" v-model:open="isOpen" :class="popoverComputedClasses" :trap-focus="false" :content-props="popoverContentProps" @focus-outside="onFocusOutside" @interact-outside="onInteractOutside" @open-auto-focus.prevent>
    <template #anchor>
      <MaskedField ref="inputRef" v-model="textModel" mask="date" :placeholder="placeholder" :icon-button="iconButtonName" data-datepicker-field :invalid="invalid" :wrapper-ref="onWrapperRef" :input-attrs="inputAttrs" @focus="onOpenChange" @icon-button-click="onIconButtonClick" @enter="onEnter" />
    </template>
    <Calendar v-model="forwarded.modelValue" :month-options="monthOptions" :select-props="selectProps" :compact="isCompactMode" @update:model-value="$emit('update:modelValue', $event)" @cell-click="onCellClick" />
  </Popover>
  <template v-else>
    <MaskedField v-model="textModel" mask="date" :placeholder="placeholder" :icon-button="iconButtonName" data-datepicker-field :invalid="invalid" :input-attrs="inputAttrs" @icon-button-click="onIconButtonClick" @focus="onOpenChange" />
    <Dialog v-if="!forceClose" v-model:open="isOpen" content-class="date-picker-dialog-content" :trap-focus="true" side="bottom-end" :show-overlay="true" @close-auto-focus.prevent @interact-outside="onDialogInteractOutside">
      <Calendar v-model="forwarded.modelValue" :month-options="monthOptions" :initial-focus="true" :select-props="selectProps" :compact="isCompactMode" :tablet-view="useDialogView" @update:model-value="$emit('update:modelValue', $event)">
        <template #footer>
          <div class="date-picker__calendar-footer">
            <DialogMobileClose type="reset" @click="onResetClick" />
            <DialogMobileClose type="close" @click="onCloseClick" />
          </div>
        </template>
      </Calendar>
    </Dialog>
  </template>
</template>

<style>
.date-picker-content {
  padding: 0;
  border-radius: 9px;
  max-height: 320px;
}

.date-picker-dialog-content {
  padding: 0;
  width: auto;
  height: auto;
  border-radius: 9px;
}
.date-picker__calendar-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}
</style>
