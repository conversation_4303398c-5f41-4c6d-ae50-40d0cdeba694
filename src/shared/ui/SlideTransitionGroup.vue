<script setup>
import { ref } from 'vue'

const el = ref(null)

function beforeEnter(element) {
  el.value = element
  const height = getElementHeight(element)
  element.style.overflow = 'hidden'
  element.style.setProperty('--height', `${height}px`)
}

function enter(element) {
  const height = getElementHeight(element)
  element.style.setProperty('--height', `${height}px`)
}

function afterEnter(element) {
  element.style.height = ''
  element.style.overflow = ''
  element.style.transition = ''
}

function beforeLeave(element) {
  el.value = element
  element.style.overflow = 'hidden'
  element.style.setProperty('--height', `${element.scrollHeight}px`)
}

function afterLeave(element) {
  element.style.height = ''
  element.style.overflow = ''
  element.style.transition = ''
}

function getElementHeight(element) {
  const prevHeight = element.style.height
  element.style.height = 'auto'
  const height = element.scrollHeight
  element.style.height = prevHeight
  return height
}
</script>

<template>
  <TransitionGroup
    name="slide-list"
    v-bind="$attrs"
    @before-enter="beforeEnter"
    @enter="enter"
    @after-enter="afterEnter"
    @before-leave="beforeLeave"
    @after-leave="afterLeave"
  >
    <slot />
  </TransitionGroup>
</template>

<style>
.slide-list-enter-active,
.slide-list-leave-active {
  transition: height 300ms ease-out;
}

.slide-list-enter-from {
  height: 0;
}
.slide-list-enter-to {
  height: var(--height);
}

.slide-list-leave-from {
  height: var(--height);
}
.slide-list-leave-to {
  height: 0;
}
</style>
