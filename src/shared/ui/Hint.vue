<script setup>
import { useTranslationsStore } from '@shared/store/translationsStore'
import Dialog from '@shared/ui/Dialog/Dialog.vue'
import Tooltip from '@shared/ui/Tooltip/Tooltip.vue'
import { useMediaQuery } from '@vueuse/core'
import { DialogClose } from 'radix-vue'
import { computed } from 'vue'
import { useTabletStore } from '../store/useTabletStore'

defineProps({
  text: {
    type: String,
    required: true,
  },
  triggerClass: {
    type: String,
    default: '',
  },
  dialogCloseText: {
    type: String,
    default: 'Закрыть',
  },
})

// @TODO: Это нужно отрефакторить, чтобы не тащить сторы из обычного компонента
// а передавать нужные данные через пропсы
const translationsStore = useTranslationsStore()
const tabletStore = useTabletStore()

const isMobile = useMediaQuery('(max-width: 679px)')
const renderTooltipComponent = computed(() => {
  if (tabletStore.isTabletMode || isMobile.value) {
    return false
  }
  return true
})
</script>

<template>
  <Tooltip v-if="renderTooltipComponent" view="hint">
    <template #trigger>
      <button class="hint-trigger" :class="triggerClass" type="button">
        <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill="none" d="M8 12.9951V12.9851M6 6.98511C6 5.88054 6.89543 4.98511 8 4.98511C9.10457 4.98511 10 5.88054 10 6.98511C10 7.49734 9.80743 7.9646 9.49074 8.31844C9.12452 8.72761 8 9.48511 8 10.4851M8 15.9851C11.866 15.9851 15 12.8511 15 8.98511C15 5.11911 11.866 1.98511 8 1.98511C4.13401 1.98511 1 5.11911 1 8.98511C1 12.8511 4.13401 15.9851 8 15.9851Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
        </svg>
      </button>
    </template>
    <template #content>
      <p>{{ text }}</p>
    </template>
  </Tooltip>
  <Dialog v-else content-class="dialog-hint-content" :show-overlay="false">
    <template #trigger>
      <button class="hint-trigger" :class="triggerClass" type="button">
        <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill="none" d="M8 12.9951V12.9851M6 6.98511C6 5.88054 6.89543 4.98511 8 4.98511C9.10457 4.98511 10 5.88054 10 6.98511C10 7.49734 9.80743 7.9646 9.49074 8.31844C9.12452 8.72761 8 9.48511 8 10.4851M8 15.9851C11.866 15.9851 15 12.8511 15 8.98511C15 5.11911 11.866 1.98511 8 1.98511C4.13401 1.98511 1 5.11911 1 8.98511C1 12.8511 4.13401 15.9851 8 15.9851Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
        </svg>
      </button>
    </template>
    <div class="dialog-hint-content__inner">
      <p class="dialog-hint-content__text">
        {{ text }}
      </p>
      <DialogClose as-child>
        <button class="dialog-hint-content__close" type="button">
          {{ translationsStore.t('Закрыть') }}
        </button>
      </DialogClose>
    </div>
  </Dialog>
</template>

<style scoped>
.hint {
  color: inherit;
}

.hint-trigger {
  all: unset;
  display: inline-flex;
  cursor: pointer;
  width: 24px;
  height: 24px;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
  opacity: 0.4;
}

.hint-trigger:hover {
  opacity: 1;
}

.hint-trigger svg {
  display: block;
  fill: currentColor;
}

.hint-trigger:focus {
  outline: none;
}

.hint-trigger:focus-visible {
  outline: 2px solid var(--fqz-poll-main-color);
}

:global(.dialog-hint-content) {
  padding: 20px;
  background-color: var(--fqz-poll-main-place-color);
  box-shadow: none;
  overflow: visible;
  border-radius: 9px;
  color: var(--fqz-poll-text-on-place);
  max-width: calc(100% - 30px);
  width: 100%;
}

:global(.dialog-hint-content::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0px 0px 15px 0px var(--fqz-poll-text-on-place);
  border-radius: 9px;
  z-index: -1;
  opacity: 0.35;
}

.dialog-hint-content__inner {
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;
  z-index: 1;
}

.dialog-hint-content__text {
  margin: 0;
  font-size: 13px;
  line-height: 1.3;
  text-align: center;
}

.dialog-hint-content__close {
  all: unset;
  cursor: pointer;
  text-align: center;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  line-height: 1.1;
  padding: 10px;
  background-color: transparent;
  font-weight: 600;
  margin-bottom: -10px;
}

@media (max-width: 679px) {
  .hint-trigger {
    width: 32px;
    height: 32px;
  }
}
</style>
