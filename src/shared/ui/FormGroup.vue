<script setup>
import { ref } from 'vue'
import FormError from './FormError.vue'

const props = defineProps({
  customClass: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  label: {
    type: String,
    default: '',
  },
  labelFor: {
    type: String,
    default: '',
  },
  success: {
    type: String,
    default: '',
  },
  successAttrs: {
    type: Object,
    default: () => ({}),
  },
  successView: {
    type: String,
    default: 'default',
  },
  error: {
    type: String,
    default: '',
  },
  errorAttrs: {
    type: Object,
    default: () => ({}),
  },
  errorView: {
    type: String,
    default: 'default',
  },
})

const errorElHeight = ref(0)

function beforeLeave(el) {
  errorElHeight.value = el.offsetHeight
}

function afterLeave() {
  errorElHeight.value = 0
}
</script>

<template>
  <div class="form-group" :class="props.customClass" :style="{ '--error-el-height': `${errorElHeight || 0}px` }">
    <label v-if="label" :for="labelFor" class="form-group__label" :class="{ 'form-group__label--required': props.required }">{{ label }}</label>
    <slot />
    <Transition name="fade-up-and-down" @before-leave="beforeLeave" @after-leave="afterLeave">
      <div v-if="error" class="form-group__error-container">
        <FormError :error="error" class="form-group__error" v-bind="errorAttrs" :view="errorView" />
        <slot name="error-text-after" />
      </div>
    </Transition>
    <Transition name="fade-up-and-down" @before-leave="beforeLeave" @after-leave="afterLeave">
      <div v-if="success" class="form-group__error-container">
        <FormError :error="success" :success="true" class="form-group__error" v-bind="successAttrs" :view="successView" />
        <slot name="error-text-after" />
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.form-group {
  width: 100%;
  min-width: 0;
}

.form-group__error {
  margin-top: 10px;
  display: block;
}

.form-group__label {
  font-size: inherit;
  color: inherit;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
  overflow: hidden;
}

.form-group__label--required::after {
  content: '*';
  color: inherit;
}

@media screen and (max-width: 679px) {
  .form-group__label {
    font-size: 14px;
    line-height: 1.1;
  }
}
</style>
