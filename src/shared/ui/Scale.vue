<script setup>
import { computed, ref, watch } from 'vue'
import Masked<PERSON><PERSON> from './MaskedField.vue'
import Slider from './Slider.vue'

const props = defineProps({
  modelValue: {
    type: Number,
    required: true,
  },
  min: {
    type: Number,
    required: true,
  },
  max: {
    type: Number,
    required: true,
  },
  step: {
    type: Number,
    default: 1,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  invalid: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'inputFocus', 'pointerdown'])

const sliderValue = computed({
  get: () => props.modelValue,
  set: newValue => emit('update:modelValue', newValue),
})

const inputValue = ref(props.modelValue.toString())

watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue.toString()
})

function updateFromInput(value) {
  const numValue = Number(value)
  if (!Number.isNaN(numValue)) {
    const roundedValue = Math.round(numValue / props.step) * props.step
    emit('update:modelValue', [Math.min(Math.max(roundedValue, props.min), props.max)])
  }
  else {
    emit('update:modelValue', [props.min])
  }
}

const inputAttrs = computed(() => ({
  inputmode: 'numeric',
}))

function onFocus() {
  emit('inputFocus')
}

function onBlur() {
  updateFromInput(inputValue.value)
}

function onEnter(e) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  updateFromInput(inputValue.value)
}
</script>

<template>
  <div class="scale-container" :class="{ 'scale-container--disabled': disabled }">
    <Slider
      v-model="sliderValue"
      :min="min"
      :max="max"
      :step="step"
      class="scale-slider"
      @pointerdown="emit('pointerdown', $event)"
    />
    <MaskedField
      v-model="inputValue"
      mask="digit"
      :input-attrs="inputAttrs"
      :min="min"
      :max="max"
      :invalid="invalid"
      class="scale-input"
      @focus="onFocus"
      @blur="onBlur"
      @enter="onEnter"
    />
  </div>
</template>

<style scoped>
.scale-container {
  display: flex;
  align-items: center;
  gap: 30px;
  width: 100%;
  padding-top: 10px;
  transition: opacity 0.3s;
}
.scale-container--disabled {
  opacity: 0.7;
}

.scale-slider {
  flex-grow: 1;
  width: 100%;
}

.scale-input {
  width: 60px;
  text-align: center;
}
.scale-input :deep(.fc-input__wrapper) {
  padding: 0;
}

@media (max-width: 679px) {
  .scale-container {
    gap: 24px;
    flex-direction: column;
    width: 100%;
    padding-top: 32px;
  }
  .scale-input {
    width: 100%;
  }
}
</style>
