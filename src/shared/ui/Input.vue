<script setup>
import { computed, onMounted, ref } from 'vue'
import Icon from './Icon.vue'

const props = defineProps({
  readMode: Boolean,
  icon: Object,
  modelValue: String,
  iconButton: String,
  mask: String,
  clearable: <PERSON><PERSON>an,
  counter: <PERSON><PERSON><PERSON>,
  appendIcon: Object,
  inputAttrs: Object,
  isInvalid: Boolean,
  placeholder: String,
  maxlength: {
    type: Number,
    default: () => 250,
  },
  wrapperRef: {
    type: Object,
  },
  autofocus: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'enter', 'iconButtonClick', 'focus', 'blur'])
const isFocused = ref(false)

const input = ref(null)
const inputValue = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

const count = computed(() => inputValue.value.length)

onMounted(() => {
  if (props.autofocus) {
    input.value.focus()
  }
})

function onFocus(e) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  isFocused.value = true
  emit('focus', e)
}

function onBlur(e) {
  isFocused.value = false
  emit('blur', e)
}

function onIconButtonClick(e) {
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()
  emit('iconButtonClick', e)
}
function onEnter(e) {
  emit('enter', e)
}
</script>

<template>
  <div
    :ref="wrapperRef"
    class="fc-input"
    :class="{ 'fc-input--invalid': isInvalid, 'fc-input--focused': isFocused, 'fc-input--icon-button': iconButton }"
  >
    <div v-if="readMode" class="fc-input-reader">
      {{ inputValue || '–' }}
    </div>
    <div v-else class="fc-input__wrapper">
      <div v-if="icon" class="fc-input__icon">
        <Icon v-bind="icon" />
      </div>
      <input
        ref="input"
        v-model="inputValue"
        type="text"
        :placeholder="placeholder"
        :maxlength="maxlength"
        v-bind="inputAttrs"
        class="fc-input__field"
        aria-hidden="false"
        @keydown.enter="onEnter"
        @focus="onFocus"
        @blur="onBlur"
      >
      <div v-if="counter || clearable || appendIcon || iconButton" class="fc-input__icons">
        <div v-if="counter" class="fc-input__count">
          {{ count }}
        </div>
        <div v-if="appendIcon" class="fc-input__append-icon">
          <Icon v-bind="appendIcon" />
        </div>
        <button v-if="iconButton" class="fc-input__icon-button" type="button" @click="onIconButtonClick">
          <Icon :name="iconButton" />
        </button>
      </div>
      <div class="fc-input__view" />
    </div>
  </div>
</template>

<style scoped>
.fc-input {
  display: block;
  font-size: 16px;
  width: 100%;
}

.fc-input__wrapper {
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding: 0 12px 0 15px;
  height: 45px;
  max-width: 100%;
  border: 1px solid rgba(207, 216, 220, 1);
  background-color: white;
  transition:
    border-color 0.3s,
    box-shadow 0.3s;
}

.fc-input--invalid .fc-input__wrapper {
  border-color: red;
}

.fc-input--focused:not(.fc-input--invalid) .fc-input__wrapper {
  border-color: var(--fqz-poll-main-color);
}

.fc-input__field,
.fc-input__icon,
.fc-input__icons {
  position: relative;
  z-index: 1;
}

.fc-input__field {
  border: none;
  background-color: transparent;
  height: 48px;
  font-size: 16px;
  flex-grow: 1;
  padding: 0;
  line-height: 1.1;
  max-width: 100%;
  text-overflow: ellipsis;
  text-align: inherit;
}

.fc-input__field:focus {
  outline: none;
  border-color: var(--fqz-poll-main-color);
}

.fc-input__field::placeholder {
  color: rgba(0, 0, 0, 0.6);
  opacity: 1;
}

.fc-input--icon-button .fc-input__field {
  padding-right: 40px;
}

.fc-input__icon {
  margin-right: 14px;
}

.fc-input__icon-button {
  width: 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: transparent;
  border: none;
  transition: color 0.3s;
  color: rgba(0, 0, 0, 0.4);
  margin-right: -8px;
  margin-top: -1px;
}

.fc-input__icon-button:focus {
  outline: none;
}

.fc-input__icon-button :deep(svg) {
  color: currentColor;
}

.fc-input__icon-button:hover {
  color: rgba(0, 0, 0, 0.6);
}

.fc-input__icons {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  padding-right: 12px;
  display: flex;
  align-items: center;
}

.fc-input__count {
  font-size: 11px;
  color: var(--f-color-text-inactive);
}

.fc-input__clear.fc-btn {
  display: flex;
  align-items: center;
}

.fc-input__clear ~ .fc-input__count {
  display: none;
}

.fc-input__view {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  border: 1px solid var(--f-color-border--dark);
  border-radius: inherit;
  pointer-events: none;
  font: inherit;
  color: var(--f-color-text);
  z-index: 0;
}

.fc-input__field:focus ~ .fc-input__view {
  border-color: var(--fqz-poll-main-color);
}

.fc-input--invalid .fc-input__view {
  border-color: red;
  box-shadow: none;
}

.fc-input--invalid .fc-input__field:focus ~ .fc-input__view {
  border-color: var(--f-color-danger);
  box-shadow: none;
}

.fc-input__append-icon {
  margin-left: 10px;
}

.fc-input__valid {
  display: none;
  margin-left: 10px;
}

.fc-input--valid .fc-input__view {
  border-color: var(--f-color-success);
  box-shadow: none;
}

.fc-input--valid .fc-input__valid {
  display: block;
}

.fc-input--valid .fc-input__count {
  display: none;
}

.fc-input--disabled .fc-input__field {
  opacity: 0.5;
}

.fc-input--disabled .fc-input__icon {
  opacity: 0.5;
}

.fc-input--disabled .fc-input__view {
  background-color: #f2f5f6;
}

.fc-input--counter .fc-input__field {
  padding-right: 35px;
}

.fc-input--clearable .fc-input__field {
  padding-right: 20px;
}

.fc-input--valid.fc-input--clearable .fc-input__field {
  padding-right: 40px;
}

@media (max-width: 679px) {
  .fc-input__icon {
    margin-right: 8px;
  }
}

.fc-input-aligner {
  display: flex;
  align-items: center;
  min-height: 48px;
}
</style>
