<script setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  share: Object,
})

const element = ref(null)

const form = computed(() => {
  return props.share.form === 'square_rounded' ? 'round-rect' : props.share.form
})

const classes = computed(() => [
  'share',
  `share-${form.value}`,
  `share-${props.share.size}`,
  props.share.substrate ? 'share-overlay' : '',
])

const options = computed(() => [
  form.value,
  props.share.style,
  'default',
  'absolute',
  'horizontal',
  `size${props.share.size}`,
  `eachCounter${props.share.for_each_counter ? 1 : 0}`,
  `counter${props.share.total_counter ? 1 : 0}`,
  `counter-${props.share.location_for_total_counter}`,
  'nomobile',
].join(','))

const networks = computed(() => {
  if (!props.share.socialNetworks) {
    return ''
  }
  return Object.entries(props.share.socialNetworks)
    .filter(([_, value]) => value === '1' || value === 1)
    .map(([key, _]) => key)
    .join(',')
})

function loadScript() {
  if (!window.uSocialShare) {
    const script = document.createElement('script')
    script.setAttribute('data-script', 'usocial')
    script.setAttribute('charset', 'utf-8')
    script.async = true
    script.src = 'https://usocial.pro/usocial/usocial.js?v=6.1.4'
    element.value.prepend(script)
  }

  setTimeout(() => {
    if (typeof window?.uSocialShare?.init === 'function') {
      window.uSocialShare.init()
    }
  }, 100)
}

onMounted(() => {
  loadScript()
})

watch(() => props.share, () => {
  if (typeof window?.uSocialShare?.init === 'function') {
    // remove from dom
    const el = document.querySelector('.uSocial-Share')
    if (el) {
      // remove all children
      el.innerHTML = ''
      setTimeout(() => {
        window.uSocialShare.init()
      }, 100)
    }
  }
})

onUnmounted(() => {
  // Clean up if necessary
})
</script>

<template>
  <div ref="element" :class="classes">
    <div
      class="uSocial-Share" data-pid="78532026d41ee6ebd8a6024837ec9a88" data-type="share" :data-options="options"
      :data-social="networks"
    />
  </div>
</template>

<style scoped>
.share-overlay {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 10px;
}

.share-overlay {
  background-color: rgba(0, 0, 0, 0.3);
  padding: 10px;
}

.share-overlay.share-square_rounded {
  border-radius: 10px;
}

.share-overlay.share-square_rounded.share-48 {
  border-radius: 15px;
}

.share-overlay.share-square_rounded.share-32 {
  border-radius: 10px;
}

.share-overlay.share-square_rounded.share-24 {
  border-radius: 8px;
}

.share-overlay.share-round {
  border-radius: 20px;
}

.share-overlay.share-round.share-48 {
  border-radius: 30px;
}

.share-overlay.share-round.share-32 {
  border-radius: 20px;
}

.share-overlay.share-round.share-24 {
  border-radius: 16px;
}

:global(.uSocial-Share .uscl-bar.uscl-size24.uscl-eachCounter1.uscl-bottom .uscl-item .ico_uscl),
:global(.uSocial-Share .uscl-bar.uscl-size24.uscl-eachCounter1.uscl-horizontal .uscl-item .ico_uscl),
:global(.uSocial-Share .uscl-bar.uscl-size24.uscl-eachCounter1.uscl-top .uscl-item .ico_uscl) {
  width: 58px !important;
}
</style>
