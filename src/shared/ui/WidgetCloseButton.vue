<script setup>
defineEmits(['close'])
</script>

<template>
  <button class="widget-close-button" aria-label="Close widget" @click="$emit('close')">
    <svg width="12" height="12" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.9393 0.93934L7 4.87868L3.06066 0.93934C2.47487 0.353553 1.52513 0.353553 0.93934 0.93934C0.353553 1.52513 0.353553 2.47487 0.93934 3.06066L4.87868 7L0.93934 10.9393C0.353553 11.5251 0.353553 12.4749 0.93934 13.0607C1.52513 13.6464 2.47487 13.6464 3.06066 13.0607L7 9.12132L10.9393 13.0607C11.5251 13.6464 12.4749 13.6464 13.0607 13.0607C13.6464 12.4749 13.6464 11.5251 13.0607 10.9393L9.12132 7L13.0607 3.06066C13.6464 2.47487 13.6464 1.52513 13.0607 0.93934C12.4749 0.353553 11.5251 0.353553 10.9393 0.93934Z" fill="currentColor" fill-opacity="0.6" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  </button>
</template>

<style scoped>
.widget-close-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  box-sizing: content-box;
  padding: 15px 15px 13px 15px;
  color: var(--fqz-poll-text-on-place);
  transition: opacity 0.3s;
}
.widget-close-button svg {
  display: block;
  max-width: 100%;
  height: auto;
}
.widget-close-button:hover {
  opacity: 0.6;
}

@media (max-width: 679px) {
  .widget-close-button {
    padding: 15px;
  }
}
</style>
