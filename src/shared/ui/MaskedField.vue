<script setup>
import { maskitoAddOnFocusPlugin, maskitoCaretGuard, maskitoDateOptionsGenerator, maskitoPrefixPostprocessorGenerator, maskitoRemoveOnBlurPlugin, maskitoTimeOptionsGenerator } from '@maskito/kit'
import { computed, toValue } from 'vue'
import Input from './Input.vue'

const props = defineProps({
  mask: {
    type: String,
    required: true,
    validator: value => ['phone', 'number'].includes(value),
  },
  min: {
    type: Number,
    default: null,
  },
  max: {
    type: Number,
    default: null,
  },
  iconButton: {
    type: String,
    default: '',
  },
  wrapperRef: {
    type: Object,
    default: null,
  },
  invalid: {
    type: Boolean,
    default: false,
  },
  inputAttrs: {
    type: Object,
    default: () => ({}),
  },
  // Add any other props that Input component accepts
})

const emit = defineEmits(['update:modelValue', 'icon-button-click', 'focus', 'blur', 'enter'])
const model = defineModel()

const maskOptions = computed(() => {
  if (props.mask === 'number') {
    return {
      mask: /^[0-9.,]+$/,
    }
  }
  if (props.mask === 'digit') {
    return {
      mask: /^\d+$/,
    }
  }
  else if (props.mask === 'time') {
    const options = maskitoTimeOptionsGenerator({
      mode: 'HH:MM',
    })
    return {
      ...options,
      preprocessors: [
        ({ elementState, data }) => {
          if (!data) {
            return { elementState, data }
          }

          // Remove non-digit characters and get the first 4 digits
          const digits = data.replace(/\D/g, '').slice(0, 4)

          if (digits.length < 2) {
            return { elementState, data: digits }
          }

          // Parse hours and minutes
          let hours = Number.parseInt(digits.slice(0, 2), 10)
          let minutes = Number.parseInt(digits.slice(2), 10) || 0

          // Adjust hours and minutes if necessary
          if (hours > 23) {
            hours = 23
            minutes = 59
          }
          else if (minutes > 59) {
            minutes = 59
          }

          const value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`

          return {
            elementState: {
              ...elementState,
              value,
            },
            data: value,
          }
        },
        ...options.preprocessors,
      ],
    }
  }
  else if (props.mask === 'day') {
    return {
      mask: /^\d+$/,
      preprocessors: [
        ({ elementState, data }) => {
          const { value, selection } = elementState
          if (value === data) {
            return {
              elementState,
              data,
            }
          }
          if (data === '' || value === data) {
            return {
              elementState,
              data,
            }
          }

          // Add '0' prefix if length is 1
          if (!value && data.length === 1) {
            return {
              elementState: {
                selection: selection.map(pos => pos + 1),
                value: `0${value}`,
              },
              data,
            }
          }

          // Ensure value is not more than props.max
          const maxDay = toValue(props.max) || 31
          const concatedValue = value.replace(/^0/, '') + data
          const numValue = Number.parseInt(concatedValue, 10)
          if (numValue > maxDay) {
            return {
              elementState: {
                selection,
                value: '',
              },
              data: maxDay.toString(),
            }
          }

          return {
            elementState: {
              selection,
              value: '',
            },
            data: concatedValue,
          }
        },
      ],
    }
  }
  else if (props.mask === 'date') {
    // date format: dd.mm.yyyy
    return maskitoDateOptionsGenerator({
      mode: 'dd.mm.yyyy',
      separator: '.',
      min: new Date(1923, 0, 1),
      max: new Date(2123, 11, 31),
    })
  }
  else if (props.mask === 'phone') {
    return {
      mask: [
        '+',
        '7',
        ' ',
        '(',
        /\d/,
        /\d/,
        /\d/,
        ')',
        ' ',
        /\d/,
        /\d/,
        /\d/,
        ' ',
        /\d/,
        /\d/,
        /\d/,
        /\d/,
      ],
      postprocessors: [
        maskitoPrefixPostprocessorGenerator('+7 '),
      ],
      preprocessors: [createCompletePhoneInsertionPreprocessor()],
      plugins: [
        maskitoAddOnFocusPlugin('+7 '),
        maskitoRemoveOnBlurPlugin('+7 '),
        maskitoCaretGuard((value, [from, to]) => [
          from === to ? '+7 '.length : 0,
          value.length,
        ]),
      ],
    }
  }
  else {
    return {}
  }
})

// Paste "89123456789" => "+7 (912) 345-67-89"
function createCompletePhoneInsertionPreprocessor() {
  const trimPrefix = value => value.replace(/^(\+?7?\s?8?)\s?/, '')
  const countDigits = value => value.replaceAll(/\D/g, '').length

  return ({ elementState, data }) => {
    const { value, selection } = elementState

    return {
      elementState: {
        selection,
        value: countDigits(value) > 11 ? trimPrefix(value) : value,
      },
      data: countDigits(data) >= 11 ? trimPrefix(data) : data,
    }
  }
}
const classes = computed(() => {
  return {
    'masked-field__input': true,
    'masked-field__input--invalid': props.invalid,
    'masked-field__input--time': props.mask === 'time',
  }
})

function onWrapperRef(ref) {
  if (typeof props.wrapperRef === 'function') {
    props.wrapperRef(ref)
  }
}
</script>

<template>
  <Input
    :ref="elementRef" v-model="model" v-maskito="maskOptions" v-bind="$attrs" :icon-button="iconButton"
    :class="classes"
    :input-attrs="inputAttrs"
    :wrapper-ref="onWrapperRef" :is-invalid="invalid" @icon-button-click="emit('icon-button-click', $event)"
    @focus="emit('focus', $event)" @blur="emit('blur', $event)" @enter="emit('enter', $event)"
  />
</template>

<style scoped>
.masked-field__input--time {
  text-align: center;
}

.masked-field__input--time :deep(.fc-input__wrapper) {
  padding: 0;
}
</style>
