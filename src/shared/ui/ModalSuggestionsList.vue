<script setup>
import { useScrollLock } from '@vueuse/core'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import Input from './Input.vue'

const props = defineProps({
  suggestions: Array,
  isLoading: <PERSON><PERSON><PERSON>,
  highlightedIndex: Number,
  selectedIndex: Number,
  placeholder: String,
  inputValue: String,
  notFoundText: String,
})

const emit = defineEmits(['select', 'update:selectedIndex', 'update:inputValue', 'close'])

const localInputValue = ref(props.inputValue)

const isLocked = useScrollLock(document.body)

onMounted(() => {
  isLocked.value = true
})

onUnmounted(() => {
  isLocked.value = false
})

watch(() => props.inputValue, (newValue) => {
  localInputValue.value = newValue
})

function updateInput(event) {
  localInputValue.value = event.target.value
  emit('update:inputValue', event.target.value)
}

function selectSuggestion(suggestion) {
  emit('select', suggestion)
}

function close() {
  emit('close')
}
</script>

<template>
  <div class="modal-suggestions-list">
    <div class="modal-header">
      <Input v-model="localInputValue" :placeholder="placeholder" autofocus @input="updateInput" />
    </div>
    <div class="suggestions-container">
      <div v-if="suggestions.length === 0 && !isLoading && localInputValue" class="not-found">
        {{ notFoundText }}
      </div>
      <simplebar data-simplebar-auto-hide="false" class="simplebar-custom simplebar-themed">
        <ul :class="{ loading: isLoading }">
          <li
            v-for="(suggestion, index) in suggestions" :key="suggestion.id"
            :class="{ selected: index === selectedIndex, highlighted: index === highlightedIndex }"
            @click="selectSuggestion(suggestion)" @mouseenter="$emit('update:selectedIndex', index)"
          >
            {{ suggestion.name }}
          </li>
        </ul>
      </simplebar>
    </div>
    <div class="modal-footer">
      <button class="close-button" @click="close">
        <svg width="14" height="14" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 2L16 16M16 2L2 16" stroke="#F96261" stroke-width="3" stroke-linecap="round" />
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>
.modal-suggestions-list {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100dvh;
  background: white;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  z-index: var(--z-index-modal);
  gap: 15px;
}

.modal-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  width: 100%;
  height: 36px;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(249, 98, 97, 1);
  border-radius: 100px;
}

.suggestions-container {
  flex-grow: 1;
  overflow: hidden;
  width: calc(100% + 20px);
  position: relative;
}

.suggestions-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 15px;
  z-index: 1;
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.suggestions-container::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 15px;
  z-index: 1;
  background: linear-gradient(0deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.suggestions-container [data-simplebar] {
  height: 100%;
  overflow-y: auto;
}

.not-found {
  color: black;
  opacity: 0.5;
  font-size: 14px;
  line-height: 1.1;
  margin-top: 10px;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  padding: 10px 20px 10px 0;
  font-size: 14px;
  line-height: 1.1;
  cursor: pointer;
  transition: opacity 0.3s;
}

li.selected {
  opacity: 0.5;
}

li.highlighted:not(.selected) {
  background-color: rgba(241, 245, 246, 1);
}

.loading {
  opacity: 0.6;
}
</style>
