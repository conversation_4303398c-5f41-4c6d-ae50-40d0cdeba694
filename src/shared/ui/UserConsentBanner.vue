<script setup>
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useMediaQuery } from '@vueuse/core'
import { computed, onMounted, ref, toValue } from 'vue'
import SlideTransition from './SlideTransition.vue'

const emit = defineEmits(['close'])
const translationsStore = useTranslationsStore()
const t = translationsStore.t

const showConsent = ref(true)

const isMobile = useMediaQuery('(max-width: 679px)')

function closeConsent() {
  showConsent.value = false
  emit('close')
}

function onConsentClick() {
  if (isMobile.value) {
    closeConsent()
  }
}

const consentHtml = computed(() => {
  return toValue(t('Мы заботимся о&nbsp;конфиденциальности ваших данных. Используя сервис FOQUZ, вы&nbsp;соглашаетесь с&nbsp;<a href=\'https://foquz.ru/agreement.pdf\' target=\'_blank\'>пользовательским соглашением</a> и&nbsp;<a href=\'https://foquz.ru/confidential.pdf\' target=\'_blank\'>политикой конфиденциальности</a>'))
})

const consentTextRef = ref(null)

onMounted(() => {
  if (consentTextRef.value) {
    const links = consentTextRef.value.querySelectorAll('a')
    links.forEach((link) => {
      link.addEventListener('click', (e) => {
        e.stopPropagation()
      })
    })
  }
})
</script>

<template>
  <SlideTransition fade>
    <div v-if="showConsent" class="user-consent" @click="onConsentClick">
      <div class="user-consent__inner">
        <div class="user-consent__content">
          <p ref="consentTextRef" class="user-consent__text" v-html="consentHtml" />
        </div>
        <button class="user-consent__close" @click.stop="closeConsent">
          <span class="user-consent__close-icon">
            <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 1L9 9M9 1L1 9" stroke="white" stroke-width="2" stroke-linecap="round" />
            </svg>
          </span>
          <span class="user-consent__close-text">{{ t('Закрыть') }}</span>
        </button>
      </div>
    </div>
  </SlideTransition>
</template>

<style scoped>
.user-consent {
  z-index: var(--z-index-modal);
  background-color: var(--fqz-brand-color-1);
  color: #fff;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: var(--fqz-poll-font-family);
  position: relative;
}

.user-consent__inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 40px;
  padding: 12px 20px;
}

.user-consent__content {
  flex-grow: 1;
  padding-right: 30px;
}

.user-consent__text {
  font-size: 13px;
  line-height: 1.2;
  margin: 0;
}

.user-consent__text a {
  color: #fff;
  text-decoration: underline;
}

.user-consent__text a:hover {
  text-decoration: underline;
}

.user-consent__close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  transition: opacity 0.3s ease;
  color: #fff;
}

.user-consent__close:hover {
  opacity: 0.8;
}

.user-consent__close-icon {
  display: block;
}

.user-consent__close-icon svg {
  display: block;
}

.user-consent__close-text {
  display: none;
}

@media (max-width: 679px) {
  .user-consent__inner {
    flex-direction: column;
    padding: 15px 20px;
  }

  .user-consent__close {
    position: relative;
    top: 0;
    right: 0;
    width: 100%;
    height: auto;
  }

  .user-consent__close-icon {
    display: none;
  }

  .user-consent__close-text {
    font-size: 14px;
    line-height: 1.2;
    font-weight: 700;
    display: block;
    margin-top: 15px;
  }
}
</style>
