<script setup>
defineProps({
  title: {
    type: String,
    default: 'Создайте свой опрос в FOQUZ',
  },
  subtitle: {
    type: String,
    default: 'Бесплатный тариф без ограничений по сроку и функционалу.',
  },
  buttonText: {
    type: String,
    default: 'Создать',
  },
})
</script>

<template>
  <div class="foquz-banner">
    <div class="foquz-banner__inner">
      <div class="foquz-banner__icon">
        <svg width="123" height="105" viewBox="0 0 123 105" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19.9551 52.5142C17.5262 50.6898 15.9551 47.7851 15.9551 44.5135V37.1945C15.0053 38.0759 14.0913 38.8979 13.2139 39.6871L13.213 39.6879C7.22025 45.0778 2.93486 48.932 0.626025 59.6552C-2.66658 74.9474 10.0306 84.2549 25.0843 89.8245C29.2682 91.3711 32.1379 93.423 35.0697 95.5193C40.0737 99.0972 45.2586 102.805 57.4671 104.35C74.4648 106.484 107.155 99.6278 115.421 86.4542C130.011 63.1401 121.124 35.6045 106.954 21.5488V70.7972C106.954 76.3201 102.477 80.7972 96.954 80.7972H72.954V91.7972L61.454 80.7972H34.954C29.4312 80.7972 24.954 76.3201 24.954 70.7972V67.5452C21.9656 65.816 19.9551 62.5846 19.9551 58.8836V52.5142Z" fill="#E8EDFF" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M29.9238 4.79688C23.8487 4.79688 18.9238 9.72174 18.9238 15.7969V58.8833C18.9238 62.7852 20.9554 66.2125 24.0184 68.1653C23.9553 67.7114 23.9227 67.2477 23.9227 66.7764V65.5906C22.0821 63.9427 20.9238 61.5483 20.9238 58.8833V15.7969C20.9238 10.8263 24.9533 6.79688 29.9238 6.79688H83.9238C86.8095 6.79688 89.3781 8.155 91.025 10.2669C92.221 10.6568 93.3179 11.2661 94.2682 12.0472C92.7349 7.81822 88.6822 4.79688 83.9238 4.79688H29.9238Z" fill="#B0C0FF" />
          <path d="M25.9238 0.796875C19.8487 0.796875 14.9238 5.72174 14.9238 11.7969V44.5129C14.9238 47.9288 16.4808 50.981 18.9238 52.9986V50.1703C17.673 48.6245 16.9238 46.6562 16.9238 44.5129V11.7969C16.9238 6.82631 20.9533 2.79688 25.9238 2.79688H69.9238C72.0554 2.79688 74.0139 3.53789 75.5557 4.77637H78.3925C76.3748 2.34516 73.3301 0.796875 69.9238 0.796875H25.9238Z" fill="#B0C0FF" />
          <path d="M35.9568 22.2969C35.9568 21.4685 36.6284 20.7969 37.4568 20.7969H93.4568C94.2852 20.7969 94.9568 21.4685 94.9568 22.2969C94.9568 23.1253 94.2852 23.7969 93.4568 23.7969H37.4568C36.6284 23.7969 35.9568 23.1253 35.9568 22.2969Z" fill="#B0C0FF" />
          <path d="M35.9568 28.2969C35.9568 27.4685 36.6284 26.7969 37.4568 26.7969H62.4568C63.2852 26.7969 63.9568 27.4685 63.9568 28.2969C63.9568 29.1253 63.2852 29.7969 62.4568 29.7969H37.4568C36.6284 29.7969 35.9568 29.1253 35.9568 28.2969Z" fill="#B0C0FF" />
          <path d="M46.9568 37.7969C46.9568 37.2446 47.4045 36.7969 47.9568 36.7969H87.9568C88.5091 36.7969 88.9568 37.2446 88.9568 37.7969C88.9568 38.3492 88.5091 38.7969 87.9568 38.7969H47.9568C47.4045 38.7969 46.9568 38.3492 46.9568 37.7969Z" fill="#B0C0FF" />
          <path d="M46.9568 45.7969C46.9568 45.2446 47.4045 44.7969 47.9568 44.7969H76.9568C77.5091 44.7969 77.9568 45.2446 77.9568 45.7969C77.9568 46.3492 77.5091 46.7969 76.9568 46.7969H47.9568C47.4045 46.7969 46.9568 46.3492 46.9568 45.7969Z" fill="#B0C0FF" />
          <path d="M46.9568 53.7969C46.9568 53.2446 47.4045 52.7969 47.9568 52.7969H82.9568C83.5091 52.7969 83.9568 53.2446 83.9568 53.7969C83.9568 54.3492 83.5091 54.7969 82.9568 54.7969H47.9568C47.4045 54.7969 46.9568 54.3492 46.9568 53.7969Z" fill="#B0C0FF" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M36.9568 65.7969C36.9568 63.5878 38.7477 61.7969 40.9568 61.7969H54.9568C57.1659 61.7969 58.9568 63.5878 58.9568 65.7969C58.9568 68.006 57.1659 69.7969 54.9568 69.7969H40.9568C38.7477 69.7969 36.9568 68.006 36.9568 65.7969ZM40.9568 63.7969C39.8522 63.7969 38.9568 64.6923 38.9568 65.7969C38.9568 66.9015 39.8522 67.7969 40.9568 67.7969H54.9568C56.0614 67.7969 56.9568 66.9015 56.9568 65.7969C56.9568 64.6923 56.0614 63.7969 54.9568 63.7969H40.9568Z" fill="#B0C0FF" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M23.9551 20.7969C23.9551 14.7217 28.8799 9.79688 34.9551 9.79688H96.9551C103.03 9.79688 107.955 14.7217 107.955 20.7969V70.7969C107.955 76.872 103.03 81.7969 96.9551 81.7969H73.9551V91.7969C73.9551 92.1973 73.7162 92.5591 73.348 92.7164C72.9798 92.8738 72.5532 92.7963 72.2639 92.5195L61.0538 81.7969H34.9551C28.8799 81.7969 23.9551 76.872 23.9551 70.7969V20.7969ZM34.9551 11.7969C29.9845 11.7969 25.9551 15.8263 25.9551 20.7969V70.7969C25.9551 75.7674 29.9845 79.7969 34.9551 79.7969H61.4551C61.7126 79.7969 61.9602 79.8962 62.1463 80.0742L71.9551 89.4565V80.7969C71.9551 80.2446 72.4028 79.7969 72.9551 79.7969H96.9551C101.926 79.7969 105.955 75.7674 105.955 70.7969V20.7969C105.955 15.8263 101.926 11.7969 96.9551 11.7969H34.9551Z" fill="#3F65F1" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M38.9542 36.7969C38.4019 36.7969 37.9542 37.2446 37.9542 37.7969C37.9542 38.3492 38.4019 38.7969 38.9542 38.7969C39.5065 38.7969 39.9542 38.3492 39.9542 37.7969C39.9542 37.2446 39.5065 36.7969 38.9542 36.7969ZM35.9542 37.7969C35.9542 36.14 37.2974 34.7969 38.9542 34.7969C40.6111 34.7969 41.9542 36.14 41.9542 37.7969C41.9542 39.4537 40.6111 40.7969 38.9542 40.7969C37.2974 40.7969 35.9542 39.4537 35.9542 37.7969Z" fill="#3F65F1" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M38.9542 44.7969C38.4019 44.7969 37.9542 45.2446 37.9542 45.7969C37.9542 46.3492 38.4019 46.7969 38.9542 46.7969C39.5065 46.7969 39.9542 46.3492 39.9542 45.7969C39.9542 45.2446 39.5065 44.7969 38.9542 44.7969ZM35.9542 45.7969C35.9542 44.14 37.2974 42.7969 38.9542 42.7969C40.6111 42.7969 41.9542 44.14 41.9542 45.7969C41.9542 47.4537 40.6111 48.7969 38.9542 48.7969C37.2974 48.7969 35.9542 47.4537 35.9542 45.7969Z" fill="#3F65F1" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M38.9542 52.7969C38.4019 52.7969 37.9542 53.2446 37.9542 53.7969C37.9542 54.3492 38.4019 54.7969 38.9542 54.7969C39.5065 54.7969 39.9542 54.3492 39.9542 53.7969C39.9542 53.2446 39.5065 52.7969 38.9542 52.7969ZM35.9542 53.7969C35.9542 52.14 37.2974 50.7969 38.9542 50.7969C40.6111 50.7969 41.9542 52.14 41.9542 53.7969C41.9542 55.4537 40.6111 56.7969 38.9542 56.7969C37.2974 56.7969 35.9542 55.4537 35.9542 53.7969Z" fill="#3F65F1" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M60.9551 65.7969C60.9551 63.5877 62.7459 61.7969 64.9551 61.7969H78.9551C81.1642 61.7969 82.9551 63.5877 82.9551 65.7969C82.9551 68.006 81.1642 69.7969 78.9551 69.7969H64.9551C62.7459 69.7969 60.9551 68.006 60.9551 65.7969ZM64.9551 63.7969C63.8505 63.7969 62.9551 64.6923 62.9551 65.7969C62.9551 66.9014 63.8505 67.7969 64.9551 67.7969H78.9551C80.0596 67.7969 80.9551 66.9014 80.9551 65.7969C80.9551 64.6923 80.0596 63.7969 78.9551 63.7969H64.9551Z" fill="#3F65F1" />
        </svg>
      </div>
      <div class="foquz-banner__content">
        <h2 class="foquz-banner__title">
          {{ title }}
        </h2>
        <p class="foquz-banner__subtitle">
          {{ subtitle }}
        </p>
        <div class="foquz-banner__buttons">
          <a href="https://foquz.ru/" target="_blank" class="foquz-banner__button">
            {{ buttonText }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.foquz-banner {
  background-color: white;
  display: flex;
  min-height: 200px;
  padding-top: 30px;
  padding-bottom: 30px;
  color: rgba(46, 47, 49, 1);
  font-family: var(--fqz-poll-font-family);
}

.foquz-banner__inner {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 586px;
  margin: 0 auto;
  gap: 40px;
}

.foquz-banner__icon {
  flex-shrink: 0;
  flex-grow: 0;
}

.foquz-banner__icon > svg {
  max-width: 100%;
  height: auto;
  display: block;
}

.foquz-banner__content {
  flex-grow: 1;
}

.foquz-banner__title {
  margin-bottom: 5px;
  font-weight: 700;
  line-height: 1.1;
  font-size: 24px;
}

.foquz-banner__subtitle {
  margin-bottom: 15px;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.1;
}

.foquz-banner__buttons {
  margin-top: 15px;
  display: flex;
}

.foquz-banner__button {
  font-size: 16px;
  text-decoration: none;
  border-radius: 100px;
  padding: 0 25px;
  background-color: transparent;
  display: inline-flex;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.2;
  color: var(--fqz-brand-color-1);
  border: 2px solid var(--fqz-brand-color-1);
  height: 48px;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.foquz-banner__button:hover,
.foquz-banner__button:focus,
.foquz-banner__button:active {
  opacity: 0.8;
}

@media (max-width: 679px) {
  .foquz-banner {
    padding-top: 30px;
    padding-bottom: 30px;
    text-align: center;
  }

  .foquz-banner__inner {
    flex-direction: column;
    gap: 20px;
    max-width: 370px;
    padding-left: 20px;
    padding-right: 20px;
    text-align: center;
  }

  .foquz-banner__buttons {
    justify-content: center;
  }

  .foquz-banner__button {
    height: 36px;
  }

  .foquz-banner__title {
    font-size: 19px;
  }

  .foquz-banner__subtitle {
    font-size: 14px;
  }
}
</style>
