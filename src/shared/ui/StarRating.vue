<script setup>
import { computed } from 'vue'
import StarRatingItem from './StarRatingItem.vue'

const props = defineProps({
  max: {
    type: Number,
    default: 5,
  },
  color: {
    type: String,
    default: '#F8CD1C',
  },
  thin: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'md',
  },
  isCanChange: {
    type: Boolean,
    default: true,
  },
  showLabels: {
    type: Boolean,
    default: false,
  },
  showNumbers: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: null,
  },
  labels: {
    type: Array,
    default: () => [],
  },
  inactive: {
    type: Boolean,
    default: false,
  },
})

const model = defineModel()

const stars = computed(() => Array.from({ length: props.max }, (_, i) => ({
  index: i + 1,
  label: props.labels[i] || '',
})))

function handleStarClick(index) {
  if (!props.isCanChange)
    return

  if (model.value === index) {
    model.value = 0
  }
  else {
    model.value = index
  }
}
</script>

<template>
  <div class="fc-star-rating-container">
    <div
      data-testid="star-rating"
      class="fc-star-rating"
      :class="{
        'fc-star-rating--labels': showLabels,
        'fc-star-rating--inactive': inactive,
        'fc-star-rating--small-on-mobile': max > 5,
        'fc-star-rating--thin': thin,
      }"
      :style="{ '--fc-star-rating-stars-count': max }"
      :data-size="size"
      :data-count="max"
    >
      <div class="fc-star-rating__wrapper">
        <div
          v-for="star in stars"
          :key="star.index"
          class="fc-star-rating__item"
          :class="{ selected: model === star.index }"
          data-testid="star-rating-item"
          @click="handleStarClick(star.index)"
        >
          <div v-if="showNumbers" class="fc-star-rating__index" data-testid="star-number">
            {{ star.index }}
          </div>
          <StarRatingItem
            :thin="thin"
            :size="size"
            :empty="model < star.index"
            :data-empty="model < star.index"
            :color="color"
          />
          <div v-if="showLabels" class="fc-star-rating__label" data-testid="star-label">
            {{ star.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.fc-star-rating {
  --fc-star-rating-stars-count: 5;
  --fc-star-rating-spacing: 16px;
  --fc-star-rating-star-size: 25px;
  --fc-star-rating-bg-color: var(--fqz-poll-main-place-color);
  width: calc(
    var(--fc-star-rating-stars-count) * var(--fc-star-rating-star-size) + var(--fc-star-rating-spacing) *
      (var(--fc-star-rating-stars-count) - 1)
  );
  max-width: 100%;
  transition: opacity 0.3s;
}

.fc-star-rating-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fc-star-rating__wrapper {
  display: flex;
  margin-top: -1px;
  justify-content: space-between !important;
}

.fc-star-rating__item {
  cursor: pointer;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.fc-star-rating__item.selected .fc-star-rating__index {
  opacity: 1;
}

.fc-star-rating__item:not(:last-child) {
  margin-right: 2px;
}

.fc-star-rating__item svg {
  width: var(--fc-star-rating-star-size);
  height: var(--fc-star-rating-star-size);
}

.fc-star-rating__label {
  font-size: 13px !important;
  font-weight: 700;
  margin-top: 15px;
  line-height: 1.1;
  overflow: hidden;
  width: 100px;
  text-align: center;
  position: relative;
}

.fc-star-rating__label:empty {
  margin-top: 0;
}

.fc-star-rating__label:after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 14px;
  background: linear-gradient(to left, var(--fqz-poll-main-place-color), transparent);
}

.fc-star-rating__index {
  text-align: center;
  font-size: var(--fqz-poll-font-size, 13px);
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.1;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.fc-star-rating[data-size='sm'] {
  --fc-star-rating-spacing: 20px;
  --fc-star-rating-star-size: 24px;
}

.fc-star-rating[data-size='md'] {
  --fc-star-rating-spacing: 20px;
  --fc-star-rating-star-size: 36px;
}

.fc-star-rating[data-size='lg'] {
  --fc-star-rating-spacing: 20px;
  --fc-star-rating-star-size: 48px;
}

.fc-star-rating--labels {
  width: auto;
}

.fc-star-rating--labels .fc-star-rating__item:not(:last-child) {
  margin-right: 10px;
}

.fc-star-rating--inactive {
  opacity: 0.5;
}

.fc-star-rating__message--label {
  color: var(--fqz-poll-text-on-bg, #333);
}

.fade-up-enter-active {
  transition: all 0.3s ease;
}

.fade-up-leave-active {
  display: none;
}

.fade-up-enter-from,
.fade-up-leave-to {
  opacity: 0;
  transform: translateY(6px);
}

@media screen and (max-width: 679px) {
  .fc-star-rating[data-size='md'] {
    --fc-star-rating-spacing: 20px;
    --fc-star-rating-star-size: 36px;
  }

  .fc-star-rating--small-on-mobile[data-size='sm'],
  .fc-star-rating--small-on-mobile[data-size='md'] {
    --fc-star-rating-spacing: 10px;
    --fc-star-rating-star-size: 24px;
  }

  .fc-star-rating__label {
    width: 62px;
    font-size: 11px !important;
  }

  .fc-star-rating__index {
    font-size: 14px !important;
    margin-bottom: 10px;
  }

  .fc-star-rating--labels .fc-star-rating__item:not(:last-child) {
    margin-right: 5px;
  }
}

@media (max-width: 320px) {
  .fc-star-rating__label {
    width: 50px;
  }
}
</style>
