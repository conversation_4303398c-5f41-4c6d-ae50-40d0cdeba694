<script setup>
import { useElementBounding, useEventListener } from '@vueuse/core'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  suggestions: Array,
  isLoading: <PERSON><PERSON><PERSON>,
  highlightedIndex: Number,
  selectedIndex: Number,
  inputRef: Object,
})

const emit = defineEmits(['select', 'update:selectedIndex'])

const listRef = ref(null)
const { top, left, width } = useElementBounding(props.inputRef)

watch([top, left, width], ([newTop, newLeft, newWidth]) => {
  if (listRef.value) {
    listRef.value.style.top = `${newTop + props.inputRef.offsetHeight}px`
    listRef.value.style.left = `${newLeft}px`
    listRef.value.style.width = `${newWidth}px`
  }
})

onMounted(() => {
  document.body.appendChild(listRef.value)
})

onUnmounted(() => {
  if (listRef.value && listRef.value.parentNode) {
    listRef.value.parentNode.removeChild(listRef.value)
  }
})

useEventListener(document, 'click', (event) => {
  if (listRef.value && !listRef.value.contains(event.target) && !props.inputRef.contains(event.target)) {
    emit('select', null)
  }
})

function selectSuggestion(suggestion) {
  emit('select', suggestion)
}

const hasFourOrMoreSuggestions = computed(() => props.suggestions.length >= 4)
</script>

<template>
  <div ref="listRef" class="suggestions-wrapper" :class="{ 'four-or-more-suggestions': hasFourOrMoreSuggestions }">
    <simplebar data-simplebar-auto-hide="false" class="simplebar-custom simplebar-themed">
      <ul :class="{ loading: isLoading }">
        <li
          v-for="(suggestion, index) in suggestions" :key="suggestion.id"
          :class="{ selected: index === selectedIndex, highlighted: index === highlightedIndex }"
          @click="selectSuggestion(suggestion)"
          @mouseenter="$emit('update:selectedIndex', index)"
        >
          {{ suggestion.name }}
        </li>
      </ul>
    </simplebar>
  </div>
</template>

<style scoped>
.suggestions-wrapper {
  position: fixed;
  background: white;
  box-shadow: 0px 5px 15px 0px rgba(46, 47, 49, 0.3);
  border-radius: 3px;
  padding-top: 13px;
  padding-bottom: 20px;
  z-index: var(--z-index-modal);
}

.suggestions-wrapper [data-simplebar] {
  max-height: 320px;
  overflow-y: auto;
}

.suggestions-wrapper.four-or-more-suggestions [data-simplebar] {
  height: 33vh;
}

.suggestions-wrapper ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: relative;
  text-align: left;
}

.suggestions-wrapper ul:before {
  content: '';
  display: block;
  height: 100%;
  width: 100%;
  background-color: rgba(241, 245, 246, 1);
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.suggestions-wrapper ul.loading:before {
  opacity: 0.6;
  pointer-events: auto;
}

.suggestions-wrapper li {
  padding: 7px 20px 7px 19px;
  line-height: 1.1;
  cursor: pointer;
  transition:
    background-color 0.3s,
    opacity 0.3s;
}

.suggestions-wrapper li:hover,
.suggestions-wrapper li.highlighted:not(.selected) {
  background-color: rgba(241, 245, 246, 1);
}

.suggestions-wrapper li.selected {
  opacity: 0.5;
}

.loading {
  padding: 10px 15px;
  text-align: center;
  color: #757575;
}

.suggestions-wrapper :deep(.simplebar-vertical.simplebar-track) {
  height: calc(100% - 7px);
  top: 7px;
}
</style>
