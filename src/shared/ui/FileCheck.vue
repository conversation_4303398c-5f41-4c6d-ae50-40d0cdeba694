<script setup>
import { useFancybox } from '@/shared/composables/useFancybox'
import Check from '@/shared/ui/Check.vue'
import Hint from '@/shared/ui/Hint.vue'
import { computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  url: {
    type: String,
    default: '',
  },
  poster: {
    type: String,
    default: '',
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  groupId: {
    type: String,
    default: '',
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  alt: {
    type: String,
    default: '',
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
  checked: {
    type: Boolean,
    default: false,
  },
  value: {
    type: [String, Number, Boolean],
    default: true,
  },
  label: {
    type: String,
    default: null,
  },
  type: {
    type: String,
    // checkbox or radio
    default: 'checkbox',
  },
  checkAttrs: {
    type: Object,
    default: () => ({}),
  },
  invalid: {
    type: Boolean,
    default: false,
  },
  hint: {
    type: String,
    default: '',
  },
  /**
   * @type {boolean | Array<'image' | 'video'>}
   * @description Если true, то показывается превью файла при клике
   * Если массив, то опционально можно указать тип файла, который будет показываться при клике
   */
  previewClickable: {
    type: [Boolean, Array],
    default: true,
  },
})

const emit = defineEmits(['change', 'update:modelValue'])

const isVideo = computed(() => {
  const videoExt = ['MP4', 'WMV', 'MOV', '3GP', 'FLV', 'MPEG-1', 'MPEG-2', 'WEBM', 'AVI']
  const urlArr = props.url.split('.')
  const url = props.url
  const isVideoExt = videoExt.includes(urlArr[urlArr.length - 1].toUpperCase())
  const isYoutube = url && url.includes('youtube.')
  return isVideoExt || isYoutube
})

const { show, addItem, removeItem } = useFancybox(props.groupId || 'gallery')

const isPreviewClickable = computed(() => {
  if (typeof props.previewClickable === 'boolean') {
    return props.previewClickable
  }
  return props.previewClickable.includes(isVideo.value ? 'video' : 'image')
})

onMounted(() => {
  addItem({
    src: computed(() => props.url),
    type: computed(() => isVideo.value ? 'video' : 'image'),
    poster: computed(() => props.poster),
    options: {
      autoHeight: true,
    },
  })
})

onUnmounted(() => {
  removeItem()
})

const isChecked = computed(() => {
  if (props.modelValue === undefined) {
    return props.checked
  }
  if (props.type === 'radio') {
    return props.modelValue === props.value
  }

  if (Array.isArray(props.modelValue)) {
    return props.modelValue.includes(props.value)
  }

  return props.modelValue === props.value
})

const isPreview = computed(() => {
  return !props.poster && !props.url
})

function openPreview() {
  if (!isPreview.value && isPreviewClickable.value) {
    show()
  }
}

const galleryItemClasses = computed(() => ({
  'file-check': true,
  'file-check--inactive': props.inactive,
  'file-check--disabled': props.disabled,
  'file-check--video': isVideo.value,
  'file-check--no-label': !props.label,
  'file-check--checked': isChecked.value,
  'file-check--invalid': props.invalid,
  'file-check--with-hint': props.hint,
  'file-check--no-poster': !props.poster,
  'file-check--preview': isPreview.value,
  'file-check--preview-clickable': isPreviewClickable.value,
}))
</script>

<template>
  <div :class="galleryItemClasses">
    <Check
      v-if="modelValue" class="file-check__check"
      :type="type"
      :value="value"
      :model-value="modelValue"
      v-bind="checkAttrs"
      @change="emit('change', $event)"
      @update:model-value="emit('update:modelValue', $event)"
    />
    <div class="file-check__preview" @click.stop.prevent="openPreview">
      <div class="file-check__preview-trigger">
        <div v-if="poster" class="file-check__background" :style="{ backgroundImage: `url(${poster})` }" />
        <div class="file-check__content">
          <img :src="poster" :alt="alt" class="file-check__image">
        </div>
        <div v-if="isPreview" class="file-check__preview-icon">
          <svg width="138" height="107" viewBox="0 0 138 107" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M118.401 71.6549L119.631 70.0783L119.623 70.0719L119.615 70.0656L118.401 71.6549ZM112.599 71.6549L111.385 70.0656L111.374 70.0739L111.364 70.0823L112.599 71.6549ZM99.1 82.2627L97.7122 83.7028L98.9663 84.9113L100.336 83.8352L99.1 82.2627ZM37.2313 58.0338L36.2611 56.2849L37.2313 58.0338ZM37.2313 29.1981L36.2611 30.947L37.2313 29.1981ZM56.2909 39.7712L57.2611 38.0222L56.2909 39.7712ZM56.2909 47.4607L57.2611 49.2096L56.2909 47.4607ZM83 25H125.304V21H83V25ZM134 33.6957V94.3044H138V33.6957H134ZM125.304 103H64.6957V107H125.304V103ZM56 94.3044V83.5H52V94.3044H56ZM64.6957 103C59.8932 103 56 99.1068 56 94.3044H52C52 101.316 57.684 107 64.6957 107V103ZM134 94.3044C134 99.1068 130.107 103 125.304 103V107C132.316 107 138 101.316 138 94.3044H134ZM125.304 25C130.107 25 134 28.8932 134 33.6957H138C138 26.684 132.316 21 125.304 21V25ZM137.231 83.8147L119.631 70.0783L117.17 73.2315L134.769 86.9679L137.231 83.8147ZM119.615 70.0656C118.454 69.1789 116.97 68.7373 115.5 68.7373V72.7373C116.207 72.7373 116.809 72.956 117.187 73.2442L119.615 70.0656ZM115.5 68.7373C114.03 68.7373 112.546 69.1789 111.385 70.0656L113.813 73.2442C114.191 72.956 114.793 72.7373 115.5 72.7373V68.7373ZM111.364 70.0823L97.8643 80.6901L100.336 83.8352L113.835 73.2275L111.364 70.0823ZM105.696 58.8696C112.707 58.8696 118.391 53.1855 118.391 46.1739H114.391C114.391 50.9764 110.498 54.8696 105.696 54.8696V58.8696ZM118.391 46.1739C118.391 39.1623 112.707 33.4783 105.696 33.4783V37.4783C110.498 37.4783 114.391 41.3714 114.391 46.1739H118.391ZM105.696 33.4783C98.684 33.4783 93 39.1623 93 46.1739H97C97 41.3714 100.893 37.4783 105.696 37.4783V33.4783ZM93 46.1739C93 53.1855 98.684 58.8696 105.696 58.8696V54.8696C100.893 54.8696 97 50.9764 97 46.1739H93ZM100.488 80.8225L84.3878 65.3082L81.6122 68.1886L97.7122 83.7028L100.488 80.8225ZM12.6127 4H72.7514V0H12.6127V4ZM81.3642 12.6387V72.9244H85.3642V12.6387H81.3642ZM72.7514 81.563H12.6127V85.563H72.7514V81.563ZM4 72.9244V12.6387H0V72.9244H4ZM12.6127 81.563C7.86065 81.563 4 77.7 4 72.9244H0C0 79.8999 5.64231 85.563 12.6127 85.563V81.563ZM81.3642 72.9244C81.3642 77.7 77.5035 81.563 72.7514 81.563V85.563C79.7219 85.563 85.3642 79.8999 85.3642 72.9244H81.3642ZM72.7514 4C77.5035 4 81.3642 7.86305 81.3642 12.6387H85.3642C85.3642 5.66313 79.7219 0 72.7514 0V4ZM12.6127 0C5.6423 0 0 5.66313 0 12.6387H4C4 7.86305 7.86065 4 12.6127 4V0ZM57.2611 38.0222L38.2015 27.4491L36.2611 30.947L55.3207 41.5201L57.2611 38.0222ZM28.3006 33.0428V54.189H32.3006V33.0428H28.3006ZM38.2015 59.7827L57.2611 49.2096L55.3207 45.7118L36.2611 56.2849L38.2015 59.7827ZM28.3006 54.189C28.3006 59.265 33.9071 62.165 38.2015 59.7827L36.2611 56.2849C34.3949 57.3201 32.3006 55.9482 32.3006 54.189H28.3006ZM38.2015 27.4491C33.9071 25.0668 28.3006 27.9669 28.3006 33.0428H32.3006C32.3006 31.2836 34.3949 29.9117 36.2611 30.947L38.2015 27.4491ZM55.3207 41.5201C57.028 42.4672 57.028 44.7647 55.3207 45.7118L57.2611 49.2096C61.7145 46.7391 61.7145 40.4927 57.2611 38.0222L55.3207 41.5201Z" fill="#A6B1BC" />
          </svg>
        </div>
      </div>
      <div v-if="isVideo" class="file-check__play-icon">
        <svg width="92" height="92" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 14C2 7.37259 7.37258 2 14 2H82C88.6274 2 94 7.37258 94 14V82C94 88.6274 88.6274 94 82 94H14C7.37259 94 2 88.6274 2 82V14Z" stroke="white" stroke-width="4" />
          <path d="M63.3877 53.2779C66.8707 51.3505 66.8707 46.5319 63.3878 44.6044L41.8367 32.6784C38.3537 30.7509 34 33.1602 34 37.0151V60.8672C34 64.7221 38.3537 67.1314 41.8367 65.204L63.3877 53.2779Z" stroke="white" stroke-width="4" />
        </svg>
      </div>
    </div>
    <div class="file-check__content-below">
      <div v-if="label || hint" class="file-check__label">
        <template v-if="label">
          {{ label }}
        </template>
        <span v-if="hint" class="file-check__hint">
          <Hint :text="hint" />
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.file-check {
  position: relative;
  width: 100%;
  transition: opacity 0.3s;
  border-radius: 8px;
  border: 1px solid rgba(207, 216, 220, 1);
  overflow: hidden;
  transition: border-color 0.3s;
  display: flex;
  flex-direction: column;
}

.file-check--checked {
  border-color: var(--fqz-poll-main-color);
}

.file-check--invalid {
  border-color: red !important;
}

.file-check :deep(.fc-check) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 9px 9px 0;
}

.file-check :deep(.fc-check__label) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.file-check :deep(.fc-check__box) {
  z-index: 10;
}

.file-check__preview {
  position: relative;
  padding-top: 75%; /* 4:3 aspect ratio */
  overflow: hidden;
  cursor: pointer;
  background-color: #fff;
  flex: 0 0 auto;
}

.file-check__preview:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--fqz-poll-main-color);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 5;
}

.file-check--video .file-check__preview:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background-color: rgba(0, 0, 0, 0.6);
  pointer-events: none;
  transition: opacity 0.3s;
}

.file-check__preview-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition: opacity 0.3s;
  cursor: zoom-in;
}

.file-check__preview-trigger:hover {
  opacity: 0.8;
}

.file-check__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: blur(10px);
  transform: scale(3);
}

.file-check__content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 11;
}

.file-check__image,
.file-check__video {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.file-check__play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

.file-check__play-icon svg {
  display: block;
}

.file-check__content-below {
  flex-grow: 1;
}

.file-check__label {
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
  transition: opacity 0.3s;
  padding: 10px;
}

.file-check--disabled .file-check__preview-trigger,
.file-check--inactive .file-check__preview-trigger {
  opacity: 0.5;
}

.file-check--disabled .file-check__label,
.file-check--inactive .file-check__label {
  opacity: 0.7;
}

.file-check--disabled :deep(.fc-check__box),
.file-check--inactive :deep(.fc-check__box) {
  opacity: 0.7;
}

.file-check--disabled :deep(.fc-check) {
  pointer-events: none;
}

.file-check :deep(.fc-check--checkbox .fc-check__box::before) {
  content: '';
  top: 0;
  left: 0;
  margin-top: -9px;
  margin-left: -9px;
  width: 50px;
  height: 50px;
  position: absolute;
}

.file-check--no-label .file-check__hint {
  width: 17px;
  height: 17px;
  margin: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-top: -2px;
}

.file-check__hint {
  display: inline-flex;
  margin-left: 10px;
  position: relative;
  top: 1.5px;
  z-index: 10;
}

.file-check--with-hint :deep(.hint-trigger) {
  width: 17px;
  height: 15px;
  margin: 0;
  vertical-align: middle;
}

.file-check--no-poster .file-check__background,
.file-check--no-poster .file-check__image,
.file-check--preview .file-check__image,
.file-check--preview .file-check__background {
  display: none;
}

.file-check--no-poster .file-check__preview {
  background-color: transparent;
}

.file-check--preview .file-check__preview {
  background-color: rgba(231, 235, 237, 1);
}

.file-check--preview .file-check__preview-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.file-check--preview .file-check__preview-icon svg {
  display: block;
}

.file-check:not(.file-check--preview-clickable) .fc-check {
  z-index: 10;
}

.file-check:not(.file-check--preview-clickable) .file-check__preview-trigger {
  pointer-events: none;
}

@media (max-width: 679px) {
  .file-check__label {
    font-size: 14px;
    line-height: 1.1;
  }
}
</style>
