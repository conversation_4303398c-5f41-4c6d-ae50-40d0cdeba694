<script setup>
import { computed, getCurrentInstance } from 'vue'

const props = defineProps({
  size: {
    // sm, md, lg
    type: String,
    default: 'md',
  },
  thin: {
    type: Boolean,
    default: false,
  },
  empty: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: '#F8CD1C',
  },
})

const starStyle = computed(() => ({
  color: props.color,
}))
const { uid } = getCurrentInstance()
const largeIconId = `path-1-inside-1_74_4162-${uid}`
const mediumIconId = `path-1-inside-1_74_4122-${uid}`
const smallIconId = `path-1-inside-1_74_4083-${uid}`

const strokeWidth = computed(() => {
  if (props.size === 'lg' && !props.thin)
    return 6
  if (props.size === 'md' && !props.thin)
    return 4
  if (props.size === 'sm' && !props.thin)
    return 2.5
  if (props.size === 'lg' && props.thin)
    return 5
  if (props.size === 'md' && props.thin)
    return 3
  if (props.size === 'sm' && props.thin)
    return 2.5
  return 0
})
</script>

<template>
  <div class="star-container" :style="starStyle" :class="{ empty }">
    <div class="star-wrapper">
      <svg v-if="size === 'lg'" class="star-outline" width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask :id="largeIconId" fill="white">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M37.9317 48.3816C38.9404 48.2533 39.6581 47.3011 39.5334 46.2547C39.5306 46.2272 37.1734 31.3715 37.1734 31.3715L47.455 20.8247C48.1773 20.0834 48.1819 18.8756 47.467 18.1267C47.1899 17.8369 46.8315 17.6468 46.4426 17.5841L32.1318 15.3175L25.6447 1.90448C25.1875 0.962692 24.0809 0.583509 23.1729 1.05678C22.8201 1.24114 22.5337 1.53765 22.3554 1.90448L15.8683 15.3175L1.55753 17.5841C0.552549 17.7466 -0.13468 18.7216 0.0223479 19.7632C0.0823881 20.1661 0.266203 20.5377 0.545159 20.8247L10.8268 31.3715L8.47785 46.1711C8.30881 47.2108 8.98496 48.1944 9.98717 48.3702C10.3853 48.4396 10.7945 48.3711 11.1519 48.1754L23.9996 41.2816L36.8482 48.1754C37.1807 48.3578 37.5585 48.4291 37.9317 48.3816Z" />
        </mask>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M37.9317 48.3816C38.9404 48.2533 39.6581 47.3011 39.5334 46.2547C39.5306 46.2272 37.1734 31.3715 37.1734 31.3715L47.455 20.8247C48.1773 20.0834 48.1819 18.8756 47.467 18.1267C47.1899 17.8369 46.8315 17.6468 46.4426 17.5841L32.1318 15.3175L25.6447 1.90448C25.1875 0.962692 24.0809 0.583509 23.1729 1.05678C22.8201 1.24114 22.5337 1.53765 22.3554 1.90448L15.8683 15.3175L1.55753 17.5841C0.552549 17.7466 -0.13468 18.7216 0.0223479 19.7632C0.0823881 20.1661 0.266203 20.5377 0.545159 20.8247L10.8268 31.3715L8.47785 46.1711C8.30881 47.2108 8.98496 48.1944 9.98717 48.3702C10.3853 48.4396 10.7945 48.3711 11.1519 48.1754L23.9996 41.2816L36.8482 48.1754C37.1807 48.3578 37.5585 48.4291 37.9317 48.3816Z" stroke="currentColor" :stroke-width="strokeWidth" :mask="`url(#${largeIconId})`" />
      </svg>
      <svg v-if="size === 'md'" class="star-outline" width="36" height="37" viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask :id="mediumIconId" fill="white">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M28.4488 36.4428C29.2053 36.3466 29.7436 35.6324 29.65 34.8477C29.648 34.827 27.88 23.6853 27.88 23.6853L35.5913 15.7751C36.133 15.2192 36.1365 14.3133 35.6003 13.7516C35.3924 13.5343 35.1236 13.3917 34.832 13.3447L24.0989 11.6448L19.2335 1.58497C18.8906 0.878636 18.0607 0.594248 17.3797 0.949198C17.115 1.08747 16.9003 1.30985 16.7666 1.58497L11.9012 11.6448L1.16815 13.3447C0.414412 13.4665 -0.10101 14.1978 0.016761 14.979C0.0617911 15.2812 0.199653 15.5599 0.408869 15.7751L8.1201 23.6853L6.35839 34.7849C6.23161 35.5647 6.73872 36.3024 7.49038 36.4343C7.78896 36.4863 8.09586 36.435 8.36396 36.2881L17.9997 31.1178L27.6362 36.2881C27.8856 36.425 28.1689 36.4784 28.4488 36.4428Z" />
        </mask>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M28.4488 36.4428C29.2053 36.3466 29.7436 35.6324 29.65 34.8477C29.648 34.827 27.88 23.6853 27.88 23.6853L35.5913 15.7751C36.133 15.2192 36.1365 14.3133 35.6003 13.7516C35.3924 13.5343 35.1236 13.3917 34.832 13.3447L24.0989 11.6448L19.2335 1.58497C18.8906 0.878636 18.0607 0.594248 17.3797 0.949198C17.115 1.08747 16.9003 1.30985 16.7666 1.58497L11.9012 11.6448L1.16815 13.3447C0.414412 13.4665 -0.10101 14.1978 0.016761 14.979C0.0617911 15.2812 0.199653 15.5599 0.408869 15.7751L8.1201 23.6853L6.35839 34.7849C6.23161 35.5647 6.73872 36.3024 7.49038 36.4343C7.78896 36.4863 8.09586 36.435 8.36396 36.2881L17.9997 31.1178L27.6362 36.2881C27.8856 36.425 28.1689 36.4784 28.4488 36.4428Z" stroke="currentColor" :stroke-width="strokeWidth" :mask="`url(#${mediumIconId})`" />
      </svg>
      <svg v-if="size === 'sm'" class="star-outline" width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask :id="`${smallIconId}`" fill="white">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M18.9659 24.6285C19.4702 24.5644 19.829 24.0883 19.7667 23.5651C19.7653 23.5513 18.5867 16.1235 18.5867 16.1235L23.7275 10.8501C24.0887 10.4795 24.091 9.87553 23.7335 9.5011C23.5949 9.35617 23.4158 9.26114 23.2213 9.22978L16.0659 8.0965L12.8224 1.38998C12.5937 0.91909 12.0405 0.729498 11.5865 0.966132C11.41 1.05831 11.2669 1.20657 11.1777 1.38998L7.93417 8.0965L0.778764 9.22978C0.276274 9.31103 -0.0673401 9.79855 0.011174 10.3193C0.0411941 10.5208 0.133102 10.7066 0.27258 10.8501L5.4134 16.1235L4.23893 23.5233C4.15441 24.0431 4.49248 24.5349 4.99358 24.6228C5.19264 24.6575 5.39724 24.6233 5.57597 24.5254L11.9998 21.0786L18.4241 24.5254C18.5904 24.6167 18.7793 24.6523 18.9659 24.6285Z" />
        </mask>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.9659 24.6285C19.4702 24.5644 19.829 24.0883 19.7667 23.5651C19.7653 23.5513 18.5867 16.1235 18.5867 16.1235L23.7275 10.8501C24.0887 10.4795 24.091 9.87553 23.7335 9.5011C23.5949 9.35617 23.4158 9.26114 23.2213 9.22978L16.0659 8.0965L12.8224 1.38998C12.5937 0.91909 12.0405 0.729498 11.5865 0.966132C11.41 1.05831 11.2669 1.20657 11.1777 1.38998L7.93417 8.0965L0.778764 9.22978C0.276274 9.31103 -0.0673401 9.79855 0.011174 10.3193C0.0411941 10.5208 0.133102 10.7066 0.27258 10.8501L5.4134 16.1235L4.23893 23.5233C4.15441 24.0431 4.49248 24.5349 4.99358 24.6228C5.19264 24.6575 5.39724 24.6233 5.57597 24.5254L11.9998 21.0786L18.4241 24.5254C18.5904 24.6167 18.7793 24.6523 18.9659 24.6285Z" stroke="currentColor" :stroke-width="strokeWidth" :mask="`url(#${smallIconId})`" />
      </svg>

      <svg v-if="size === 'lg'" class="star-fill" width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="path-1-inside-1_74_4138" fill="white">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M37.9317 48.5066C38.9404 48.3783 39.6581 47.4261 39.5334 46.3797C39.5306 46.3522 37.1734 31.4965 37.1734 31.4965L47.455 20.9497C48.1773 20.2084 48.1819 19.0006 47.467 18.2517C47.1899 17.9619 46.8315 17.7718 46.4426 17.7091L32.1318 15.4425L25.6447 2.02948C25.1875 1.08769 24.0809 0.708509 23.1729 1.18178C22.8201 1.36614 22.5337 1.66265 22.3554 2.02948L15.8683 15.4425L1.55753 17.7091C0.552549 17.8716 -0.13468 18.8466 0.0223479 19.8882C0.0823881 20.2911 0.266203 20.6627 0.545159 20.9497L10.8268 31.4965L8.47785 46.2961C8.30881 47.3358 8.98496 48.3194 9.98717 48.4952C10.3853 48.5646 10.7945 48.4961 11.1519 48.3004L23.9996 41.4066L36.8482 48.3004C37.1807 48.4828 37.5585 48.5541 37.9317 48.5066Z" />
        </mask>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M37.9317 48.5066C38.9404 48.3783 39.6581 47.4261 39.5334 46.3797C39.5306 46.3522 37.1734 31.4965 37.1734 31.4965L47.455 20.9497C48.1773 20.2084 48.1819 19.0006 47.467 18.2517C47.1899 17.9619 46.8315 17.7718 46.4426 17.7091L32.1318 15.4425L25.6447 2.02948C25.1875 1.08769 24.0809 0.708509 23.1729 1.18178C22.8201 1.36614 22.5337 1.66265 22.3554 2.02948L15.8683 15.4425L1.55753 17.7091C0.552549 17.8716 -0.13468 18.8466 0.0223479 19.8882C0.0823881 20.2911 0.266203 20.6627 0.545159 20.9497L10.8268 31.4965L8.47785 46.2961C8.30881 47.3358 8.98496 48.3194 9.98717 48.4952C10.3853 48.5646 10.7945 48.4961 11.1519 48.3004L23.9996 41.4066L36.8482 48.3004C37.1807 48.4828 37.5585 48.5541 37.9317 48.5066Z" fill="currentColor" stroke="currentColor" :stroke-width="2" :mask="`url(#${largeIconId})`" />
      </svg>
      <svg v-if="size === 'md'" class="star-fill" width="36" height="37" viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M28.3226 35.4508L28.3225 35.4508C28.2522 35.4598 28.1815 35.4467 28.1172 35.4114L28.1172 35.4114L28.1089 35.407L18.4725 30.2367L17.9997 29.983L17.5269 30.2367L7.89115 35.407L7.89113 35.4069L7.88362 35.4111C7.81255 35.45 7.73538 35.4619 7.66215 35.4491C7.47979 35.4165 7.30165 35.2147 7.34543 34.9454L7.34602 34.9417L9.10774 23.842L9.18639 23.3465L8.83615 22.9872L1.12596 15.0782C1.12583 15.078 1.1257 15.0779 1.12557 15.0778C1.06397 15.0142 1.02027 14.9285 1.00584 14.8316L1.00559 14.8299C0.964922 14.5602 1.14493 14.3621 1.32669 14.332C1.32705 14.332 1.32741 14.3319 1.32778 14.3318L12.0577 12.6324L12.5739 12.5507L12.8015 12.0801L17.666 2.02207C17.6661 2.02184 17.6662 2.0216 17.6663 2.02136C17.7085 1.93502 17.7721 1.87253 17.8424 1.83569C18.0048 1.75142 18.2275 1.80314 18.3337 2.02114C18.3338 2.02133 18.3339 2.02153 18.334 2.02172L23.1986 12.0801L23.4262 12.5507L23.9424 12.6324L34.6727 14.3319C34.6731 14.332 34.6734 14.332 34.6737 14.3321C34.7451 14.3438 34.8168 14.3794 34.877 14.4422C35.0425 14.6156 35.0401 14.9078 34.8752 15.0771C34.8752 15.0771 34.8751 15.0772 34.8751 15.0772L27.164 22.9872L26.8137 23.3465L26.8924 23.842L27.88 23.6853C26.8924 23.842 26.8924 23.842 26.8924 23.8421L26.8925 23.8425L26.8927 23.844L26.8937 23.8501L26.8974 23.8739L26.9122 23.9671L26.9683 24.3208L27.1686 25.5832L27.7766 29.4154C27.8883 30.1197 27.9991 30.8183 28.1024 31.4699C28.4136 33.4324 28.6571 34.9684 28.6551 34.9477L28.656 34.9568L28.6571 34.966C28.6895 35.2378 28.5025 35.4279 28.3226 35.4508Z" fill="currentColor" stroke="currentColor" stroke-width="2" />
      </svg>
      <svg v-if="size === 'sm'" class="star-fill" width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.3835 21.2257C18.5612 22.3462 18.7084 23.2747 18.7557 23.5685L12.4726 20.1974L11.9998 19.9437L11.527 20.1974L5.24426 23.5686L6.40104 16.2803L6.47969 15.7847L6.12945 15.4255L1.03672 10.2014L8.0906 9.08419L8.60684 9.00243L8.83441 8.53189L12 1.98649L15.1657 8.53189L15.3932 9.00243L15.9095 9.08419L22.9634 10.2014L17.8706 15.4255L17.5204 15.7847L17.599 16.2802L18.5867 16.1235L17.599 16.2803L17.5991 16.2806L17.5992 16.2816L17.5999 16.2856L17.6024 16.3015L17.6123 16.3636L17.6497 16.5994L17.7832 17.441L18.1885 19.9958C18.2553 20.4167 18.3213 20.833 18.3835 21.2257Z" fill="currentColor" stroke="currentColor" stroke-width="2" />
      </svg>
    </div>
  </div>
</template>

<style scoped>
.star-container {
  position: relative;
  cursor: pointer;
  display: flex;
  justify-content: center;
}

.star-wrapper {
  position: relative;
  width: var(--fc-star-rating-star-size);
  height: var(--fc-star-rating-star-size);
}

.star-outline {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.star-container:not(.empty) .star-fill {
  opacity: 1;
}
</style>
