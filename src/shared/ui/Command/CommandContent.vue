<script setup>
import { ComboboxContent, ComboboxEmpty, useForwardPropsEmits } from 'radix-vue'
import Simplebar from 'simplebar-vue'
import { computed, onMounted, ref } from 'vue'
import CommandHeader from './CommandHeader.vue'

const props = defineProps({
  class: { type: null, required: false },
  placeholderText: { type: String, required: true },
  emptyText: { type: String, required: true },
  view: { type: String, required: false, default: 'default' },
  compact: { type: Boolean, required: false, default: false },
  searchable: { type: Boolean, required: false, default: false },
  searchAutofocus: { type: Boolean, required: false, default: false },
})

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props

  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps)
const commandContentRef = ref(null)
const simplebarRef = ref(null)

onMounted(() => {
  if (simplebarRef.value && simplebarRef.value.recalculate) {
    setTimeout(() => {
      simplebarRef.value.recalculate()
    }, 100)
  }
  const el = commandContentRef.value.$el
  if (el) {
    const selectedCommandItems = el.querySelectorAll('[data-state="checked"]')
    if (selectedCommandItems.length > 0) {
      selectedCommandItems[0].scrollIntoView({
        behavior: 'instant',
        block: 'nearest',
        inline: 'nearest',
      })
    }
  }
})

const showShadowTop = ref(false)
const showShadowBottom = ref(false)
const commandContentClasses = computed(() => {
  return {
    'command-content': true,
    'command-content--searchable': props.searchable,
    'command-content--modal': props.view === 'modal',
    'command-content--shadow-top': showShadowTop.value,
    'command-content--shadow-bottom': showShadowBottom.value,
    'command-content--compact': props.compact,
    [props.class]: true,
  }
})

function handleScroll(event) {
  const scrollElement = event.target
  const scrollHeight = scrollElement.scrollHeight
  const clientHeight = scrollElement.clientHeight
  const scrollTop = scrollElement.scrollTop

  const isScrolledToTop = scrollTop === 0
  const isScrolledToBottom = scrollTop + clientHeight >= scrollHeight - 1

  if (isScrolledToTop) {
    showShadowTop.value = false
  }
  else {
    showShadowTop.value = true
  }

  if (isScrolledToBottom) {
    showShadowBottom.value = false
  }
  else {
    showShadowBottom.value = true
  }
}
</script>

<template>
  <ComboboxContent
    v-bind="forwarded"
    ref="commandContentRef"
    :class="commandContentClasses"
    aria-hidden="false"
    data-aria-hidden="false"
  >
    <CommandHeader v-if="searchable" :search-autofocus="searchAutofocus" :placeholder-text="placeholderText" class="command-content__header" />
    <ComboboxEmpty class="command-content__empty">
      {{ emptyText }}
    </ComboboxEmpty>
    <Simplebar ref="simplebarRef" data-simplebar-auto-hide="false" class="simplebar-custom simplebar-themed" @scroll="handleScroll">
      <slot />
    </Simplebar>
    <div v-if="$slots.footer" class="command-content__footer">
      <slot name="footer" />
    </div>
  </ComboboxContent>
</template>

<style scoped>
.command-content {
  flex-grow: 1;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.command-content [data-simplebar] {
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-grow: 1;
}

.command-content:not(.command-content--searchable) .simplebar-vertical.simplebar-track {
  height: calc(100% - 7px);
  top: 7px;
}

.command-content__header {
  margin-bottom: 15px;
}

.command-content__empty {
  width: 100%;
  height: 100%;
  padding-left: 20px;
  padding-right: 20px;
  font-size: 16px;
  position: relative;
  z-index: 1;
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.5);
}

:global(.command-content .simplebar-scrollable-y::before),
:global(.command-content .simplebar-scrollable-y::after) {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 15px;
  background: linear-gradient(360deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s;
}

:global(.command-content .simplebar-scrollable-y::before) {
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
  top: 0;
  bottom: auto;
}

:global(.command-content--shadow-top .simplebar-scrollable-y::before),
:global(.command-content--shadow-bottom .simplebar-scrollable-y::after) {
  opacity: 1;
}

.command-content__footer {
  margin-top: 15px;
  padding-left: 20px;
  padding-right: 20px;
}

.command-content--modal {
  padding-top: 15px;
  padding-bottom: 15px;
}

.command-content--modal .command-root__header {
  padding-left: 20px;
}

.command-content--compact .command-content__header {
  display: none;
}

.command-content--compact:not(.command-content--modal) {
  padding-top: 12px;
  padding-bottom: 2px;
}

.command-content--compact .command-content__empty {
  padding-left: 10px;
  padding-right: 10px;
}
</style>
