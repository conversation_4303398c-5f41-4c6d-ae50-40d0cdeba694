<script setup>
import Input from '@shared/ui/Input.vue'
import { ComboboxInput } from 'radix-vue'

defineProps({
  placeholderText: {
    type: String,
    required: true,
  },
  modelValue: {
    type: String,
    required: true,
  },
  searchAutofocus: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const model = defineModel({
  type: String,
  required: true,
})
</script>

<template>
  <div class="command-root__header" aria-hidden="false" data-aria-hidden="false">
    <ComboboxInput :placeholder="placeholderText" :auto-focus="searchAutofocus" as-child aria-hidden="false" data-aria-hidden="false">
      <Input v-model="model" :autofocus="searchAutofocus" :auto-focus="searchAutofocus" />
    </ComboboxInput>
  </div>
</template>

<style scoped>
.command-root__header {
  width: 100%;
  padding: 20px 20px 0;
  display: block;
}

.command-root__header input {
  width: 100%;
}
</style>
