<script setup>
import { ComboboxRoot, useForwardPropsEmits } from 'radix-vue'
import { computed } from 'vue'

const props = defineProps({
  modelValue: { type: null, required: false, default: '' },
  defaultValue: { type: null, required: false },
  open: { type: Boolean, required: false, default: true },
  defaultOpen: { type: Boolean, required: false },
  searchTerm: { type: String, required: false },
  selectedValue: { type: null, required: false },
  multiple: { type: Boolean, required: false },
  disabled: { type: Boolean, required: false },
  name: { type: String, required: false },
  dir: { type: String, required: false },
  filterFunction: { type: Function, required: false },
  displayValue: { type: Function, required: false },
  resetSearchTermOnBlur: { type: Boolean, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
  searchable: { type: Boolean, required: false, default: true },
  emptyText: { type: String, required: false, default: 'Совпадений не найдено' },
  // can be 'default', 'modal', 'tablet-modal'
  view: { type: String, required: false, default: 'default' },
  placeholderText: {
    type: String,
    default: 'Поиск',
  },
  nothingFoundText: {
    type: String,
    default: 'Совпадений не найдено',
  },
})

const emits = defineEmits([
  'update:modelValue',
  'update:open',
  'update:searchTerm',
  'update:selectedValue',
])

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props

  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)
const classes = computed(() => {
  return {
    'command-root': true,
    'command-root--searchable': props.searchable,
    'command-root--disabled': props.disabled,
    'command-root--modal': props.view === 'modal',
    'command-root--tablet-modal': props.view === 'tablet-modal',
  }
})

function onFocusOutside() {
}
function onInteractOutside() {
}
</script>

<template>
  <ComboboxRoot
    v-bind="forwarded"
    :class="classes"
    aria-hidden="false"
    data-aria-hidden="false"
    :default-open="true"
    :open="true"
    @focus-outside="onFocusOutside"
    @interact-outside="onInteractOutside"
  >
    <slot />
  </ComboboxRoot>
</template>

<style>
.command-root {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  overflow: hidden;
  gap: 15px;
  background-color: white;
  padding: 12px 0 12px;
  width: 100%;
  flex-grow: 1;
}

.command-root--searchable {
  padding: 0;
  padding-bottom: 10px;
}

.command-root--modal {
  padding: 0;
}

.command-root--modal .command-root__header {
  margin-bottom: 15px !important;
  padding: 0 20px !important;
}

.command-root--modal .command-item {
  padding: 10px 20px;
  font-size: 14px;
}

.command-root--modal .command-item__content::after {
  display: none !important;
}
</style>
