<script setup>
import Check from '@shared/ui/Check.vue'
import { ComboboxItem, useForwardPropsEmits } from 'radix-vue'
import { computed } from 'vue'

const props = defineProps({
  value: { type: null, required: true },
  disabled: { type: Boolean, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
  // can be 'default', 'checkbox', 'radio'
  view: { type: String, required: false, default: 'default' },
  selected: { type: Boolean, required: false, default: false },
  disabled: { type: Boolean, required: false, default: false },
})
const emits = defineEmits(['select'])

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props

  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)
const classes = computed(() => {
  return {
    'command-item': true,
    'command-item--disabled': props.disabled,
    'command-item--selected': props.selected,
    'command-item--checkbox': props.view === 'checkbox',
    'command-item--radio': props.view === 'radio',
    [props.class]: !!props.class,
  }
})

const showCheckbox = computed(() => {
  return props.view === 'checkbox' || props.view === 'radio'
})
</script>

<template>
  <ComboboxItem
    v-bind="forwarded"
    :class="classes"
  >
    <div class="command-item__content">
      <Check v-if="showCheckbox" :type="view" :checked="selected" class="command-item__check" @click.stop.prevent="$emit('select', $event)">
        <slot />
      </Check>
      <span v-else class="command-item__content-inner">
        <slot />
      </span>
    </div>
  </ComboboxItem>
</template>

<style>
.command-item {
  padding: 10px 20px 10px 20px;
  font-size: 16px;
  line-height: 1.1;
  font-weight: 400;
  color: #000;
  background-color: white;
  cursor: pointer;
  z-index: 2;
  position: relative;
  transition:
    background-color 0.3s,
    opacity 0.3s;
}

.command-item[data-state='checked'],
.command-item--selected:not(.command-item--checkbox):not(.command-item--radio) {
  pointer-events: none;
  opacity: 0.5;
  background-color: white !important;
}

.command-item--disabled {
  pointer-events: none;
  opacity: 0.7;
}

.command-item__content {
  position: relative;
}

.command-item__content > * {
  position: relative;
  z-index: 1;
}

.command-item__content-inner {
  z-index: 2;
}

.command-item__content-inner .select-item__label-description {
  margin-top: 2px;
}

.command-item__content::after {
  content: '';
  position: absolute;
  top: 50%;
  left: -20px;
  width: calc(100% + 40px);
  height: calc(100% + 16px);
  background-color: rgba(241, 245, 246, 1);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
  transform: translateY(-50%);
}

.command-item[data-highlighted] .command-item__content::after {
  opacity: 1;
}

.command-item--checkbox,
.command-item--radio {
  padding-left: 20px !important;
}

.command-item--checkbox[data-state='checked'],
.command-item--radio[data-state='checked'] {
  background-color: white !important;
  opacity: 1;
  pointer-events: auto;
}

/* .command-item--checkbox .command-item__check,
.command-item--radio .command-item__check {
  pointer-events: none;
} */

/* .command-item--checkbox[data-highlighted],
.command-item--radio[data-highlighted] {
  background-color: transparent !important;
} */
</style>
