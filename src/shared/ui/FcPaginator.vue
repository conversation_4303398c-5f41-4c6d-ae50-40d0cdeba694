<script>
import { nextTick, ref, watch } from 'vue'

export default {
  name: 'FcPaginator',
  props: {
    items: {
      type: Array,
      required: true,
    },
    active: {
      type: [String, Number],
      required: true,
    },
    activeIndex: {
      type: [String, Number],
      required: true,
    },
    realIndex: {
      type: [String, Number],
      required: true,
    },
  },
  setup(props) {
    const track = ref(null)
    const initialized = ref(false)

    const swiper = ref(null)
    const swiperInstance = ref(null)

    const stylesString = `
      .swiper-wrapper ::slotted(swiper-slide) {
        width: auto;
        align-self: center;
      }
      .swiper-wrapper ::slotted(swiper-slide:last-of-type) {
        padding-right: 20px;
      }
    `

    const swiperParams = {
      injectStyles: [stylesString],
    }

    watch(swiper, async (newSwiper) => {
      if (newSwiper) {
        Object.assign(newSwiper, swiperParams)
        newSwiper.initialize()
        swiperInstance.value = newSwiper.swiper
      }
    }, { immediate: true })

    watch(() => props.realIndex, (idx) => {
      if (swiperInstance.value) {
        nextTick(() => {
          swiperInstance.value.updateSlides()
          swiperInstance.value.slideTo(idx, !initialized.value ? 0 : 300)
          initialized.value = true
        })
      }
    }, { immediate: true })

    function getActiveIndex(itemRealIndex) {
      if (itemRealIndex === -1 || props.realIndex === -1) {
        return false
      }

      return props.realIndex === itemRealIndex
    }

    return { track, swiper, getActiveIndex }
  },
}
</script>

<template>
  <div class="fc-paginator">
    <swiper-container
      ref="swiper"
      slides-per-view="auto"
      :mutation-observer="true"
      :space-between="0"
      :free-mode="true"
      :init="false"
      :center-insufficient-slides="true"
    >
      <swiper-slide
        v-for="(item, index) in items"
        :key="item.id"
        class="fc-paginator-slide"
      >
        <div
          class="fc-paginator-item"
          :data-blocked="item.blocked"
          :data-active="getActiveIndex(item.realIndex)"
          :data-visible="item.visible"
        >
          <div class="fc-paginator-item__marker">
            <div class="fc-paginator-item__index">
              {{ index + 1 }}
            </div>
          </div>
          <div v-if="item.label" class="fc-paginator-item__label">
            {{ item.label }}
          </div>
        </div>
      </swiper-slide>
    </swiper-container>
  </div>
</template>

<style scoped>
.fc-paginator {
  display: block;
}

.fc-paginator-item {
  display: flex;
  align-items: center;
  padding-left: 14px;
  transition: all 0.3s;
}

.fc-paginator-item__marker {
  flex-shrink: 0;
  position: relative;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fc-paginator-item__index {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #cfd8dc;
  color: var(--fqz-poll-text-on-place, black);
  font-weight: bold;
  font-size: 13px;
  line-height: 22px;
  padding-top: 0;
  opacity: 0.8;
  transition:
    opacity 0.3s,
    background 0.3s;
}

.fc-paginator-item__label {
  display: none;
  margin-left: 9px;
  font-size: 13px;
}

.fc-paginator-item[data-visible='false'] {
  opacity: 0.5;
}

.fc-paginator-item[data-active='true'] {
  opacity: 1 !important;
}

.fc-paginator-item[data-active='true'] .fc-paginator-item__label {
  display: block;
  white-space: nowrap;
  font-weight: 700;
}

.fc-paginator-item[data-active='true'] .fc-paginator-item__index {
  background: var(--fc-paginator-color, #3a5cdc);
  color: white;
  position: relative;
  opacity: 1;
}

.fc-paginator-item__marker {
  padding: 6px;
  border-radius: 50%;
  position: relative;
}

.fc-paginator-item__marker::after {
  content: '';
  display: block;
  position: absolute;
  border-radius: 50%;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s;
  border: 3px solid var(--fc-paginator-color, #3a5cdc);
}

.fc-paginator-item[data-active='true'] .fc-paginator-item__marker::after {
  opacity: 0.4;
}

@media (max-width: 679px) {
  .fc-paginator-item[data-active='true'] .fc-paginator-item__label {
    display: block !important;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 700;
  }
}
</style>
