<script setup>
import { computed, ref, toValue } from 'vue'

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  number: {
    type: Number,
    required: true,
  },
  showNumber: {
    type: Boolean,
    default: false,
  },
  class: {
    type: String,
    default: '',
  },
  simplified: {
    type: Boolean,
    default: false,
  },
  cardSorting: {
    type: Boolean,
    default: false, // TODO подумать насчёт иконок (как компоненты, как шрифт, как background-image)
  },
})

const full = ref(false)
function onClick() {
  full.value = !full.value
}
const priorityItemClasses = computed(() => ({
  'priority-item': true,
  'priority-item_full': toValue(full),
  'priority-item--simplified': toValue(props.simplified),
}))
</script>

<template>
  <div
    :class="priorityItemClasses"
    @click="onClick"
  >
    <div class="priority-item__content">
      <div class="priority-item__drag-icon">
        <svg
          v-if="cardSorting"
          class="priority-item__card-sorting-icon"
          xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none"
        >
          <path d="M9.00781 16.9912V16.9812M9.00781 9.9834V9.9734M9.00781 2.9834V2.9734" stroke="currentColor" stroke-opacity="0.5" stroke-width="4" stroke-linecap="round" />
        </svg>
        <svg
          v-else
          width="8" height="18" viewBox="0 0 8 18" fill="none" xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M4 1V17M4 1L7 4M4 1L1 4M4 17L7 14M4 17L1 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>
      <div v-if="showNumber" class="priority-item__number">
        {{ number }}.
      </div>
      <div class="priority-item__text">
        {{ text }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.priority-item {
  display: flex;
  align-items: center;
  transition:
    opacity 0.15s,
    box-shadow 0.15s;
}

.priority-item__content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-color: var(--fqz-poll-main-place-color);
  padding: 15px;
  border-radius: 8px;
  width: 100%;
  position: relative;
}

.priority-item__content::before {
  content: '';
  position: absolute;
  inset: 0;
  border: 1px solid currentColor;
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.15s;
  pointer-events: none;
}

.priority-item--simplified .priority-item__content::before {
  opacity: 0.2;
}

/* Override border when item is being dragged */
:global(.sortable-ghost .priority-item__content::before) {
  opacity: 0 !important;
}

.priority-item__number {
  font-family: var(--fqz-poll-font-family);
  color: var(--fqz-poll-text-on-place);
  font-weight: bold;
  font-size: 16px;
  line-height: 1.1;
  margin-right: 5px;
  width: 25px;
  flex: 0 0 auto;
}

.priority-item__text {
  font-family: var(--fqz-poll-font-family);
  font-size: var(--fqz-poll-font-size);
  color: var(--fqz-poll-text-on-place);
  flex: 1;
  line-height: 1.1;
}

.priority-item:not(.priority-item_full) .priority-item__text {
  overflow: hidden;
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.priority-item__drag-icon {
  color: var(--fqz-poll-text-on-place);
  opacity: 0.5;
  display: block;
  cursor: move;
  flex: 0 0 auto;
}

.priority-item__drag-icon > svg {
  margin-right: 20px;
  display: block;
}

.priority-item__card-sorting-icon {
  margin-right: 5px !important;
  margin-left: -10px;
}

@media screen and (max-width: 679px) {
  .priority-item__text {
    font-size: 14px;
    line-height: 1.1;
  }

  .priority-item__number {
    font-size: 14px;
    line-height: 1.1;
    width: 23px;
  }
}
</style>
