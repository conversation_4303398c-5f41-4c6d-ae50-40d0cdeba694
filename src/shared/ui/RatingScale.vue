<script setup>
import { computed } from 'vue'

const props = defineProps({
  max: {
    type: Number,
    required: true,
  },
  color: {
    type: String,
    required: true,
  },
  showLabels: {
    type: Boolean,
    default: false,
  },
  showNumbers: {
    type: Boolean,
    default: true,
  },
  labels: {
    type: Array,
    default: () => [],
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  error: {
    type: String,
    default: '',
  },
})

const model = defineModel()

const stars = computed(() => Array.from({ length: props.max }, (_, i) => i + 1))

function handleStarClick(index) {
  if (model.value === index) {
    model.value = 0
  }
  else {
    model.value = index
  }
}
</script>

<template>
  <div class="rating-scale" data-testid="rating-scale" :style="{ '--fc-rating-scale-color': color }">
    <div class="rating-scale__wrapper">
      <div
        v-for="star in stars"
        :key="star"
        class="rating-scale__item"
        :class="{ active: model === star, inactive }"
        data-testid="rating-scale-item"
        @click="handleStarClick(star)"
      >
        <span v-if="showNumbers">{{ star }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.rating-scale {
  --fc-rating-scale-offset: 6px;
  --fc-rating-scale-height: 36px;
  width: 100%;
}

.rating-scale__wrapper {
  display: flex;
  margin-left: calc(-1 * var(--fc-rating-scale-offset));
  margin-right: calc(-1 * var(--fc-rating-scale-offset));
}

.rating-scale__item.inactive {
  opacity: 0.5;
}

.rating-scale__item {
  flex-grow: 1;
  margin-left: var(--fc-rating-scale-offset);
  margin-right: var(--fc-rating-scale-offset);
  border: 2px solid var(--fc-rating-scale-color);
  border-radius: 4px;
  line-height: 1.1;
  font-size: 15px;
  flex-basis: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--fc-rating-scale-height);
  cursor: pointer;
  transition:
    background 0.3s,
    opacity 0.3s;
}

.rating-scale__item.active {
  background: var(--fc-rating-scale-color);
  color: white;
}

.rating-scale__label {
  margin-top: 15px;
  font-weight: 700;
  text-align: center;
}

@media screen and (max-width: 767.98px) {
  .rating-scale {
    --fc-rating-scale-offset: 2.5px;
  }
  .rating-scale__item {
    font-size: 13px;
  }
}

@media screen and (min-width: 768px) {
  .survey--tablet .rating-scale {
    --fc-rating-scale-height: 60px;
  }

  .survey--tablet .rating-scale__label {
    font-size: 30px;
    margin-top: 30px;
  }
}
</style>
