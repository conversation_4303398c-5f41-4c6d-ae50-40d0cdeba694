<script setup>
import FilePreview from '@shared/ui/FilePreview.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import { computed, ref } from 'vue'

const props = defineProps({
  attachments: {
    type: Array,
    default: () => [],
  },
  error: {
    type: String,
    default: null,
  },
  errorKind: {
    type: String,
    default: null,
  },
  isScreenshotLoading: {
    type: Boolean,
    default: false,
  },
  shouldShowScreenshotButton: {
    type: Boolean,
    default: false,
  },
  buttonText: {
    type: String,
    default: null,
  },
  screenshotButtonText: {
    type: String,
    default: null,
  },
  description: {
    type: String,
    default: null,
  },
  maxFiles: {
    type: Number,
    default: 0,
  },
  uploadEnabled: {
    type: <PERSON>ole<PERSON>,
    default: false,
  },
  groupId: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['fileUpload', 'screenshotRequest', 'remove'])

const fileInput = ref(null)

const isMaxFilesReached = computed(() => {
  return props.attachments.length >= props.maxFiles
})

const shouldShowUploadButton = computed(() => {
  return props.uploadEnabled
})

function handleFileUpload() {
  if (!isMaxFilesReached.value) {
    fileInput.value?.click()
  }
}

function handleFileInputChange(event) {
  const files = Array.from(event.target.files)
  if (files.length > 0) {
    emit('fileUpload', files)
  }
  // Reset file input
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

function handleScreenshotRequest() {
  emit('screenshotRequest')
}

function handleRemove(index) {
  emit('remove', index)
}

function getFileType(attachment) {
  if (attachment.type === 'screenshot') {
    return 'image'
  }

  if (attachment.file && attachment.file.type) {
    if (attachment.file.type.startsWith('image/'))
      return 'image'
  }

  return attachment.type || 'file'
}

const variantsFileAttachmentClasses = computed(() => {
  return {
    'file-attachment': true,
    'file-attachment--has-error': !!props.error,
    'file-attachment--screenshots-enabled': !!props.shouldShowScreenshotButton,
  }
})
</script>

<template>
  <div :class="variantsFileAttachmentClasses">
    <p v-if="description" class="file-attachment__description">
      {{ description }}
    </p>
    <FormGroup
      class="file-attachment__list"
      :error="error"
      :error-attrs="{ class: 'file-attachment__error--inline' }"
    >
      <TransitionGroup
        name="file-list" tag="div"
        class="file-attachment__items"
        :class="{ 'file-attachment__items--empty': attachments.length === 0 }"
      >
        <FilePreview
          v-for="(attachment, index) in attachments" :key="attachment.uuid || `${attachment.type}-${index}`"
          :file="attachment" :is-uploading="attachment.isUploading || false" :name="attachment.name"
          :type="attachment.type || getFileType(attachment)"
          :preview-url="attachment.previewUrl || ''" :full-url="attachment.fullUrl || ''"
          :group="groupId"
          @remove="handleRemove(index)"
        />
      </TransitionGroup>

      <div
        v-if="shouldShowUploadButton || shouldShowScreenshotButton"
        key="action-buttons"
        class="file-attachment__actions"
      >
        <button
          v-if="shouldShowUploadButton" :disabled="isMaxFilesReached"
          class="file-attachment__button file-attachment__button--upload" type="button"
          @click="handleFileUpload"
        >
          <div class="file-attachment__button-icon">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.4"
                d="M12.5833 6.62071L7.22519 11.9714C6.56878 12.6269 5.67849 12.9951 4.75019 12.9951C3.82188 12.9951 2.93159 12.6269 2.27518 11.9714C1.61877 11.3159 1.25 10.4268 1.25 9.49982C1.25 8.57281 1.61877 7.68377 2.27518 7.02827L7.63332 1.67762C8.07093 1.24062 8.66445 0.995117 9.28333 0.995117C9.9022 0.995117 10.4957 1.24062 10.9333 1.67762C11.3709 2.11461 11.6168 2.70731 11.6168 3.32532C11.6168 3.94332 11.3709 4.53602 10.9333 4.97301L5.56936 10.3237C5.35055 10.5422 5.05379 10.6649 4.74436 10.6649C4.43492 10.6649 4.13816 10.5422 3.91935 10.3237C3.70055 10.1052 3.57763 9.80882 3.57763 9.49982C3.57763 9.19082 3.70055 8.89447 3.91935 8.67597L8.86937 3.7387"
                stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
              />
            </svg>
          </div>
          <span class="file-attachment__button-text">{{ buttonText || 'Прикрепить файл' }}</span>
        </button>

        <button
          v-if="shouldShowScreenshotButton" :disabled="isMaxFilesReached || isScreenshotLoading"
          class="file-attachment__button file-attachment__button--screenshot" type="button" @click="handleScreenshotRequest"
        >
          <div class="file-attachment__button-icon">
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path opacity="0.4" d="M3 1.00001H1V3M0.999998 9L1 11H3M9 1L11 1.00001L11 3M11 9L11 11L9 11" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </div>
          <span class="file-attachment__button-text">
            {{ screenshotButtonText || 'Сделать скриншот' }}
          </span>
        </button>
      </div>
    </FormGroup>

    <!-- Hidden file input -->
    <input
      ref="fileInput" type="file" multiple accept="image/*" style="display: none"
      @change="handleFileInputChange"
    >
  </div>
</template>

<style scoped>
.file-attachment {
  width: 100%;
}

.file-attachment__description {
  font-size: var(--fqz-poll-font-size, 16px);
  line-height: 1.3;
  color: var(--fqz-poll-text-on-place);
  font-family: var(--fqz-poll-font-family);
  margin: 0 0 15px 0;
}

@media (max-width: 679px) {
  .file-attachment__description {
    font-size: 14px;
  }
}

.file-attachment__list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-attachment__items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px 20px;
  transition: transform 0.3s;
}

.file-attachment__items--empty {
  display: none;
}

.file-attachment__actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.file-attachment__error {
  text-align: left;
  width: 100%;
}

:global(.file-attachment__error--inline) {
  margin-top: 0 !important;
}

.file-attachment__button {
  all: unset;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  align-self: flex-start;
  padding: 0 15px;
  border-radius: var(--fqz-poll-next-button-radius, 18px);
  cursor: pointer;
  height: 36px;
  line-height: 1.3;
  color: #000;
  font-size: 12px;
  font-family: var(--fqz-poll-font-family);
  transition: opacity 0.3s;
  gap: 12px;
  box-sizing: border-box;
  flex-shrink: 0;
  position: relative;
  width: auto;
  min-width: fit-content;
}

.file-attachment__button:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--fqz-poll-next-button-radius, 18px);
  border: 1px solid #000;
  opacity: 0.2;
  width: 100%;
  height: 100%;
}

.file-attachment__button:hover {
  opacity: 0.7;
}

.file-attachment__button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed;
}

.file-attachment__button-icon {
  margin-top: -1px;
}

.file-attachment__button-icon svg {
  display: block;
}

.file-attachment__button-text {
  white-space: nowrap;
}

/* Transition animations */
.file-list-enter-active,
.file-list-leave-active,
.file-list-move {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.file-list-leave-active {
  position: absolute;
}

.file-list-enter-from,
.file-list-leave-to {
  opacity: 0;
  transform: translateY(4px);
}

@media (max-width: 679px) {
  .file-attachment__button {
    width: 100%;
    min-width: 0;
  }
  .file-attachment--screenshots-enabled .file-attachment__actions {
    flex-direction: column;
    gap: 10px;
    min-width: 0;
  }
}
</style>
