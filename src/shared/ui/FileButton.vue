<script setup>
import { computed, toValue } from 'vue'

const props = defineProps({
  text: {
    type: String,
    default: 'Загрузить фото, видео, аудио',
  },
  allowedFormats: {
    type: Array,
    default: () => [],
  },
  invalid: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['click'])

function handleClick() {
  if (props.disabled) {
    return
  }

  emit('click')
}

const lowerCaseText = computed(() => toValue(props.text)?.toLowerCase?.() || '')
</script>

<template>
  <button
    class="file-button"
    type="button"
    :class="{ 'file-button--invalid': invalid, 'file-button--disabled': disabled }"
    @click="handleClick"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path opacity="0.4" d="M18.5 9.43839L10.4628 17.4644C9.47817 18.4476 8.14274 19 6.75028 19C5.35782 19 4.02239 18.4476 3.03777 17.4644C2.05315 16.4811 1.5 15.1476 1.5 13.7571C1.5 12.3665 2.05315 11.033 3.03777 10.0497L11.075 2.02375C11.7314 1.36825 12.6217 1 13.55 1C14.4783 1 15.3686 1.36825 16.025 2.02375C16.6814 2.67924 17.0502 3.56829 17.0502 4.4953C17.0502 5.42231 16.6814 6.31135 16.025 6.96685L7.97904 14.9928C7.65083 15.3206 7.20569 15.5047 6.74154 15.5047C6.27738 15.5047 5.83224 15.3206 5.50403 14.9928C5.17583 14.6651 4.99144 14.2206 4.99144 13.7571C4.99144 13.2935 5.17583 12.849 5.50403 12.5213L12.929 5.11537" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>

    <span class="file-button__text">{{ lowerCaseText }}</span>
  </button>
</template>

<style scoped>
.file-button {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  padding: 0 10px;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.2);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition:
    background-color 0.3s,
    border-color 0.3s,
    opacity 0.3s;
}

.file-button:not(.file-button--disabled):hover {
  background-color: #f3f3f3;
}

.file-button__text {
  font-size: 11px;
  line-height: 1.1;
  text-align: center;
  margin-top: 5px;
  color: rgba(0, 0, 0, 1);
}

.file-button--invalid {
  border-color: rgba(255, 0, 0, 1);
}

.file-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
