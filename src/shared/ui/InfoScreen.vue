<script setup>
import { computed, toValue } from 'vue'
import { useTranslationsStore } from '../store/translationsStore'

const props = defineProps({
  /**
   * @type {('test-mode-limit-over' | 'poll-period-over' | 'poll-unavailable' | 'poll-unhandled-error' | 'poll-archived' | 'poll-server-error' | 'timer-is-up' | 'save-answer-error' | 'quota-limit-over')}
   */
  type: {
    type: String,
    required: true,
    default: 'test-mode-limit-over',
  },
  text: {
    type: String,
    default: '',
  },
  error: {
    type: Object,
    default: null,
  },
})

const translationsStore = useTranslationsStore()
const t = translationsStore.t

const text = computed(() => {
  if (props.error && props.error.message.includes('Опрос не найден')) {
    return toValue(t('Опрос не найден'))
  }
  if (props.type === 'test-mode-limit-over') {
    const text = toValue(t('К сожалению, лимит для прохождения опроса в тестовом режиме исчерпан.<br>Для дальнейшего прохождения опрос необходимо опубликовать.'))
    return text.split('<br>').map(line => `<span>${line}</span>`).join('')
  }
  if (props.type === 'timer-is-up') {
    return `<span>${toValue(t('К сожалению, время для прохождения опроса истекло'))}</span>`
  }
  if (props.type === 'poll-unavailable' || props.type === 'poll-period-over' || props.type === 'poll-unhandled-error') {
    const text = toValue(t('В настоящее время опрос недоступен'))
    return `<span>${text}</span>`
  }
  if (props.type === 'poll-server-error') {
    const firstLine = 'Опрос по техническим причинам недоступен.'
    const secondLine = 'Наши специалисты работают над устранением проблемы.'
    return props.text || `<span>${firstLine}</span><span>${secondLine}</span>`
  }
  if (props.type === 'save-answer-error') {
    const firstLine = toValue(t('Произошла ошибка при сохранении ответа.'))
    const secondLine = toValue(t('Перезагрузите страницу и попробуйте ещё раз.'))
    return `<span>${firstLine}</span><span>${secondLine}</span>`
  }
  if (props.type === 'quota-limit-over') {
    // Use the translation function to get the appropriate language text
    const text = toValue(t('Нужное количество ответов по опросу собрано'))
    return `<span>${text}</span>`
  }
  return ''
})

const infoScreenClasses = computed(() => ({
  'info-screen': true,
  [`info-screen--${props.type}`]: true,
}))

const showNotAvailableIcon = computed(() =>
  props.type === 'test-mode-limit-over'
  || props.type === 'poll-unavailable'
  || props.type === 'poll-unhandled-error'
  || props.type === 'poll-archived'
  || props.type === 'company-answers-limit-over'
  || props.type === 'poll-server-error'
  || props.type === 'save-answer-error',
)
</script>

<template>
  <div :class="infoScreenClasses">
    <div class="info-screen__icon">
      <svg v-if="showNotAvailableIcon" class="info-screen__icon-svg info-screen__icon-svg--answers-limit-over" width="120" height="110" viewBox="0 0 120 110" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 11.3896C0 5.27505 4.81537 0.265625 10.8193 0.265625H92.3374C96.9921 0.**********.936 3.28134 102.47 7.48125C106.486 8.30564 109.683 11.4134 110.763 15.4061C116 16.2497 120 20.7905 120 26.2656V85.2857C120 91.3608 115.075 96.2857 109 96.2857H76V108.266C76 108.663 75.7645 109.023 75.4001 109.182C75.0357 109.341 74.6117 109.269 74.3201 108.999L60.6078 96.2857H28C23.307 96.2857 19.3005 93.3468 17.7203 89.2091C12.2384 88.6421 8 83.8782 8 78.1416V76.884C3.37458 75.6044 0 71.2642 0 66.1416V11.3896ZM8 74.7895V18.3896C8 12.275 12.8154 7.26562 18.8193 7.26562H100.207C98.7462 4.28734 95.7595 2.26562 92.3374 2.26562H10.8193C5.97712 2.26562 2 6.32156 2 11.3896V66.1416C2 70.1773 4.52631 73.577 8 74.7895ZM17.1492 87.1018C17.0511 86.511 17 85.9043 17 85.2857V26.2656C17 20.1905 21.9249 15.2656 28 15.2656H108.627C107.39 11.7511 104.13 9.26562 100.337 9.26562H18.8193C13.9771 9.26562 10 13.3216 10 18.3896V78.1416C10 82.6127 13.0953 86.296 17.1492 87.1018ZM28 17.2656C23.0294 17.2656 19 21.2951 19 26.2656V85.2857C19 90.2562 23.0294 94.2857 28 94.2857H61C61.2521 94.2857 61.495 94.3809 61.6799 94.5524L74 105.975V95.2857C74 94.7334 74.4477 94.2857 75 94.2857H109C113.971 94.2857 118 90.2562 118 85.2857V26.2656C118 21.2951 113.971 17.2656 109 17.2656H28Z" fill="currentColor" />
        <path d="M88 43.8656L80.4 36.2656L50 66.6656L57.6 74.2656L88 43.8656Z" fill="currentColor" />
        <path d="M50 43.8656L57.6 36.2656L88 66.6656L80.4 74.2656L50 43.8656Z" fill="currentColor" />
      </svg>
      <svg v-else-if="type === 'poll-period-over'" class="info-screen__icon-svg info-screen__icon-svg--poll-period-over" width="100" height="104" viewBox="0 0 100 104" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.48993 2.07182C4.91153 1.71508 5.54251 1.76766 5.89925 2.18926L19.7855 18.6003C27.9358 11.8058 38.4217 7.71809 49.8623 7.71809C61.1036 7.71809 71.4231 11.6646 79.511 18.2476L93.0988 2.18926C93.4555 1.76766 94.0865 1.71507 94.5081 2.07182C94.9297 2.42856 94.9823 3.05954 94.6256 3.48115L81.035 19.5427C90.7436 28.1528 96.8623 40.7209 96.8623 54.7181C96.8623 67.1765 92.015 78.5027 84.1035 86.9136C84.3279 87.226 84.6115 87.6208 84.9391 88.077C85.7825 89.2517 86.9174 90.8334 88.0831 92.4613C90.4055 95.7047 92.874 99.1647 93.377 99.9202C94.1005 101.007 93.9973 102.264 93.1805 103.058C92.3884 103.829 91.1455 103.945 90.067 103.297C89.2351 102.797 87.2713 101.449 84.9953 99.8873C84.4939 99.5433 83.9773 99.1888 83.4544 98.8307C80.7949 97.0094 77.9441 95.0759 75.9344 93.8296C68.4739 98.8128 59.5074 101.718 49.8623 101.718C40.2172 101.718 31.2507 98.8128 23.7902 93.8296C21.7805 95.0759 18.9297 97.0094 16.2702 98.8307C15.7473 99.1888 15.2307 99.5433 14.7294 99.8873C12.4533 101.449 10.4895 102.797 9.65765 103.297C8.57915 103.945 7.33622 103.829 6.54412 103.058C5.72731 102.264 5.6241 101.007 6.34766 99.9202C6.85063 99.1647 9.31912 95.7047 11.6415 92.4613C12.8072 90.8334 13.9421 89.2517 14.7855 88.077C15.1131 87.6208 15.3967 87.226 15.6211 86.9136C7.70961 78.5027 2.8623 67.1765 2.8623 54.7181C2.8623 40.9204 8.80781 28.5114 18.2766 19.9133L4.37248 3.48115C4.01574 3.05954 4.06832 2.42856 4.48993 2.07182ZM17.0432 88.362C16.8588 88.6188 16.6458 88.9152 16.4101 89.2435C15.567 90.4178 14.4326 91.9988 13.2676 93.6257C10.9288 96.892 8.49076 100.31 8.01243 101.029C7.87262 101.239 7.85803 101.384 7.86318 101.456C7.86842 101.53 7.89682 101.584 7.93863 101.624C8.00518 101.689 8.23421 101.819 8.62767 101.582C9.40222 101.117 11.2911 99.8209 13.5665 98.2596C14.0745 97.911 14.6017 97.5492 15.1401 97.1805C17.4881 95.5726 20.03 93.8461 22.0146 92.5836C20.2605 91.2914 18.5994 89.8802 17.0432 88.362ZM77.71 92.5836C79.6946 93.8461 82.2365 95.5726 84.5845 97.1805C85.1228 97.5492 85.6501 97.911 86.158 98.2595C88.4335 99.8209 90.3224 101.117 91.0969 101.582C91.4904 101.819 91.7194 101.689 91.786 101.624C91.8278 101.584 91.8562 101.53 91.8614 101.456C91.8666 101.384 91.852 101.239 91.7122 101.029C91.2338 100.31 88.7958 96.892 86.457 93.6257C85.292 91.9988 84.1576 90.4178 83.3145 89.2435C83.0788 88.9152 82.8658 88.6188 82.6814 88.362C81.1252 89.8802 79.4641 91.2914 77.71 92.5836ZM49.8623 9.71809C25.0095 9.71809 4.8623 29.8653 4.8623 54.7181C4.8623 79.5709 25.0095 99.7181 49.8623 99.7181C74.7151 99.7181 94.8623 79.5709 94.8623 54.7181C94.8623 29.8653 74.7151 9.71809 49.8623 9.71809ZM49.8623 14.7181C27.7709 14.7181 9.8623 32.6267 9.8623 54.7181C9.8623 76.8095 27.7709 94.7181 49.8623 94.7181C71.9537 94.7181 89.8623 76.8095 89.8623 54.7181C89.8623 32.6267 71.9537 14.7181 49.8623 14.7181ZM7.8623 54.7181C7.8623 31.5221 26.6663 12.7181 49.8623 12.7181C73.0583 12.7181 91.8623 31.5221 91.8623 54.7181C91.8623 77.914 73.0583 96.7181 49.8623 96.7181C26.6663 96.7181 7.8623 77.914 7.8623 54.7181ZM50.3454 53.8425C50.8289 54.1093 51.0047 54.7176 50.7379 55.2012L34.7379 84.2012C34.4711 84.6847 33.8628 84.8605 33.3792 84.5937C32.8957 84.3269 32.7199 83.7186 32.9867 83.235L48.9867 54.235C49.2535 53.7514 49.8618 53.5757 50.3454 53.8425Z" fill="currentColor" />
        <path d="M63.2388 37.9668C64.0979 37.1928 65.3668 38.252 64.7587 39.2356L53.5671 57.3399L47.4256 52.2133L63.2388 37.9668Z" fill="currentColor" />
        <path d="M53.5671 57.3399C52.1514 59.0359 49.6289 59.263 47.933 57.8474C46.2371 56.4317 46.0099 53.9092 47.4256 52.2133C48.8412 50.5174 51.3637 50.2902 53.0596 51.7059C54.7555 53.1216 54.9827 55.644 53.5671 57.3399Z" fill="currentColor" />
        <path d="M23.4931 35.5839C22.5962 34.7354 23.7322 33.3331 24.7484 34.0345L51.1638 52.2664L46.8094 57.6414L23.4931 35.5839Z" fill="currentColor" />
        <path d="M51.1638 52.2664C52.9555 53.7179 52.8765 55.6468 51.6741 57.1311C50.4716 58.6154 48.601 59.0928 46.8094 57.6414C45.4656 56.5528 45.0967 54.2609 46.2991 52.7767C47.5016 51.2924 49.3722 50.8149 51.1638 52.2664Z" fill="currentColor" />
        <path d="M55.8624 54.7181C55.8624 58.0318 53.1761 60.7181 49.8624 60.7181C46.5486 60.7181 43.8624 58.0318 43.8624 54.7181C43.8624 51.4044 46.5486 48.7181 49.8624 48.7181C53.1761 48.7181 55.8624 51.4044 55.8624 54.7181Z" fill="currentColor" />
        <path d="M71.7162 7.98049C70.4772 7.40737 70.0644 5.79343 71.0944 4.89755C72.0961 4.02636 73.1324 3.36079 74.3359 2.73429C76.3161 1.70345 78.48 1.07274 80.704 0.878168C82.928 0.683595 85.1685 0.928973 87.2976 1.60029C89.4268 2.27161 91.4029 3.35572 93.113 4.79072C94.8232 6.22573 96.234 7.98352 97.2648 9.96375C98.2957 11.944 98.9264 14.1079 99.121 16.3318C99.3155 18.5558 99.0702 20.7963 98.3988 22.9255C98.0136 24.1473 97.4924 25.3187 96.847 26.4184C96.1459 27.613 94.4423 27.5022 93.6358 26.3761C90.625 22.1723 83.2984 13.3382 71.7162 7.98049Z" fill="currentColor" />
        <path d="M28.2819 7.98049C29.5209 7.40737 29.9337 5.79343 28.9037 4.89755C27.9021 4.02636 26.8657 3.36079 25.6622 2.73429C23.682 1.70345 21.5181 1.07274 19.2941 0.878168C17.0702 0.683595 14.8296 0.928973 12.7005 1.60029C10.5714 2.27161 8.59528 3.35572 6.88511 4.79072C5.17494 6.22573 3.76416 7.98352 2.73332 9.96375C1.70248 11.944 1.07176 14.1079 0.877191 16.3318C0.682619 18.5558 0.927996 20.7963 1.59931 22.9255C1.98455 24.1473 2.50573 25.3187 3.15114 26.4184C3.85229 27.613 5.55583 27.5022 6.36239 26.3761C9.37311 22.1723 16.6997 13.3382 28.2819 7.98049Z" fill="currentColor" />
      </svg>
      <svg v-else-if="type === 'quota-limit-over'" class="info-screen__icon-svg" width="117" height="92" viewBox="0 0 117 92" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M85.0001 43.6L77.4001 36L47.0001 66.4L54.6001 74L85.0001 43.6Z" fill="currentColor" />
        <path d="M35.6001 55L43.2001 47.4L62.2001 66.4L54.6001 74L35.6001 55Z" fill="currentColor" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 11C0 4.92487 4.92486 0 11 0H77C82.738 0 87.4499 4.3935 87.9552 10H106C112.075 10 117 14.9249 117 21V69C117 75.0751 112.075 80 106 80H103V81C103 87.0751 98.0751 92 92 92H26C19.9249 92 15 87.0751 15 81V71H11C4.92487 71 0 66.0751 0 60V11ZM15 69V31C15 24.9249 19.9249 20 26 20H29.0448C29.5501 14.3935 34.262 10 40 10H85.9451C85.4476 5.50005 81.6326 2 77 2H11C6.02943 2 2 6.02944 2 11V60C2 64.9706 6.02943 69 11 69H15ZM31.0549 20H92C98.0751 20 103 24.9249 103 31V78H106C110.971 78 115 73.9706 115 69V21C115 16.0294 110.971 12 106 12H40C35.3674 12 31.5524 15.5001 31.0549 20ZM26 22C21.0294 22 17 26.0294 17 31V81C17 85.9706 21.0294 90 26 90H92C96.9706 90 101 85.9706 101 81V31C101 26.0294 96.9706 22 92 22H26Z" fill="currentColor" />
      </svg>
      <svg v-else-if="type === 'timer-is-up'" class="info-screen__icon-svg" width="58" height="105" viewBox="0 0 58 105" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1 1C1 0.447715 1.44772 0 2 0H56C56.5523 0 57 0.447715 57 1V5C57 5.55228 56.5523 6 56 6H54V29.0631C53.9999 35.4549 49.1107 39.4747 44.7114 42.6593C44.1843 43.0408 43.6648 43.4098 43.1576 43.7701C41.4784 44.9629 39.9347 46.0594 38.7008 47.1952C37.0951 48.6733 36.2501 50.0137 36.2501 51.4263C36.2501 52.7463 37.134 54.1286 38.8603 55.7316C40.1931 56.9692 41.8696 58.2003 43.6789 59.5289C44.1846 59.9002 44.7007 60.2792 45.2226 60.668C47.5702 62.4173 49.9986 64.3435 51.8387 66.5528C53.6887 68.7739 55 71.3481 55 74.3828V99H57C57.5523 99 58 99.4477 58 100V104C58 104.552 57.5523 105 57 105H1C0.447715 105 0 104.552 0 104V100C0 99.4477 0.447715 99 1 99H3V74.3828C3 71.3481 4.31128 68.7739 6.16127 66.5528C8.00136 64.3435 10.4298 62.4173 12.7774 60.668C13.2993 60.2792 13.8154 59.9002 14.3211 59.5289C16.1304 58.2003 17.8069 56.9692 19.1397 55.7316C20.866 54.1286 21.7499 52.7463 21.7499 51.4263C21.7499 50.0137 20.9049 48.6733 19.2992 47.1952C18.0653 46.0594 16.5216 44.9629 14.8424 43.7701C14.3353 43.4099 13.8157 43.0408 13.2886 42.6593C8.88928 39.4747 4.00008 35.4549 4 29.0631V6H2C1.44772 6 1 5.55228 1 5V1ZM55 4V2H3V4H55ZM6 6V29.0631C6.00007 34.3253 9.9858 37.7994 14.4614 41.0392C14.9457 41.3898 15.4371 41.7387 15.9274 42.0868C17.6289 43.2947 19.3173 44.4935 20.6537 45.7237C22.3761 47.3092 23.7499 49.1512 23.7499 51.4263C23.7499 53.6426 22.2899 55.5357 20.5006 57.1972C19.0748 58.5212 17.271 59.8452 15.446 61.1847C14.954 61.5458 14.4605 61.9081 13.9724 62.2718C11.6326 64.0153 9.37356 65.821 7.69805 67.8327C6.03245 69.8325 5 71.9684 5 74.3828V99H53V74.3828C53 71.9684 51.9675 69.8325 50.3019 67.8327C48.6264 65.821 46.3674 64.0153 44.0276 62.2718C43.5395 61.9081 43.046 61.5458 42.554 61.1847C40.729 59.8452 38.9252 58.5212 37.4994 57.1972C35.7101 55.5357 34.2501 53.6426 34.2501 51.4263C34.2501 49.1512 35.6239 47.3092 37.3463 45.7237C38.6827 44.4935 40.3711 43.2947 42.0726 42.0867C42.5629 41.7386 43.0543 41.3898 43.5386 41.0392C48.0142 37.7994 51.9999 34.3253 52 29.0631V6H6ZM2 101V103H56V101H2ZM10 15C10.5523 15 11 15.4477 11 16V27.0751C11.0001 31.4227 14.4476 34.3896 18.5603 37.1717C19.0178 37.4812 19.1377 38.1029 18.8283 38.5603C18.5188 39.0178 17.8971 39.1377 17.4397 38.8283C13.4086 36.1014 9.00008 32.5945 9 27.0751V16C9 15.4477 9.44771 15 10 15Z" fill="currentColor" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29 63.5C24.8 63.5 8 68.2184 8 77.1442V96H50V77.1442C50 68.2184 33.2 63.5 29 63.5ZM45 84C45 83.4477 45.4477 83 46 83C46.5523 83 47 83.4477 47 84V92C47 92.5523 46.5523 93 46 93H28C27.4477 93 27 92.5523 27 92C27 91.4477 27.4477 91 28 91H45V84Z" fill="currentColor" />
      </svg>
    </div>
    <p class="info-screen__text" v-html="text" />
  </div>
</template>

<style scoped>
.info-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  z-index: 1;
  flex-grow: 1;
  align-self: stretch;
  height: 100%;
  color: inherit;
  max-width: 680px;
  margin: 0 auto;
}

.info-screen--test-mode-limit-over {
  max-width: 702px;
}

.info-screen--poll-unavailable {
  max-width: 780px;
}

.info-screen--quota-limit-over,
.info-screen--save-answer-error {
  max-width: 715px;
}

.info-screen__icon {
  width: 120px;
  height: 120px;
  margin-bottom: 25px;
}

.info-screen__icon-svg {
  display: block;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  margin: 0 auto;
}

.info-screen__text {
  font-weight: 700;
  font-size: var(--fqz-poll-title-font-size);
  line-height: 1.1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-screen__text span {
  display: block;
}

@media (max-width: 679px) {
  .info-screen {
    max-width: 330px;
    padding: 0 15px;
  }
  .info-screen--poll-period-over {
    max-width: 310px;
  }
  .info-screen--quota-limit-over,
  .info-screen--save-answer-error {
    max-width: 322px;
  }
  .info-screen__text {
    font-size: 19px !important;
    line-height: 1.1;
  }
  .info-screen__icon {
    width: 60px;
    height: 60px;
  }

  .info-screen--timer-is-up .info-screen__icon-svg {
    width: 29px;
    height: 52px;
  }
}

@media (max-width: 330px) {
  .info-screen {
    max-width: 310px;
  }
}
</style>
