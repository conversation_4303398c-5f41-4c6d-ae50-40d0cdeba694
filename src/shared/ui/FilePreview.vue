<script setup>
import { useFancybox } from '@/shared/composables/useFancybox'
import { computed, onMounted, onUnmounted } from 'vue'
import { ALLOWED_IMAGE_PREVIEW_FORMATS } from '../constants/files'
import Spinner from './Spinner.vue'

const props = defineProps({
  file: {
    type: File,
    required: true,
  },
  group: {
    type: String,
    required: false,
  },
  previewUrl: {
    type: String,
    required: true,
  },
  fullUrl: {
    type: String,
    required: true,
  },
  type: {
    // 'image' | 'video' | 'audio' | 'screenshot' | null
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  isUploading: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: false,
  },
  view: {
    type: String,
    // default | compact
    default: 'default',
  },
  /**
   * @description Если стоит true, то открытие медиа будет в новой вкладке
   * По умолчанию при клике мы показываем медиа в Fancybox
   * @default false
   */
  openInNewTab: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['remove'])

const { show, addItem, removeItem, isFormatSupported } = useFancybox(props.group)

const isPlaceholder = computed(() => !props.isUploading && !props.previewUrl && props.type !== 'audio' && props.type !== 'screenshot')

const isFormatNotSupported = computed(() => {
  if (props.fullUrl) {
    return !isFormatSupported(props.fullUrl)
  }
  return false
})

onMounted(() => {
  if (!props.openInNewTab) {
    addItem({
      src: computed(() => props.fullUrl),
      type: computed(() => props.type),
      id: computed(() => props.file?.uuid || props.file?.id || props.id),
      timestamp: computed(() => props.file?.timestamp || 0),
    })
  }
})

function handleRemove() {
  removeItem()
  emit('remove')
}

function openPreview() {
  // Allow opening preview if there's a fullUrl, even for unsupported formats
  const canOpenPreview = (props.fullUrl && !props.openInNewTab) || (!isPlaceholder.value && !props.openInNewTab)

  if (canOpenPreview) {
    show()
  }

  if (props.openInNewTab) {
    window.open(props.fullUrl, '_blank')
  }
}

onUnmounted(() => {
  if (!props.openInNewTab) {
    removeItem()
  }
})

const isUploading = computed(() => props.isUploading)

const nameWithoutExtension = computed(() => {
  if (props.name?.includes?.('.'))
    return props.name.split('.').slice(0, -1).join(' ')

  return props.name
})

const truncatedName = computed(() => {
  const name = nameWithoutExtension.value
  if (name?.length > 60)
    return `${name.slice(0, 60)}...`

  return name
})

const extension = computed(() => {
  if (props.name?.includes?.('.'))
    return `.${props.name.split('.').pop()}`

  return ''
})

const isImagePreviewSupported = computed(() => {
  const previewUrl = props.previewUrl || ''
  if (!previewUrl)
    return false

  const extension = previewUrl.split('.').pop()?.toLowerCase?.()
  return extension ? ALLOWED_IMAGE_PREVIEW_FORMATS.includes(extension) : false
})

const previewImageStyles = computed(() => {
  const isImageOrVideo = props.type === 'image' || props.type === 'picture' || props.type === 'video'

  if (isImageOrVideo && isImagePreviewSupported.value)
    return { backgroundImage: `url(${props.previewUrl})` }

  return {}
})

const filePreviewClasses = computed(() => {
  return {
    'file-preview': true,
    'file-preview--default': props.view === 'default',
    'file-preview--compact': props.view === 'compact',
    'file-preview--no-preview': !props.previewUrl,
    'file-preview--uploading': isUploading.value,
    'file-preview--image': props.type === 'image' || props.type === 'picture',
    'file-preview--video': props.type === 'video',
    'file-preview--audio': props.type === 'audio',
    'file-preview--screenshot': props.type === 'screenshot',
    'file-preview--placeholder': isPlaceholder.value,
    'file-preview--format-not-supported': isFormatNotSupported.value,
    'file-preview--image-preview-not-supported': !isImagePreviewSupported.value,
  }
})

const triggerComponent = computed(() => {
  return props.openInNewTab ? 'a' : 'div'
})

const triggerProps = computed(() => {
  if (props.openInNewTab) {
    return {
      href: props.fullUrl,
      target: '_blank',
      rel: 'noopener noreferrer',
    }
  }
  return {}
})

const shouldShowPlaceholder = computed(() => {
  const isAudio = props.type === 'audio'
  const isScreenshot = props.type === 'screenshot'

  if (isAudio || isScreenshot)
    return false

  return isPlaceholder.value
})

const shouldShowNotSupportedPlaceholder = computed(() => {
  const isAudio = props.type === 'audio'

  if (isAudio)
    return false

  return !isImagePreviewSupported.value
})
</script>

<template>
  <div
    :class="filePreviewClasses"
  >
    <div v-if="isUploading" class="file-preview__uploading">
      <Spinner />
    </div>
    <div v-else class="file-preview__inner">
      <component
        :is="triggerComponent"
        class="file-preview__trigger"
        v-bind="triggerProps"
        @click.stop.prevent="openPreview"
      >
        <div v-if="type === 'screenshot'" class="file-preview__screenshot-placeholder">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="49" fill="none" viewBox="0 0 48 49">
            <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-opacity=".4" stroke-width="2" d="m43 33.86-5.87-4.58a2.7 2.7 0 0 0-1.63-.52c-.61 0-1.2.19-1.63.52l-7.57 5.95L14.13 23.5a2.7 2.7 0 0 0-1.63-.51c-.61 0-1.2.18-1.63.51L5 29.43M11 2H1v10m0 26v10h10M37 2h10v10m0 26v10H37m-7-27a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z" />
          </svg>
        </div>
        <div v-else-if="shouldShowNotSupportedPlaceholder" class="file-preview__placeholder-not-supported">
          <svg v-if="type === 'image' || type === 'picture'" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M47 36L37.1273 28.2942C36.6958 27.9647 36.1104 27.7795 35.5 27.7795C34.8896 27.7795 34.3042 27.9647 33.8727 28.2942L26.3 34.2449L14.1273 22.515C13.6958 22.1854 13.1104 22.0003 12.5 22.0003C11.8896 22.0003 11.3042 22.1854 10.8728 22.515L1 32.4898M7 47H41C44.3137 47 47 44.3137 47 41V7C47 3.68629 44.3137 1 41 1H7C3.68629 1 1 3.68629 1 7V41C1 44.3137 3.68629 47 7 47ZM30 20C33.3137 20 36 17.3137 36 14C36 10.6863 33.3137 8 30 8C26.6863 8 24 10.6863 24 14C24 17.3137 26.6863 20 30 20Z" stroke="black" stroke-opacity="0.4" stroke-width="2" stroke-linejoin="round" />
          </svg>
          <svg v-else-if="type === 'video'" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 7C1 3.68629 3.68629 1 7 1H41C44.3137 1 47 3.68629 47 7V41C47 44.3137 44.3137 47 41 47H7C3.68629 47 1 44.3137 1 41V7Z" stroke="black" stroke-opacity="0.4" stroke-width="2" />
            <path d="M31.6939 26.639C33.4354 25.6752 33.4354 23.2659 31.6939 22.3022L20.9184 16.3392C19.1769 15.3755 17 16.5801 17 18.5076V30.4336C17 32.3611 19.1769 33.5657 20.9184 32.602L31.6939 26.639Z" stroke="black" stroke-opacity="0.4" stroke-width="2" />
          </svg>
        </div>
        <div v-else-if="shouldShowPlaceholder" class="file-preview__placeholder">
          <svg class="file-preview__placeholder-icon" width="138" height="107" viewBox="0 0 138 107" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M118.401 71.6549L119.631 70.0783L119.623 70.0719L119.615 70.0656L118.401 71.6549ZM112.599 71.6549L111.385 70.0656L111.374 70.0739L111.364 70.0823L112.599 71.6549ZM99.1 82.2627L97.7122 83.7028L98.9663 84.9113L100.336 83.8352L99.1 82.2627ZM37.2313 58.0338L36.2611 56.2849L37.2313 58.0338ZM37.2313 29.1981L36.2611 30.947L37.2313 29.1981ZM56.2909 39.7712L57.2611 38.0222L56.2909 39.7712ZM56.2909 47.4607L57.2611 49.2096L56.2909 47.4607ZM83 25H125.304V21H83V25ZM134 33.6957V94.3044H138V33.6957H134ZM125.304 103H64.6957V107H125.304V103ZM56 94.3044V83.5H52V94.3044H56ZM64.6957 103C59.8932 103 56 99.1068 56 94.3044H52C52 101.316 57.684 107 64.6957 107V103ZM134 94.3044C134 99.1068 130.107 103 125.304 103V107C132.316 107 138 101.316 138 94.3044H134ZM125.304 25C130.107 25 134 28.8932 134 33.6957H138C138 26.684 132.316 21 125.304 21V25ZM137.231 83.8147L119.631 70.0783L117.17 73.2315L134.769 86.9679L137.231 83.8147ZM119.615 70.0656C118.454 69.1789 116.97 68.7373 115.5 68.7373V72.7373C116.207 72.7373 116.809 72.956 117.187 73.2442L119.615 70.0656ZM115.5 68.7373C114.03 68.7373 112.546 69.1789 111.385 70.0656L113.813 73.2442C114.191 72.956 114.793 72.7373 115.5 72.7373V68.7373ZM111.364 70.0823L97.8643 80.6901L100.336 83.8352L113.835 73.2275L111.364 70.0823ZM105.696 58.8696C112.707 58.8696 118.391 53.1855 118.391 46.1739H114.391C114.391 50.9764 110.498 54.8696 105.696 54.8696V58.8696ZM118.391 46.1739C118.391 39.1623 112.707 33.4783 105.696 33.4783V37.4783C110.498 37.4783 114.391 41.3714 114.391 46.1739H118.391ZM105.696 33.4783C98.684 33.4783 93 39.1623 93 46.1739H97C97 41.3714 100.893 37.4783 105.696 37.4783V33.4783ZM93 46.1739C93 53.1855 98.684 58.8696 105.696 58.8696V54.8696C100.893 54.8696 97 50.9764 97 46.1739H93ZM100.488 80.8225L84.3878 65.3082L81.6122 68.1886L97.7122 83.7028L100.488 80.8225ZM12.6127 4H72.7514V0H12.6127V4ZM81.3642 12.6387V72.9244H85.3642V12.6387H81.3642ZM72.7514 81.563H12.6127V85.563H72.7514V81.563ZM4 72.9244V12.6387H0V72.9244H4ZM12.6127 81.563C7.86065 81.563 4 77.7 4 72.9244H0C0 79.8999 5.64231 85.563 12.6127 85.563V81.563ZM81.3642 72.9244C81.3642 77.7 77.5035 81.563 72.7514 81.563V85.563C79.7219 85.563 85.3642 79.8999 85.3642 72.9244H81.3642ZM72.7514 4C77.5035 4 81.3642 7.86305 81.3642 12.6387H85.3642C85.3642 5.66313 79.7219 0 72.7514 0V4ZM12.6127 0C5.6423 0 0 5.66313 0 12.6387H4C4 7.86305 7.86065 4 12.6127 4V0ZM57.2611 38.0222L38.2015 27.4491L36.2611 30.947L55.3207 41.5201L57.2611 38.0222ZM28.3006 33.0428V54.189H32.3006V33.0428H28.3006ZM38.2015 59.7827L57.2611 49.2096L55.3207 45.7118L36.2611 56.2849L38.2015 59.7827ZM28.3006 54.189C28.3006 59.265 33.9071 62.165 38.2015 59.7827L36.2611 56.2849C34.3949 57.3201 32.3006 55.9482 32.3006 54.189H28.3006ZM38.2015 27.4491C33.9071 25.0668 28.3006 27.9669 28.3006 33.0428H32.3006C32.3006 31.2836 34.3949 29.9117 36.2611 30.947L38.2015 27.4491ZM55.3207 41.5201C57.028 42.4672 57.028 44.7647 55.3207 45.7118L57.2611 49.2096C61.7145 46.7391 61.7145 40.4927 57.2611 38.0222L55.3207 41.5201Z" fill="#A6B1BC" />
          </svg>
        </div>
        <div v-else-if="type === 'video' || type === 'audio'" class="file-preview__icon">
          <svg v-if="type === 'video' && view === 'default'" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 7C1 3.68629 3.68629 1 7 1H41C44.3137 1 47 3.68629 47 7V41C47 44.3137 44.3137 47 41 47H7C3.68629 47 1 44.3137 1 41V7Z" stroke="white" stroke-width="2" />
            <path d="M31.6939 26.639C33.4354 25.6752 33.4354 23.2659 31.6939 22.3022L20.9184 16.3392C19.1769 15.3755 17 16.5801 17 18.5076V30.4336C17 32.3611 19.1769 33.5657 20.9184 32.602L31.6939 26.639Z" stroke="white" stroke-width="2" />
          </svg>
          <svg v-else-if="type === 'video' && view === 'compact'" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.5 3.86957C1.5 2.28475 2.78475 1 4.36957 1H20.6304C22.2153 1 23.5 2.28475 23.5 3.86957V20.1304C23.5 21.7153 22.2153 23 20.6304 23H4.36957C2.78475 23 1.5 21.7153 1.5 20.1304V3.86957Z" stroke="white" stroke-width="2" />
            <path d="M16.1797 13.2621C17.0126 12.8012 17.0126 11.6489 16.1797 11.188L11.0262 8.33613C10.1933 7.87522 9.15217 8.45136 9.15217 9.37318V15.0769C9.15217 15.9988 10.1933 16.5749 11.0262 16.114L16.1797 13.2621Z" stroke="white" stroke-width="2" />
          </svg>
          <svg v-if="type === 'audio'" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 15.6667L17.8356 14.6803C17.3534 14.7606 17 15.1778 17 15.6667H18ZM34 13H35C35 12.706 34.8707 12.427 34.6464 12.237C34.4221 12.047 34.1256 11.9653 33.8356 12.0136L34 13ZM7 2H41V0H7V2ZM46 7V41H48V7H46ZM41 46H7V48H41V46ZM2 41V7H0V41H2ZM7 46C4.23858 46 2 43.7614 2 41H0C0 44.866 3.13401 48 7 48V46ZM46 41C46 43.7614 43.7614 46 41 46V48C44.866 48 48 44.866 48 41H46ZM41 2C43.7614 2 46 4.23858 46 7H48C48 3.13401 44.866 0 41 0V2ZM7 0C3.13401 0 0 3.13401 0 7H2C2 4.23858 4.23858 2 7 2V0ZM19 33V15.6667H17V33H19ZM18.1644 16.6531L34.1644 13.9864L33.8356 12.0136L17.8356 14.6803L18.1644 16.6531ZM33 13V30.3333H35V13H33ZM17 33C17 34.6569 15.6569 36 14 36V38C16.7614 38 19 35.7614 19 33H17ZM14 36C12.3431 36 11 34.6569 11 33H9C9 35.7614 11.2386 38 14 38V36ZM11 33C11 31.3431 12.3431 30 14 30V28C11.2386 28 9 30.2386 9 33H11ZM14 30C15.6569 30 17 31.3431 17 33H19C19 30.2386 16.7614 28 14 28V30ZM33 30.3333C33 31.9902 31.6569 33.3333 30 33.3333V35.3333C32.7614 35.3333 35 33.0948 35 30.3333H33ZM30 33.3333C28.3431 33.3333 27 31.9902 27 30.3333H25C25 33.0948 27.2386 35.3333 30 35.3333V33.3333ZM27 30.3333C27 28.6765 28.3431 27.3333 30 27.3333V25.3333C27.2386 25.3333 25 27.5719 25 30.3333H27ZM30 27.3333C31.6569 27.3333 33 28.6765 33 30.3333H35C35 27.5719 32.7614 25.3333 30 25.3333V27.3333Z" fill="black" fill-opacity="0.4" />
          </svg>
        </div>
        <div
          v-if="isImagePreviewSupported"
          class="file-preview__background"
          :style="previewImageStyles"
        />
      </component>
      <div v-if="truncatedName" class="file-preview__name" :data-extension="extension">
        <span class="file-preview__name-text">{{ truncatedName }}</span>
        <span class="file-preview__extension">{{ extension }}</span>
      </div>
      <button class="file-preview__close" type="button" @click.stop="handleRemove">
        <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M2.82913 3.70268L1.18688 2.06043C0.820427 1.69397 0.820427 1.09679 1.18688 0.730337C1.55333 0.363884 2.15052 0.363884 2.51697 0.730338L4.15922 2.37259L5.88291 0.648904C6.3308 0.201017 7.03656 0.201016 7.48445 0.648904C7.93233 1.09679 7.93233 1.80255 7.48445 2.25044L5.76076 3.97413L7.40301 5.61638C7.76946 5.98283 7.76946 6.58002 7.40301 6.94647C7.03656 7.31292 6.43938 7.31292 6.07292 6.94647L4.43067 5.30422L2.70698 7.0279C2.2591 7.47579 1.55333 7.47579 1.10545 7.0279C0.65756 6.58002 0.657559 5.87425 1.10545 5.42637L2.82913 3.70268Z" fill="white" />
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>
.file-preview {
  position: relative;
  background-color: transparent;
  flex: 0 0 auto;
  width: 100px;
}

.file-preview__uploading {
  width: 100px;
  height: 100px;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: white;
  border-radius: 8px;
  cursor: progress;
  z-index: 5;
}

.file-preview__uploading .spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.file-preview__trigger {
  border-radius: 8px;
  height: 100px;
  width: 100px;
  overflow: hidden;
  position: relative;
  cursor: zoom-in;
  transition: background-color 0.3s;
  background-color: white;
  display: block;
}

.file-preview__background {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  transition: transform 0.3s;
}

.file-preview--video .file-preview__background:before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  transition: opacity 0.3s;
}

.file-preview__trigger:hover .file-preview__background {
  transform: scale(1.1);
}

.file-preview__trigger:hover .file-preview__background:before {
  opacity: 0;
}

.file-preview--no-preview .file-preview__background {
  display: none;
}

.file-preview--no-preview .file-preview__trigger {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.file-preview--video.file-preview--no-preview .file-preview__icon {
  opacity: 1 !important;
}

.file-preview--video.file-preview--no-preview .file-preview__icon svg path {
  stroke: rgba(0, 0, 0, 0.4);
}

.file-preview--no-preview .file-preview__trigger:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

.file-preview__icon {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 1;
  transform: translate(-50%, -50%);
  transition:
    transform 0.3s,
    opacity 0.3s;
}

.file-preview__icon svg {
  display: block;
}

.file-preview__trigger:hover .file-preview__icon {
  opacity: 0;
}

.file-preview--audio .file-preview__trigger {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.file-preview--audio .file-preview__background:before {
  display: none;
}

.file-preview--audio .file-preview__trigger {
  background-color: white;
}

.file-preview--audio .file-preview__trigger:hover,
.file-preview--no-preview .file-preview__trigger:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

.file-preview--audio .file-preview__trigger:hover .file-preview__icon {
  opacity: 1;
}

.file-preview__name {
  font-size: 11px;
  line-height: 1.1;
  color: var(--fqz-poll-text-on-place);
  margin-top: 10px;
}

.file-preview__name-text {
  overflow-wrap: anywhere;
}

.file-preview__extension {
  display: inline;
}

.file-preview__close {
  position: absolute;
  top: -10px;
  right: -10px;
  background: rgba(0, 0, 0, 1);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 16px;
  padding: 0;
  line-height: 1;
  cursor: pointer;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
}

.file-preview__close svg {
  display: block;
}

.file-preview__close:hover {
  opacity: 0.8;
}

.file-preview--compact {
  width: 48px;
  height: 48px;
}

.file-preview--compact .file-preview__name {
  display: none;
}

.file-preview--compact .file-preview__close {
  display: none;
}

.file-preview--compact .file-preview__trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.file-preview--compact.file-preview--video .file-preview__background:before {
  background-color: rgba(0, 0, 0, 0.6);
}

.file-preview--placeholder .file-preview__background,
.file-preview--image-preview-not-supported .file-preview__background {
  display: none;
}

.file-preview__placeholder,
.file-preview__placeholder-not-supported {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(231, 235, 237, 1);
}

.file-preview__placeholder-icon {
  display: block;
  width: 70px;
  height: 70px;
  max-width: 100%;
  max-height: 100%;
}

.file-preview--compact .file-preview__placeholder-icon {
  width: 24px;
  height: 19px;
}

.file-preview__placeholder-not-supported {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.file-preview__placeholder-not-supported svg {
  width: 46px;
  height: 46px;
}

.file-preview--compact .file-preview__placeholder-not-supported svg {
  width: 24px;
  height: 24px;
}

.file-preview__screenshot-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.file-preview--screenshot .file-preview__trigger {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

.file-preview--screenshot .file-preview__background {
  display: none;
}
</style>
