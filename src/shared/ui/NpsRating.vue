<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Number,
    default: -1,
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  max: {
    type: Number,
    default: 11,
  },
  colors: {
    type: Array,
    default: () => [],
  },
  startLabel: {
    type: String,
    default: '',
  },
  endLabel: {
    type: String,
    default: '',
  },
  fromOne: {
    type: Boolean,
    default: false,
  },
  design: {
    type: Number,
    default: 1,
  },
})

const emit = defineEmits(['update:modelValue'])

const scale = computed(() => {
  return Array.from({ length: props.max + 1 }, (_, i) => ({
    value: i,
    color: props.colors[i] || '#f1f5f6',
  }))
})

function handleRatingClick(index) {
  if (props.modelValue === index) {
    emit('update:modelValue', -1)
  }
  else {
    emit('update:modelValue', index)
  }
}

const isColored = computed(() => props.design !== 2)
</script>

<template>
  <div
    class="nps-scale"
    :class="{
      'nps-scale--colored': isColored,
      'nps-scale--inited': modelValue >= 0,
      'nps-scale--from-one': fromOne,
      'nps-scale--inactive': inactive,
    }"
    v-bind="$attrs"
  >
    <div class="nps-scale__list">
      <div
        v-for="(item, index) in scale"
        :key="index"
        class="nps-scale__item cursor-pointer"
        :class="{ active: modelValue >= 0 && index === modelValue }"
        :style="{ 'background-color': isColored ? item.color : '#f1f5f6' }"
        data-testid="rating-nps-item"
        @click="handleRatingClick(index)"
      >
        <div class="nps-scale__item-bg" />
        <span>{{ item.value }}</span>
      </div>
    </div>

    <div v-if="startLabel || endLabel" class="nps-scale__labels">
      <div>{{ startLabel }}</div>
      <div>{{ endLabel }}</div>
    </div>

    <input v-if="modelValue >= 0" type="hidden" name="rating" :value="modelValue">
  </div>
</template>

<style scoped>
.nps-scale {
  display: inline-block;
  width: 100%;
  transition: opacity 0.3s;
}

.nps-scale__list {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.nps-scale__item {
  position: relative;
  flex-grow: 1;
  height: 36px;
  cursor: pointer;
  margin: 0;
  flex-basis: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f1f5f6;
  color: black;
  font-size: 15px;
  font-weight: 700;
  padding: 0 4px;
  border-radius: 4px;
  transition: opacity 0.3s;
}

.nps-scale__item:before,
.nps-scale__item-bg {
  content: '';
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 4px;
  background: inherit;
  border: 1px solid #cfd8dc;
  transition: all 250ms;
  z-index: 1;
}

.nps-scale__item span {
  z-index: 2;
}

.nps-scale__item:only-child {
  width: 42px;
}

.nps-scale--colored .nps-scale__item {
  color: white;
}

.nps-scale--colored .nps-scale__item:before,
.nps-scale--colored .nps-scale__item-bg {
  border: none;
}

.nps-scale--inited .nps-scale__item:not(.active) {
  opacity: 0.2;
}

.nps-scale--inited .nps-scale__item.active:before,
.nps-scale--inited .nps-scale__item.active .nps-scale__item-bg {
  transform: scaleX(1.24) scaleY(1.24);
}

.nps-scale--from-one .nps-scale__item:first-child {
  display: none;
}

.nps-scale--inactive .nps-scale__item {
  opacity: 0.5;
}

.nps-scale__labels {
  margin-top: 15px;
  opacity: 0.5;
  display: flex;
  justify-content: space-between;
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
  gap: 30px;
}

.nps-scale__labels div:first-child {
  text-align: left;
}

.nps-scale__labels div:last-child {
  text-align: right;
}

.nps-scale__labels div {
  max-width: 280px;
  overflow: hidden;
  line-height: 1.1;
}

@media screen and (max-width: 679px) {
  .nps-scale__labels {
    margin-top: 15px;
    font-size: 13px;
    gap: 15px;
  }

  .nps-scale__labels div {
    max-width: 150px;
  }

  .nps-scale__list {
    gap: 5px;
  }

  .nps-scale__item {
    height: 36px;
    width: auto;
    max-width: none;
    flex-basis: 0;
    font-size: 13px;
  }

  .nps-scale--inited .nps-scale__item.active:before,
  .nps-scale--inited .nps-scale__item.active .nps-scale__item-bg {
    transform: scaleY(1.5) scaleX(1);
  }
}

@media screen and (max-width: 514px) {
  .nps-scale--inited .nps-scale__item.active:before,
  .nps-scale--inited .nps-scale__item.active .nps-scale__item-bg {
    transform: scaleY(1.5) scaleX(1.18);
  }
}
</style>
