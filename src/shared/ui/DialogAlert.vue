<script setup>
import { onClickOutside } from '@vueuse/core'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  variant: {
    type: String,
    // error, plain, success
    default: 'plain',
  },
  dismissable: {
    type: Boolean,
    default: true,
  },
  dismissAfter: {
    type: Number,
    default: 0,
  },
  open: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:open'])

const anchorEl = ref(null)

const alertPosition = ref(getAnchorPosition())

const transitionOpen = ref(false)

const dialogContentClasses = computed(() => {
  return {
    'dialog-alert-content': true,
    'dialog-alert-content--error': props.variant === 'error',
    'dialog-alert-content--success': props.variant === 'success',
    'dialog-alert-content--plain': props.variant === 'plain',
  }
})

function updatePosition() {
  if (transitionOpen.value) {
    alertPosition.value = getAnchorPosition()
  }
}

function addListeners() {
  window.addEventListener('resize', updatePosition)
  window.addEventListener('scroll', updatePosition)
}

function removeListeners() {
  window.removeEventListener('resize', updatePosition)
  window.removeEventListener('scroll', updatePosition)
}

watch(() => props.open, (newVal) => {
  transitionOpen.value = newVal
  if (newVal) {
    alertPosition.value = getAnchorPosition()
    addListeners()
    if (props.dismissAfter) {
      setTimeout(() => {
        transitionOpen.value = false
        removeListeners()
      }, props.dismissAfter)
    }
  }
  else {
    removeListeners()
  }
})

onMounted(() => {
  if (props.open) {
    addListeners()
  }
})

onUnmounted(() => {
  removeListeners()
})
function getAnchorPosition() {
  if (!anchorEl.value)
    return { top: 0, left: 0 }
  const rect = anchorEl.value.getBoundingClientRect()
  return {
    top: `${rect.bottom}px`,
    left: '50%',
    transform: 'translateX(-50%)',
  }
}

watch(() => props.open, (newVal) => {
  transitionOpen.value = newVal
})

const alertContentRef = ref(null)
onClickOutside(alertContentRef, () => {
  if (props.dismissable && transitionOpen.value) {
    emit('update:open', false)
  }
})
</script>

<template>
  <Teleport to="body">
    <Transition name="alert-fade-up" appear @after-leave="emit('update:open', false)">
      <div
        v-if="transitionOpen"
        ref="alertContentRef"
        :class="dialogContentClasses"
        :style="alertPosition"
        class="dialog-alert-content dialog-alert-content--desktop"
      >
        <div class="dialog-alert-content__inner">
          <p class="dialog-alert-content__text">
            <slot name="content" />
          </p>
        </div>
      </div>
    </Transition>
  </Teleport>

  <span ref="anchorEl">
    <slot name="anchor" />
  </span>
</template>

<style>
.dialog-alert-content {
  padding: 23px 20px 25px;
  background-color: white;
  box-shadow: none;
  border-radius: 9px;
  color: black;
  width: auto;
  overflow: hidden;
  min-width: 140px;
  max-width: 290px;
  z-index: var(--z-index-modal);
  margin-top: 10px;
}

.dialog-alert-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0px 5px 15px 0px rgba(46, 47, 49, 1);
  border-radius: 9px;
  z-index: 1;
  pointer-events: none;
}

.dialog-alert-content__inner {
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;
  z-index: 1;
}

.dialog-alert-content__text {
  margin: 0;
  line-height: 1.3;
  font-size: 14px;
  text-align: center;
}

.dialog-alert-content--success {
  background-color: rgba(171, 239, 183, 1);
}

.dialog-alert-content--desktop {
  position: fixed;
  z-index: var(--z-index-modal);
}

.alert-fade-up-enter-active,
.alert-fade-up-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.alert-fade-up-enter-from,
.alert-fade-up-leave-to {
  opacity: 0;
  transform: translateY(10px) translateX(-50%);
}

.alert-fade-up-enter-to,
.alert-fade-up-leave-from {
  opacity: 1;
  transform: translateY(0) translateX(-50%);
}
</style>
