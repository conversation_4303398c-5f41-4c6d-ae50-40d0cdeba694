<script setup>
import {
  Slider<PERSON><PERSON><PERSON>,
  <PERSON>lide<PERSON>R<PERSON>,
  SliderThumb,
  SliderTrack,
} from 'radix-vue'
import { computed, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array,
    required: true,
  },
  min: {
    type: Number,
    default: 0,
  },
  max: {
    type: Number,
    default: 100,
  },
  step: {
    type: Number,
    default: 1,
  },
})

const emit = defineEmits(['update:modelValue', 'pointerdown'])

const startValue = computed(() => {
  return props.modelValue[0]
})

const sliderTrack = ref()
const emptyTrackPos = ref('0%')

watch(() => props.modelValue, (v) => {
  const start = v[0]
  const percentLeft = ((start - props.min) / (props.max - props.min)) * 100
  emptyTrackPos.value = `${percentLeft}%`
}, { immediate: true })
</script>

<template>
  <div class="slider-container" :style="{ '--empty-track-pos': emptyTrackPos }">
    <SliderRoot
      :model-value="modelValue"
      :min="min"
      :max="max"
      :step="step"
      class="slider-root"
      @update:model-value="emit('update:modelValue', $event)"
      @pointerdown="emit('pointerdown', $event)"
    >
      <SliderTrack ref="sliderTrack" class="slider-track">
        <SliderRange class="slider-range" />
      </SliderTrack>
      <SliderThumb class="slider-thumb">
        <div class="slider-value">
          {{ startValue }}
        </div>
      </SliderThumb>
    </SliderRoot>
  </div>
</template>

<style>
.slider-root {
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
  touch-action: none;
  width: 100%;
}

.slider-track {
  background-color: var(--black-a10);
  position: relative;
  border-radius: 9999px;
  height: 6px;
  overflow: hidden;
  left: 9px;
  width: calc(100% - 18px);
}

.slider-track:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid var(--fqz-poll-main-color);
  border-radius: 40px;
  opacity: 0.5;
  z-index: 1;
  left: var(--empty-track-pos);
  width: calc(100% - var(--empty-track-pos));
}

.slider-range {
  position: absolute;
  background-color: var(--fqz-poll-main-color);
  border-radius: 9999px;
  height: 100%;
  opacity: 0.5;
  z-index: 2;
}

.slider-range:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid var(--fqz-poll-main-color);
  opacity: 0.1;
  z-index: 3;
}

.slider-thumb {
  display: block;
  width: 24px;
  height: 24px;
  background-color: var(--fqz-poll-main-color);
  border-radius: 100%;
  position: relative;
  z-index: 2;
  cursor: pointer;
  transition: opacity 0.3s ease-in-out;
}

.slider-thumb:hover {
  /* opacity: 0.9; */
}

.slider-thumb:focus {
  outline: none;
}

.slider-value {
  position: absolute;
  top: -100%;
  left: 50%;
  transform: translateX(-50%);
  color: var(--fqz-poll-text-on-place);
  font-size: 15px;
  line-height: 1.2;
  font-weight: 700;
}

@media (max-width: 679px) {
  .slider-value {
    font-size: 13px;
  }
}
</style>
