<script setup>
import { computed, ref, toValue, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  type: {
    type: String,
    default: 'heart',
  },
  smiles: {
    type: Array,
    required: true,
  },
  showLabels: {
    type: Boolean,
    default: false,
  },
  labels: {
    type: Array,
    default: () => [],
  },
  inactive: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const value = ref(props.modelValue)

watch(() => props.modelValue, (newValue) => {
  value.value = newValue
})

const icons = computed(() => props.smiles.map(smile => ({
  ...smile,
  url: smile.url,
})))

const label = computed(() => {
  const selectedSmile = icons.value.find(icon => icon.id === value.value)
  return selectedSmile ? selectedSmile.text : ''
})

function handleClick(id) {
  if (value.value === id) {
    value.value = ''
  }
  else {
    value.value = id
  }
  emit('update:modelValue', value.value)
}

function getLabel(icon) {
  return props.labels[icons.value.indexOf(icon)] || icon.label || ''
}
</script>

<template>
  <div class="smile-rating" :class="{ 'smile-rating--labels': showLabels, 'smile-rating--inited': !!value, 'smile-rating--inactive': inactive }" :data-icons="icons.length" :data-type="type" data-testid="smile-rating">
    <div class="smile-rating__wrapper">
      <div class="smile-rating__items">
        <div
          v-for="icon in icons"
          :key="icon.id"
          class="smile-rating-item"
          :class="{ active: value === icon.id }"
          :data-id="icon.id"
          data-testid="smile-rating-item"
          @click="handleClick(icon.id)"
        >
          <div v-if="icon.url" class="smile-rating-item__icon">
            <img :src="icon.url" :alt="toValue(icon.label) || ''">
          </div>
          <div v-else class="smile-rating-item__icon smile-rating-item__icon--empty" />
          <div v-if="showLabels" class="smile-rating-item__label" data-testid="smile-rating-item-label">
            {{ getLabel(icon) }}
          </div>
        </div>
      </div>
      <div v-if="!showLabels && label" class="smile-rating__label">
        {{ label }}
      </div>
    </div>
  </div>
</template>

<style scoped>
:root {
  --fc-rating-bg-color: #ffffff;
  --fc-rating-icon-size: 34px;
  --fc-rating-icon-scale-ratio: 1.3;
  --fc-rating-item-size: 34px;
  --fc-rating-item-gap: 13px;
  --fc-rating-item-label: 13px;
  --fc-rating-item-label-offset: 15px;
  --fc-rating-item-label-width: 100px;
  --fc-rating-label: 16px;
  --fc-rating-label-offset: 15px;
}

.smile-rating--inactive {
  opacity: 0.5;
}

.smile-rating__items {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-left: calc(-1 * var(--fc-rating-item-gap));
  margin-right: calc(-1 * var(--fc-rating-item-gap));
}

.smile-rating-item {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: var(--fc-rating-item-gap);
  margin-right: var(--fc-rating-item-gap);
  width: var(--fc-rating-item-size);
  flex-shrink: 0;
  transition: opacity 250ms;
}

.smile-rating-item__icon {
  width: var(--fc-rating-icon-size);
  will-change: transform;
  transition: transform 250ms;
  font-size: 0;
  object-fit: contain;
}

.smile-rating-item__icon img {
  width: 100%;
  height: auto;
  display: block;
}

.smile-rating-item__label {
  width: 100%;
  max-width: var(--fc-rating-item-label-width);
  text-align: center;
  margin-top: 15px;
  font-weight: bold;
  font-size: 13px;
  line-height: 1.1;
  overflow: hidden;
  position: relative;
}

.smile-rating-item__label:empty {
  margin-top: 0;
}

.smile-rating-item__label::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 10px;
  background: linear-gradient(to left, var(--fqz-poll-main-place-color), transparent);
}

.smile-rating-item.active .smile-rating-item__icon {
  transform: scale(var(--fc-rating-icon-scale-ratio));
}

.smile-rating--inited .smile-rating-item:not(.active) {
  opacity: 0.5;
}

.smile-rating[data-icons='2'] {
  --fc-rating-item-size: 60px;
  --fc-rating-icon-size: 60px;
  --fc-rating-icon-scale-ratio: 1.4;
  --fc-rating-item-gap: 25px;
}

.smile-rating[data-icons='3'] {
  --fc-rating-item-size: 46px;
  --fc-rating-icon-size: 46px;
  --fc-rating-icon-scale-ratio: 1.3;
  --fc-rating-item-gap: 30px;
}

.smile-rating[data-icons='5'] {
  --fc-rating-item-size: 34px;
  --fc-rating-icon-size: 34px;
  --fc-rating-icon-scale-ratio: 1.35;
  --fc-rating-item-gap: 12.5px;
}

.smile-rating[data-type='like'] .smile-rating-item__icon {
  margin-bottom: -10px;
}

@media screen and (max-width: 767.98px) {
  .smile-rating[data-icons='2'] {
    --fc-rating-item-gap: 19px;
  }
  .smile-rating[data-icons='3'] {
    --fc-rating-item-gap: 19px;
  }
  .smile-rating[data-icons='5'] {
    --fc-rating-item-gap: 12.5px;
  }
}

.smile-rating.smile-rating--labels {
  --fc-rating-item-size: 100px;
  --fc-rating-item-gap: 5px;
}

@media screen and (max-width: 679px) {
  .smile-rating.smile-rating--labels[data-icons='2'] {
    --fc-rating-item-size: 100px;
    --fc-rating-item-gap: 10px;
  }
  .smile-rating.smile-rating--labels[data-icons='3'] {
    --fc-rating-item-size: 100px;
    --fc-rating-item-gap: 10px;
  }
  .smile-rating.smile-rating--labels[data-icons='5'] {
    --fc-rating-item-size: 60px;
    --fc-rating-item-gap: 2.5px;
  }
  .smile-rating-item__label {
    font-size: 11px;
  }
}
</style>
