<script setup>
import DialogAlert from '@/shared/ui/DialogAlert.vue'
import Tooltip from '@/shared/ui/Tooltip/Tooltip.vue'
import { computed, ref } from 'vue'

const props = defineProps({
  promocode: {
    type: String,
    required: true,
  },
  copyButtonText: {
    type: String,
    default: 'Копировать промокод',
  },
  copySuccessText: {
    type: String,
    default: 'Скопировано',
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
})

const isOpen = ref(false)
const isTooltipOpen = ref(false)

const componentClasses = computed(() => {
  return {
    'copy-promocode': true,
    'copy-promocode--tablet': props.tabletView,
  }
})

function copyPromocode() {
  navigator.clipboard.writeText(props.promocode)
}

function onClick() {
  copyPromocode()
  isOpen.value = true
}
</script>

<template>
  <DialogAlert v-model:open="isOpen" :text="copySuccessText" :dismiss-after="2000" variant="success">
    <template #anchor>
      <div :class="componentClasses">
        <span class="copy-promocode__text">{{ promocode }}</span>
        <Tooltip v-model:open="isTooltipOpen">
          <template #trigger>
            <button type="button" class="copy-promocode__button" @click.prevent="onClick">
              <span class="copy-promocode__button-icon">
                <svg width="19" height="21" viewBox="0 0 19 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14 13H16C17.1046 13 18 12.1046 18 11V3C18 1.89543 17.1046 1 16 1H10C8.89543 1 8 1.89543 8 3V5M3 20H9C10.1046 20 11 19.1046 11 18V10C11 8.89543 10.1046 8 9 8H3C1.89543 8 1 8.89543 1 10V18C1 19.1046 1.89543 20 3 20Z" stroke="black" stroke-width="2" />
                </svg>
              </span>
            </button>
          </template>
          <template #content>
            <span class="copy-promocode__tooltip-text">{{ copyButtonText }}</span>
          </template>
        </Tooltip>
      </div>
    </template>
    <template #content>
      {{ copySuccessText }}
    </template>
  </DialogAlert>
</template>

<style scoped>
.copy-promocode {
  display: flex;
  align-items: center;

  justify-content: space-between;
  border-radius: 4px;
  background: rgba(255, 255, 255, 1);
  font-size: 16px;
  line-height: 1;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
  min-height: 45px;
  border: 1px solid rgba(207, 216, 220, 1);
  color: black;
  gap: 10px;
  min-width: 140px;
  max-width: 290px;
  line-height: 1 !important;
}

.copy-promocode__button {
  all: unset;
  cursor: pointer;
  padding-top: 10px;
  padding-bottom: 10px;
  flex: 0 0 auto;
}

.copy-promocode__text {
  line-height: 1;
  color: black;
  flex-grow: 1;
  overflow-wrap: anywhere;
}

.copy-promocode .copy-promocode__text {
  margin: 0;
  font-weight: 400;
  font-size: 16px;
}

.copy-promocode__button-icon {
  transition:
    transform 0.3s,
    opacity 0.3s;
  opacity: 0.4;
  display: block;
}

.copy-promocode__button-icon > svg {
  display: block;
}

.copy-promocode__button:hover .copy-promocode__button-icon {
  opacity: 1;
  transform: scale(1.3);
}

@media (max-width: 679px) {
  .copy-promocode__button:hover .copy-promocode__button-icon {
    opacity: 0.4;
    transform: scale(1);
  }
}
</style>
