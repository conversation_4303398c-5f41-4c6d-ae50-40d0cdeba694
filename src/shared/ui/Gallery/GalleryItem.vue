<script setup>
import { useFancybox } from '@/shared/composables/useFancybox'
import StarRating from '@shared/ui/StarRating.vue'
import { useResizeObserver } from '@vueuse/core'
import { computed, onMounted, onUnmounted, ref } from 'vue'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  selectable: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'default',
  },
  groupId: {
    type: String,
    default: '',
  },
  selectButtonText: {
    type: String,
    default: 'Выбрать',
  },
  previewSize: {
    type: Object,
    default: () => ({}),
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  rating: {
    type: Number,
    default: 0,
  },
  isPlaceholder: {
    type: Boolean,
    default: false,
  },
  // Тип плейсхолдера: 'default', 'image', 'video'
  placeholderType: {
    type: String,
    default: 'default',
  },
})

const emit = defineEmits(['select', 'rate', 'update:previewSize'])

const rating = computed({
  get() {
    return props.rating || props.item.rating || 0
  },
  set(v) {
    emit('rate', props.item.id, v)
  },
})

const isVideo = computed(() => {
  const videoExt = ['MP4', 'WMV', 'MOV', '3GP', 'FLV', 'MPEG-1', 'MPEG-2', 'WEBM', 'AVI']
  const urlArr = props.item.url.split('.')
  const url = props.item.url
  const isVideoExt = videoExt.includes(urlArr[urlArr.length - 1].toUpperCase())
  const isYoutube = url && url.includes('youtube.')
  return isVideoExt || isYoutube
})

const { show, addItem, removeItem } = useFancybox(props.groupId || 'gallery')

function openPreview() {
  if (!props.selected && !props.isPlaceholder) {
    show()
  }
  else if (!props.disabled && props.selected) {
    emit('select')
  }
}

onMounted(() => {
  if (!props.isPlaceholder) {
    addItem({
      src: computed(() => props.item.src),
      id: computed(() => props.item.id),
      type: computed(() => isVideo.value ? 'video' : 'image'),
      poster: computed(() => props.item.poster),
    })
  }
})

onUnmounted(() => {
  removeItem()
})

const galleryItemClasses = computed(() => ({
  'gallery-item': true,
  'gallery-item--selectable': props.selectable,
  'gallery-item--selected': props.selected,
  'gallery-item--inactive': props.inactive,
  'gallery-item--disabled': props.disabled,
  'gallery-item--video': isVideo.value,
  'gallery-item--rating': props.type === 'rating',
  'gallery-item--no-label': !props.item.label,
  'gallery-item--no-poster': !props.item.poster,
  'gallery-item--placeholder': props.isPlaceholder,
}))

const galleryItemPreviewTrigger = ref(null)

useResizeObserver(galleryItemPreviewTrigger, (entries) => {
  emit('update:previewSize', {
    width: entries[0].contentRect.width,
    height: entries[0].contentRect.height,
  })
})

const shouldShowSelectButton = computed(() => {
  const isSelectable = props.selectable && props.type !== 'rating'
  const isNotPlaceholder = !props.isPlaceholder
  return isSelectable && isNotPlaceholder
})
</script>

<template>
  <div :class="galleryItemClasses">
    <div class="gallery-item__preview" @click="openPreview">
      <div ref="galleryItemPreviewTrigger" class="gallery-item__preview-trigger">
        <template v-if="isPlaceholder">
          <div class="gallery-item__placeholder">
            <svg v-if="placeholderType === 'default'" width="138" height="107" viewBox="0 0 138 107" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M52.1797 88.8824V91.5421C52.1797 99.9865 59.0099 106.84 67.4456 106.84H122.278C130.714 106.84 137.544 99.9865 137.544 91.5421V36.5757C137.544 28.1312 130.714 21.2773 122.278 21.2773H89.5554V25.2773H122.278C128.495 25.2773 133.544 30.3312 133.544 36.5757V80.3453L119.313 69.2113L119.297 69.1985C118.142 68.3142 116.665 67.8737 115.203 67.8737C113.741 67.8737 112.263 68.3142 111.109 69.1985L111.087 69.2153L99.0638 78.6862L89.5554 69.5013V75.0628L97.5405 82.7761C98.2598 83.471 99.3819 83.5276 100.168 82.9087L113.549 72.3675C113.921 72.0865 114.511 71.8737 115.203 71.8737C115.896 71.8737 116.487 72.0874 116.858 72.3692L116.865 72.3743L133.544 85.4242V91.5421C133.544 97.7865 128.495 102.84 122.278 102.84H67.4456C61.2282 102.84 56.1797 97.7865 56.1797 91.5421V88.8824H52.1797ZM105.032 34.5757C98.3061 34.5757 92.8618 40.0403 92.8618 46.771C92.8618 53.5018 98.3061 58.9664 105.032 58.9664C111.758 58.9664 117.203 53.5018 117.203 46.771C117.203 40.0403 111.758 34.5757 105.032 34.5757ZM96.8618 46.771C96.8618 42.2403 100.524 38.5757 105.032 38.5757C109.54 38.5757 113.203 42.2403 113.203 46.771C113.203 51.3018 109.54 54.9664 105.032 54.9664C100.524 54.9664 96.8618 51.3018 96.8618 46.771Z" fill="#A6B1BC" />
              <path d="M2 12.6387C2 6.76309 6.75148 2 12.6127 2H72.7514C78.6127 2 83.3642 6.76309 83.3642 12.6387V72.9244C83.3642 78.7999 78.6127 83.563 72.7514 83.563H12.6127C6.75148 83.563 2 78.7999 2 72.9244V12.6387Z" stroke="#A6B1BC" stroke-width="4" />
              <path d="M56.2909 47.4607C59.3712 45.7519 59.3712 41.4799 56.2909 39.7712L37.2313 29.1981C34.151 27.4893 30.3006 29.6252 30.3006 33.0428V54.189C30.3006 57.6066 34.151 59.7426 37.2313 58.0338L56.2909 47.4607Z" stroke="#A6B1BC" stroke-width="4" />
            </svg>
            <svg v-if="placeholderType === 'image'" width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M94 71L74.2545 55.5884C73.3917 54.9293 72.2209 54.5589 71 54.5589C69.7791 54.5589 68.6083 54.9293 67.7455 55.5884L52.6 67.4898L28.2545 44.03C27.3917 43.3709 26.2209 43.0005 25 43.0005C23.7791 43.0005 22.6083 43.3709 21.7455 44.03L2 63.9796M17 94H79C87.2843 94 94 87.2843 94 79V17C94 8.71573 87.2843 2 79 2H17C8.71573 2 2 8.71573 2 17V79C2 87.2843 8.71573 94 17 94ZM59.5 40C65.8513 40 71 34.8513 71 28.5C71 22.1487 65.8513 17 59.5 17C53.1487 17 48 22.1487 48 28.5C48 34.8513 53.1487 40 59.5 40Z" stroke="#A6B1BC" stroke-width="4" stroke-linejoin="round" />
            </svg>
            <svg v-if="placeholderType === 'video'" width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 14C2 7.37259 7.37258 2 14 2H82C88.6274 2 94 7.37258 94 14V82C94 88.6274 88.6274 94 82 94H14C7.37259 94 2 88.6274 2 82V14Z" stroke="#A6B1BC" stroke-width="4" />
              <path d="M63.3877 53.2779C66.8707 51.3505 66.8707 46.5319 63.3878 44.6044L41.8367 32.6784C38.3537 30.7509 34 33.1602 34 37.0151V60.8672C34 64.7221 38.3537 67.1314 41.8367 65.204L63.3877 53.2779Z" stroke="#A6B1BC" stroke-width="4" />
            </svg>
          </div>
        </template>
        <template v-else>
          <div class="gallery-item__background" :style="{ backgroundImage: `url(${item.poster})` }" />
          <div class="gallery-item__content">
            <img :src="item.poster" :alt="item.label" class="gallery-item__image">
          </div>
        </template>
      </div>
      <div v-if="isVideo" class="gallery-item__play-icon">
        <svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 14C2 7.37259 7.37258 2 14 2H82C88.6274 2 94 7.37258 94 14V82C94 88.6274 88.6274 94 82 94H14C7.37259 94 2 88.6274 2 82V14Z" stroke="white" stroke-width="4" />
          <path d="M63.3877 53.2779C66.8707 51.3505 66.8707 46.5319 63.3878 44.6044L41.8367 32.6784C38.3537 30.7509 34 33.1602 34 37.0151V60.8672C34 64.7221 38.3537 67.1314 41.8367 65.204L63.3877 53.2779Z" stroke="white" stroke-width="4" />
        </svg>
      </div>
    </div>
    <div v-if="shouldShowSelectButton" class="gallery-item__select">
      <button class="gallery-item__select-button" :class="{ 'gallery-item__select-button--selected': selected }" type="button" @click="$emit('select')">
        {{ selectButtonText }}
      </button>
    </div>
    <div class="gallery-item__selected-check">
      <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 3.82353L5.94118 8.76471L13 1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </div>
    <div v-if="type === 'rating'" class="gallery-item__rating">
      <StarRating
        v-model="rating"
        :max="5"
      />
    </div>
    <div v-if="item.label" class="gallery-item__label">
      {{ item.label }}
    </div>
  </div>
</template>

<style scoped>
.gallery-item {
  position: relative;
  width: 100%;
  transition: opacity 0.3s;
}

.gallery-item__preview {
  position: relative;
  padding-top: 75%; /* 4:3 aspect ratio */
  overflow: hidden;
  cursor: pointer;
  border-radius: 8px;
  max-width: 100%;
}

.gallery-item__preview:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--fqz-poll-main-color);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 5;
}

.gallery-item--video .gallery-item__preview:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background-color: rgba(0, 0, 0, 0.6);
  pointer-events: none;
  transition: opacity 0.3s;
}

.gallery-item__preview-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition: opacity 0.3s;
  cursor: zoom-in;
}

.gallery-item__preview-trigger:hover {
  opacity: 0.8;
}

.gallery-item--selected .gallery-item__preview-trigger {
  pointer-events: none;
}

.gallery-item--selected .gallery-item__preview:after {
  opacity: 0.7;
}

.gallery-item__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: blur(10px);
  transform: scale(3);
}

.gallery-item__content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-item__image,
.gallery-item__video {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.gallery-item__play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

.gallery-item__select {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;
  transition: opacity 0.3s;
}

.gallery-item__selected-check {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 6;
  transition:
    opacity 0.3s,
    transform 0.3s;
  border: 2px solid white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.6);
  pointer-events: none;
}

.gallery-item__selected-check svg {
  display: block;
}

.gallery-item--selected .gallery-item__selected-check {
  opacity: 1;
  transform: scale(1);
}

.gallery-item__select-button {
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  border: 2px solid white;
  font-weight: 400;
  font-size: 13px;
  line-height: 1.17;
  color: white;
  padding: 0 16px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.gallery-item__select-button:hover {
  opacity: 0.8;
}

.gallery-item--selected .gallery-item__select {
  opacity: 0;
  pointer-events: none;
}

.gallery-item__select-button--selected {
  background-color: var(--fqz-poll-secondary-color);
}

.gallery-item__label {
  margin-top: 15px;
  font-size: 15px;
  line-height: 1.1;
  text-align: center;
  transition: opacity 0.3s;
}

.gallery-item__rating {
  transition: opacity 0.3s;
  margin-top: 15px;
  margin-bottom: 15px;
}

.gallery-item--no-label .gallery-item__rating {
  margin-bottom: 0;
}

.gallery-item--inactive .gallery-item__preview-trigger,
.gallery-item--inactive .gallery-item__label,
.gallery-item--inactive .gallery-item__rating {
  opacity: 0.5;
}

.gallery-item--inactive .gallery-item__select-button {
  opacity: 0.7;
}

.gallery-item--disabled .gallery-item__select-button {
  opacity: 0.7;
  pointer-events: none;
  cursor: not-allowed;
}

.gallery-item--placeholder .gallery-item__preview {
  cursor: default;
  background-color: #eceff1;
}

.gallery-item__placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #eceff1;
  width: 90px;
  height: 90px;
  transform: translate(-50%, -50%);
  display: block;
}

.gallery-item__placeholder svg {
  display: block;
  max-width: 100%;
  max-height: 100%;
}

.gallery-item--placeholder .gallery-item__preview-trigger {
  pointer-events: none;
}

@media (max-width: 679px) {
  .gallery-item__label {
    font-size: 13px;
    line-height: 1.1;
  }
}
</style>
