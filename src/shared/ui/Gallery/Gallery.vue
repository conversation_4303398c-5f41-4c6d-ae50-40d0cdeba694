<script setup>
import { useTabletStore } from '@/shared/store/useTabletStore'
import { computed, nextTick, onMounted, ref, toValue, watch } from 'vue'
import GalleryItem from './GalleryItem.vue'

const props = defineProps({
  gallery: {
    type: Array,
    required: true,
  },
  selectable: {
    type: Boolean,
    default: false,
  },
  groupId: {
    type: String,
    default: '',
  },
  selectedItems: {
    type: [Array, Object],
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: 'default',
    validator: value => ['default', 'rating'].includes(value),
  },
  error: {
    type: String,
    default: null,
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  selectButtonText: {
    type: String,
    default: 'Выбрать',
  },
  showPlaceholder: {
    type: Boolean,
    default: true,
  },
  slidesPerView: {
    type: Number,
    default: null,
  },

  // Тип плейсхолдера: 'default', 'image', 'video'
  placeholderType: {
    type: String,
    default: 'default',
  },
})

const emit = defineEmits(['update:selectedItems', 'update:rating', 'swiper'])

const isSelected = computed(() => (item) => {
  if (!props.selectedItems && !props.selectable)
    return false
  if (props.multiple) {
    return props.selectedItems.some(selectedItem => selectedItem.id === item.id)
  }
  else {
    return props.selectedItems?.id === item.id
  }
})

function toggleSelectItem(item) {
  const items = toValue(props.selectedItems)
  let newSelectedItems
  if (props.multiple) {
    newSelectedItems = [...items]
    const isObject = !!item && typeof item === 'object'
    const index = isObject
      ? newSelectedItems.findIndex(i => i.id === item.id)
      : newSelectedItems.indexOf(item)
    if (index > -1) {
      newSelectedItems.splice(index, 1)
    }
    else {
      newSelectedItems.push(item)
    }
  }
  else {
    const normalizedSelectedItems = Array.isArray(items) ? items : [items]

    const foundItem = normalizedSelectedItems.find((i) => {
      const isObject = !!i && typeof i === 'object'
      return isObject ? i.id === item.id : i === item
    })
    newSelectedItems = foundItem ? null : item
  }
  emit('update:selectedItems', newSelectedItems)
}

const buttonPrev = ref(null)
const buttonNext = ref(null)
const tabletStore = useTabletStore()
const tabletView = computed(() => tabletStore.isTabletMode)

function setRating(itemId, rating) {
  emit('update:rating', { id: itemId, rating })
}

const swiper = ref(null)

onMounted(() => {
  const swiperInstance = swiper.value.swiper
  if (swiperInstance) {
    swiperInstance.navigation.nextEl = buttonNext.value
    swiperInstance.navigation.prevEl = buttonPrev.value
    swiperInstance.navigation.update()
  }
})

const previewSize = ref({})

function getButtonClasses(direction) {
  return {
    [`gallery__button-${direction}`]: true,
  }
}

const desktopSlidesPerView = computed(() => {
  // If slidesPerView prop is provided, use it
  if (props.slidesPerView !== null) {
    return props.slidesPerView
  }
  // Otherwise use default logic
  const galleryLength = props.gallery.length

  if (tabletView.value && galleryLength > 2) {
    return 3
  }
  else if (galleryLength > 1) {
    return 2
  }
  return 1
})

defineExpose({
  swiper,
})

const galleryStyles = computed(() => {
  return {
    '--gallery-item-width': `${previewSize.value?.width || 0}px`,
    '--gallery-item-height': `${previewSize.value?.height || 0}px`,
  }
})

const galleryClasses = computed(() => {
  return {
    'gallery': true,
    'gallery--inactive': props.inactive,
    'gallery--single': props.gallery.length === 1,
  }
})

const hasValidItems = computed(() => {
  if (!props.gallery || props.gallery.length === 0)
    return false
  return props.gallery.some(item => item.url)
})

const displayItems = computed(() => {
  if (!hasValidItems.value && props.showPlaceholder) {
    return [{
      id: 'placeholder',
      label: '',
      url: '',
      poster: '',
      isPlaceholder: true,
      rating: ref(0),
    }]
  }

  const galleryItems = props.gallery || []
  return galleryItems.map(item => ({
    ...item,
    isPlaceholder: !item.url,
  }))
})
watch(() => props.gallery, () => {
  if (swiper.value) {
    const swiperInstance = swiper.value.swiper
    if (swiperInstance) {
      nextTick(() => {
        swiperInstance.update()
      })
    }
  }
}, { deep: true })
</script>

<template>
  <div :style="galleryStyles" :class="galleryClasses">
    <div v-if="error" class="gallery__error">
      {{ error }}
    </div>
    <swiper-container
      ref="swiper"
      :slides-per-view="desktopSlidesPerView"
      :space-between="10"
      :pagination="false"
      :resize-observer="false"
      :mutation-observer="false"
      :auto-height="true"
      :navigation="{}"
      :breakpoints="{
        679: {
          slidesPerView: desktopSlidesPerView,
        },
        0: {
          slidesPerView: 1,
        },
      }"
    >
      <swiper-slide v-for="(item, index) in displayItems" :key="item.id" :style="{ maxWidth: '100%' }">
        <GalleryItem
          v-if="index === 0"
          v-model:preview-size="previewSize"
          :item="item"
          :selectable="selectable"
          :group-id="groupId"
          :select-button-text="selectButtonText"
          :disabled="item.isDisabled"
          :inactive="inactive"
          :selected="isSelected(item)"
          :type="type"
          :rating="item.rating?.value"
          :is-placeholder="item.isPlaceholder"
          :placeholder-type="placeholderType"
          @select="toggleSelectItem(item)"
          @rate="setRating"
        />
        <GalleryItem
          v-else
          :item="item"
          :selectable="selectable"
          :group-id="groupId"
          :select-button-text="selectButtonText"
          :disabled="item.isDisabled"
          :inactive="inactive"
          :selected="isSelected(item)"
          :type="type"
          :rating="item.rating?.value"
          :is-placeholder="item.isPlaceholder"
          @select="toggleSelectItem(item)"
          @rate="setRating"
        />
      </swiper-slide>
    </swiper-container>
    <div ref="buttonPrev" :class="getButtonClasses('prev')" @click="swiper.swiper.slidePrev()">
      <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M13 5H1M1 5L5 1M1 5L5 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </div>
    <div ref="buttonNext" :class="getButtonClasses('next')" @click="swiper.swiper.slideNext()">
      <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 5H13M13 5L9 1M13 5L9 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </div>
  </div>
</template>

<style scoped>
.gallery {
  width: 100%;
  position: relative;
  --gallery-item-height: 200px;
}

.gallery__error {
  color: red;
  margin-bottom: 10px;
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  color: var(--fqz-poll-main-color);
}

:deep(.swiper-pagination-bullet-active) {
  background-color: var(--fqz-poll-main-color);
}

.gallery__button-prev,
.gallery__button-next {
  position: absolute;
  top: calc(var(--gallery-item-height, 50%) / 2);
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 10;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: background-color 0.3s;
}

.gallery__button-prev {
  left: -16px;
}

.gallery__button-next {
  right: -16px;
}

.gallery__button-prev:hover,
.gallery__button-next:hover {
  background: rgba(0, 0, 0, 1);
}

.gallery__button-prev.swiper-button-lock,
.gallery__button-next.swiper-button-lock {
  opacity: 0;
  pointer-events: none;
}

.gallery :deep(.swiper-button-prev) {
  display: none !important;
}

.gallery :deep(.swiper-button-next) {
  display: none !important;
}

.gallery__button-prev.swiper-button-disabled,
.gallery__button-next.swiper-button-disabled {
  opacity: 0;
  pointer-events: none;
}

swiper-container::part(button-prev),
swiper-container::part(button-next) {
  display: none !important;
}

swiper-container::part(swiper-slide) {
  max-width: 100%;
}

.gallery--single swiper-slide {
  width: 100% !important;
}

.gallery--single :deep(.gallery-item__preview) {
  padding-top: 56.2%;
}

@media (max-width: 679px) {
  .gallery__button-prev {
    left: 5px;
  }
  .gallery__button-next {
    right: 5px;
  }
}
</style>
