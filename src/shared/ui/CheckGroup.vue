<script setup>
import { toValue } from 'vue'
import Check from './Check.vue'

defineProps({
  options: {
    type: Array,
    required: true,
  },
  type: {
    type: String,
    default: 'checkbox',
    validator: value => ['checkbox', 'radio'].includes(value),
  },
  field: {
    type: String,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  checkAttrs: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['change'])

const model = defineModel({
  type: [Array, String, Number, Boolean],
  default: () => [],
})

function onChange(event) {
  emit('change', event)
}
</script>

<template>
  <div class="check-group">
    <div
      v-for="option in options"
      :key="option.value"
      class="check-group__item"
    >
      <Check
        v-model="model"
        :type="type"
        :label="toValue(option.label)"
        :value="option.value"
        :disabled="option.disabled"
        :inactive="option.inactive"
        :hint="option.description"
        v-bind="checkAttrs"
        @change="onChange"
      />
      <slot name="after-item" :option="option" />
    </div>
  </div>
</template>

<style scoped>
.check-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.check-group__item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
