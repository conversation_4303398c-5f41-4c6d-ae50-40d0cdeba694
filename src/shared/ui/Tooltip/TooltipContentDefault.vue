<script setup>
import { Toolt<PERSON>Arrow, TooltipContent, useForwardPropsEmits } from 'radix-vue'

const props = defineProps({
  showArrow: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['pointer-down-outside', 'focus-outside'])

const forwarded = useForwardPropsEmits(props, emit)
</script>

<template>
  <TooltipContent
    class="TooltipContentDefault"
    align="center"
    v-bind="forwarded"
    @pointer-down-outside.prevent
    @focus-outside.prevent
  >
    <div class="TooltipContentDefault__inner">
      <slot />
    </div>
    <TooltipArrow
      v-if="showArrow"
      class="TooltipContentDefault__arrow"
      as="div"
      :width="19"
      :height="6"
    >
      <svg width="19" height="6" viewBox="0 0 19 6" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M13.7154 3.29183L13.9649 2.85601C14.988 1.06896 16.6854 0 18.5 0H0.5C2.32393 0 4.01489 1.13804 4.95754 2.99998C6.91704 6.87043 11.5777 7.02573 13.7154 3.29183Z" fill="black" />
      </svg>
    </TooltipArrow>
  </TooltipContent>
</template>

<style>
.TooltipContentDefault {
  animation-duration: 0.3s;
  animation-timing-function: ease;
  z-index: var(--z-index-popover);
  position: relative;
}

.TooltipContentDefault > span {
  display: block;
  visibility: visible !important;
}

.TooltipContentDefault__inner {
  border-radius: 8px;
  padding: 15px;
  font-size: 12px;
  line-height: 1.3;
  color: white;
  background: black;
  text-align: center;
  min-width: 120px;
  max-width: 500px;
  position: relative;
  z-index: 2;
}

.TooltipContentDefault[data-side='bottom'] .TooltipContentDefault__inner {
  border-radius: 8px;
}

.TooltipContentDefault[data-side='bottom'] .TooltipContentDefault__arrow {
  /* transform: translateX(-1px) translateY(0px) !important; */
}

.TooltipContentDefault[data-state='delayed-open'][data-side='top'] {
  animation-name: tooltipSlideDownAndFade;
}

.TooltipContentDefault[data-state='closed'][data-side='top'] {
  animation-name: tooltipSlideUpAndFadeOut;
}

.TooltipContentDefault[data-state='delayed-open'][data-side='right'] {
  animation-name: tooltipSlideLeftAndFade;
}

.TooltipContentDefault[data-state='delayed-open'][data-side='bottom'] {
  animation: 2s tooltipSlideUpAndFade;
}

.TooltipContentDefault[data-state='closed'][data-side='bottom'] {
  animation-name: tooltipSlideDownAndFadeOut;
}

.TooltipContentDefault[data-state='delayed-open'][data-side='left'] {
  animation-name: tooltipSlideRightAndFade;
}

.TooltipContentDefault__arrow {
  fill: var(--fqz-poll-main-place-color);
  color: var(--fqz-poll-main-place-color);
  width: 12px;
  height: 6px;
  transform: translateX(1px);
  z-index: 2;
}

.TooltipContentDefault__arrow svg {
  display: block;
}
</style>
