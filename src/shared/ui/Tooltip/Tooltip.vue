<script setup>
import { Toolt<PERSON>Portal, TooltipProvider, TooltipRoot, TooltipTrigger, useForwardPropsEmits } from 'radix-vue'
import HintTooltipContent from './HintTooltipContent.vue'
import TooltipContentDefault from './TooltipContentDefault.vue'

const props = defineProps({
  showArrow: {
    type: Boolean,
    default: true,
  },
  delayDuration: {
    type: Number,
    default: 200,
  },
  view: {
    type: String,
    // 'hint' | 'default'
    default: 'default',
  },
  open: {
    type: Boolean,
    default: false,
  },
  alignPosition: {
    type: String,
    // 'end' | 'center'
    default: 'end',
  },
  maxWidthContent: {
    type: Number,
    default: 290,
  },
  notDesign: {
    type: Boolean,
    default: false,
  },
  disableCloseOnTriggerClick: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['close', 'update:open'])

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <TooltipProvider
    v-bind="forwarded"
  >
    <TooltipRoot
      :ignore-non-keyboard-focus="true"
      :disable-closing-trigger="disableCloseOnTriggerClick"
    >
      <TooltipTrigger
        v-if="$slots.trigger"
        as-child
      >
        <slot name="trigger" />
      </TooltipTrigger>
      <TooltipPortal>
        <TooltipContentDefault v-if="view === 'default' && $slots.content">
          <slot name="content" />
        </TooltipContentDefault>
        <HintTooltipContent
          v-if="view === 'hint' && $slots.content"
          :align-position="alignPosition"
          :max-width-content="maxWidthContent"
          :not-design="notDesign"
        >
          <slot name="content" />
        </HintTooltipContent>
      </TooltipPortal>
    </TooltipRoot>
  </TooltipProvider>
</template>
