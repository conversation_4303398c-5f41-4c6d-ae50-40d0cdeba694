<script setup>
import { TooltipArrow, TooltipContent, useForwardPropsEmits } from 'radix-vue'
import { computed } from 'vue'

const props = defineProps({
  showArrow: {
    type: Boolean,
    default: true,
  },
  alignPosition: {
    type: String,
    default: 'end',
  },
  maxWidthContent: {
    type: Number,
    default: 290,
  },
  notDesign: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['pointer-down-outside', 'focus-outside'])

const maxWidth = computed(() => `${props.maxWidthContent}px`)

const forwarded = useForwardPropsEmits(props, emit)
</script>

<template>
  <TooltipContent
    class="HintTooltipContent"
    :align="alignPosition"
    :side-offset="3"
    :align-offset="10"
    v-bind="forwarded"
  >
    <div
      class="HintTooltipContent__inner"
      :class="{
        'HintTooltipContent__inner--center': alignPosition === 'center',
        'HintTooltipContent__inner--bg-white': notDesign,
      }"
    >
      <slot />
    </div>
    <TooltipArrow
      v-if="showArrow"
      class="HintTooltipContent__arrow"
      as="div"
      width="12"
      :height="6"
    >
      <template v-if="alignPosition === 'end'">
        <svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11 0H0C4.65631 0 6 6 9 5.99923C10.3 5.99923 11 5.3 11 4V0Z" fill="currentColor" />
        </svg>
      </template>
      <template v-else-if="alignPosition === 'center'">
        <svg width="18" height="6" viewBox="0 0 18 6" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11 0.000732422H0C4.65631 0.000732422 6 6.00073 9 5.99997C10.3 5.99997 11 5.30073 11 4.00073V0.000732422Z" fill="white" />
          <path d="M7 0H18C13.3437 0 12 6.00073 9 5.99997C7.7 5.99997 7 5.3 7 4V0Z" fill="white" />
        </svg>
      </template>
    </TooltipArrow>
  </TooltipContent>
</template>

<style>
.HintTooltipContent {
  animation-duration: 0.3s;
  animation-timing-function: ease;
  z-index: var(--z-index-popover);
  position: relative;
}

.HintTooltipContent:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  box-shadow: 0px 5px 15px 0px var(--fqz-poll-text-on-place);
  opacity: 0.35;
  border-radius: 8px;
  z-index: -1;
}

.HintTooltipContent > span {
  display: block;
  visibility: visible !important;
}

.HintTooltipContent__inner {
  border-radius: 8px 8px 0 8px;
  padding: 15px;
  font-size: 13px;
  line-height: 1.3;
  color: var(--fqz-poll-text-on-place);
  background: var(--fqz-poll-main-place-color);
  min-width: 140px;
  max-width: v-bind(maxWidth);
  position: relative;
  z-index: 2;
}

.HintTooltipContent__inner--center {
  border-radius: 8px;
}

.HintTooltipContent__inner--bg-white {
  background: #ffffff;
}

.HintTooltipContent[data-side='bottom'] .HintTooltipContent__inner {
  border-radius: 8px 0 8px 8px;
}

.HintTooltipContent[data-side='bottom']::after {
  border-radius: 8px 0 8px 8px;
}

.HintTooltipContent[data-side='bottom'] .HintTooltipContent__arrow {
  transform: translateX(-1px) translateY(0px) !important;
}

.HintTooltipContent[data-state='delayed-open'][data-side='top'] {
  animation-name: tooltipSlideDownAndFade;
}

.HintTooltipContent[data-state='closed'][data-side='top'] {
  animation-name: tooltipSlideUpAndFadeOut;
}

.HintTooltipContent[data-state='delayed-open'][data-side='right'] {
  animation-name: tooltipSlideLeftAndFade;
}

.HintTooltipContent[data-state='delayed-open'][data-side='bottom'] {
  animation-name: tooltipSlideUpAndFade;
}

.HintTooltipContent[data-state='closed'][data-side='bottom'] {
  animation-name: tooltipSlideDownAndFadeOut;
}

.HintTooltipContent[data-state='delayed-open'][data-side='left'] {
  animation-name: tooltipSlideRightAndFade;
}

.HintTooltipContent__arrow {
  fill: var(--fqz-poll-main-place-color);
  color: var(--fqz-poll-main-place-color);
  width: 12px;
  height: 6px;
  transform: translateX(1px);
  z-index: 2;
}

.HintTooltipContent__arrow svg {
  display: block;
}
</style>
