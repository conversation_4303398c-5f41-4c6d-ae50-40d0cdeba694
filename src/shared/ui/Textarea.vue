<script setup>
import { onMounted, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  isInvalid: {
    type: Boolean,
    default: false,
  },
  maxlength: {
    type: Number,
    default: () => 500,
  },
  minlength: {
    type: Number,
    default: null,
  },
  minHeight: {
    type: String,
    default: '100px',
  },
  placeholder: {
    type: String,
    default: '',
  },
  adaptiveHeight: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue'])
const textareaRef = ref(null)

function adjustHeight() {
  if (props.adaptiveHeight && textareaRef.value) {
    textareaRef.value.style.height = props.minHeight
    const scrollHeight = textareaRef.value.scrollHeight
    textareaRef.value.style.height = `${Math.min(scrollHeight, 250)}px`

    if (textareaRef.value.scrollHeight > 250) {
      textareaRef.value.style.height = '250px'
      textareaRef.value.style.overflowY = 'auto'
    }
    else {
      textareaRef.value.style.overflowY = 'hidden'
    }
  }
}

function onInput(event) {
  emit('update:modelValue', event.target.value)
  adjustHeight()
}

onMounted(() => {
  adjustHeight()
})

watch(() => props.modelValue, () => {
  adjustHeight()
})
</script>

<template>
  <textarea
    ref="textareaRef"
    :value="modelValue"
    :style="{ '--min-height': minHeight }"
    class="form-control"
    :class="[{ 'is-invalid': isInvalid, 'adaptive-height': adaptiveHeight }]"
    :maxlength="maxlength"
    :minlength="minlength"
    :placeholder="placeholder"
    v-bind="$attrs"
    @input="onInput"
  />
</template>

<style scoped>
.form-control {
  --min-height: 100px;
  --min-height-mobile: 70px;
  display: block;
  width: 100%;
  padding: 10px 15px 9px 15px;
  line-height: 1.36;
  color: black;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #cfd8dc;
  border-radius: 4px;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  min-height: var(--min-height-mobile);
  border-radius: 4px;
  font-size: 16px;
  box-shadow: none !important;
  margin: 0;
  resize: none;
  overflow: hidden;
}

.form-control.adaptive-height {
  max-height: 250px;
}

.form-control::placeholder {
  color: #6c757d;
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control:focus {
  color: black;
  background-color: #fff;
  border-color: var(--fqz-poll-main-color);
  outline: 0;
}

.form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

@media screen and (min-width: 768px) {
  textarea.form-control {
    min-height: var(--min-height);
  }
}
</style>
