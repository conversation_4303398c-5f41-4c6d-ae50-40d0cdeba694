<script setup>
import icons from '@/shared/assets/icons'
import { computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  size: {
    type: [Number, String],
    default: '24px',
  },
  width: {
    type: Number,
  },
  height: {
    type: Number,
  },
  color: {
    type: String,
    default: 'currentColor',
  },
})

const iconSvg = computed(() => {
  return icons[props.name] || ''
})

const iconStyle = computed(() => {
  return {
    width: `${props.width || props.size}`,
    height: `${props.height || props.size}`,
    fill: props.color,
  }
})
</script>

<template>
  <span
    class="fc-icon"
    :style="iconStyle"
    v-html="iconSvg"
  />
</template>

<style scoped>
.fc-icon {
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.fc-icon :deep(svg) {
  display: block;
}
</style>
