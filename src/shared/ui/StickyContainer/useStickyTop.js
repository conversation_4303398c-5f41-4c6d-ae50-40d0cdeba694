import { useElementBounding } from '@vueuse/core'
import { computed, onMounted, onUnmounted, ref, watchEffect } from 'vue'

const list = ref([])

export function useStickyTop(container) {
  const prevSticky = ref()

  onMounted(() => {
    const elemRef = container
    const newList = list.value
    newList.push(elemRef)
    list.value = newList
  })

  onUnmounted(() => {
    list.value = list.value.filter(elem => elem.value !== container.value)
  })

  watchEffect(() => {
    const elem = container.value
    const orderedList = Array.from(document.querySelectorAll('.sticky-container'))

    const newList = list.value
    newList.sort((a, b) => {
      const aIndex = orderedList.indexOf(a.value)
      const bIndex = orderedList.indexOf(b.value)

      return aIndex - bIndex
    })

    list.value = newList

    const index = list.value.findIndex(item => item.value === elem) - 1
    if (index >= 0) {
      prevSticky.value = list.value[index]
    }
    else {
      prevSticky.value = null
    }
  })

  const stickyTop = computed(() => {
    if (prevSticky.value) {
      const { height, top } = useElementBounding(prevSticky.value)

      return height.value + top.value
    }
    else {
      return 0
    }
  })
  return { stickyTop }
}
