<script setup>
import { useElementBounding } from '@vueuse/core'
import { computed, useTemplateRef } from 'vue'
import { useStickyTop } from './useStickyTop'

const container = useTemplateRef('container')
const sticky = useTemplateRef('sticky')

const { left, right, top, width } = useElementBounding(container)
const { stickyTop } = useStickyTop(sticky)
const { height } = useElementBounding(sticky)

const containerStyle = computed(() => {
  return { height: `${height.value}px` }
})

const isSticky = computed(() => {
  return top.value < stickyTop.value
})

const stickyStyle = computed(() => {
  if (isSticky.value) {
    return {
      top: `${stickyTop.value}px`,
      left: `${left.value}px`,
      right: `${right.value - width.value}px`,
    }
  }
  else {
    return null
  }
})
</script>

<template>
  <div
    ref="container"
    :style="containerStyle"
  >
    <div
      ref="sticky"
      class="sticky-container"
      :class="{ 'sticky-container_fixed': isSticky }"
      :style="stickyStyle"
    >
      <slot :is-sticky="isSticky" />
    </div>
  </div>
</template>

<style scoped>
.sticky-container_fixed {
  position: fixed;
  z-index: var(--z-index-sticky);
}
</style>
