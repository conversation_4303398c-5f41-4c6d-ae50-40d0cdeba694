<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: 'Перетащите файлы сюда, чтобы загрузить их.',
  },
  description: {
    type: String,
    default: null,
  },
  maxSizeInMb: {
    type: Number,
    default: 5,
  },
})

const defaultDescription = computed(() => {
  return `Максимальный размер файла — ${props.maxSizeInMb} Мб`
})
</script>

<template>
  <div class="file-drop-placeholder">
    <div class="file-drop-placeholder__inner">
      <div class="file-drop-placeholder__icon">
        <svg class="" width="38" height="40" viewBox="0 0 38 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M36 18.8768L19.9256 34.9288C17.9563 36.8952 15.2855 38 12.5006 38C9.71564 38 7.04478 36.8952 5.07554 34.9288C3.10631 32.9623 2 30.2951 2 27.5141C2 24.7331 3.10631 22.0659 5.07554 20.0995L21.15 4.0475C22.4628 2.73651 24.2434 2 26.1 2C27.9566 2 29.7372 2.73651 31.05 4.0475C32.3628 5.35849 33.1004 7.13657 33.1004 8.99059C33.1004 10.8446 32.3628 12.6227 31.05 13.9337L14.9581 29.9857C14.3017 30.6412 13.4114 31.0094 12.4831 31.0094C11.5548 31.0094 10.6645 30.6412 10.0081 29.9857C9.35165 29.3302 8.98288 28.4411 8.98288 27.5141C8.98288 26.5871 9.35165 25.6981 10.0081 25.0426L24.8581 10.2307" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>
      <h3 class="file-drop-placeholder__title">
        {{ title }}
      </h3>
      <p class="file-drop-placeholder__description">
        {{ description || defaultDescription }}
      </p>
    </div>
  </div>
</template>

<style scoped>
.file-drop-placeholder {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  position: relative;
  background-color: transparent;
  align-self: stretch;
}

.file-drop-placeholder__inner {
  position: relative;
}

.file-drop-placeholder:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--fqz-poll-main-place-color);
  opacity: 0.95;
}

.file-drop-placeholder:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px dashed var(--fqz-poll-text-on-place);
  opacity: 0.4;
  border-radius: 8px;
  pointer-events: none;
}

.file-drop-placeholder__icon {
  margin-bottom: 15px;
  color: var(--fqz-poll-text-on-place);
  opacity: 0.4;
}

.file-drop-placeholder__title {
  font-size: 15px;
  line-height: 1.2;
  margin-bottom: 5px;
  color: var(--fqz-poll-text-on-place);
  font-weight: 400;
}

.file-drop-placeholder__description {
  font-size: 12px;
  line-height: 1.2;
  color: var(--fqz-poll-text-on-place);
  font-weight: 400;
}
</style>
