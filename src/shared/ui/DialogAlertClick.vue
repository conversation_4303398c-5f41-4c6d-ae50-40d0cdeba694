<script setup>
import { onClickOutside } from '@vueuse/core'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  variant: {
    type: String,
    // error, plain, success
    default: 'plain',
  },
  dismissable: {
    type: Boolean,
    default: true,
  },
  dismissAfter: {
    type: Number,
    default: 0,
  },
  open: {
    type: Boolean,
    default: false,
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 }),
  },
  preferredPosition: {
    type: String,
    default: 'top',
    validator: value => ['top', 'left', 'right'].includes(value),
  },
  offset: {
    type: Number,
    default: 10,
  },
})

const emit = defineEmits(['update:open'])

const anchorEl = ref(null)

const alertPosition = ref(getAnchorPosition())

const transitionOpen = ref(false)

const dialogContentClasses = computed(() => {
  return {
    'dialog-alert-content': true,
    'dialog-alert-content--error': props.variant === 'error',
    'dialog-alert-content--success': props.variant === 'success',
    'dialog-alert-content--plain': props.variant === 'plain',
  }
})

function updatePosition() {
  if (transitionOpen.value) {
    alertPosition.value = getAnchorPosition()
  }
}

function addListeners() {
  window.addEventListener('resize', updatePosition)
  window.addEventListener('scroll', updatePosition)
}

function removeListeners() {
  window.removeEventListener('resize', updatePosition)
  window.removeEventListener('scroll', updatePosition)
}

watch(() => props.open, (newVal) => {
  transitionOpen.value = newVal
  if (newVal) {
    alertPosition.value = getAnchorPosition()
    addListeners()
    if (props.dismissAfter) {
      setTimeout(() => {
        transitionOpen.value = false
        removeListeners()
      }, props.dismissAfter)
    }
  }
  else {
    removeListeners()
  }
})

onMounted(() => {
  if (props.open) {
    addListeners()
  }
})

onUnmounted(() => {
  removeListeners()
})

function getAnchorPosition() {
  if (!anchorEl.value)
    return { top: 0, left: 0 }

  const rect = anchorEl.value.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const tooltipWidth = 290 // max-width from CSS
  const tooltipHeight = 100 // approximate height
  const safeMargin = 20 // safe margin from screen edges

  if (props.position) {
    const { x, y } = props.position
    let position = {
      left: `${x}px`,
      top: `${y}px`,
      transform: 'translate(-50%, 10px)',
    }

    // Check available space in all directions
    const spaceBelow = viewportHeight - y
    const spaceLeft = x
    const spaceRight = viewportWidth - x

    // First try to position below the click point
    if (spaceBelow >= tooltipHeight + safeMargin) {
      const adjustedX = Math.min(
        Math.max(x, tooltipWidth / 2 + safeMargin),
        viewportWidth - tooltipWidth / 2 - safeMargin,
      )
      position = {
        left: `${adjustedX}px`,
        top: `${y + props.offset}px`,
        transform: 'translateX(-50%)',
      }
    }
    // If not enough space below, try sides based on click position
    else if (x < viewportWidth / 2) {
      // For left side clicks, try right side
      if (spaceRight >= tooltipWidth + safeMargin) {
        position = {
          left: `${x + props.offset}px`,
          top: `${y}px`,
          transform: 'translateY(-50%)',
        }
      }
      else {
        // If no space on right, try above
        const adjustedX = Math.min(
          Math.max(x, tooltipWidth / 2 + safeMargin),
          viewportWidth - tooltipWidth / 2 - safeMargin,
        )
        position = {
          left: `${adjustedX}px`,
          top: `${y - tooltipHeight - props.offset}px`,
          transform: 'translateX(-50%)',
        }
      }
    }
    else {
      // For right side clicks, try left side
      if (spaceLeft >= tooltipWidth + safeMargin) {
        position = {
          left: `${x - tooltipWidth - props.offset}px`,
          top: `${y}px`,
          transform: 'translateY(-50%)',
        }
      }
      else {
        // If no space on left, try above
        const adjustedX = Math.min(
          Math.max(x, tooltipWidth / 2 + safeMargin),
          viewportWidth - tooltipWidth / 2 - safeMargin,
        )
        position = {
          left: `${adjustedX}px`,
          top: `${y - tooltipHeight - props.offset}px`,
          transform: 'translateX(-50%)',
        }
      }
    }

    return position
  }

  // Default positioning logic for non-positioned tooltips
  const spaceBelow = viewportHeight - rect.bottom
  const spaceRight = viewportWidth - rect.left
  const spaceLeft = rect.left

  if (spaceBelow >= tooltipHeight + safeMargin || props.preferredPosition === 'top') {
    const adjustedX = Math.min(
      Math.max(rect.left + rect.width / 2, tooltipWidth / 2 + safeMargin),
      viewportWidth - tooltipWidth / 2 - safeMargin,
    )
    return {
      top: `${rect.bottom + props.offset}px`,
      left: `${adjustedX}px`,
      transform: 'translateX(-50%)',
    }
  }
  else if (spaceRight >= tooltipWidth + safeMargin) {
    return {
      top: `${rect.top + rect.height / 2}px`,
      left: `${rect.right + props.offset}px`,
      transform: 'translateY(-50%)',
    }
  }
  else if (spaceLeft >= tooltipWidth + safeMargin) {
    return {
      top: `${rect.top + rect.height / 2}px`,
      left: `${rect.left - tooltipWidth - props.offset}px`,
      transform: 'translateY(-50%)',
    }
  }

  // Fallback to top with adjusted position
  const adjustedX = Math.min(
    Math.max(rect.left + rect.width / 2, tooltipWidth / 2 + safeMargin),
    viewportWidth - tooltipWidth / 2 - safeMargin,
  )
  return {
    top: `${rect.bottom + props.offset}px`,
    left: `${adjustedX}px`,
    transform: 'translateX(-50%)',
  }
}

watch(() => props.open, (newVal) => {
  transitionOpen.value = newVal
})

const alertContentRef = ref(null)
onClickOutside(alertContentRef, () => {
  if (props.dismissable && transitionOpen.value) {
    emit('update:open', false)
  }
})
</script>

<template>
  <Teleport to="body">
    <Transition name="alert-fade-up" appear @after-leave="emit('update:open', false)">
      <div
        v-if="transitionOpen"
        ref="alertContentRef"
        :class="dialogContentClasses"
        :style="alertPosition"
        class="dialog-alert-content dialog-alert-content--desktop"
      >
        <div class="dialog-alert-content__inner">
          <p class="dialog-alert-content__text">
            <slot name="content" />
          </p>
        </div>
      </div>
    </Transition>
  </Teleport>

  <span ref="anchorEl">
    <slot name="anchor" />
  </span>
</template>

<style>
.dialog-alert-content {
  padding: 23px 20px 25px;
  background-color: white;
  box-shadow: none;
  border-radius: 9px;
  color: black;
  width: 100%;
  overflow: hidden;
  min-width: 140px;
  max-width: 290px;
  z-index: var(--z-index-modal);
  margin-top: 10px;
}

.dialog-alert-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0px 5px 15px 0px rgba(46, 47, 49, 1);
  border-radius: 9px;
  z-index: 1;
  pointer-events: none;
}

.dialog-alert-content__inner {
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;
  z-index: 1;
}

.dialog-alert-content__text {
  margin: 0;
  line-height: 1.3;
  font-size: 14px;
  text-align: center;
}

.dialog-alert-content--success {
  background-color: rgba(171, 239, 183, 1);
}

.dialog-alert-content--desktop {
  position: fixed;
  z-index: var(--z-index-modal);
}

.alert-fade-up-enter-active,
.alert-fade-up-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.alert-fade-up-enter-from,
.alert-fade-up-leave-to {
  opacity: 0;
  transform: translateY(10px) translateX(-50%);
}

.alert-fade-up-enter-to,
.alert-fade-up-leave-from {
  opacity: 1;
  transform: translateY(0) translateX(-50%);
}
</style>
