<script setup>
import { computed } from 'vue'

const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  elapsedTime: {
    type: Number,
    required: true,
  },
})

const formattedTime = computed(() => {
  const hours = Math.floor(props.elapsedTime / 3600)
  const minutes = Math.floor((props.elapsedTime % 3600) / 60)
  const seconds = props.elapsedTime % 60

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    seconds.toString().padStart(2, '0'),
  ].join(':')
})
</script>

<template>
  <div class="poll-timer">
    <span v-if="label" class="poll-timer__label">{{ label }}</span>
    <span class="poll-timer__time">{{ formattedTime }}</span>
  </div>
</template>

<style scoped>
.poll-timer {
  display: flex;
  flex-direction: column;
  gap: 3px;
  font-family: var(--fqz-poll-font-family);
  color: inherit;
}

.poll-timer__label {
  font-size: 12px;
  line-height: 12px;
}

.poll-timer__time {
  font-weight: bold;
  font-size: 22px;
  line-height: 1.17;
}

@media (max-width: 679px) {
  .poll-timer__time {
    font-size: 14px;
  }
}
</style>
