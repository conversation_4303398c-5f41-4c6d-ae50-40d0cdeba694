<script setup>
import FoquzLabel from './FoquzLabel.vue'
import WidgetCloseButton from './WidgetCloseButton.vue'

defineProps({
  showCloseButton: {
    type: Boolean,
    default: true,
  },
  isAboveAll: {
    type: Boolean,
    default: false,
  },
  showFoquzLabel: {
    type: Boolean,
    default: true,
  },
})

defineEmits(['close'])
</script>

<template>
  <div class="app-root__widget-topline" :class="{ 'app-root__widget-topline--above-all': isAboveAll }">
    <FoquzLabel
      v-if="showFoquzLabel"
      class="widget-topline__foquz-label"
    />
    <WidgetCloseButton
      v-if="showCloseButton"
      class="widget-topline__close-button"
      @close="$emit('close')"
    />
  </div>
</template>

<style scoped>
.app-root__widget-topline {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 15px 13px 15px;
  flex: 0 0 auto;
  position: relative;
  z-index: 20;
  min-height: var(--widget-topline-height-desktop);
  transition: background-color 0.3s ease;
}

.app-root__widget-topline--above-all {
  z-index: var(--z-index-modal);
  background: transparent !important;
}

.widget-topline__close-button {
  position: absolute;
  right: 0;
  top: 0;
}

@media (max-width: 679px) {
  .app-root__widget-topline {
    padding-bottom: 7px;
    min-height: var(--widget-topline-height-mobile);
  }
  .widget-topline__foquz-label {
    margin-top: 0;
  }
}
</style>
