import { maskito } from '@maskito/vue'
// #if VITE_USE_SENTRY
import * as Sentry from '@sentry/vue'
// #endif
import { createHead } from '@unhead/vue'
import { createPinia } from 'pinia'
import simplebar from 'simplebar-vue'
import { register } from 'swiper/element/bundle'
import { createApp } from 'vue'
import CopyPromocode from '../shared/ui/CopyPromocode.vue'
import App from './App.vue'
import './style.css'
import 'simplebar-vue/dist/simplebar.min.css'
import '@shared/styles/simplebar-overrides.css'
import '@shared/styles/fancybox-custom-styles.css'
import '@shared/styles/tablet-mode/index.scss'
import '@fancyapps/ui/dist/fancybox/fancybox.css'

const urlParams = new URLSearchParams(window.location.search)
const enableUlibkaRadugiTheme = import.meta.env.VITE_ENABLE_ULIBKA_RADUGI_THEME === 'true'
const enableApiMock = import.meta.env.VITE_ENABLE_API_MOCK === 'true'
const isDev = import.meta.env.MODE === 'development'
const enableApiMockByUrlParam = isDev && (urlParams.get('_fz_enable_mocks') === '1'
  || urlParams.get('_fz_enable_mocks') === 'true')

if (enableUlibkaRadugiTheme || enableApiMock || enableApiMockByUrlParam) {
  import('@features/custom-themes/themes/styles/theme-1.css')
}

register()

const app = createApp(App)

app.directive('maskito', maskito)
app.component('simplebar', simplebar)
const head = createHead()
app.use(head)
app.use(createPinia())

app.component('CopyPromocode', CopyPromocode)
/**
 * @description Включаем моковый API, если переменная окружения установлена в true
 */
async function enableApiMockingIfNeeded() {
  // Если в продакшене, то не включаем моки
  if (import.meta.env.PROD) {
    return
  }

  if (!enableApiMock && !enableApiMockByUrlParam) {
    return
  }

  const { worker } = await import('../../mocks/browser')

  // `worker.start()` returns a Promise that resolves
  // once the Service Worker is up and ready to intercept requests.
  return worker.start()
}

// #if VITE_USE_SENTRY
if (window.location.hostname !== 'localhost' && import.meta.env.VITE_SENTRY_DSN) {
  Sentry.init({
    app,
    dsn: import.meta.env.VITE_SENTRY_DSN,
    integrations: [
      Sentry.browserTracingIntegration({
        tracePropagationTargets: [new RegExp(import.meta.env.VITE_SENTRY_TRACE_PROPAGATION_TARGETS)],
      }),
      Sentry.replayIntegration(),
    ],
    tracesSampleRate: Number(import.meta.env.VITE_SENTRY_TRACES_SAMPLE_RATE),
  })
}
// #endif

enableApiMockingIfNeeded().then(() => {
  app.mount(`[data-foquz-poll-app]`)
})
