@import './custom-fonts.css';

:root {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-offcanvas: 1050;
  --z-index-modal: 1060;
  --z-index-popover: 1070;
  --z-index-tooltip: 1080;

  --fqz-poll-finish-bg-image: var(--fqz-poll-bg-image);

  --fqz-poll-bg-cover: block;

  --fqz-poll-start-screen-bg: var(--fqz-poll-finish-bg-image);
  --fqz-poll-end-screen-bg: var(--fqz-poll-finish-bg-image);

  --fqz-poll-bg-color: #cfd8dc;
  --fqz-poll-text-on-bg: white;

  --fqz-poll-main-color: #3f65f1;

  --fqz-poll-main-place-color: rgba(255, 255, 255, 1);

  --fqz-poll-button-text: white;

  --fqz-poll-text-on-place: black;

  --fqz-poll-link-color: white;
  --fqz-poll-font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
    'Open Sans', 'Helvetica Neue', sans-serif;
  --fqz-poll-font-size: 14px;
  --f-poll-font-size: 14px;
  --fqz-poll-service-font-size: 14px;
  --fqz-poll-error-font-size: 13px;
  --fqz-poll-button-font-size: 16px;
  --fqz-poll-title-font-size: 30px;

  --fqz-poll-next-button-radius: 24px;
  --fqz-poll-next-button-background-color: rgba(63, 101, 241, 0);
  --fqz-poll-next-button-text-color: rgba(255, 255, 255, 1);
  --fqz-poll-next-button-stroke-color: rgba(63, 101, 241, 1);

  --fqz-poll-back-button-radius: 24px;
  --fqz-poll-back-button-background-color: rgba(255, 255, 255, 0);
  --fqz-poll-back-button-text-color: rgba(255, 255, 255, 1);
  --fqz-poll-back-button-stroke-color: rgba(255, 255, 255, 1);

  --fqz-poll-start-button-radius: 24px;
  --fqz-poll-start-button-background-color: rgba(63, 101, 241, 1);
  --fqz-poll-start-button-text-color: rgba(255, 255, 255, 1);
  --fqz-poll-start-button-stroke-color: rgba(63, 101, 241, 1);
  --fqz-poll-buttons-bg: rgba(0, 0, 0, 0.85);
  --fc-paginator-color: var(--fqz-poll-main-color);

  /* FOQUZ brand theme */
  --fqz-brand-color-1: rgba(63, 101, 241, 1);

  /* Simplified mode variables */
  --fqz-poll-simplified-max-width: 680px;
  --fqz-app-max-iframe-height: none;
  --show-gradient-shadow: block;
  --widget-topline-height-desktop: 42px;
  --widget-topline-height-mobile: 32px;
}

[data-foquz-poll-app] {
  min-height: 100vh; /* fallback */
  min-height: 100dvh;
  display: flex;
  flex-direction: column;
}

.simplified-mode [data-foquz-poll-app] {
  max-height: 100vh; /* fallback */
  max-height: 100dvh;
}

.simplified-iframe-mode [data-foquz-poll-app] {
  min-height: 0;
  flex: 0 0 auto;
  max-height: var(--fqz-app-max-iframe-height);
}

/* Box sizing rules */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Prevent font size inflation */
html {
  -moz-text-size-adjust: none;
  -webkit-text-size-adjust: none;
  text-size-adjust: none;
}

/* Remove default margin in favour of better control in authored CSS */
body,
h1,
h2,
h3,
h4,
p,
figure,
blockquote,
dl,
dd {
  margin: 0;
}

/* Remove list styles on ul, ol elements with a list role, which suggests default styling will be removed */
ul[role='list'],
ol[role='list'] {
  list-style: none;
}

html {
  -webkit-tap-highlight-color: transparent;
}

/* Set core body defaults */
body {
  line-height: 1.5;
  font-family: var(--fqz-poll-font-family);
  background-color: var(--fqz-poll-bg-color);
  max-width: 100%;
}

body.simplified-iframe-mode {
  overflow: hidden;
}

body.simplified-mode {
  background-color: transparent;
}

/* Set shorter line heights on headings and interactive elements */
h1,
h2,
h3,
h4,
button,
input,
label {
  line-height: 1.1;
}

/* Balance text wrapping on headings */
h1,
h2,
h3,
h4 {
  text-wrap: balance;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
  color: currentColor;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Make sure textarea without a rows attribute are not tiny */
textarea:not([rows]) {
  min-height: 10em;
}

/* Anything that has been anchored to should have extra scroll margin */
:target {
  scroll-margin-block: 5ex;
}

.end-page-text {
  font-size: var(--fqz-poll-title-font-size, 30px);
  line-height: 1.1;
  text-align: center;
  font-weight: 700;
}

.app-root__foquz-label {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 100;
}

@media (max-width: 679px) {
  .app-root__foquz-label {
    top: 15px;
    left: 15px;
  }
}

.fade-up-enter-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.fade-up-enter-from,
.fade-up-leave-to {
  opacity: 0;
  transform: translateY(3px);
}

.fade-up-leave-active {
  opacity: 0;
  position: absolute;
}

.fade-up-and-down-enter-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.fade-up-and-down-leave-active {
  display: none;
}

.fade-up-and-down-enter-from,
.fade-up-and-down-leave-to {
  opacity: 0;
  transform: translateY(3px);
}

.copy-promocode-container,
.country-select-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  margin-bottom: 15px;
}

@media screen and (max-width: 679px) {
  .end-page-text {
    font-size: 19px;
  }
}

@keyframes tooltipSlideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes tooltipSlideRightAndFade {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes tooltipSlideDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes tooltipSlideLeftAndFade {
  from {
    opacity: 0;
    transform: translateX(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes tooltipSlideUpAndFadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-2px);
  }
}

@keyframes tooltipSlideDownAndFadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(2px);
  }
}

.fqz-shadow-top::before {
  content: '';
  display: var(--show-gradient-shadow);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 15px;
  background: linear-gradient(180deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s;
  display: var(--show-gradient-shadow, none);
}

.fqz-shadow-bottom::after {
  content: '';
  display: var(--show-gradient-shadow);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 15px;
  background: linear-gradient(360deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s;
  display: var(--show-gradient-shadow, none);
}

.fqz-shadow-top--active::before {
  opacity: 1;
}

.fqz-shadow-bottom--active::after {
  opacity: 1;
}

/* Outer shadow variants for widget line and poll actions */
.fqz-shadow-top-outer::before {
  top: auto;
  bottom: -15px;
  background: linear-gradient(180deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
}

.fqz-shadow-bottom-outer::after {
  top: -15px;
  background: linear-gradient(360deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
input[data-com-onepassword-filled],
input[data-lastpass-filled],
input[data-bwfilled] {
  -webkit-box-shadow: 0 0 0 1000px transparent inset;
  transition: background-color 5000s ease-in-out 0s;
  background-color: transparent;
}
