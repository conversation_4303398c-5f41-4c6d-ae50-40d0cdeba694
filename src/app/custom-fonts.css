/* ALSHauss Font */
@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-Bold.woff2') format('woff2');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-BlackItalic.woff2') format('woff2');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-BoldItalic.woff2') format('woff2');
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-LightItalic.woff2') format('woff2');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-ThinItalic.woff2') format('woff2');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-MediumItalic.woff2') format('woff2');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-Regular.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ALSHauss';
  src: url('@/features/custom-themes/themes/fonts/ALSHauss/subset-ALSHauss-RegularItalic.woff2') format('woff2');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

/* Arial Black font */
@font-face {
  font-family: 'Arial Black';
  src: url('@/features/custom-themes/themes/fonts/ArialBlack/subset-ArialBlack.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Benzin';
  src: url('@/features/custom-themes/themes/fonts/Benzin/Benzin-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Benzin';
  src: url('@/features/custom-themes/themes/fonts/Benzin/Benzin-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Proxima Nova';
  src: url('@/features/custom-themes/themes/fonts/ProximaNova/ProximaNova-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
