import { QUESTION_TYPES, parseBoolean, toValue } from "..";
import { validateForm } from "../../validation";
import {
  isEmail,
  isPhoneNumber,
  isRequired,
  isWebsite,
  maxLength,
  minLength,
} from "../../validation/validators";
import {
  ASSESMENT_VARIANT_TYPES,
  MASK_TYPES,
  TEXT_VARIANT_TYPES,
  VARIANT_TYPES,
} from "./questionTypes";
import { translate } from "../translations";
import { declOfNum } from "../index";

const getVariantsWordForm = (count) => {
  return declOfNum(count, ["вариант", "варианта", "вариантов"]);
};

/**
 * Валидаторы для комментария
 * @param {Object} Опции валидации
 * @param {string} value - Значение
 * @param {boolean} enabled - Включен ли комментарий
 * @param {boolean} required - Обязательное ли поле
 * @param {number} minLength - Минимальная длина
 * @param {number} maxLength - Максимальная длина
 * @returns {Array<Function>} - Массив валидаторов
 */
const getCommmentValidators = ({
  value,
  enabled,
  required,
  minLength: valueMinLength,
  maxLength: valueMaxLength,
}) => {
  const validators = [];
  const valueIsNotEmpty = !!value;
  if (!enabled) {
    return [];
  }
  if (required) {
    validators.push(() => isRequired(value));
  }

  if (valueMinLength && valueIsNotEmpty) {
    validators.push(() => minLength(value, valueMinLength));
  }

  if (maxLength && valueIsNotEmpty) {
    validators.push(() => maxLength(value, valueMaxLength));
  }

  return validators;
};

const getAssessmentsValidators = ({
  value,
  type,
  enabled,
  required,
  visible,
  selfVariant,
  attachments,
}) => {
  const isAssessmentVariantsText = type === ASSESMENT_VARIANT_TYPES.TEXT;

  if (isAssessmentVariantsText) {
    if (attachments && attachments.enabled) {
      const textIsEmpty = !value || value.trim() === "";
      const attachmentsList = toValue(attachments?.attachments);
      const hasAttachments = attachmentsList && attachmentsList.length > 0;

      if (enabled && visible && required && textIsEmpty && !hasAttachments) {
        return [() => translate("Обязательное поле")];
      }

      if (!textIsEmpty) {
        return getCommmentValidators({
          value,
          enabled: enabled && visible,
          required: false, // Not required since we already checked above
          minLength: selfVariant.minLength,
          maxLength: selfVariant.maxLength,
        });
      }

      return [];
    }

    // Standard text assessment validation (without attachments)
    return getCommmentValidators({
      value,
      enabled: enabled && visible,
      required,
      minLength: selfVariant.minLength,
      maxLength: selfVariant.maxLength,
    });
  } else {
    const notNullOrUndefined = value !== null && value !== undefined;
    const isValidValue = Array.isArray(value)
      ? value.length > 0
      : notNullOrUndefined;
    if (visible && required && !isValidValue) {
      return [() => translate("Нужно выбрать хотя бы один вариант ответа")];
    }
  }

  return [];
};

const getRatingValidators = ({
  value,
  isValueRequired,
  comment,
  assessments,
}) => {
  return {
    value: isValueRequired
      ? [() => isRequired(value, translate("Нужно поставить оценку"))]
      : [],
    commentValue: getCommmentValidators(comment),
    assessments: getAssessmentsValidators(assessments),
    selfVariant: getCommmentValidators({
      ...assessments.selfVariant,
      enabled:
        assessments.selfVariant.enabled && assessments.selfVariant.visible,
    }),
  };
};

const validateRating = (question, answerState) => {
  const { value, isValueDirty, comment, assessments } = answerState;
  const {
    value: valueValidators,
    commentValue: commentValidators,
    assessments: assessmentsValidators,
    selfVariant: selfVariantValidators,
  } = getRatingValidators({
    value: value.val,
    isValueRequired: question.isRequired === 1,
    isSkipped: answerState.isSkipped.val,
    comment: {
      value: comment.value.val,
      enabled: question.comment_enabled === 1,
      required: question.comment_required === 1,
      minLength: question.textFieldParam?.min,
      maxLength: question.textFieldParam?.max,
    },
    assessments: {
      value: assessments.value.val,
      type: question.assessmentVariantsType,
      enabled: question.isHaveExtra,
      required: question.extra_required === 1,
      visible: assessments.visible.val,
      selfVariant: {
        enabled: question.isHaveCustomField === 1,
        value: assessments.selfVariant.value.val,
        isDirty: assessments.selfVariant.isDirty.val,
        visible: assessments.selfVariant.visible.val,
        required: answerState.assessments.selfVariant.visible.val,
        name: `self-variant-${question.question_id}`,
        text: question.self_variant_text || "Свой вариант",
        minLength: question.textFieldParam?.min,
        maxLength: question.textFieldParam?.max,
      },
      attachments: answerState.assessments?.attachments
        ? answerState.assessments.attachments
        : null,
    },
  });

  // Если вопрос пропущен, то валидация не нужна
  if (answerState.isSkipped.val) {
    return {
      value: null,
      commentValue: null,
    };
  }

  const formData = {
    value: value?.val,
    commentValue: comment?.value?.val,
    assessments: assessments?.value?.val,
    selfVariant: assessments?.selfVariant?.value?.val,
  };

  let validators = {
    value: valueValidators,
    commentValue: commentValidators,
    assessments: assessmentsValidators,
    selfVariant: selfVariantValidators,
  };

  // убираем не измененные (non-dirty) поля из валидации
  if (!isValueDirty.val) {
    validators.value = [];
  }

  if (!comment.isDirty.val) {
    validators.commentValue = [];
  }

  if (!assessments.isDirty.val) {
    validators.assessments = [];
  }

  if (!assessments.selfVariant.isDirty.val) {
    validators.selfVariant = [];
  }

  return validateForm(formData, validators);
};

/**
 * Валидация текстового ответа
 * @param {Object} question - Вопрос
 * @param {Object} answerState - Состояние ответа (то, что ввел пользователь)
 */
const validateTextResponse = (question, answerState) => {
  const {
    value,
    isValueDirty,
    isSkipped,
    minLength: valueMinLength,
    maxLength: valueMaxLength,
    variantType,
    maskType,
    isRequired: isValueRequired,
  } = answerState;

  const isTextareaVariant = variantType === TEXT_VARIANT_TYPES.TEXTAREA;
  const isInputVariant = variantType === TEXT_VARIANT_TYPES.INPUT;

  const hasNoMask = maskType === MASK_TYPES.NONE;
  const hasPhoneMask = maskType === MASK_TYPES.PHONE;
  const hasEmailMask = maskType === MASK_TYPES.EMAIL;
  const hasNumberMask = maskType === MASK_TYPES.NUMBER;
  const hasWebsiteMask = maskType === MASK_TYPES.WEBSITE;
  const hasFioMask = maskType === MASK_TYPES.FIO;

  const isInputWithoutMask = isInputVariant && hasNoMask;

  // Если вопрос пропущен, то валидация не нужна
  if (isSkipped.val) {
    return {
      value: null,
    };
  }

  // Для textarea и для инпута без маски валидация такая же как у комментария
  if (isTextareaVariant || isInputWithoutMask) {
    const formData = {
      value: value?.val,
    };
    const validators = {
      value: getCommmentValidators({
        value: value.val,
        enabled: true,
        required: isValueRequired.val,
        minLength: valueMinLength,
        maxLength: valueMaxLength,
      }),
    };

    if (!isValueDirty.val) {
      validators.value = [];
    }

    return validateForm(formData, validators);
  } else if (hasPhoneMask) {
    // Валидация для телефона
    const formData = {
      value: value?.val,
    };
    const validators = {
      value: [],
    };

    if (!isValueDirty.val) {
      validators.value = [];
    } else {
      if (question.isRequired === 1 || !!value?.val) {
        validators.value.push(() => isPhoneNumber(value.val));
      }
    }

    return validateForm(formData, validators);
  } else if (hasEmailMask) {
    // Валидация для email
    const formData = {
      value: value?.val,
    };
    const validators = {
      value: [],
    };

    if (!isValueDirty.val) {
      validators.value = [];
    } else {
      if (question.isRequired === 1 || !!value?.val) {
        validators.value.push(() => isRequired(value.val));
      }

      if (valueMinLength && !!value?.val) {
        validators.value.push(() => minLength(value.val, valueMinLength));
      }

      if (value?.val) {
        validators.value.push(() => isEmail(value.val));
      }
    }

    return validateForm(formData, validators);
  } else if (hasNumberMask) {
    // Валидация для числа
    const formData = {
      value: value?.val,
    };
    const validators = {
      value: [],
    };

    if (!isValueDirty.val) {
      validators.value = [];
    } else {
      if (question.isRequired === 1 || !!value?.val) {
        validators.value.push(() => isRequired(value.val));
      }

      if (valueMinLength && !!value?.val) {
        validators.value.push(() => minLength(value.val, valueMinLength));
      }
    }

    return validateForm(formData, validators);
  } else if (hasWebsiteMask) {
    // Валидация для сайта
    const formData = {
      value: value?.val,
    };
    const validators = {
      value: [],
    };

    if (!isValueDirty.val) {
      validators.value = [];
    } else {
      if (question.isRequired === 1 || !!value?.val) {
        validators.value.push(() => isRequired(value.val));
      }

      if (valueMinLength && !!value?.val) {
        validators.value.push(() => minLength(value.val, valueMinLength));
      }
      if (value?.val) {
        validators.value.push(() => isWebsite(value.val));
      }
    }

    return validateForm(formData, validators);
  } else if (hasFioMask) {
    // Валидация для ФИО
    const isNameDirty = answerState.fio.name.isDirty.val;
    const isNameRequired = parseBoolean(question.maskConfig?.name?.required);
    const isNameVisible = parseBoolean(question.maskConfig?.name?.visible);
    const nameValue = answerState.fio.name.value.val;
    const nameMinLength = question.maskConfig?.name?.minLength;
    const nameMaxLength = question.maskConfig?.name?.maxLength;

    const isSurnameDirty = answerState.fio.surname.isDirty.val;
    const isSurnameRequired = parseBoolean(
      question.maskConfig?.surname?.required,
    );
    const isSurnameVisible = parseBoolean(
      question.maskConfig?.surname?.visible,
    );
    const surnameValue = answerState.fio.surname.value.val;
    const surnameMinLength = question.maskConfig?.surname?.minLength;
    const surnameMaxLength = question.maskConfig?.surname?.maxLength;

    const isPatronymicDirty = answerState.fio.patronymic.isDirty.val;
    const isPatronymicRequired = parseBoolean(
      question.maskConfig?.patronymic?.required,
    );
    const patronymicValue = answerState.fio.patronymic.value.val;
    const patronymicMinLength = question.maskConfig?.patronymic?.minLength;
    const patronymicMaxLength = question.maskConfig?.patronymic?.maxLength;
    const isPatronymicVisible = parseBoolean(
      question.maskConfig?.patronymic?.visible,
    );

    const formData = {
      name: nameValue,
      surname: surnameValue,
      patronymic: patronymicValue,
    };

    const validators = {
      name: [],
      surname: [],
      patronymic: [],
    };

    if (isNameRequired || !!nameValue) {
      validators.name.push(() => isRequired(nameValue));
    }

    if (nameMinLength && !!nameValue) {
      validators.name.push(() => minLength(nameValue, nameMinLength));
    }

    if (nameMaxLength && !!nameValue) {
      validators.name.push(() => maxLength(nameValue, nameMaxLength));
    }

    if (isSurnameRequired || !!surnameValue) {
      validators.surname.push(() => isRequired(surnameValue));
    }

    if (surnameMinLength && !!surnameValue) {
      validators.surname.push(() => minLength(surnameValue, surnameMinLength));
    }

    if (surnameMaxLength && !!surnameValue?.val) {
      validators.surname.push(() => maxLength(surnameValue, surnameMaxLength));
    }

    if (isPatronymicRequired) {
      validators.patronymic.push(() => isRequired(patronymicValue));
    }

    if (patronymicMinLength && !!patronymicValue) {
      validators.patronymic.push(() =>
        minLength(patronymicValue, patronymicMinLength),
      );
    }

    if (patronymicMaxLength && !!patronymicValue) {
      validators.patronymic.push(() =>
        maxLength(patronymicValue, patronymicMaxLength),
      );
    }

    // убираем не измененные (non-dirty) поля из валидации
    // если поле отключено, то его валидация также не нужна
    if (!isNameDirty || !isNameVisible) {
      validators.name = [];
    }

    if (!isSurnameDirty || !isSurnameVisible) {
      validators.surname = [];
    }

    if (!isPatronymicDirty || !isPatronymicVisible) {
      validators.patronymic = [];
    }

    return validateForm(formData, validators);
  }

  const formData = {
    value: value?.val,
  };
  const validators = {
    value: [],
  };

  return validateForm(formData, validators);
};

const validateVariants = (question, answerState) => {
  const { value, isValueDirty, isSkipped, selfVariant, comment } = answerState;
  const isValueRequired = question.isRequired === 1;

  // If the question is skipped, no validation is needed
  if (isSkipped.val) {
    return {
      value: null,
      selfVariant: null,
    };
  }

  const formData = {
    value: value?.val,
  };

  let isCommentRequired = question.comment_required === 1;
  const minChooseVariants = question.min_choose_variants;

  const detailAnswersWithSelfVariant = [
    ...question.detail_answers,
    {
      comment_required: question.self_variant_comment_required,
      id: 0,
    },
  ];

  // Check if comment is required for selected answers
  let isCommentRequiredForSelectedAnswers = Array.isArray(value.val)
    ? detailAnswersWithSelfVariant.some(
        (answer) =>
          answer.comment_required === 1 && value.val.includes(answer.id),
      )
    : detailAnswersWithSelfVariant.find(
        (answer) => answer.id === value.val && answer.comment_required === 1,
      );

  const validators = {
    value: [],
    minChooseVariants: [],
    selfVariant: getCommmentValidators({
      value: selfVariant.value.val,
      enabled: selfVariant.enabled,
      required: selfVariant.visible.val,
      minLength: question.selfVariantParam?.min,
      maxLength: question.selfVariantParam?.max,
    }),
    commentValue: getCommmentValidators({
      value: comment.value.val,
      enabled: question.comment_enabled === 1,
      required: isCommentRequired || isCommentRequiredForSelectedAnswers,
      minLength: question.textFieldParam?.min,
      maxLength: question.textFieldParam?.max,
    }),
  };

  const isValueArray = Array.isArray(value.val);
  const selectedCount = isValueArray ? value.val.length : value.val ? 1 : 0;
  const isMultipleVariant = question.variantsType === VARIANT_TYPES.MULTIPLE;
  const minChooseVariantsEnabled =
    minChooseVariants && minChooseVariants > 0 && isMultipleVariant;

  if (isValueRequired || (!isValueRequired && selectedCount > 0)) {
    const isSelfVariantSelected = isValueArray
      ? value.val.includes(0) || value.val.includes("is_self_answer")
      : value.val === 0 || value.val === "is_self_answer";

    if (selectedCount === 0 && !minChooseVariantsEnabled) {
      // No option selected
      validators.value.push(() =>
        translate("Нужно выбрать хотя бы один вариант ответа"),
      );
    } else if (minChooseVariantsEnabled && selectedCount < minChooseVariants) {
      // Not enough options selected
      const isSelfVariantValid = validators.selfVariant.every(
        (validator) => !validator(),
      );
      if (selectedCount === 1 && isSelfVariantSelected && !isSelfVariantValid) {
        // Если свой вариант не валиден, то ничего не делаем.
        // Из-за ошибки дальше мы пройти не сможем. Как только свой вариант будет валиден,
        // мы сможем пройти по остальным валидаторам.
      } else if (!answerState.removeOthersItemId.val) {
        // Need to select more options
        const errorFirstPart = translate("Необходимо выбрать хотя бы");
        const symbolWordForm = getVariantsWordForm(minChooseVariants);
        const errorSecondPart = translate(`{count} ${symbolWordForm}`, {
          count: minChooseVariants,
          word: symbolWordForm,
        });
        validators.minChooseVariants.push(() =>
          translate(`${errorFirstPart} ${errorSecondPart}`),
        );
      }
    }
  }

  if (!isValueDirty.val) {
    validators.value = [];
    validators.minChooseVariants = [];
  }

  if (!selfVariant.isDirty.val) {
    validators.selfVariant = [];
  }

  if (!comment.isDirty.val) {
    validators.commentValue = [];
  }

  return validateForm(formData, validators);
};

const validateIntermediateBlock = (question, answerState) => {
  const { agreement, isSkipped } = answerState;

  // If the question is skipped, no validation is needed
  if (isSkipped.val) {
    return {
      agreement: null,
    };
  }

  const formData = {
    agreement: agreement.value.val,
  };

  const validators = {
    agreement: [],
  };

  if (agreement.isRequired.val) {
    validators.agreement.push(() =>
      isRequired(
        agreement.value.val,
        translate("Вы должны согласиться с условиями"),
      ),
    );
  }

  if (!agreement.isDirty.val) {
    validators.agreement = [];
  }

  return validateForm(formData, validators);
};

/**
 * Объект с валидациями для разных типов вопросов
 * Тип вопроса - ключ, значение - функция валидации
 * Возвращает массив валидаторов
 */
const questionValidationMap = {
  // для различных вопросов по рейтингу мы используем одинаковые валидаторы
  [QUESTION_TYPES.STAR_RATING]: validateRating,
  [QUESTION_TYPES.RATING]: validateRating,
  [QUESTION_TYPES.RATING_NPS]: validateRating,
  [QUESTION_TYPES.SMILE_RATING]: validateRating,

  // валидация для текстового ответа
  [QUESTION_TYPES.TEXT]: validateTextResponse,

  // валидация для вариантов
  [QUESTION_TYPES.VARIANTS]: validateVariants,

  // Валидация для промежуточного блока (соглашение с условиями)
  [QUESTION_TYPES.INTERMEDIATE_BLOCK]: validateIntermediateBlock,
};

/**
 * Валидация ответа по типу
 * @param {Object} question - Вопрос
 * @param {Object} answerState - Состояние ответа (то, что ввел пользователь)
 */
export default function validateByType(question, answerState) {
  const validate = questionValidationMap[question.type];
  if (!validate) {
    throw new Error(`Unknown question type: ${question.type}`);
  }

  return validate(question, answerState);
}
