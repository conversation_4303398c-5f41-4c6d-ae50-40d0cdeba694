import { QUESTION_TYPES } from "./questionTypes";

const markRatingAsDirty = (answerState) => {
  const { isValueDirty, comment, assessments } = answerState;

  if (!isValueDirty?.val) {
    isValueDirty.val = true;
  }

  if (!comment.isDirty.val) {
    comment.isDirty.val = true;
  }

  if (assessments?.enabled) {
    if (!assessments.isDirty.val) {
      assessments.isDirty.val = true;
    }
    if (
      assessments.selfVariant?.enabled &&
      assessments.selfVariant?.visible.val
    ) {
      if (!assessments.selfVariant.isDirty.val) {
        assessments.selfVariant.isDirty.val = true;
      }
    }
  }
};

const markTextAsDirty = (answerState) => {
  const { isValueDirty, fio } = answerState;

  if (!isValueDirty.val) {
    isValueDirty.val = true;
  }

  if (fio?.enabled) {
    if (!fio.name.isDirty.val) {
      fio.name.isDirty.val = true;
    }

    if (!fio.surname.isDirty.val) {
      fio.surname.isDirty.val = true;
    }

    if (!fio.patronymic.isDirty.val) {
      fio.patronymic.isDirty.val = true;
    }
  }
};

const markVariantsAsDirty = (answerState) => {
  const { isValueDirty, comment, selfVariant } = answerState;

  if (!isValueDirty.val) {
    isValueDirty.val = true;
  }

  if (comment?.enabled) {
    if (!comment.isDirty.val) {
      comment.isDirty.val = true;
    }
  }

  if (selfVariant?.enabled) {
    if (!selfVariant.isDirty.val) {
      selfVariant.isDirty.val = true;
    }
  }
};

const dirtyMap = {
  [QUESTION_TYPES.STAR_RATING]: markRatingAsDirty,
  [QUESTION_TYPES.RATING]: markRatingAsDirty,
  [QUESTION_TYPES.RATING_NPS]: markRatingAsDirty,
  [QUESTION_TYPES.SMILE_RATING]: markRatingAsDirty,
  [QUESTION_TYPES.TEXT]: markTextAsDirty,
  [QUESTION_TYPES.VARIANTS]: markVariantsAsDirty,
};

/**
 * Пометить вопрос как измененный (dirty) в зависимости от типа вопроса
 * Только измененные (dirty) вопросы валидируются
 * @param {Object} question - объект вопроса
 * @param {Object} answerState - объект состояния ответа
 */
export default function markDirtyByType(question, answerState) {
  const mark = dirtyMap[question.type];

  if (mark) {
    mark(answerState);
  }
}
