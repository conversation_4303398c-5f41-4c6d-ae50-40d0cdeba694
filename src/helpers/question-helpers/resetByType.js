import { ASSESMENT_VARIANT_TYPES, QUESTION_TYPES } from "./questionTypes";
const resetRating = (answerState) => {
  const { value, errors, comment, assessments } = answerState;

  value.val = null;
  errors.val = { value: null, commentValue: null };
  value.val = null;
  comment.isDirty.val = false;
  comment.isRequired.val = false;
  if (assessments?.enabled) {
    if (assessments.type === ASSESMENT_VARIANT_TYPES.SINGLE) {
      assessments.value.val = null;
    } else if (assessments.type === ASSESMENT_VARIANT_TYPES.MULTIPLE) {
      assessments.value.val = [];
    } else {
      assessments.value.val = "";
    }
  }
};

const resetTextQuestion = (answerState) => {
  const { value } = answerState;

  value.val = "";
};

const resetVariantsQuestion = (answerState) => {
  const { value, errors, isValueDirty, comment, selfVariant } = answerState;

  value.val = Array.isArray(value.val) ? [] : null;
  errors.val = { value: null, commentValue: null };
  isValueDirty.val = false;

  if (comment?.enabled) {
    comment.isDirty.val = false;
    // comment.isRequired.val = false;
  }

  if (selfVariant?.enabled) {
    selfVariant.isDirty.val = false;
  }
};

const resetMap = {
  [QUESTION_TYPES.STAR_RATING]: resetRating,
  [QUESTION_TYPES.RATING]: resetRating,
  [QUESTION_TYPES.RATING_NPS]: resetRating,
  [QUESTION_TYPES.SMILE_RATING]: resetRating,
  [QUESTION_TYPES.TEXT]: resetTextQuestion,
  [QUESTION_TYPES.VARIANTS]: resetVariantsQuestion,
};

/**
 * Сброс состояния ответа вопроса по типу вопроса
 * @param {Object} question - вопрос
 * @param {Object} answerState - состояние ответа
 * @returns {void}
 */
export default function resetByType(question, answerState) {
  const reset = resetMap[question.type];

  if (reset) {
    reset(answerState, question);
  }
}
