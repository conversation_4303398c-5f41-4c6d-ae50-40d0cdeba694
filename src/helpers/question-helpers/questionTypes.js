// Типы вопросов
export const QUESTION_TYPES = {
  VARIANTS: 1,
  TEXT: 2,
  SMILE_RATING: 11,
  RATING_NPS: 12,
  STAR_RATING: 15,
  RATING: 18,
  INTERMEDIATE_BLOCK: 16, // Новый тип вопроса
};

// Типы вопросов для NPS
export const RATING_NPS_TYPES = {
  DEFAULT: 1,
  VARIANTS: 2,
};

// Дизайн NPS
export const RATING_NPS_DESIGN = {
  COLORED: 1,
  BLACK_AND_WHITE: 2,
  CUSTOM: 3,
};

// Варианты у поля "Текстовый ответ"
export const TEXT_VARIANT_TYPES = {
  INPUT: 0,
  TEXTAREA: 1,
};

export const MASK_TYPES = {
  NONE: 0,
  PHONE: 1,
  EMAIL: 2,
  NUMBER: 3,
  WEBSITE: 4,
  FIO: 5,
  DATE: 6,
  DATE_RANGE: 7,
  DATE_MONTH: 8,
};

export const ASSESMENT_VARIANT_TYPES = {
  SINGLE: 0,
  MULTIPLE: 1,
  TEXT: 2,
};

export const VARIANT_TYPES = {
  SINGLE: 0,
  MULTIPLE: 1,
};

export const VARIANT_ITEM_TYPES = {
  DEFAULT: 0,
  REMOVE_OTHERS: 1,
};

// Типы блоков промежуточного экрана
export const INTERMEDIATE_BLOCK_TYPES = {
  INTERMEDIATE: 1,
  START: 2,
  END: 3,
};
