import api from "../../api";
import {
  ASSESMENT_VARIANT_TYPES,
  MASK_TYPES,
  QUESTION_TYPES,
  TEXT_VARIANT_TYPES,
  VARIANT_TYPES,
} from "./questionTypes";

const saveRatingAnswer = async (question, answerState, config) => {
  const { assetsRoot, authKey } = config;
  const {
    questionId,
    value,
    comment,
    assessments,
    isSkipped,
    saveAnswerError,
  } = answerState;

  const isAssessmentVariantsText =
    assessments.type === ASSESMENT_VARIANT_TYPES.TEXT;
  const isAssessmentVariantsSingle =
    assessments.type === ASSESMENT_VARIANT_TYPES.SINGLE;
  const isAssessmentVariantsMultiple =
    assessments.type === ASSESMENT_VARIANT_TYPES.MULTIPLE;

  const isRatingNps = question.type === QUESTION_TYPES.RATING_NPS;
  const isSmileRating = question.type === QUESTION_TYPES.SMILE_RATING;
  const selfVariantEnabledAndVisible =
    assessments?.selfVariant?.enabled &&
    assessments?.selfVariant?.visible.val &&
    assessments?.selfVariant?.value.val;

  let foquzAnswerItem = {
    rating: value.val,
    answer: comment.value.val,
  };

  if (comment?.value?.val) {
    foquzAnswerItem.answer = comment.value.val;
  }

  if (isAssessmentVariantsText && assessments?.value?.val) {
    foquzAnswerItem.answer = assessments.value.val;
  }

  if (isAssessmentVariantsSingle && assessments?.value?.val) {
    foquzAnswerItem.detail_item = [assessments.value.val];
  }

  if (isAssessmentVariantsMultiple && assessments?.value?.val?.length > 0) {
    foquzAnswerItem.detail_item = assessments.value.val;
  }

  if (selfVariantEnabledAndVisible) {
    foquzAnswerItem.self_variant = assessments.selfVariant.value.val;

    if (!isSmileRating) {
      foquzAnswerItem.detail_item.push(`is_self_answer`);
    }
  }

  if (isRatingNps) {
    foquzAnswerItem.answer = undefined;
    if (!value.val && value.val !== 0) {
      foquzAnswerItem.rating = -1;
    }
    if (comment?.value?.val) {
      foquzAnswerItem.comment = comment.value.val;
    }
    if (isAssessmentVariantsText && assessments?.value?.val) {
      foquzAnswerItem.comment = assessments.value.val;
    }
  }

  if (isSmileRating) {
    const index = question.smiles.findIndex((smile) => smile.id === value.val);
    foquzAnswerItem.answer = value.val;
    foquzAnswerItem.rating = index + 1;
    if (comment.value.val) {
      foquzAnswerItem.comment = comment.value.val;
    }

    if (foquzAnswerItem.self_variant) {
      if (isAssessmentVariantsSingle) {
        foquzAnswerItem.detail_item = {
          self_variant: foquzAnswerItem.self_variant,
        };
      } else if (
        isAssessmentVariantsMultiple &&
        foquzAnswerItem.detail_item &&
        Array.isArray(foquzAnswerItem.detail_item)
      ) {
        foquzAnswerItem.detail_item.push({
          self_variant: foquzAnswerItem.self_variant,
        });
        foquzAnswerItem.detail_item = foquzAnswerItem.detail_item.filter(
          (item) => item !== "is_self_answer",
        );
      }
      delete foquzAnswerItem.self_variant;
    } else if (isAssessmentVariantsText && assessments?.value?.val) {
      foquzAnswerItem.detail_item = {
        text_answer: assessments.value.val,
      };
    }
  }

  // Add attachment data if available
  if (assessments?.attachments?.handler) {
    const attachmentData = assessments.attachments.handler.getAttachmentData();
    Object.assign(foquzAnswerItem, attachmentData);
  }

  try {
    await api.saveAnswer({
      endpointUrl: assetsRoot,
      questionId,
      authKey,
      isSkipped: isSkipped.val,
      foquzAnswerItem,
    });
  } catch (error) {
    saveAnswerError.val = error;
  }
};

const saveTextResponse = async (question, answerState, config) => {
  const { assetsRoot, authKey } = config;
  const {
    questionId,
    value,
    isSkipped,
    variantType,
    maskType,
    saveAnswerError,
    fio,
  } = answerState;

  const isTextareaVariant = variantType === TEXT_VARIANT_TYPES.TEXTAREA;
  const isInputVariant = variantType === TEXT_VARIANT_TYPES.INPUT;

  const hasNoMask = maskType === MASK_TYPES.NONE;
  const hasPhoneMask = maskType === MASK_TYPES.PHONE;
  const hasEmailMask = maskType === MASK_TYPES.EMAIL;
  const hasNumberMask = maskType === MASK_TYPES.NUMBER;
  const hasWebsiteMask = maskType === MASK_TYPES.WEBSITE;
  const hasFioMask = maskType === MASK_TYPES.FIO;

  const isInputWithoutMask = isInputVariant && hasNoMask;

  let foquzAnswerItem = {};

  if (
    isTextareaVariant ||
    isInputWithoutMask ||
    hasEmailMask ||
    hasPhoneMask ||
    hasNumberMask ||
    hasWebsiteMask
  ) {
    foquzAnswerItem = {
      answer: value.val,
    };
  }

  if (hasFioMask) {
    foquzAnswerItem = {
      answer: {
        name: fio?.name.value?.val,
        surname: fio?.surname.value?.val,
        patronymic: fio?.patronymic.value?.val,
      },
    };
  }

  try {
    await api.saveAnswer({
      endpointUrl: assetsRoot,
      questionId,
      authKey,
      isSkipped: isSkipped.val,
      foquzAnswerItem,
    });
  } catch (error) {
    saveAnswerError.val = error;
  }
};

// save variants (similar to assessments)
const saveVariants = async (question, answerState, config) => {
  const { assetsRoot, authKey } = config;
  const {
    value,
    questionId,
    comment,
    isSkipped,
    saveAnswerError,
    selfVariant,
  } = answerState;

  let foquzAnswerItem = {
    detail_item: value.val,
  };

  if (question.variantsType === VARIANT_TYPES.SINGLE) {
    foquzAnswerItem.detail_item = [value.val];
  }

  if (selfVariant?.enabled && selfVariant?.visible.val) {
    foquzAnswerItem.self_variant = selfVariant.value.val;
  }

  if (comment?.value?.val) {
    foquzAnswerItem.answer = comment.value.val;
  }

  try {
    await api.saveAnswer({
      endpointUrl: assetsRoot,
      questionId,
      authKey,
      isSkipped: isSkipped.val,
      foquzAnswerItem,
    });
  } catch (error) {
    saveAnswerError.val = error;
  }
};

const saveIntermediateBlock = async (question, answerState, config) => {
  const { assetsRoot, authKey } = config;
  const { questionId, isSkipped, saveAnswerError } = answerState;

  try {
    await api.saveAnswer({
      endpointUrl: assetsRoot,
      questionId,
      authKey,
      isSkipped: isSkipped.val,
      foquzAnswerItem: {},
    });
  } catch (error) {
    saveAnswerError.val = error;
  }
};

const saveAnswerByTypeMap = {
  [QUESTION_TYPES.STAR_RATING]: saveRatingAnswer,
  [QUESTION_TYPES.RATING]: saveRatingAnswer,
  [QUESTION_TYPES.RATING_NPS]: saveRatingAnswer,
  [QUESTION_TYPES.SMILE_RATING]: saveRatingAnswer,
  [QUESTION_TYPES.TEXT]: saveTextResponse,
  [QUESTION_TYPES.VARIANTS]: saveVariants,
  [QUESTION_TYPES.INTERMEDIATE_BLOCK]: saveIntermediateBlock,
};

/**
 * Сохранение ответа по API по типу вопроса
 * @param {Object} question - вопрос
 * @param {Object} answerState - состояние ответа
 * @param {Object} config - конфигурация
 * @returns {Promise<any>} - промис с результатом сохранения
 */
export default async function saveAnswerByType(question, answerState, config) {
  const save = saveAnswerByTypeMap[question.type];

  return await save(question, answerState, config);
}
