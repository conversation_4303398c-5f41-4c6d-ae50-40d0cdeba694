import van from "vanjs-core";
import {
  ASSESMENT_VARIANT_TYPES,
  INTERMEDIATE_BLOCK_TYPES,
  MASK_TYPES,
  QUESTION_TYPES,
  RATING_NPS_TYPES,
  VARIANT_ITEM_TYPES,
  VARIANT_TYPES,
} from "./questionTypes";
import { AttachmentHandler } from "../AttachmentHandler";
import { prepareDesignVariablesForScreenshotModal } from "../index";

export const PARAMETER_CONDITIONS = {
  EQUALS: 0,
  GREATER_THAN: 1,
  LESS_THAN: 2,
  CONTAINS: 3,
  STARTS_WITH: 4,
  ENDS_WITH: 5,
};

export const LOGIC_CONDITION_TYPES = {
  ANSWER: 0,
  PARAMETER: 1,
};

export const VISIBILITY_CONDITIONS = {
  SHOW_ONLY_IF: 0,
  HIDE_IF: 1,
};

function getCommentState(question, answerState) {
  const filledAnswer = question.answer?.answer || "";
  const state = {
    value: van.state(filledAnswer),
    minLength: question.textFieldParam?.min,
    maxLength: question.textFieldParam?.max,
    isDirty: van.state(false),
    enabled: van.state(question.comment_enabled === 1),
    isRequired: van.state(false),
  };

  state.isRequired = van.derive(
    () => question.comment_required === 1 && !answerState.isSkipped.val,
  );

  return state;
}

function getAssessmentsState(question, answerState, config) {
  const getDefaultValue = () => {
    if (question.assessmentVariantsType === ASSESMENT_VARIANT_TYPES.SINGLE) {
      return null;
    } else if (
      question.assessmentVariantsType === ASSESMENT_VARIANT_TYPES.MULTIPLE
    ) {
      return [];
    } else if (
      question.assessmentVariantsType === ASSESMENT_VARIANT_TYPES.TEXT
    ) {
      return "";
    }
  };

  const detailItemJson = question.answer?.detail_item;
  let detailItem = null;
  try {
    if (detailItemJson && typeof detailItemJson === "string") {
      detailItem = JSON.parse(detailItemJson);

      if (Array.isArray(detailItem)) {
        detailItem = detailItem.map((id) => {
          if (Number.parseInt(id) === 0) {
            return "is_self_answer";
          }
          return Number.parseInt(id);
        });
      }
    } else if (Array.isArray(detailItemJson)) {
      detailItem = detailItemJson.map((item) => {
        if (typeof item === "string") {
          return Number.parseInt(item);
        }
        return item;
      });
    } else {
      detailItem = detailItemJson;
    }
  } catch (error) {
    console.error("Error parsing detail_item", error);
  }

  const areAssessmentsSingle =
    question.assessmentVariantsType === ASSESMENT_VARIANT_TYPES.SINGLE;
  const areAssessmentsMultiple =
    question.assessmentVariantsType === ASSESMENT_VARIANT_TYPES.MULTIPLE;
  const areAssessmentsText =
    question.assessmentVariantsType === ASSESMENT_VARIANT_TYPES.TEXT;

  // Получение заполненного уточняющего вопроса от респондента
  if (areAssessmentsSingle) {
    // Если уточняющий вопрос с вариантами типа "Один вариант"

    if (Array.isArray(detailItem)) {
      detailItem = detailItem?.[0];
    }
  } else if (areAssessmentsMultiple) {
    // Если уточняющий вопрос с вариантами типа "Несколько вариантов"
    detailItem = detailItem || [];
  } else if (areAssessmentsText) {
    // Если уточняющий вопрос с вариантами типа "Текст"

    // Для вопроса "Смайл-рейтинг" текст ответа приходит в detail_item.text_answer
    if (question.type === QUESTION_TYPES.SMILE_RATING) {
      detailItem = question.answer?.detail_item?.text_answer || "";
    } else {
      detailItem = question.answer?.answer || "";
    }
  }

  const selfVariantText = question.self_variant_text || "Свой вариант";

  const resolveSelfvariantValue = () => {
    if (typeof detailItem === "object" && detailItem.self_variant) {
      return detailItem.self_variant;
    } else if (Array.isArray(detailItem)) {
      const selfVariantValue = detailItem.find(
        (item) => typeof item === "object" && item.self_variant,
      );

      if (selfVariantValue) {
        return selfVariantValue.self_variant;
      }
    }
    return "";
  };

  const resolvedSelfVariantValue = resolveSelfvariantValue();

  if (resolvedSelfVariantValue) {
    if (Array.isArray(detailItem)) {
      detailItem.push("is_self_answer");
    } else if (detailItem) {
      detailItem = `is_self_answer`;
    }
  }

  const state = {
    value: van.state(detailItem || getDefaultValue()),
    isDirty: van.state(false),
    enabled: question.isHaveExtra,
    type: question.assessmentVariantsType,
    selfVariant: {
      enabled: question.isHaveCustomField === 1,
      value: van.state(resolvedSelfVariantValue),
      isDirty: van.state(false),
      name: `self-variant-${question.id}`,
      text: selfVariantText,
      fieldLength: {
        min: question.selfVariantParam?.min,
        max: question.selfVariantParam?.max,
      },
    },
  };

  // При изменении значения уточняющего вопроса, сбрасываем ошибки
  van.derive(() => {
    const valueIsNotEmpty = Array.isArray(state.value.val)
      ? state.value.val.length > 0
      : !!state.value.val;
    if (valueIsNotEmpty) {
      state.isDirty.val = false;
      answerState.errors.val = { ...answerState.errors.val, assessments: null };
      if (state.selfVariant.enabled) {
        state.selfVariant.isDirty.val = false;
        answerState.errors.val = {
          ...answerState.errors.val,
          selfVariant: null,
        };
      }
    }
  });

  const assessmentsEnabled = state.enabled;
  const extraQuestionRateFrom =
    question.starRatingOptions?.extra_question_rate_from ||
    question.extra_question_rate_from ||
    null;
  const extraQuestionRateTo =
    question.starRatingOptions?.extra_question_rate_to ||
    question.extra_question_rate_to ||
    null;

  const assessmentsVisible = van.derive(() => {
    let visible = false;
    if (!assessmentsEnabled) {
      return false;
    }

    const forAllRates =
      extraQuestionRateFrom === null && extraQuestionRateTo === null;

    // @NOTE Поле 'rating' может прийти из смайл-рейтинга, поэтому проверяем его наличие тоже
    const valueOrRating = answerState.value.val || answerState.rating?.val;

    if (forAllRates && valueOrRating) {
      return true;
    } else if (forAllRates && !valueOrRating) {
      return false;
    }

    if (
      valueOrRating >= extraQuestionRateFrom &&
      valueOrRating <= extraQuestionRateTo
    ) {
      visible = true;
    }

    if (!visible && state.isDirty.val) {
      // clean errors if not visible
      const errors = answerState.errors.val;
      if (errors && errors.assessments) {
        answerState.errors.val = { ...errors, assessments: null };
      }
    }

    return visible;
  });

  state.visible = assessmentsVisible;

  const selfVariantIsVisible = van.derive(() => {
    if (!assessmentsEnabled || !state.selfVariant?.enabled) {
      return false;
    }

    const assessmentsValue = state.value.val;
    const selfVariantZeroValue = 0;
    const selfVariantValue = "is_self_answer";

    if (Array.isArray(assessmentsValue)) {
      return (
        assessmentsValue.includes(selfVariantValue) ||
        assessmentsValue.includes(selfVariantZeroValue)
      );
    } else {
      return (
        assessmentsValue === selfVariantValue ||
        assessmentsValue === selfVariantZeroValue
      );
    }
  });

  state.selfVariant.visible = selfVariantIsVisible;

  state.selfVariant.required = van.derive(
    () =>
      state.selfVariant.enabled &&
      question.extra_required === 1 &&
      !answerState.isSkipped.val &&
      selfVariantIsVisible.val,
  );

  // Add attachments state to assessments
  state.attachments = getAttachmentState(question, config);

  return state;
}

function getFioState(question) {
  const state = {};

  state.enabled = question.maskType === MASK_TYPES.FIO;

  if (state.enabled) {
    // comes in form of '{"name":"Name","surname":"Surname","patronymic":""}'
    let predefinedValue = question.answer?.answer;

    if (predefinedValue) {
      try {
        predefinedValue = JSON.parse(predefinedValue);
      } catch (error) {
        console.error("Error parsing predefinedValue", error);
      }
    }

    state.name = {
      value: van.state(predefinedValue?.name || ""),
      isDirty: van.state(false),
    };
    state.surname = {
      value: van.state(predefinedValue?.surname || ""),
      isDirty: van.state(false),
    };
    state.patronymic = {
      value: van.state(predefinedValue?.patronymic || ""),
      isDirty: van.state(false),
    };
  }

  return state;
}

function getAttachmentState(question, config) {
  // Only support attachments for star rating, rating scale, and smile rating questions with questionScreenshot
  const supportedTypes = [
    QUESTION_TYPES.STAR_RATING,
    QUESTION_TYPES.RATING,
    QUESTION_TYPES.SMILE_RATING,
  ];
  const hasQuestionScreenshot =
    question.questionScreenshot &&
    question.questionScreenshot.max_files > 0 &&
    (question.questionScreenshot.upload_enabled === 1 ||
      question.questionScreenshot.make_screenshot_enabled === 1);

  if (!supportedTypes.includes(question.type) || !hasQuestionScreenshot) {
    return {
      enabled: false,
      handler: null,
      attachments: van.state([]),
      error: van.state(null),
      errorKind: van.state("default"),
    };
  }

  // Prepare design variables from pollData if available
  let designVariables = {};
  if (config?.pollData?.design) {
    designVariables = prepareDesignVariablesForScreenshotModal(
      config.pollData.design,
    );
  }

  const attachmentHandlerOptions = {
    maxFiles: question.questionScreenshot.max_files || 0,
    filesizeLimit: 5,
    questionId: question.question_id,
    authKey: config?.authKey,
    assetsRoot: config?.assetsRoot || "",
    isPreviewMode: config?.isPreviewMode || false,
    designVariables: designVariables,
  };

  const handler = new AttachmentHandler(attachmentHandlerOptions);

  // Load previous answer attachments if available
  if (question.answer) {
    handler.addAttachmentsFromPreviousAnswer(question.answer);
  }

  return {
    enabled: true,
    handler,
    attachments: handler.allAttachments,
    error: handler.error,
    errorKind: handler.errorKind,
    showUploadButton: question.questionScreenshot.upload_enabled === 1,
    showScreenshotButton:
      question.questionScreenshot.make_screenshot_enabled === 1,
    maxFiles: question.questionScreenshot.max_files || 0,
    buttonText: question.questionScreenshot.button_text || "Прикрепить файл",
    screenshotButtonText:
      question.questionScreenshot.screenshot_button_text || "Сделать скриншот",
    description: question.questionScreenshot.description || null,
  };
}

function getQuestionVariantsState(question) {
  const getDefaultValue = () => {
    // received as json string array
    let predefinedValue = question.answer?.detail_item || "[]";

    if (predefinedValue) {
      try {
        predefinedValue = JSON.parse(predefinedValue);
        if (Array.isArray(predefinedValue)) {
          predefinedValue = predefinedValue.map((item) => {
            return Number.parseInt(item);
          });
        } else {
          predefinedValue = [];
        }
      } catch (e) {
        console.error("failed to parse default value for variant", e);
      }
    }

    if (question?.answer?.is_self_variant && Array.isArray(predefinedValue)) {
      predefinedValue.push("is_self_answer");
    }

    if (question.variantsType === VARIANT_TYPES.SINGLE) {
      return predefinedValue?.[0] || null;
    } else if (question.variantsType === VARIANT_TYPES.MULTIPLE) {
      return predefinedValue || [];
    }
  };

  const state = {
    value: van.state(getDefaultValue()),
    type: question.variantsType,
    maxChooseVariants: question.maxChooseVariants,
    isUnrequired: question.isUnrequired,
    selfVariant: {
      enabled: question.isHaveCustomField === 1,
      value: van.state(question.answer?.self_variant || ""),
      isDirty: van.state(false),
    },
    removeOthersItemId: van.state(null), // Add this line
  };

  van.derive(() => {
    const value = state.value.val;
    if (Array.isArray(value) && value.length >= 0) {
      const selfVariantType = question.self_variant_nothing;
      const variantsWithSelfVariant = [
        ...question.variants,
        {
          id: "is_self_answer",
          type: selfVariantType,
        },
      ];
      const selectedVariants = variantsWithSelfVariant.filter((variant) =>
        value.includes(variant.id),
      );
      const removeOthersItem = selectedVariants.find(
        (item) => item.type === VARIANT_ITEM_TYPES.REMOVE_OTHERS,
      );
      if (removeOthersItem) {
        // Удаляем все элементы, кроме последнего
        while (state.value.val.length > 1) {
          state.value.val.shift();
        }
        const leftVariantId = state.value.val[0];
        const leftVariant = variantsWithSelfVariant.find(
          (variant) => variant.id === leftVariantId,
        );
        if (leftVariant.type === VARIANT_ITEM_TYPES.REMOVE_OTHERS) {
          state.removeOthersItemId.val = leftVariant.id;
        } else {
          state.removeOthersItemId.val = null;
        }
      } else {
        state.removeOthersItemId.val = null;
      }
    } else if (Array.isArray(value) && value.length === 0) {
      state.removeOthersItemId.val = null;
    }
  });

  state.selfVariant.visible = van.derive(() => {
    const enabled = question.isHaveCustomField;
    if (!enabled) {
      return false;
    }
    const value = state.value.val;
    const selfVariantName = "is_self_answer";
    if (Array.isArray(value)) {
      return value.includes(selfVariantName) || value.includes(0);
    }
    return value === selfVariantName || value === 0;
  });

  return state;
}

function getIntermediateBlockState(question, defaultState, config) {
  const agreementEnabled = question.intermediateBlock?.agreement === 1;
  const errors = agreementEnabled
    ? { agreement: "Вы должны согласиться с условиями" }
    : {};

  // Function to preload an image and get its dimensions
  const preloadImage = (imageData) => {
    return new Promise((resolve) => {
      if (imageData.external_logo) {
        const img = new Image();
        img.onload = () => {
          imageData.width.val = img.naturalWidth;
          imageData.height.val = img.naturalHeight;
          resolve(imageData);
        };
        img.onerror = () => {
          // If loading fails, resolve with original data
          resolve(imageData);
        };
        img.src = imageData.external_logo;
      } else {
        // If it's not an external image, resolve immediately
        resolve(imageData);
      }
    });
  };

  // Preload all images
  defaultState.endScreenImages =
    question.endScreenImages?.map?.((img) => {
      const width = van.state(img.width || img.original_width);
      const height = van.state(img.height || img.original_height);

      const image = {
        ...img,
        width,
        height,
      };

      const hasNoSize = !image.width.val && !image.height.val;
      if (img.external_logo && hasNoSize) {
        preloadImage(image);
      }

      return image;
    }) || [];

  const isAnswerEmpty =
    question.answer?.isEmpty === undefined ? true : question.answer?.isEmpty;

  return {
    ...defaultState,
    errors: van.state(errors),
    isValueDirty: van.state(true),
    screenType:
      question?.intermediateBlock?.screen_type ||
      INTERMEDIATE_BLOCK_TYPES.DEFAULT,
    agreement: {
      value: van.state(agreementEnabled ? !isAnswerEmpty : null),
      isDirty: van.state(true),
      enabled: van.state(agreementEnabled),
      isRequired: van.state(agreementEnabled),
    },
    comment: {
      ...getCommentState(question, defaultState),
      enabled: van.state(false),
    },
    assessments: {
      ...getAssessmentsState(question, defaultState, config),
      enabled: van.state(false),
    },
    isSkipped: van.state(false),
  };
}

function getStarRatingState(question, config) {
  const defaultState = {
    id: question.id,
    questionId: question.question_id,
    errors: van.state({}),
    value: van.state(question.stars || null),
    isValueDirty: van.state(false),
    isSkipped: van.state(question.skipped === 1 || false),
    saveAnswerError: van.state(null),
    type: question.type,
  };

  return {
    ...defaultState,
    isValueDirty: van.state(true),
    comment: { ...getCommentState(question, defaultState) },
    assessments: { ...getAssessmentsState(question, defaultState, config) },
    starRatingOptions: {
      count: question.starRatingOptions.count,
      size: question.starRatingOptions.size,
      labelsArray: question.starRatingOptions.labelsArray,
      color: question.starRatingOptions.color,
    },
  };
}

function getRatingState(question, config) {
  const defaultState = {
    id: question.id,
    questionId: question.question_id,
    errors: van.state({}),
    value: van.state(question.answer?.rating || null),
    isValueDirty: van.state(false),
    isSkipped: van.state(question.skipped === 1 || false),
  };

  return {
    ...defaultState,
    comment: { ...getCommentState(question, defaultState) },
    assessments: { ...getAssessmentsState(question, defaultState, config) },
  };
}

function getRatingNpsState(question, answerDefaultState, config) {
  const ratingExists =
    question.answer?.rating !== null || question.answer?.rating !== undefined;
  const defaultState = {
    value: van.state(ratingExists ? question.answer?.rating : null),
    isValueDirty: van.state(true),
    fromOne: question.fromOne,
    variantType:
      question.set_variants === 1
        ? RATING_NPS_TYPES.VARIANTS
        : RATING_NPS_TYPES.DEFAULT,
    npsRatingSetting: {
      design: question.npsRatingSetting.design,
      startColor: question.npsRatingSetting.start_point_color,
      endColor: question.npsRatingSetting.end_point_color,
      startLabel: question.npsRatingSetting.start_label,
      endLabel: question.npsRatingSetting.end_label,
    },
  };

  const predefinedCommentValue = question.answer?.self_variant || "";
  return {
    ...defaultState,
    comment: {
      ...getCommentState(question, answerDefaultState),
      value: van.state(predefinedCommentValue),
    },
    assessments: {
      ...getAssessmentsState(question, answerDefaultState, config),
    },
  };
}

function getTextState(question, defaultState) {
  const isPhoneMask = question.maskType === MASK_TYPES.PHONE;
  let predefinedValue = question.answer?.answer;

  // Если значение номера телефона начинается не с +, то добавляем +
  // С сервера заполненное респондентом значение приходит без +
  if (isPhoneMask && predefinedValue && !predefinedValue.includes("+")) {
    predefinedValue = `+${predefinedValue.trim()}`;
  }

  return {
    isValueDirty: van.state(false),
    value: van.state(predefinedValue || ""),
    variantType: question.variantsType,
    maskType: question.maskType,
    dateType: question.dateType,
    minLength: question.textFieldParam?.min,
    maxLength: question.textFieldParam?.max,
    fio: getFioState(question, defaultState),
  };
}

function getSmileRatingState(question, defaultState, config) {
  const predefinedValue = question.answer?.answer;
  const predefinedId = Number.parseInt(predefinedValue);

  const filledCommentAnswer = question.answer?.self_variant || "";

  const state = {
    ...defaultState,
    value: van.state(predefinedId || null),
    isValueDirty: van.state(true),
    comment: {
      ...getCommentState(question, defaultState),
      value: van.state(filledCommentAnswer),
    },
    smiles: question.smiles,
  };

  state.rating = van.derive(() => {
    const value = state.value.val || defaultState.value.val;
    if (value !== null || value !== undefined || !isNaN(value)) {
      const index = question.smiles.findIndex((smile) => smile.id === value);
      return index + 1;
    } else {
      return null;
    }
  });

  const assessmentsState = getAssessmentsState(question, state, config);

  state.assessments = assessmentsState;

  return state;
}

function getAnswerStateByType(question, answers, config) {
  const isEmpty =
    question.answer?.isEmpty === undefined ? true : question.answer?.isEmpty;

  const defaultState = {
    id: question.id,
    questionId: question.question_id,
    errors: van.state({}),
    value: van.state(null),
    isValueDirty: van.state(false),
    isSkipped: van.state(question.skipped === 1 || false),
    saveAnswerError: van.state(null),
    type: question.type,
    isEmpty: isEmpty,
  };

  if (question._answer) {
    defaultState.value.val = question._answer;
  }

  defaultState.isRequired = van.derive(
    () => question.isRequired === 1 && !defaultState.isSkipped.val,
  );

  defaultState.isActive = van.derive(() => {
    // Если ответ уже был установлен, то вопрос неактивный
    // Ответ может быть заранее установлен при включеной опции
    // По клику > Шкала оценок
    if (question._answer) {
      return false;
    }

    if (
      !question.questionViewLogic ||
      question.questionViewLogic.length === 0
    ) {
      return true; // Если нет логики отображения, то вопрос всегда активен
    }

    /**
     * Проверка условия логики отображения для вопроса
     * @param {object} logic - Условие логики отображения
     * @returns {boolean} - Возвращает true, если условие выполнено, иначе false
     */
    const checkCondition = (logic) => {
      if (logic.condition_type === LOGIC_CONDITION_TYPES.ANSWER) {
        // Answer to question
        const answersArr = Object.values(answers);
        const conditionAnswer = answersArr.find(
          (answer) => answer.questionId === logic.condition_question_id,
        );
        if (!conditionAnswer) return false;

        let answerValue = conditionAnswer.value.val;
        if (!logic.variants || logic.variants.length === 0) {
          return false;
        }
        const isSmileRating =
          conditionAnswer.type === QUESTION_TYPES.SMILE_RATING;

        if (isSmileRating && conditionAnswer.rating.val) {
          answerValue = conditionAnswer.rating.val;
        }
        if (Array.isArray(answerValue)) {
          return logic.variants.some((v) => answerValue.includes(v));
        } else {
          return logic.variants.includes(answerValue);
        }
      } else if (logic.condition_type === LOGIC_CONDITION_TYPES.PARAMETER) {
        const paramValue = config.additionalParams[logic.parameter];

        if (!paramValue) return false;

        switch (logic.parameter_condition) {
          case PARAMETER_CONDITIONS.EQUALS:
            return paramValue === logic.parameter_value;
          case PARAMETER_CONDITIONS.GREATER_THAN:
            return paramValue.length > logic.parameter_value.length;
          case PARAMETER_CONDITIONS.LESS_THAN:
            return paramValue.length < logic.parameter_value.length;
          case PARAMETER_CONDITIONS.CONTAINS:
            return paramValue.includes(logic.parameter_value);
          case PARAMETER_CONDITIONS.STARTS_WITH:
            return paramValue.startsWith(logic.parameter_value);
          case PARAMETER_CONDITIONS.ENDS_WITH:
            return paramValue.endsWith(logic.parameter_value);
          default:
            return false;
        }
      }
      return false;
    };

    let isActive = true;

    for (const logic of question.questionViewLogic) {
      const showOnlyIfCondition =
        logic.visibility === VISIBILITY_CONDITIONS.SHOW_ONLY_IF;
      const hideIfCondition =
        logic.visibility === VISIBILITY_CONDITIONS.HIDE_IF;

      if (showOnlyIfCondition && !checkCondition(logic)) {
        isActive = false;
        break;
      }

      if (hideIfCondition && checkCondition(logic)) {
        isActive = false;
        break;
      }
    }

    return isActive;
  });

  switch (question.type) {
    case QUESTION_TYPES.STAR_RATING:
      return {
        ...defaultState,
        ...getStarRatingState(question, config),
      };
    case QUESTION_TYPES.SMILE_RATING:
      return {
        ...defaultState,
        ...getSmileRatingState(question, defaultState, config),
      };
    case QUESTION_TYPES.RATING:
      return {
        ...defaultState,
        ...getRatingState(question, config),
      };
    case QUESTION_TYPES.RATING_NPS:
      return {
        ...defaultState,
        ...getRatingNpsState(question, defaultState, config),
      };
    case QUESTION_TYPES.TEXT:
      return {
        ...defaultState,
        ...getTextState(question, defaultState),
      };
    case QUESTION_TYPES.VARIANTS:
      return {
        ...defaultState,
        ...getQuestionVariantsState(question),
        comment: { ...getCommentState(question, defaultState) },
      };
    case QUESTION_TYPES.INTERMEDIATE_BLOCK:
      return getIntermediateBlockState(question, defaultState, config);
    default:
      return defaultState;
  }
}

export default getAnswerStateByType;
