import van from "vanjs-core";
import { CSS_VARIABLE_PREFIX } from "../constants";
import getAnswerStateByType from "./question-helpers/getAnswerStateByType";
import { QUESTION_TYPES } from "./question-helpers/questionTypes";

export const hexToRgba = (hex, opacity) => {
  let r = 0,
    g = 0,
    b = 0;

  // 3 digits
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);

    // 6 digits
  } else if (hex.length === 7) {
    r = parseInt(hex[1] + hex[2], 16);
    g = parseInt(hex[3] + hex[4], 16);
    b = parseInt(hex[5] + hex[6], 16);
  }

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

/**
 * Get CSS variables for the widget
 * @param {Object} design - Design options for the widget
 * @returns {string} - The CSS variables string
 */
export const getCssVariables = (design, assetsRoot) => {
  if (!design) {
    return "";
  }
  const assetsRootWithoutSlash = assetsRoot.replace(/\/$/, "");

  const variables = [];
  if (design["background_color"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}background-color: ${design["background_color"]}`,
    );
  }
  if (design["background_image"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}bg-url: url(${assetsRootWithoutSlash}${design["background_image"]})`,
    );
  }
  if (design["mobile_background_image"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}mobile-bg-url: url(${assetsRootWithoutSlash}${design["mobile_background_image"]})`,
    );
  }
  if (design["main_color"]) {
    variables.push(`${CSS_VARIABLE_PREFIX}main-color: ${design["main_color"]}`);
  }
  if (design["font_family"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}font-family: ${design["font_family"]}`,
    );
  }

  if (design["font_size"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}text-font-size: ${design["font_size"]}px`,
    );
  }

  if (design["title_font_size"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}title-font-size: ${design["title_font_size"]}px`,
    );
  }
  if (design["main_place_color"]) {
    const mainPlaceColor = design["main_place_color"];
    variables.push(`${CSS_VARIABLE_PREFIX}main-place-color: ${mainPlaceColor}`);

    const rgbaRegex =
      /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*(0|0?\.\d+|1(\.0)?)\s*\)$/;
    const match = mainPlaceColor.match(rgbaRegex);
    if (match) {
      const opacity = match[1];
      variables.push(`${CSS_VARIABLE_PREFIX}main-place-opacity: ${opacity}`);
    }
  }
  if (design["text_on_bg"]) {
    variables.push(`${CSS_VARIABLE_PREFIX}text-on-bg: ${design["text_on_bg"]}`);
  }
  if (design["text_on_place"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}text-on-place: ${design["text_on_place"]}`,
    );

    const textOnPlace = design["text_on_place"]; // rgba
    const [, r, g, b] = textOnPlace.match(
      /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/,
    );
    const scrollbarThumbRgba = `rgba(${r}, ${g}, ${b}, 0.3)`;
    const scrollbarThumbHoverRgba = `rgba(${r}, ${g}, ${b}, 0.6)`;
    const scrollbarTrackRgba = `rgba(${r}, ${g}, ${b}, 0.2)`;

    if (scrollbarThumbRgba) {
      variables.push(
        `${CSS_VARIABLE_PREFIX}scrollbar-thumb-bg: ${scrollbarThumbRgba}`,
      );
    }

    if (scrollbarThumbHoverRgba) {
      variables.push(
        `${CSS_VARIABLE_PREFIX}scrollbar-thumb-bg-hover: ${scrollbarThumbHoverRgba}`,
      );
    }

    if (scrollbarTrackRgba) {
      variables.push(
        `${CSS_VARIABLE_PREFIX}scrollbar-track-bg: ${scrollbarTrackRgba}`,
      );
    }
  }
  if (design["star_color"]) {
    variables.push(`${CSS_VARIABLE_PREFIX}star-color: ${design["star_color"]}`);
  }
  if (design["link_color"]) {
    variables.push(`${CSS_VARIABLE_PREFIX}link-color: ${design["link_color"]}`);
  }

  if (design["back_button_background_color"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}back-button-bg: ${design["back_button_background_color"]}`,
    );
  }
  if (design["back_button_stroke_color"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}back-button-border: ${design["back_button_stroke_color"]}`,
    );
  }
  if (design["back_button_radius"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}back-button-radius: ${design["back_button_radius"]}px`,
    );
  }

  if (design["next_button_background_color"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}next-button-bg: ${design["next_button_background_color"]}`,
    );
  }
  if (design["next_button_stroke_color"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}next-button-border: ${design["next_button_stroke_color"]}`,
    );
  }
  if (design["next_button_radius"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}next-button-radius: ${design["next_button_radius"]}px`,
    );
  }

  if (design["start_button_background_color"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}start-button-bg: ${design["start_button_background_color"]}`,
    );
  }
  if (design["start_button_stroke_color"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}start-button-border: ${design["start_button_stroke_color"]}`,
    );
  }
  if (design["start_button_radius"]) {
    variables.push(
      `${CSS_VARIABLE_PREFIX}start-button-radius: ${design["start_button_radius"]}px`,
    );
  }

  return variables.join(";");
};

const stateProto = Object.getPrototypeOf(van.state());

/**
 * Convert a different type of value to value getter
 * It is used to get the value of a state or a function
 * @see https://vanjs.org/tutorial#polymorphic-binding
 * @param {any} v - The value to convert
 * @returns {any} - The value of the state or the result of the function
 */
export const toValue = (v) => {
  const protoOfV = Object.getPrototypeOf(v ?? 0);
  if (protoOfV === stateProto) return v.val;
  if (protoOfV === Function.prototype) return v();
  return v;
};

/** Implement debounce function
 * @param {Function} fn - The function to debounce
 * @param {number} delay - The delay in milliseconds
 * @returns {Function} - The debounced function
 */
export const debounce = (fn, delay) => {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      fn(...args);
    }, delay);
  };
};

/** Implement throttle function
 * @param {Function} fn - The function to throttle
 * @param {number} delay - The delay in milliseconds
 * @returns {Function} - The throttled function
 **/
export const throttle = (fn, delay) => {
  let lastCall = 0;
  let timeoutId;
  return function (...args) {
    const now = new Date().getTime();
    const remainingTime = delay - (now - lastCall);

    if (remainingTime <= 0) {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      lastCall = now;
      fn(...args);
    } else if (!timeoutId) {
      timeoutId = setTimeout(() => {
        lastCall = new Date().getTime();
        timeoutId = null;
        fn(...args);
      }, remainingTime);
    }
  };
};

/** Функция для склонения по числам. 0 телефонов, 1 телефон, 2 телефона, 5 телефонов */
export const declOfNum = (number, titles) => {
  const cases = [2, 0, 1, 1, 1, 2];
  return titles[
    number % 100 > 4 && number % 100 < 20
      ? 2
      : cases[number % 10 < 5 ? number % 10 : 5]
  ];
};

/**
 * Парсим строку в булево значение
 * Некоторые значения из API могут приходить в виде строки, например "true" или "false"
 * @example "true" -> true
 * @param {string | boolean} value - Значение для парсинга
 * @returns {boolean} - Результат парсинга
 */
export const parseBoolean = (value) => {
  if (typeof value === "string") {
    return value.toLowerCase() === "true";
  }
  return Boolean(value);
};

/**
 * Убирает лишние слеши из URL
 * @param {string} url - URL
 * @returns {string} - Нормализованный URL
 */
export const normalizeUrl = (url) => {
  return url.replace(/([^:]\/)\/+/g, "$1");
};

/**
 * Checks if an rgba color has transparency.
 * @param {string} color - The rgba color string.
 * @returns {boolean} - True if the color has transparency, false otherwise.
 */
function hasTransparency(color) {
  if (!color.startsWith("rgba")) {
    return false;
  }

  const rgbaValues = color
    .replace("rgba(", "")
    .replace(")", "")
    .split(",")
    .map((value) => parseFloat(value.trim()));

  const alpha = rgbaValues[3];
  return alpha < 1;
}

/**
 * Prepare design variables for screenshot modal
 * Converts pollData.design object to CSS variables expected by FoquzScreenshot
 * @param {Object} design - Design object from pollData.design
 * @returns {Object} - CSS variables for FoquzScreenshot
 */
export const prepareDesignVariablesForScreenshotModal = (design) => {
  if (!design) {
    return {};
  }

  const cssVariables = {};

  if (design.main_color) {
    cssVariables["--fz-screenshot-primary-color-for-buttons"] =
      design.main_color;
    cssVariables["--fz-screenshot-primary-button-bg"] = design.main_color;
    cssVariables["--fz-screenshot-primary-button-border"] = design.main_color;
  }

  if (design.next_button_background_color) {
    cssVariables["--fz-screenshot-primary-button-bg"] =
      design.next_button_background_color;
  }

  if (design.next_button_stroke_color) {
    cssVariables["--fz-screenshot-primary-button-border"] =
      design.next_button_stroke_color;
  }

  if (design.next_button_text_color) {
    cssVariables["--fz-screenshot-primary-button-text"] =
      design.next_button_text_color;
  }

  if (design.next_button_radius) {
    cssVariables["--fz-screenshot-primary-button-radius"] =
      `${design.next_button_radius}px`;
  }

  if (design.text_on_place) {
    cssVariables["--fz-screenshot-text-color"] = design.text_on_place;
  }

  if (design.main_place_color) {
    cssVariables["--fz-screenshot-background-color"] = design.main_place_color;
  }

  if (design.font_family) {
    cssVariables["--fz-screenshot-font-family"] = design.font_family;
  }

  if (design.font_size) {
    cssVariables["--fz-screenshot-font-size-base"] = `${design.font_size}px`;
    cssVariables["--fz-screenshot-font-size-sm"] =
      `${Math.max(14, parseInt(design.font_size) - 2)}px`;
  }

  if (design.back_button_background_color) {
    cssVariables["--fz-screenshot-secondary-button-bg"] =
      design.back_button_background_color;
  }

  if (design.back_button_text_color) {
    cssVariables["--fz-screenshot-secondary-button-text"] =
      design.back_button_text_color;
  }

  if (design.back_button_stroke_color) {
    cssVariables["--fz-screenshot-secondary-button-border"] =
      design.back_button_stroke_color;
  }

  if (design.back_button_radius) {
    cssVariables["--fz-screenshot-secondary-button-radius"] =
      `${design.back_button_radius}px`;
  }

  return cssVariables;
};

/**
 * Extract translations needed for the screenshot interface
 * @param {Function} translate - Translation function from translations helper
 * @returns {Object} Translations for the screenshot interface
 */
export const extractScreenshotTranslations = (translate) => {
  const translations = {
    instructionText: translate(
      "Для создания скриншота выделите нужную область и нажмите «Готово»",
    ),
    cancelButton: translate("Отменить"),
    cancelButtonSecond: translate("Отмена"),
    doneButtonMobile: translate("Сделать скриншот экрана"),
    doneButtonDesktop: translate("Готово"),
  };

  return translations;
};

export { getAnswerStateByType, QUESTION_TYPES, hasTransparency };
