/**
 * Типы файлов, которые разрешены для загрузки в приложении
 * Эти форматы разрешены для загрузки и поддерживаются для предпросмотра в браузерах
 */
export const ALLOWED_FILE_TYPES = {
  image: ["jpg", "jpeg", "png", "gif", "webp"],
  video: ["mp4", "wmv", "mov", "3gp", "flv", "webm", "avi"],
  audio: ["mp3", "ogg", "wav", "m4a", "aac"],
};

/**
 * Типы файлов, которые разрешены для загрузки, но не поддерживаются для предпросмотра в браузерах
 * Эти форматы можно загрузить, но они будут отображаться с сообщением "Формат не поддерживается"
 */
export const ALLOWED_BUT_NOT_SUPPORTED = {
  image: ["heic"],
  audio: ["amr"],
  video: [],
};

export const ALLOWED_IMAGE_PREVIEW_FORMATS = [
  "jpg",
  "jpeg",
  "png",
  "gif",
  "webp",
];

/**
 * Get file type from filename or File object
 * @param {string|File} input - Filename string or File object
 * @returns {string} - File type: "image", "video", "audio", or "file"
 */
export function getFileType(input) {
  let filename = "";
  let mimeType = "";

  if (typeof input === "string") {
    filename = input;
  } else if (input && typeof input === "object") {
    filename = input.name || "";
    mimeType = input.type || "";
  }

  // Check MIME type first if available
  if (mimeType) {
    if (mimeType.startsWith("image/")) return "image";
    if (mimeType.startsWith("video/")) return "video";
    if (mimeType.startsWith("audio/")) return "audio";
  }

  // Fallback to extension check
  const extension = getFileExtension(filename);
  if (!extension) return "file";

  // Check in allowed types
  for (const [type, extensions] of Object.entries(ALLOWED_FILE_TYPES)) {
    if (extensions.includes(extension)) {
      return type;
    }
  }

  // Check in allowed but not supported types
  for (const [type, extensions] of Object.entries(ALLOWED_BUT_NOT_SUPPORTED)) {
    if (extensions.includes(extension)) {
      return type;
    }
  }

  return "file";
}

/**
 * Extract file extension from filename
 * @param {string} filename
 * @returns {string|null} - Lowercase extension without dot, or null
 */
export function getFileExtension(filename) {
  if (!filename || typeof filename !== "string") return null;

  const lastDotIndex = filename.lastIndexOf(".");
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
    return null;
  }

  return filename.slice(lastDotIndex + 1).toLowerCase();
}

/**
 * Check if file type is allowed (currently only images)
 * @param {string|File} input - Filename string or File object
 * @returns {boolean}
 */
export function isFileTypeAllowed(input) {
  return getFileType(input) === "image";
}

/**
 * Check if image can be previewed in browser
 * @param {string} filename - Filename to check
 * @returns {boolean}
 */
export function canPreviewImage(filename) {
  const extension = getFileExtension(filename);
  return extension ? ALLOWED_IMAGE_PREVIEW_FORMATS.includes(extension) : false;
}

/**
 * Extract name without extension
 * @param {string} filename
 * @returns {string}
 */
export function getNameWithoutExtension(filename) {
  if (!filename || typeof filename !== "string") return "";

  const lastDotIndex = filename.lastIndexOf(".");
  if (lastDotIndex === -1) return filename;

  return filename.slice(0, lastDotIndex);
}
