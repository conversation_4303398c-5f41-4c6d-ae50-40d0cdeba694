import { QUESTION_TYPES, declOfNum, toValue } from ".";

const resolveVariableValue = (value, question, answer) => {
  if (Array.isArray(value)) {
    const items = question.detail_answers.filter((variant) =>
      value.includes(variant.id),
    );
    const hasSelfVariant =
      value.includes(0) || value.includes("is_self_answer");

    const itemsWithText = items.map((item) => item.variant);

    if (hasSelfVariant) {
      itemsWithText.push(
        toValue(answer?.selfVariant?.value) || question.self_variant_text,
      );
    }

    return itemsWithText.join(", ");
  } else if (value === 0) {
    return toValue(answer?.selfVariant?.value) || question.self_variant_text;
  } else if (question.type === QUESTION_TYPES.STAR_RATING) {
    const declinedWord = declOfNum(value, ["звезда", "звезды", "звезд"]);
    return `${value} ${declinedWord}`;
  } else {
    return value;
  }
};

export default function replacePlaceholders(
  text,
  answers,
  pollData,
  questions = [],
  currentQuestion,
) {
  if (!text) return text;

  // eslint-disable-next-line no-unused-vars
  return text.replace(/(\{)([^}]+)(\})/g, (match, _, placeholder, _2) => {
    placeholder = placeholder.replace(/<\/?[^>]+(>|$)/g, "");
    let [type, param] = placeholder.split(".");

    // remove brackets from match
    match = match.replace("{", "").replace("}", "");

    if (type === "URL") {
      const p = new URLSearchParams(window.location.search).get(param);
      if (p) {
        return match.replace(placeholder, p || "");
      }

      return match.replace(placeholder, "");
    } else if (type === "ANSWER") {
      const answerIndex = parseInt(param) - 1;

      const questionsWithoutIntermediateBlocks = questions.filter(
        (question) => question.type !== QUESTION_TYPES.INTERMEDIATE_BLOCK,
      );
      const targetQuestion = questionsWithoutIntermediateBlocks?.[answerIndex];

      if (!targetQuestion) return "";

      const targetAnswer = answers.val[targetQuestion?.id];

      let value = toValue(targetAnswer?.value);

      const isFalseOrNull = value === false || value === null;

      if (isFalseOrNull) {
        return match.replace(placeholder, "");
      }

      const resolvedValue = resolveVariableValue(
        value,
        targetQuestion,
        targetAnswer,
      );

      return match.replace(placeholder, resolvedValue || "");
    } else if (type === "FILIAL") {
      const variables = pollData.variables;
      const variable = variables?.[placeholder];
      return match.replace(placeholder, variable || "");
    } else if (type === "ФИО" && currentQuestion) {
      const fio = pollData.variables?.fio;
      return match.replace(placeholder, fio || "");
    } else if (type === "Промокод" && currentQuestion) {
      const promocode =
        pollData.variables?.codes?.[currentQuestion.question_id];

      return match.replace(placeholder, promocode || "");
    } else {
      return match.replace(placeholder, "");
    }
  });
}
