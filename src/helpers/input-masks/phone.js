import { maskitoUpdateElement } from "@maskito/core";
import {
  maskito<PERSON><PERSON><PERSON><PERSON><PERSON>,
  maskitoPrefixPostprocessorGenerator,
  maskitoWithPlaceholder,
} from "@maskito/kit";

/**
 * It is better to use en quad for placeholder characters
 * instead of simple space.
 * @see https://symbl.cc/en/2000
 */
const PLACEHOLDER = "+  (___) ___ - ____";
const {
  /**
   * Use this utility to remove placeholder characters
   * ___
   * @example
   * inputRef.addEventListener('blur', () => {
   *     // removePlaceholder('+1 (212) 555-____') => '+1 (212) 555'
   *     const cleanValue = removePlaceholder(this.value);
   *
   *     inputRef.value = cleanValue === '+1' ? '' : cleanValue;
   * });
   */
  removePlaceholder,
  plugins,
  ...placeholderOptions
} = maskitoWithPlaceholder(PLACEHOLDER);

function createCompletePhoneInsertionPreprocessor() {
  const trimPrefix = (value) => value.replace(/^(\+?7?\s?8?)\s?/, "");
  const countDigits = (value) => value.replaceAll(/\D/g, "").length;

  return ({ elementState, data }) => {
    const { value, selection } = elementState;

    return {
      elementState: {
        selection,
        value: countDigits(value) > 11 ? trimPrefix(value) : value,
      },
      data: countDigits(data) >= 11 ? trimPrefix(data) : data,
    };
  };
}

export default {
  preprocessors: [
    ...placeholderOptions.preprocessors,
    createCompletePhoneInsertionPreprocessor(),
  ],
  postprocessors: [
    maskitoPrefixPostprocessorGenerator("+7"),
    ...placeholderOptions.postprocessors,
  ],
  mask: [
    "+",
    "7",
    " ",
    "(",
    /\d/,
    /\d/,
    /\d/,
    ")",
    " ",
    /\d/,
    /\d/,
    /\d/,
    " ",
    "-",
    " ",
    /\d/,
    /\d/,
    /\d/,
    /\d/,
  ],
  plugins: [
    ...plugins,
    maskitoEventHandler("focus", (element) => {
      const initialValue = element.value || "+7 (";

      maskitoUpdateElement(
        element,
        initialValue + PLACEHOLDER.slice(initialValue.length),
      );
    }),
    maskitoEventHandler("blur", (element) => {
      const cleanValue = removePlaceholder(element.value);

      maskitoUpdateElement(element, cleanValue === "+7" ? "" : cleanValue);
    }),
  ],
};
