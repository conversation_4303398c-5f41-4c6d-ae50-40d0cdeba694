import { ATTACHMENT_TYPE } from "../constants";
import { nanoid } from "nanoid";
import { getFileType } from "./fileTypeHelpers";
import api from "../api";

/**
 * Guard functions
 */
export const canAddAttachment = (currentCount, maxFiles) =>
  currentCount < maxFiles;

export const isWithinSizeLimit = (fileSize, limitInBytes) =>
  fileSize <= limitInBytes;

/**
 * DTO conversion helpers
 */
export const createFileDTO = (file, options = {}) => {
  const {
    uuid = nanoid(),
    id = Date.now().toString() + Math.random().toString(36).substr(2, 9),
    timestamp = Date.now(),
    isUploading = false,
    previewUrl = null,
    fullUrl = null,
    serverData = null,
  } = options;

  return {
    uuid,
    id,
    type: getFileType(file),
    name: file.name,
    file,
    isUploading,
    previewUrl,
    fullUrl,
    timestamp,
    serverData,
  };
};

export const createScreenshotDTO = (screenshotData, options = {}) => {
  const {
    uuid = nanoid(),
    id = Date.now().toString(),
    isUploading = false,
  } = options;

  return {
    uuid,
    id,
    type: ATTACHMENT_TYPE.SCREENSHOT,
    name: `Screenshot-${screenshotData.timestamp || Date.now()}.jpg`,
    fullUrl: `screenshot-${uuid}`,
    timestamp: screenshotData.timestamp,
    html: screenshotData.html,
    selection: screenshotData.selection,
    client: {
      ...screenshotData.client,
      isMobile: screenshotData.isMobile,
    },
    isUploading,
    isMobile: screenshotData.isMobile,
    userAgent: screenshotData.userAgent,
  };
};

export const createAttachmentFromServerFile = (serverFile, assetsRoot = "") => {
  if (serverFile.type === ATTACHMENT_TYPE.SCREENSHOT) {
    console.log("DEBUG SERVER FILE", serverFile);
    return {
      type: ATTACHMENT_TYPE.SCREENSHOT,
      attachment: {
        uuid: serverFile.uuid || nanoid(),
        id: serverFile.id || Date.now().toString(),
        type: ATTACHMENT_TYPE.SCREENSHOT,
        name: serverFile.name,
        fullUrl: `screenshot-${serverFile.uuid}`,
        timestamp: serverFile.timestamp || Date.now(),
        isUploading: false,
      },
    };
  }

  return {
    type: ATTACHMENT_TYPE.FILE,
    attachment: {
      uuid: serverFile.uuid || nanoid(),
      id: serverFile.id || Date.now().toString(),
      type: getFileType(serverFile.name || serverFile.origin_name || ""),
      name: serverFile.origin_name || serverFile.name || "Unknown file",
      isUploading: false,
      previewUrl: serverFile.image
        ? `${api.getBaseAssetsUrl(assetsRoot)}${serverFile.image}`
        : null,
      fullUrl: serverFile.link
        ? `${api.getBaseAssetsUrl(assetsRoot)}${serverFile.link}`
        : null,
      timestamp: serverFile.timestamp || Date.now(),
      serverData: serverFile,
    },
  };
};

/**
 * Upload strategy pattern
 */
export const getUploadStrategy = (isPreviewMode) => {
  return isPreviewMode
    ? { authKey: "preview", queryParams: { preview: "1" } }
    : { authKey: null, queryParams: {} };
};

/**
 * Build upload URL with query parameters
 */
export const buildUploadUrl = (baseUrl, params = {}) => {
  const url = new URL(baseUrl);
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      url.searchParams.append(key, value);
    }
  });
  return url.toString();
};
