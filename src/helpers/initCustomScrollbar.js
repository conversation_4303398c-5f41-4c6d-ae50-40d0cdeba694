import SimpleBar from "simplebar";
// import "simplebar/dist/simplebar.min.css";
import "@/styles/simplebar.css";

// You will need a ResizeObserver polyfill for browsers that don't support it! (iOS Safari, Edge, ...)
import ResizeObserver from "resize-observer-polyfill";

export default function initCustomScrollbar(el, opts = {}) {
  const defaults = {
    autoHide: false,
    forceVisible: false,
    resizeObserver: ResizeObserver,
    classNames: {
      contentEl: "_fc-sb-9m4nv18fd-content",
      contentWrapper: "_fc-sb-9m4nv18fd-content-wrapper",
      offset: "_fc-sb-9m4nv18fd-offset",
      mask: "_fc-sb-9m4nv18fd-mask",
      wrapper: "_fc-sb-9m4nv18fd-wrapper",
      placeholder: "_fc-sb-9m4nv18fd-placeholder",
      scrollbar: "_fc-sb-9m4nv18fd-scrollbar",
      track: "_fc-sb-9m4nv18fd-track",
      heightAutoObserverWrapperEl:
        "_fc-sb-9m4nv18fd-height-auto-observer-wrapper",
      heightAutoObserverEl: "_fc-sb-9m4nv18fd-height-auto-observer",
      visible: "_fc-sb-9m4nv18fd-visible",
      horizontal: "_fc-sb-9m4nv18fd-horizontal",
      vertical: "_fc-sb-9m4nv18fd-vertical",
      hover: "_fc-sb-9m4nv18fd-hover",
      dragging: "_fc-sb-9m4nv18fd-dragging",
      scrolling: "_fc-sb-9m4nv18fd-scrolling",
      scrollable: "_fc-sb-9m4nv18fd-scrollable",
      mouseEntered: "_fc-sb-9m4nv18fd-mouse-entered",
      ...opts?.additionalClasses,
    },
  };
  if (el && el instanceof Element) {
    const simpleBar = new SimpleBar(el, { ...defaults, ...opts });
    if (opts.disableObserver) {
      simpleBar.resizeObserver.disconnect();
    }
    return simpleBar;
  } else {
    return null;
  }
}
