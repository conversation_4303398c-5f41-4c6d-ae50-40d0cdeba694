import van from "vanjs-core";
import api from "../api";
import { isFileTypeAllowed } from "@/helpers/fileTypeHelpers";
import commonStyles from "@/styles/common.module.css";
import widgetStyles from "@components/core/widget/styles.module.css";
import { FILE_SIZE, ATTACHMENT_ERROR_KIND } from "../constants";
import {
  canAddAttachment,
  isWithinSizeLimit,
  createFileDTO,
  createScreenshotDTO,
  createAttachmentFromServerFile,
  getUploadStrategy,
} from "./attachmentHelpers";
import { translate } from "./translations";
import { extractScreenshotTranslations } from "./index";

export class AttachmentHandler {
  constructor(options = {}) {
    this.maxFiles = van.state(options.maxFiles || 0);
    this.filesizeLimit = options.filesizeLimit || FILE_SIZE.DEFAULT_LIMIT_MB;
    this.filesizeLimitInBytes = this.filesizeLimit * FILE_SIZE.BYTES_IN_MB;
    this.questionId = options.questionId;
    this.authKey = options.authKey;
    this.assetsRoot = options.assetsRoot || "";
    this.isPreviewMode = options.isPreviewMode || false;
    this.designVariables = options.designVariables || {};

    this.files = van.state([]);
    this.screenshots = van.state([]);
    this.error = van.state(null);
    this.errorKind = van.state(ATTACHMENT_ERROR_KIND.DEFAULT);
    this.isScreenshotLoading$ = van.state(false);
    this.screenshotLibrary = null;

    this.errorTimeoutId = null;

    this.allAttachments = van.derive(() => {
      const attachments = [...this.files.val, ...this.screenshots.val];
      attachments.sort((a, b) => a.timestamp - b.timestamp);
      return attachments;
    });

    this.isMaxFilesReached = van.derive(() => {
      return !canAddAttachment(
        this.allAttachments.val.length,
        this.maxFiles.val,
      );
    });
  }

  addFile(fileData) {
    if (!canAddAttachment(this.allAttachments.val.length, this.maxFiles.val)) {
      this.error.val = `${translate("Максимальное количество файлов:")} ${this.maxFiles.val}`;
      return false;
    }

    const file = createFileDTO(fileData, {
      previewUrl: fileData.previewUrl,
      fullUrl: fileData.fullUrl,
      timestamp: fileData.timestamp,
    });

    this.files.val = [...this.files.val, file];
    this.error.val = null;
    return true;
  }

  addScreenshot(screenshotData) {
    if (!canAddAttachment(this.allAttachments.val.length, this.maxFiles.val)) {
      this.error.val = `${translate("Максимальное количество файлов:")} ${this.maxFiles.val}`;
      return false;
    }

    const screenshot = createScreenshotDTO(screenshotData);

    this.screenshots.val = [...this.screenshots.val, screenshot];
    this.error.val = null;
    return true;
  }

  async uploadFiles(files, authKey = null, questionId = null) {
    if (!files || files.length === 0) return;

    this.error.val = null;

    const finalAuthKey = authKey || this.authKey;
    const finalQuestionId = questionId || this.questionId;

    if (!canAddAttachment(this.allAttachments.val.length, this.maxFiles.val)) {
      this.showTemporaryError(
        `${translate("Максимальное количество файлов:")} ${this.maxFiles.val}`,
        ATTACHMENT_ERROR_KIND.MAX_FILES_REACHED,
      );
      return;
    }

    const validFiles = [];
    const invalidFiles = [];

    for (const file of files) {
      if (!this.isFileTypeAllowed(file)) {
        invalidFiles.push({ file, reason: "type" });
      } else if (!isWithinSizeLimit(file.size, this.filesizeLimitInBytes)) {
        invalidFiles.push({ file, reason: "size" });
      } else {
        validFiles.push(file);
      }
    }

    if (invalidFiles.length > 0) {
      const typeErrors = invalidFiles.filter((f) => f.reason === "type");
      const sizeErrors = invalidFiles.filter((f) => f.reason === "size");

      if (typeErrors.length > 0) {
        this.showTemporaryError(
          translate("Недопустимый тип файла"),
          ATTACHMENT_ERROR_KIND.INVALID_TYPE,
        );
      }

      if (sizeErrors.length > 0) {
        const fileNames = sizeErrors.map((f) => f.file.name);
        const message = `${translate("Файл слишком большой")}: «${fileNames.join("», «")}». ${translate("Размер файла не должен превышать {size} Мб").replace("{size}", this.filesizeLimit)}`;
        this.showTemporaryError(message, ATTACHMENT_ERROR_KIND.FILE_TOO_LARGE);
      }
    }

    if (validFiles.length === 0) return;

    const remainingSlots = this.maxFiles.val - this.allAttachments.val.length;
    if (remainingSlots <= 0) return;

    const filesToUpload = validFiles.slice(0, remainingSlots);

    try {
      const uploadingFiles = filesToUpload.map((file, index) =>
        createFileDTO(file, {
          isUploading: true,
          timestamp: Date.now() + index,
        }),
      );

      this.files.val = [...this.files.val, ...uploadingFiles];

      const uuids = uploadingFiles.map((f) => f.uuid);
      const uploadStrategy = getUploadStrategy(this.isPreviewMode);

      const { files: uploadedFiles } = await api.uploadFiles({
        authKey: uploadStrategy.authKey || finalAuthKey,
        files: filesToUpload,
        questionId: finalQuestionId,
        isPreview: this.isPreviewMode,
        uuids,
        assetsRoot: this.assetsRoot,
      });

      uploadingFiles.forEach((uploadingFile) => {
        const fileIndex = this.files.val.findIndex(
          (f) => f.uuid === uploadingFile.uuid,
        );
        if (fileIndex !== -1) {
          const uploadedFile = uploadedFiles.find(
            (f) => f.uuid === uploadingFile.uuid,
          );

          if (uploadedFile) {
            const newFiles = [...this.files.val];
            newFiles[fileIndex] = {
              ...newFiles[fileIndex],
              id: uploadedFile.id,
              name:
                uploadedFile.origin_name ||
                uploadedFile.name ||
                newFiles[fileIndex].name,
              isUploading: false,
              previewUrl: uploadedFile.image
                ? `${api.getBaseAssetsUrl(this.assetsRoot)}${uploadedFile.image}`
                : null,
              fullUrl: uploadedFile.link
                ? `${api.getBaseAssetsUrl(this.assetsRoot)}${uploadedFile.link}`
                : null,
              timestamp: newFiles[fileIndex].timestamp,
              serverData: uploadedFile,
            };
            this.files.val = newFiles;
          }
        }
      });
    } catch (error) {
      console.error("File upload error:", error);

      if (error.status === 413) {
        this.showTemporaryError(
          translate("Произошла ошибка, попробуйте ещё раз"),
        );
      } else {
        this.showTemporaryError(translate("Не удалось загрузить файлы"));
      }

      this.files.val = this.files.val.filter((f) => !f.isUploading);
    }
  }

  removeAttachment(index) {
    const attachment = this.allAttachments.val[index];
    if (!attachment) return;

    if (attachment.type === "screenshot") {
      const screenshotIndex = this.screenshots.val.findIndex(
        (s) => s.id === attachment.id,
      );
      if (screenshotIndex !== -1) {
        const newScreenshots = [...this.screenshots.val];
        newScreenshots.splice(screenshotIndex, 1);
        this.screenshots.val = newScreenshots;
      }
    } else {
      const fileIndex = this.files.val.findIndex((f) => f.id === attachment.id);
      if (fileIndex !== -1) {
        const newFiles = [...this.files.val];
        newFiles.splice(fileIndex, 1);
        this.files.val = newFiles;
      }
    }

    this.error.val = null;
  }

  extractAttachmentFromPreviousAnswer(serverFile) {
    return createAttachmentFromServerFile(serverFile, this.assetsRoot);
  }

  addAttachmentsFromPreviousAnswer(previousAnswer) {
    if (!previousAnswer) return;

    const files = previousAnswer?.files || previousAnswer?.answer?.files || [];

    if (files && Array.isArray(files)) {
      files.forEach((serverFile) => {
        const { type, attachment } =
          this.extractAttachmentFromPreviousAnswer(serverFile);

        if (type === "screenshot") {
          const screenshotWithFlag = {
            ...attachment,
            fromPreviousAnswer: true,
          };
          this.screenshots.val = [...this.screenshots.val, screenshotWithFlag];
        } else {
          this.files.val = [...this.files.val, attachment];
        }
      });
    }
  }

  validateFile(file) {
    const errors = [];

    if (!this.isFileTypeAllowed(file)) {
      errors.push({ type: "type", message: "Invalid file type" });
    }

    if (file.size > this.filesizeLimitInBytes) {
      errors.push({ type: "size", message: "File too large" });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  isFileTypeAllowed(file) {
    return isFileTypeAllowed(file);
  }

  showTemporaryError(message, kind = ATTACHMENT_ERROR_KIND.DEFAULT) {
    if (this.errorTimeoutId) {
      clearTimeout(this.errorTimeoutId);
    }

    this.error.val = message;
    this.errorKind.val = kind;

    this.errorTimeoutId = setTimeout(() => {
      if (this.errorKind.val === kind) {
        this.error.val = null;
        this.errorKind.val = ATTACHMENT_ERROR_KIND.DEFAULT;
      }
      this.errorTimeoutId = null;
    }, 3000);
  }

  createPreviewUrl(file) {
    if (file.type.startsWith("image/")) {
      return URL.createObjectURL(file);
    }
    return null;
  }

  clearAll() {
    this.files.val = [];
    this.screenshots.val = [];
    this.error.val = null;
    this.errorKind.val = "default";
  }

  clearError() {
    this.error.val = null;
  }

  updateMaxFiles(maxFiles) {
    this.maxFiles.val = maxFiles;
  }

  updateFromPreview(previewData) {
    if (!previewData) return;

    if (previewData.max_files !== undefined) {
      this.updateMaxFiles(previewData.max_files);
    }

    if (
      previewData.max_files === 0 ||
      (previewData.upload_enabled === 0 &&
        previewData.make_screenshot_enabled === 0)
    ) {
      this.clearAll();
    }

    this.clearError();
  }

  async loadScreenshotLibrary() {
    if (this.screenshotLibrary) {
      return this.screenshotLibrary;
    }

    this.isScreenshotLoading$.val = true;

    try {
      const { FoquzScreenshot } = await import("@foquz/foquz-screenshot/dist");
      this.screenshotLibrary = FoquzScreenshot;
      return FoquzScreenshot;
    } catch (error) {
      console.error("Failed to load screenshot library:", error);
      this.showTemporaryError(
        translate("Не удалось загрузить библиотеку скриншотов"),
      );
      throw error;
    } finally {
      this.isScreenshotLoading$.val = false;
    }
  }

  async captureScreenshot() {
    if (this.isMaxFilesReached.val) {
      this.showTemporaryError(
        `${translate("Максимальное количество файлов:")} ${this.maxFiles.val}`,
      );
      return false;
    }

    const widgetElements = [
      document.querySelector(`.${widgetStyles["fc-widget"]}`),
      document.querySelector(`.${widgetStyles["fc-widget-modal-container"]}`),
    ].filter(Boolean);

    try {
      const FoquzScreenshot = await this.loadScreenshotLibrary();

      const screenshotInstance = new FoquzScreenshot({
        designVariables: this.designVariables,
        translations: extractScreenshotTranslations(translate),
      });

      widgetElements.forEach((element) => {
        element.classList.add(commonStyles["fc-widget-screenshot-capture"]);
        element.setAttribute("data-ignore-node", "");
      });

      const screenshotResult = await screenshotInstance.capture();

      widgetElements.forEach((element) => {
        element.classList.remove(commonStyles["fc-widget-screenshot-capture"]);
        element.removeAttribute("data-ignore-node");
      });

      if (screenshotResult.cancelled) {
        return false;
      }

      const screenshotData = {
        html: screenshotResult.html,
        timestamp: Date.now(),
        selection: {
          x: screenshotResult.selection.x,
          y: screenshotResult.selection.y,
          width: screenshotResult.selection.width,
          height: screenshotResult.selection.height,
        },
        client: {
          visibleWidth: screenshotResult.dimensions.width,
          visibleHeight: screenshotResult.dimensions.height,
          fullWidth: screenshotResult.dimensions.scrollWidth,
          fullHeight: screenshotResult.dimensions.scrollHeight,
          scrollX: screenshotResult.clientData.scrollX,
          scrollY: screenshotResult.clientData.scrollY,
        },
        userAgent: screenshotResult.clientData.userAgent,
        isMobile: screenshotResult.isMobile,
      };

      return this.addScreenshot(screenshotData);
    } catch (error) {
      console.error("Screenshot capture error:", error);
      this.showTemporaryError(translate("Не удалось сделать скриншот"));

      widgetElements.forEach((element) => {
        element.classList.remove(commonStyles["fc-widget-screenshot-capture"]);
        element.removeAttribute("data-ignore-node");
      });

      return false;
    }
  }

  getAttachmentData() {
    const data = {};

    const newScreenshots = this.screenshots.val.filter(
      (screenshot) => !screenshot.fromPreviousAnswer,
    );

    const oldScreenshots = this.screenshots.val.filter(
      (screenshot) => screenshot.fromPreviousAnswer,
    );

    if (newScreenshots.length > 0) {
      data.clarifyingQuestionScreenshots = newScreenshots.map((screenshot) => ({
        name: screenshot.name,
        uuid: screenshot.uuid,
        html: screenshot.html,
        timestamp: screenshot.timestamp,
        selection: screenshot.selection,
        client: screenshot.client,
        userAgent: screenshot.userAgent,
        isMobile: screenshot.isMobile,
      }));
    }

    const fileIds = this.files.val
      .filter((file) => file.serverData?.id || file.id)
      .map((file) => file.serverData?.id || file.id)
      .filter((id) => !!id);

    const screenshotIds = oldScreenshots
      .filter((screenshot) => screenshot.id)
      .map((screenshot) => screenshot.id)
      .filter((id) => !!id);

    if (fileIds.length > 0) {
      data.clarifyingQuestionAttachmentIds = fileIds;
    }

    if (screenshotIds.length > 0) {
      data.clarifyingQuestionScreenshotIds = screenshotIds;
    }

    return data;
  }
}
