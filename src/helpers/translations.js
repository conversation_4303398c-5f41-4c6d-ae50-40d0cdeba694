// src/helpers/translations.js

let translations = {};
export let LANGUAGE_IDS = {
  ru_RU: 1,
  en_US: 2,
  fr_FR: 3,
  de_DE: 4,
  ch_CH: 5,
  vi_VN: 6,
  tr_TR: 7,
  es_ES: 8,
  it_IT: 9,
  ja_JP: 10,
  ko_KR: 11,
  pt_PT: 12,
  zh_TW: 13,
};

export let SHORTCODE_TO_LANGUAGE_ID = {
  ru: LANGUAGE_IDS.ru_RU,
  en: LANGUAGE_IDS.en_US,
  fr: LANGUAGE_IDS.fr_FR,
  de: LANGUAGE_IDS.de_DE,
  ch: LANGUAGE_IDS.ch_CH,
  vi: LANGUAGE_IDS.vi_VN,
  tr: LANGUAGE_IDS.tr_TR,
  es: LANGUAGE_IDS.es_ES,
  it: LANGUAGE_IDS.it_IT,
  ja: LANGUAGE_IDS.ja_JP,
  ko: LANGUAGE_IDS.ko_KR,
  pt: LANGUAGE_IDS.pt_PT,
  zh: LANGUAGE_IDS.zh_TW,
};

let currentLanguageId = LANGUAGE_IDS.ru_RU; // Default to Russian

/**
 * Initialize translations
 * @param {Object} translationsData - Object containing translations for all languages
 * @param {string} languageId - The language id to set as current (LANGUAGE_IDS.ru_RU as a default)
 */
export function initializeTranslations(
  translationsData,
  languageId = LANGUAGE_IDS.ru_RU,
) {
  translations = translationsData;
  currentLanguageId = languageId;
}

/**
 * Get a translated message
 * @param {string} key - The message key
 * @param {Object} params - Parameters to replace in the message
 * @returns {string} - Translated message
 */
export function translate(key, params = {}) {
  const message = translations[currentLanguageId]?.[key] || key;

  return message.replace(/{(\w+)}/g, (match, param) => {
    return params[param] !== undefined ? params[param] : match;
  });
}

/**
 * Get plural form of a translated message
 * @param {string} key - The message key (use the singular form as the base)
 * @param {number} count - The count to determine plural form
 * @param {Object} params - Additional parameters to replace in the message
 * @returns {string} - Translated plural message
 */
export function translatePlural(key, count, params = {}) {
  const pluralForms = [`${key}`, `${key}s`, `${key}s`];

  let form;
  if (currentLanguageId === LANGUAGE_IDS.ru_RU) {
    form = getPluralFormRussian(count);
  } else {
    form = count === 1 ? 0 : 1;
  }

  const pluralKey = pluralForms[form];
  return translate(pluralKey, { ...params, count });
}

/**
 * Get the correct plural form index for Russian language
 * @param {number} count
 * @returns {number} - Index of the plural form (0, 1, or 2)
 */
function getPluralFormRussian(count) {
  if (count % 10 === 1 && count % 100 !== 11) {
    return 0;
  } else if (
    [2, 3, 4].includes(count % 10) &&
    ![12, 13, 14].includes(count % 100)
  ) {
    return 1;
  } else {
    return 2;
  }
}
