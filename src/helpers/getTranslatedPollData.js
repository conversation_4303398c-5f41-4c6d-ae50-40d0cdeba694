import { MASK_TYPES, QUESTION_TYPES } from "./question-helpers/questionTypes";

export default function getTranslatedPollData(pollData, defaultLang) {
  if (!defaultLang || !defaultLang.id) return pollData;

  const defaultLangId = defaultLang.id;
  const langQuestions = defaultLang.questions || {};

  function applyLabels(question, labels) {
    try {
      if (!question.starRatingOptions) {
        question.starRatingOptions = {};
      }

      let parsedLabels = null;
      if (typeof labels === "string") {
        try {
          parsedLabels = JSON.parse(labels);
        } catch (error) {
          console.error("Error parsing labels:", error);
        }
      } else {
        parsedLabels = labels;
      }

      if (question.type === QUESTION_TYPES.SMILE_RATING) {
        question.smiles = question.smiles.map((smile) => ({
          ...smile,
          label: parsedLabels?.[smile.id] || smile.label || "",
        }));
      } else if (question.type === QUESTION_TYPES.RATING_NPS) {
        question.npsRatingSetting = {
          ...(question.npsRatingSetting || {}),
          start_label:
            parsedLabels?.[0] || question.npsRatingSetting?.start_label,
          end_label: parsedLabels?.[1] || question.npsRatingSetting?.end_label,
        };
      } else if (
        question.type === QUESTION_TYPES.TEXT &&
        question.maskType === MASK_TYPES.FIO
      ) {
        if (parsedLabels.name && question.maskConfig.name) {
          question.maskConfig.name.value = parsedLabels.name.name;
          question.maskConfig.name.placeholderText =
            parsedLabels.name.placeholder;
        }

        if (parsedLabels.surname && question.maskConfig.surname) {
          question.maskConfig.surname.value = parsedLabels.surname.name;
          question.maskConfig.surname.placeholderText =
            parsedLabels.surname.placeholder;
        }

        if (parsedLabels.patronymic && question.maskConfig.patronymic) {
          question.maskConfig.patronymic.value = parsedLabels.patronymic.name;
          question.maskConfig.patronymic.placeholderText =
            parsedLabels.patronymic.placeholder;
        }
      } else {
        question.starRatingOptions.labelsArray = parsedLabels;
      }
    } catch (e) {
      console.error("Error parsing labels:", e);
    }
  }

  function translateQuestion(question) {
    const questionLangData = langQuestions[question.question_id || question.id];
    if (questionLangData) {
      question.name = questionLangData.name || question.name;
      question.description =
        questionLangData.description || question.description;
      question.description_html =
        questionLangData.description_html || question.description_html;
      question.subdescription =
        questionLangData.sub_description || question.subdescription;
      question.placeholderText =
        questionLangData.placeholder_text || question.placeholderText;
      question.selectPlaceholderText =
        questionLangData.select_placeholder_text ||
        question.selectPlaceholderText;
      question.answerText =
        questionLangData.detail_question || question.answerText;
      question.selfVariantPlaceholderText =
        questionLangData.self_variant_placeholder_text ||
        question.selfVariantPlaceholderText;
      question.comment_label =
        questionLangData.comment_label || question.comment_label;
      question.skip_text = questionLangData.skip_text || question.skip_text;
      question.self_variant_text =
        questionLangData.self_variant_text || question.self_variant_text;

      if (questionLangData.labels) {
        applyLabels(question, questionLangData.labels);
      }

      if (questionLangData.detailLangs && question.detail_answers) {
        question.detail_answers.forEach((answer) => {
          const answerTranslation = questionLangData.detailLangs[answer.id];
          if (answerTranslation) {
            answer.variant = answerTranslation.question || answer.variant;
          }
        });
      }

      if (questionLangData.screenshotLang && question.questionScreenshot) {
        question.questionScreenshot = {
          ...question.questionScreenshot,
          description: questionLangData.screenshotLang.description,
          button_text: questionLangData.screenshotLang.button_text,
          screenshot_button_text:
            questionLangData.screenshotLang.screenshot_button_text,
        };
      }
    } else if (question.langs) {
      const translation = question.langs.find(
        (lang) => lang.foquz_poll_lang_id === defaultLangId,
      );
      if (translation) {
        question.name = translation.name || question.name;
        question.description = translation.description || question.description;
        question.description_html =
          translation.description_html || question.description_html;
        question.subdescription =
          translation.sub_description || question.subdescription;
        question.placeholderText =
          translation.placeholder_text || question.placeholderText;
        question.selectPlaceholderText =
          translation.select_placeholder_text || question.selectPlaceholderText;
        question.answerText =
          translation.detail_question || question.answerText;
        question.selfVariantPlaceholderText =
          translation.self_variant_placeholder_text ||
          question.selfVariantPlaceholderText;
        question.comment_label =
          translation.comment_label || question.comment_label;
        question.skip_text = translation.skip_text || question.skip_text;
        question.self_variant_text =
          translation.self_variant_text || question.self_variant_text;
        question.selfVariantPlaceholderText =
          translation.selfVariantPlaceholderText ||
          question.selfVariantPlaceholderText;

        if (translation.labels) {
          applyLabels(question, translation.labels);
        }
      }
    }

    if (question.intermediateBlock && question.intermediateBlock.langs) {
      const blockTranslation = question.intermediateBlock.langs.find(
        (lang) => lang.lang_id === defaultLangId,
      );
      if (blockTranslation) {
        question.intermediateBlock.text =
          blockTranslation.text || question.intermediateBlock.text;
        question.intermediateBlock.complaint_button_text =
          blockTranslation.complaint_button_text ||
          question.intermediateBlock.complaint_button_text;
        question.intermediateBlock.unsubscribe_button_text =
          blockTranslation.unsubscribe_button_text ||
          question.intermediateBlock.unsubscribe_button_text;
        question.intermediateBlock.poll_button_text =
          blockTranslation.poll_button_text ||
          question.intermediateBlock.poll_button_text;
        question.intermediateBlock.ready_button_text =
          blockTranslation.ready_button_text ||
          question.intermediateBlock.ready_button_text;
        question.intermediateBlock.close_widget_button_text =
          blockTranslation.close_widget_button_text ||
          question.intermediateBlock.close_widget_button_text;
        question.intermediateBlock.external_link =
          blockTranslation.external_link ||
          question.intermediateBlock.external_link;
        question.intermediateBlock.scores_button_text =
          blockTranslation.scores_button_text ||
          question.intermediateBlock.scores_button_text;
        question.intermediateBlock.start_over_button_text =
          blockTranslation.start_over_button_text ||
          question.intermediateBlock.start_over_button_text;
        question.intermediateBlock.agreement_text =
          blockTranslation.agreement_text ||
          question.intermediateBlock.agreement_text;
      }
    }

    if (question.detail_answers) {
      question.detail_answers.forEach((answer) => {
        if (!answer.detailLangs) return answer;
        const answerTranslation = answer.detailLangs.find(
          (lang) => lang.foquz_poll_lang_id === defaultLangId,
        );
        if (answerTranslation) {
          answer.variant = answerTranslation.question || answer.variant;
        }
      });
    }

    return question;
  }

  const translatedQuestions = pollData.questions.map(translateQuestion);

  const translatedPollData = {
    ...pollData,
    questions: translatedQuestions,
    design: {
      ...pollData.design,
      back_text: defaultLang.back_text || pollData.design.back_text,
      next_text: defaultLang.next_text || pollData.design.next_text,
      finish_text: defaultLang.finish_text || pollData.design.finish_text,
      unrequired_text:
        defaultLang.unrequired_text || pollData.design.unrequired_text,
    },
  };

  return translatedPollData;
}
