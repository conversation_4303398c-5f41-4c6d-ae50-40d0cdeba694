
.fc-widget-fade:global(-enter-active),
.fc-widget-fade:global(-leave-active) {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.fc-widget-fade:global(-enter-from) {
  opacity: 0;
  transform: translateY(4px);
}

.fc-widget-fade:global(-enter-to) {
  opacity: 1;
  transform: none;
}

.fc-widget-fade:global(-leave-from) {
  opacity: 1;
  transform: none;
}

.fc-widget-fade:global(-leave-to) {
  opacity: 0;
  transform: translateY(4px);
}

.fc-widget-fade-in:global(-enter-active) {
  transition: opacity 0.3s, transform 0.3s;
}

.fc-widget-fade-in:global(-enter-from) {
  opacity: 0;
  transform: translateY(6px);
}

.fc-widget-fade-in:global(-enter-to) {
  opacity: 1;
  transform: none;
}

@keyframes FcWidgetElemSlideUp {
  0% {
    overflow: hidden;
    height: var(--fc-widget-el-height);
  }

  100% {
    overflow: hidden;
    height: 0;
  }
}

@keyframes FcWidgetElemSlideDown {
  0% {
    overflow: hidden;
    height: 0;
  }

  100% {
    overflow: hidden;
    height: var(--fc-widget-el-height);
  }
}

.fc-widget-slide:global(-enter-from) {
  height: 0;
  overflow: hidden;
}

.fc-widget-slide:global(-enter-to) {
  animation: FcWidgetElemSlideDown 0.3s;
}

.fc-widget-slide:global(-leave-to) {
  animation: FcWidgetElemSlideUp 0.3s;
}

.fc-widget-screenshot-capture {
  opacity: 0 !important;
  transition: opacity 0.3s ease;
  pointer-events: none !important;
}
