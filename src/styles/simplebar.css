[data-simplebar] {
  position: relative;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
}

._fc-sb-9m4nv18fd-wrapper,
._fc-sb-9m4nv18fd-wrapper--modal {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit;
}

._fc-sb-9m4nv18fd-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0;
}

._fc-sb-9m4nv18fd-offset {
  direction: inherit !important;
  box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

._fc-sb-9m4nv18fd-wrapper--modal ._fc-sb-9m4nv18fd-content {
  height: 100%;
}

._fc-sb-9m4nv18fd-content-wrapper {
  direction: inherit;
  box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  max-width: 100%; /* Not required for horizontal scroll to trigger */
  max-height: 100%; /* Needed for vertical scroll to trigger */
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

._fc-sb-9m4nv18fd-content-wrapper::-webkit-scrollbar,
._fc-sb-9m4nv18fd-hide-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

._fc-sb-9m4nv18fd-content:before,
._fc-sb-9m4nv18fd-content:after {
  content: " ";
  display: table;
}

._fc-sb-9m4nv18fd-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none;
}

._fc-sb-9m4nv18fd-height-auto-observer-wrapper {
  box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  flex-grow: inherit;
  flex-shrink: 0;
  flex-basis: 0;
}

._fc-sb-9m4nv18fd-height-auto-observer {
  box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

._fc-sb-9m4nv18fd-track {
  z-index: 2;
  position: absolute;
  right: 5px;
  bottom: 0;
  pointer-events: none;
}
[data-simplebar]._fc-sb-9m4nv18fd-dragging {
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

[data-simplebar]._fc-sb-9m4nv18fd-dragging ._fc-sb-9m4nv18fd-content {
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

[data-simplebar]._fc-sb-9m4nv18fd-dragging ._fc-sb-9m4nv18fd-track {
  pointer-events: all;
}

._fc-sb-9m4nv18fd-scrollbar {
  position: absolute;
  left: 0;
  right: 0;
  min-height: 10px;
}

._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical {
  top: 0;
  width: 11px;
}

._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical:before {
  content: "";
  position: absolute;
  background: var(--fqz-widget-scrollbar-track-bg);
  border-radius: 7px;
  left: auto;
  top: 0px;
  height: 100%;
  width: 4px;
  right: 0;
  transition: opacity 0.2s 0.5s linear;
}

._fc-sb-9m4nv18fd-track--modal._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical {
  top: 10px;
  height: calc(100% - 20px);
}

@media screen and (max-width: 768px) {
  ._fc-sb-9m4nv18fd-track--modal._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical {
  top: 10px;
  height: calc(100% - 20px);
    right: 3px;
  }
}

._fc-sb-9m4nv18fd-track--modal._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical:before {
  background: rgba(255, 255, 255, 0.472);
  /* top: 4px; */
  /* height: calc(100% - 8px); */
}

._fc-sb-9m4nv18fd-track--modal._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical
  ._fc-sb-9m4nv18fd-scrollbar:before {
  background: white;
}

._fc-sb-9m4nv18fd-scrollbar:before {
  content: "";
  display: block;
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: auto;
  width: 4px;
  left: calc(100% - 2px);
  transform: translateX(-50%);
  border-radius: 7px;
  background-color: var(--fqz-widget-scrollbar-thumb-bg);
  transition:
    background-color 0.3s,
    width 0.3s,
    transform 0.3s !important;
  pointer-events: none;
}

._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-hover
  ._fc-sb-9m4nv18fd-scrollbar._fc-sb-9m4nv18fd-hover:before {
  width: 10px;
  background-color: var(--fqz-widget-scrollbar-thumb-bg-hover);
}

._fc-sb-9m4nv18fd-dragging ._fc-sb-9m4nv18fd-scrollbar:before {
  width: 10px !important;
  transition:
    background-color 0.3s,
    width 0.3s,
    transform 0.3s !important;
  background-color: var(--fqz-widget-scrollbar-thumb-bg-hover) !important;
}

._fc-sb-9m4nv18fd-dragging
  ._fc-sb-9m4nv18fd-track--modal._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical
  ._fc-sb-9m4nv18fd-scrollbar:before {
  background-color: white !important;
}

._fc-sb-9m4nv18fd-dragging ._fc-sb-9m4nv18fd-wrapper:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

._fc-sb-9m4nv18fd-scrollbar._fc-sb-9m4nv18fd-visible:before {
  background-color: var(--fqz-widget-scrollbar-thumb-bg);
}

._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-horizontal {
  left: 0;
  height: 11px;
}

._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-horizontal
  ._fc-sb-9m4nv18fd-scrollbar {
  right: auto;
  left: 0;
  top: 0;
  bottom: 0;
  min-height: 0;
  min-width: 10px;
  width: auto;
}

/* Rtl support */
[data-simplebar-direction="rtl"]
  ._fc-sb-9m4nv18fd-track._fc-sb-9m4nv18fd-vertical {
  right: auto;
  left: 0;
}

._fc-sb-9m4nv18fd-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
  -ms-overflow-style: scrollbar !important;
}

._fc-sb-9m4nv18fd-dummy-scrollbar-size > div {
  width: 200%;
  height: 200%;
  margin: 10px 0;
}

._fc-sb-9m4nv18fd-hide-scrollbar {
  position: fixed;
  left: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

._fc-sb-9m4nv18fd-mouse-entered {
}

._fc-sb-9m4nv18fd-scrolling ._fc-sb-9m4nv18fd-scrollbar:before {
  transition: opacity 0.2s linear;
}
