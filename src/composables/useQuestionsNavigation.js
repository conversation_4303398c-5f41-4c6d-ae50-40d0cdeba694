import markDirtyByType from "@/helpers/question-helpers/markDirtyByType";
import {
  INTERMEDIATE_BLOCK_TYPES,
  QUESTION_TYPES,
} from "@/helpers/question-helpers/questionTypes";
import saveAnswerByType from "@/helpers/question-helpers/saveAnswerByType";
import van from "vanjs-core";
import { debounce } from "../helpers";
import { VARIANT_TYPES } from "../helpers/question-helpers/questionTypes";
import validateByType from "@/helpers/question-helpers/validateByType";
import api from "../api";

// @TODO: Добавить документацию
export function useQuestionsNavigation({
  questions,
  answers,
  pollData,
  onFormSuccess,
  apiConfig,
  widgetKey,
  hasScrollVisible,
  closeByFinishButton,
  onCloseWidgetButtonClick = () => {},
}) {
  const pollStatus = pollData?.answer?.status;
  const questionsById = questions.reduce((acc, question) => {
    acc[question.id] = question;
    return acc;
  }, {});

  /**
   * Находит первый активный и неотвеченный ответ
   * Если статус опроса "done", то возвращает первый активный финальный экран
   * Если ответ пустой (isEmpty), то он не считается неотвеченным
   * @returns {Object} - Первый активный и неотвеченный ответ
   */
  const findFirstUnansweredAndActiveAnswer = () => {
    let arr = Object.values(answers.val);

    if (pollStatus === "done") {
      return arr.find(
        (answer) =>
          answer.screenType === INTERMEDIATE_BLOCK_TYPES.END &&
          answer.isActive.val,
      );
    }

    if (pollStatus === "in-progress") {
      arr = arr.filter((a) => a?.screenType !== INTERMEDIATE_BLOCK_TYPES.START);
    }

    return arr.reduce((result, a) => {
      if (result) return result; // If we've already found a result, return it

      const isEmpty = a.isEmpty;
      const isIntermediateBlock =
        a.screenType === INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE;

      if (isIntermediateBlock) {
        const nextAnswers = arr.slice(arr.indexOf(a) + 1);
        const nextActiveNonEmptyQuestion = nextAnswers.find(
          (nextA) =>
            nextA.isActive.val &&
            nextA.isEmpty &&
            nextA.screenType !== INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE,
        );

        return nextActiveNonEmptyQuestion || a;
      }

      return a.isActive.val && isEmpty ? a : null;
    }, null);
  };

  const firstActiveAndUnansweredAnswer = findFirstUnansweredAndActiveAnswer();

  const activeAnswers = van.derive(() => {
    const arr = Object.values(answers.val);
    if (arr.length <= 1) {
      return arr;
    }
    return arr.filter(
      (answer) =>
        answer.isActive.val &&
        answer.screenType !== INTERMEDIATE_BLOCK_TYPES.END,
    );
  });

  const firstAnswer =
    firstActiveAndUnansweredAnswer || Object.values(answers.val)[0];

  const currentQuestionId = van.state(
    firstAnswer?.id || activeAnswers.val[0]?.id,
  );

  const currentQuestion = van.derive(
    () => questionsById[currentQuestionId.val],
  );

  const currentAnswer = van.derive(() => answers.val[currentQuestionId.val]);

  const isStartScreen = van.derive(() => {
    const question = currentQuestion.val;
    return (
      question.type === QUESTION_TYPES.INTERMEDIATE_BLOCK &&
      question.intermediateBlock?.screen_type === INTERMEDIATE_BLOCK_TYPES.START
    );
  });

  const isEndScreen = van.derive(() => {
    const question = currentQuestion.val;
    return (
      question.type === QUESTION_TYPES.INTERMEDIATE_BLOCK &&
      question.intermediateBlock?.screen_type === INTERMEDIATE_BLOCK_TYPES.END
    );
  });

  const findNextActiveQuestionId = () => {
    const answerss = activeAnswers.val;
    const currentAnswerIndex = activeAnswers.val.findIndex(
      (answer) => answer.id === currentAnswer.val.id,
    );
    const nextAnswer = answerss[currentAnswerIndex + 1];
    if (nextAnswer) {
      return nextAnswer.id;
    } else {
      return null;
    }
  };

  const findPrevActiveQuestionId = () => {
    const answerss = activeAnswers.val;
    const currentAnswerIndex = activeAnswers.val.findIndex(
      (answer) => answer.id === currentAnswer.val.id,
    );
    const prevAnswer = answerss[currentAnswerIndex - 1];
    if (prevAnswer) {
      return prevAnswer.id;
    } else {
      return null;
    }
  };

  const shouldShowNavButtons = van.state(false);

  const debouncedIsShowNavButtons = debounce(() => {
    if ((isStartScreen.val || isEndScreen.val) && hasScrollVisible.val) {
      shouldShowNavButtons.val = true;
      return;
    } else if (
      (isStartScreen.val || isEndScreen.val) &&
      !hasScrollVisible.val
    ) {
      shouldShowNavButtons.val = false;
      return;
    }

    const question = currentQuestion.val;
    const answer = currentAnswer.val;

    if (question.question_id !== answer.questionId) {
      shouldShowNavButtons.val = false;
      return;
    }

    const prevButtonDisabled = pollData.design["show_prev_button"] === 0;
    const questionCommentEnabled = answer.comment?.enabled?.val;
    const questionAssessmentsEnabled = answer.assessments?.enabled;

    const isRatingQuestion =
      question.type === QUESTION_TYPES.RATING ||
      question.type === QUESTION_TYPES.RATING_NPS ||
      question.type === QUESTION_TYPES.STAR_RATING ||
      question.type === QUESTION_TYPES.SMILE_RATING;

    const ratingQuestionWithoutCommentAndAssessments =
      isRatingQuestion &&
      !questionCommentEnabled &&
      !questionAssessmentsEnabled;

    if (prevButtonDisabled && ratingQuestionWithoutCommentAndAssessments) {
      shouldShowNavButtons.val = false;
      return;
    }

    const singleVariantsWithoutSelfVariantAndComment =
      question.type === QUESTION_TYPES.VARIANTS &&
      question.variantsType === VARIANT_TYPES.SINGLE &&
      !answer.selfVariant?.enabled &&
      !questionCommentEnabled;

    if (prevButtonDisabled && singleVariantsWithoutSelfVariantAndComment) {
      shouldShowNavButtons.val = false;
      return;
    }

    shouldShowNavButtons.val = true;
  }, 50);

  van.derive(() => {
    currentQuestion.val;
    hasScrollVisible.val;
    debouncedIsShowNavButtons();
  });

  const isFirstStep = van.derive(() => {
    // find first active question
    const answerss = activeAnswers.val;
    const firstActiveAnswer = answerss[0];

    if (!firstActiveAnswer) {
      return false;
    }
    return currentQuestionId.val === firstActiveAnswer.id;
  });

  const isLastStep = van.derive(() => {
    const answerss = activeAnswers.val;
    const lastActiveAnswer = answerss[answerss.length - 1];

    if (!lastActiveAnswer) {
      return false;
    }
    return currentQuestionId.val === lastActiveAnswer.id;
  });

  const showPrevButton = pollData.design["show_prev_button"] === 1;

  const prevButtonText = pollData.design.back_text || "Вернуться";

  const nextButtonText = pollData.design.next_text || "Далее";

  const finishButtonText = pollData.design.finish_text || "Завершить";

  // Group 1: Next / Finish buttons
  const nextButtonBackgroundColor =
    pollData.design.next_button_background_color || "rgba(63, 101, 241, 0)";
  const nextButtonTextColor =
    pollData.design.next_button_text_color || "rgba(255, 255, 255, 1)";
  const nextButtonStrokeColor =
    pollData.design.next_button_stroke_color || "rgba(63, 101, 241, 1)";
  const nextButtonRadius = pollData.design.next_button_radius || 24;

  // Group 2: Back / Test Report buttons
  const backButtonBackgroundColor =
    pollData.design.back_button_background_color || "rgba(255, 255, 255, 0)";
  const backButtonTextColor =
    pollData.design.back_button_text_color || "rgba(255, 255, 255, 1)";
  const backButtonStrokeColor =
    pollData.design.back_button_stroke_color || "rgba(255, 255, 255, 1)";
  const backButtonRadius = pollData.design.back_button_radius || 24;

  // Group 3: Take Survey / Start Over / Done buttons
  const startButtonBackgroundColor =
    pollData.design.start_button_background_color || "rgba(63, 101, 241, 1)";
  const startButtonTextColor =
    pollData.design.start_button_text_color || "rgba(255, 255, 255, 1)";
  const startButtonStrokeColor =
    pollData.design.start_button_stroke_color || "rgba(63, 101, 241, 1)";
  const startButtonRadius = pollData.design.start_button_radius || 24;

  const validateFields = (fields = []) => {
    const answer = currentAnswer.val;
    const question = currentQuestion.val;

    const errors = validateByType(question, answer, { fields });

    answer.errors.val = errors;

    return !Object.values(errors).some((error) => error);
  };

  const debouncedValidateFields = debounce(validateFields, 200);

  const activeStartScreen = van.derive(() => {
    const answerss = activeAnswers.val;
    const screen = answerss.find(
      (answer) =>
        answer.screenType === INTERMEDIATE_BLOCK_TYPES.START &&
        answer.isActive.val,
    );

    if (!screen) {
      return null;
    }
    return questionsById[screen.id];
  });

  const startButtonText = van.derive(() => {
    const activeStartScreenVal = activeStartScreen.val;
    if (activeStartScreenVal) {
      return (
        activeStartScreenVal.intermediateBlock?.poll_button_text ||
        "Пройти опрос"
      );
    } else {
      return pollData?.design.start_text || "Пройти опрос";
    }
  });

  const readyButtonEnabled = van.derive(() => {
    if (currentQuestion.val && isEndScreen.val) {
      return currentQuestion.val?.intermediateBlock?.ready_button === 1;
    } else {
      return false;
    }
  });

  const readyButtonText = van.derive(() => {
    if (currentQuestion.val && isEndScreen.val && readyButtonEnabled.val) {
      return (
        currentQuestion.val?.intermediateBlock?.ready_button_text || "Готово"
      );
    } else {
      return "";
    }
  });

  const readyButtonExternalLink = van.derive(() => {
    if (currentQuestion.val && isEndScreen.val && readyButtonEnabled.val) {
      return currentQuestion.val?.intermediateBlock?.external_link || "";
    } else {
      return "";
    }
  });

  const closeWidgetButtonEnabled = van.derive(() => {
    if (currentQuestion.val && isEndScreen.val) {
      const closeWidgetButtonEnabled =
        currentQuestion.val?.intermediateBlock?.close_widget_button === 1;
      return closeWidgetButtonEnabled;
    } else {
      return false;
    }
  });

  const closeWidgetButtonText = van.derive(() => {
    if (
      currentQuestion.val &&
      isEndScreen.val &&
      closeWidgetButtonEnabled.val
    ) {
      return (
        currentQuestion.val?.intermediateBlock?.close_widget_button_text ||
        "Завершить"
      );
    } else {
      return "";
    }
  });

  const canGoToNextStep = van.derive(() => {
    const isSkipped = currentAnswer.val.isSkipped.val;
    const errors = currentAnswer.val.errors.val;
    const hasErrors = Object.values(errors).some((error) => error);
    return isSkipped || !hasErrors;
  });

  const handleFormFinish = async () => {
    markDirtyByType(currentQuestion.val, currentAnswer.val);
    const success = validateFields();
    const isSkipped = currentAnswer.val.isSkipped.val;
    if (isSkipped || success) {
      if (onFormSuccess && typeof onFormSuccess === "function") {
        onFormSuccess(answers.val);
      }

      if (closeByFinishButton) {
        // Если в настройках виджета включена опция "Закрыть виджет по кнопке «Завершить»".
        // Закрываем виджет.
        onCloseWidgetButtonClick();
      } else {
        // Переходим на первый конечный экран.
        const firstActiveEndScreen = Object.values(answers.val).find(
          (answer) =>
            answer.screenType === INTERMEDIATE_BLOCK_TYPES.END &&
            answer.isActive.val,
        );
        currentQuestionId.val = firstActiveEndScreen.id;
      }

      try {
        await saveAnswerByType(
          currentQuestion.val,
          currentAnswer.val,
          apiConfig,
        );
        if (pollStatus !== "done") {
          await api.updatePollStatus({
            authKey: pollData.answer.auth_key,
            key: widgetKey,
            status: "done",
            assetsRoot: apiConfig.assetsRoot,
          });
        }
      } catch (error) {
        console.error("Failed to save answer:", error);
      }
    }
  };

  const goToNextStep = () => {
    if (isLastStep.val) {
      handleFormFinish();
      return;
    }
    markDirtyByType(currentQuestion.val, currentAnswer.val);
    const success = validateFields();
    const isSkipped = currentAnswer.val.isSkipped.val;
    if (isSkipped || success) {
      try {
        saveAnswerByType(currentQuestion.val, currentAnswer.val, apiConfig);
      } catch (error) {
        console.error("Failed to save answer:", error);
      }

      const nextQuestionId = findNextActiveQuestionId();
      if (nextQuestionId) {
        currentQuestionId.val = nextQuestionId;
      }
    }
  };

  const goToPrevStep = () => {
    const prevQuestionId = findPrevActiveQuestionId();
    if (prevQuestionId !== undefined || prevQuestionId !== null) {
      currentQuestionId.val = prevQuestionId;
    }
  };

  const isIntermediateBlock = van.derive(() => {
    const question = currentQuestion.val;
    return question.type === QUESTION_TYPES.INTERMEDIATE_BLOCK;
  });

  return {
    currentQuestionId,
    isFirstStep,
    isLastStep,
    currentAnswer,
    currentQuestion,
    showPrevButton,
    prevButtonText,
    nextButtonText,
    finishButtonText,
    canGoToNextStep,
    shouldShowNavButtons,
    onPrevClick: goToPrevStep,
    goToNextStep,
    goToPrevStep,
    onStartButtonClick: goToNextStep,
    onNextClick: goToNextStep,
    onFinishClick: handleFormFinish,
    isStartScreen,
    isEndScreen,
    startButtonText,
    readyButtonEnabled,
    readyButtonText,
    readyButtonExternalLink,
    closeWidgetButtonEnabled,
    closeWidgetButtonText,
    validateFields,
    debouncedValidateFields,
    isIntermediateBlock,
    hasScrollVisible,
    startButtonStyle: {
      backgroundColor: startButtonBackgroundColor,
      color: startButtonTextColor,
      borderColor: startButtonStrokeColor,
      borderRadius: startButtonRadius,
    },
    backButtonStyle: {
      backgroundColor: backButtonBackgroundColor,
      color: backButtonTextColor,
      borderColor: backButtonStrokeColor,
      borderRadius: backButtonRadius,
    },
    nextButtonStyle: {
      backgroundColor: nextButtonBackgroundColor,
      color: nextButtonTextColor,
      borderColor: nextButtonStrokeColor,
      borderRadius: nextButtonRadius,
    },
  };
}
