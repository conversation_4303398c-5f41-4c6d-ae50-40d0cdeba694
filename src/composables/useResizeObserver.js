import van from "vanjs-core";
/**
 * Используется для отслеживания изменения размера элемента.
 * @param {import("vanjs-core").State} ref - ссылка на элемент, размер которого нужно отслеживать.
 * @param {ResizeObserverOptions} options - опции ResizeObserver.
 * @returns {import("vanjs-core").State<object>} - объект с размерами элемента.
 */
export default function useResizeObserver(ref, enabled = true, options) {
  const size = van.state({ width: 0, height: 0 });

  van.derive(() => {
    if (ref.val && enabled) {
      const observer = new ResizeObserver((entries) => {
        const { width, height } = entries[0].contentRect;
        size.val = { width, height };
      });

      observer.observe(ref.val, options);
    }
  });

  return size;
}
