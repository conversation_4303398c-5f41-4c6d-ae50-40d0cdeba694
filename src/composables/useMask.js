import van from "vanjs-core";
import phoneMaskOptions from "../helpers/input-masks/phone";
import { Mask<PERSON> } from "@maskito/core";

/**
 * Композабл для работы с масками ввода
 * @param {String} type - Тип маски. Возможные значения: 'phone', 'number', 'date', 'card', 'cvv', 'expiry'
 * @param {Object} options - Опции маски
 */
export default function useMask(ref, type, options = {}) {
  const initializeMask = (el, type, options) => {
    // let mask = null;

    // @TODO: Destroy the mask on teardown!!
    if (type === "phone") {
      new Maskito(el, phoneMaskOptions);
    } else if (type === "number") {
      new Maskito(el, { mask: /^\d+$/ });
    } else if (type === "custom") {
      new Maskito(el, options);
    }
  };
  van.derive(() => {
    if (ref.val) {
      initializeMask(ref.val, type, options);
    }
  });
}
