import van from "vanjs-core";

export function useWindowSize() {
  const width = van.state(window.innerWidth);
  const height = van.state(window.innerHeight);

  const updateSize = () => {
    width.val = window.innerWidth;
    height.val = window.innerHeight;
  };

  van.derive(() => {
    window.addEventListener("resize", updateSize);
    return () => window.removeEventListener("resize", updateSize);
  });

  return { width, height };
}
