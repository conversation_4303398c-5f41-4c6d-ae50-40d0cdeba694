import van from "vanjs-core";
import { throttle } from "../helpers";

/**
 * Tracks the scroll position of an element.
 * @param {import("vanjs-core").State} ref - Reference to the element to track.
 * @param {boolean} initialEnabled - Initial state of the enabled property.
 * @returns {import("vanjs-core").State<object>} - Object with scroll position and state.
 */
export default function useElementScroll(ref, initialEnabled = true) {
  const scrollState = van.state({
    scrollY: 0,
    scrollX: 0,
    reachedTop: true,
    reachedBottom: false,
  });

  const enabled = van.state(initialEnabled);

  const updateScrollState = throttle(() => {
    if (ref.val) {
      const { scrollTop, scrollLeft, scrollHeight, clientHeight } = ref.val;
      scrollState.val = {
        scrollY: scrollTop,
        scrollX: scrollLeft,
        reachedTop: scrollTop <= 10,
        reachedBottom: scrollTop + 10 + clientHeight >= scrollHeight,
      };
    }
  }, 100);

  van.derive(() => {
    if (ref.val && enabled.val) {
      ref.val.addEventListener("scroll", updateScrollState);
      updateScrollState(); // Initial call to set the state
    } else if (!enabled.val) {
      ref.val.removeEventListener("scroll", updateScrollState);
    }
  });

  return { scrollState, enabled, forceUpdate: () => updateScrollState() };
}
