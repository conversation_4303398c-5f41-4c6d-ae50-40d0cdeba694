import van from "vanjs-core";

/**
 * Locks or unlocks body scroll based on a reactive state.
 * @param {import("vanjs-core").State<boolean>} isLocked - Reactive state to determine if scroll should be locked.
 * @returns {object} - Object with methods to manually lock and unlock scroll.
 */
export default function useBodyScrollLock(isLocked) {
  let originalOverflow;
  let originalPaddingRight;

  const lockScroll = () => {
    originalOverflow = document.body.style.overflow;
    originalPaddingRight = document.body.style.paddingRight;

    const scrollbarWidth =
      window.innerWidth - document.documentElement.clientWidth;
    document.body.style.overflow = "hidden";
    document.body.style.paddingRight = `${scrollbarWidth}px`;
  };

  const unlockScroll = () => {
    document.body.style.overflow = originalOverflow;
    document.body.style.paddingRight = originalPaddingRight;
  };

  van.derive(() => {
    if (isLocked.val) {
      lockScroll();
    } else {
      unlockScroll();
    }
  });

  return {
    lock: () => (isLocked.val = true),
    unlock: () => (isLocked.val = false),
  };
}
