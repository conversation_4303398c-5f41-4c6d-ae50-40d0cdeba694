/**
 * Fetch widget data from the server
 * @param {string} url - The URL of the page where the widget is embedded
 * @param {string} widgetCode - The widget code
 * @param {string} cookies - The cookies of the user
 * @param {string} baseUrl - The base URL of the API
 * @param {boolean} isMobile - Whether the device is mobile
 * @returns {Promise<Object>} - The widget data
 * @param {boolean} isHistory - Whether to track the history
 */
async function fetchWidgetData(
  url,
  widgetCode,
  cookies,
  baseUrl,
  additionalParams = {},
  eventName = null,
  isMobile = null,
  isHistory = null,
) {
  const data = {
    url,
    widget_code: widgetCode,
    cookies,
  };

  if (isMobile !== null) {
    data.mobile = isMobile;
  }

  if (isHistory !== null) {
    data.history = isHistory;
  }

  const formData = new FormData();
  for (const key in data) {
    formData.append(key, data[key]);
  }

  if (eventName) {
    formData.append("event", eventName);
  }

  // send additionalParams in the form of params[key]: value
  for (const key in additionalParams) {
    formData.append(`params[${key}]`, additionalParams[key]);
  }

  let endpointUrl = `${baseUrl}v1/visit`;

  // @TODO: make sure it doesn't break on production
  if (import.meta.env.DEV || import.meta.env.MODE === "test") {
    // take all query params and append them to the form data
    const url = new URL(window.location.href);
    const searchParams = url.searchParams;
    for (const [key, value] of searchParams) {
      // replace if the key already exists
      formData.set(key, value);
    }
  }

  const response = await fetch(endpointUrl, {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    throw new Error("Failed to fetch widget data");
  }

  const widgetData = await response.json();

  return widgetData;
}

async function fetchPollData(widgetData, assetsRoot, additionalParams = {}) {
  let key = widgetData.key || window.FOQUZ_SDK._test.widgetKey;

  if (import.meta.env.DEV || import.meta.env.MODE === "test") {
    // take all query params and append them to the form data
    const url = new URL(window.location.href);
    const searchParams = url.searchParams;
    const keyParam = searchParams.get("key");
    if (keyParam) {
      key = keyParam;
    }
  }

  // Build URL with additional parameters
  const url = new URL(`${assetsRoot}foquz/api/p/answer`);
  url.searchParams.append("key", key);

  // Add additional parameters to URL
  for (const [paramKey, paramValue] of Object.entries(additionalParams)) {
    if (paramValue !== null && paramValue !== undefined) {
      url.searchParams.append(paramKey, paramValue);
    }
  }

  const response = await fetch(url.toString(), { method: "POST" });

  if (!response.ok) {
    throw new Error("Failed to fetch poll data");
  }

  const pollData = await response.json();

  return pollData;
}

async function saveAnswer({
  authKey,
  questionId,
  foquzAnswerItem,
  endpointUrl,
  isSkipped,
}) {
  let foquzAnswerItemString = new URLSearchParams(foquzAnswerItem).toString();

  if (isSkipped) {
    foquzAnswerItemString = "skipped=1";
  } else {
    // convert each field of foquzAnswerItem to a string
    // if the field is an object, convert it to a key[innerKey]=value string
    // if the field is an array, convert it to a key[]=value1&key[]=value2 string
    const foquzAnswerItemEntries = Object.entries(foquzAnswerItem);
    const foquzAnswerItemStringEntries = foquzAnswerItemEntries.map(
      ([key, value]) => {
        if (value === null || value === undefined) {
          return ``;
        }
        if (Array.isArray(value)) {
          return value
            .map((v, index) => {
              // Handle arrays of objects
              if (typeof v === "object" && v !== null) {
                const objEntries = Object.entries(v);
                return objEntries
                  .map(([objKey, objValue]) => {
                    // Encode the value properly to handle special characters
                    const encodedValue = encodeURIComponent(objValue || "");
                    return `${key}[${index}][${objKey}]=${encodedValue}`;
                  })
                  .join("&");
              }
              // Handle arrays of primitive values
              return `${key}[]=${encodeURIComponent(v || "")}`;
            })
            .join("&");
        }

        if (typeof value === "object") {
          const innerEntries = Object.entries(value);
          return innerEntries
            .map(
              ([innerKey, innerValue]) => `${key}[${innerKey}]=${innerValue}`,
            )
            .join("&");
        }

        return `${key}=${value}`;
      },
    );

    // filter out falsy values
    foquzAnswerItemString = foquzAnswerItemStringEntries
      .filter(Boolean)
      .join("&");
  }

  const formData = new FormData();

  formData.append("FoquzAnswerItem", foquzAnswerItemString);

  const response = await fetch(
    `${endpointUrl}foquz/api/p/save-answer?authKey=${authKey}&questionId=${questionId}`,
    {
      method: "POST",
      body: formData,
    },
  );

  if (!response.ok) {
    throw new Error("Failed to save answer");
  }

  const data = await response.json();
  return data;
}

async function updatePollStatus({ status, assetsRoot, key }) {
  const formData = new FormData();
  formData.append("status", status);
  formData.append("key", key);

  let url = `${assetsRoot}foquz/api/p/change-status?key=${key}`;

  const response = await fetch(url, {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    throw new Error("Failed to update poll status");
  }

  const data = await response.json();
  return data;
}

async function uploadFiles({
  authKey,
  files,
  questionId,
  isPreview = false,
  uuids = [],
  assetsRoot = "",
}) {
  const formData = new FormData();

  // Append each file to the FormData
  files.forEach((file, index) => {
    formData.append("files[]", file);
    if (uuids[index]) {
      formData.append("uuids[]", uuids[index]);
    }
  });

  // Build the URL with query parameters
  const baseUrl = `${assetsRoot}foquz/api/p/upload-files`;
  const params = {
    authKey,
    question_id: questionId,
  };

  if (isPreview) {
    params.preview = "1";
  }

  const url = buildFoquzUrl(baseUrl, params);

  const response = await fetch(url, {
    method: "POST",
    body: formData,
  });

  if (!response.ok) {
    const error = new Error("Failed to upload files");
    error.status = response.status;
    throw error;
  }

  const data = await response.json();
  return data;
}

/**
 * Build URL with query parameters
 * @param {string} baseUrl - Base URL
 * @param {Object} params - Query parameters
 * @returns {string} - Full URL with query parameters
 */
function buildFoquzUrl(baseUrl, params = {}) {
  const url = new URL(baseUrl);
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      url.searchParams.append(key, value);
    }
  });
  return url.toString();
}

function getBaseAssetsUrl(assetsRoot = "") {
  return assetsRoot;
}

export default {
  fetchWidgetData,
  fetchPollData,
  saveAnswer,
  updatePollStatus,
  uploadFiles,
  getBaseAssetsUrl,
};
