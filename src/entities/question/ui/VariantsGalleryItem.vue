<script setup>
import { useFancybox } from '@shared/composables/useFancybox'
import Check from '@shared/ui/Check.vue'
import { computed, onMounted, onUnmounted, toValue } from 'vue'

const props = defineProps({
  variant: {
    type: Object,
    required: true,
  },
  previewClickable: {
    type: [Boolean, Array],
    default: true,
  },
  type: {
    type: String,
    default: 'checkbox',
    validator: value => ['checkbox', 'radio'].includes(value),
  },
  mediaType: {
    type: String,
    default: 'image',
    validator: value => ['image', 'video'].includes(value),
  },
  view: {
    type: String,
    default: 'default',
    validator: value => ['default', 'compact'].includes(value),
  },
  groupId: {
    type: String,
    default: '',
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: [Array, String, Number],
    default: () => [],
  },
})

const emit = defineEmits(['update:model-value'])
const isPlaceholder = computed(() => !toValue(props.variant.poster) && !toValue(props.variant.url))

const { show, addItem, removeItem } = useFancybox(props.groupId || 'gallery')

onMounted(() => {
  addItem({
    src: computed(() => props.variant.url),
    type: computed(() => props.mediaType),
    poster: computed(() => props.variant.poster),
  })
})

onUnmounted(() => {
  removeItem()
})

const isPreviewClickable = computed(() => {
  if (typeof props.previewClickable === 'boolean') {
    return props.previewClickable
  }
  return props.previewClickable.includes(props.mediaType)
})

const isChecked = computed(() => {
  if (props.type === 'radio') {
    return props.modelValue === props.variant.value
  }
  return Array.isArray(props.modelValue) && props.modelValue.includes(props.variant.value)
})

function openPreview() {
  if (!isChecked.value && isPreviewClickable.value) {
    show()
  }
}

function handlePreviewClick() {
  if (isPreviewClickable.value) {
    openPreview()
  }
  else {
    emit('update:model-value', variant.value)
  }
}

const galleryItemClasses = computed(() => ({
  'variants-gallery-item': true,
  'variants-gallery-item--inactive': props.inactive,
  'variants-gallery-item--disabled': props.disabled,
  'variants-gallery-item--video': props.mediaType === 'video',
  'variants-gallery-item--checked': isChecked.value,
  'variants-gallery-item--no-poster': !props.variant.poster,
  'variants-gallery-item--preview-clickable': isPreviewClickable.value,
  'variants-gallery-item--placeholder': isPlaceholder.value,
}))
</script>

<template>
  <div :class="galleryItemClasses">
    <div class="variants-gallery-item__preview" @click="handlePreviewClick">
      <Check
        :type="type"
        :value="variant.value"
        :model-value="modelValue"
        :checked="isChecked"
        class="variants-gallery-item__check"
        @click.stop
        @update:model-value="emit('update:model-value', $event)"
      />
      <div
        v-if="isPlaceholder"
        class="variants-gallery-item__placeholder"
      >
        <svg
          width="70" height="70" viewBox="0 0 138 107" fill="none" xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M118.401 71.6549L119.631 70.0783L119.623 70.0719L119.615 70.0656L118.401 71.6549ZM112.599 71.6549L111.385 70.0656L111.374 70.0739L111.364 70.0823L112.599 71.6549ZM99.1 82.2627L97.7122 83.7028L98.9663 84.9113L100.336 83.8352L99.1 82.2627ZM37.2313 58.0338L36.2611 56.2849L37.2313 58.0338ZM37.2313 29.1981L36.2611 30.947L37.2313 29.1981ZM56.2909 39.7712L57.2611 38.0222L56.2909 39.7712ZM56.2909 47.4607L57.2611 49.2096L56.2909 47.4607ZM83 25H125.304V21H83V25ZM134 33.6957V94.3044H138V33.6957H134ZM125.304 103H64.6957V107H125.304V103ZM56 94.3044V83.5H52V94.3044H56ZM64.6957 103C59.8932 103 56 99.1068 56 94.3044H52C52 101.316 57.684 107 64.6957 107V103ZM134 94.3044C134 99.1068 130.107 103 125.304 103V107C132.316 107 138 101.316 138 94.3044H134ZM125.304 25C130.107 25 134 28.8932 134 33.6957H138C138 26.684 132.316 21 125.304 21V25ZM137.231 83.8147L119.631 70.0783L117.17 73.2315L134.769 86.9679L137.231 83.8147ZM119.615 70.0656C118.454 69.1789 116.97 68.7373 115.5 68.7373V72.7373C116.207 72.7373 116.809 72.956 117.187 73.2442L119.615 70.0656ZM115.5 68.7373C114.03 68.7373 112.546 69.1789 111.385 70.0656L113.813 73.2442C114.191 72.956 114.793 72.7373 115.5 72.7373V68.7373ZM111.364 70.0823L97.8643 80.6901L100.336 83.8352L113.835 73.2275L111.364 70.0823ZM105.696 58.8696C112.707 58.8696 118.391 53.1855 118.391 46.1739H114.391C114.391 50.9764 110.498 54.8696 105.696 54.8696V58.8696ZM118.391 46.1739C118.391 39.1623 112.707 33.4783 105.696 33.4783V37.4783C110.498 37.4783 114.391 41.3714 114.391 46.1739H118.391ZM105.696 33.4783C98.684 33.4783 93 39.1623 93 46.1739H97C97 41.3714 100.893 37.4783 105.696 37.4783V33.4783ZM93 46.1739C93 53.1855 98.684 58.8696 105.696 58.8696V54.8696C100.893 54.8696 97 50.9764 97 46.1739H93ZM100.488 80.8225L84.3878 65.3082L81.6122 68.1886L97.7122 83.7028L100.488 80.8225ZM12.6127 4H72.7514V0H12.6127V4ZM81.3642 12.6387V72.9244H85.3642V12.6387H81.3642ZM72.7514 81.563H12.6127V85.563H72.7514V81.563ZM4 72.9244V12.6387H0V72.9244H4ZM12.6127 81.563C7.86065 81.563 4 77.7 4 72.9244H0C0 79.8999 5.64231 85.563 12.6127 85.563V81.563ZM81.3642 72.9244C81.3642 77.7 77.5035 81.563 72.7514 81.563V85.563C79.7219 85.563 85.3642 79.8999 85.3642 72.9244H81.3642ZM72.7514 4C77.5035 4 81.3642 7.86305 81.3642 12.6387H85.3642C85.3642 5.66313 79.7219 0 72.7514 0V4ZM12.6127 0C5.6423 0 0 5.66313 0 12.6387H4C4 7.86305 7.86065 4 12.6127 4V0ZM57.2611 38.0222L38.2015 27.4491L36.2611 30.947L55.3207 41.5201L57.2611 38.0222ZM28.3006 33.0428V54.189H32.3006V33.0428H28.3006ZM38.2015 59.7827L57.2611 49.2096L55.3207 45.7118L36.2611 56.2849L38.2015 59.7827ZM28.3006 54.189C28.3006 59.265 33.9071 62.165 38.2015 59.7827L36.2611 56.2849C34.3949 57.3201 32.3006 55.9482 32.3006 54.189H28.3006ZM38.2015 27.4491C33.9071 25.0668 28.3006 27.9669 28.3006 33.0428H32.3006C32.3006 31.2836 34.3949 29.9117 36.2611 30.947L38.2015 27.4491ZM55.3207 41.5201C57.028 42.4672 57.028 44.7647 55.3207 45.7118L57.2611 49.2096C61.7145 46.7391 61.7145 40.4927 57.2611 38.0222L55.3207 41.5201Z" fill="#A6B1BC"
          />
        </svg>
      </div>
      <div v-else class="variants-gallery-item__preview-trigger">
        <div class="variants-gallery-item__content">
          <img :src="variant.poster" :alt="variant.text" class="variants-gallery-item__image">
        </div>
      </div>
      <div v-if="mediaType === 'video'" class="variants-gallery-item__icon">
        <svg v-if="view === 'default'" width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 7C1 3.68629 3.68629 1 7 1H41C44.3137 1 47 3.68629 47 7V41C47 44.3137 44.3137 47 41 47H7C3.68629 47 1 44.3137 1 41V7Z" stroke="white" stroke-width="2" />
          <path d="M31.6939 26.639C33.4354 25.6752 33.4354 23.2659 31.6939 22.3022L20.9184 16.3392C19.1769 15.3755 17 16.5801 17 18.5076V30.4336C17 32.3611 19.1769 33.5657 20.9184 32.602L31.6939 26.639Z" stroke="white" stroke-width="2" />
        </svg>
        <svg v-else-if="view === 'compact'" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1.5 3.86957C1.5 2.28475 2.78475 1 4.36957 1H20.6304C22.2153 1 23.5 2.28475 23.5 3.86957V20.1304C23.5 21.7153 22.2153 23 20.6304 23H4.36957C2.78475 23 1.5 21.7153 1.5 20.1304V3.86957Z" stroke="white" stroke-width="2" />
          <path d="M16.1797 13.2621C17.0126 12.8012 17.0126 11.6489 16.1797 11.188L11.0262 8.33613C10.1933 7.87522 9.15217 8.45136 9.15217 9.37318V15.0769C9.15217 15.9988 10.1933 16.5749 11.0262 16.114L16.1797 13.2621Z" stroke="white" stroke-width="2" />
        </svg>
      </div>
    </div>
    <div v-if="variant.label" class="variants-gallery-item__label">
      {{ variant.label }}
    </div>
  </div>
</template>

<style scoped>
.variants-gallery-item {
  position: relative;
  width: 100%;
  transition: opacity 0.3s;
  transition: border-color 0.3s;
  display: flex;
  flex-direction: column;
  --item-border-radius: 8px;
}

.variants-gallery-item__check {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 10;
}

.variants-gallery-item:not(.variants-gallery-item--preview-clickable) :deep(.variants-gallery-item__check) {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
}

.variants-gallery-item:not(.variants-gallery-item--preview-clickable) :deep(.variants-gallery-item__check label) {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  padding-top: 5px;
  padding-left: 5px;
}

.variants-gallery-item__preview {
  position: relative;
  padding-top: 100%;
  overflow: hidden;
  background-color: transparent;
  flex: 0 0 auto;
  border-radius: var(--item-border-radius);
}

.variants-gallery-item--video .variants-gallery-item__preview:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  background-color: rgba(0, 0, 0, 0.6);
  pointer-events: none;
  transition: opacity 0.3s;
}

.variants-gallery-item__placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(231, 235, 237, 1);
  transition: opacity 0.3s;
}

.variants-gallery-item__placeholder svg {
  display: block;
  max-width: 100%;
  max-height: 100%;
}

.variants-gallery-item__preview-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition: opacity 0.3s;
}

.variants-gallery-item__preview-trigger:after,
.variants-gallery-item__placeholder:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid var(--fqz-poll-text-on-place);
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.3s;
  border-radius: var(--item-border-radius);
  opacity: 0.1;
  z-index: 11;
}

.variants-gallery-item--preview-clickable .variants-gallery-item__preview-trigger {
  cursor: zoom-in;
}

.variants-gallery-item:not(.variants-gallery-item--preview-clickable) .variants-gallery-item__preview-trigger {
  pointer-events: none;
}

.variants-gallery-item__preview-trigger:hover {
  opacity: 0.8;
}

.variants-gallery-item__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: blur(10px);
  transform: scale(3);
}

.variants-gallery-item__content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 11;
}

.variants-gallery-item__image {
  width: calc(100% - 2px);
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  margin-left: 1px;
  object-fit: contain;
}

.variants-gallery-item__icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

.variants-gallery-item__label {
  font-size: 11px;
  line-height: 1.1;
  transition: opacity 0.3s;
  padding-top: 10px;
  align-self: flex-start;
  word-break: break-all;
}

.variants-gallery-item--disabled {
  pointer-events: none;
}

.variants-gallery-item--disabled .variants-gallery-item__preview-trigger,
.variants-gallery-item--disabled .variants-gallery-item__placeholder {
  opacity: 0.5;
}

.variants-gallery-item--disabled .variants-gallery-item__label {
  opacity: 0.7;
}

.variants-gallery-item:not(.variants-gallery-item--preview-clickable) .variants-gallery-item__preview {
  cursor: pointer;
}

.variants-gallery-item:not(.variants-gallery-item--preview-clickable) .variants-gallery-item__preview-trigger {
  pointer-events: none;
}

.variants-gallery-item--placeholder .variants-gallery-item__preview-trigger {
  display: none;
}
</style>
