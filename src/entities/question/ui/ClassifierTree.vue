<script setup>
import Check from '@shared/ui/Check.vue'
import Hint from '@shared/ui/Hint.vue'
import Icon from '@shared/ui/Icon.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import FadeTransition from '@shared/ui/transitions/FadeTransition.vue'
import { computed } from 'vue'

const props = defineProps({
  node: {
    type: Object,
    required: true,
  },
  isListType: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  disableSelectCategory: {
    type: Boolean,
    default: false,
  },
  showTooltips: {
    type: <PERSON><PERSON>an,
    default: false,
  },
  tooltipCloseText: {
    type: String,
    default: 'Закрыть',
  },
  isSelectableCategories: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['toggle', 'toggleOpen'])

const hasChildren = computed(() => props.node.children && Object.keys(props.node.children).length > 0)

function toggleOpenNode() {
  emit('toggleOpen', props.node)
}

function onCheckChange() {
  emit('toggle', props.node)
}

const isLeaf = computed(() => !hasChildren.value)

const state = computed(() => {
  const traverse = (node) => {
    if (!node.children || node.children.length === 0) {
      return [node.isSelected.value && !node.disabled.value ? 1 : 0, node.isSelected.value ? 'all' : 'none']
    }

    const childResults = node.children.map(child => traverse(child))
    const count = childResults.reduce((sum, [childCount]) => sum + childCount, 0)

    let state = 'none'
    if (childResults.every(([, childState]) => childState === 'all')) {
      state = 'all'
    }
    else if (childResults.some(([, childState]) => childState !== 'none')) {
      state = 'some'
    }

    return [count, state]
  }

  if (!props.node.children) {
    return [0, 'none']
  }

  return traverse(props.node)
})

const checkedChildrenCount = computed(() => state.value[0])
const checkedChildrenState = computed(() => state.value[1])

const isCheckedValue = computed(() => {
  if (checkedChildrenState.value === 'all') {
    return true
  }
  else if (checkedChildrenState.value === 'some') {
    return 'intermediate'
  }
  else {
    return false
  }
})
const hasCheckedChildren = computed(() => checkedChildrenState.value !== 'none')

const isInactive = computed(() => {
  return !isLeaf.value && props.disableSelectCategory && !props.node.isSelected.value && !props.node.disabled.value
})
</script>

<template>
  <div
    class="classifier-tree-node"
    :class="{
      'classifier-tree-node--disabled': node.disabled.value,
      'classifier-tree-node--list': isListType,
      'classifier-tree-node--inactive': isInactive,
      'classifier-tree-node--leaf': node.children === null,
      'classifier-tree-node--selectable-categories': props.isSelectableCategories,
    }"
  >
    <div class="classifier-tree-node__content">
      <Check
        v-if="multiple && isSelectableCategories && hasChildren" :checked="isCheckedValue" :disabled="node.disabled.value"
        :type="multiple ? 'checkbox' : 'radio'" class="classifier-tree-node__check" @change="onCheckChange"
      />
      <span
        v-if="hasChildren" class="classifier-tree-node__text" :class="{
          'classifier-tree-node__text--bold': hasChildren,
        }"
      >
        {{ node.title }}
      </span>
      <Check
        v-else :checked="node.isSelected.value" :disabled="node.disabled.value" :inactive="isInactive"
        :label="node.title" :type="multiple ? 'checkbox' : 'radio'" @change="onCheckChange"
      />
      <FadeTransition>
        <span v-if="!multiple && hasChildren && hasCheckedChildren" class="classifier-tree-node__indicator">
          <svg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M7 1L3 5L1 2.98438" stroke="white" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </FadeTransition>
      <span v-if="multiple && hasChildren && hasCheckedChildren" class="classifier-tree-node__count">
        ({{ checkedChildrenCount }})
      </span>
      <Hint
        v-if="node.description.value && showTooltips"
        :text="node.description"
        :dialog-close-text="tooltipCloseText"
        trigger-class="classifier-tree-node__hint-trigger"
      />
      <button
        v-if="hasChildren" class="classifier-tree-node__toggle"
        :class="{ 'classifier-tree-node__toggle--open': node.isOpen.value }" type="button"
        @click="toggleOpenNode(node)"
      >
        <Icon name="chevron-bottom" width="13px" height="7px" />
      </button>
    </div>
    <SlideTransition>
      <div v-if="node.children && node.isOpen.value" class="classifier-tree-node__children">
        <div class="classifier-tree-node__children-inner">
          <ClassifierTree
            v-for="child in node.children" :key="child.id" :node="child" :multiple="multiple"
            :disable-select-category="disableSelectCategory" :show-tooltips="showTooltips"
            :is-selectable-categories="isSelectableCategories"
            @toggle="$emit('toggle', $event)"
            @toggle-open="$emit('toggleOpen', $event)"
          />
        </div>
      </div>
    </SlideTransition>
  </div>
</template>

<style scoped>
.classifier-tree-node {
  padding-bottom: 20px;
}

.classifier-tree-node--list {
  padding-bottom: 15px;
}

.classifier-tree-node:last-of-type {
  padding-bottom: 0;
}

.classifier-tree-node__check,
.classifier-tree-node__text {
  transition: opacity 0.3s;
}

.classifier-tree-node--disabled .classifier-tree-node__check,
.classifier-tree-node--disabled .classifier-tree-node__text {
  opacity: 0.7;
  cursor: not-allowed;
}

.classifier-tree-node--disabled:not(.classifier-tree-node--selectable-categories):not(.classifier-tree-node--leaf)
  .classifier-tree-node__check,
.classifier-tree-node--disabled:not(.classifier-tree-node--selectable-categories):not(.classifier-tree-node--leaf)
  .classifier-tree-node__text {
  opacity: 1;
  cursor: default;
}

.classifier-tree-node--inactive .classifier-tree-node__check {
  opacity: 0.7;
}

.classifier-tree-node__content {
  display: flex;
  align-items: center;
  min-height: 22px;
}

.classifier-tree-node__text {
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
}

.classifier-tree-node__text--bold {
  font-weight: bold;
}

:deep(.classifier-tree-node__hint-trigger) {
  margin-left: 6px;
  flex: 0 0 auto;
  margin-top: -7px;
  top: 3px;
  position: relative;
}

.classifier-tree-node__indicator,
.classifier-tree-node__count {
  margin-left: 8px;
  flex: 0 0 auto;
}

.classifier-tree-node__indicator {
  margin-left: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--fqz-poll-main-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.classifier-tree-node__indicator svg {
  display: block;
}

.classifier-tree-node__toggle {
  background: none;
  border: none;
  cursor: pointer;
  margin-left: 10px;
  transition: opacity 0.3s ease;
  flex: 0 0 auto;
  width: 45px;
  align-items: center;
  justify-content: flex-start;
  display: inline-flex;
  padding: 0;
  align-self: stretch;
}

.classifier-tree-node__toggle .fc-icon {
  transition: transform 0.3s ease;
}

.classifier-tree-node__toggle:hover {
  opacity: 0.8;
}

.classifier-tree-node__toggle--open .fc-icon {
  transform: rotate(180deg);
}

.classifier-tree-node__children {
  margin-left: 15px;
}

.classifier-tree-node__children-inner {
  padding-top: 15px;
}

.classifier-tree-node__children .classifier-tree-node {
  padding-bottom: 15px;
}

.classifier-tree-node__children .classifier-tree-node:last-of-type {
  padding-bottom: 0;
}

.classifier-tree-node--leaf :deep(.classifier-tree-node__hint-trigger) {
  margin-top: -6px;
}

@media (max-width: 679px) {
  .classifier-tree-node__text {
    font-size: 14px;
    line-height: 1.1;
  }

  .classifier-tree-node__toggle {
    margin-left: 10px;
  }

  .classifier-tree-node__count {
    margin-left: 0;
  }

  .classifier-tree-node__count:before {
    content: ' ';
    display: inline-block;
  }

  :deep(.classifier-tree-node__hint-trigger) {
    margin-top: -15px;
    margin-left: 4px;
    top: 6px;
  }

  .classifier-tree-node--leaf :deep(.classifier-tree-node__hint-trigger) {
    margin-top: -10px;
  }
}
</style>
