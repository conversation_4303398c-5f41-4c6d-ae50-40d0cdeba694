<script setup>
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import FormGroup from '@shared/ui/FormGroup.vue'
import PriorityItem from '@shared/ui/PriorityItem.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, ref, toValue } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const simplifiedMode = useSimplifiedStore()

const {
  items,
  showItemNumber,
  error,
  commentEnabled,
  commentController,
} = toValue(props.question)

const {
  title: commentTitle,
  error: commentError,
  value: commentValue,
  maxLength,
  minLength,
  placeholderText,
} = commentController || {}

const el = ref(null)

function onDragStart() {
  document.body.style.cursor = 'grabbing'
  document.dispatchEvent(new CustomEvent('droppable-drag-start'))
}

function onDragEnd() {
  document.body.style.cursor = 'auto'
  document.dispatchEvent(new CustomEvent('droppable-drag-end'))
}

const priorityQuestionClasses = computed(() => ({
  'priority-question': true,
  'priority-question--simplified': simplifiedMode.isSimplifiedMode,
}))
</script>

<template>
  <div :class="priorityQuestionClasses">
    <FormGroup :error="error" error-view="filled" :error-attrs="{ style: { marginTop: '2px' } }">
      <VueDraggable
        ref="el"
        v-model="items"
        item-key="id"
        class="priority-question__items"
        :animation="300"
        easing="ease"
        :force-fallback="true"
        :fallback-on-body="true"
        @start="onDragStart"
        @end="onDragEnd"
      >
        <PriorityItem
          v-for="(item, index) in items"
          :key="item.id"
          :text="toValue(item.name)"
          :number="index + 1"
          :show-number="showItemNumber"
          :simplified="simplifiedMode.isSimplifiedMode"
          class="priority-question__item"
        />
      </VueDraggable>
    </FormGroup>
    <div v-if="toValue(commentEnabled)" class="priority-question__comment">
      <FormGroup
        custom-class="filials-question__comment-form-group"
        :label="commentTitle"
        :error="commentError"
      >
        <Textarea
          v-model="commentValue"
          :maxlength="maxLength"
          :minlength="minLength"
          :placeholder="placeholderText"
          :is-invalid="commentError"
          :required="false"
        />
      </FormGroup>
    </div>
  </div>
</template>

<style scoped>
.priority-question {
  font-family: var(--fqz-poll-font-family);
}

.priority-question__error {
  color: red;
  font-size: var(--fqz-poll-error-font-size);
  margin-top: 8px;
}

.priority-question__items {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.priority-question__item {
  width: 100%;
  cursor: grab;
}

.sortable-fallback {
  transition: box-shadow 0.3s;
  box-shadow: none !important;
}

.sortable-fallback > * {
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.5) !important;
}

.sortable-ghost {
  border: 2px dotted var(--fqz-poll-text-on-bg);
  border-radius: 8px;
  background-color: transparent;
  transition: opacity 0.15s ease-in-out;
}

:global(.priority-question--simplified .sortable-ghost) {
  border-color: var(--fqz-poll-text-on-place) !important;
  opacity: 0.2 !important ;
}

.sortable-ghost > * {
  opacity: 0;
  transition: opacity 0.15s ease-in-out;
}

.sortable-chosen {
  cursor: grabbing;
  user-select: none;
}

.sortable-drag {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  opacity: 1 !important;
  user-select: none;
}

.priority-question__comment {
  margin-top: 25px;
  background-color: var(--fqz-poll-main-place-color);
  color: var(--fqz-poll-text-on-place);
  border-radius: 8px;
  padding: 30px 50px;
}

.priority-question--simplified .priority-question__comment {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
  margin-top: 0;
}

@media (max-width: 679px) {
  .priority-question__comment {
    padding: 30px 15px;
    margin-top: 25px;
  }

  .priority-question--simplified .priority-question__comment {
    padding: 0;
    margin-top: 20px;
  }
}
</style>
