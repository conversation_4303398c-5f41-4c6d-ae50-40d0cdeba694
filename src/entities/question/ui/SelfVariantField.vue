<script setup>
import FormGroup from '@shared/ui/FormGroup.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'

defineProps({
  variantsController: {
    type: Object,
    required: true,
  },
  visible: {
    type: Boolean,
    required: true,
  },
})
</script>

<template>
  <SlideTransition>
    <FormGroup
      v-if="visible"
      custom-class="question-variants__self-variant-comment"
      :error="variantsController.selfVariantCommentError.value"
    >
      <Textarea
        v-model.trim="variantsController.selfVariantComment.value"
        min-height="70px"
        :placeholder="variantsController.selfVariantPlaceholderText.value"
        :minlength="variantsController.selfVariantMinLength.value"
        :maxlength="variantsController.selfVariantMaxLength.value"
        :is-invalid="variantsController.selfVariantCommentError.value"
      />
    </FormGroup>
  </SlideTransition>
</template>
