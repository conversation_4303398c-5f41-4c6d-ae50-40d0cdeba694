<script setup>
import FormGroup from '@shared/ui/FormGroup.vue'
import Select from '@shared/ui/Select/FcSelect.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import { computed, toValue } from 'vue'

const props = defineProps({
  nodes: {
    type: Array,
    required: true,
  },
  modelValue: {
    type: Array,
    default: null,
  },
  defaultValue: {
    type: Object,
    default: null,
  },
  error: {
    type: String,
    default: null,
  },
  depth: {
    type: Number,
    default: 0,
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'update:open'])

const selectedNode = computed({
  get() {
    const mv = toValue(props.modelValue)
    const item = mv?.[props.depth]
    return item
  },
  set(newValue) {
    const mv = toValue(props.modelValue)
    let modelValue = [...mv]

    modelValue[props.depth] = newValue

    // remove all items after current depth
    modelValue = modelValue.slice(0, props.depth + 1)

    emit('update:modelValue', modelValue)
  },
})

const options = computed(() => {
  return props.nodes.map(node => ({
    ...node,
    id: node.id,
    label: toValue(node.title),
    description: toValue(node.description),
    children: node.children,
    value: node.id,
    disabled: node.disabled,
    isCategory: node.isCategory,
  }))
})

const selectedNodeChildren = computed(() => {
  const children = selectedNode.value?.children || []
  if (selectedNode.value) {
    return children
  }
  return []
})

function getItemClass(option) {
  return option.isCategory ? 'classifier-select-tree__item--category' : ''
}

function getTriggerClass(option) {
  return option.isCategory ? 'classifier-select-tree__trigger--category' : ''
}
</script>

<template>
  <div class="classifier-select-tree">
    <FormGroup :error="selectedNode ? null : error">
      <Select
        v-model="selectedNode" :options="options" :searchable="true" :full-width="true"
        :invalid="!!error && !selectedNode"
        :item-class="getItemClass"
        :trigger-item-class="getTriggerClass"
        :tablet-view="props.tabletView"
        @update:open="$emit('update:open', $event)"
      />
      <SlideTransition>
        <div
          v-if="selectedNode && selectedNodeChildren.length > 0" :key="`${selectedNode.id}inner`"
          class="child-select" :data-key="`${selectedNode.id}inner`"
        >
          <div class="child-select-inner">
            <ClassifierSelectTree
              :model-value="modelValue" :nodes="selectedNodeChildren" :error="error"
              :depth="depth + 1"
              :tablet-view="props.tabletView"
              @update:model-value="$emit('update:modelValue', $event)"
              @update:open="$emit('update:open', $event)"
            />
          </div>
        </div>
      </SlideTransition>
    </FormGroup>
  </div>
</template>

<style scoped>
.child-select {
  padding-left: 15px;
}

.child-select-inner {
  padding-top: 15px;
}

:global(.classifier-select-tree__item--category .select-item__label-text),
:global(.classifier-select-tree__trigger--category .select-trigger__label-text) {
  font-weight: 700 !important;
}
</style>
