<script setup>
import MaskTypes from '@/shared/constants/maskTypes'
import DatePicker from '@/shared/ui/DatePicker/DatePicker.vue'
import FormGroup from '@/shared/ui/FormGroup.vue'
import Input from '@/shared/ui/Input.vue'
import MaskedField from '@/shared/ui/MaskedField.vue'
import FcSelect from '@/shared/ui/Select/FcSelect.vue'
import Textarea from '@/shared/ui/Textarea.vue'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import { computed, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const {
  id: questionId,
  maskedField,
  isMultiline,
  isRequired,
  months,
  maskType,
} = props.question

const {
  touched,
  error,
  nameConfig,
  surnameConfig,
  patronymicConfig,
  nameError,
  surnameError,
  patronymicError,
  fieldConfig,
  field,
  date,
  fromError,
  toError,
  dateError,
  from,
  fromText,
  to,
  toText,
  day,
  daysInMonth,
  dayError,
  selectedMonth,
  monthError,
  t,
} = maskedField

const placeholderText = computed(() => toValue(toValue(fieldConfig).placeholderText))
const maxLength = computed(() => toValue(fieldConfig).maxLength || 250)
const isSkipped = computed(() => props.question.skipped.value)

const textFieldContainerClasses = computed(() => {
  const classes = ['text-question-field-container']
  if (toValue(maskType) === MaskTypes.Site || toValue(maskType) === MaskTypes.Email) {
    classes.push('text-question-field-container--full-width')
  }
  return classes
})

const fromPeriodInvalid = computed(() => touched ? toValue(fromError) : null)
const toPeriodInvalid = computed(() => touched ? toValue(toError) : null)
const selectSearchPlaceholderText = computed(() => t('Поиск'))
const selectNothingFoundText = computed(() => t('Совпадений не найдено'))
const monthSelectPlaceholderText = computed(() => t('Месяц'))

const datePlaceholder = computed(() => toValue(placeholderText) || '00.00.0000')

const selectProps = computed(() => ({
  nothingFoundText: selectNothingFoundText.value,
  searchPlaceholderText: selectSearchPlaceholderText.value,
}))

const periodError = computed(() => toValue(error) || toValue(fromError) || toValue(toError))

const simplifiedStore = useSimplifiedStore()
const tabletStore = useTabletStore()
const datePickerAlign = computed(() => simplifiedStore.isSimplifiedMode ? 'center' : undefined)
const isTabletMode = computed(() => tabletStore.isTabletMode)

function resetSkip() {
  props.question.skipped.value = false
}
</script>

<template>
  <div>
    <div :class="{ 'text-question--skipped': isSkipped }">
      <FormGroup v-if="maskType === MaskTypes.NoMask" :error="error" class="form-group--plain-text">
        <Textarea
          v-if="isMultiline"
          v-model="field" :is-invalid="error" :placeholder="placeholderText" :maxlength="maxLength"
          data-testid="text-question-field"
          @focus="resetSkip"
        />
        <Input
          v-else v-model="field" :is-invalid="error" :placeholder="placeholderText"
          :input-attrs="{ 'maxlength': maxLength, 'data-testid': 'text-question-field' }"
          @focus="resetSkip"
        />
      </FormGroup>
      <div v-else-if="maskType === MaskTypes.Name" class="name-mask-container">
        <FormGroup v-if="surnameConfig.visible" :error="surnameError" :label="surnameConfig.label">
          <Input
            v-model="surnameConfig.value" :is-invalid="surnameError" :placeholder="surnameConfig.placeholderText"
            :input-attrs="{ 'maxlength': surnameConfig.maxLength, 'data-testid': 'input-surname' }"
            @focus="resetSkip"
          />
        </FormGroup>
        <FormGroup v-if="nameConfig.visible" :error="nameError" :label="nameConfig.label">
          <Input
            v-model="nameConfig.value" :is-invalid="nameError" :placeholder="nameConfig.placeholderText"
            :input-attrs="{ 'maxlength': nameConfig.maxLength, 'data-testid': 'input-name' }"
            @focus="resetSkip"
          />
        </FormGroup>
        <FormGroup v-if="patronymicConfig.visible" :error="patronymicError" :label="patronymicConfig.label">
          <Input
            v-model="patronymicConfig.value" :is-invalid="patronymicError" :placeholder="patronymicConfig.placeholderText"
            :input-attrs="{ 'maxlength': patronymicConfig.maxLength, 'data-testid': 'input-patronym' }"
            @focus="resetSkip"
          />
        </FormGroup>
      </div>
      <div v-else :class="textFieldContainerClasses">
        <FormGroup v-if="maskType === MaskTypes.Phone" :error="error">
          <MaskedField
            v-model="field" mask="phone" :is-invalid="error" :placeholder="placeholderText"
            :input-attrs="{ 'inputmode': 'tel', 'data-testid': 'phone-input' }"
            @focus="resetSkip"
          />
        </FormGroup>
        <FormGroup v-if="maskType === MaskTypes.Email" :error="error">
          <Input
            v-model="field" :is-invalid="error" :placeholder="placeholderText"
            :input-attrs="{ 'inputmode': 'email', 'data-testid': 'email-input' }"
            @focus="resetSkip"
          />
        </FormGroup>
        <FormGroup v-if="maskType === MaskTypes.Site" :error="error">
          <Input
            v-model="field" :is-invalid="error" :placeholder="placeholderText"
            :input-attrs="{ 'inputmode': 'url', 'data-testid': 'site-input' }"
            @focus="resetSkip"
          />
        </FormGroup>
        <FormGroup v-if="maskType === MaskTypes.Number" :error="error">
          <MaskedField
            v-model="field" mask="number" :is-invalid="error" :placeholder="placeholderText"
            :input-attrs="{ 'inputmode': 'numeric', 'data-testid': 'number-input', 'maxlength': maxLength }"
            @focus="resetSkip"
          />
        </FormGroup>
        <FormGroup v-if="maskType === MaskTypes.Date" :error="dateError" @click="resetSkip">
          <DatePicker
            v-model="date"
            v-model:text="field"
            :month-options="months"
            :invalid="dateError"
            :select-props="selectProps"
            :placeholder="datePlaceholder"
            :align="datePickerAlign"
            :tablet-view="isTabletMode"
            :input-attrs="{ 'id': questionId, 'name': `date-${questionId}`, 'data-testid': 'date-picker-input' }"
            @opened="resetSkip"
          />
        </FormGroup>
      </div>
      <FormGroup
        v-if="maskType === MaskTypes.Period" :error="!fromPeriodInvalid && !toPeriodInvalid && periodError"
        class="period-mask-container__form-group"
        @click="resetSkip"
      >
        <div class="period-mask-container">
          <FormGroup class="period-mask-container__from-form-group" :error="fromPeriodInvalid">
            <DatePicker
              v-model="from"
              v-model:text="fromText" :month-options="months" :invalid="!!periodError" :select-props="selectProps"
              :placeholder="datePlaceholder" :tablet-view="isTabletMode" :input-attrs="{ 'id': `date-from-${questionId}`, 'data-testid': `date-range-from`, 'name': `date-from-${questionId}` }"
              @opened="resetSkip"
            />
          </FormGroup>
          <span class="period-mask-container__separator"> - </span>
          <FormGroup class="period-mask-container__to-form-group" :error="toPeriodInvalid">
            <DatePicker
              v-model="to" v-model:text="toText" :month-options="months" :invalid="!!periodError"
              :select-props="selectProps" :placeholder="datePlaceholder" :tablet-view="isTabletMode"
              :input-attrs="{ 'id': `date-to-${questionId}`, 'data-testid': `date-range-to`, 'name': `date-to-${questionId}` }"
              @opened="resetSkip"
            />
          </FormGroup>
        </div>
      </FormGroup>
      <FormGroup
        v-if="maskType === MaskTypes.DateMonth" :error="error || dayError || monthError"
        class="date-month-mask-container__form-group"
        @click="resetSkip"
      >
        <div class="date-month-mask-container">
          <MaskedField
            v-model="day"
            mask="day" :max="daysInMonth" :is-invalid="dayError" placeholder="00" class="date-month-mask-container__day"
            :input-attrs="{ inputmode: 'numeric' }" data-testid="day-input"
            @focus="resetSkip"
          />
          <FcSelect
            v-model="selectedMonth"
            :options="months" searchable :search-placeholder-text="selectSearchPlaceholderText"
            :nothing-found-text="selectNothingFoundText" :placeholder="monthSelectPlaceholderText"
            :invalid="monthError" :clearable="!isRequired" :tablet-view="isTabletMode"
            data-testid="month-select"
          />
        </div>
      </FormGroup>
    </div>
    <div v-if="toValue(question.skip)" class="skip-container">
      <Check v-model="question.skipped.value" :label="toValue(question.skipText)" />
    </div>
  </div>
</template>

<style scoped>
.text-question-field-container {
  max-width: 220px;
  width: 100%;
  margin: auto;
  transition: opacity 0.3s;
}

.text-question-field-container--full-width {
  max-width: 100%;
}

.name-mask-container {
  display: flex;
  flex-direction: column;
  gap: 25px;
  width: 100%;
  margin: auto;
  transition: opacity 0.3s;
}

.period-mask-container {
  display: flex;
  width: 100%;
  transition: opacity 0.3s;
}

.period-mask-container__form-group {
  width: 100%;
  max-width: 460px;
  display: flex;
  flex-direction: column;
  margin: auto;
  transition: opacity 0.3s;
}

.period-mask-container__separator {
  font-size: 16px;
  flex-shrink: 0;
  flex-grow: 0;
  width: 20px;
  height: 45px;
  line-height: 44px;
  text-align: center;
  transition: opacity 0.3s;
}

.date-month-mask-container__form-group {
  display: flex;
  max-width: 293px;
  flex-direction: column;
  margin: auto;
  transition: opacity 0.3s;
}

.date-month-mask-container {
  display: flex;
  width: 100%;
  justify-content: center;
  gap: 15px;
  transition: opacity 0.3s;
}

.date-month-mask-container__day {
  width: 58px;
  flex-shrink: 0;
  flex-grow: 0;
  text-align: center;
  transition: opacity 0.3s;
}

.date-month-mask-container__day :deep(.fc-input__wrapper) {
  padding: 0;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.skip-container label {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.skip-container input[type='checkbox'] {
  margin-right: 8px;
}

.text-question--skipped {
  opacity: 0.7;
}

@media (max-width: 679px) {
  .date-month-mask-container {
    max-width: 100%;
  }

  .date-month-mask-container__form-group {
    max-width: 100%;
  }

  .date-month-mask-container :deep(.select-trigger) {
    max-width: none;
    flex-grow: 1;
  }
}
</style>
