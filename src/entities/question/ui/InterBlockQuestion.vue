<script setup>
import { usePollStore } from '@entities/poll/model/store'
import { usePointsStore } from '@features/points/store/pointsStore'
import PointsReportScreen from '@features/points/ui/PointsReportScreen.vue'
import { usePreviewStore } from '@shared/store/previewStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import SocialNetworks from '@shared/ui/SocialNetworks.vue'
import PageHeader from '@widgets/ui/PageHeader.vue'
import { computed, onMounted, onUnmounted, ref, toValue, watch } from 'vue'
import { setImageViewed } from '../api'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const pollStore = usePollStore()
const pointsStore = usePointsStore()
const showPointsScreen = ref(false)
const previewStore = usePreviewStore()
const simplifiedStore = useSimplifiedStore()

// Fetch points if enabled and on end screen
watch(() => props.question.isEndScreen, async (isEndScreen) => {
  if (isEndScreen && pointsStore.isEnabled) {
    try {
      await pointsStore.fetchPointsData(pollStore.authKey)
    }
    catch (error) {
      console.error('Failed to fetch points:', error)
    }
  }
}, { immediate: true })

function getImageStyle(image) {
  const definedHeight = image.height || image.original_height
  const definedWidth = image.width || image.original_width

  return {
    background: props.question.isFiveSecondTest ? null : props.question.imagesBackground.value,
    width: definedWidth ? `${definedWidth}px` : 'auto',
    height: definedHeight ? `${definedHeight}px` : 'auto',
  }
}

const textRef = ref(null)
const text = computed(() => toValue(props.question.text))
const questionId = computed(() => toValue(props.question.questionId))
const descriptionHtml = computed(() => toValue(props.question.description_html))
const unrequiredText = computed(() => !toValue(props.question.isRequired) ? toValue(pollStore.unrequiredText) : '')
const agreementText = computed(() => toValue(props.question.agreementText))
const isDefaultScreen = computed(() => toValue(props.question.questionId) === 'end-default')
const isImageViewed = props.question.isImageViewed
const isImageVisible = props.question.isImageVisible

const htmlRegex = /<[a-z][\s\S]*>/i

const agreementTextContainsHtml = computed(() => agreementText.value && htmlRegex.test(agreementText.value))

function handleUnsubscribe() {
  props.question.showUnsubscribeConfirmation()
}

async function handleConfirmUnsubscribe() {
  props.question.showUnsubscribeSuccess()
  if (previewStore.isPreviewMode) {
    Promise.resolve()
  }
  if (pollStore.hasContact) {
    try {
      await props.question.unsubscribe(pollStore.authKey)
    }
    catch (err) {
      console.error('Failed to unsubscribe', err)
    }
  }
}

function handleStaySubscribed() {
  props.question.hideUnsubscribeConfirmation()
}

function handleStartOver() {
  // refresh the page
  window.location.reload()
}

function handleFinish() {
  simplifiedStore.closeWidget()
}

function getImageClass(image) {
  const classes = {
    'interscreen-image': true,
    'interscreen-image--no-size': !image.width || !image.height,
    'interscreen-image--no-height': !image.height,
    'interscreen-image--no-width': !image.width,
    'interscreen-image--external': image.isExternal,
  }
  return classes
}

const shouldShowUnsubscribeButton = computed(() => {
  const showUnsubscribe = props.question.showUnsubscribe.value

  // @NOTE: В режиме ревью показываем кнопку отписки, чтобы можно было проверить тексты
  if (showUnsubscribe && previewStore.isPreviewMode) {
    return true
  }

  return props.question.screenType.value !== 1 && showUnsubscribe && pollStore.hasContact
})

onMounted(() => {
  props.question.mounted.value = true
  // Add event listener for ESC key
  window.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && isImageVisible.value) {
      e.preventDefault()
      e.stopPropagation()
    }
  })
})

onUnmounted(() => {
  props.question.mounted.value = false
  // Remove event listener
  window.removeEventListener('keydown', (e) => {
    if (e.key === 'Escape' && isImageVisible.value) {
      e.preventDefault()
      e.stopPropagation()
    }
  })
})

function getImageSrc(image) {
  const isExternalLogo = image.external_logo && image.external_logo.includes('http')
  return isExternalLogo ? image.external_logo : (image.url || image.logo)
}

const report = computed(() => {
  return toValue(pointsStore.formattedResultsData)
})

async function handleShowPoints() {
  if (previewStore.isPreviewMode) {
    return
  }

  showPointsScreen.value = true
  try {
    await pointsStore.fetchResultsData(pollStore.authKey)
  }
  catch (error) {
    console.error('Failed to fetch results:', error)
  }
}

const showBackButton = computed(() => {
  return pollStore.allowEditAfterDone
    && props.question.isEndScreen
    && !previewStore.isPreviewMode
    && !pollStore.hasQuoteRestrictedQuestions
})

function handleBack() {
  pollStore.goPrev()
}

const shouldShowCloseWidgetButton = computed(() => {
  return props.question.isEndScreen && props.question.showFinish.value && simplifiedStore.isWidgetMode
})

const showButtonContainer = computed(() => {
  if (!props.question.isStartScreen && !props.question.isEndScreen) {
    return false
  }

  return showBackButton.value
    || (props.question.isEndScreen && props.question.showPoints.value)
    || props.question.isStartScreen
    || (props.question.isEndScreen && props.question.showReady.value)
    || (props.question.isEndScreen && props.question.showStartOver.value)
    || shouldShowCloseWidgetButton.value
})

function handlePrint() {
  const authKey = pollStore.authKey
  if (!authKey)
    return

  const printUrl = new URL(window.location.href)
  printUrl.searchParams.set('print-report', '1')
  printUrl.searchParams.set('auth-key', authKey)

  window.open(printUrl.toString(), '_blank')
}

const surveyInterscreenClasses = computed(() => {
  const classes = {
    'survey-interscreen': true,
    'survey-interscreen--default': isDefaultScreen.value,
    'survey-interscreen--start-screen': props.question.isStartScreen,
    'survey-interscreen--end-screen': props.question.isEndScreen,
    'survey-interscreen--simplified': simplifiedStore.isSimplifiedMode,
    'survey-interscreen--in-iframe': simplifiedStore.isInIframe,
  }
  return classes
})

async function sendImageViewedData() {
  try {
    await setImageViewed(pollStore.authKey, questionId.value)
  }
  catch (error) {
    console.error('Failed to send image viewed data:', error)
  }
}

function showImage() {
  isImageVisible.value = true
  if (!previewStore.isPreviewMode) {
    sendImageViewedData()
  }
  if (props.question.imageShowTime.value) {
    setTimeout(() => {
      isImageVisible.value = false
      props.question.isImageViewed.value = true
      props.question.validate()
    }, props.question.imageShowTime.value * 1000)
  }
}
</script>

<template>
  <div :class="surveyInterscreenClasses">
    <Transition name="fade-up" mode="out-in">
      <div
        v-if="!question.showUnsubscribeScreen.value
          && !showPointsScreen
          && !question.showUnsubscribeSuccessScreen.value"
        key="content"
        class="survey-interscreen__wrapper"
        :class="{
          'survey-interscreen__wrapper--has-images': question.images.value?.length > 0,
          'five-second-test--with-bg': question.showBgInstruction.value,
          'five-second-test': question.isFiveSecondTest,
        }"
      >
        <template v-if="question.isFiveSecondTest">
          <PageHeader
            :description-html="descriptionHtml"
            :unrequired-text="unrequiredText"
            class="text"
            :class="{
              'five-second-test--viewed': isImageViewed,
            }"
          />
          <p v-if="isImageViewed" class="five-second-test__viewed-text">
            {{ toValue(question.texts.imageAlreadyShown) }}
          </p>
          <div v-if="isImageVisible" class="five-second-test__fullscreen-image" @click.stop>
            <div v-if="question.images.value && question.images.value.length" class="interscreen-images">
              <component
                :is="toValue(image.link) ? 'a' : 'div'"
                v-for="image in question.images.value"
                :key="image.id"
                :class="getImageClass(image)"
                :style="getImageStyle(image)"
                :href="toValue(image.link)"
                target="_blank"
              >
                <div class="interscreen-image__wrapper">
                  <img :src="getImageSrc(image)" :alt="toValue(image.description)">
                </div>
              </component>
            </div>
          </div>
          <FormGroup
            v-if="!isImageVisible && !isImageViewed"
            :error="question.error.value"
            :error-attrs="{ class: `five-second-test--error${question.showBgInstruction.value ? '_bg' : ''}` }"
          >
            <div class="five-second-test__button-container">
              <button
                class="btn survey-interscreen__start-button "
                type="button"
                @click="showImage"
              >
                {{ question.showImageButtonText.value || 'Показать изображение' }}
              </button>
            </div>
          </FormGroup>
        </template>

        <template v-else>
          <div v-if="text" ref="textRef" class="text" v-html="text" />
          <div v-if="question.customHtml" class="text" v-html="question.customHtml" />

          <div v-if="question.agreement.value" class="survey-interscreen__agreement">
            <Check
              :id="`agreement-${question.id}`" v-model="question.agreementValue.value"
              :label="!agreementTextContainsHtml ? agreementText : ''"
              :class="agreementTextContainsHtml ? 'survey-interscreen__agreement-html-check' : ''"
            >
              <div v-if="agreementTextContainsHtml" @click.stop v-html="agreementText" />
            </Check>
          </div>

          <div v-if="question.images.value && question.images.value.length" class="interscreen-images">
            <component
              :is="toValue(image.link) ? 'a' : 'div'" v-for="image in question.images.value" :key="image.id"
              :class="getImageClass(image)" :style="getImageStyle(image)" :href="toValue(image.link)" target="_blank"
            >
              <div class="interscreen-image__wrapper">
                <img :src="getImageSrc(image)" :alt="toValue(image.description)">
              </div>
            </component>
          </div>
          <div
            v-if="showButtonContainer"
            class="survey-interscreen__button-container"
          >
            <button
              v-if="showBackButton"
              type="button"
              class="btn survey-interscreen__back-button"
              @click.prevent="handleBack"
            >
              {{ pollStore.backButtonText }}
            </button>
            <button
              v-if="question.isEndScreen && question.showPoints.value"
              class="btn survey-interscreen__points-button" type="button" @click.prevent="handleShowPoints"
            >
              {{ question.texts.points }}
            </button>
            <button
              v-if="question.isStartScreen" class="btn survey-interscreen__start-button"
              :disabled="question.blocked.value" type="button" @click="pollStore.goNext"
            >
              {{ toValue(question.texts.takeSurvey) }}
            </button>
            <a
              v-if="question.isEndScreen && question.showReady.value"
              class="btn survey-interscreen__ready-button" :href="question.readyLink.value || '#'" target="_blank"
            >
              {{ question.texts.ready }}
            </a>
            <button
              v-if="question.isEndScreen && question.showStartOver.value"
              class="btn survey-interscreen__start-over-button" type="button" @click="handleStartOver"
            >
              {{ question.texts.startOver }}
            </button>
            <button
              v-if="shouldShowCloseWidgetButton"
              class="btn survey-interscreen__finish-button" type="button" @click="handleFinish"
            >
              {{ question.texts.finish }}
            </button>
          </div>

          <div
            v-if="shouldShowUnsubscribeButton"
            class="survey-start__additional-actions d-flex justify-content-center"
          >
            <button class="btn btn-survey-white" type="button" @click.prevent="handleUnsubscribe">
              {{ question.texts.unsubscribe }}
            </button>
          </div>
          <div
            v-if="question.socialNetworksEnabled.value && !pollStore.hasContact"
            class="survey-start__additional-actions-social"
          >
            <SocialNetworks :share="question.socialNetworks.value" />
          </div>
        </template>
      </div>

      <div v-else-if="question.showUnsubscribeScreen.value" key="unsubscribe" class="survey-confirmation-interscreen">
        <div class="survey-confirmation-interscreen__text">
          {{ question.texts.unsubscribeConfirm }}
        </div>
        <div class="survey-confirmation-interscreen__buttons">
          <button
            class="btn survey-confirmation-interscreen__button survey-confirmation-interscreen__button--unsubscribe"
            type="button" @click="handleConfirmUnsubscribe"
          >
            {{ question.texts.unsubscribe }}
          </button>
          <button
            class="btn btn-survey-white survey-confirmation-interscreen__button survey-confirmation-interscreen__button--stay-subscribed"
            type="button" @click="handleStaySubscribed"
          >
            {{ question.texts.staySubscribed }}
          </button>
        </div>
      </div>

      <div v-else-if="showPointsScreen" key="points" class="survey-points-report-interscreen">
        <PointsReportScreen
          :results="report"
          :loading="pointsStore.isGetResultsLoading"
          :back-button-text="pollStore.backButtonText"
          @back="showPointsScreen = false"
          @print="handlePrint"
        />
      </div>
      <div v-else-if="question.showUnsubscribeSuccessScreen.value" key="unsubscribe-success" class="survey-unsubscribe-success-interscreen">
        <div class="survey-unsubscribe-success-interscreen__text">
          {{ question.texts.unsubscribeSuccess }}
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.five-second-test--viewed {
  opacity: 0.2;
}
.survey-interscreen {
  width: 100%;
  color: var(--fqz-poll-text-on-bg);
  font-size: inherit;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

.survey-interscreen__wrapper,
.survey-confirmation-interscreen {
  max-width: 750px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding: 25px 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.survey-confirmation-interscreen {
  text-align: center;
  gap: 0;
}

.survey-points-report-interscreen {
  flex-grow: 1;
  padding-top: 25px;
  width: 100%;
}

.survey-confirmation-interscreen__text,
.survey-unsubscribe-success-interscreen__text {
  font-size: var(--fqz-poll-font-size);
}

.survey-confirmation-interscreen__buttons {
  display: flex;
  flex-direction: column;
  margin-top: 40px;
}

.survey-confirmation-interscreen__button--unsubscribe {
  font-weight: 700 !important;
  height: 48px;
  padding-left: 25px !important;
  padding-right: 25px !important;
  min-width: 0;
}

.survey-confirmation-interscreen__button--stay-subscribed {
  margin-top: 30px;
  font-size: var(--fqz-poll-font-size);
}

.survey-interscreen__agreement {
  font: inherit;
  color: var(--fqz-poll-text-on-place);
  background: var(--fqz-poll-main-place-color);
  padding: 30px 50px;
  border-radius: 8px;
  width: 100%;
}

:global(.five-second-test--error_bg) {
  margin-top: 30px !important;
}

:global(.five-second-test--error) {
  padding: 10px 15px;
  background-color: #f96261;
  margin: 30px 0 0 0 !important;
  color: #fff !important;
  border-radius: 8px;
}

:global(.survey-interscreen__agreement-html-check .fc-check__label) {
  padding-top: 0 !important;
}

:global(.survey-interscreen__agreement-html-check label) {
  align-items: center;
}

:global(.survey-interscreen__agreement-html-check .fc-check__label a) {
  color: var(--fqz-poll-text-on-place) !important;
}

:global(.survey-interscreen__agreement-html-check .fc-check__label a:hover),
:global(.survey-interscreen__agreement-html-check .fc-check__label a:focus),
:global(.survey-interscreen__agreement-html-check .fc-check__label a:active) {
  color: var(--fqz-poll-text-on-place) !important;
}

.text {
  text-align: left;
  width: 100%;
  /* font-size: var(--fqz-poll-header-font-size); */
}

/* .text > :deep(p > span:not(.inter-block-promocode)) {
  font-size: var(--fqz-poll-title-font-size);
} */

.text :deep(a) {
  color: var(--fqz-poll-link-color);
}

.interscreen-images {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: -7.5px;
  margin-bottom: -7.5px;
}

.interscreen-image {
  margin: 7.5px;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  display: inline-flex;
}

.interscreen-image__wrapper img {
  display: block;
  width: 100%;
  height: 100%;
}

.interscreen-image--no-height .interscreen-image__wrapper img {
  max-height: 100%;
  height: auto;
}

.interscreen-image--no-width .interscreen-image__wrapper img {
  max-width: 100%;
  width: auto;
}

.survey-start__additional-actions {
  text-align: center;
  display: flex;
  align-items: center;
  gap: 20px;
}

.survey-interscreen__buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  gap: 10px;
}

.survey-interscreen__buttons .btn:not(:first-child) {
  margin-left: 10px;
}

.btn {
  padding: 10px 20px 10px;
  border-radius: 5px;
  cursor: pointer;
  background-color: var(--fqz-poll-start-button-background-color);
  color: var(--fqz-poll-start-button-text-color);
  border-radius: var(--fqz-poll-start-button-radius);
  border: 2px solid var(--fqz-poll-start-button-stroke-color);
  font-size: 16px;
  line-height: 1.1;
  font-weight: 500;
  align-self: center;
  font-family: inherit;
  transition: opacity 0.3s;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-survey-white {
  background-color: transparent;
  color: var(--fqz-poll-link-color);
  text-decoration: underline;
  padding: 0;
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
  border: none;
}

.btn:not(:disabled):hover {
  opacity: 0.8;
}

.survey-interscreen__button-container {
  text-align: center;
  gap: 10px;
  display: flex;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 10px;
}

.survey-interscreen__start-button,
.survey-interscreen__start-over-button,
.survey-interscreen__ready-button,
.survey-interscreen__points-button,
.survey-interscreen__back-button,
.survey-interscreen__finish-button {
  padding-left: 25px;
  padding-right: 25px;
  white-space: nowrap;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: none !important;
}

.survey-interscreen__points-button,
.survey-interscreen__back-button {
  color: var(--fqz-poll-back-button-text-color) !important;
  border: 2px solid var(--fqz-poll-back-button-stroke-color) !important;
  background: var(--fqz-poll-back-button-background-color) !important;
  border-radius: var(--fqz-poll-back-button-radius) !important;
}

.fade-up-enter-active,
.fade-up-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}

.fade-up-enter-from {
  opacity: 1;
  transform: translateY(30px);
}

.fade-up-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.fade-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

.survey-unsubscribe-success-interscreen {
  text-align: center;
  gap: 30px;
}

.survey-start__additional-actions-social {
  margin-bottom: -3px;
}

:global(.text .copy-promocode-container:last-of-type),
:global(.text .copy-promocode-container:last-of-type + p:empty) {
  margin-bottom: 0;
}

.survey-interscreen__additional-actions {
  margin-top: 20px;
}

:global(.inter-block-promocode) {
  word-break: break-all;
}

.survey-interscreen--simplified {
  color: var(--fqz-poll-text-on-place);
  padding-top: 0;
}

.survey-interscreen--simplified .survey-interscreen__wrapper {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
}

.survey-interscreen--simplified .survey-interscreen__wrapper .survey-interscreen__agreement {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  background: transparent;
}

.survey-interscreen--simplified .survey-interscreen__button-container {
  margin-top: 0;
}

.survey-interscreen--in-iframe.survey-interscreen--default {
  padding-top: 20px !important;
  padding-bottom: 12px !important;
}

.survey-interscreen--simplified.survey-interscreen--start-screen,
.survey-interscreen--simplified.survey-interscreen--end-screen {
  padding-bottom: 0;
}

.survey-interscreen--in-iframe .survey-points-report-interscreen {
  padding-top: 0;
  padding-bottom: 0;
}

.survey-interscreen--in-iframe :deep(.points-report-screen) {
  padding-bottom: 0;
}

.five-second-test {
  width: 100%;
  max-width: 780px;
  color: var(--fqz-poll-text-on-bg);
  border-radius: 8px;
  padding: 40px 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  @media screen and (max-width: 679px) {
    padding: 0;
  }
}

.five-second-test.survey-interscreen__wrapper {
  padding: 40px 50px;
  gap: 30px !important;
}

.five-second-test--with-bg {
  background-color: var(--fqz-poll-main-place-color);
  color: var(--fqz-poll-text-on-place);
}
.survey-interscreen--simplified .five-second-test {
  color: var(--fqz-poll-text-on-place);
}

.five-second-test__instruction {
  text-align: center;
  font-size: var(--fqz-poll-title-font-size);
  color: var(--fqz-poll-text-on-bg);

  @media screen and (max-width: 679px) {
    font-size: 14px !important;
    line-height: 1.2;
  }
}

.five-second-test--with-bg .five-second-test__instruction {
  color: var(--fqz-poll-text-on-place);
}

.five-second-test__instruction--viewed {
  opacity: 0.5;
}

.five-second-test__fullscreen-image {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: 50px 15px;
}

.five-second-test__fullscreen-image .interscreen-images {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 0;
  margin-bottom: 0;
  height: 100%;
}

.five-second-test__fullscreen-image .interscreen-image {
  height: 100% !important;
  margin: 0;
}

.five-second-test__fullscreen-image .interscreen-image img {
  height: 100%;
  object-fit: contain;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.five-second-test__button-container {
  text-align: center;
}

.five-second-test__unrequired-text {
  color: var(--poll-text-on-bg);
  font-size: 14px;
  line-height: 1.2;
  margin-top: 18px;
}

.five-second-test__viewed-text {
  font-family: var(--fqz-poll-font-family);
  color: var(--fqz-poll-text-on-bg);
  font-size: var(--fqz-poll-font-size);
  margin: 0;
  text-align: center;

  @media screen and (max-width: 679px) {
    font-size: 14px;
    line-height: 1.2;
  }
}

.five-second-test--with-bg .five-second-test__viewed-text {
  color: var(--fqz-poll-text-on-place);
}
.survey-interscreen--simplified .five-second-test__viewed-text {
  color: var(--fqz-poll-text-on-place);
}

.survey-interscreen--simplified .five-second-test.survey-interscreen__wrapper {
  padding: 0 !important;
}

.text.page-header {
  padding: 0;
}

.survey-interscreen--simplified .five-second-test__fullscreen-image {
  padding: 0;
}

@media screen and (max-width: 679px) {
  :global(.five-second-test--error) {
    margin: 25px -15px 0 -15px !important;
  }

  .survey-interscreen__wrapper {
    padding: 30px 20px;
    gap: 25px;
  }

  .five-second-test.survey-interscreen__wrapper {
    padding: 40px 15px;
  }

  .survey-interscreen__agreement {
    padding: 29px 15px;
    margin-left: -20px;
    margin-right: -20px;
    width: calc(100% + 40px);
  }

  :global(.survey-interscreen__agreement .fc-check__label) {
    padding-top: 0 !important;
  }

  :global(.survey-interscreen__agreement label) {
    align-items: center;
  }

  .survey-interscreen__buttons {
    flex-direction: column;
  }

  .survey-interscreen__button-container {
    align-items: center;
    justify-content: center;
    margin-top: 0;
  }

  .survey-interscreen__start-button,
  .survey-interscreen__start-over-button,
  .survey-interscreen__ready-button,
  .survey-interscreen__points-button,
  .survey-interscreen__back-button,
  .survey-interscreen__finish-button {
    height: 36px;
    padding-left: 25px;
    padding-right: 25px;
  }

  .survey-interscreen__buttons .btn:not(:first-child) {
    margin-left: 0;
    margin-top: 18px;
  }

  .survey-interscreen .text :deep(*) {
    font-size: 14px !important;
  }

  .survey-confirmation-interscreen__text,
  .survey-unsubscribe-success-interscreen__text {
    font-size: 14px;
    line-height: 1.1;
  }

  .btn-survey-white {
    font-size: 13px;
    line-height: 1.1;
    font-weight: 400;
  }

  .survey-interscreen--simplified .survey-interscreen__wrapper {
    padding-top: 0;
  }

  .survey-interscreen--simplified .survey-interscreen__wrapper .survey-interscreen__agreement {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
    margin-right: 0;
    width: 100%;
  }

  .survey-interscreen--in-iframe.survey-interscreen--default {
    padding-top: 0px !important;
    padding-bottom: 18px !important;
  }

  .survey-interscreen--in-iframe.survey-interscreen--start-screen,
  .survey-interscreen--in-iframe.survey-interscreen--end-screen {
    padding-bottom: 0;
  }

  .survey-interscreen--in-iframe :deep(.points-report-screen) {
    padding-top: 0;
    padding-bottom: 0;
  }
}
</style>
