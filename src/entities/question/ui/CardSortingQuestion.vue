<script setup>
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import PriorityItem from '@shared/ui/PriorityItem.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, nextTick, ref, toValue } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const {
  items,
  itemsCategories,
  enableGallery,
  galleryController,
  colNameCards,
  colNameCategories,
  error,
  success,
  skip,
  skipped,
  skipText,
  commentEnabled,
  commentController,
} = toValue(props.question)

const {
  title: commentTitle,
  error: commentError,
  value: commentValue,
  maxLength,
  minLength,
  placeholderText,
} = commentController || {}

const elCards = ref(null)
const draged = ref(false)

function onDragStart() {
  skipped.value = false

  document.body.style.cursor = 'grabbing'
  draged.value = true
  elCards.value?.resume()
  document.dispatchEvent(new CustomEvent('droppable-drag-start'))
}

async function onDragEnd() {
  document.body.style.cursor = 'auto'
  draged.value = false
  await nextTick()
  if (toValue(success)) {
    elCards.value?.pause()
  }
  else {
    elCards.value?.resume()
  }
  document.dispatchEvent(new CustomEvent('droppable-drag-end'))
}

const simplifiedMode = useSimplifiedStore()

const cardSortingQuestionClasses = computed(() => ({
  'card-sorting-question': true,
  'card-sorting-question--simplified': simplifiedMode.isSimplifiedMode,
}))
</script>

<template>
  <div :class="cardSortingQuestionClasses">
    <div
      v-if="toValue(enableGallery)"
      class="gallery-container card-sorting-question__comment"
    >
      <Gallery
        :gallery="galleryController.gallery.value"
        :selectable="false"
        type="default"
        :group-id="`${question.questionId}-${question.type}`"
      />
    </div>
    <div
      class="card-sorting-question__grid"
      :class="{ 'card-sorting-question__grid_skipped': skipped }"
    >
      <div
        class="card-sorting-question__cards card-sorting-question__col"
        :class="[
          { 'card-sorting-question__col_draged': draged },
        ]"
      >
        <div
          v-if="colNameCards"
          class="card-sorting-question__header"
        >
          {{ colNameCards }}
        </div>
        <FormGroup
          :error="error"
          :success="success"
          error-view="filled"
          success-view="filled"
          :error-attrs="{ style: { marginTop: '2px' } }"
          :success-attrs="{ style: { marginTop: '2px' } }"
        >
          <VueDraggable
            ref="elCards"
            v-model="items"
            item-key="id"
            group="categories"
            class="card-sorting-question__items"
            :class="{ 'card-sorting-question__items_success': success }"
            :animation="300"
            easing="ease"
            :force-fallback="true"
            :fallback-on-body="true"
            @start="onDragStart"
            @end="onDragEnd"
          >
            <PriorityItem
              v-for="(card, index) in items"
              :key="card.id"
              :text="toValue(card.name)"
              :number="index + 1"
              :simplified="simplifiedMode.isSimplifiedMode"
              :card-sorting="true"
              :full="true"
              class="card-sorting-question__item"
            />
          </VueDraggable>
        </FormGroup>
      </div>
      <div
        class="card-sorting-question__categories card-sorting-question__col"
        :class="[
          { 'card-sorting-question__col_draged': draged },
        ]"
      >
        <div
          v-if="colNameCategories"
          class="card-sorting-question__header"
        >
          {{ colNameCategories }}
        </div>
        <div
          v-for="category in itemsCategories"
          :key="category.id"
          class="card-sorting-question__category"
        >
          <div class="card-sorting-question__category-header">
            {{ category.name }}
          </div>
          <VueDraggable
            v-model="category.cards"
            item-key="id"
            group="categories"
            class="card-sorting-question__items"
            :animation="300"
            easing="ease"
            :force-fallback="true"
            :fallback-on-body="true"
            @start="onDragStart"
            @end="onDragEnd"
          >
            <PriorityItem
              v-for="(card, index) in category.cards"
              :key="card.id"
              :text="toValue(card.name)"
              :number="index + 1"
              :card-sorting="true"
              :simplified="simplifiedMode.isSimplifiedMode"
              :full="true"
              class="card-sorting-question__item"
            />
          </VueDraggable>
        </div>
      </div>
    </div>
    <div v-if="commentEnabled || skip" class="card-sorting-question__comment">
      <SlideTransition>
        <div v-if="commentEnabled && !skipped">
          <FormGroup
            custom-class="card-sorting-question__comment-form-group"
            :label="commentTitle"
            :error="commentError"
          >
            <Textarea
              v-model="commentValue"
              :maxlength="maxLength"
              :minlength="minLength"
              :placeholder="placeholderText"
              :is-invalid="commentError"
              :required="false"
            />
          </FormGroup>
        </div>
      </SlideTransition>
      <div v-if="skip" class="skip-container">
        <Check v-model="skipped" :label="toValue(skipText)" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.gallery-container {
  margin-bottom: 30px;
}

.card-sorting-question {
  font-family: var(--fqz-poll-font-family);
}

.card-sorting-question__header {
  margin-bottom: 15px;
  margin-left: 10px;
  font-weight: 700;
}

.card-sorting-question__comment-form-group {
  padding-bottom: 30px;
}

.card-sorting-question__category-header {
  margin-bottom: 10px;
  margin-left: 10px;
}

.card-sorting-question__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 767px) {
  .card-sorting-question__grid {
    gap: 10px;
  }
}

.card-sorting-question__grid_skipped {
  opacity: 0.5;
}

.card-sorting-question__error {
  color: red;
  font-size: var(--fqz-poll-error-font-size);
  margin-top: 8px;
}

.card-sorting-question__items {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-height: 49px;
}

.card-sorting-question__items_success {
  opacity: 0.5;
}

.card-sorting-question__col .card-sorting-question__items {
  position: relative;
}

.card-sorting-question__col .card-sorting-question__items::before {
  content: '';
  pointer-events: none;
  position: absolute;
  inset: 0;
  border: 2px dotted var(--fqz-poll-text-on-bg);
  border-radius: 8px;
  background-color: transparent;
  opacity: 0;
  transition: opacity 0.15s ease-in-out;
}

.card-sorting-question--simplified .card-sorting-question__col .card-sorting-question__items::before {
  border-color: var(--fqz-poll-text-on-place) !important;
}

.card-sorting-question__col .card-sorting-question__items:empty::before {
  opacity: 1;
}

.card-sorting-question--simplified .card-sorting-question__col .card-sorting-question__items:empty::before {
  opacity: 0.25;
}

.card-sorting-question__col_draged .card-sorting-question__items:empty::before {
  opacity: 0.25;
}

.card-sorting-question--simplified .card-sorting-question__col_draged .card-sorting-question__items:empty::before {
  opacity: 0.1;
}

.card-sorting-question__category + .card-sorting-question__category {
  margin-top: 15px;
}

.card-sorting-question__item {
  width: 100%;
  cursor: grab;
}

.sortable-fallback {
  transition: box-shadow 0.3s;
  box-shadow: none !important;
}

.sortable-fallback > * {
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.5) !important;
}

.sortable-ghost {
  border: 2px dotted var(--fqz-poll-text-on-bg);
  border-radius: 8px;
  background-color: transparent;
  transition: opacity 0.15s ease-in-out;
}

:global(.card-sorting-question--simplified .sortable-ghost) {
  border-color: var(--fqz-poll-text-on-place) !important;
  opacity: 0.2 !important ;
}

.sortable-ghost > * {
  opacity: 0;
  transition: opacity 0.15s ease-in-out;
}

.sortable-chosen {
  cursor: grabbing;
  user-select: none;
}

.sortable-drag {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  opacity: 1 !important;
  user-select: none;
}

.card-sorting-question__comment {
  margin-top: 25px;
  background-color: var(--fqz-poll-main-place-color);
  color: var(--fqz-poll-text-on-place);
  border-radius: 8px;
  padding: 30px 50px;
}

@media (max-width: 767px) {
  .card-sorting-question__comment {
    padding: 30px 15px;
    margin-top: 25px;
  }
}
</style>
