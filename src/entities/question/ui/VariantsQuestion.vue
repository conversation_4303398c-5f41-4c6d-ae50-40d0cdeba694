<script setup>
import { useTabletStore } from '@shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, onMounted, toValue } from 'vue'
import Variants from './Variants.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const tabletStore = useTabletStore()
const tabletView = computed(() => tabletStore.isTabletMode)

onMounted(() => {
  if (tabletView.value) {
    props.question.isDropdown = false
    props.question.dropdown.value = false

    if (props.question.variantsController?.dropdown) {
      props.question.variantsController.dropdown.value = false
    }
  }
})

const showComment = computed(() => toValue(props.question.hasComment) && !toValue(props.question.skipped))

function onChange(_event) {
  if (toValue(props.question.skipped)) {
    props.question.skipped.value = false
  }
}
const questionId = computed(() => toValue(props.question.questionId))

const variantsWithCommentRequired = computed(() => toValue(props.question.variantsWithCommentRequired))
const commentRequiredForAllVariants = computed(() => toValue(props.question.commentRequiredForAllVariants))

const shouldShowCommentRequiredError = computed(() =>
  variantsWithCommentRequired.value.length > 0
  && !toValue(props.question.commentController.value)
  && !commentRequiredForAllVariants.value)
</script>

<template>
  <div>
    <form :id="`answer-form-${toValue(question.id)}`">
      <div class="survey-questions__wrapper_pre">
        <div v-if="toValue(question.enableGallery)" class="gallery-container">
          <Gallery
            :gallery="question.galleryController.gallery.value"
            :selectable="false"
            :inactive="toValue(question.skipped)"
            :group-id="`${toValue(question.questionId)}-${question.type}-gallery`"
            type="default"
          />
        </div>

        <Variants
          :variants-controller="question.variantsController"
          :group-id="`${questionId}-${question.type}`"
          :tablet-view="tabletView"
          :has-comment="question.hasComment"
          @change="onChange"
        />
        <SlideTransition>
          <div v-if="showComment">
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="question.commentController.title.value"
              :error="toValue(question.commentRequiredError) || question.commentController.error.value"
            >
              <Textarea
                v-model="question.commentController.value.value"
                :maxlength="toValue(question.commentController.maxLength)"
                :minlength="toValue(question.commentController.minLength)"
                :placeholder="question.commentController.placeholderText.value"
                :is-invalid="toValue(question.commentRequiredError) || question.commentController.error.value"
              />
              <template #error-text-after>
                <div v-if="shouldShowCommentRequiredError && variantsWithCommentRequired.length > 0">
                  <div
                    v-for="variant in variantsWithCommentRequired"
                    :key="toValue(variant.id)"
                    class="comment-required-variants-error-item"
                  >
                    {{ toValue(variant.label) || toValue(variant.alternativeLabel) }}
                  </div>
                </div>
              </template>
            </FormGroup>
          </div>
        </SlideTransition>

        <div v-if="toValue(question.skip)" class="skip-container">
          <Check v-model="question.skipped.value" :label="toValue(question.skipText)" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.survey-questions__comment-form-group {
  padding-top: 30px;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.skip-container label {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.skip-container input[type='checkbox'] {
  margin-right: 8px;
}

.comment-required-variants-error-item {
  font-size: 12px;
  line-height: 1.3;
  margin-top: 5px;
  color: var(--fqz-poll-text-on-place, black);
  opacity: 0.6;
}

.gallery-container {
  margin-top: -10px;
  margin-bottom: 30px;
}
</style>
