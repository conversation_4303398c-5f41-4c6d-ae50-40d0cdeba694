<script setup>
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import NpsRating from '@shared/ui/NpsRating.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, isRef, toValue } from 'vue'
import Variants from './Variants.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const {
  id,
  isVariants,
  variants,
  npsStartLabel,
  npsEndLabel,
  fromOne,
  npsDesign,
  skipped,
  err,
  rating,
  commentEnabled,
  commentController,
  skip,
  skipText,
} = toValue(props.question)

const simplifiedStore = useSimplifiedStore()
const tabletStore = useTabletStore()

const tabletView = computed(() => tabletStore.isTabletMode)

const { title, error, value, maxLength, minLength, placeholderText } = commentController

const showLabels = computed(() => true)
const showNumbers = computed(() => true)
const colors = computed(() => toValue(props.question.colors))
const isSkipped = computed(() => toValue(skipped))

const hasLastSelectedVariant = computed(() => {
  if (isVariants.value && variants.value.length > 0) {
    const lastVariant = variants.value[variants.value.length - 1]
    return lastVariant.rating.value !== null && lastVariant.rating.value !== -1
  }

  if (!isRef(rating)) {
    return false
  }

  return rating.value !== null && rating.value !== -1
})

const npsRatingContainerClasses = computed(() => ({
  'nps-rating-container': true,
  'nps-rating-container--simplified': simplifiedStore.isSimplifiedMode,
  'nps-rating-container--has-selected-variant': hasLastSelectedVariant.value,
}))
</script>

<template>
  <div>
    <form :id="`answer-form-${id}`">
      <div class="survey-questions__wrapper_pre">
        <div v-if="question.hasGallery" class="gallery-container">
          <Gallery
            :gallery="question.galleryController.gallery.value"
            :selectable="false"
            :inactive="skipped"
            type="default"
            :group-id="question.questionId"
          />
        </div>

        <div v-if="isVariants" :class="npsRatingContainerClasses">
          <div v-for="variant in variants" :key="variant.id" class="nps-rating-item">
            <FormGroup :error="variant.error.value" :error-attrs="{ class: 'question-nps-rating-error' }">
              <p class="nps-rating-item__label" :class="{ 'nps-rating-item__label--skipped': isSkipped || variant.skipped }">
                {{ variant.text.value }}
              </p>
              <NpsRating
                v-model="variant.rating.value"
                :max="10"
                :colors="colors"
                :show-labels="showLabels"
                :show-numbers="showNumbers"
                :start-label="npsStartLabel"
                :end-label="npsEndLabel"
                :from-one="fromOne"
                :design="npsDesign"
                :inactive="skipped"
                data-testid="rating-nps"
              />
              <SlideTransition>
                <div v-if="variant.variantsController.enabled.value && !skipped" class="survey-questions__variants-form-group-container">
                  <Variants
                    :variants-controller="variant.variantsController"
                    media-view="gallery"
                    custom-class="survey-questions__variants-form-group"
                    :tablet-view="tabletView"
                  />
                </div>
              </SlideTransition>
            </FormGroup>
          </div>
        </div>
        <div v-else :class="npsRatingContainerClasses">
          <FormGroup :error="err" :error-attrs="{ class: 'question-nps-rating-error' }">
            <NpsRating
              v-model="rating"
              :max="10"
              :colors="colors"
              :show-labels="showLabels"
              :show-numbers="showNumbers"
              :start-label="npsStartLabel"
              :end-label="npsEndLabel"
              :from-one="fromOne"
              :design="npsDesign"
              :inactive="skipped"
              data-testid="rating-nps"
            />
          </FormGroup>
        </div>

        <SlideTransition>
          <div v-if="commentEnabled && !skipped">
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="title"
              :error="error"
              data-testid="comment"
            >
              <Textarea
                v-model="value"
                :maxlength="maxLength"
                :minlength="minLength"
                :placeholder="placeholderText"
                :is-invalid="error"
              />
            </FormGroup>
          </div>
        </SlideTransition>
        <SlideTransition>
          <div v-if="question.variantsController.enabled.value && !skipped" class="survey-questions__variants-form-group-container">
            <Variants
              :variants-controller="question.variantsController"
              media-view="gallery"
              custom-class="survey-questions__variants-form-group"
              :tablet-view="tabletView"
            />
          </div>
        </SlideTransition>

        <div v-if="skip" class="skip-container">
          <Check v-model="skipped" :label="skipText" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.nps-rating-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.nps-rating__message-container {
  margin-top: 20px;
  text-align: center;
  position: relative;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

:deep(.question-nps-rating-error) {
  margin-top: 20px;
  text-align: center;
}

.nps-rating__message-container:empty {
  display: none;
}

@media screen and (min-width: 768px) {
  .survey--tablet .survey-questions__variants {
    font-size: 22px;
  }
}

.nps-rating-item {
  padding-bottom: 20px;
  width: 100%;
  position: relative;
}

.nps-rating-item:last-of-type {
  padding-bottom: 0;
}

.nps-rating-item:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
}

.nps-rating-item:last-child::before {
  display: none;
}

.nps-rating-item__label {
  margin-bottom: 20px;
  text-align: center;
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
  overflow: hidden;
  transition: opacity 0.3s;
}

.nps-rating-item__label--skipped {
  opacity: 0.7;
}

.nps-rating-container--simplified.nps-rating-container--has-selected-variant {
  padding-bottom: 5px;
}

.survey-questions__variants-form-group {
  padding-top: 20px;
}

.survey-questions__variants-form-group-container {
  width: 100%;
}

@media screen and (max-width: 679px) {
  .nps-rating-item__label {
    font-size: 14px;
  }

  .nps-rating-container--simplified.nps-rating-container--has-selected-variant {
    padding-bottom: 8px;
  }
}
</style>
