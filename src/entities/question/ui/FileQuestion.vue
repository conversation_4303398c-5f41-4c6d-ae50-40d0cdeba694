<script setup>
import { ALLOWED_BUT_NOT_SUPPORTED, ALLOWED_FILE_TYPES } from '@/shared/constants/files'
import { usePollStore } from '@entities/poll/model/store'
import { useTranslationsStore } from '@shared/store/translationsStore'
import FileButton from '@shared/ui/FileButton.vue'
import FileDropPlaceholder from '@shared/ui/FileDropPlaceholder.vue'
import FilePreview from '@shared/ui/FilePreview.vue'
import FormError from '@shared/ui/FormError.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Textarea from '@shared/ui/Textarea.vue'
import FadeTransition from '@shared/ui/transitions/FadeTransition.vue'
import { useDropZone } from '@vueuse/core'
import { computed, ref, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const store = usePollStore()
const translationsStore = useTranslationsStore()
const t = translationsStore.t

const dropZoneRef = ref(null)
const fileInput = ref(null)

const imageMimeTypes = [...ALLOWED_FILE_TYPES.image, ...ALLOWED_BUT_NOT_SUPPORTED.image].map(name => `image/${name}`)
const videoMimeTypes = ALLOWED_FILE_TYPES.video.map(name => `video/${name}`)
const audioMimeTypes = [...ALLOWED_FILE_TYPES.audio, ...ALLOWED_BUT_NOT_SUPPORTED.audio].map(name => `audio/${name}`)

const imageExtensions = [...ALLOWED_FILE_TYPES.image, ...ALLOWED_BUT_NOT_SUPPORTED.image].map(name => `.${name}`)
const videoExtensions = ALLOWED_FILE_TYPES.video.map(name => `.${name}`)
const audioExtensions = [...ALLOWED_FILE_TYPES.audio, ...ALLOWED_BUT_NOT_SUPPORTED.audio].map(name => `.${name}`)

const fileTypes = computed(() => {
  if (props.question.fileTypes.value) {
    return props.question.fileTypes.value
  }
  return ['image', 'video', 'audio']
})

const allowedMimeTypes = computed(() => {
  const formats = []
  if (fileTypes.value.includes('image')) {
    formats.push(...imageMimeTypes)
  }
  if (fileTypes.value.includes('video')) {
    formats.push(...videoMimeTypes)
  }
  if (fileTypes.value.includes('audio')) {
    formats.push(...audioMimeTypes)
  }
  return formats
})

const allowedExtensions = computed(() => {
  const formats = []
  if (fileTypes.value.includes('image')) {
    formats.push(...imageExtensions)
  }
  if (fileTypes.value.includes('video')) {
    formats.push(...videoExtensions)
  }
  if (fileTypes.value.includes('audio')) {
    formats.push(...audioExtensions)
  }
  return formats
})

function onDrop(files) {
  if (files) {
    props.question.uploadFiles({ authKey: store.authKey, files })
  }
}

const uploadDisabled = computed(() => {
  return props.question.files.value.length >= props.question.filesLength.value
})

const { isOverDropZone } = useDropZone(dropZoneRef, {
  onDrop,
  dataTypes: allowedMimeTypes.value,
})

const allowedFormatsText = computed(() => {
  const formats = []
  if (fileTypes.value.includes('image')) {
    formats.push('фото')
  }
  if (fileTypes.value.includes('video')) {
    formats.push('видео')
  }
  if (fileTypes.value.includes('audio')) {
    formats.push('аудио')
  }
  return t(`Загрузить ${formats.join(', ')}`)
})

function handleFileInputChange(event) {
  if (uploadDisabled.value) {
    return
  }

  const files = Array.from(event.target.files)
  props.question.uploadFiles({ authKey: store.authKey, files })
  fileInput.value.value = '' // Reset file input
}

function openFileDialog() {
  if (uploadDisabled.value) {
    return
  }

  fileInput.value.click()
}

function removeFile(index) {
  props.question.removeFile(store.authKey, index)
}

const error = computed(() => {
  return props.question.error.value
})
const questionId = computed(() => {
  return props.question.questionId
})
const errorKind = computed(() => {
  return props.question.errorKind.value
})

const dropPlaceholderTitle = computed(() => {
  let text = toValue(t('Перетащите файлы сюда, чтобы загрузить их'))

  if (!text.endsWith('.')) {
    text += '.'
  }
  return text
})

const dropPlaceholderDescription = computed(() => {
  return toValue(t('Размер файла не должен превышать {size} Мб', { size: props.question.filesizeLimit }))
})
</script>

<template>
  <div>
    <div ref="dropZoneRef" class="file-dropzone" :class="{ 'file-dropzone--active': isOverDropZone }">
      <div class="file-previews">
        <TransitionGroup
          name="file-list"
          tag="div"
          class="file-dropzone__list"
        >
          <FilePreview
            v-for="(file, index) in question.files.value" :id="file.id" :key="file.name"
            :file="file"
            :is-uploading="file.isUploading"
            :name="file.name"
            :type="file.type"
            :group="questionId"
            :link="file.link"
            :original-name="file.originalName"
            :preview-url="file.picture"
            :full-url="file.link"
            @remove="removeFile(index)"
          />
          <FileButton
            key="file-button"
            :text="allowedFormatsText"
            :allowed-formats="allowedMimeTypes"
            :disabled="uploadDisabled"
            :invalid="question.error.value"
            @click="openFileDialog"
          />
          <FormError :key="errorKind" :error="error" class="file-dropzone__error" :class="{ 'file-dropzone__error--too-large': errorKind === 'too-large' }" />
        </TransitionGroup>
        <input
          ref="fileInput"
          type="file"
          :accept="allowedExtensions.join(',')"
          multiple
          class="file-input"
          @change="handleFileInputChange"
        >
      </div>
      <FadeTransition>
        <div v-if="isOverDropZone" class="file-dropzone__overlay">
          <FileDropPlaceholder :title="dropPlaceholderTitle" :description="dropPlaceholderDescription" />
        </div>
      </FadeTransition>
    </div>

    <FormGroup
      v-if="question.commentController.enabled.value" custom-class="survey-questions__comment-form-group"
      :label="question.commentController.title.value" :error="question.commentController.error.value"
    >
      <Textarea
        v-model="question.commentController.value.value"
        :maxlength="question.commentController.maxLength.value"
        :minlength="question.commentController.minLength.value"
        :placeholder="question.commentController.placeholderText.value"
        :is-invalid="question.commentController.error.value"
      />
    </FormGroup>
  </div>
</template>

<style scoped>
.file-dropzone {
  transition: background-color 0.3s;
  position: relative;
}

.file-dropzone--active {
  background-color: #f0f0f0;
}

.file-dropzone__overlay {
  position: absolute;
  top: -25px;
  left: -35px;
  width: calc(100% + 70px);
  height: calc(100% + 50px);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.file-input {
  display: none;
}

.file-previews {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.file-dropzone__list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px 20px;
  transition: transform 0.3s;
}

.survey-questions__comment-form-group {
  margin-top: 30px;
}

.file-dropzone__form-group {
  margin-bottom: 0;
}

:deep(.file-dropzone__error) {
  text-align: left;
  transition:
    margin-top 0.3s,
    transform 0.3s;
  width: 100%;
}

.file-dropzone__error--too-large {
  margin-top: 30px !important;
}

.file-list-enter-active,
.file-list-leave-active,
.file-list-move {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.file-list-leave-active {
  position: absolute;
}

.file-list-leave-active.file-dropzone__error {
  bottom: 0;
}

.file-dropzone__error.file-list-leave-from {
  transform: translateY(0);
  opacity: 1;
}

.file-dropzone__error.file-list-leave-to {
  opacity: 0;
  transform: translateY(4px);
}

.file-dropzone__error--file-too-large.file-list-leave-to {
  transform: translateY(16px);
}

.file-list-enter-from,
.file-list-leave-to {
  opacity: 0;
  transform: translateY(4px);
}

@media (max-width: 679px) {
  .file-dropzone__overlay {
    left: -5px;
    width: calc(100% + 10px);
  }
}
</style>
