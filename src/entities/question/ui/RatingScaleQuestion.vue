<script setup>
import Check from '@shared/ui/Check.vue'
import FormError from '@shared/ui/FormError.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import RatingScale from '@shared/ui/RatingScale.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, toValue } from 'vue'
import Variants from './Variants.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const {
  id,
  stars,
  count,
  color,
  showLabels,
  showNumbers,
  labels,
  err,
  skipped,
  hasComment,
  commentController,
  isClarifyingQuestionVisible,
  variantsController,
  skip,
  skipText,
} = props.question

const {
  title: commentTitle,
  error: commentError,
  value: commentValue,
  maxLength: commentMaxLength,
  minLength: commentMinLength,
  placeholderText: commentPlaceholderText,
} = commentController

const activeLabel = computed(() => {
  const currentStars = toValue(stars)
  if (currentStars) {
    return toValue(labels)[currentStars - 1]
  }
  return ''
})
</script>

<template>
  <div>
    <form :id="`answer-form-${id}`">
      <div class="survey-questions__wrapper_pre px-2">
        <div v-if="question.hasGallery" class="gallery-container">
          <Gallery
            :gallery="question.galleryController.gallery.value"
            :selectable="false"
            :inactive="skipped"
            :group-id="question.questionId"
            type="default"
          />
        </div>

        <div class="rating-scale-container">
          <RatingScale
            v-model="stars"
            :max="count"
            :color="color"
            :show-labels="showLabels"
            :show-numbers="showNumbers"
            :labels="labels"
            :error="err"
            :inactive="skipped"
          />
        </div>

        <TransitionGroup
          name="fade-up"
          class="rating-scale__message-container"
          tag="div"
        >
          <FormError v-if="err" :key="1" :error="err" class="question-rating-error" />
          <div v-if="activeLabel && !showLabels" :key="2" class="rating-scale__message">
            {{ activeLabel }}
          </div>
        </TransitionGroup>

        <SlideTransition>
          <div v-if="hasComment && !skipped" data-testid="comment">
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="commentTitle"
              :error="commentError"
            >
              <Textarea
                v-model="commentValue"
                :maxlength="commentMaxLength"
                :minlength="commentMinLength"
                :placeholder="commentPlaceholderText"
                :is-invalid="commentError"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <SlideTransition>
          <div v-if="isClarifyingQuestionVisible">
            <Variants
              :variants-controller="variantsController"
              :group-id="`${question.questionId}-variants`"
              custom-class="survey-questions__variants-form-group"
            />
          </div>
        </SlideTransition>

        <div v-if="skip" class="skip-container">
          <Check v-model="skipped" :label="skipText" data-testid="skip-checkbox" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.rating-scale-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  padding-top: 1px;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.survey-questions__variants {
  padding-top: 1rem;
}

.rating-scale__message-container {
  margin-top: 20px;
  text-align: center;
  position: relative;
}

.survey-questions__variants-form-group {
  padding-top: 30px;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.rating-scale__message-container:empty {
  display: none;
}

:global(.rating-scale__message-container .fade-up-leave-active) {
  position: absolute !important;
  width: 100% !important;
  left: 0 !important;
}

.rating-scale__message {
  font-size: var(--fqz-poll-font-size, 13px);
  font-weight: 700;
  line-height: 1.1;
  overflow: hidden;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

@media screen and (max-width: 679px) {
  .survey--tablet .survey-questions__variants {
    font-size: 22px;
  }
  .rating-scale__message {
    font-size: 14px;
  }
}
</style>
