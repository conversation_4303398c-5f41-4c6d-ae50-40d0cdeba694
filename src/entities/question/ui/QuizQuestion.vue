<script setup>
import MaskTypes from '@/shared/constants/maskTypes'
import DatePicker from '@/shared/ui/DatePicker/DatePicker.vue'
import FormGroup from '@/shared/ui/FormGroup.vue'
import Input from '@/shared/ui/Input.vue'
import Masked<PERSON>ield from '@/shared/ui/MaskedField.vue'
import FcSelect from '@/shared/ui/Select/FcSelect.vue'
import Textarea from '@/shared/ui/Textarea.vue'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import { computed, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const selectSearchPlaceholderText = computed(() => toValue(props.question.t('Поиск')))
const selectNothingFoundText = computed(() => toValue(props.question.t('Совпадений не найдено')))
const monthSelectPlaceholderText = computed(() => toValue(props.question.t('Месяц')))

const selectProps = computed(() => ({
  nothingFoundText: selectNothingFoundText.value,
  searchPlaceholderText: selectSearchPlaceholderText.value,
}))

const months = computed(() => toValue(props.question.months))

const simplifiedStore = useSimplifiedStore()
const isSimplified = computed(() => simplifiedStore.isEnabled)
const tabletStore = useTabletStore()
const isTabletMode = computed(() => tabletStore.isTabletMode)
const datePickerAlign = computed(() => isSimplified.value ? 'center' : undefined)
</script>

<template>
  <div class="quiz-question-fields">
    <div v-for="field in question.maskedFields.value" :key="field.id" class="quiz-question-fields__field">
      <FormGroup v-if="field.type.value === MaskTypes.NoMask" :error="field.error.value" :label="toValue(field.label)" :required="field.fieldConfig?.value?.required">
        <Textarea
          v-if="field.fieldConfig.value.multiline"
          v-model="field.field.value"
          :is-invalid="field.error.value"
          :placeholder="toValue(field.fieldConfig.value.placeholderText)"
          :maxlength="field.fieldConfig.value.maxLength"
          data-testid="quiz-question-nomask-field"
        />
        <Input
          v-else
          v-model="field.field.value"
          :is-invalid="field.error.value"
          :placeholder="toValue(field.fieldConfig.value.placeholderText)"
          :input-attrs="{ 'maxlength': field.fieldConfig.value.maxLength, 'data-testid': `quiz-question-nomask-field` }"
        />
      </FormGroup>

      <div v-else-if="field.type.value === MaskTypes.Name" class="name-mask-container">
        <FormGroup
          v-if="field.surnameConfig.value.visible"
          :error="field.surnameError.value"
          :label="field.surnameConfig.value.label"
          :required="field.surnameConfig.value.required"
        >
          <Input
            v-model="field.surnameConfig.value.value"
            :is-invalid="field.surnameError.value"
            :placeholder="toValue(field.surnameConfig.value.placeholderText)"
            :input-attrs="{ 'maxlength': field.surnameConfig.value.maxLength, 'data-testid': `quiz-question-surname-field` }"
          />
        </FormGroup>
        <FormGroup
          v-if="field.nameConfig.value.visible"
          :error="field.nameError.value"
          :label="field.nameConfig.value.label"
          :required="field.nameConfig.value.required"
        >
          <Input
            v-model="field.nameConfig.value.value"
            :is-invalid="field.nameError.value"
            :placeholder="toValue(field.nameConfig.value.placeholderText)"
            :input-attrs="{ 'maxlength': field.nameConfig.value.maxLength, 'data-testid': `quiz-question-name-field` }"
          />
        </FormGroup>
        <FormGroup
          v-if="field.patronymicConfig.value.visible"
          :error="field.patronymicError.value"
          :label="field.patronymicConfig.value.label"
          :required="field.patronymicConfig.value.required"
        >
          <Input
            v-model="field.patronymicConfig.value.value"
            :is-invalid="field.patronymicError.value"
            :placeholder="toValue(field.patronymicConfig.value.placeholderText)"
            :input-attrs="{ 'maxlength': field.patronymicConfig.value.maxLength, 'data-testid': `quiz-question-patronymic-field` }"
          />
        </FormGroup>
      </div>

      <FormGroup v-else-if="field.type.value === MaskTypes.Phone" :error="field.error.value" :label="toValue(field.label)" :required="field.fieldConfig?.value?.required" class="quiz-question-fields__field--half-width">
        <MaskedField
          v-model="field.field.value"
          mask="phone"
          :is-invalid="field.error.value"
          :placeholder="toValue(field.fieldConfig.value.placeholderText)"
          :input-attrs="{ 'inputmode': 'tel', 'data-testid': `quiz-question-phone-field` }"
        />
      </FormGroup>

      <FormGroup v-else-if="field.type.value === MaskTypes.Email" :error="field.error.value" :label="toValue(field.label)" :required="field.fieldConfig?.value?.required">
        <Input
          v-model="field.field.value"
          :is-invalid="field.error.value"
          :placeholder="toValue(field.fieldConfig.value.placeholderText)"
          :input-attrs="{ 'inputmode': 'email', 'maxlength': field.fieldConfig.value.maxLength, 'data-testid': `quiz-question-email-field` }"
        />
      </FormGroup>

      <FormGroup v-else-if="field.type.value === MaskTypes.Site" :error="field.error.value" :label="toValue(field.label)" :required="field.fieldConfig?.value?.required">
        <Input
          v-model="field.field.value"
          :is-invalid="field.error.value"
          :placeholder="toValue(field.fieldConfig.value.placeholderText)"
          :input-attrs="{ 'inputmode': 'url', 'maxlength': field.fieldConfig.value.maxLength, 'data-testid': `quiz-question-site-field` }"
        />
      </FormGroup>

      <FormGroup
        v-else-if="field.type.value === MaskTypes.Number"
        :error="field.error.value"
        :label="toValue(field.label)"
        class="quiz-question-fields__field--half-width"
        :required="field.fieldConfig?.value?.required"
      >
        <MaskedField
          v-model="field.field.value"
          mask="number"
          :is-invalid="field.error.value"
          :placeholder="toValue(field.fieldConfig.value.placeholderText)"
          :input-attrs="{ 'inputmode': 'numeric', 'maxlength': field.fieldConfig.value.maxLength, 'data-testid': `quiz-question-number-field` }"
        />
      </FormGroup>

      <FormGroup
        v-else-if="field.type.value === MaskTypes.Date"
        :error="field.dateError.value"
        :label="toValue(field.label)"
        class="quiz-question-fields__field--half-width"
        :required="field.fieldConfig?.value?.required"
      >
        <DatePicker
          v-model="field.date.value"
          v-model:text="field.field.value"
          :month-options="months"
          :invalid="field.dateError.value"
          :placeholder="toValue(field.fieldConfig.value.placeholderText)"
          :select-props="selectProps"
          :align="datePickerAlign"
          :input-attrs="{ 'data-testid': `quiz-question-date-field` }"
          :tablet-view="isTabletMode"
        />
      </FormGroup>

      <FormGroup
        v-else-if="field.type.value === MaskTypes.Period"
        :error="!field.fromError.value && !field.toError.value ? field.error.value : null"
        :label="toValue(field.label)"
        class="period-mask-container__form-group"
        :required="field.fieldConfig?.value?.required"
      >
        <div class="period-mask-container">
          <FormGroup :error="field.fromError.value">
            <DatePicker
              v-model="field.from.value"
              v-model:text="field.fromText.value"
              :month-options="months"
              :placeholder="toValue(field.fieldConfig.value.placeholderText)"
              :invalid="!!field.fromError.value || !!field.error.value"
              :select-props="selectProps"
              :input-attrs="{ 'data-testid': `quiz-question-date-from-field` }"
              :tablet-view="isTabletMode"
            />
          </FormGroup>
          <span class="period-mask-container__separator"> - </span>
          <FormGroup :error="field.toError.value">
            <DatePicker
              v-model="field.to.value"
              v-model:text="field.toText.value"
              :placeholder="toValue(field.fieldConfig.value.placeholderText)"
              :month-options="months"
              :invalid="!!field.toError.value || !!field.error.value"
              :select-props="selectProps"
              :input-attrs="{ 'data-testid': `quiz-question-date-to-field` }"
              :tablet-view="isTabletMode"
            />
          </FormGroup>
        </div>
      </FormGroup>

      <FormGroup
        v-else-if="field.type.value === MaskTypes.DateMonth"
        :error="field.error.value || field.dayError.value || field.monthError.value"
        class="date-month-mask-container__form-group"
        :label="toValue(field.label)"
        :required="field.fieldConfig?.value?.required"
      >
        <div class="date-month-mask-container">
          <MaskedField
            v-model="field.day.value"
            mask="day"
            :max="field.daysInMonth"
            :is-invalid="field.dayError.value"
            placeholder="00"
            class="date-month-mask-container__day"
            :input-attrs="{ 'inputmode': 'numeric', 'data-testid': `quiz-question-date-day-field` }"
          />
          <FcSelect
            v-model="field.selectedMonth.value"
            :options="months"
            searchable
            :search-placeholder-text="selectSearchPlaceholderText"
            :nothing-found-text="selectNothingFoundText"
            :placeholder="monthSelectPlaceholderText"
            :invalid="field.monthError.value"
            :clearable="!field.fieldConfig?.value?.required"
            :tablet-view="isTabletMode"
          />
        </div>
      </FormGroup>
    </div>
  </div>
</template>

<style scoped>
.quiz-question-fields {
  display: flex;
  flex-direction: column;
  gap: 25px;
  width: 100%;
  margin: auto;
}

.quiz-question-fields__field {
  width: 100%;
}

.quiz-question-fields__field--half-width {
  max-width: 280px;
}

.name-mask-container {
  display: flex;
  flex-direction: column;
  gap: 25px;
  width: 100%;
  margin: auto;
}

.period-mask-container {
  display: flex;
  width: 100%;
}

.period-mask-container__form-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: auto;
}

.period-mask-container__separator {
  font-size: 16px;
  flex-shrink: 0;
  flex-grow: 0;
  width: 20px;
  height: 45px;
  line-height: 44px;
  text-align: center;
}

.date-month-mask-container__form-group {
  display: flex;
  max-width: 280px;
  flex-direction: column;
}

.date-month-mask-container {
  display: flex;
  width: 100%;
  justify-content: center;
  gap: 15px;
}

.date-month-mask-container__day {
  width: 58px;
  flex-shrink: 0;
  flex-grow: 0;
  text-align: center;
}

.date-month-mask-container__day :deep(.fc-input__wrapper) {
  padding: 0;
}

@media (max-width: 679px) {
  .quiz-question-fields__field--half-width {
    max-width: 100%;
  }
  .date-month-mask-container {
    max-width: none;
    width: 100%;
  }
  .date-month-mask-container__form-group {
    max-width: none;
    width: 100%;
  }
  .date-month-mask-container :deep(.select-trigger) {
    flex-grow: 1;
    max-width: none;
  }
}
</style>
