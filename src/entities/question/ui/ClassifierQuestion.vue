<script setup>
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import Select from '@shared/ui/Select/FcSelect.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import SlideTransitionGroup from '@shared/ui/SlideTransitionGroup.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, toValue } from 'vue'
import ClassifierSelectTree from './ClassifierSelectTree.vue'
import ClassifierTree from './ClassifierTree.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const translationsStore = useTranslationsStore()
const t = translationsStore.t
const tabletStore = useTabletStore()
const tabletView = computed(() => tabletStore.isTabletMode)

const isSkipped = computed(() => toValue(props.question.skipped))
const error = computed(() => toValue(props.question.error))

function toggleNode(node) {
  props.question.toggleNode(node)
}

function toggleOpenNode(node) {
  props.question.toggleOpenNode(node)
}

const classifierQuestionClasses = computed(() => ({
  'classifier-question--skipped': isSkipped.value,
  'classifier-question--tree': toValue(!props.question.isListType),
  'classifier-question--list': toValue(props.question.isListType),
  'classifier-question--multiple': toValue(props.question.isMultipleChoice),
  'classifier-question--single': toValue(!props.question.isMultipleChoice),
  'classifier-question--dropdown': toValue(props.question.isDropdown),
}))

const maxChooseVariants = computed(() => toValue(props.question.maxChooseVariants))
const selectedTrees = computed(() => toValue(props.question.selectedTrees))
const tooltipCloseText = computed(() => toValue(props.question.t('Закрыть')))

function onDropdownOpen(isOpen) {
  if (isOpen) {
    props.question.skipped.value = false
  }
}

const isAddButtonDisabled = computed(() => {
  const reachedMaxVariants = selectedTrees.value.length >= maxChooseVariants.value
  const lastElemItems = selectedTrees.value[selectedTrees.value.length - 1].selectedItems
  const hasLeafItems = lastElemItems.some(item => !item.children || item.children.length === 0)
  return reachedMaxVariants || !hasLeafItems
})

const getSelectTreeListAddButtonClass = computed(() => ({
  'classifier-select-tree-list__button': true,
  'classifier-select-tree-list__button--add': true,
  'classifier-select-tree-list__button--disabled': isAddButtonDisabled.value,
}))

function onAddTreeNode() {
  if (!isAddButtonDisabled.value) {
    props.question.skipped.value = false
    props.question.addTreeNode()
  }
}

function onRemoveTreeNode(index) {
  props.question.skipped.value = false
  props.question.removeTreeNode(index)
}

function onUpdateTreeSelectedItems(index, selected) {
  props.question.selectedTrees.value[index].selectedItems = toValue(selected)
}
</script>

<template>
  <div>
    <form :id="`answer-form-${question.id}`">
      <div
        class="survey-questions__wrapper_pre"
        :class="{ 'survey-questions__wrapper_pre--dropdown': toValue(question.isDropdown) }"
      >
        <div v-if="toValue(question.enableGallery)" class="gallery-container">
          <Gallery
            :gallery="toValue(question.galleryController.gallery)"
            :selectable="false"
            :inactive="isSkipped"
            type="default"
            :group-id="`${question.questionId}-${question.type}`"
          />
        </div>

        <FormGroup :error="error" :error-attrs="{ class: 'classifier-question__error' }">
          <div
            class="classifier-question"
            :class="classifierQuestionClasses"
          >
            <div
              v-if="toValue(question.isDropdown) && toValue(question.isTreeType)"
              class="classifier-select-tree-list"
            >
              <template v-if="toValue(question.isMultipleChoice)">
                <SlideTransitionGroup>
                  <div v-for="(tree, index) in selectedTrees" :key="index" class="classifier-select-tree-list__item">
                    <div class="classifier-select-tree-list__item-content">
                      <ClassifierSelectTree
                        :nodes="tree.nodes"
                        :error="tree.error"
                        :model-value="tree.computedSelectedItems"
                        :tablet-view="tabletView"
                        @update:open="onDropdownOpen"
                        @update:model-value="onUpdateTreeSelectedItems(index, $event)"
                      />
                    </div>
                    <div class="classifier-select-tree-list__buttons">
                      <button
                        v-if="index === selectedTrees.length - 1"
                        type="button"
                        :class="getSelectTreeListAddButtonClass"
                        @click="onAddTreeNode"
                      >
                        <span class="classifier-select-tree-list__button-icon">
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 1V11M1 6H11" stroke="#00C968" stroke-width="2" stroke-linecap="round" />
                          </svg>
                        </span>
                        <span class="classifier-select-tree-list__button-text">
                          {{ t('Добавить вариант') }}
                        </span>
                      </button>
                      <button v-if="selectedTrees?.length > 1" type="button" class="classifier-select-tree-list__button classifier-select-tree-list__button--remove" @click="onRemoveTreeNode(index)">
                        <span class="classifier-select-tree-list__button-icon">
                          <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 1L9 9M9 1L1 9" stroke="#F96261" stroke-width="2" stroke-linecap="round" />
                          </svg>
                        </span>
                        <span class="classifier-select-tree-list__button-text">
                          {{ t('Удалить') }}
                        </span>
                      </button>
                    </div>
                  </div>
                </SlideTransitionGroup>
              </template>
              <template v-else>
                <ClassifierSelectTree
                  v-model="selectedTrees[0].selectedItems"
                  :nodes="selectedTrees[0].nodes"
                  :root="true"
                  :error="selectedTrees[0].error"
                  :tablet-view="tabletView"
                  @update:open="onDropdownOpen"
                />
              </template>
            </div>
            <template v-else-if="toValue(question.isDropdown) && toValue(question.isListType)">
              <Select
                v-model="question.selectedDropdownListValue.value"
                :options="question.selectListTypeOptions.value"
                :searchable="true"
                :multiple="toValue(question.isMultipleChoice)"
                :invalid="error"
                :full-width="true"
                :tablet-view="tabletView"
                @update:open="onDropdownOpen"
              />
            </template>
            <template v-else>
              <ClassifierTree
                v-for="item in question.items.value"
                :key="item.id"
                :node="item"
                :multiple="toValue(question.isMultipleChoice)"
                :is-selectable-categories="toValue(question.isSelectableCategories)"
                :disable-select-category="toValue(question.disableSelectCategory)"
                :is-list-type="toValue(question.isListType)"
                :show-tooltips="toValue(question.showTooltips)"
                :tooltip-close-text="tooltipCloseText"
                @toggle="toggleNode"
                @toggle-open="toggleOpenNode"
              />
            </template>
          </div>
        </FormGroup>

        <SlideTransition>
          <div v-if="toValue(question.commentEnabled) && !toValue(question.skipped)">
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="toValue(question.commentController.title)"
              :error="toValue(question.commentController.error)"
            >
              <Textarea
                v-model="question.commentController.value.value"
                :maxlength="toValue(question.commentController.maxLength)"
                :minlength="toValue(question.commentController.minLength)"
                :placeholder="toValue(question.commentController.placeholderText)"
                :is-invalid="toValue(question.commentController.error)"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <div v-if="toValue(question.skip)" class="skip-container">
          <Check v-model="question.skipped.value" :label="toValue(question.skipText)" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.skip-container {
  margin-top: 30px;
  text-align: center;
}

.classifier-select-tree-list__item-content {
  display: flex;
  flex-direction: column;
  padding-top: 30px;
}

.classifier-select-tree-list__item:first-child .classifier-select-tree-list__item-content {
  padding-top: 0;
}

.classifier-question {
  opacity: 1;
  transition: opacity 0.3s;
}

.classifier-question--skipped {
  opacity: 0.7;
}

:deep(.classifier-question__error) {
  margin-top: 20px;
}

.survey-questions__wrapper_pre--dropdown :deep(.classifier-question__error) {
  margin-top: 10px;
}

.classifier-select-tree-list {
  display: flex;
  flex-direction: column;
}

.classifier-select-tree-list__buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  gap: 10px;
}

.classifier-select-tree-list__button {
  all: unset;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  align-self: flex-start;
  padding: 0 15px;
  border-radius: var(--fqz-poll-next-button-radius, 18px);
  cursor: pointer;
  height: 36px;
  line-height: 1.3;
  color: #000;
  font-size: 12px;
  transition: opacity 0.3s;
  gap: 10px;
}

.classifier-select-tree-list__button:hover {
  opacity: 0.8;
}

.classifier-select-tree-list__button--disabled {
  opacity: 0.5 !important;
  cursor: not-allowed;
}

.classifier-select-tree-list__button-icon {
  margin-top: -1px;
}

.classifier-select-tree-list__button-icon svg {
  display: block;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}
</style>
