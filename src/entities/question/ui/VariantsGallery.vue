<script setup>
import { isVideo } from '@shared/helpers/files'
import debounce from 'lodash.debounce'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import VariantsGalleryItem from './VariantsGalleryItem.vue'

const props = defineProps({
  variants: {
    type: Array,
    required: true,
  },
  modelValue: {
    type: [Array, String, Number],
    default: () => [],
  },
  type: {
    type: String,
    default: 'checkbox',
    validator: value => ['checkbox', 'radio'].includes(value),
  },
  groupId: {
    type: String,
    default: '',
  },
  inactive: {
    type: Boolean,
    default: false,
  },
  tabletView: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:model-value'])
const showShadowStart = ref(false)
const showShadowEnd = ref(false)

const buttonPrev = ref(null)
const buttonNext = ref(null)
const swiper = ref(null)

onMounted(() => {
  const swiperInstance = swiper.value.swiper
  if (swiperInstance) {
    swiperInstance.navigation.nextEl = buttonNext.value
    swiperInstance.navigation.prevEl = buttonPrev.value
    swiperInstance.navigation.update()

    // Add event listeners for shadow states
    swiperInstance.on('slideChange', () => {
      updateGallerySlides(swiperInstance)
    })

    // Listen for swiper initialization
    swiperInstance.on('init', () => {
      updateGallerySlides(swiperInstance)
    })

    // Force initial update if swiper is already initialized
    if (swiperInstance.initialized) {
      updateGallerySlides(swiperInstance)
    }
  }
})

/**
 * Обновляет высоту контейнера свайпера на основе высоты видимых слайдов
 * @description Используется вместо встроенной опции auto-height в swiper,
 * так как она работает некорректно при slidesPerView: 'auto'
 * @param {object} swiperInstance - Экземпляр свайпера
 */
function updateAutoHeight(swiperInstance) {
  if (!swiperInstance?.slides?.length)
    return

  const visibleSlides = swiperInstance.visibleSlides
  if (!visibleSlides?.length)
    return

  // Find the tallest visible slide
  const maxHeight = Math.max(...visibleSlides.map(slide => slide.offsetHeight))
  // Set the height of the swiper wrapper
  if (swiperInstance.wrapperEl) {
    swiperInstance.wrapperEl.style.height = `${maxHeight}px`
  }
}

/**
 * Управляет отображением градиентных теней по краям галереи на мобильных устройствах
 * @param {object} swiperInstance - Экземпляр свайпера
 */
function updateGradientShadows(swiperInstance) {
  if (!swiperInstance?.slides?.length)
    return

  // Показывает тени только на мобильных устройствах и если есть более одного слайда
  if (window.innerWidth <= 679 && swiperInstance.slides.length > 1) {
    requestAnimationFrame(() => {
      const isAtStart = swiperInstance.isBeginning
      const isAtEnd = swiperInstance.isEnd

      showShadowStart.value = !isAtStart
      showShadowEnd.value = !isAtEnd
    })
  }
  else {
    showShadowStart.value = false
    showShadowEnd.value = false
  }
}

/**
 * Обновляет состояние галереи: высоту контейнера и отображение теней
 * @param {object} swiperInstance - Экземпляр свайпера
 */
function updateGallerySlides(swiperInstance) {
  if (!swiperInstance?.slides?.length)
    return

  updateGradientShadows(swiperInstance)
  updateAutoHeight(swiperInstance)
}

function getButtonClasses(direction) {
  return {
    [`variants-gallery__button-${direction}`]: true,
  }
}

const galleryStyles = computed(() => {
  return {
    '--variants-gallery-item-width': `100px`,
    '--variants-gallery-item-height': `100px`,
  }
})

const galleryClasses = computed(() => {
  return {
    'variants-gallery': true,
    'variants-gallery--inactive': props.inactive,
    'variants-gallery--single': props.variants.length === 1,
    'variants-gallery--shadow-start': showShadowStart.value,
    'variants-gallery--shadow-end': showShadowEnd.value,
  }
})

function handleResize() {
  if (swiper.value?.swiper) {
    updateGallerySlides(swiper.value.swiper)
  }
}

const debouncedUpdateGallerySlides = debounce((swiperInstance) => {
  updateGradientShadows(swiperInstance)
}, 100)

onMounted(() => {
  nextTick(() => {
    const swiperInstance = swiper.value?.swiper
    if (swiperInstance) {
      swiperInstance.navigation.nextEl = buttonNext.value
      swiperInstance.navigation.prevEl = buttonPrev.value
      swiperInstance.navigation.update()

      // Add event listeners for shadow states
      swiperInstance.on('progress', () => debouncedUpdateGallerySlides(swiperInstance))

      // Initial update
      setTimeout(() => {
        updateGallerySlides(swiperInstance)
      }, 100)
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (swiper.value?.swiper) {
    swiper.value.swiper.destroy(true, true)
  }
})

watch(() => props.variants, () => {
  if (swiper.value?.swiper) {
    const swiperInstance = swiper.value.swiper
    nextTick(() => {
      swiperInstance.update()
      updateGallerySlides(swiperInstance)
    })
  }
}, { deep: true })

const spaceBetween = computed(() => {
  return props.tabletView ? 25 : 20
})
</script>

<template>
  <div :style="galleryStyles" :class="galleryClasses">
    <swiper-container
      ref="swiper"
      slides-per-view="auto"
      :space-between="spaceBetween"
      :pagination="false"
      :auto-height="false"
      :watch-slides-progress="true"
      :navigation="{}"
    >
      <swiper-slide v-for="(variant) in variants" :key="variant.value" class="variants-gallery__slide">
        <VariantsGalleryItem
          :variant="variant"
          :type="type"
          :group-id="groupId"
          :inactive="inactive"
          :disabled="variant.disabled"
          :model-value="modelValue"
          :preview-clickable="isVideo(variant.url) ? true : false"
          :media-type="isVideo(variant.url) ? 'video' : 'image'"
          @update:model-value="emit('update:model-value', $event)"
        />
      </swiper-slide>
    </swiper-container>
    <div ref="buttonPrev" :class="getButtonClasses('prev')" @click="swiper.swiper.slidePrev()">
      <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M13 5H1M1 5L5 1M1 5L5 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </div>
    <div ref="buttonNext" :class="getButtonClasses('next')" @click="swiper.swiper.slideNext()">
      <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 5H13M13 5L9 1M13 5L9 9" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </div>
  </div>
</template>

<style scoped>
.variants-gallery {
  width: 100%;
  position: relative;
  --variants-gallery-item-height: 200px;
}

.variants-gallery__button-prev,
.variants-gallery__button-next {
  position: absolute;
  top: calc(var(--variants-gallery-item-height, 50%) / 2);
  transform: translateY(-55%);
  cursor: pointer;
  z-index: 10;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: background-color 0.3s;
}

.variants-gallery__button-prev {
  left: -16px;
}

.variants-gallery__button-next {
  right: -16px;
}

.variants-gallery__button-prev:hover,
.variants-gallery__button-next:hover {
  background: rgba(0, 0, 0, 1);
}

.variants-gallery__button-prev.swiper-button-lock,
.variants-gallery__button-next.swiper-button-lock {
  opacity: 0;
  pointer-events: none;
}

.variants-gallery :deep(.swiper-button-prev) {
  display: none !important;
}

.variants-gallery :deep(.swiper-button-next) {
  display: none !important;
}

.variants-gallery__button-prev.swiper-button-disabled,
.variants-gallery__button-next.swiper-button-disabled {
  opacity: 0;
  pointer-events: none;
}

swiper-container::part(button-prev),
swiper-container::part(button-next) {
  display: none !important;
}

.variants-gallery__slide {
  width: 100% !important;
  max-width: 100px !important;
  height: auto !important;
  align-self: flex-start;
}

.variants-gallery--single swiper-slide {
  width: 100% !important;
}

@media (max-width: 679px) {
  .variants-gallery__button-prev {
    left: 0;
  }

  .variants-gallery__button-next {
    right: 0;
  }

  .variants-gallery swiper-container::before,
  .variants-gallery swiper-container::after {
    content: '';
    position: absolute;
    top: 0;
    width: 25px;
    height: 100%;
    pointer-events: none;
    z-index: 10;
    opacity: 0;
    display: block;
    transition: opacity 0.3s;
  }

  .variants-gallery swiper-container::before {
    left: 0;
    background: linear-gradient(90deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
  }

  .variants-gallery swiper-container::after {
    right: 0;
    background: linear-gradient(270deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
  }

  .variants-gallery--shadow-start swiper-container::before {
    opacity: 1;
  }

  .variants-gallery--shadow-end swiper-container::after {
    opacity: 1;
  }
}
</style>
