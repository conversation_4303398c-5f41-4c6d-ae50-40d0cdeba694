<script setup>
import { fetchAddress } from '@/shared/api'
import Autocomplete from '@/shared/ui/Autocomplete.vue'
import FormGroup from '@/shared/ui/FormGroup.vue'
import { computed, ref, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

// TODO: Временно отключен API адресов. После восстановления работы API установить в true
const IS_ADDRESS_API_AVAILABLE = false

const isMobile = ref(window.innerWidth <= 679)

window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth <= 679
})

async function fetchAddressSuggestions(query) {
  if (!IS_ADDRESS_API_AVAILABLE)
    return []

  const regions = toValue(props.question.arRegionsIDs)
  const cities = toValue(props.question.arCitiesIDs)
  const districts = toValue(props.question.arDistrictsIDs)
  const streets = toValue(props.question.arStreetsIDs)

  if (!regions || regions.length === 0) {
    return await fetchAddress(query, { limit: 15 })
  }

  const promises = regions.map((regionID, index) => {
    const options = {
      regionId: regionID,
      cityId: cities?.[index] || '',
      districtId: districts?.[index] || '',
      streetId: streets?.[index] || '',
      limit: 15,
    }

    return fetchAddress(query, options)
  })

  try {
    const results = await Promise.all(promises)
    // Flatten and deduplicate results
    const uniqueResults = [...new Set(results.flat())]
    return uniqueResults.slice(0, 15) // Limit to 15 total results
  }
  catch (error) {
    console.error('Error fetching address suggestions:', error)
    return []
  }
}

function onSelect() {
  props.question.isSelected.value = true
}

const touched = computed(() => {
  return toValue(props.question.touched)
})
const isInputInvalid = computed(() => {
  const invalid = toValue(props.question.touched) && toValue(props.question.error)
  return !!invalid
})
</script>

<template>
  <div>
    <FormGroup :error="touched && props.question.error.value">
      <Autocomplete
        v-model="question.value.value"
        v-model:has-suggestions="question.hasSuggestions.value"
        :placeholder="question.placeholderText.value"
        :fetch-suggestions="fetchAddressSuggestions"
        :view="isMobile ? 'mobile' : 'desktop'"
        :is-invalid="isInputInvalid"
        :debounce="300"
        :disable-popup="!IS_ADDRESS_API_AVAILABLE"
        @select="onSelect"
      />
    </FormGroup>
  </div>
</template>
