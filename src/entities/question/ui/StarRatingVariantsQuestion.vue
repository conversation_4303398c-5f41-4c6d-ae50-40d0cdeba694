<script setup>
import Check from '@shared/ui/Check.vue'
import FormError from '@shared/ui/FormError.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import StarRating from '@shared/ui/StarRating.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { onMounted, ref, toValue } from 'vue'
import Variants from './Variants.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const questionRef = ref(null)

onMounted(() => {
  props.question.domNodeElement.value = questionRef.value
})

const {
  showLabels,
  showNumbers,
  labels,
  skipped,
  id,
  variants,
  count,
  size,
  color,
  isSkipVariant,
  skipText,
  commentEnabled,
  skip,
  commentController,
  enableGallery,
  galleryController,
  questionId,
} = toValue(props.question)

const {
  title: commentTitle,
  error: commentError,
  value: commentValue,
  maxLength,
  minLength,
  placeholderText,
} = commentController || {}

function variantItemLabelClasses(variant) {
  return {
    'star-rating-variant-item__label': true,
    'star-rating-variant-item__label--skipped': toValue(variant.skipped) || skipped.value,
  }
}
</script>

<template>
  <div ref="questionRef">
    <form :id="`answer-form-${id}`">
      <div class="survey-questions__wrapper_pre">
        <SlideTransition>
          <div v-if="enableGallery" class="gallery-container">
            <Gallery
              :gallery="galleryController.gallery.value"
              :selectable="false"
              :inactive="skipped"
              type="default"
              :group-id="questionId"
            />
          </div>
        </SlideTransition>

        <div class="star-rating-variants-container">
          <div v-for="variant in variants" :key="variant.id" class="star-rating-variant-item">
            <FormGroup>
              <p :class="variantItemLabelClasses(variant)">
                {{ variant.text }}
              </p>
              <StarRating
                v-model="variant.stars.value"
                :max="count"
                :size="size"
                :color="color"
                :show-labels="showLabels"
                :show-numbers="showNumbers"
                :labels="labels"
                :inactive="variant.skipped.value || skipped"
              />
              <TransitionGroup
                name="fade-up"
                class="fc-star-rating__message-container"
                tag="div"
              >
                <FormError v-if="variant.error.value" :key="1" :error="variant.error.value" class="question-rating-error" />
                <div v-if="labels[variant.stars.value - 1] && !showLabels" :key="2" class="fc-star-rating__message">
                  {{ labels[variant.stars.value - 1] }}
                </div>
              </TransitionGroup>
              <SlideTransition>
                <div v-if="variant.variantsController && variant.variantsController.enabled.value">
                  <Variants :variants-controller="variant.variantsController" custom-class="survey-questions__variants-form-group" />
                </div>
              </SlideTransition>
              <div v-if="isSkipVariant" class="variant-skip-container">
                <Check v-model="variant.skipped.value" :label="skipText" />
              </div>
            </FormGroup>
          </div>
        </div>

        <SlideTransition>
          <div v-if="commentEnabled && !skipped">
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="commentTitle"
              :error="commentError"
            >
              <Textarea
                v-model="commentValue"
                :maxlength="maxLength"
                :minlength="minLength"
                :placeholder="placeholderText"
                :is-invalid="commentError"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <div v-if="skip && !isSkipVariant" class="skip-container">
          <Check v-model="skipped" :label="skipText" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.star-rating-variants-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.star-rating-variant-item {
  width: 100%;
  padding-bottom: 20px;
  position: relative;
}

.star-rating-variant-item:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
}

.star-rating-variant-item:last-child::before {
  display: none;
}

.star-rating-variant-item__label {
  margin-bottom: 20px;
  text-align: center;
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
  transition: opacity 0.3s;
  overflow: hidden;
}

.star-rating-variant-item__label--skipped {
  opacity: 0.7;
}

.fc-star-rating__message-container {
  margin-top: 20px;
  text-align: center;
  position: relative;
}

.fc-star-rating__message-container:empty {
  display: none;
}

.fc-star-rating__message {
  font-size: var(--fqz-poll-font-size, 13px);
  font-weight: 600;
  line-height: 1.1;
  overflow: hidden;
}

.survey-questions__variants-form-group {
  padding-top: 20px;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.skip-container,
.variant-skip-container {
  margin-top: 20px;
  text-align: center;
}

:deep(.question-star-rating-error) {
  margin-top: 10px;
  text-align: center;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

@media screen and (max-width: 679px) {
  .star-rating-variant-item__label,
  .fc-star-rating__message {
    font-size: 14px;
  }
}
</style>
