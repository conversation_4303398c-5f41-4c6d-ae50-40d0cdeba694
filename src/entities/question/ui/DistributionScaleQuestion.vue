<script setup>
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import Scale from '@shared/ui/Scale.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import { StickyContainer } from '@shared/ui/StickyContainer'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const isSkipped = computed(() => toValue(props.question.skipped))

function onPointerDown(variant) {
  props.question.skipped.value = false
  if (variant) {
    variant.skipped.value = false
  }
}

const simplifiedMode = useSimplifiedStore()

const distributionScaleClasses = computed(() => {
  const classes = ['distribution-scale-question']
  if (simplifiedMode.isSimplifiedMode) {
    classes.push('scale--simplified')
  }
  return classes
})
</script>

<template>
  <div :class="distributionScaleClasses">
    <form :id="`answer-form-${question.id}`">
      <div class="survey-questions__wrapper_pre">
        <div v-if="question.enableGallery.value" class="gallery-container">
          <Gallery
            :gallery="question.galleryController.gallery.value"
            :selectable="false"
            :inactive="isSkipped"
            type="default"
            :group-id="`${question.questionId}-${question.type}-gallery`"
          />
        </div>

        <FormGroup
          :error="question.error"
          :error-attrs="{ class: 'question-scale-error' }"
        >
          <StickyContainer>
            <div class="scale-indicator__panel">
              {{ question.indicatorText }}
              <span class="scale-indicator__indicator">
                <span
                  :class="{ 'scale-indicator__count_error': question.isOverflowed.value }"
                >
                  {{ question.countVariants }}
                </span>
                /
                <span>
                  {{ question.scaleEnd }}
                </span>
              </span>
            </div>
          </StickyContainer>
          <div class="scale-items-container">
            <div v-for="variant in toValue(question.variants)" :key="variant.id" class="scale-item">
              <FormGroup
                :error="variant.error.value"
                :error-attrs="{ class: 'question-scale-error' }"
              >
                <p class="scale-item__label" :class="{ 'scale-item__label--skipped': isSkipped || variant.skipped.value }">
                  {{ variant.text }}
                </p>
                <Scale
                  v-model="variant.rating.value"
                  :min="0"
                  :max="question.scaleEnd.value"
                  :step="1"
                  :invalid="variant.invalid.value"
                  :disabled="question.skipped.value || variant.skipped.value"
                  @input-focus="variant.skipped.value = false"
                  @pointerdown="() => onPointerDown(variant)"
                />
                <div v-if="question.isSkipVariant.value" class="variant-skip-container">
                  <Check v-model="variant.skipped.value" :label="question.skipText.value" />
                </div>
              </FormGroup>
            </div>
          </div>
        </FormGroup>

        <SlideTransition>
          <div
            v-if="question.commentController.enabled.value && !question.skipped.value"
            class="survey-questions__comment-form-group"
          >
            <FormGroup
              :label="question.commentController.title.value"
              :error="question.commentController.error.value"
            >
              <Textarea
                v-model="question.commentController.value.value"
                :maxlength="question.commentController.maxLength.value"
                :minlength="question.commentController.minLength.value"
                :placeholder="question.commentController.placeholderText.value"
                :is-invalid="!!question.commentController.error.value"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <div v-if="question.skip.value && !question.isSkipVariant.value" class="skip-container">
          <Check v-model="question.skipped.value" :label="question.skipText.value" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.scale-items-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.scale-indicator {
  width: 100%;
  font-size: var(--fqz-poll-font-size);
  color: var(--fqz-poll-text-on-place);
  margin-bottom: 10px;
}

@media screen and (max-width: 679px) {
  .scale-indicator {
    font-size: 14px;
  }
}

.scale-indicator__panel {
  position: relative;
  padding: 10px 0;
}

.scale-indicator__indicator {
  white-space: nowrap;
  font-weight: 700;
}

.scale-indicator__panel::before {
  content: '';
  position: absolute;
  inset: 0;
  background-color: var(--fqz-poll-main-place-color);
  opacity: 0.85;
  z-index: -1;
}

.scale-indicator__count_error {
  color: red;
}

.survey-questions__comment-form-group {
  margin-top: 30px;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.variant-skip-container {
  margin-top: 20px;
}

:deep(.question-scale-error) {
  margin-top: 20px;
  text-align: center;
}

@media screen and (min-width: 768px) {
  .survey--tablet .survey-questions__variants {
    font-size: 22px;
  }
}

.scale-item {
  width: 100%;
  position: relative;
}

.scale-item:last-child::before {
  display: none;
}

.scale-item__label {
  margin-bottom: 20px;
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
  overflow: hidden;
  transition: opacity 0.3s;
}

.scale-item__label--skipped {
  opacity: 0.7;
}

@media screen and (max-width: 679px) {
  .scale-item__label {
    font-size: 14px;
  }
}

.gallery-container {
  margin-bottom: 20px;
  margin-top: -10px;
}
</style>
