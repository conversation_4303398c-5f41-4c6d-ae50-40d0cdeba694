<script setup>
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import Scale from '@shared/ui/Scale.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const isSkipped = computed(() => toValue(props.question.skipped))

function onPointerDown(variant) {
  props.question.skipped.value = false
  if (variant) {
    variant.skipped.value = false
  }
}
</script>

<template>
  <div>
    <form :id="`answer-form-${question.id}`">
      <div class="survey-questions__wrapper_pre">
        <div v-if="question.enableGallery.value" class="gallery-container">
          <Gallery
            :gallery="question.galleryController.gallery.value"
            :selectable="false"
            :inactive="isSkipped"
            type="default"
            :group-id="`${question.questionId}-${question.type}-gallery`"
          />
        </div>

        <div v-if="question.isVariants.value" class="scale-items-container">
          <div v-for="variant in toValue(question.variants)" :key="variant.id" class="scale-item">
            <FormGroup :error="variant.error.value" :error-attrs="{ class: 'question-scale-error' }">
              <p class="scale-item__label" :class="{ 'scale-item__label--skipped': isSkipped || variant.skipped.value }">
                {{ variant.text }}
              </p>
              <Scale
                v-model="variant.rating.value"
                :min="question.scaleStart.value"
                :max="question.scaleEnd.value"
                :step="question.scaleStep.value"
                :disabled="question.skipped.value || variant.skipped.value"
                @input-focus="variant.skipped.value = false"
                @pointerdown="() => onPointerDown(variant)"
              />
              <div v-if="question.isSkipVariant.value" class="variant-skip-container">
                <Check v-model="variant.skipped.value" :label="question.skipText.value" />
              </div>
            </FormGroup>
          </div>
        </div>
        <div v-else class="scale-container--single">
          <FormGroup :error="question.error.value" :error-attrs="{ class: 'question-scale-error' }">
            <Scale
              v-model="question.rating.value"
              :min="question.scaleStart.value"
              :max="question.scaleEnd.value"
              :step="question.scaleStep.value"
              :disabled="question.skipped.value"
              @input-focus="question.skipped.value = false"
              @pointerdown="() => onPointerDown()"
            />
          </FormGroup>
        </div>

        <SlideTransition>
          <div v-if="question.commentEnabled.value && !question.skipped.value">
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="question.commentController.title.value"
              :error="question.commentController.error.value"
            >
              <Textarea
                v-model="question.commentController.value.value"
                :maxlength="question.commentController.maxLength.value"
                :minlength="question.commentController.minLength.value"
                :placeholder="question.commentController.placeholderText.value"
                :is-invalid="question.commentController.error.value"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <div v-if="question.skip.value && !question.isSkipVariant.value" class="skip-container">
          <Check v-model="question.skipped.value" :label="question.skipText.value" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.scale-items-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.variant-skip-container {
  margin-top: 20px;
}

:deep(.question-scale-error) {
  margin-top: 20px;
  text-align: center;
}

@media screen and (min-width: 768px) {
  .survey--tablet .survey-questions__variants {
    font-size: 22px;
  }
}

.scale-item {
  padding-bottom: 20px;
  width: 100%;
  position: relative;
}

.scale-item:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
}

.scale-item:last-child::before {
  display: none;
}

.scale-item__label {
  margin-bottom: 20px;
  text-align: center;
  font-size: var(--fqz-poll-font-size);
  line-height: 1.1;
  overflow: hidden;
  transition: opacity 0.3s;
}

.scale-item__label--skipped {
  opacity: 0.7;
}

@media screen and (max-width: 679px) {
  .scale-item__label {
    font-size: 14px;
  }
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}
</style>
