<script setup>
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const isSkipped = computed(() => toValue(props.question.skipped))
const gradient = computed(() => toValue(props.question.gradient))

function getButtonStyle(row, index) {
  if (toValue(props.question.form) === 'circle') {
    const size = index === 0 || index === 4 ? 'large' : index === 1 || index === 3 ? 'medium' : 'small'
    const color = gradient.value[index]
    return {
      'borderRadius': '50%',
      'border': `2px solid var(--color)`,
      'background-color': '#fff',
      'width': size === 'large' ? '48px' : size === 'medium' ? '36px' : '30px',
      'height': size === 'large' ? '48px' : size === 'medium' ? '36px' : '30px',
      '--color': color,
    }
  }
  else {
    return {
      borderRadius: '4px',
      border: 'none',
    }
  }
}

function onClick(row, index) {
  if (row.rating.value === -1) {
    row.rating.value = index + 1
  }
  else if (row.rating.value === index + 1) {
    row.rating.value = -1
  }
  else {
    row.rating.value = index + 1
  }
}

function getScaleClasses(row) {
  return {
    'diff-question-scale--circle': toValue(props.question.form) === 'circle',
    'diff-question-scale--error': row.error.value,
    'diff-question-scale--has-labels': toValue(row.startLabel) || toValue(row.endLabel),
    'diff-question-scale--has-start-label': toValue(row.startLabel),
    'diff-question-scale--has-end-label': toValue(row.endLabel),
    'diff-question-scale--disabled': isSkipped.value,
  }
}

function getButtonClasses(row, index) {
  return {
    'diff-question-button--selected': toValue(row.rating) === index + 1,
    'diff-question-button--circle': toValue(props.question.form) === 'circle',
    'diff-question-button--rectangle': toValue(props.question.form) === 'rect',
  }
}

const containerClasses = computed(() => ({
  'diff-question-container': true,
  'diff-question-container--circle': toValue(props.question.form) === 'circle',
  'diff-question-container--has-start-labels': props.question.differentialRows.value.some(row => toValue(row.startLabel)),
  'diff-question-container--has-end-labels': props.question.differentialRows.value.some(row => toValue(row.endLabel)),
}))
</script>

<template>
  <div>
    <form :id="`answer-form-${question.id}`">
      <div class="survey-questions__wrapper_pre">
        <SlideTransition>
          <div v-if="toValue(question.enableGallery)" class="gallery-container">
            <Gallery
              :gallery="question.galleryController.gallery.value"
              :selectable="false"
              :inactive="isSkipped"
              type="default"
              :group-id="`${question.questionId}-${question.type}`"
            />
          </div>
        </SlideTransition>

        <div :class="containerClasses">
          <FormGroup :error="question.error.value" :error-attrs="{ class: 'question-diff-error' }" class="diff-question-form-group">
            <div v-for="row in question.differentialRows.value" :key="row.id" class="diff-question-row">
              <div class="diff-question-scale" :class="getScaleClasses(row)">
                <div class="diff-question-label diff-question-label--start" :class="{ 'diff-question-label--skipped': isSkipped, 'diff-question-label--placeholder': !toValue(row.startLabel) }">
                  {{ row.startLabel || ' ' }}
                </div>
                <div class="diff-question-buttons">
                  <button
                    v-for="index in 5"
                    :key="index"
                    type="button"
                    class="diff-question-button"
                    :class="getButtonClasses(row, index - 1)"
                    :style="getButtonStyle(row, index - 1)"
                    @click="onClick(row, index - 1)"
                  >
                    <span v-if="props.question.form.value === 'rect'" class="diff-question-button__text">
                      {{ index }}
                    </span>
                    <svg v-else class="diff-question-button__icon" width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M1 3.82353L5.94118 8.76471L13 1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                  </button>
                </div>
                <div class="diff-question-label diff-question-label--end" :class="{ 'diff-question-label--skipped': isSkipped, 'diff-question-label--placeholder': !toValue(row.endLabel) }">
                  {{ row.endLabel || ' ' }}
                </div>
              </div>
            </div>
          </FormGroup>
        </div>
      </div>

      <SlideTransition>
        <div v-if="toValue(question.commentEnabled) && !toValue(question.skipped)">
          <FormGroup
            custom-class="survey-questions__comment-form-group"
            :label="question.commentController.title.value"
            :error="question.commentController.error.value"
          >
            <Textarea
              v-model="question.commentController.value.value"
              :maxlength="question.commentController.maxLength.value"
              :minlength="question.commentController.minLength.value"
              :placeholder="question.commentController.placeholderText.value"
              :is-invalid="question.commentController.error.value"
            />
          </FormGroup>
        </div>
      </SlideTransition>

      <div v-if="toValue(question.skip)" class="skip-container">
        <Check v-model="question.skipped.value" :label="question.skipText.value" />
      </div>
    </form>
  </div>
</template>

<style scoped>
.diff-question-form-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.diff-question-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.diff-question-row {
  width: 100%;
}

.diff-question-row:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

.diff-question-scale {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  gap: 15px;
  transition: opacity 0.3s ease;
  justify-content: flex-start;
}

.diff-question-scale--disabled {
  opacity: 0.5;
}

.diff-question-scale:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 0, 0, 0.2);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  top: -4px;
  height: calc(100% + 8px);
  border-radius: 8px;
}

.diff-question-scale--error:before {
  opacity: 1;
}

.diff-question-scale--circle .diff-question-buttons {
  flex: 0 0 240px;
  justify-content: space-between;
  gap: 0;
  align-items: center;
}

.diff-question-scale--circle .diff-question-button {
  flex: 0 0 auto;
  min-height: 0;
  align-self: center;
  background-color: transparent;
}

.diff-question-scale--circle .diff-question-button--selected {
  background-color: var(--color) !important;
}

.diff-question-scale--circle .diff-question-button--selected .diff-question-button__icon {
  transform: none;
  opacity: 1;
}

.diff-question-label,
.diff-question-label-placeholder {
  font-size: 15px;
  line-height: 1.1;
  width: 155px;
  text-align: center;
  transition: opacity 0.3s;
  display: none;
}

.diff-question-label--start {
  text-align: left;
}

.diff-question-label--end {
  text-align: right;
}

.diff-question-container .diff-question-scale {
  justify-content: center;
}

.diff-question-container--circle .diff-question-form-group {
  gap: 15px;
}

.diff-question-container--has-start-labels .diff-question-scale {
  justify-content: flex-start;
}

.diff-question-container--has-end-labels .diff-question-scale {
  justify-content: flex-end;
}

.diff-question-container--has-start-labels .diff-question-label--start {
  display: block;
  text-align: left;
}

.diff-question-container--circle .diff-question-label--start {
  text-align: right;
}

.diff-question-container--has-end-labels .diff-question-label--end {
  display: block;
  text-align: left;
}

.diff-question-container--has-start-labels.diff-question-container--has-end-labels .diff-question-label--start {
  text-align: right;
}

.diff-question-container--has-start-labels.diff-question-container--has-end-labels .diff-question-label--end {
  text-align: left;
}

.diff-question-label--end {
  text-align: left;
}

.diff-question-label--skipped {
  opacity: 0.7;
}

.diff-question-buttons {
  display: flex;
  flex-grow: 1;
  text-align: center;
  align-items: center;
  gap: 2px;
  align-self: stretch;
}

.diff-question-button {
  min-height: 36px;
  display: flex;
  align-items: center;
  align-self: stretch;
  height: 100%;
  justify-content: center;
  font-size: 15px;
  flex-grow: 1;
  font-weight: normal;
  overflow: hidden;
  cursor: pointer;
  transition:
    color 0.3s ease,
    background-color 0.3s ease;
  position: relative;
}

.diff-question-button:focus {
  outline: none;
}

.diff-question-button__text {
  position: relative;
  z-index: 1;
}

.diff-question-button__icon {
  display: block;
  transition: transform 0.3s ease;
  opacity: 0;
  transform: scale(0.3);
  flex: 0 0 auto;
}

.diff-question-button--rectangle {
  color: var(--fqz-poll-text-on-place);
}

.diff-question-button--rectangle:hover {
  background-color: inherit;
}

.diff-question-button--rectangle:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.15;
  transition: 0.3s ease;
}

.diff-question-button--rectangle:not(.diff-question-button--selected):hover::before {
  opacity: 0.3;
}

.diff-question-button--selected.diff-question-button--rectangle {
  color: white;
  background-color: var(--fqz-poll-main-color);
}

.diff-question-button--selected.diff-question-button--circle {
  opacity: 1;
  transform: scale(1);
}

.diff-question-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

:deep(.question-diff-error) {
  margin-top: 5px;
  text-align: center;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

@media screen and (max-width: 679px) {
  .diff-question-form-group {
    gap: 16px;
  }
  .diff-question-label {
    font-size: 13px;
  }

  .diff-question-label-placeholder {
    display: none;
  }

  .diff-question-button {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  .diff-question-scale {
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-between !important;
  }
  .diff-question-scale:before {
    top: -8px;
    height: calc(100% + 14px);
  }
  .diff-question-buttons {
    width: 100%;
  }
  .diff-question-scale--circle .diff-question-buttons {
    flex: 0 0 auto;
  }
  .diff-question-label--start {
    text-align: left !important;
    order: -2;
    align-self: flex-start;
    margin-right: auto;
  }
  .diff-question-label--end {
    align-self: flex-end;
    order: -1;
    text-align: right !important;
    margin-left: auto;
  }
  .diff-question-scale--has-end-label:not(.diff-question-scale--has-start-label) {
    justify-content: flex-end;
  }

  .diff-question-container--has-start-labels .diff-question-label--placeholder {
    display: none;
  }
  .diff-question-container--has-end-labels .diff-question-label--placeholder {
    display: none;
  }
}

@media screen and (max-width: 450px) {
  .diff-question-label {
    width: 134px;
  }
}

@media screen and (max-width: 321px) {
  .diff-question-label {
    width: 120px;
  }
}
</style>
