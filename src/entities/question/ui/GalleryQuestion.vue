<script setup>
import { useTabletStore } from '@/shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, ref, watch } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const tabletStore = useTabletStore()
const isTabletMode = computed(() => tabletStore.isTabletMode)

const questionId = computed(() => props.question.questionId)
const selectButtonText = computed(() => props.question.t('Выбрать'))

function updateRating({ id, rating }) {
  props.question.skipped.value = false
  props.question.galleryController.gallery.value.find(item => item.id === id).rating = rating
}
const galleryRef = ref(null)

watch(
  () => galleryRef.value?.swiper,
  (newVal) => {
    if (newVal) {
      props.question.galleryController.sliderInstance.value = newVal
    }
  },
  { immediate: true },
)
</script>

<template>
  <div>
    <div class="survey-questions__wrapper_pre gallery-question">
      <FormGroup :error="question.galleryController.error.value" :error-attrs="{ class: 'question-gallery-error' }">
        <Gallery
          ref="galleryRef"
          :gallery="question.galleryController.gallery.value"
          :selectable="question.galleryController.selectable.value"
          :inactive="question.galleryController.skipped.value"
          :selected-items="question.galleryController.selectedItems.value"
          :multiple="question.galleryController.multiple.value"
          :select-button-text="selectButtonText"
          :group-id="`${questionId}-${question.type}`"
          :show-placeholder="false"
          :slides-per-view="isTabletMode ? 2 : null"
          type="rating"
          @update:rating="updateRating"
        />
      </FormGroup>

      <SlideTransition>
        <div v-if="question.commentEnabled.value && !question.skipped.value">
          <FormGroup
            custom-class="survey-questions__comment-form-group"
            :label="question.commentController.title.value"
            :error="question.commentController.error.value"
          >
            <Textarea
              v-model="question.commentController.value.value"
              :maxlength="question.commentController.maxLength.value"
              :minlength="question.commentController.minLength.value"
              :placeholder="question.commentController.placeholderText.value"
              :is-invalid="question.commentController.error.value"
            />
          </FormGroup>
        </div>
      </SlideTransition>

      <div v-if="question.skip.value" class="skip-container">
        <Check v-model="question.skipped.value" :label="question.skipText.value" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.gallery-question {
  margin-top: -10px;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

:deep(.question-gallery-error) {
  margin-top: 20px;
  text-align: center;
}
</style>
