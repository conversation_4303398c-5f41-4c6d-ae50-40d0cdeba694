<script setup>
import { useTabletStore } from '@shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

function updateSelectedItems(newSelectedItems) {
  props.question.skipped.value = false
  props.question.galleryController.selectedItems.value = newSelectedItems
}

function updateRating(newRating) {
  props.question.galleryController.rating.value = newRating
}

function updateSwiper(swiper) {
  props.question.galleryController.sliderInstance.value = swiper
}

const questionId = computed(() => props.question.questionId)
const selectButtonText = computed(() => props.question.t('Выбрать'))
const chooseType = computed(() => toValue(props.question.chooseType))
const tabletStore = useTabletStore()
const tabletView = computed(() => tabletStore.isTabletMode)
const slidesPerView = computed(() => (tabletView.value ? 2 : null))
</script>

<template>
  <div class="media-variants-question">
    <div class="survey-questions__wrapper_pre">
      <FormGroup :error="question.galleryController.error.value" :error-attrs="{ class: 'question-gallery-error' }">
        <Gallery
          :gallery="question.galleryController.gallery.value"
          :selectable="question.galleryController.selectable.value"
          :inactive="question.galleryController.skipped.value"
          :selected-items="question.galleryController.selectedItems.value"
          :multiple="question.galleryController.multiple.value"
          :select-button-text="selectButtonText"
          :group-id="`${questionId}-${question.type}`"
          :type="question.galleryController.type.value"
          :placeholder-type="chooseType === 'video' ? 'video' : 'image'"
          :slides-per-view="slidesPerView"
          @update:selected-items="updateSelectedItems"
          @update:rating="updateRating"
          @swiper="updateSwiper"
        />
      </FormGroup>

      <SlideTransition>
        <div v-if="toValue(question.commentEnabled) && !toValue(question.skipped)">
          <FormGroup
            custom-class="survey-questions__comment-form-group"
            :label="question.commentController.title.value"
            :error="question.commentController.error.value"
          >
            <Textarea
              v-model="question.commentController.value.value"
              :maxlength="question.commentController.maxLength.value"
              :minlength="question.commentController.minLength.value"
              :placeholder="question.commentController.placeholderText.value"
              :is-invalid="question.commentController.error.value"
            />
          </FormGroup>
        </div>
      </SlideTransition>

      <div v-if="toValue(question.skip)" class="skip-container">
        <Check v-model="question.skipped.value" :label="question.skipText.value" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.media-variants-question {
  margin-top: -10px;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

:deep(.question-gallery-error) {
  margin-top: 20px;
  text-align: center;
}
</style>
