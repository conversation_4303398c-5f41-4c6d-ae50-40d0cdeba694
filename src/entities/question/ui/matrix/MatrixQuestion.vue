<script setup>
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import FcSelect from '@shared/ui/Select/FcSelect.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { useResizeObserver } from '@vueuse/core'
import { computed, nextTick, onMounted, ref, toValue, watch } from 'vue'
import Variants from '../Variants.vue'
import MatrixTable from './MatrixTable.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const isSkipped = computed(() => toValue(props.question.skipped))
const matrixType = computed(() => toValue(props.question.matrixSettings).type)
const reachedScrollStart = ref(false)
const reachedScrollEnd = ref(false)
const scrollElementLeft = ref(0)
const hasScroll = ref(false)

const errorScroll = ref()

const simplifiedMode = useSimplifiedStore()

const PREVIEW_ROW_PLACEHOLDER_TEXT = '__foquz__empty'

function getRowClasses(row) {
  const rows = toValue(props.question.rows)
  const cols = toValue(props.question.cols)
  return {
    'matrix-question__row': true,
    'matrix-question__row--error': row.error.value,
    'matrix-question__row--disabled': isSkipped.value,
    'matrix-question__row--skipped': row.skipped.value,
    'matrix-question__row--three-or-less-cols': cols.length <= 3,
    'matrix-question__row--single': rows.length === 1,
    'matrix-question__row--dropdown': toValue(props.question.dropdownVariants),
  }
}

function getCellClasses(row, colIndex) {
  return {
    'matrix-question__cell': true,
    'matrix-question__cell--selected':
      row.selectedCols.value.includes(colIndex),
  }
}

const containerClasses = computed(() => ({
  'matrix-question': true,
  'matrix-question--has-scroll': hasScroll.value,
  'matrix-question--skipped': isSkipped.value,
  'matrix-question--reached-scroll-start': reachedScrollStart.value,
  'matrix-question--reached-scroll-end': reachedScrollEnd.value,
  'matrix-question--dropdown': toValue(props.question.dropdownVariants),
  'matrix-question--simplified': simplifiedMode.isSimplifiedMode,
  'matrix-question--error-block': toValue(props.question.error) && (hasScroll.value || errorScroll.value),
  'matrix-question--error-block-mobile': toValue(props.question.error),
}))

const cellStyle = computed(() => {
  const colCount = toValue(props.question.cols).length
  if (colCount <= 3) {
    return { width: `calc(${100 / colCount}% - 2px)` }
  }
  return {}
})

function setScrollVisualBoundaries(el) {
  const scrollLeft = el.scrollLeft
  const clientWidth = el.clientWidth
  const scrollWidth = el.scrollWidth
  const reachedStart = scrollLeft <= 2
  const reachedEnd = Math.abs(scrollLeft + clientWidth - scrollWidth) < 1
  scrollElementLeft.value = scrollLeft
  hasScroll.value = el.scrollWidth > el.clientWidth
  if (
    reachedStart !== reachedScrollStart.value
    || reachedEnd !== reachedScrollEnd.value
  ) {
    reachedScrollStart.value = reachedStart
    reachedScrollEnd.value = reachedEnd
  }
}

function onScroll(evt) {
  setScrollVisualBoundaries(evt.target)
}

function onCellClick(evt, row, colIndex) {
  const closestButton = evt.target.closest('button')
  if (closestButton) {
    closestButton.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'center',
    })
  }
  props.question.toggleCol(row, colIndex)
}
const simplebarRef = ref(null)
onMounted(() => {
  const scrollElement = simplebarRef.value?.scrollElement
  if (scrollElement) {
    setScrollVisualBoundaries(scrollElement)
  }
})

useResizeObserver(simplebarRef, () => {
  const scrollElement = simplebarRef.value?.scrollElement
  if (scrollElement) {
    setScrollVisualBoundaries(scrollElement)
  }
})

const selectOptions = computed(() => {
  return toValue(props.question.cols).map((col, index) => ({
    id: index,
    label: toValue(props.question.translatedCols.value[index]),
    value: index,
  }))
})

function onSelectChange(row, value) {
  props.question.skipped.value = false
  row.skipped.value = false
  if (toValue(props.question.multipleChoice)) {
    row.selectedCols.value = value.map(v => v.value)
  }
  else {
    row.selectedCols.value = [value.value]
  }
}

// @NOTE: В режиме предпросмотра строка изначально приходит строкой вида "__foquz__empty"
// и только потом при редактировании приходит строка с названием
function shouldShowRowTitle(row) {
  const title = toValue(row.rawTitle) || ''
  return !title.startsWith(PREVIEW_ROW_PLACEHOLDER_TEXT)
}

watch(
  props.question.cols,
  (_) => {
    if (matrixType.value === 'standart') {
      const scrollElement = simplebarRef.value?.scrollElement
      if (scrollElement) {
        scrollElement.scrollTo(0, 0)
        nextTick().then(() => {
          simplebarRef.value.recalculate()

          setScrollVisualBoundaries(scrollElement)
        })
      }
    }
  },
  { deep: true },
)
</script>

<template>
  <div>
    <form :id="`answer-form-${question.id}`">
      <div
        class="survey-questions__wrapper_pre"
        :class="{
          'survey-questions__wrapper_pre--dropdown': toValue(question.dropdownVariants),
          'survey-questions__wrapper_pre--has-comment': question.commentEnabled.value,
          'survey-questions__wrapper_pre--has-skip': toValue(question.skip) && !toValue(question.skipVariant),
        }"
      >
        <div v-if="toValue(question.enableGallery)" class="gallery-container">
          <Gallery
            :gallery="question.galleryController.gallery.value"
            :selectable="false"
            :inactive="isSkipped"
            type="default"
            :group-id="`${question.questionId}-${question.type}`"
          />
        </div>

        <FormGroup
          :error="question.error.value"
          :error-attrs="{ class: 'matrix-question__error' }"
        >
          <div
            :class="containerClasses"
            :style="{ '--scroll-left': `${scrollElementLeft || 0}px` }"
          >
            <simplebar
              v-if="matrixType === 'standart' || toValue(question.dropdownVariants)"
              ref="simplebarRef"
              data-simplebar-auto-hide="false"
              class="simplebar-custom simplebar-themed"
              @scroll="onScroll"
            >
              <div
                class="matrix-question__table"
              >
                <div
                  v-for="row in question.rows.value"
                  :key="row.id"
                  :class="getRowClasses(row)"
                >
                  <div
                    v-if="shouldShowRowTitle(row)"
                    class="matrix-question__row-title"
                  >
                    {{ row.title }}
                  </div>
                  <div
                    v-if="!toValue(question.dropdownVariants)"
                    class="matrix-question__scale"
                  >
                    <button
                      v-for="(col, colIndex) in toValue(
                        question.translatedCols,
                      )"
                      :key="colIndex"
                      type="button"
                      :class="getCellClasses(row, colIndex)"
                      :style="cellStyle"
                      @click="onCellClick($event, row, colIndex)"
                    >
                      <span class="matrix-question__cell-text">
                        {{ col }}
                      </span>
                    </button>
                  </div>
                  <div v-else class="matrix-question__select">
                    <FcSelect
                      :model-value="
                        row.selectedCols.value.map(
                          (colIndex) => selectOptions[colIndex],
                        )
                      "
                      :options="selectOptions"
                      :searchable="true"
                      :invalid="row.error.value"
                      :full-width="true"
                      :multiple="toValue(question.multipleChoice)"
                      :placeholder="toValue(question.selectPlaceholderText)"
                      @update:model-value="
                        (value) => onSelectChange(row, value)
                      "
                    />
                  </div>
                  <SlideTransition>
                    <Variants
                      v-if="row.variantsController && row.variantsController.enabled.value"
                      :variants-controller="row.variantsController"
                      custom-class="matrix-question__variants"
                    />
                  </SlideTransition>
                  <div
                    v-if="toValue(question.skipVariant)"
                    class="matrix-question__skip"
                  >
                    <Check
                      :model-value="row.skipped.value"
                      :label="question.skipText.value"
                      @update:model-value="question.toggleSkip(row)"
                    />
                  </div>
                </div>
              </div>
            </simplebar>
            <MatrixTable
              v-else-if="matrixType === 'marker'"
              v-model:error="errorScroll"
              :question="question"
            />
          </div>
        </FormGroup>

        <SlideTransition>
          <div v-if="question.commentEnabled.value && !question.skipped.value">
            <FormGroup
              custom-class="matrix-question__comment-form-group"
              :label="question.commentController.title.value"
              :error="question.commentController.error.value"
            >
              <Textarea
                v-model="question.commentController.value.value"
                :maxlength="question.commentController.maxLength.value"
                :minlength="question.commentController.minLength.value"
                :placeholder="question.commentController.placeholderText.value"
                :is-invalid="question.commentController.error.value"
              />
            </FormGroup>
          </div>
        </SlideTransition>
        <div
          v-if="toValue(question.skip) && !toValue(question.skipVariant)"
          class="skip-container skip-container--matrix-question"
          :class="[{ 'matrix-question--skip-all': !hasScroll && !errorScroll && !question.error.value },
                   { 'matrix-table-question--skip--tablemod': matrixType === 'marker' },
          ]"
        >
          <Check
            v-model="question.skipped.value"
            :label="question.skipText.value"
          />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.matrix-question--skip-all {
  margin-top: 10px !important;
}

.matrix-question {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
}

.matrix-question :global(.simplebar-content-wrapper) {
  -webkit-overflow-scrolling: auto !important;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

.matrix-question:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 15px;
  z-index: 1;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(90deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
  transition: opacity 0.3s;
  display: var(--show-gradient-shadow, none);
}

.matrix-question:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 15px;
  z-index: 1;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, var(--fqz-poll-main-place-color) 100%);
  transition: opacity 0.3s;
  display: var(--show-gradient-shadow, none);
}

.matrix-question:not(.matrix-question--has-scroll):before,
.matrix-question:not(.matrix-question--has-scroll):after {
  display: none;
}

.matrix-question--error-block {
  padding-bottom: 20px;
}

.matrix-question--has-scroll {
  /* padding-bottom: 20px; */
}

.matrix-question--reached-scroll-start:before {
  opacity: 0;
}

.matrix-question--reached-scroll-end:after {
  opacity: 0;
}
.matrix-question:not(.matrix-question--dropdown) .simplebar-themed {
  padding-bottom: 21px;
}

.matrix-question--dropdown .simplebar-scrollable-y:before,
.matrix-question--dropdown .simplebar-scrollable-y:after {
  display: none;
}

:global(.matrix-question--dropdown .simplebar-track) {
  display: none;
}

.matrix-question--dropdown .matrix-question__table {
  padding-top: 0;
}

:global(.matrix-question--dropdown .simplebar-themed) {
  overflow: visible !important;
}

:global(.matrix-question--dropdown .simplebar-content-wrapper) {
  overflow: visible !important;
}

.matrix-question--has-scroll .simplebar-themed {
  padding-bottom: 31px;
}

.matrix-question :global(.simplebar-track) {
  z-index: 3;
}

.matrix-question :global(.simplebar-mask) {
  z-index: auto !important;
}

.matrix-question--skipped .matrix-question__row-title {
  opacity: 0.7;
}

.matrix-question--skipped .matrix-question__scale {
  opacity: 0.5;
}

.matrix-question__row--skipped .matrix-question__row-title {
  opacity: 0.7;
}

.matrix-question__row--skipped .matrix-question__scale {
  opacity: 0.5;
}

.matrix-question__comment-form-group {
  margin-top: 30px;
}

.matrix-question__table {
  min-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  display: flex;
  flex-direction: column;
  gap: 21px;
  padding-top: 9px;
}

.matrix-question--simplified .matrix-question__table {
  padding-top: 0;
}

.matrix-question__row {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  padding-bottom: 20px;
}

.matrix-question__row:before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: calc(100% + 20px);
  background-color: rgba(255, 0, 0, 0.2);
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.3s;
  transform: translateX(var(--scroll-left));
  z-index: 7;
  pointer-events: none;
}

.matrix-question__row--error:not(.matrix-question__row--dropdown):before {
  opacity: 1;
}

.matrix-question__row--single:before {
  height: calc(100% + 20px) !important;
}

.matrix-question__row:first-child:before {
  top: -9px;
  height: calc(100% + 9px);
}

.matrix-question__row:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  min-width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
  transform: translateX(var(--scroll-left));
  z-index: 7;
  transition:
    opacity 0.3s,
    transform 0.1s;
}

.matrix-question__row:last-child {
  padding-bottom: 0;
}

.matrix-question__row:last-child::after {
  display: none;
}

.matrix-question__row:last-child:before {
  height: calc(100% + 30px);
}

.matrix-question__row--three-or-less-cols .matrix-question__cell {
  min-width: none;
  max-width: none;
}

.matrix-question__row-title {
  margin-bottom: 10px;
  font-size: var(--fqz-poll-font-size);
  color: var(--fqz-poll-text-on-place);
  font-family: var(--fqz-poll-font-family);
  transition: opacity 0.3s;
  transform: translateX(var(--scroll-left));
  position: relative;
  z-index: 5;
  transition:
    opacity 0.3s,
    transform 0.1s;
}

.matrix-question__scale {
  display: flex;
  flex-wrap: nowrap;
  gap: 2px;
  min-width: 100%;
  transition: opacity 0.3s;
}

/* .matrix-question__scale::-webkit-scrollbar {
  display: none;
} */

.matrix-question__cell {
  flex: 1 0 auto;
  min-width: 60px;
  max-width: 192px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  font-size: 13px;
  line-height: 1.1;
  background-color: transparent;
  color: var(--fqz-poll-text-on-place);
  border: none;
  cursor: pointer;
  transition: opacity 0.3s ease;
  position: relative;
  border-radius: 4px;
  border: none;
  overflow: hidden;
}

.matrix-question__cell-text {
  position: relative;
  z-index: 1;
}

.matrix-question__cell:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
  transition:
    background-color 0.3s,
    opacity 0.3s;
}

.matrix-question__cell:last-child {
  margin-right: 0;
}

.matrix-question__cell--selected {
  color: white;
}

.matrix-question__cell--selected:after {
  opacity: 1;
  background-color: var(--fqz-poll-main-color);
}

.matrix-question__cell:not(.matrix-question__cell--selected):hover:after {
  opacity: 0.2;
}

.matrix-question__skip {
  margin-top: 20px;
  transform: translateX(var(--scroll-left));
  position: relative;
  z-index: 3;
  transition:
    opacity 0.3s,
    transform 0.1s;
}

.matrix-question__variants {
  margin-top: 20px;
  transform: translateX(var(--scroll-left));
  z-index: 5;
  position: relative;
}

:global(.matrix-question__error) {
  margin-top: 0 !important;
  text-align: center;
}

:global(.survey-questions__wrapper_pre--dropdown .matrix-question__error) {
  margin-top: 16px !important;
  text-align: center;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.matrix-question__select {
  width: 100%;
  transition: opacity 0.3s;
}

.matrix-question--skipped .matrix-question__select,
.matrix-question__row--skipped .matrix-question__select {
  opacity: 0.7;
}

@media screen and (max-width: 679px) {
  .matrix-question__row-title {
    font-size: 14px;
  }

  .matrix-question--skip-all {
    margin-top: 30px !important;
  }

  .matrix-question--error-block-mobile {
    padding-bottom: 20px;
  }

  .matrix-question__cell {
    min-width: 50px;
    min-height: 36px;
    flex-grow: 1;
    max-width: 122px;
    padding: 5px 10px;
  }
}
</style>
