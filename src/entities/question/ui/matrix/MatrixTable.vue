<script setup>
import Variants from '@entities/question/ui/Variants.vue'
import { useTabletStore } from '@shared/store/useTabletStore.ts'
import Check from '@shared/ui/Check.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import { useResizeObserver } from '@vueuse/core'
import debounce from 'lodash.debounce'
import { computed, onBeforeUnmount, onMounted, ref, toValue, watch } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const hasScrollModel = defineModel('error')

const tabletStore = useTabletStore()

const PREVIEW_ROW_PLACEHOLDER_TEXT = '__foquz__empty'

const isTabletMode = computed(() => tabletStore.isTabletMode)
const isSkipped = computed(() => props.question.skipped.value)
const reachedScrollStart = ref(false)
const reachedScrollEnd = ref(false)
const hasScroll = ref(false)
const rowTitleRefs = ref([])

const columnNamesHeight = ref(0)

const inputType = computed(() => Number(props.question.matrixSettings.value.multiple_choice) === 0 ? 'radio' : 'checkbox')

const containerClasses = computed(() => {
  return {
    'matrix-table-question': true,
    'matrix-table-question--has-scroll': hasScroll.value,
    'matrix-table-question--skipped': isSkipped.value,
    'matrix-table-question--reached-scroll-start': reachedScrollStart.value,
    'matrix-table-question--reached-scroll-end': reachedScrollEnd.value,
  }
})

function getRowClasses(row) {
  return {
    'matrix-table-question__row': true,
    'matrix-table-question__row--skipped': row.skipped.value,
  }
}

const columnSkipped = computed(() => {
  return props.question.rows.value.every(row => row.skipped.value)
})

const scrollElementLeft = ref(0)
const scrollElementWidth = ref(0)

function setScrollVisualBoundaries(el) {
  const scrollLeft = el.scrollLeft
  const clientWidth = el.clientWidth
  const scrollWidth = el.scrollWidth
  const reachedStart = scrollLeft <= 2
  const reachedEnd = Math.abs(scrollLeft + clientWidth - scrollWidth) < 1
  scrollElementLeft.value = scrollLeft
  hasScroll.value = el.scrollWidth > el.clientWidth
  scrollElementWidth.value = el.clientWidth
  if (reachedStart !== reachedScrollStart.value || reachedEnd !== reachedScrollEnd.value) {
    reachedScrollStart.value = reachedStart
    reachedScrollEnd.value = reachedEnd
  }
}

function onScroll(evt) {
  setScrollVisualBoundaries(evt.target)
}

const simplebarRef = ref(null)
onMounted(() => {
  const scrollElement = simplebarRef.value?.scrollElement
  if (scrollElement) {
    setScrollVisualBoundaries(scrollElement)
  }
})

useResizeObserver(simplebarRef, () => {
  const scrollElement = simplebarRef.value?.scrollElement
  if (scrollElement) {
    setScrollVisualBoundaries(scrollElement)
  }
})

const columnNamesRef = ref(null)
useResizeObserver(columnNamesRef, (entries) => {
  const height = entries[0].contentRect.height
  if (height) {
    const fixedHeight = Number.parseFloat(height.toFixed(4))
    if (fixedHeight !== columnNamesHeight.value) {
      columnNamesHeight.value = Number.parseFloat(fixedHeight + 1).toFixed(1)
    }
  }
})

/**
 * Стили для контейнера
 */
const getContainerStyles = computed(() => {
  const styles = {}
  if (columnNamesHeight.value) {
    styles['--matrix-table-question-rows-height'] = `${columnNamesHeight.value || 0}px`
  }

  if (hasScroll.value && scrollElementWidth.value) {
    styles['--matrix-table-question-content-width'] = `${scrollElementWidth.value}px`
  }

  if (hasScroll.value && scrollElementLeft.value) {
    styles['--matrix-table-question-content-left'] = `${scrollElementLeft.value}px`
  }

  return styles
})

const variantsRowInnerRef = ref(null)
const variantsRowHeights = ref([])
const variantCellRef = ref(null)
const rowTitleTextRef = ref([])
const bodyEl = ref(null)
const containerRef = ref(null)

const containerWidth = ref(0)

const cellPaddingFromCssVariable = ref(20)

function onCellClick(row, colIndex) {
  const rowElement = variantsRowInnerRef.value[row.position]
  const cellList = rowElement.querySelectorAll('.matrix-table-question__variant-cell')
  const cell = cellList[colIndex]

  if (cell) {
    cell.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'center',
    })
  }

  if (inputType.value === 'radio') {
    if (row.selectedCols.value.includes(colIndex)) {
      return
    }
  }
  props.question.toggleCol(row, colIndex)
}

function calculateContainerWidth() {
  if (containerRef.value) {
    const width = containerRef.value.offsetWidth
    containerWidth.value = width
    containerRef.value.style.setProperty(
      '--matrix-table-question-container-width',
      `${width}px`,
    )
  }
}

/**
 * Calculates and updates the height for a specific row
 */
function calculateRowHeight(rowEl, rowIndex, forceRecalculate = false) {
  if (!rowEl)
    return

  if (forceRecalculate) {
    const containerEl = containerRef.value
    if (containerEl) {
      containerEl.style.removeProperty(`--matrix-table-question-row-height-${rowIndex + 1}`)
    }
    void rowEl.offsetHeight // Trigger reflow
  }

  // Получаем элементы строки
  const titleTextEl = rowEl.querySelector('.matrix-table-question__row-title-text')
  const variantsSkipEl = rowEl.querySelector('.matrix-table-question__variants-skip')
  const variantsInputEl = rowEl.querySelector('.matrix-table-question__variants-input')
  const skipRowEl = rowEl.querySelector('.matrix-table-question__skip-row:not(.matrix-table-question__skip-row--placeholder)')

  // Рассчитываем высоту компонентов
  const titleHeight = titleTextEl?.offsetHeight || 0
  const variantsSkipHeight = variantsSkipEl?.offsetHeight || 0
  const variantsInputHeight = variantsInputEl?.offsetHeight || 0
  const skipRowHeight = skipRowEl?.offsetHeight || 0

  // Учитываем отступы строки
  const rowStyles = window.getComputedStyle(rowEl)
  const paddingTop = Number.parseFloat(rowStyles.paddingTop) || 0
  const paddingBottom = Number.parseFloat(rowStyles.paddingBottom) || 0
  const totalPadding = paddingTop + paddingBottom

  // Базовая высота - заголовок + отступы
  let totalHeight = titleHeight + totalPadding

  // Добавляем высоту вариантов и skip ряда (если есть)
  if (variantsSkipEl) {
    totalHeight = Math.max(totalHeight, titleHeight + variantsSkipHeight + totalPadding)
  }
  else {
    totalHeight += variantsInputHeight
    if (skipRowEl) {
      const skipStyles = window.getComputedStyle(skipRowEl)
      const skipBottom = Number.parseFloat(skipStyles.bottom) || 0
      totalHeight += skipRowHeight + skipBottom
    }
  }

  if (titleHeight > 0) {
    if (isTabletMode.value) {
      totalHeight = Math.max(totalHeight, 95)
    }
    else {
      totalHeight = Math.max(totalHeight, 65)
    }
  }

  // Обновляем высоту только если она изменилась
  if (forceRecalculate || totalHeight !== variantsRowHeights.value[rowIndex]) {
    variantsRowHeights.value[rowIndex] = totalHeight
    const containerEl = containerRef.value
    if (containerEl) {
      containerEl.style.setProperty(
        `--matrix-table-question-row-height-${rowIndex + 1}`,
        `${totalHeight}px`,
      )
    }
  }
}

function recalculateAllRows() {
  if (variantsRowInnerRef.value && Array.isArray(variantsRowInnerRef.value)) {
    variantsRowInnerRef.value.forEach((rowEl, index) => {
      calculateRowHeight(rowEl, index, true)
    })
  }
}

function shouldShowRowTitle(row) {
  const title = toValue(row.rawTitle) || ''
  return !title.startsWith(PREVIEW_ROW_PLACEHOLDER_TEXT)
}

const recalculateAllRowsDebounced = debounce(recalculateAllRows, 100)

onMounted(() => {
  bodyEl.value = document.body

  // Получаем значение padding из CSS переменной
  if (containerRef.value) {
    const cellPadding = window.getComputedStyle(containerRef.value).getPropertyValue('--variant-cell-padding-y')
    cellPaddingFromCssVariable.value = Number.parseFloat(cellPadding) || 20
  }

  // Настраиваем ResizeObserver для строк и их содержимого
  if (rowTitleRefs.value && Array.isArray(rowTitleRefs.value)) {
    rowTitleRefs.value.forEach((rowEl, index) => {
      // Наблюдаем за изменением размеров всей строки
      useResizeObserver(rowEl, () => calculateRowHeight(rowEl, index))

      // Наблюдаем за изменением размеров заголовка строки
      const titleTextEl = rowEl.querySelector('.matrix-table-question__row-title-text')
      if (titleTextEl) {
        useResizeObserver(titleTextEl, () => calculateRowHeight(rowEl, index))
      }

      // Наблюдаем за изменением размеров блока variants-skip
      const variantsSkipEl = rowEl.querySelector('.matrix-table-question__variants-skip')
      if (variantsSkipEl) {
        useResizeObserver(variantsSkipEl, () => calculateRowHeight(rowEl, index))
      }
      else {
        // Если variants-skip нет, наблюдаем за отдельными компонентами
        const variantsInputEl = rowEl.querySelector('.matrix-table-question__variants-input')
        if (variantsInputEl) {
          useResizeObserver(variantsInputEl, () => {
            setTimeout(() => calculateRowHeight(rowEl, index), 100)
          })
        }

        const skipRowEl = rowEl.querySelector('.matrix-table-question__skip-row')
        if (skipRowEl && !skipRowEl.classList.contains('matrix-table-question__skip-row--placeholder')) {
          useResizeObserver(skipRowEl, () => calculateRowHeight(rowEl, index))
        }
      }
    })
  }

  // Наблюдаем за изменением размеров ячеек (для второго столбца)
  if (variantCellRef.value?.length > 0) {
    variantCellRef.value.forEach((cellEl) => {
      useResizeObserver(cellEl, (entries) => {
        const height = entries[0].contentRect.height
        if (height) {
          const fixedHeight = Number.parseFloat(height.toFixed(4))
          cellEl.style.setProperty(`--height`, `${fixedHeight}px`)
        }
      })
    })
  }

  calculateContainerWidth()
  useResizeObserver(containerRef, calculateContainerWidth)

  if (hasScroll.value) {
    // window.addEventListener('resize', recalculateAllRowsDebounced)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', recalculateAllRowsDebounced)
})

function calculateColumnWidths() {
  if (!columnNamesRef.value || !containerRef.value)
    return

  const columns = columnNamesRef.value.querySelectorAll('.matrix-table-question__column-title')

  columns.forEach((colEl, index) => {
    const width = colEl.offsetWidth
    containerRef.value.style.setProperty(
      `--matrix-table-column-width-${index}`,
      `${width}px`,
    )
  })
}
const columnStyles = computed(() => {
  const colCount = toValue(props.question.cols.value).length

  if (colCount <= 3) {
    return { width: `calc(${100 / colCount}% - 10px)` }
  }

  return isTabletMode.value
    ? { 'min-width': '78px', 'max-width': '283px' }
    : { 'min-width': '60px', 'max-width': '192px' }
})

useResizeObserver(columnNamesRef, calculateColumnWidths)

watch(hasScroll, (newHasScroll) => {
  hasScrollModel.value = newHasScroll
})

function getRowHeight(rowIndex) {
  if (!rowTitleTextRef.value[rowIndex]) {
    return 0
  }

  return rowTitleTextRef.value[rowIndex].offsetHeight
}
</script>

<template>
  <div>
    <div ref="containerRef" :class="containerClasses" :style="getContainerStyles">
      <div class="matrix-table-question__rows">
        <div class="matrix-table-question__row-placeholder" :style="{ height: `${columnNamesHeight}px` }">
          <div class="matrix-table-question__row-placeholder-text" />
        </div>
        <div
          v-for="(row, rowIndex) in question.rows.value"
          :key="row.id"
          ref="rowTitleRefs"
          class="matrix-table-question__row-title"
          :class="getRowClasses(row)"
          :style="{ 'min-height': `var(--matrix-table-question-row-height-${rowIndex + 1})` }"
        >
          <div class="matrix-table-question__row-title-content">
            <div
              v-if="shouldShowRowTitle(row)"
              ref="rowTitleTextRef"
              class="matrix-table-question__row-title-text"
              :class="{ 'matrix-table-question__row--padding-min': getRowHeight(rowIndex) < 20 }"
            >
              <span>{{ row.title }}</span>
            </div>
          </div>
          <div class="matrix-table-question__variants-skip">
            <SlideTransition>
              <Variants
                v-if="row.variantsController && row.variantsController.enabled.value"
                :variants-controller="row.variantsController"
                custom-class="matrix-table-question__variants-input"
                :class="{ 'matrix-table-question__row--padding-min': getRowHeight(rowIndex) < 20 }"
              />
            </SlideTransition>

            <Check
              v-if="question.skipVariant.value"
              :model-value="row.skipped.value"
              :label="question.skipText.value"
              class="matrix-table-question__skip-row"
              :class="{ 'matrix-table-question__row--padding-min': getRowHeight(rowIndex) < 20 }"
              @update:model-value="question.toggleSkip(row)"
            />
          </div>
        </div>
      </div>
      <div
        v-if="question.cols.value.length > 0"
        class="matrix-table-question__columns-with-variants"
      >
        <simplebar ref="simplebarRef" data-simplebar-auto-hide="false" class="simplebar-custom simplebar-themed" @scroll="onScroll">
          <div class="matrix-table-question__content">
            <div ref="columnNamesRef" class="matrix-table-question__column-names">
              <div
                v-for="(column, colIndex) in toValue(question.translatedCols)"
                :key="colIndex"
                class="matrix-table-question__column-title"
                :class="{ 'matrix-table-question__column-skipped': columnSkipped }"
                :style="{
                  '--column-width': `var(--matrix-table-column-width-${colIndex})`,
                  ...columnStyles,
                }"
              >
                <div class="matrix-table-question__column-title-text">
                  <span>
                    {{ column }}
                  </span>
                </div>
              </div>
            </div>
            <div class="matrix-table-question__variants">
              <div
                v-for="(row, rowIndex) in question.rows.value"
                :key="row.id"
                class="matrix-table-question__variants-row"
                :style="{ 'min-height': `var(--matrix-table-question-row-height-${rowIndex + 1})` }"
              >
                <div ref="variantsRowInnerRef" class="matrix-table-question__variants-row-inner">
                  <div
                    v-if="shouldShowRowTitle(row)"
                    class="matrix-table-question__variant-cell-row-name"
                  >
                    <span>
                      {{ row.title }}
                    </span>
                  </div>
                  <div class="matrix-table-question__variant-cell-list">
                    <div
                      v-for="(column, colIndex) in toValue(question.translatedCols)"
                      :key="colIndex"
                      ref="variantCellRef"
                      class="matrix-table-question__variant-cell"
                      :style="{
                        width: `var(--matrix-table-column-width-${colIndex})`,
                        flex: '0 0 auto',
                        ...columnStyles,
                      }"
                    >
                      <div class="matrix-table-question__variant-cell-content">
                        <div
                          class="matrix-table-question__variant-cell-column-name"
                          @click="onCellClick(row, colIndex)"
                        >
                          <span>
                            {{ column }}
                          </span>
                        </div>
                        <Check
                          :model-value="row.selectedCols.value"
                          :value="colIndex"
                          :type="inputType"
                          :inactive="row.skipped.value"
                          :invalid="row.error.value"
                          :check-attrs="{ 'data-testid': 'variants-check' }"
                          class="matrix-table-question__variant-cell-input"
                          @update:model-value="onCellClick(row, colIndex)"
                        />
                      </div>
                    </div>
                  </div>
                  <SlideTransition>
                    <Variants
                      v-if="row.variantsController && row.variantsController.enabled.value"
                      :variants-controller="row.variantsController"
                      custom-class="matrix-table-question__variants-input matrix-table-question__variants-input--mobile"
                    />
                  </SlideTransition>
                  <Check
                    v-if="question.skipVariant.value"
                    :model-value="row.skipped.value"
                    :label="question.skipText.value"
                    class="matrix-table-question__skip-row matrix-table-question__skip-row--mobile"
                    @update:model-value="question.toggleSkip(row)"
                  />
                </div>
              </div>
            </div>
          </div>
        </simplebar>
      </div>
    </div>
  </div>
</template>

<style scoped>
.matrix-table-question {
  display: flex;
  width: 100%;
  padding-bottom: 3px;
  --rows-width: 160px;
  --variant-cell-padding-y: 20px;
  --success-check-shift-x: 7px;
}

.matrix-table-question__skip-row--placeholder {
  visibility: hidden;
  height: 0;
  margin: 0;
  padding: 0;
}

.matrix-table-question__row--padding-min {
  margin-bottom: 7px;
}

.matrix-table-question__skip-row--mobile,
.matrix-table-question__variants-input--mobile {
  display: none;
}

.matrix-table-question__variants-skip {
  min-width: var(--matrix-table-question-container-width);
  z-index: 98;
  position: absolute;
}

.matrix-table-question__variants-input {
  /* margin-top: 30px; */
  margin-top: 20px;
  z-index: 5;
}

.matrix-table-question__rows {
  flex: 0 0 var(--rows-width);
  display: flex;
  flex-direction: column;
}

.matrix-table-question__row-title-text {
  /* margin-bottom: 10px; */
}

.matrix-table-question__row--skipped .matrix-table-question__row-title-text {
  opacity: 0.7;
}

.matrix-table-question__column-skipped .matrix-table-question__column-title-text {
  opacity: 0.7;
}

.matrix-table-question__row-placeholder,
.matrix-table-question__row-title {
  display: flex;
  font-size: 13px;
  line-height: 1.1;
  position: relative;
}

.matrix-table-question__row-title {
  display: inline-block;
}

.matrix-table-question__row-placeholder:after,
.matrix-table-question__row-title:after {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
}

.matrix-table-question__row-placeholder-text {
  margin-top: auto;
  line-height: 1.2;
}

.matrix-table-question__row-title:last-child:after {
  display: none;
}

.matrix-table-question__row-placeholder-text {
  padding-bottom: 15px;
  margin-top: auto;
}

.matrix-table-question__row-title {
  position: relative;
  padding: 20px 10px 20px 0;
  min-height: calc(var(--min-row-height, 0px) + 40px);
}

.matrix-table-question__columns-with-variants {
  flex: 1 1 auto;
  overflow-x: auto;
  position: relative;
}

.matrix-table-question :global(.matrix-table-question__error) {
  margin-top: 20px !important;
  text-align: center;
}

.matrix-table-question__column-names {
  display: flex;
  text-align: left;
  gap: 10px;
  position: relative;
}

.matrix-table-question__column-names:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  transform: translateX(var(--matrix-table-question-content-left, 0));
  z-index: 11;
  opacity: 0.1;
}

.matrix-table-question__column-title {
  text-align: center;
  font-size: 13px;
  line-height: 1.2;
  padding-bottom: 15px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  flex: 1 0 auto;
}

.matrix-table-question__variant-cell-input {
  display: flex;
  justify-content: center;
}

.matrix-table-question__variant-cell-input:deep(.fc-check__box) {
  margin: 0;
}

.matrix-table-question--rows-above-variants .matrix-table-question__column-title {
  padding-bottom: 10px;
}

.matrix-table-question--rows-above-variants .matrix-table-question__variants-row {
  min-height: 0 !important;
}

.matrix-table-question__variants {
  display: flex;
  flex-direction: column;
}

.matrix-table-question__variants-row {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  padding: 20px 0;
}

.matrix-table-question__variants-row:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  transform: translateX(var(--matrix-table-question-content-left, 0));
  opacity: 0.1;
  z-index: 10;
}

.matrix-table-question__variants-row:last-of-type:after {
  display: none;
}

.matrix-table-question__variants-row-inner {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 1px;
}

.matrix-table-question__variant-cell-row-name,
.matrix-table-question__variant-cell-column-name {
  display: none;
}

.matrix-table-question__variant-cell-row-name {
  font-size: 15px;
  line-height: 1.1;
  font-weight: 700;
  transform: translateX(var(--matrix-table-question-content-left, 0));
  position: relative;
  margin-bottom: 20px;
  z-index: 10;
}

.matrix-table-question__variant-cell-row-name--visible {
  display: block;
}

.matrix-table-question__variant-cell-list {
  display: flex;
  gap: 10px;
  position: relative;
}

.matrix-table-question__skip-row {
  white-space: nowrap;
  z-index: 100;
  /* transform: translateX(var(--matrix-table-question-content-left, 0)); */
  position: relative;
  margin-top: 20px;
  font-size: 13px;
  line-height: 1.1;
}

.matrix-table-question:not(.matrix-table-question--forbid-scroll) {
  margin-bottom: -3px;
}

.matrix-table-question:not(.matrix-table-question--forbid-scroll) :global(.simplebar-content-wrapper) {
  padding-right: var(--success-check-shift-x);
}

.matrix-table-question--has-scroll .simplebar-themed {
  padding-bottom: 8px;
}

.matrix-table-question--has-scroll :global(.simplebar-track.simplebar-horizontal) {
  bottom: 4px;
  width: calc(100% - 4px);
}

.matrix-table-question__skip-option {
  padding: 5px 10px;
  color: rgba(0, 0, 0, 0.5);
}

.matrix-table-question__columns-with-variants:before,
.matrix-table-question__columns-with-variants:after {
  content: '';
  position: absolute;
  top: 0;
  width: 15px;
  z-index: 10;
  height: 100%;
  pointer-events: none;
  transition: opacity 0.3s;
  display: var(--show-gradient-shadow, none);
}

.matrix-table-question__columns-with-variants:before {
  left: 0;
  background: linear-gradient(90deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
}

.matrix-table-question__columns-with-variants:after {
  right: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, var(--fqz-poll-main-place-color) 100%);
}

.matrix-table-question:not(.matrix-table-question--has-scroll) .matrix-table-question__columns-with-variants:before,
.matrix-table-question:not(.matrix-table-question--has-scroll) .matrix-table-question__columns-with-variants:after {
  display: none;
}

.matrix-table-question--reached-scroll-start .matrix-table-question__columns-with-variants:before {
  opacity: 0;
}

.matrix-table-question--reached-scroll-end .matrix-table-question__columns-with-variants:after {
  opacity: 0;
}

.matrix-table-question :global(.simplebar-track) {
  z-index: 3;
}

.matrix-table-question :global(.simplebar-mask) {
  z-index: auto !important;
}

.matrix-table-question__variant-cell {
  align-self: flex-start;
  flex: 0 1 auto;
  z-index: 99;
}

.matrix-table-question--skipped .matrix-table-question__row-title-text,
.matrix-table-question--skipped .matrix-table-question__column-title-text,
.matrix-table-question--skipped .matrix-table-question__variant-cell,
.matrix-table-question--skipped .matrix-table-question__variant-cell-row-name,
.matrix-table-question--skipped .matrix-table-question__variant-cell-column-name {
  opacity: 0.7;
}

:global(.matrix-table-question--forbid-scroll .matrix-table-question__columns-with-variants)
  :global(.simplebar-wrapper),
:global(.matrix-table-question--forbid-scroll .simplebar-content),
:global(.matrix-table-question--forbid-scroll .simplebar-mask),
:global(.matrix-table-question--forbid-scroll .simplebar-content-wrapper) {
  overflow: visible !important;
}

:global(.matrix-table-question--forbid-scroll .simplebar-track) {
  display: none;
}

:global(.matrix-table-question--forbid-scroll .simplebar-content-wrapper) {
  padding-right: 0 !important;
}

.matrix-table-question--forbid-scroll .matrix-table-question__columns-with-variants {
  min-width: 0;
}

.matrix-table-question--forbid-scroll .simplebar-themed {
  padding-bottom: 0;
}

.matrix-table-question--has-scroll:not(.matrix-table-question--forbid-scroll)
  .matrix-table-question__variant-cell:last-child {
  padding-right: var(--success-check-shift-x);
  box-sizing: border-box;
}

.matrix-table-question--forbid-scroll .matrix-table-question__variant-cell:last-child {
  padding-right: 0 !important;
}

/** remove gradient shadow */
.matrix-table-question--forbid-scroll .matrix-table-question__columns-with-variants:before,
.matrix-table-question--forbid-scroll .matrix-table-question__columns-with-variants:after {
  display: none;
}

.matrix-table-question--forbid-scroll .matrix-table-question__variant-cell-list {
  width: calc(100% + 10px);
}

.matrix-table-question--forbid-scroll {
  margin-bottom: -20px;
}

.matrix-table-question__comment-form-group {
  margin-top: 29px;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

@media (max-width: 679px) {
  .matrix-question--simplified {
    .matrix-table-question {
      overflow: hidden;
    }
  }

  .matrix-table-question__variant-select-anchor,
  .matrix-table-question__column-names,
  .matrix-table-question__rows {
    display: none;
  }

  .matrix-table-question__skip-row--mobile,
  .matrix-table-question__variants-input--mobile {
    display: block;
  }

  .matrix-table-question__variants-input {
    position: relative;
    min-width: 100%;
  }

  .matrix-table-question__variants {
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .matrix-table-question__variants-row {
    flex-direction: column;
    gap: 10px;
    padding: 0;
    width: 100%;
    padding-bottom: 20px;
    min-height: 0 !important;
  }
  .matrix-table-question__variants-row:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .matrix-table-question__variant-cell {
    width: 100% !important;
    padding: 0;
  }

  .matrix-table-question__variant-cell:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .matrix-table-question__variant-cell-row-name,
  .matrix-table-question__variant-cell-column-name {
    display: block;
  }

  .matrix-table-question__variant-cell-content {
    display: flex;
    gap: 15px;
    width: 100%;
    align-items: flex-start;
  }

  .matrix-table-question__variant-cell-row-name {
    font-weight: 700;
    font-size: 14px;
    line-height: 1.1;
    margin-bottom: 15px;
  }

  .matrix-table-question__variant-cell-column-name {
    font-size: 14px;
    line-height: 1.2;
    flex: 0 0 270px;
  }

  .matrix-table-question__variant-cell-input {
    order: -1;
  }

  .matrix-table-question__variant-cell-list {
    flex-direction: column;
    gap: 15px;
  }

  .matrix-table-question__skip-row {
    margin-top: 20px;
    bottom: 0;
    z-index: 10;
    position: relative;
  }

  .matrix-table-question__skip-row :deep(.fc-check__label) {
    font-size: 14px;
    line-height: 1.1;
    padding-top: 4px;
  }

  .matrix-table-question__skip-container {
    margin-top: 30px;
  }

  .matrix-table-question--forbid-scroll .matrix-table-question__variant-cell-list {
    width: auto;
  }

  .matrix-table-question--forbid-scroll {
    margin-bottom: 0;
  }

  .matrix-table-question:not(.matrix-table-question--forbid-scroll) {
    margin-bottom: 0;
  }

  .matrix-table-question .matrix-table-question__columns-with-variants {
    overflow: visible !important;
    min-width: 0;
  }

  .matrix-table-question .matrix-table-question__columns-with-variants:after,
  .matrix-table-question .matrix-table-question__columns-with-variants:before {
    display: none;
  }

  :global(.matrix-table-question .simplebar-content-wrapper) {
    padding-right: 0 !important;
  }

  .matrix-table-question__variant-cell:last-child {
    padding-right: 0 !important;
  }

  :global(.matrix-table-question .matrix-table-question__columns-with-variants),
  :global(.matrix-table-question .simplebar-wrapper),
  :global(.matrix-table-question .simplebar-content),
  :global(.matrix-table-question .simplebar-mask),
  :global(.matrix-table-question .simplebar-content-wrapper) {
    overflow: visible !important;
  }

  :global(.matrix-table-question .simplebar-track) {
    display: none;
  }

  :global(.matrix-table-question__variant-cell-row-name .hint-trigger),
  :global(.matrix-table-question__variant-cell-column-name .hint-trigger) {
    margin-top: -1px;
    vertical-align: middle;
  }
}
</style>
