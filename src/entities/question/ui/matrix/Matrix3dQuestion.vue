<script setup>
import { useTabletStore } from '@shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import Hint from '@shared/ui/Hint.vue'
import FcSelect from '@shared/ui/Select/FcSelect.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { useResizeObserver } from '@vueuse/core'
import { computed, nextTick, onMounted, ref, toValue, watch } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const tabletStore = useTabletStore()
const isTabletMode = computed(() => tabletStore.isTabletMode)

const isSkipped = computed(() => props.question.skipped.value)
const reachedScrollStart = ref(false)
const reachedScrollEnd = ref(false)
const scrollElementLeft = ref(0)
const scrollElementWidth = ref(0)
const hasScroll = ref(false)

const columnNamesHeight = ref(0)

const columnCount = computed(() => props.question.columns.value.length)

const containerClasses = computed(() => {
  const rowsAboveVariants = props.question.rowsAboveVariants.value
  const forbidScroll = rowsAboveVariants ? columnCount.value <= 4 : columnCount.value <= 3
  return {
    'matrix-3d-question': true,
    'matrix-3d-question--has-scroll': hasScroll.value,
    'matrix-3d-question--skipped': isSkipped.value,
    'matrix-3d-question--reached-scroll-start': reachedScrollStart.value,
    'matrix-3d-question--reached-scroll-end': reachedScrollEnd.value,
    'matrix-3d-question--rows-above-variants': props.question.rowsAboveVariants.value,
    'matrix-3d-question--forbid-scroll': forbidScroll,
  }
})

const columnStyles = computed(() => {
  if (columnCount.value <= 3 && !props.question.rowsAboveVariants.value) {
    const smallShift = isTabletMode.value ? 0 : 10
    const width = `calc(${100 / columnCount.value}% - ${smallShift}px)`
    return { width, flex: '0 0 auto' }
  }
  else if (props.question.rowsAboveVariants.value && columnCount.value <= 4) {
    const smallShift = isTabletMode.value ? 0 : 10
    const width = `calc(${100 / columnCount.value}% - ${smallShift}px)`
    return { width, flex: '0 0 auto' }
  }

  const width = isTabletMode.value ? '195px' : '130px'
  return { width, flex: '0 0 auto' }
})

function setScrollVisualBoundaries(el) {
  const scrollLeft = el.scrollLeft
  const clientWidth = el.clientWidth
  const scrollWidth = el.scrollWidth
  const reachedStart = scrollLeft <= 2
  const reachedEnd = Math.abs(scrollLeft + clientWidth - scrollWidth) < 1
  scrollElementLeft.value = scrollLeft
  hasScroll.value = el.scrollWidth > el.clientWidth
  scrollElementWidth.value = el.clientWidth
  if (reachedStart !== reachedScrollStart.value || reachedEnd !== reachedScrollEnd.value) {
    reachedScrollStart.value = reachedStart
    reachedScrollEnd.value = reachedEnd
  }
}

function onScroll(evt) {
  setScrollVisualBoundaries(evt.target)
}

const simplebarRef = ref(null)
onMounted(() => {
  const scrollElement = simplebarRef.value?.scrollElement
  if (scrollElement) {
    setScrollVisualBoundaries(scrollElement)
  }
})

useResizeObserver(simplebarRef, () => {
  const scrollElement = simplebarRef.value?.scrollElement
  if (scrollElement) {
    setScrollVisualBoundaries(scrollElement)
  }
})

const columnNamesRef = ref(null)
useResizeObserver(columnNamesRef, (entries) => {
  const height = entries[0].contentRect.height
  if (height) {
    const fixedHeight = Number.parseFloat(height.toFixed(4))
    if (fixedHeight !== columnNamesHeight.value) {
      columnNamesHeight.value = fixedHeight + 1
    }
  }
})

const variantsRowInnerRef = ref(null)
const variantsRowHeights = ref([])
const variantCellRef = ref(null)
const rowTitleTextRef = ref(null)
const bodyEl = ref(null)
const containerRef = ref(null)
const selectedVariants = computed(() => props.question.selectedVariants.value)
const error = computed(() => props.question.error.value)

function scrollToFirstEmptyCell() {
  const [emptyRowIndex, emptyColumnIndex] = selectedVariants.value.reduce((acc, row) => {
    if (acc.length > 0) {
      return acc
    }

    const variantsLength = selectedVariants.value.length
    const rowLength = row.length

    for (let i = 0; variantsLength > i; i++) {
      for (let j = 0; rowLength > j; j++) {
        if (selectedVariants.value[i][j].length === 0) {
          return [i, j]
        }
      }
    }
    return acc
  }, [])

  if (emptyRowIndex !== undefined && emptyColumnIndex !== undefined) {
    const rowNumber = emptyRowIndex + 1
    const columnNumber = emptyColumnIndex + 1
    const finalIndex = rowNumber * columnNumber - 1
    const cellEl = variantCellRef.value?.[finalIndex]

    if (cellEl) {
      const selectTriggerEl = cellEl.querySelector('.select-trigger')
      if (selectTriggerEl) {
        selectTriggerEl.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'end' })
      }
    }
  }
}

const isTouched = computed(() => props.question.touched.value)

/**
 * Скролл до первой пустой инвалидной ячейки
 */
watch([isTouched, error], ([touched, err]) => {
  // Если есть ошибка и вопрос обязательный
  if (touched && err) {
    scrollToFirstEmptyCell()
  }
}, { deep: true })

const cellPaddingFromCssVariable = ref(20)

/**
 * Calculates and updates the height for a specific row
 */
function calculateRowHeight(rowEl, rowIndex) {
  const rowElHeight = rowEl?.offsetHeight || 0

  if (rowElHeight) {
    const totalPadding = cellPaddingFromCssVariable.value * 2
    const fixedHeight = Number.parseFloat(rowElHeight.toFixed(4))
    const rowTitleTextEl = rowTitleTextRef.value?.[rowIndex]
    const rowTitleTextHeight = rowTitleTextEl?.offsetHeight || 0
    const maxHeight = Math.max(fixedHeight, rowTitleTextHeight)

    // Update height if changed
    if (maxHeight !== variantsRowHeights.value[rowIndex]) {
      variantsRowHeights.value[rowIndex] = maxHeight
      const containerEl = containerRef.value
      if (containerEl) {
        containerEl.style.setProperty(`--matrix-3d-question-row-height-${rowIndex + 1}`, `${maxHeight + totalPadding}px`)
      }
    }
  }
}

onMounted(() => {
  bodyEl.value = document.body
  /**
   * Вычисление высоты строк
   */
  if (variantsRowInnerRef.value && Array.isArray(variantsRowInnerRef.value)) {
    variantsRowInnerRef.value.forEach((rowEl, index) => {
      useResizeObserver(rowEl, () => calculateRowHeight(rowEl, index))
    })

    if (variantCellRef.value?.length > 0) {
      // Вычисление высоты ячеек
      variantCellRef.value.forEach((cellEl) => {
        useResizeObserver(cellEl, (entries) => {
          const height = entries[0].contentRect.height
          if (height) {
            const fixedHeight = Number.parseFloat(height.toFixed(4))
            cellEl.style.setProperty(`--height`, `${fixedHeight}px`)
          }
        })
      })
    }
  }

  if (containerRef.value) {
    const cellPadding = window.getComputedStyle(containerRef.value).getPropertyValue('--variant-cell-padding-y')
    cellPaddingFromCssVariable.value = Number.parseFloat(cellPadding) || 20
  }
})

watch(props.question.rows, () => {
  variantsRowHeights.value = []
  nextTick().then(() => {
    if (variantsRowInnerRef.value && Array.isArray(variantsRowInnerRef.value)) {
      variantsRowInnerRef.value.forEach((rowEl, index) => {
        calculateRowHeight(rowEl, index)
      })
    }
  })
})

/**
 * Стили для контейнера
 */
const getContainerStyles = computed(() => {
  const styles = {}
  if (columnNamesHeight.value) {
    styles['--matrix-3d-question-rows-height'] = `${columnNamesHeight.value || 0}px`
  }

  if (hasScroll.value && scrollElementWidth.value) {
    styles['--matrix-3d-question-content-width'] = `${scrollElementWidth.value}px`
  }

  if (hasScroll.value && scrollElementLeft.value) {
    styles['--matrix-3d-question-content-left'] = `${scrollElementLeft.value}px`
  }

  return styles
})

/**
 * Скролл до первой открытой ячейки
 */
function onSelectOpen(open) {
  if (open) {
    const focusedElement = document.activeElement
    if (focusedElement) {
      focusedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'end' })
    }
    if (props.question.skipped.value) {
      props.question.skipped.value = false
    }
  }
}

/**
 * Проверка на ошибку
 */
function isSelectInvalid(rowIndex, columnIndex) {
  if (!props.question.error.value) {
    return false
  }
  const cell = props.question.getSelectedVariants(rowIndex, columnIndex)
  if (Array.isArray(cell) && cell.length === 0) {
    return true
  }
  return false
}
</script>

<template>
  <div>
    <form :id="`answer-form-${question.id}`">
      <div v-if="question.enableGallery.value" class="gallery-container">
        <Gallery
          :gallery="question.galleryController.gallery.value"
          :selectable="false"
          :inactive="isSkipped"
          type="default"
          :group-id="question.questionId"
        />
      </div>
      <FormGroup v-if="question.rows.value.length > 0" :error="question.error.value" :error-attrs="{ class: 'matrix-3d-question__error' }">
        <div ref="containerRef" :class="containerClasses" :style="getContainerStyles">
          <div v-if="!question.rowsAboveVariants.value" class="matrix-3d-question__rows">
            <div class="matrix-3d-question__row-placeholder" :style="{ height: `${columnNamesHeight}px` }">
              <div v-if="question.skipColumn.value" class="matrix-3d-question__row-placeholder-text">
                {{ question.skipText.value }}
              </div>
            </div>
            <div
              v-for="(row, rowIndex) in question.rows.value"
              :key="row.id"
              class="matrix-3d-question__row-title"
              :style="{ 'min-height': `var(--matrix-3d-question-row-height-${rowIndex + 1})` }"
            >
              <div ref="rowTitleTextRef" class="matrix-3d-question__row-title-text">
                <span>
                  {{ row.name }}
                </span>
                <Hint v-if="question.showTooltips.value && row.description" :text="row.description" />
              </div>
            </div>
          </div>
          <div v-if="question.columns.value.length > 0" class="matrix-3d-question__columns-with-variants">
            <simplebar ref="simplebarRef" data-simplebar-auto-hide="false" class="simplebar-custom simplebar-themed" @scroll="onScroll">
              <div class="matrix-3d-question__content">
                <div ref="columnNamesRef" class="matrix-3d-question__column-names">
                  <div
                    v-for="(column, columnIndex) in question.columns.value"
                    :key="column.id"
                    class="matrix-3d-question__column-title"
                    :style="columnStyles"
                  >
                    <div class="matrix-3d-question__column-title-text">
                      <span>
                        {{ column.name }}
                      </span>
                      <Hint v-if="question.showTooltips.value && column.description" :text="column.description" />
                    </div>
                    <Check
                      v-if="question.skipVariant.value && question.skipColumn.value"
                      :model-value="column.skipped"
                      class="matrix-3d-question__skip-column"
                      :label="question.rowsAboveVariants.value ? question.skipText.value : ''"
                      @update:model-value="question.toggleSkipColumn(columnIndex)"
                    />
                  </div>
                </div>
                <div class="matrix-3d-question__variants">
                  <div
                    v-for="(row, rowIndex) in question.rows.value"
                    :key="row.id"
                    class="matrix-3d-question__variants-row"
                    :style="{ 'min-height': `var(--matrix-3d-question-row-height-${rowIndex + 1})` }"
                  >
                    <div ref="variantsRowInnerRef" class="matrix-3d-question__variants-row-inner">
                      <div class="matrix-3d-question__variant-cell-row-name" :class="{ 'matrix-3d-question__variant-cell-row-name--visible': question.rowsAboveVariants.value }">
                        <span>
                          {{ row.name }}
                        </span>
                        <Hint v-if="question.showTooltips.value && toValue(row.description)" :text="row.description" />
                      </div>
                      <div class="matrix-3d-question__variant-cell-list">
                        <div
                          v-for="(column, columnIndex) in question.columns.value"
                          :key="column.id"
                          ref="variantCellRef"
                          class="matrix-3d-question__variant-cell"
                          :style="columnStyles"
                        >
                          <div class="matrix-3d-question__variant-cell-content">
                            <div class="matrix-3d-question__variant-cell-column-name">
                              <span>
                                {{ column.name }}
                              </span>
                              <Hint v-if="question.showTooltips.value && toValue(column.description)" :text="column.description" />
                            </div>
                            <FcSelect
                              :options="column.variants"
                              :multiple="question.multipleChoice.value"
                              :placeholder="question.selectPlaceholderText.value"
                              :success="true"
                              :full-width="true"
                              :searchable="true"
                              popover-content-class="matrix-3d-question__variant-select-popover-content"
                              trigger-class="matrix-3d-question__variant-select-trigger"
                              :model-value="question.getSelectedVariants(rowIndex, columnIndex)"
                              :invalid="isSelectInvalid(rowIndex, columnIndex)"
                              :tablet-view="isTabletMode"
                              @update:open="onSelectOpen"
                              @update:model-value="question.setSelectedVariants(rowIndex, columnIndex, $event)"
                            >
                              <template #anchor>
                                <div class="matrix-3d-question__variant-select-anchor" aria-hidden="true" />
                              </template>
                            </FcSelect>
                          </div>
                        </div>
                      </div>
                      <Check
                        v-if="question.skipVariant.value && question.skipRow.value"
                        v-model="row.skipped"
                        :label="question.skipText.value"
                        class="matrix-3d-question__skip-row"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </simplebar>
          </div>
        </div>
      </FormGroup>

      <SlideTransition>
        <div v-if="question.commentEnabled.value && !question.skipped.value">
          <FormGroup
            custom-class="matrix-3d-question__comment-form-group"
            :label="question.commentController.title.value"
            :error="question.commentController.error.value"
          >
            <Textarea
              v-model="question.commentController.value.value"
              :maxlength="question.commentController.maxLength.value"
              :minlength="question.commentController.minLength.value"
              :placeholder="question.commentController.placeholderText.value"
              :is-invalid="question.commentController.error.value"
            />
          </FormGroup>
        </div>
      </SlideTransition>

      <div v-if="question.skip.value && !question.skipVariant.value" class="matrix-3d-question__skip-container">
        <Check v-model="question.skipped.value" :label="question.skipText.value" />
      </div>
    </form>
  </div>
</template>

<style scoped>
.matrix-3d-question {
  display: flex;
  width: 100%;
  --rows-width: 160px;
  --variant-cell-padding-y: 20px;
  --success-check-shift-x: 7px;
}

.matrix-3d-question__rows {
  flex: 0 0 var(--rows-width);
  display: flex;
  flex-direction: column;
}

.matrix-3d-question__row-placeholder,
.matrix-3d-question__row-title {
  display: flex;
  font-size: 13px;
  line-height: 1.1;
  position: relative;
}

.matrix-3d-question__row-title {
  display: inline-block;
}

:global(.matrix-3d-question__row-title .hint-trigger),
:global(.matrix-3d-question__column-title .hint-trigger) {
  vertical-align: middle;
  margin-left: 10px;
  margin-top: -2px;
  height: 17px;
  width: 17px;
}

:global(.matrix-3d-question__variant-cell-row-name .hint-trigger),
:global(.matrix-3d-question__variant-cell-column-name .hint-trigger) {
  margin-left: 10px;
  margin-top: -2px;
  vertical-align: middle;
  padding: 0;
  height: 17px;
  width: 17px;
}

.matrix-3d-question__row-placeholder:after,
.matrix-3d-question__row-title:after {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  opacity: 0.1;
}

.matrix-3d-question__row-placeholder-text {
  margin-top: auto;
  line-height: 1.2;
}

.matrix-3d-question__row-title:last-child:after {
  display: none;
}

.matrix-3d-question__row-placeholder-text {
  padding-bottom: 15px;
  margin-top: auto;
}

.matrix-3d-question__row-title {
  padding: 20px 10px 20px 0;
}

.matrix-3d-question__columns-with-variants {
  flex: 1 1 auto;
  overflow-x: auto;
  position: relative;
}

.matrix-3d-question :global(.matrix-3d-question__error) {
  margin-top: 20px !important;
  text-align: center;
}

.matrix-3d-question__column-names {
  display: flex;
  text-align: left;
  gap: 10px;
  position: relative;
}

.matrix-3d-question__column-names:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  transform: translateX(var(--matrix-3d-question-content-left, 0));
  z-index: 11;
  opacity: 0.1;
}

.matrix-3d-question__column-title {
  font-size: 13px;
  line-height: 1.2;
  padding-bottom: 15px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.matrix-3d-question--rows-above-variants .matrix-3d-question__column-title {
  padding-bottom: 10px;
}

.matrix-3d-question--rows-above-variants .matrix-3d-question__variants-row {
  min-height: 0 !important;
}

.matrix-3d-question__variants {
  display: flex;
  flex-direction: column;
}

.matrix-3d-question__variants-row {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  padding: 20px 0;
}

.matrix-3d-question__variants-row:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--fqz-poll-text-on-place);
  transform: translateX(var(--matrix-3d-question-content-left, 0));
  opacity: 0.1;
  z-index: 10;
}

.matrix-3d-question__variants-row:last-of-type:after {
  display: none;
}

.matrix-3d-question__variants-row-inner {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.matrix-3d-question__variant-cell-row-name,
.matrix-3d-question__variant-cell-column-name {
  display: none;
}

.matrix-3d-question__variant-cell-row-name {
  font-size: 15px;
  line-height: 1.1;
  font-weight: 700;
  transform: translateX(var(--matrix-3d-question-content-left, 0));
  position: relative;
  margin-bottom: 20px;
  z-index: 10;
}

.matrix-3d-question__variant-cell-row-name--visible {
  display: block;
}

.matrix-3d-question__variant-cell-list {
  display: flex;
  gap: 10px;
  position: relative;
}

.matrix-3d-question__skip-column {
  margin-top: 10px;
}

.matrix-3d-question__skip-column :deep(.fc-check__label) {
  font-size: 13px;
  line-height: 1.1;
}

.matrix-3d-question__skip-row {
  transform: translateX(var(--matrix-3d-question-content-left, 0));
  z-index: 10;
  position: relative;
  margin-top: 20px;
  font-size: 13px;
  line-height: 1.1;
}

:global(.matrix-3d-question__variant-select-trigger) {
  font-size: 13px;
  scroll-margin-inline-end: var(--success-check-shift-x);
}

:global(.matrix-3d-question__variant-select-trigger > .select-trigger__icon) {
  margin-left: 10px;
}

.matrix-3d-question__variant-select-anchor {
  position: absolute;
  top: 0;
  left: -160px;
  width: calc(var(--matrix-3d-question-content-width, 100%) + 160px);
  height: var(--height, 20px);
  transform: translateX(calc(var(--matrix-3d-question-content-left, 0)));
  pointer-events: none;
  z-index: -5;
}

.matrix-3d-question--rows-above-variants .matrix-3d-question__variant-select-anchor {
  left: 0;
  width: calc(var(--matrix-3d-question-content-width, 100%));
}

.matrix-3d-question:not(.matrix-3d-question--forbid-scroll) {
  margin-bottom: -3px;
}

.matrix-3d-question:not(.matrix-3d-question--forbid-scroll) :global(.simplebar-content-wrapper) {
  padding-right: var(--success-check-shift-x);
}

.matrix-3d-question--has-scroll .simplebar-themed {
  padding-bottom: 8px;
}

.matrix-3d-question--has-scroll :global(.simplebar-track.simplebar-horizontal) {
  bottom: 4px;
  width: calc(100% - 4px);
}

.matrix-3d-question__skip-container {
  margin-top: 30px;
}

.matrix-3d-question__skip-option {
  padding: 5px 10px;
  color: rgba(0, 0, 0, 0.5);
}

.matrix-3d-question__columns-with-variants:before,
.matrix-3d-question__columns-with-variants:after {
  content: '';
  position: absolute;
  top: 0;
  width: 15px;
  z-index: 10;
  height: 100%;
  pointer-events: none;
  transition: opacity 0.3s;
  display: var(--show-gradient-shadow, none);
}

.matrix-3d-question__columns-with-variants:before {
  left: 0;
  background: linear-gradient(90deg, var(--fqz-poll-main-place-color) 0%, rgba(255, 255, 255, 0) 100%);
}

.matrix-3d-question__columns-with-variants:after {
  right: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, var(--fqz-poll-main-place-color) 100%);
}

.matrix-3d-question:not(.matrix-3d-question--has-scroll) .matrix-3d-question__columns-with-variants:before,
.matrix-3d-question:not(.matrix-3d-question--has-scroll) .matrix-3d-question__columns-with-variants:after {
  display: none;
}

.matrix-3d-question--reached-scroll-start .matrix-3d-question__columns-with-variants:before {
  opacity: 0;
}

.matrix-3d-question--reached-scroll-end .matrix-3d-question__columns-with-variants:after {
  opacity: 0;
}

.matrix-3d-question :global(.simplebar-track) {
  z-index: 3;
}

.matrix-3d-question :global(.simplebar-mask) {
  z-index: auto !important;
}

.matrix-3d-question__variant-cell {
  align-self: flex-start;
}

.matrix-3d-question--skipped .matrix-3d-question__row-title-text,
.matrix-3d-question--skipped .matrix-3d-question__column-title-text,
.matrix-3d-question--skipped .matrix-3d-question__variant-cell,
.matrix-3d-question--skipped .matrix-3d-question__variant-cell-row-name,
.matrix-3d-question--skipped .matrix-3d-question__variant-cell-column-name {
  opacity: 0.7;
}

:global(.matrix-3d-question--forbid-scroll .matrix-3d-question__columns-with-variants) :global(.simplebar-wrapper),
:global(.matrix-3d-question--forbid-scroll .simplebar-content),
:global(.matrix-3d-question--forbid-scroll .simplebar-mask),
:global(.matrix-3d-question--forbid-scroll .simplebar-content-wrapper) {
  overflow: visible !important;
}

:global(.matrix-3d-question--forbid-scroll .simplebar-track) {
  display: none;
}

:global(.matrix-3d-question--forbid-scroll .simplebar-content-wrapper) {
  padding-right: 0 !important;
}

.matrix-3d-question--forbid-scroll .matrix-3d-question__columns-with-variants {
  min-width: 0;
}

.matrix-3d-question--forbid-scroll .simplebar-themed {
  padding-bottom: 0;
}

.matrix-3d-question--has-scroll:not(.matrix-3d-question--forbid-scroll) .matrix-3d-question__variant-cell:last-child {
  padding-right: var(--success-check-shift-x);
  box-sizing: border-box;
}

.matrix-3d-question--forbid-scroll .matrix-3d-question__variant-cell:last-child {
  padding-right: 0 !important;
}

/** remove gradient shadow */
.matrix-3d-question--forbid-scroll .matrix-3d-question__columns-with-variants:before,
.matrix-3d-question--forbid-scroll .matrix-3d-question__columns-with-variants:after {
  display: none;
}

.matrix-3d-question--forbid-scroll .matrix-3d-question__variant-cell-list {
  width: calc(100% + 10px);
}

.matrix-3d-question--forbid-scroll {
  margin-bottom: -20px;
}

.matrix-3d-question__comment-form-group {
  margin-top: 29px;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

@media (max-width: 679px) {
  .matrix-3d-question__variant-select-anchor,
  .matrix-3d-question__column-names,
  .matrix-3d-question__rows {
    display: none;
  }

  .matrix-3d-question__variants {
    flex-direction: column;
    width: 100%;
    gap: 20px;
  }

  .matrix-3d-question__variants-row {
    flex-direction: column;
    gap: 10px;
    padding: 0;
    width: 100%;
    padding-bottom: 20px;
    min-height: 0 !important;
  }
  .matrix-3d-question__variants-row:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .matrix-3d-question__variant-cell {
    width: 100% !important;
    padding: 0;
  }

  .matrix-3d-question__variant-cell:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .matrix-3d-question__variant-cell-row-name,
  .matrix-3d-question__variant-cell-column-name {
    display: block;
  }

  .matrix-3d-question__variant-cell-content {
    display: flex;
    gap: 10px;
    width: 100%;
    align-items: center;
  }

  .matrix-3d-question__variant-cell-row-name {
    font-weight: 700;
    font-size: 15px;
    line-height: 1.1;
    margin-bottom: 20px;
  }

  .matrix-3d-question__variant-cell-column-name {
    font-size: 13px;
    line-height: 1.2;
    flex: 0 0 160px;
  }

  .matrix-3d-question__variant-cell-list {
    flex-direction: column;
  }

  .matrix-3d-question__skip-row {
    margin-top: 20px;
  }

  .matrix-3d-question__skip-row :deep(.fc-check__label) {
    font-size: 13px;
    line-height: 1.1;
    padding-top: 4px;
  }

  .matrix-3d-question__skip-container {
    margin-top: 30px;
  }

  .matrix-3d-question--forbid-scroll .matrix-3d-question__variant-cell-list {
    width: auto;
  }

  .matrix-3d-question--forbid-scroll {
    margin-bottom: 0;
  }

  .matrix-3d-question:not(.matrix-3d-question--forbid-scroll) {
    margin-bottom: 0;
  }

  .matrix-3d-question .matrix-3d-question__columns-with-variants {
    overflow: visible !important;
    min-width: 0;
  }

  .matrix-3d-question .matrix-3d-question__columns-with-variants:after,
  .matrix-3d-question .matrix-3d-question__columns-with-variants:before {
    display: none;
  }

  :global(.matrix-3d-question .simplebar-content-wrapper) {
    padding-right: 0 !important;
  }

  .matrix-3d-question__variant-cell:last-child {
    padding-right: 0 !important;
  }

  :global(.matrix-3d-question .matrix-3d-question__columns-with-variants),
  :global(.matrix-3d-question .simplebar-wrapper),
  :global(.matrix-3d-question .simplebar-content),
  :global(.matrix-3d-question .simplebar-mask),
  :global(.matrix-3d-question .simplebar-content-wrapper) {
    overflow: visible !important;
  }

  :global(.matrix-3d-question .simplebar-track) {
    display: none;
  }

  :global(.matrix-3d-question__variant-cell-row-name .hint-trigger),
  :global(.matrix-3d-question__variant-cell-column-name .hint-trigger) {
    margin-top: -1px;
    vertical-align: middle;
  }
}
</style>
