<script setup>
import DatePicker from '@/shared/ui/DatePicker/DatePicker.vue'
import FormGroup from '@/shared/ui/FormGroup.vue'
import MaskedField from '@/shared/ui/MaskedField.vue'
import FcSelect from '@/shared/ui/Select/FcSelect.vue'
import MaskTypes from '@shared/constants/maskTypes'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import { computed, toValue } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const translationsStore = useTranslationsStore()
const simplifiedStore = useSimplifiedStore()
const tabletStore = useTabletStore()
const datePickerAlign = computed(() => simplifiedStore.isSimplifiedMode ? 'center' : undefined)
const isTabletMode = computed(() => tabletStore.isTabletMode)

const {
  maskType,
  maskedField: {
    error,
    dateError,
    timeError,
    dayError,
    monthError,
    date,
    field,
    fieldConfig,
    time,
    day,
    selectedMonth,
    daysInMonth,
    months,
  },
  isRequired,
} = toValue(props.question)

const monthSelectPlaceholderText = computed(() => toValue(translationsStore.t('Месяц')))

const timePlaceholder = computed(() => toValue(fieldConfig.value?.placeholderText) || '00:00')

const datePlaceholder = computed(() => toValue(fieldConfig.value?.placeholderText) || '00.00.0000')
</script>

<template>
  <div>
    <FormGroup v-if="maskType === MaskTypes.Date" :error="dateError" class="date-question__date-form-group">
      <DatePicker
        v-model="date"
        v-model:text="field"
        :month-options="months"
        :invalid="dateError"
        :select-props="selectProps"
        :placeholder="datePlaceholder"
        :align="datePickerAlign"
        :tablet-view="isTabletMode"
        :input-attrs="{ 'data-testid': 'datepicker-input' }"
      />
    </FormGroup>
    <FormGroup v-else-if="maskType === MaskTypes.DateMonth" :error="error || dayError || monthError" class="date-question__date-month-form-group">
      <div class="date-month-mask-container">
        <MaskedField
          v-model="day"
          mask="day"
          :max="daysInMonth"
          :is-invalid="dayError"
          placeholder="00"
          class="date-month-mask-container__day"
          :input-attrs="{ 'inputmode': 'numeric', 'data-testid': 'datemonth-day-input' }"
        />
        <FcSelect
          v-model="selectedMonth"
          :options="months"
          :is-invalid="error"
          searchable
          :search-placeholder-text="selectSearchPlaceholderText"
          :nothing-found-text="selectNothingFoundText"
          :placeholder="monthSelectPlaceholderText"
          :invalid="monthError"
          :clearable="!isRequired"
          :tablet-view="isTabletMode"
        />
      </div>
    </FormGroup>
    <FormGroup v-else-if="maskType === MaskTypes.Time" :error="timeError" class="date-question__time-form-group">
      <MaskedField
        v-model="time"
        mask="time"
        :is-invalid="timeError"
        :placeholder="timePlaceholder"
        class="date-question__time-input"
        :input-attrs="{ 'inputmode': 'numeric', 'data-testid': 'time-input' }"
      />
    </FormGroup>
    <div v-if="maskType === MaskTypes.DateTime" class="date-datetime-mask-container">
      <FormGroup :error="dateError" class="date-datetime-mask-container__date-form-group">
        <DatePicker
          v-model="date"
          v-model:text="field"
          :month-options="months"
          :invalid="dateError"
          :select-props="selectProps"
          :placeholder="datePlaceholder"
          :tablet-view="isTabletMode"
          :input-attrs="{ 'data-testid': 'datepicker-input' }"
        />
      </FormGroup>
      <FormGroup :error="timeError" class="date-datetime-mask-container__time-form-group">
        <MaskedField
          v-model="time"
          mask="time"
          :is-invalid="timeError"
          :placeholder="timePlaceholder"
          class="date-question__time-input"
          :input-attrs="{ 'inputmode': 'numeric', 'data-testid': 'time-input' }"
        />
      </FormGroup>
    </div>
    <div v-if="maskType === MaskTypes.DateMonthTime" class="date-datemonthtime-mask-container">
      <div class="date-datemonthtime-mask-container__date-month">
        <FormGroup :error="dayError || monthError" class="date-datemonthtime-mask-container__day-form-group">
          <div class="date-datemonthtime-mask-container__day-form-group-inner">
            <MaskedField
              v-model="day"
              mask="day"
              :max="daysInMonth"
              :is-invalid="dayError"
              placeholder="00"
              class="date-month-mask-container__day"
              :input-attrs="{ 'inputmode': 'numeric', 'data-testid': 'datemonth-day-input' }"
            />
            <FcSelect
              v-model="selectedMonth"
              :options="months"
              :is-invalid="error"
              searchable
              :search-placeholder-text="selectSearchPlaceholderText"
              :nothing-found-text="selectNothingFoundText"
              :placeholder="monthSelectPlaceholderText"
              :invalid="monthError"
              :clearable="!isRequired"
              :tablet-view="isTabletMode"
            />
          </div>
        </FormGroup>
      </div>
      <FormGroup :error="timeError" class="date-datemonthtime-mask-container__time-form-group">
        <MaskedField
          v-model="time"
          mask="time"
          :is-invalid="timeError"
          :placeholder="timePlaceholder"
          class="date-question__time-input"
          :input-attrs="{ 'inputmode': 'numeric', 'data-testid': 'time-input' }"
        />
      </FormGroup>
    </div>
  </div>
</template>

<style scoped>
.date-question__date-form-group {
  max-width: 220px;
  width: 100%;
  margin: 0 auto;
}

.date-question__date-month-form-group {
  max-width: 293px;
  width: 100%;
  margin: 0 auto;
}

.date-question__time-form-group {
  max-width: 220px;
  width: 100%;
  margin: 0 auto;
}

.date-datetime__form-group {
  max-width: 460px;
  width: 100%;
  margin: 0 auto;
}

.date-datemonthtime__form-group {
  max-width: 528px;
  width: 100%;
  margin: 0 auto;
}

.date-month-mask-container {
  display: flex;
  gap: 15px;
}

.date-month-mask-container__day {
  width: 58px;
  flex-shrink: 0;
  flex-grow: 0;
  text-align: center;
}

.date-month-mask-container__day :deep(.fc-input__wrapper) {
  padding: 0;
}

.date-datetime-mask-container {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.date-datetime-mask-container :deep(.form-group) {
  width: auto;
}

.date-datetime-mask-container :deep(.masked-field__input) {
  max-width: 220px;
  min-width: 0;
}

.date-datemonthtime-mask-container {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.date-datemonthtime-mask-container :deep(.masked-field__input) {
  max-width: 220px;
  min-width: 0;
}

.date-datemonthtime-mask-container__date-month {
  display: flex;
  gap: 15px;
  flex-basis: 293px;
  flex-grow: 0;
  flex-shrink: 0;
}

.date-datemonthtime-mask-container__day-form-group-inner {
  display: flex;
  gap: 15px;
  width: 100%;
}

.date-datemonthtime-mask-container__time-form-group {
  flex-basis: 220px;
  flex-grow: 0;
  flex-shrink: 0;
}

@media (max-width: 679px) {
  .date-question__date-form-group {
    max-width: 100%;
  }
  .date-datetime-mask-container {
    flex-direction: column;
    gap: 15px;
  }

  .date-month-mask-container {
    max-width: 100%;
  }

  .date-question__date-month-form-group {
    max-width: 100%;
  }

  .date-datetime__form-group {
    max-width: 100%;
  }

  .date-question__time-form-group {
    max-width: 100%;
  }

  .date-month-mask-container :deep(.select-trigger) {
    max-width: 100%;
  }

  .date-datetime-mask-container :deep(.masked-field__input) {
    max-width: 100%;
  }

  .date-datemonthtime-mask-container {
    flex-direction: column;
  }
  .date-datemonthtime-mask-container__date-month {
    flex-basis: auto;
  }
  .date-datemonthtime-mask-container__date-month :deep(.select-trigger) {
    max-width: 100%;
  }
  .date-datemonthtime-mask-container__time-form-group {
    flex-basis: auto;
  }
  .date-datemonthtime-mask-container__time-form-group :deep(.masked-field__input--time) {
    max-width: 100%;
  }
}
</style>
