import { BaseQuestion, InterBlockModel, StarsQuestionModel } from '@entities/question'
import { AddressQuestionModel } from '../model/AddressQuestionModel'
import { CardSortingQuestionModel } from '../model/CardSortingQuestionModel'
import { ClassifierQuestionModel } from '../model/ClassifierQuestionModel'
import { DateQuestionModel } from '../model/DateQuestionModel'
import { DiffQuestionModel } from '../model/DiffQuestionModel'
import { DistributionScaleQuestionModel } from '../model/DistributionScaleQuestionModel'
import { FileQuestionModel } from '../model/FileQuestionModel'
import { FirstClickTestQuestionModel } from '../model/FirstClickTestQuestionModel'
import { GalleryQuestionModel } from '../model/GalleryQuestionModel'
import { Matrix3dQuestionModel } from '../model/Matrix3dQuestionModel'
import { MatrixQuestionModel } from '../model/MatrixQuestionModel'
import { MediaVariantsQuestionModel } from '../model/MediaVariantsQuestionModel'
import { NpsRatingQuestionModel } from '../model/NpsRatingQuestionModel'
import { PriorityQuestionModel } from '../model/PriorityQuestionModel'
import { QuizQuestionModel } from '../model/QuizQuestionModel'
import { RatingScaleQuestionModel } from '../model/RatingScaleQuestionModel'
import { ScaleQuestionModel } from '../model/ScaleQuestionModel'
import { SmileQuestionModel } from '../model/SmileQuestionModel'
import { StarsVariantsQuestionModel } from '../model/StarsVariantsQuestionModel'
import { TextQuestionModel } from '../model/TextQuestionModel'
import * as QuestionTypes from '../model/types'
import { VariantsQuestionModel } from '../model/VariantsQuestionModel'

export const modelByType = {
  [QuestionTypes.STARS_QUESTION]: StarsQuestionModel,
  [QuestionTypes.INTER_BLOCK]: InterBlockModel,
  [QuestionTypes.SMILE_QUESTION]: SmileQuestionModel,
  [QuestionTypes.RATING_QUESTION]: RatingScaleQuestionModel,
  [QuestionTypes.NPS_QUESTION]: NpsRatingQuestionModel,
  [QuestionTypes.TEXT_QUESTION]: TextQuestionModel,
  [QuestionTypes.ADDRESS_QUESTION]: AddressQuestionModel,
  [QuestionTypes.FILIALS_QUESTION]: ClassifierQuestionModel,
  [QuestionTypes.VARIANTS_QUESTION]: VariantsQuestionModel,
  [QuestionTypes.STAR_VARIANTS_QUESTION]: StarsVariantsQuestionModel,
  [QuestionTypes.DATE_QUESTION]: DateQuestionModel,
  [QuestionTypes.QUIZ_QUESTION]: QuizQuestionModel,
  [QuestionTypes.PRIORITY_QUESTION]: PriorityQuestionModel,
  [QuestionTypes.SCALE_QUESTION]: ScaleQuestionModel,
  [QuestionTypes.DISTRIBUTION_SCALE_QUESTION]: DistributionScaleQuestionModel,
  [QuestionTypes.DIFF_QUESTION]: DiffQuestionModel,
  [QuestionTypes.CARD_SORTING_QUESTION]: CardSortingQuestionModel,
  [QuestionTypes.CLASSIFIER_QUESTION]: ClassifierQuestionModel,
  [QuestionTypes.MATRIX_QUESTION]: MatrixQuestionModel,
  [QuestionTypes.FILE_QUESTION]: FileQuestionModel,
  [QuestionTypes.MEDIA_VARIANTS_QUESTION]: MediaVariantsQuestionModel,
  [QuestionTypes.GALLERY_QUESTION]: GalleryQuestionModel,
  [QuestionTypes.MATRIX_3D_QUESTION]: Matrix3dQuestionModel,
  [QuestionTypes.FIRST_CLICK_TEST_QUESTION]: FirstClickTestQuestionModel,
}

/**
 * Создает модель вопроса в зависимости от типа и режима
 * @param {string} type - Тип вопроса
 * @param {object} data - Данные вопроса
 * @param {string|null} mode - Режим (preview, designPreview)
 * @returns {BaseQuestion} Возвращает модель вопроса
 */
export function createModelByType(type, data, mode = null) {
  const Model = modelByType[type]
  if (!Model) {
    return new BaseQuestion(data, mode)
  }
  return new Model(data, mode)
}
