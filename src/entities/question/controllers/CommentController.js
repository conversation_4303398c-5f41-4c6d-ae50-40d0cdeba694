import { declOfNum } from '@shared/helpers/string'
import { computed, ref, toValue, watch } from 'vue'
import { useTranslationsStore } from '../../../shared/store/translationsStore'

export class CommentController {
  constructor(data) {
    this.translationsStore = useTranslationsStore()
    this.enabled = ref(toValue(data.enabled))
    this.translations = data.translations
    this._title = ref(toValue(data.title))

    this.title = computed({
      get: () => {
        const commentTitle = toValue(this.translations).comment_label || this._title.value || this.translationsStore.t('Ваш комментарий')?.value
        return commentTitle
      },
      set: (value) => {
        this._title.value = value
      },
    })
    this._placeholderText = ref(data.placeholderText)
    this.placeholderText = computed({
      get: () => {
        const placeholder = toValue(this.translations).placeholder_text || this._placeholderText.value || ''
        return placeholder
      },
      set: (value) => {
        this._placeholderText.value = value
      },
    })
    this.value = ref(data.value || '')
    this.touched = ref(false)
    this.error = ref(null)
    this._required = ref(data.required)
    this.required = computed({
      get: () => {
        return toValue(this._required)
      },
      set: (value) => {
        this._required.value = value
      },
    })
    this.skipped = computed(() => toValue(data.skipped))

    this.minLength = ref(data.minLength)
    this.maxLength = ref(data.maxLength)

    this.isValid = computed(() => {
      return this.validate()
    })

    this.hasValue = computed(() => toValue(this.value).length > 0)

    watch(this.value, (_v) => {
      if (this.touched.value) {
        this.validate()
      }
    })
  }

  getData() {
    return this.value
  }

  getSymbolWordForm(n) {
    return declOfNum(n, ['символ', 'символа', 'символов'])
  }

  validate() {
    const val = toValue(this.value)
    this.error.value = null

    if (!this.touched.value || this.skipped.value)
      return true

    if (!val && !this.required.value)
      return true

    if (this.required.value && !val) {
      this.error.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    if (val.length > 0) {
      if (val.length < toValue(this.minLength)) {
        const count = toValue(this.minLength)
        const word = this.getSymbolWordForm(count)
        const characters = this.translationsStore.t(`{count} ${word}`, { count })
        this.error.value = this.translationsStore.t(`Должно быть введено хотя бы {characters}`, { characters: toValue(characters) })
        return false
      }
      else if (val.length > toValue(this.maxLength)) {
        const count = toValue(this.maxLength)
        const word = this.getSymbolWordForm(count)
        const characters = this.translationsStore.t(`{count} ${word}`, { count })

        this.error.value = this.translationsStore.t(`Должно быть введено не более {characters}`, { characters: toValue(characters) })
        return false
      }
    }
    return true
  }

  updateFromPreview(previewData) {
    // Make sure we handle reactivity properly by updating ref values

    this.enabled.value = previewData.enabled
    this.required.value = previewData.required
    this.title.value = previewData.title
    this.placeholderText.value = previewData.placeholderText

    // Update lengths
    this.minLength.value = previewData.textFieldParam?.min || 0
    this.maxLength.value = previewData.textFieldParam?.max || 1000

    // Reset validation state
    this.error.value = null
  }

  setValue(newValue) {
    this.value = newValue
    this.validate()
  }
}
