import { getBaseAssetsUrl } from '@shared/api'
import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, ref, toValue, watch } from 'vue'

export class GalleryController {
  constructor(data) {
    this.enabled = computed({
      get: () => toValue(data.enabled),
      set: (value) => { data.enabled = value },
    })

    this._randomVariantsOrder = ref(data.randomVariantsOrder)
    this.randomVariantsOrder = computed({
      get: () => this._randomVariantsOrder.value,
      set: (value) => { this._randomVariantsOrder.value = value },
    })

    this._maxChooseVariants = ref(data.maxChooseVariants)
    this.maxChooseVariants = computed({
      get: () => this._maxChooseVariants.value,
      set: (value) => { this._maxChooseVariants.value = value },
    })
    this.sliderInstance = ref(null)

    this._selectable = ref(data.selectable || false)
    this.selectable = computed({
      get: () => this._selectable.value,
      set: (value) => { this._selectable.value = value },
    })
    this.skipped = computed({
      get: () => toValue(data.skipped),
      set: (value) => { data.skipped = value },
    })

    this._multiple = ref(data.multiple)
    this.multiple = computed({
      get: () => this._multiple.value,
      set: (value) => { this._multiple.value = value },
    })

    this._type = ref(data.type)
    this.type = computed({
      get: () => this._type.value,
      set: (value) => { this._type.value = value },
    })

    this.previousRawAnswerObject = data.answer

    // Предыдущий ответ респондента
    // Может быть массив, а может быть объект если тип вопроса "rating"
    this.previousAnswers = null

    try {
      const defaultStringifiedAnswers = this.type.value === 'rating' ? '{}' : '[]'
      this.previousAnswers = JSON.parse(data.answer?.answer || defaultStringifiedAnswers)
      if (Array.isArray(this.previousAnswers)) {
        this.previousAnswers = this.previousAnswers.map(id => Number(id))
      }
    }
    catch {
      this.previousAnswers = this.type.value === 'rating' ? {} : []
    }

    this.labelKey = computed(() => this.type.value === 'rating' ? 'description' : 'label')

    this.isRequired = computed({
      get: () => toValue(data.isRequired),
      set: (value) => { data.isRequired = value },
    })
    this.error = ref(null)
    this.translationsStore = useTranslationsStore()
    this.t = this.translationsStore.t
    this.translations = data.translations

    this.showSliderArrows = computed(() => {
      return this.gallery.value.length > 1
    })

    this.touched = ref(false)

    const transformedGallery = this.sortGallery(data.gallery.map(item => this.createGalleryItem(item)))

    this.gallery = ref(transformedGallery)

    // create array of id: rating
    this.invalidRatingItems = computed(() => this.gallery.value.filter(item => item.rating === 0))

    this.setupWatchers()

    const isRating = this.type.value === 'rating'

    if (!this.selectable.value) {
      this.selectedItems = ref([])
      this.previousSelectedItems = ref([])
      this.isValid = computed(() => true)
      return
    }

    const transformedPreviousSelectedItems = isRating ? [] : this.previousAnswers.map(id => this.gallery.value.find(item => item.id === id))
    this.previousSelectedItems = ref(transformedPreviousSelectedItems)

    this.selectedItems = ref(this.multiple.value ? this.previousSelectedItems.value : this.previousSelectedItems.value?.[0])

    this.isValid = computed(() => {
      return this.validate()
    })
  }

  getImgUrl(src) {
    return src && src.includes('http') ? src : getBaseAssetsUrl() + src
  }

  createGalleryItem(item) {
    const src = ref(this.getImgUrl(item.src))
    const url = ref(this.getImgUrl(item.url))
    const poster = ref(this.getImgUrl(item.poster))
    const rating = this.previousAnswers ? Number.parseInt(this.previousAnswers?.[item.id]) || 0 : 0
    const hasPreviousRating = this.previousAnswers ? !!this.previousAnswers?.[item.id] : false

    const _label = ref(item.description)
    return {
      ...item,
      src: computed({
        get: () => {
          return item.url ? url.value : src.value
        },
        set: (value) => {
          url.value = value
        },
      }),
      url: computed({
        get: () => {
          return item.url ? url.value : src.value
        },
        set: (value) => {
          url.value = value
        },
      }),
      poster: computed({
        get: () => {
          return item.poster ? poster.value : src.value
        },
        set: (value) => {
          poster.value = value
        },
      }),
      label: computed({
        get: () => {
          const lang = this.translationsStore.selectedLang
          if (lang && Array.isArray(item?.langFiles)) {
            const langFile = item.langFiles.find(lf => lf.foquz_poll_lang_id === lang.id)
            return langFile?.description || _label.value
          }
          return _label.value
        },
        set: (value) => {
          _label.value = value
        },
      }),
      description: computed(() => _label.value),
      isInactive: computed(() => this.skipped.value),
      isDisabled: computed(() => {
        if (!this.multiple.value || this.skipped.value || !this.maxChooseVariants.value)
          return false

        const isSelected = this.selectedItems.value.some(i => i.id === item.id)
        const isMaxChooseVariants = this.selectedItems.value.length >= this.maxChooseVariants.value

        return isMaxChooseVariants && !isSelected
      }),
      isSkipped: computed(() => this.skipped.value),
      rating: ref(rating),
      hasPreviousRating: ref(hasPreviousRating),
    }
  }

  get itemTemplateName() {
    return 'gallery-item-basic-template'
  }

  updateFromPreview(previewData) {
    if (!previewData)
      return

    if (previewData.enabled !== undefined)
      this.enabled.value = previewData.enabled

    if (previewData.randomVariantsOrder !== undefined)
      this.randomVariantsOrder.value = previewData.randomVariantsOrder

    if (previewData.maxChooseVariants !== undefined)
      this.maxChooseVariants.value = Number.parseInt(previewData.maxChooseVariants) || Infinity

    if (previewData.selectable !== undefined)
      this.selectable.value = previewData.selectable

    if (previewData.multiple !== undefined) {
      const isMultipleValueChanged = this.multiple.value !== previewData.multiple
      this.multiple.value = previewData.multiple
      if (isMultipleValueChanged) {
        this.selectedItems.value = previewData.multiple ? [] : null
      }
    }

    if (previewData.type !== undefined)
      this.type.value = previewData.type

    if (previewData.isRequired !== undefined)
      this.isRequired.value = previewData.isRequired

    if (Array.isArray(previewData.gallery)) {
      const galleryMap = new Map(this.gallery.value.map(item => [item.id, item]))
      const transformedGallery = previewData.gallery.map((item, index) => {
        const id = item.persistentId || item.id
        const existingItem = galleryMap.get(id)
        if (existingItem) {
          const rawUrl = item.url || item.src
          const src = rawUrl ? this.getImgUrl(rawUrl) : ''
          const poster = src
          const srcChanged = existingItem.src !== src
          const posterChanged = existingItem.poster !== poster

          if (srcChanged)
            existingItem.src = src
          if (posterChanged)
            existingItem.poster = poster

          existingItem.label = item.description

          existingItem.position = index

          galleryMap.set(id, existingItem)
        }
        else {
          const newItem = this.createGalleryItem({
            ...item,
            id,
            src: item.url || item.src || '',
            poster: item.preview || item.url || item.src || '',
            position: index,
            rating: ref(0),
            hasPreviousRating: ref(false),
            isInactive: computed(() => this.skipped.value),
            isDisabled: computed(() => false),
            isSkipped: computed(() => this.skipped.value),
            label: computed(() => item.description || ''),
          })
          galleryMap.set(id, newItem)
          return newItem
        }
        return existingItem
      })

      this.gallery.value = transformedGallery
      this.setupWatchers()
    }
  }

  isVideo(media) {
    const videoExt = ['MP4', 'WMV', 'MOV', '3GP', 'FLV', 'MPEG-1', 'MPEG-2', 'WebM']
    const urlArr = media.url.split('.')
    return media.url !== media.poster && videoExt.includes(urlArr[urlArr.length - 1].toUpperCase())
  }

  setAllAsTouched() {
    this.touched.value = true
  }

  toggleSelectItem(item) {
    if (!this.selectable.value)
      return

    if (this.multiple.value) {
      const index = this.selectedItems.value.indexOf(item)
      if (index > -1) {
        this.selectedItems.value.splice(index, 1)
      }
      else {
        this.selectedItems.value.push(item)
      }
    }
    else {
      this.selectedItems.value = this.selectedItems.value === item ? null : item
    }
  }

  scrollToFirstSlide() {
    const swiper = this.sliderInstance.value?.swiper
    if (swiper) {
      swiper.slideTo(0, 1000)
    }
  }

  slideToFirstEmptyRating() {
    const swiper = this.sliderInstance.value?.swiper
    if (!swiper)
      return

    const activeIndex = swiper.activeIndex
    const galleryLength = this.gallery.value.length

    // Search for empty rating from active index to the end
    for (let i = activeIndex + 1; i < galleryLength; i++) {
      if (this.gallery.value[i].rating === 0) {
        swiper.slideTo(i, 1000)
        return
      }
    }

    // If all next items have rating, search from the beginning to active index
    for (let i = activeIndex - 1; i >= 0; i--) {
      if (this.gallery.value[i].rating === 0) {
        swiper.slideTo(i, 1000)
        return
      }
    }

    // If no empty ratings found, do nothing
  }

  setRating(item, rating) {
    if (this.type.value !== 'rating')
      return
    this.gallery.value.find(i => i.id === item.id).rating = rating
  }

  validate() {
    this.error.value = null
    let isValid = true
    const isRequired = this.isRequired.value
    const isSkipped = this.skipped.value
    const isTouched = this.touched.value
    const isSelectable = this.selectable.value

    if (isSkipped || !isRequired || !isTouched || !isSelectable) {
      return true
    }

    if (this.type.value === 'rating') {
      const allRatingsAreSet = this.gallery.value.every(r => r.rating > 0)
      if (isRequired && !allRatingsAreSet) {
        this.error.value = this.t('Нужно поставить все оценки')
        isValid = false
      }
      return isValid
    }

    const hasCheckedVariant = Array.isArray(this.selectedItems.value) ? this.selectedItems.value.length > 0 : !!this.selectedItems.value

    if (isRequired && !hasCheckedVariant) {
      this.error.value = this.t('Нужно выбрать один из вариантов')
      isValid = false
    }
    return isValid
  }

  get hasValue() {
    if (this.previousRawAnswerObject) {
      return true
    }
    if (this.type.value === 'rating') {
      return this.gallery.value.some(item => toValue(item.hasPreviousRating))
    }

    const areSelectedItemsArray = Array.isArray(this.selectedItems.value)
    const hasSelectedItems = areSelectedItemsArray ? this.selectedItems.value.length > 0 : !!this.selectedItems.value
    return hasSelectedItems
  }

  getData() {
    const data = {
      selectedItems: this.selectedItems.value,
      rating: this.gallery.value.map(item => ({ id: item.id, rating: item.rating, position: item.position })),
    }
    return data
  }

  sortGallery(gallery) {
    let sortedGallery = gallery.sort((a, b) => a.position - b.position)

    if (this.randomVariantsOrder.value) {
      sortedGallery = shuffle(sortedGallery)
    }

    return sortedGallery
  }

  setupWatchers() {
    if (this.type.value === 'rating') {
      this.gallery.value.forEach((item) => {
        watch(() => item.rating, (v) => {
          this.validate()

          if (v !== 0) {
            this.slideToFirstEmptyRating()
          }
        })
      })
    }

    watch(this.skipped, (v) => {
      if (v) {
        this.error.value = null
        this.selectedItems.value = []
        if (this.type.value === 'rating') {
          this.gallery.value.forEach((item) => {
            item.rating = 0
          })
        }
      }
    })
  }
}
