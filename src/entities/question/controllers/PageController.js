import { getScrollElement } from '@/shared/helpers/dom'
import { usePreviewStore } from '@shared/store/previewStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import throttle from 'lodash.throttle'
import { computed, nextTick, ref, shallowRef, toValue, watch } from 'vue'

export function usePageController() {
  const previewStore = usePreviewStore()
  const simplifiedStore = useSimplifiedStore()
  const currentActivePage = ref(null)
  const initialized = ref(false)
  const disableQuestionAutoscroll = ref(false)
  const currentQuestion = shallowRef(null)
  const currentQuestionIndex = ref(0)
  const isScrollingToNextQuestion = ref(false)

  const allQuestionsAreWithinViewport = ref(false)
  const scrollDistanceInPercent = ref(0)

  const visibleQuestions = computed(() => {
    return currentActivePage.value?.visibleQuestions || []
  })

  const multipleQuestionsOnPage = computed(() => {
    return visibleQuestions.value.length > 1
  })
  const activeQuestions = shallowRef([])

  const activeQuestionsIndexes = computed(() => {
    return activeQuestions.value.map(q =>
      toValue(visibleQuestions).findIndex(vq => vq.id === q.id))
  })

  const questionWatchers = new Map()

  const clearQuestionWatchers = () => {
    questionWatchers.forEach(unwatch => unwatch())
    questionWatchers.clear()
  }

  const getScrollPosition = () => {
    const scrollElement = getScrollElement()
    return scrollElement === window ? window.scrollY : scrollElement.scrollTop
  }

  const getViewportHeight = () => {
    const scrollElement = getScrollElement()
    return scrollElement === window ? window.innerHeight : scrollElement.clientHeight
  }

  const getDocumentHeight = () => {
    const scrollElement = getScrollElement()
    return scrollElement === window
      ? document.documentElement.scrollHeight
      : scrollElement.scrollHeight
  }

  const scrollToPosition = (position, options = { behavior: 'smooth' }) => {
    const scrollElement = getScrollElement()

    setTimeout(() => {
      scrollElement.scrollTo({ top: position, ...options })
    }, 50)
  }

  const scrollToQuestion = (index) => {
    // Если целевой вопрос уже активен,
    // он, вероятно, уже находится в зоне видимости. Дальше не скролим.
    if (activeQuestionsIndexes.value.includes(index)) {
      return
    }

    // Если это первый вопрос (и он не был ранее определен как активный),
    // скроллим к верху страницы.
    // Заметка: Изначальная проверка `isFirstQuestion` была здесь. Немного изменено.
    if (index === 0) {
      return scrollToPosition(0)
    }

    const question = visibleQuestions.value[index]
    const element = question?.domNodeElement?.value

    if (!element)
      return

    isScrollingToNextQuestion.value = true
    const viewportHeight = getViewportHeight()
    const elementRect = element.getBoundingClientRect()
    const elementHeight = elementRect.height
    const scrollY = getScrollPosition()

    // Calculate optimal scroll position based on element height
    let targetPosition

    if (elementHeight > viewportHeight * 0.7) {
      // For tall elements, align to top with small padding
      targetPosition = scrollY + elementRect.top - 80
    }
    else {
      // For normal elements, center them in viewport
      const elementTop = scrollY + elementRect.top
      const viewportThird = viewportHeight / 5.2 + (viewportHeight / 8) * scrollDistanceInPercent.value
      targetPosition = elementTop - viewportThird
    }

    // Ensure we don't scroll past the element
    const maxScroll = getDocumentHeight() - viewportHeight
    targetPosition = Math.min(targetPosition, maxScroll)
    targetPosition = Math.max(targetPosition, 0)

    scrollToPosition(targetPosition)

    setTimeout(() => {
      isScrollingToNextQuestion.value = false
    }, 300)
  }

  const scrollToNextQuestionInViewport = () => {
    const indexes = activeQuestionsIndexes.value
    const nextQuestionIndex = indexes[indexes.length - 1] + 1
    if (nextQuestionIndex < visibleQuestions.value.length) {
      scrollToQuestion(nextQuestionIndex)
    }
  }

  const scrollToPrevQuestionInViewport = () => {
    const indexes = activeQuestionsIndexes.value
    const prevQuestionIndex = indexes[indexes.length - 1] - 1
    if (prevQuestionIndex >= 0) {
      scrollToQuestion(prevQuestionIndex)
    }
  }

  const firstQuestionInViewPort = computed(() => {
    const indexes = activeQuestionsIndexes.value
    const isFirstQuestion = indexes.length === 1 && indexes[0] === 0
    const isNoQuestions = indexes.length === 0
    return isFirstQuestion || isNoQuestions
  })

  const lastQuestionInViewPort = computed(() => {
    if (!visibleQuestions.value || visibleQuestions.value.length === 0)
      return false

    const indexes = activeQuestionsIndexes.value
    return indexes[indexes.length - 1] === visibleQuestions.value.length - 1
  })

  const updateCurrentQuestionIndex = () => {
    const questions = visibleQuestions.value
    if (questions.length <= 1)
      return

    const scrollY = getScrollPosition()
    const documentHeight = getDocumentHeight()
    const viewportHeight = getViewportHeight()
    scrollDistanceInPercent.value = scrollY / (documentHeight - viewportHeight)

    // Constants for thresholds
    const TOP_THRESHOLD = 30 // pixels from top
    const BOTTOM_THRESHOLD = 30 // pixels from bottom
    // Adjust TOP_THIRD based on scroll position - move reference point down as we scroll down
    const TOP_THIRD = viewportHeight / 3.5 + (viewportHeight / 8) * scrollDistanceInPercent.value

    // Reset active questions
    activeQuestions.value = []

    // Check if we're close to the top of the page
    if (scrollY <= TOP_THRESHOLD) {
      currentQuestion.value = questions[0]
      currentQuestionIndex.value = 0
      activeQuestions.value = [questions[0]]
      return
    }

    // Check if we're close to the bottom of the page
    if (scrollY + viewportHeight >= documentHeight - BOTTOM_THRESHOLD) {
      currentQuestion.value = questions[questions.length - 1]
      currentQuestionIndex.value = questions.length - 1
      const lastQuestion = questions[questions.length - 1]
      activeQuestions.value = [...activeQuestions.value, lastQuestion]
    }

    // Find all questions in the top third of viewport
    questions.forEach((question, index) => {
      const element = question.domNodeElement?.value
      if (element) {
        const rect = element.getBoundingClientRect()
        if (rect.top <= TOP_THIRD && rect.bottom >= 0) {
          activeQuestions.value = [...activeQuestions.value, question]
          // Set the first question in top third as current
          if (!currentQuestion.value) {
            currentQuestion.value = question
            currentQuestionIndex.value = index
          }
        }
      }
    })

    // If no questions in top third, find closest to the reference point
    if (activeQuestions.value.length === 0) {
      const viewportReference = scrollY + TOP_THIRD
      let closestQuestionIndex = null
      let smallestDistance = Infinity

      questions.forEach((question, index) => {
        const element = question.domNodeElement?.value
        if (element) {
          const rect = element.getBoundingClientRect()
          const questionTop = scrollY + rect.top
          const distanceFromTop = Math.abs(questionTop - viewportReference)

          if (distanceFromTop < smallestDistance) {
            smallestDistance = distanceFromTop
            closestQuestionIndex = index
          }
        }
      })

      if (closestQuestionIndex !== null) {
        currentQuestionIndex.value = closestQuestionIndex
        currentQuestion.value = questions[closestQuestionIndex]
        activeQuestions.value = [questions[closestQuestionIndex]]
      }
    }
  }

  const checkIfAllQuestionsAreWithinViewport = () => {
    // Check if all questions are within viewport (with 30px tolerance)
    const TOLERANCE = 30
    const viewportHeight = getViewportHeight()
    const questions = visibleQuestions.value
    let allWithinViewport = true

    questions.forEach((question) => {
      const element = question.domNodeElement?.value
      if (element) {
        const rect = element.getBoundingClientRect()
        const isWithin = (
          rect.top >= -TOLERANCE
          && rect.bottom <= viewportHeight + TOLERANCE
        )
        if (!isWithin)
          allWithinViewport = false
      }
      else {
        allWithinViewport = false
      }
    })

    return allWithinViewport
  }

  const handleScroll = throttle(() => {
    updateCurrentQuestionIndex()
  }, 100)

  const handleResize = throttle(() => {
    allQuestionsAreWithinViewport.value = checkIfAllQuestionsAreWithinViewport()
    updateCurrentQuestionIndex()
  }, 100)

  const setupScrollAndResizeListeners = () => {
    nextTick().then(() => {
      updateCurrentQuestionIndex()
      allQuestionsAreWithinViewport.value = checkIfAllQuestionsAreWithinViewport()
    })
    const scrollElement = getScrollElement()
    scrollElement.addEventListener('scroll', handleScroll)
    scrollElement.addEventListener('resize', handleResize)
  }

  const destroyScrollAndResizeListeners = () => {
    const scrollElement = getScrollElement()
    scrollElement.removeEventListener('scroll', handleScroll)
    scrollElement.removeEventListener('resize', handleResize)
    clearQuestionWatchers()
  }

  /**
   * Scroll to the first visible error
   */
  const scrollToFirstVisibleError = async () => {
    await nextTick()
    const firstError = document.querySelector('[data-validation-error]')
    if (firstError) {
      await nextTick()
      const scrollY = getScrollPosition()
      const viewportHeight = getViewportHeight()
      const elementTop = scrollY + firstError.getBoundingClientRect().top
      const viewportThird = viewportHeight / 4.5 + (viewportHeight / 8) * scrollDistanceInPercent.value
      const targetPosition = elementTop - viewportThird
      scrollToPosition(targetPosition, { behavior: 'smooth', block: 'center' })
    }
  }

  const setupQuestionWatchers = () => {
    if (!currentActivePage.value || disableQuestionAutoscroll.value)
      return

    const questions = currentActivePage.value.visibleQuestions
    if (questions.length <= 1)
      return // Don't set watchers if only one question

    questions.forEach((question, index) => {
      if (index === questions.length - 1)
        return // Skip last question

      // Clear any existing watcher for this question
      if (questionWatchers.has(question.id)) {
        questionWatchers.get(question.id)()
        questionWatchers.delete(question.id)
      }

      // Set new watcher
      const unwatch = watch(
        () => question.canMoveToNextQuestion?.value,
        (newValue) => {
          if (newValue === true && !isScrollingToNextQuestion.value) {
            setTimeout(() => {
              scrollToQuestion(index + 1)
              question.canMoveToNextQuestion.value = false
            }, 300)
          }
        },
      )
      questionWatchers.set(question.id, unwatch)
    })
  }

  const setup = ({ disableAutoscroll }) => {
    setupScrollAndResizeListeners()
    if (!disableAutoscroll)
      setupQuestionWatchers()
  }

  const initialize = ({ activePage, disableAutoscroll }) => {
    // Clear existing watchers when page changes
    clearQuestionWatchers()

    const disableAutoscrollValue = computed(() => {
      if (simplifiedStore.isSimplifiedMode) {
        return true
      }
      return toValue(disableAutoscroll) && !previewStore.isNonFunctionalPreview
    })

    disableQuestionAutoscroll.value = toValue(disableAutoscrollValue)

    watch([activePage, disableAutoscrollValue], ([p, disableAutoscroll]) => {
      if (!p)
        return

      currentActivePage.value = p
      setup({ disableAutoscroll })
      initialized.value = true
    }, { immediate: true })

    watch(disableAutoscrollValue, (value) => {
      disableQuestionAutoscroll.value = value
      if (value) {
        setupQuestionWatchers()
      }
    })
  }

  return {
    initialize,
    initialized,
    currentQuestion,
    disableQuestionAutoscroll,
    multipleQuestionsOnPage,
    allQuestionsAreWithinViewport,
    scrollToNextQuestionInViewport,
    scrollToPrevQuestionInViewport,
    destroyScrollAndResizeListeners,
    currentQuestionIndex,
    firstQuestionInViewPort,
    lastQuestionInViewPort,
    clearQuestionWatchers,
    scrollToQuestion,
    scrollToFirstVisibleError,
    activeQuestions,
  }
}
