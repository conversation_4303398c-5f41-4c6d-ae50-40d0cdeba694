import { getBaseApiUrl } from '@shared/api'

export async function setImageViewed(authKey, questionId) {
  const formData = new FormData()
  formData.append('authKey', authKey)
  formData.append('questionId', questionId)

  const url = new URL(`${getBaseApiUrl()}/foquz/api/p/set-image-viewed`)

  const response = await fetch(url.toString(), {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error('Failed to send image viewed data')
  }

  return response.json()
}
