import { getBaseAssetsUrl } from '@shared/api'
import { ALLOWED_BUT_NOT_SUPPORTED, ALLOWED_FILE_TYPES, ALLOWED_IMAGE_PREVIEW_FORMATS } from '@shared/constants/files'
import { nanoid } from 'nanoid'
import { computed, ref } from 'vue'
import { uploadFiles } from '../../poll/api'

export class VariantsAttachmentHandler {
  constructor(options) {
    // Configuration
    this.maxFiles = ref(options.maxFiles || 0)
    this.filesizeLimit = options.filesizeLimit || 5
    this.filesizeLimitInBytes = this.filesizeLimit * 1024 * 1024
    this.t = options.t
    this.isPreviewMode = options.isPreviewMode || false
    this.questionId = options.questionId

    // State
    this.files = ref([])
    this.screenshots = ref([])
    this.error = ref(null)
    this.errorKind = ref('default')

    // Computed
    this.allAttachments = computed(() => {
      const attachments = [...this.files.value, ...this.screenshots.value]

      const uploadingAttachments = attachments.filter(attachment => attachment.isUploading)
      const nonUploadingAttachments = attachments.filter(attachment => !attachment.isUploading)

      nonUploadingAttachments.sort((a, b) => a.timestamp - b.timestamp)

      uploadingAttachments.sort((a, b) => a.timestamp - b.timestamp)

      return [...nonUploadingAttachments, ...uploadingAttachments]
    })

    this.isMaxFilesReached = computed(() => {
      return this.allAttachments.value.length >= this.maxFiles.value
    })
  }

  // Add file with loading state
  addFile(fileData) {
    if (this.allAttachments.value.length >= this.maxFiles.value) {
      this.error.value = `Максимальное количество файлов: ${this.maxFiles.value}`
      return false
    }

    const file = {
      uuid: nanoid(),
      id: Date.now().toString(),
      type: 'file',
      name: fileData.name,
      file: fileData,
      isUploading: false,
      previewUrl: fileData.previewUrl || null,
      fullUrl: fileData.fullUrl || null,
      timestamp: fileData.timestamp || Date.now(),
    }

    this.files.value.push(file)
    this.error.value = null
    return true
  }

  // Add screenshot with metadata
  addScreenshot(screenshotData) {
    if (this.allAttachments.value.length >= this.maxFiles.value) {
      this.error.value = `Максимальное количество файлов: ${this.maxFiles.value}`
      return false
    }

    const uuid = nanoid()

    const screenshot = {
      uuid,
      id: Date.now().toString(),
      type: 'screenshot',
      name: `Screenshot-${screenshotData.timestamp || Date.now()}.jpg`,
      fullUrl: `screenshot-${uuid}`,
      timestamp: screenshotData.timestamp,
      html: screenshotData.html,
      selection: screenshotData.selection,
      client: {
        ...screenshotData.client,
        isMobile: screenshotData.isMobile,
      },
      isUploading: false,
      isMobile: screenshotData.isMobile,
      userAgent: screenshotData.userAgent,
    }

    this.screenshots.value.push(screenshot)
    this.error.value = null
    return true
  }

  // Upload files to server
  async uploadFiles(files, authKey, questionId) {
    if (!files || files.length === 0) {
      return
    }

    this.error.value = null

    const remainingSlots = this.maxFiles.value - this.allAttachments.value.length
    if (remainingSlots <= 0) {
      this.showTemporaryError(this.t(`Максимальное количество файлов: ${this.maxFiles.value}`))
      return
    }

    // Validate files
    const validFiles = []
    const invalidFiles = []

    for (const file of files) {
      if (!this.isFileTypeAllowed(file)) {
        invalidFiles.push({ file, reason: 'type' })
      }
      else if (file.size > this.filesizeLimitInBytes) {
        invalidFiles.push({ file, reason: 'size' })
      }
      else {
        validFiles.push(file)
      }
    }

    // Show error for invalid files
    if (invalidFiles.length > 0) {
      const typeErrors = invalidFiles.filter(f => f.reason === 'type')
      const sizeErrors = invalidFiles.filter(f => f.reason === 'size')

      if (typeErrors.length > 0) {
        this.showTemporaryError(this.t('Недопустимый тип файла'))
      }

      if (sizeErrors.length > 0) {
        const fileNames = sizeErrors.map(f => f.file.name)
        const firstTranslation = this.t('Файл слишком большой')
        const secondTranslation = this.t('Размер файла не должен превышать {size} Мб', { size: this.filesizeLimit })
        const message = computed(() => `${firstTranslation.value}: «${fileNames.join('», «')}». ${secondTranslation.value}`)
        this.showTemporaryError(message, 'too-large')
      }
    }

    if (validFiles.length === 0) {
      return
    }

    const currentRemainingSlots = this.maxFiles.value - this.allAttachments.value.length
    if (currentRemainingSlots <= 0) {
      this.showTemporaryError(this.t(`Максимальное количество файлов: ${this.maxFiles.value}`))
      return
    }

    const filesToUpload = validFiles.slice(0, currentRemainingSlots)

    try {
      const uploadingFiles = filesToUpload.map(file => ({
        uuid: nanoid(),
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: 'image',
        name: file.name,
        file,
        isUploading: true,
        previewUrl: this.createPreviewUrl(file),
        fullUrl: null,
        timestamp: Date.now(),
      }))

      this.files.value.push(...uploadingFiles)

      // Prepare UUIDs array for API call
      const uuids = uploadingFiles.map(f => f.uuid)

      // Make actual API call
      const { files: uploadedFiles } = await uploadFiles({
        authKey: this.isPreviewMode ? 'preview' : authKey,
        files: filesToUpload,
        questionId: questionId || this.questionId,
        isPreview: this.isPreviewMode,
        uuids,
      })

      // Update files with server response using UUID for identification
      uploadingFiles.forEach((uploadingFile) => {
        const fileIndex = this.files.value.findIndex(f => f.uuid === uploadingFile.uuid)
        if (fileIndex !== -1) {
          const uploadedFile = uploadedFiles.find(f => f.uuid === uploadingFile.uuid)

          if (uploadedFile) {
            const baseUrl = getBaseAssetsUrl()
            const fullUrl = uploadedFile.link ? `${baseUrl}${uploadedFile.link}` : null
            let previewUrl = uploadedFile.image ? `${baseUrl}${uploadedFile.image}` : null

            if (!previewUrl && fullUrl && uploadedFile.type === 'picture') {
              const extension = uploadedFile.link?.split('.').pop()?.toLowerCase()
              if (extension && ALLOWED_IMAGE_PREVIEW_FORMATS.includes(extension)) {
                previewUrl = fullUrl
              }
            }

            this.files.value[fileIndex] = {
              ...this.files.value[fileIndex],
              id: uploadedFile.id,
              isUploading: false,
              previewUrl,
              fullUrl,
              timestamp: uploadedFile.timestamp || Date.now(),
              serverData: uploadedFile,
            }
          }
        }
      })
    }
    catch (error) {
      console.error('File upload error:', error)

      // Handle 413 error specifically
      if (error.status === 413) {
        this.showTemporaryError(this.t('Произошла ошибка, попробуйте ещё раз'))
      }
      else {
        this.showTemporaryError(this.t('Не удалось загрузить файлы'))
      }

      // Remove uploading files on error
      this.files.value = this.files.value.filter(f => !f.isUploading)
    }
  }

  // Remove file or screenshot
  removeAttachment(index) {
    const attachment = this.allAttachments.value[index]
    if (!attachment)
      return

    if (attachment.type === 'screenshot') {
      const screenshotIndex = this.screenshots.value.findIndex(s => s.id === attachment.id)
      if (screenshotIndex !== -1) {
        this.screenshots.value.splice(screenshotIndex, 1)
      }
    }
    else {
      const fileIndex = this.files.value.findIndex(f => f.id === attachment.id)
      if (fileIndex !== -1) {
        this.files.value.splice(fileIndex, 1)
      }
    }

    this.error.value = null
  }

  // Extract attachment from server file data
  extractAttachmentFromPreviousAnswer(serverFile) {
    if (serverFile.type === 'screenshot') {
      return {
        type: 'screenshot',
        attachment: {
          uuid: serverFile.uuid || nanoid(),
          id: serverFile.id || Date.now().toString(),
          type: 'screenshot',
          name: serverFile.name,
          fullUrl: `screenshot-${serverFile.uuid}`,
          timestamp: serverFile.timestamp || Date.now(),
          isUploading: false,
          isFromPreviousAnswer: true,
        },
      }
    }
    else {
      // For supported image formats, use the main file as preview if no separate preview image
      const baseUrl = getBaseAssetsUrl()
      const fullUrl = serverFile.link ? `${baseUrl}${serverFile.link}` : null
      let previewUrl = serverFile.image ? `${baseUrl}${serverFile.image}` : null

      // If no separate preview image but file is a supported image format, use the main file
      if (!previewUrl && fullUrl) {
        const extension = serverFile.link?.split('.').pop()?.toLowerCase()
        if (extension && ALLOWED_IMAGE_PREVIEW_FORMATS.includes(extension)) {
          previewUrl = fullUrl
        }
      }

      const file = {
        type: 'file',
        attachment: {
          uuid: serverFile.uuid || nanoid(),
          id: serverFile.id || Date.now().toString(),
          type: 'image',
          name: serverFile.name || serverFile.origin_name || 'Unknown file',
          isUploading: false,
          previewUrl,
          fullUrl,
          timestamp: serverFile.timestamp || Date.now(),
          serverData: serverFile,
          isFromPreviousAnswer: true,
        },
      }

      console.log('extracted attachment', file.attachment)
      return file
    }
  }

  // Load existing attachments from previous answer
  addAttachmentsFromPreviousAnswer(previousAnswer) {
    if (!previousAnswer) {
      return
    }

    const files = previousAnswer?.files || previousAnswer?.answer?.files || []

    if (files && Array.isArray(files)) {
      files.forEach((serverFile) => {
        const { type, attachment } = this.extractAttachmentFromPreviousAnswer(serverFile)

        if (type === 'screenshot') {
          this.screenshots.value.push(attachment)
        }
        else {
          this.files.value.push(attachment)
        }
      })
    }
  }

  // Validate file type and size
  validateFile(file) {
    const errors = []

    if (!this.isFileTypeAllowed(file)) {
      errors.push({ type: 'type', message: 'Invalid file type' })
    }

    if (file.size > this.filesizeLimitInBytes) {
      errors.push({ type: 'size', message: 'File too large' })
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  // Check if file type is allowed
  isFileTypeAllowed(file) {
    const fileType = this.getFileType(file)

    if (fileType !== 'image') {
      return false
    }

    if (file.type && !file.type.startsWith('image/')) {
      return false
    }

    return true
  }

  // Get file type from file name
  getFileType(file) {
    const regex = /\.[0-9a-z]+$/i
    const fileExtension = file.name.match(regex)?.[0]?.slice(1).toLowerCase()

    // Check in ALLOWED_FILE_TYPES
    for (const [fileType, extensions] of Object.entries(ALLOWED_FILE_TYPES)) {
      if (extensions.includes(fileExtension)) {
        return fileType
      }
    }

    // Check in ALLOWED_BUT_NOT_SUPPORTED
    for (const [fileType, extensions] of Object.entries(ALLOWED_BUT_NOT_SUPPORTED)) {
      if (extensions.includes(fileExtension)) {
        return fileType
      }
    }

    return null
  }

  // Show temporary error
  showTemporaryError(message, kind = 'default') {
    this.error.value = message
    this.errorKind.value = kind
    setTimeout(() => {
      if (this.errorKind.value === kind) {
        this.error.value = null
      }
    }, 3000)
  }

  // Create preview URL for file
  createPreviewUrl(file) {
    if (file.type.startsWith('image/')) {
      return URL.createObjectURL(file)
    }
    return null
  }

  // Get data for API submission
  getAttachmentData() {
    const data = {}

    const newScreenshots = this.screenshots.value.filter(screenshot => !screenshot.isFromPreviousAnswer)
    const oldScreenshots = this.screenshots.value.filter(screenshot => screenshot.isFromPreviousAnswer)

    if (newScreenshots.length > 0) {
      data.clarifyingQuestionScreenshots = newScreenshots.map(screenshot => ({
        name: screenshot.name,
        uuid: screenshot.uuid,
        html: screenshot.html,
        timestamp: screenshot.timestamp,
        selection: screenshot.selection,
        client: screenshot.client,
        userAgent: screenshot.userAgent,
        isMobile: screenshot.isMobile,
      }))
    }

    const fileIds = this.files.value
      .filter(file => file.serverData?.id || file.id)
      .map(file => file.serverData?.id || file.id)
      .filter(id => !!id)

    const screenshotIds = oldScreenshots
      .filter(screenshot => screenshot.id)
      .map(screenshot => screenshot.id)
      .filter(id => !!id)

    if (fileIds.length > 0) {
      data.clarifyingQuestionAttachmentIds = fileIds
    }

    if (screenshotIds.length > 0) {
      data.clarifyingQuestionScreenshotIds = screenshotIds
    }

    return data
  }

  // Get screenshot data
  getScreenshotData() {
    return {
      screenshots: this.screenshots.value.map(screenshot => ({
        uuid: screenshot.uuid,
        html: screenshot.html,
        timestamp: screenshot.timestamp,
        selection: screenshot.selection,
        client: screenshot.client,
        isMobile: screenshot.isMobile,
      })),
      files: this.files.value.map(file => ({
        uuid: file.uuid,
        name: file.name,
        file: file.file,
        previewUrl: file.previewUrl,
        fullUrl: file.fullUrl,
      })),
    }
  }

  // Clear all attachments
  clearAll() {
    this.files.value = []
    this.screenshots.value = []
    this.error.value = null
    this.errorKind.value = 'default'
  }

  // Clear error
  clearError() {
    this.error.value = null
  }

  // Update max files limit
  updateMaxFiles(maxFiles) {
    this.maxFiles.value = maxFiles
  }

  // Update from preview data
  updateFromPreview(previewData) {
    if (!previewData) {
      return
    }

    // Update max files if provided
    if (previewData.max_files !== undefined) {
      this.updateMaxFiles(previewData.max_files)
    }

    // Clear attachments if max files is now 0 or file attachment is disabled
    if (previewData.max_files === 0
      || (previewData.upload_enabled === 0 && previewData.make_screenshot_enabled === 0)) {
      this.clearAll()
    }

    // Clear error state when settings change
    this.clearError()
  }
}
