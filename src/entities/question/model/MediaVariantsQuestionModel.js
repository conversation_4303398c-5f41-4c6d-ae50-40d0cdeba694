import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, nextTick, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'

export class MediaVariantsQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const translationsStore = useTranslationsStore()
    this.t = translationsStore.t
    this.data = data

    this.gallery = data.gallery || []
    this.isRequired = ref(!!data.isRequired)
    this.answer = data.answer || {}

    // Make fields reactive
    this.chooseType = ref(data.chooseType) // 'image' | 'video'
    this.variantsType = ref(data.variantsType) // 0 - single, 1 - multiple
    this.maxChooseVariants = ref(data.max_choose_variants || 0)
    this.randomVariantsOrder = ref(data.random_variants_order === 1)
    this.commentEnabled = ref(data.isHaveComment && !data.answerText)
    this.isCommentRequired = ref(data.comment_required)
    this.textFieldParam = ref(data.textFieldParam || {})
    this.placeholderText = ref(data.placeholderText)
    this.skip = ref(data.skip)

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || toValue(this.t('Затрудняюсь ответить'))
    })
    this.skipped = ref(this.previousAnswer?.skipped === 1)
    this.canMoveToNextQuestion = ref(false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: true,
      randomVariantsOrder: toValue(this.randomVariantsOrder),
      gallery: this.gallery,
      selectable: true,
      multiple: toValue(this.variantsType) === 1,
      type: toValue(this.chooseType),
      isRequired: toValue(this.isRequired),
      skipped: this.skipped,
      translations: this.translations,
      maxChooseVariants: toValue(this.maxChooseVariants),
    })

    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: toValue(this.isCommentRequired),
      skipped: this.skipped,
      value: this.answer.comment || this.answer.self_variant || '',
      placeholderText: toValue(this.placeholderText),
      minLength: toValue(this.textFieldParam).min || 0,
      maxLength: toValue(this.textFieldParam).max || 1000,
      title: data.comment_label || toValue(this.t('Ваш комментарий')),
      translations: this.translations,
    })

    this.touched = ref(false)

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return !this.isValid.value
    })

    watch(this.skipped, (v) => {
      if (v) {
        this.galleryController.error.value = null
        this.galleryController.touched.value = false
        this.galleryController.selectedItems.value = []
        this.markInteracted()
        if (toValue(this.commentEnabled)) {
          this.commentController.touched.value = false
          this.commentController.error.value = null
        }
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    watch(this.galleryController.selectedItems, (selectedItems) => {
      if (selectedItems) {
        this.markInteracted()
      }
      if (!this.galleryController.multiple.value && !!selectedItems) {
        this.canMoveToNextQuestion.value = true
      }
    })
  }

  validate() {
    if (!this.touched.value || this.skipped.value)
      return true

    let galleryValid = true
    let commentValid = true

    galleryValid = this.galleryController.validate()

    if (toValue(this.commentEnabled))
      commentValid = this.commentController.validate()

    return galleryValid && commentValid
  }

  setAllAsTouched() {
    this.touched.value = true
    if (toValue(this.commentEnabled))
      this.commentController.touched.value = true
    this.galleryController.setAllAsTouched()
  }

  get hasValue() {
    return this.galleryController?.hasValue || this.commentController?.hasValue
  }

  getData() {
    const data = {
      answer: [],
    }

    if (this.skip && this.skipped.value) {
      return { skipped: 1 }
    }

    if (toValue(this.commentEnabled))
      data.comment = this.commentController.value.value

    const galleryData = this.galleryController.getData()
    const items = galleryData.selectedItems
    data.answer = Array.isArray(items) ? items.map(i => i?.id) : [items?.id]
    return data
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (toValue(this.skip) && toValue(this.skipped))
        return ''

      const items = this.galleryController.selectedItems.value
      const selectedItems = Array.isArray(items) ? items : [items]

      return selectedItems.map((item, index) => {
        const imageOrPicture = toValue(this.chooseType) === 'image' || toValue(this.chooseType) === 'picture'
        const type = imageOrPicture ? toValue(this.t('Изображение')) : toValue(this.t('Видео'))
        return `${type} ${index + 1}`
      }).join(', ')
    })
  }

  resetSelectedItems() {
    const items = this.galleryController.selectedItems.value
    if (Array.isArray(items)) {
      this.galleryController.selectedItems.value = []
    }
    else {
      this.galleryController.selectedItems.value = null
    }
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update variants type and media type
    if (previewData.variantsType !== undefined) {
      this.variantsType.value = previewData.variantsType
    }

    const chooseTypeChanged = previewData.chooseType !== undefined && previewData.chooseType !== toValue(this.chooseType)
    if (chooseTypeChanged) {
      this.chooseType.value = previewData.chooseType
      this.resetSelectedItems()
    }

    // Update max choose variants and random order
    if (previewData.max_choose_variants !== undefined) {
      this.maxChooseVariants.value = previewData.max_choose_variants || 0
    }
    if (previewData.random_variants_order !== undefined) {
      this.randomVariantsOrder.value = previewData.random_variants_order
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }

    if (previewData.skip_text !== undefined) {
      this._skipText.value = previewData.skip_text
    }

    if (!toValue(this.skip)) {
      this.skipped.value = false
    }

    // Update gallery controller with new media items
    if (previewData.chooseMedia) {
      this.galleryController.updateFromPreview({
        enabled: true,
        randomVariantsOrder: toValue(this.randomVariantsOrder),
        gallery: previewData.chooseMedia,
        selectable: true,
        multiple: toValue(this.variantsType) === 1,
        type: toValue(this.chooseType),
        isRequired: toValue(this.isRequired),
        maxChooseVariants: toValue(this.maxChooseVariants),
      })
    }

    // Update comment-related fields
    if (previewData.isHaveComment !== undefined) {
      this.commentEnabled.value = previewData.isHaveComment && !previewData.answerText
    }

    // Update comment controller if enabled
    if (toValue(this.commentEnabled)) {
      this.commentController.updateFromPreview({
        enabled: toValue(this.commentEnabled),
        required: !!previewData.comment_required,
        title: previewData.comment_label || this.t('Ваш комментарий'),
        placeholderText: previewData.placeholderText || '',
        textFieldParam: {
          min: previewData.textFieldParam?.min || 0,
          max: previewData.textFieldParam?.max || 1000,
        },
      })
    }
  }
}
