import useVariablesReplacer from '@shared/composables/useVariablesReplacer'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, ref, toValue, watch } from 'vue'
import { withRootUrl } from '../../../shared/helpers/general'
import { unsubscribe } from '../../poll/api'
import { BaseQuestion } from './BaseQuestion'
import { INTERMEDIATE_BLOCK_TYPES } from './types'

export class InterBlockModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const translationsStore = useTranslationsStore()
    this.t = translationsStore.t
    this.translations = computed(() => {
      return toValue(translationsStore.getInterBlockTranslation(data))
    })
    this.questionTranslations = computed(() => {
      return toValue(translationsStore.getQuestionTranslation(data)) || {}
    })

    this.points = ref({
      points: 0,
      max: 0,
      percent: 0,
    })

    this.variablesReplacer = useVariablesReplacer(this.appContext)

    this.mounted = ref(false)

    this.isImageVisible = ref(false)

    const prevText = ref('')

    this._intermediateBlockInnerRef = ref(data.intermediateBlock || {})
    this.intermediateBlock = computed({
      get: () => this._intermediateBlockInnerRef.value,
      set: value => this._intermediateBlockInnerRef.value = value,
    })

    this.isRequired = ref(!!data.isRequired)
    this.questionId = data.question_id

    // Make name writable with a ref
    this._name = ref(data.name || '')
    this.name = computed({
      get: () => this.questionTranslations?.value.name || this._name.value || '',
      set: (value) => {
        this._name.value = value
      },
    })

    // Make description_html writable
    this._description_html = ref(data.description_html || '')
    this.description_html = computed({
      get: () => {
        const translations = this.translations.value
        const description = translations?.text || this._description_html.value || ''
        return this.replaceVariables(description)
      },
      set: (value) => {
        this._description_html.value = value
      },
    })

    this._showBgInstructionInnerRef = ref(!!this.intermediateBlock.value.show_bg_instruction)
    this.showBgInstruction = computed({
      get: () => this._showBgInstructionInnerRef.value,
      set: value => this._showBgInstructionInnerRef.value = value,
    })

    this._imageShowTimeInnerRef = ref(this.intermediateBlock.value.image_show_time || 0)
    this.imageShowTime = computed({
      get: () => this._imageShowTimeInnerRef.value,
      set: value => this._imageShowTimeInnerRef.value = value,
    })

    this._showImageButtonTextInnerRef = ref(this.intermediateBlock.value.show_image_button_text || '')
    this.showImageButtonText = computed({
      get: () => {
        const translations = toValue(this.translations)
        return translations?.show_image_button_text || this._showImageButtonTextInnerRef.value
      },
      set: value => this._showImageButtonTextInnerRef.value = value,
    })

    this._descriptionInnerRef = ref(data.description || '')
    this.description = computed({
      get: () => {
        if (this.textIsReplaced.value)
          return this._descriptionInnerRef.value

        const translations = toValue(this.translations)
        const defaultDescription = translations?.description || this._descriptionInnerRef.value
        return defaultDescription
      },
      set: value => this._descriptionInnerRef.value = value,
    })

    this.textIsReplaced = ref(false)
    this._textInnerRef = ref(this.intermediateBlock.value.text)
    this.text = computed({
      get: () => {
        if (this.textIsReplaced.value)
          return this._textInnerRef.value

        const translations = toValue(this.translations)
        const defaultText = translations?.text || this._textInnerRef.value
        return defaultText
      },
      set: value => this._textInnerRef.value = value,
    })

    watch([this.variables, this.translations, this.mounted], ([_variables, _translations, _mounted]) => {
      if (!_variables || Object.keys(_variables).length === 0)
        return

      const text = _translations?.text || this.intermediateBlock.value.text

      if (text && _mounted) {
        this.textIsReplaced.value = true
        this.text.value = this.variablesReplacer.replace({
          text,
          variables: _variables,
          answers: this.stringifiedAnswers,
          previewMode: this.mode,
        })
      }

      prevText.value = text
    }, { immediate: true, deep: true })

    this.loading = ref(true)

    this.langs = data.langs

    this.showNumber.value = !!this.intermediateBlock.value.show_question_number

    this._screenTypeInnerRef = ref(this.intermediateBlock.value.screen_type)
    this.screenType = computed({
      get: () => this._screenTypeInnerRef.value,
      set: value => this._screenTypeInnerRef.value = value,
    })

    this.isRequired = ref(this.isFiveSecondTest ? data.isRequired : true)

    this._agreementInnerRef = ref(!!this.intermediateBlock.value.agreement)
    this.agreement = computed({
      get: () => this.screenType.value === INTERMEDIATE_BLOCK_TYPES.END ? false : this._agreementInnerRef.value,
      set: value => this._agreementInnerRef.value = value,
    })
    this._agreementTextInnerRef = ref(this.intermediateBlock.value.agreement_text)
    this.agreementText = computed({
      get: () => {
        const defaultAgreementText = this._agreementTextInnerRef.value || toValue(this.t('Я согласен/согласна на обработку персональных данных'))
        const translations = toValue(this.translations)
        return translations?.agreement_text || defaultAgreementText
      },
      set: value => this._agreementTextInnerRef.value = value,
    })

    /**
     * @example
     * {
      id: 18460,
      intermediate_block_id: 18603,
      social_networks_enabled: 1,
      social_networks: '{"vk":"1","ok":"1"}',
      form: 'square',
      style: 'style1',
      substrate: 1,
      total_counter: 1,
      location_for_total_counter: 'after',
      for_each_counter: 1,
      location_for_each_counter: null,
      size: '32',
      socialNetworks: {
        vk: '1',
        ok: '1',
      }
     */
    this._socialNetworksInnerRef = ref(this.intermediateBlock.value.socNetworks || {})
    this.socialNetworks = computed({
      get: () => this._socialNetworksInnerRef.value,
      set: value => this._socialNetworksInnerRef.value = value,
    })

    this._socialNetworksEnabledInnerRef = ref(this.intermediateBlock.value.socNetworks?.social_networks_enabled)
    this.socialNetworksEnabled = computed({
      get: () => this._socialNetworksEnabledInnerRef.value,
      set: value => this._socialNetworksEnabledInnerRef.value = value,
    })
    this.agreementValue = ref(false)
    this.touched = ref(false)
    this.isImageViewed = ref(data.image_showed)

    this.error = ref(null)

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      if (this.isFiveSecondTest) {
        return !this.isValid.value
      }
      return this.agreement.value && !this.agreementValue.value
    })

    if (this.screenType.value === 3)
      this.agreement.value = false

    this.showUnsubscribeScreen = ref(false)
    this.showUnsubscribeSuccessScreen = ref(false)

    this._textsInnerRef = ref({
      takeSurvey: this.intermediateBlock.value.poll_button_text,
      unsubscribe: this.intermediateBlock.value.unsubscribe_button_text,
      complain: this.intermediateBlock.value.complaint_button_text,
      ready: this.intermediateBlock.value.ready_button_text,
      points: this.intermediateBlock.value.scores_button_text,
      startOver: this.intermediateBlock.value.start_over_button_text,
      finish: this.intermediateBlock.value.close_widget_button_text,
    })
    this.texts = {
      imageAlreadyShown: computed(() => toValue(this.t('Изображение уже было показано'))),
      needToSeeImage: computed(() => toValue(this.t('Нужно посмотреть изображение'))),
      takeSurvey: computed({
        get: () => {
          const defaultPollButtonText = this._textsInnerRef.value.takeSurvey || toValue(this.t('Пройти опрос'))
          return toValue(this.translations).poll_button_text || defaultPollButtonText
        },
        set: value => this._textsInnerRef.value.takeSurvey = value,
      }),
      unsubscribe: computed({
        get: () => {
          const defaultUnsubscribeButtonText = this._textsInnerRef.value.unsubscribe || toValue(this.t('Отписаться от рассылки'))
          return toValue(this.translations).unsubscribe_button_text || defaultUnsubscribeButtonText
        },
        set: value => this._textsInnerRef.value.unsubscribe = value,
      }),
      complain: computed({
        get: () => {
          const defaultComplainButtonText = this._textsInnerRef.value.complain || toValue(this.t('Жалоба'))
          return toValue(this.translations).complaint_button_text || defaultComplainButtonText
        },
        set: value => this._textsInnerRef.value.complain = value,
      }),
      ready: computed({
        get: () => {
          const defaultReadyButtonText = this._textsInnerRef.value.ready || toValue(this.t('Готово'))
          return toValue(this.translations).ready_button_text || defaultReadyButtonText
        },
        set: value => this._textsInnerRef.value.ready = value,
      }),
      points: computed({
        get: () => {
          const defaultScoresButtonText = this._textsInnerRef.value.points || toValue(this.t('Отчет о тестировании'))
          return toValue(this.translations).scores_button_text || defaultScoresButtonText
        },
        set: value => this._textsInnerRef.value.points = value,
      }),
      startOver: computed({
        get: () => {
          const defaultStartOverButtonText = this._textsInnerRef.value.startOver || toValue(this.t('Начать заново'))
          return toValue(this.translations).start_over_button_text || defaultStartOverButtonText
        },
        set: value => this._textsInnerRef.value.startOver = value,
      }),
      finish: computed({
        get: () => {
          const defaultFinishButtonText = this._textsInnerRef.value.finish || toValue(this.t('Завершить'))
          return toValue(this.translations).close_widget_button_text || defaultFinishButtonText
        },
        set: value => this._textsInnerRef.value.finish = value,
      }),
      unsubscribeConfirm: computed(() => toValue(this.t('Вы будете отписаны от рассылки'))),
      staySubscribed: computed(() => toValue(this.t('Я хочу остаться'))),
      unsubscribeSuccess: computed(() => toValue(this.t('Вы отписаны от рассылки'))),
    }
    this._showComplainInnerRef = ref(this.intermediateBlock.value.complaint_button)
    this.showComplain = computed({
      get: () => this._showComplainInnerRef.value,
      set: value => this._showComplainInnerRef.value = value,
    })
    this._showUnsubscribeInnerRef = ref(this.intermediateBlock.value.unsubscribe_button)
    this.showUnsubscribe = computed({
      get: () => this._showUnsubscribeInnerRef.value,
      set: value => this._showUnsubscribeInnerRef.value = value,
    })
    this._showReadyInnerRef = ref(this.intermediateBlock.value.ready_button)
    this.showReady = computed({
      get: () => this._showReadyInnerRef.value,
      set: value => this._showReadyInnerRef.value = value,
    })
    this._showPointsInnerRef = ref(
      this.screenType.value === INTERMEDIATE_BLOCK_TYPES.END
      && this.intermediateBlock.value.scores_button,
    )
    this.showPoints = computed({
      get: () => this._showPointsInnerRef.value,
      set: value => this._showPointsInnerRef.value = value,
    })
    this._showStartOverInnerRef = ref(this.intermediateBlock.value.start_over_button)
    this.showStartOver = computed({
      get: () => this._showStartOverInnerRef.value,
      set: value => this._showStartOverInnerRef.value = value,
    })
    this._showFinishInnerRef = ref(this.intermediateBlock.value.close_widget_button)
    this.showFinish = computed({
      get: () => this._showFinishInnerRef.value,
      set: value => this._showFinishInnerRef.value = value,
    })

    this._readyLinkInnerRef = ref(this.intermediateBlock.value.external_link)
    this.readyLink = computed({
      get: () => this._readyLinkInnerRef.value,
      set: value => this._readyLinkInnerRef.value = value,
    })

    const images = data.endScreenImages || []
    this.images = ref(this.prepareImages(images))
    this._imagesBackgroundInnerRef = ref(this.intermediateBlock.value.logos_backcolor)
    this.imagesBackground = computed({
      get: () => this._imagesBackgroundInnerRef.value,
      set: value => this._imagesBackgroundInnerRef.value = value,
    })

    this.share = null
  }

  positionSorter(a, b) {
    return a.position - b.position
  }

  get isEndScreen() {
    return this.screenType.value === 3
  }

  get isStartScreen() {
    return this.screenType.value === 2
  }

  get isTextScreen() {
    return this.screenType.value === 1
  }

  get isFiveSecondTest() {
    return this.screenType.value === INTERMEDIATE_BLOCK_TYPES.FIVE_SECOND_TEST
  }

  get hasValue() {
    return false
  }

  setAllAsTouched() {
    this.touched.value = true
  }

  validate() {
    this.error.value = null

    if (!this.touched.value)
      return true

    if (this.isFiveSecondTest && !this.isImageViewed.value && this.isRequired.value) {
      this.error.value = this.t('Нужно посмотреть изображение')
      return false
    }
    return true
  }

  getData() {
    // Implement getData logic if needed
    return null
  }

  checkValidity() {
    if (toValue(this.agreement)) {
      return this.agreementValue.value
    }
    return true
  }

  setPoints(points) {
    if (!points) {
      this.points.value = {
        points: 0,
        max: 0,
        percent: 0,
      }
      return
    }
    this.points.value = {
      points: points.points || 0,
      max: points.max || 0,
      percent: points.percent || 0,
    }
  }

  showUnsubscribeConfirmation() {
    this.showUnsubscribeScreen.value = true
  }

  hideUnsubscribeConfirmation() {
    this.showUnsubscribeScreen.value = false
  }

  showUnsubscribeSuccess() {
    this.showUnsubscribeScreen.value = false
    this.showUnsubscribeSuccessScreen.value = true
  }

  async unsubscribe(authKey) {
    await unsubscribe(authKey)
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition) && (this.isTextScreen || this.isFiveSecondTest))
      return true

    // Inter Block only supports BEHAVIOR_ALWAYS
    return false
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    this.isImageVisible.value = false
    this.isImageViewed.value = false

    if (previewData.intermediateBlock) {
      this.intermediateBlock.value = previewData.intermediateBlock
    }
    if (previewData.intermediateBlock?.text !== undefined) {
      this.text.value = previewData.intermediateBlock.text
    }
    if (previewData.intermediateBlock?.description !== undefined) {
      this.description.value = previewData.intermediateBlock.description
    }
    if (previewData.intermediateBlock?.show_bg_instruction !== undefined) {
      this.showBgInstruction.value = !!previewData.intermediateBlock.show_bg_instruction
    }
    if (previewData.intermediateBlock?.image_show_time !== undefined) {
      this.imageShowTime.value = previewData.intermediateBlock.image_show_time
    }
    if (previewData.intermediateBlock?.show_image_button_text !== undefined) {
      this.showImageButtonText.value = previewData.intermediateBlock.show_image_button_text
    }
    if (previewData.intermediateBlock?.show_question_number !== undefined) {
      this.showNumber.value = !!previewData.intermediateBlock.show_question_number
    }
    if (previewData.intermediateBlock?.agreement !== undefined) {
      this.agreement.value = !!previewData.intermediateBlock.agreement
    }
    if (previewData.intermediateBlock?.agreement_text !== undefined) {
      this.agreementText.value = previewData.intermediateBlock.agreement_text
    }
    if (previewData.intermediateBlock?.socNetworks !== undefined) {
      this.socialNetworks.value = previewData.intermediateBlock.socNetworks
    }
    if (previewData.intermediateBlock?.socNetworks?.social_networks_enabled !== undefined) {
      this.socialNetworksEnabled.value = previewData.intermediateBlock.socNetworks.social_networks_enabled
      if (this.socialNetworksEnabled.value) {
        this.socialNetworks.value = previewData.intermediateBlock?.socNetworks
      }
    }
    if (previewData.intermediateBlock?.screen_type !== undefined) {
      this.screenType.value = previewData.intermediateBlock.screen_type
    }
    if (previewData.intermediateBlock?.poll_button_text !== undefined) {
      this.texts.takeSurvey.value = previewData.intermediateBlock.poll_button_text
    }
    if (previewData.intermediateBlock?.unsubscribe_button_text !== undefined) {
      this.texts.unsubscribe.value = previewData.intermediateBlock.unsubscribe_button_text
    }
    if (previewData.intermediateBlock?.complaint_button_text !== undefined) {
      this.texts.complain.value = previewData.intermediateBlock.complaint_button_text
    }
    if (previewData.intermediateBlock?.ready_button_text !== undefined) {
      this.texts.ready.value = previewData.intermediateBlock.ready_button_text
    }
    if (previewData.intermediateBlock?.scores_button_text !== undefined) {
      this.texts.points.value = previewData.intermediateBlock.scores_button_text
    }
    if (previewData.intermediateBlock?.start_over_button_text !== undefined) {
      this.texts.startOver.value = previewData.intermediateBlock.start_over_button_text
    }
    if (previewData.intermediateBlock?.close_widget_button_text !== undefined) {
      this.texts.finish.value = previewData.intermediateBlock.close_widget_button_text
    }
    if (previewData.intermediateBlock?.complaint_button !== undefined) {
      this.showComplain.value = previewData.intermediateBlock.complaint_button
    }
    if (previewData.intermediateBlock?.unsubscribe_button !== undefined) {
      this.showUnsubscribe.value = previewData.intermediateBlock.unsubscribe_button
    }
    if (previewData.intermediateBlock?.ready_button !== undefined) {
      this.showReady.value = previewData.intermediateBlock.ready_button
    }
    if (previewData.intermediateBlock?.scores_button !== undefined) {
      this.showPoints.value = previewData.intermediateBlock.scores_button
    }
    if (previewData.intermediateBlock?.scores_button_text !== undefined) {
      this.texts.points.value = previewData.intermediateBlock.scores_button_text
    }
    if (previewData.intermediateBlock?.start_over_button !== undefined) {
      this.showStartOver.value = previewData.intermediateBlock.start_over_button
    }
    if (previewData.intermediateBlock?.close_widget_button !== undefined) {
      this.showFinish.value = previewData.intermediateBlock.close_widget_button
    }
    if (previewData.intermediateBlock?.external_link !== undefined) {
      this.readyLink.value = previewData.intermediateBlock.external_link
    }
    if (previewData.intermediateBlock?.logos_backcolor !== undefined) {
      this.imagesBackground.value = previewData.intermediateBlock.logos_backcolor
    }

    if (previewData.endScreenImages) {
      this.images.value = this.prepareImages(previewData.endScreenImages)
    }

    this.showUnsubscribeScreen.value = false
    this.showUnsubscribeSuccessScreen.value = false
  }

  prepareImage(image) {
    const _link = ref(image.link)
    const link = computed({
      get: () => this.variablesReplacer.replace({
        text: _link.value,
        variables: this.variables,
        answers: this.stringifiedAnswers,
        ignoreInteractiveElements: true,
        previewMode: this.mode,
      }),
      set: value => _link.value = value,
    })

    const _description = ref(image.description)
    const description = computed({
      get: () => this.variablesReplacer.replace({
        text: _description.value,
        variables: this.variables,
        answers: this.stringifiedAnswers,
        ignoreInteractiveElements: true,
        previewMode: this.mode,
      }),
      set: value => _description.value = value,
    })

    return {
      id: image.id,
      url: withRootUrl(image.logo) || image.external_logo,
      width: image.width,
      height: image.height,
      link,
      description,
      isExternal: !!image.external_logo,
    }
  }

  prepareImages(images) {
    const sortedImages = [...images].sort(this.positionSorter)
    return sortedImages.map(image => this.prepareImage(image))
  }
}
