import { useTranslationsStore } from '@shared/store/translationsStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import { computed, isRef, nextTick, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'
import { CLASSIFIER_QUESTION } from './types'

export class Matrix3dQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const tabletStore = useTabletStore()
    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    /**
     * @type {number|null}
     * @description ID вопроса-донора для строк
     */
    this.donorRowsId = ref(data.donor_rows)

    /**
     * @type {number|null}
     * @description ID вопроса-донора для столбцов
     */
    this.donorColumnsId = ref(data.donor_columns)

    /**
     * @type {BaseQuestion|null}
     * @description Вопрос-донор для строк
     */
    this.donorRowsQuestion = shallowRef(null)

    /**
     * @type {BaseQuestion|null}
     * @description Вопрос-донор для столбцов
     */
    this.donorColumnsQuestion = shallowRef(null)

    /**
     * @type {boolean}
     * @description Брать выбранные варианты из донора или невыбранные (для строк)
     */
    this.donorRowsSelectedType = ref(data.donorSelected)

    /**
     * @type {boolean}
     * @description Брать выбранные варианты из донора или невыбранные (для столбцов)
     */
    this.donorColumnsSelectedType = ref(data.donorColumnsSelected)

    // Add computed properties for donor variants
    this.selectedDonorRowVariants = computed(() => {
      if (!toValue(this.donorRowsId))
        return []

      const donorQuestion = toValue(this.donorRowsQuestion)
      if (!donorQuestion)
        return []

      const donorVariants = toValue(donorQuestion.getDonorVariants(donorQuestion, toValue(this.donorRowsSelectedType)))

      return donorVariants || []
    })

    this.selectedDonorColumnVariants = computed(() => {
      if (!toValue(this.donorColumnsId))
        return []

      const donorQuestion = toValue(this.donorColumnsQuestion)
      if (!donorQuestion)
        return []

      const donorVariants = toValue(donorQuestion.getDonorVariants(donorQuestion, toValue(this.donorColumnsSelectedType)))

      return donorVariants || []
    })

    const matrixSettings = data.matrixSettings || {}
    const matrixElements = Array.isArray(data.matrixElements)
      ? { rows: data.matrixElements, columns: [] }
      : data.matrixElements || { rows: [], columns: [] }

    this.rowsAboveVariants = ref(matrixSettings.rowsAboveVariants === '1')
    this.matrixElements = ref(matrixElements)
    this.variantsType = ref(data.variantsType || 0)
    this.multipleChoice = computed(() => toValue(this.variantsType) === 1)

    this._selectPlaceholderText = ref(data.selectPlaceholderText)
    this.selectPlaceholderText = computed({
      get: () => {
        const translations = toValue(this.translations)
        if (translations) {
          return translations.placeholder_text || this._selectPlaceholderText.value || ''
        }
        return this._selectPlaceholderText.value || ''
      },
      set: (value) => {
        this._selectPlaceholderText.value = value
      },
    })
    this.showTooltips = ref(data.show_tooltips === 1)

    this.skip = ref(data.skip)

    this._skipText = ref(data.skip_text)
    this.skipText = computed({
      get: () => {
        const translations = toValue(this.translations)
        if (translations) {
          return translations.skip_text || this._skipText.value || toValue(this.t('Затрудняюсь ответить'))
        }
        return this._skipText.value || toValue(this.t('Затрудняюсь ответить'))
      },
      set: (value) => {
        this._skipText.value = value
      },
    })
    this.skipped = ref(data.answer?.skipped === 1)
    this.skipVariant = ref(data.skip_variant === 1)
    this.skipRow = ref(data.skip_row === 1)
    this.skipColumn = ref(data.skip_column === 1)

    this.skipItem = computed(() => ({
      label: computed(() => toValue(this.skipText)),
      id: '-1',
      value: '-1',
    }))

    this.parsedPreviousAnswer = {}
    if (typeof data.answer?.answer === 'string') {
      try {
        this.parsedPreviousAnswer = JSON.parse(data.answer?.answer || '{}')
      }
      catch (error) {
        console.error('Error parsing previous answer:', error)
      }
    }

    // Initial rows and columns initialization
    this.rows = ref(!this.hasDonorRows ? this.initializeRows() : [])
    this.columns = ref(!this.hasDonorColumns ? this.initializeColumns() : [])

    // Initialize selectedVariants early to avoid undefined errors in watcher
    this.selectedVariants = ref(this.initializeSelectedVariants(this.parsedPreviousAnswer))

    this.selectedVariantsForDonorInitialized = false

    // Watch for changes in donor variants
    watch([this.selectedDonorRowVariants, this.selectedDonorColumnVariants], ([newRowVariants, newColumnVariants]) => {
      let rowsChanged = false
      let columnsChanged = false

      // Store existing rows and columns before processing
      const existingRows = this.rows.value || []
      const existingColumns = this.columns.value || []

      if (toValue(this.hasDonorRows)) {
        const newRows = this.initializeRows(newRowVariants)

        const preservedRows = []

        newRows.forEach((newRow) => {
          const existingRow = existingRows.find(r => r.id === newRow.id)
          if (existingRow) {
            preservedRows.push(existingRow)
          }
          else {
            preservedRows.push(newRow)
            rowsChanged = true
          }
        })

        // Check if any rows were removed
        if (existingRows.length !== preservedRows.length) {
          rowsChanged = true
        }

        this.rows.value = preservedRows
      }

      if (toValue(this.hasDonorColumns)) {
        const newColumns = this.initializeColumns(newColumnVariants)
        const preservedColumns = []

        newColumns.forEach((newColumn) => {
          const existingColumn = existingColumns.find(c => c.id === newColumn.id)
          if (existingColumn) {
            preservedColumns.push(existingColumn)
          }
          else {
            preservedColumns.push(newColumn)
            columnsChanged = true
          }
        })

        // Check if any columns were removed
        if (existingColumns.length !== preservedColumns.length) {
          columnsChanged = true
        }

        this.columns.value = preservedColumns
      }

      // Only reset selected variants if structure changed significantly
      if ((rowsChanged || columnsChanged) && this.selectedVariants?.value && this.selectedVariantsForDonorInitialized) {
        const currentSelectedVariants = this.selectedVariants.value
        const newSelectedVariants = this.initializeSelectedVariants(this.parsedPreviousAnswer)

        // Try to preserve current selections for existing row/column combinations
        toValue(this.rows).forEach((row, rowIndex) => {
          toValue(this.columns).forEach((column, columnIndex) => {
          // Find the old indices for this row/column combination
            const oldRowIndex = currentSelectedVariants.findIndex((_, idx) => {
              const oldRow = existingRows?.[idx]
              return oldRow?.id === row.id
            })
            const oldColumnIndex = currentSelectedVariants[0]?.findIndex((_, idx) => {
              const oldColumn = existingColumns?.[idx]
              return oldColumn?.id === column.id
            })

            // If we found the old selection, preserve it
            if (oldRowIndex >= 0 && oldColumnIndex >= 0 && currentSelectedVariants[oldRowIndex]?.[oldColumnIndex]) {
              newSelectedVariants[rowIndex][columnIndex] = currentSelectedVariants[oldRowIndex][oldColumnIndex]
            }
          })
        })

        this.selectedVariants.value = newSelectedVariants
      }
      else {
        this.selectedVariants.value = this.initializeSelectedVariants(this.parsedPreviousAnswer)
        this.selectedVariantsForDonorInitialized = true
      }
    }, { immediate: true })

    this.touched = ref(false)
    this.error = ref(null)

    this.commentEnabled = ref(data.comment_enabled === 1)
    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: data.comment_required === 1,
      skipped: this.skipped,
      value: data.answer?.self_variant || '',
      placeholderText: data.placeholderText,
      minLength: this.textFieldParam.min || 0,
      maxLength: this.textFieldParam.max || 1000,
      title: data.comment_label || toValue(this.t('Ваш комментарий')),
      translations: this.translations,
    })

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: toValue(this.enableGallery),
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this.isValid = computed(() => {
      if (toValue(this.skip) && toValue(this.skipped))
        return true
      return this.validate()
    })

    this.blocked = computed(() => {
      return toValue(this.touched) && !toValue(this.isValid)
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        const selectedVariants = toValue(this.selectedVariants)
        // clear all cells
        selectedVariants.forEach((row, rowIndex) => {
          row.forEach((cell, cellIndex) => {
            cell.forEach(() => {
              this.selectedVariants.value[rowIndex][cellIndex] = []
            })
          })
        })
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    this.columns.value.forEach((column) => {
      column.variants.forEach((variant) => {
        watch(variant.selected, () => {
          this.validate()
        })
      })
    })

    watch(this.selectedVariants, (selectedVariants) => {
      const hasAnySelection = selectedVariants.some(row =>
        row.some(cell => cell.length > 0 && cell[0].id !== '-1'),
      )

      if (hasAnySelection) {
        this.markInteracted()
      }

      const singleRowAndColWithSingleSelection
        = toValue(this.rows).length === 1
        && toValue(this.columns).length === 1
        && !toValue(this.multipleChoice)
        && selectedVariants?.[0]?.[0]?.length === 1

      if (
        !toValue(this.commentEnabled)
        && singleRowAndColWithSingleSelection
        && !tabletStore.isTabletMode
      ) {
        this.canMoveToNextQuestion.value = true
      }
    }, { deep: true })

    watch(this.translations, () => {
      this.resetSelectedVariants()
    })
  }

  get hasGallery() {
    return toValue(this.enableGallery)
  }

  positionSorter(a, b) {
    if (a.position === null)
      return 1
    if (b.position === null)
      return -1
    return a.position - b.position
  }

  initializeRows(donorVariants = []) {
    let rows = toValue(this.matrixElements).rows

    if (toValue(this.donorRows)) {
      if (donorVariants.length === 0) {
        return []
      }
      rows = toValue(this.matrixElements).rows.filter((row) => {
        const rowId = row.donor_dictionary_element_id || row.donor_variant_id
        return donorVariants.some(v => v.id === rowId)
      })

      // Определяем, выбран ли вариант "Свой вариант" у вопроса-донора
      const donorRowSelfVariant = donorVariants.find(v => v.id === 'is_self_answer')

      if (donorRowSelfVariant) {
        // Если вариант "Свой вариант" у вопроса-донора есть,
        // Получаем "Свой вариант" у вопроса-реципиента
        // У вопроса реципиента свой вариант приходит с полями
        // donor_variant_id: null
        // donor_dictionary_element_id: null
        const recipientSelfVariant = toValue(this.matrixElements).rows.find(v => v.donor_variant_id === null && v.donor_dictionary_element_id === null)

        if (recipientSelfVariant) {
          rows.push({
            ...recipientSelfVariant,
            // Название столбца берем из значения поля донора
            name: computed(() => {
              return toValue(donorRowSelfVariant.alternativeVariantLabel)
            }),
            description: computed(() => {
              return toValue(donorRowSelfVariant.description)
            }),
          })
        }
      }
    }

    return rows
      .sort(this.positionSorter)
      .map((row, index) => this.createRow(row, index))
  }

  initializeColumns(donorVariants = []) {
    let columns = toValue(this.matrixElements).columns

    const donorVariantsByIds = donorVariants.reduce((acc, v) => {
      acc[v.id] = v
      return acc
    }, {})

    if (toValue(this.donorColumns)) {
      if (donorVariants.length === 0) {
        return []
      }
      columns = toValue(this.matrixElements).columns.filter((column) => {
        const columnId = column.donor_dictionary_element_id || column.donor_variant_id
        return donorVariantsByIds[columnId]
      })

      // Определяем, выбран ли вариант "Свой вариант" у вопроса-донора
      const donorColumnSelfVariant = donorVariants.find(v => v.id === 'is_self_answer')

      if (donorColumnSelfVariant) {
        // Если вариант "Свой вариант" у вопроса-донора есть,
        // Получаем "Свой вариант" у вопроса-реципиента
        // У вопроса реципиента свой вариант приходит с полями
        // donor_variant_id: null
        // donor_dictionary_element_id: null
        const recipientSelfVariant = toValue(this.matrixElements).columns.find(v => v.donor_variant_id === null && v.donor_dictionary_element_id === null)

        if (recipientSelfVariant) {
          columns.push({
            ...recipientSelfVariant,
            // Название столбца берем из значения поля донора
            name: computed(() => {
              return toValue(donorColumnSelfVariant.alternativeVariantLabel)
            }),
            description: computed(() => {
              const id = recipientSelfVariant.donor_variant_id || recipientSelfVariant.donor_dictionary_element_id
              return toValue(donorColumnSelfVariant.description) || toValue(donorVariantsByIds[id]?.description)
            }),
          })
        }
      }
    }

    return columns
      .sort(this.positionSorter)
      .map((column, colIndex) => {
        const transformedVariants = this.getTransformedVariants(column?.variants)
        return this.createColumn(column, colIndex, transformedVariants)
      })
  }

  initializeSelectedVariants(previousAnswer) {
    return toValue(this.rows).map(row =>
      toValue(this.columns).map((column) => {
        const previousValue = previousAnswer[row.id]?.[column.id]
        if (previousValue) {
          if (previousValue === '-1') {
            return [toValue(this.skipItem)]
          }
          else if (Array.isArray(previousValue)) {
            return previousValue.map((id) => {
              if (id === '-1') {
                return toValue(this.skipItem)
              }
              else {
                return this.findVariantById(column, id)
              }
            }).filter(Boolean)
          }
          else {
            const variant = this.findVariantById(column, previousValue)
            return variant ? [variant] : []
          }
        }
        return []
      }),
    )
  }

  findVariantById(column, id) {
    return column.variants.find(v => v.id === id)
  }

  getTransformedVariants(variants) {
    const transformedVariants = variants.map(variant => this.createVariant(variant))

    if (toValue(this.skipVariant)) {
      transformedVariants.push(toValue(this.skipItem))
    }

    return transformedVariants
  }

  getSelectedVariants(rowIndex, columnIndex) {
    return this.selectedVariants.value?.[rowIndex]?.[columnIndex]
  }

  setSelectedVariants(rowIndex, columnIndex, value) {
    // set arrays if not set
    if (!Array.isArray(this.selectedVariants.value[rowIndex])) {
      this.selectedVariants.value[rowIndex] = []
    }
    if (!Array.isArray(this.selectedVariants.value[rowIndex][columnIndex])) {
      this.selectedVariants.value[rowIndex][columnIndex] = []
    }

    if (Array.isArray(value)) {
      const selectedVariants = this.selectedVariants.value?.[rowIndex]?.[columnIndex]
      // find item that is not in selectedVariants
      const lastAddedVariant = value.find(v => !selectedVariants.some(sv => sv.id === v.id))
      if (lastAddedVariant?.id === '-1') {
        this.selectedVariants.value[rowIndex][columnIndex] = [lastAddedVariant]
      }
      else {
        const valueWithoutSkip = value.filter(v => v.id !== '-1')
        this.selectedVariants.value[rowIndex][columnIndex] = [...valueWithoutSkip]
      }
    }
    else {
      this.selectedVariants.value[rowIndex][columnIndex] = [value]
    }

    if (toValue(this.skipped))
      this.skipped.value = false

    this.validate()
  }

  resetFields() {
    this.touched.value = false
    this.error.value = null
    this.columns.value.forEach((column) => {
      column.variants.forEach((variant) => {
        if (isRef(variant.selected))
          variant.selected.value = false
        else
          variant.selected = false
      })
    })

    if (toValue(this.commentEnabled)) {
      this.commentController.touched.value = false
      this.commentController.error.value = null
    }
  }

  resetSelectedVariants() {
    this.selectedVariants.value = toValue(this.rows).map(_ =>
      toValue(this.columns).map(() => []),
    )
  }

  resetSkipState() {
    this.skipped.value = false

    this.rows.value.forEach((row) => {
      row.skipped = false
    })

    this.columns.value.forEach((column) => {
      column.skipped = false
    })
  }

  setAllAsTouched() {
    this.touched.value = true

    if (toValue(this.commentEnabled)) {
      this.commentController.touched.value = true
    }
  }

  validate() {
    this.error.value = null
    let valid = true

    if (toValue(this.skip) && toValue(this.skipped))
      return true
    if (!toValue(this.touched))
      return true

    const isRequired = toValue(this.isRequired)

    if (isRequired) {
      const selectedVariants = toValue(this.selectedVariants)
      const allCellsAnswered = selectedVariants.every((row) => {
        if (toValue(row.skipped))
          return true
        return row.every(cell => cell.length > 0)
      })
      if (!allCellsAnswered) {
        this.error.value = this.t('Не все оценки поставлены')
        valid = false
      }
    }

    let commentValid = true

    if (toValue(this.commentEnabled)) {
      commentValid = this.commentController.validate()
    }

    return valid && commentValid
  }

  getData() {
    if (toValue(this.skip) && toValue(this.skipped)) {
      return { skipped: 1 }
    }

    const answer = {}
    toValue(this.rows).forEach((row, rowIndex) => {
      answer[row.id] = {}
      toValue(this.columns).forEach((column, columnIndex) => {
        const isSkipped = toValue(column.skipped)
        if (isSkipped) {
          answer[row.id][column.id] = ['-1']
        }
        else {
          const selectedVariants = this.getSelectedVariants(rowIndex, columnIndex)
          if (selectedVariants && selectedVariants.length > 0) {
            const selectedVariantsIds = Array.isArray(selectedVariants) ? selectedVariants.map(v => v.id) : [selectedVariants.id]
            answer[row.id][column.id] = selectedVariantsIds
          }
          else {
            answer[row.id][column.id] = ['']
          }
        }
      })
    })

    const data = { answer }

    if (toValue(this.commentEnabled)) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    if (toValue(this.skip) && toValue(this.skipped)) {
      return this.t('Пропущено')
    }

    return toValue(this.rows).map(row =>
      `${row.name}: ${toValue(this.columns).map(column =>
        `${column.name} - ${this.getSelectedVariants(toValue(this.rows).indexOf(row), toValue(this.columns).indexOf(column)).map(v => v.name).join(', ') || this.t('Не выбрано')}`,
      ).join('; ')}`,
    ).join(' | ')
  }

  get hasValue() {
    if (toValue(this.skip) && toValue(this.skipped))
      return true

    if (this.parsedPreviousAnswer && Object.keys(this.parsedPreviousAnswer).length > 0) {
      return true
    }
    return this.selectedVariants.value.some(row => row.some(cell => cell.length > 0))
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // 3D Matrix question only supports BEHAVIOR_ALWAYS
    return false
  }

  /**
   * Override addDependencies to handle multiple donors
   */
  addDependencies(allQuestionsById) {
    if (toValue(this.donorRowsId)) {
      this.donorRowsQuestion.value = allQuestionsById[toValue(this.donorRowsId)]
    }

    if (toValue(this.donorColumnsId)) {
      this.donorColumnsQuestion.value = allQuestionsById[toValue(this.donorColumnsId)]
    }

    // Handle view logic dependencies
    if (Array.isArray(this.viewLogic) && this.viewLogic.length) {
      this.viewLogic.forEach((rule) => {
        const dependentQuestion = allQuestionsById[rule.condition_question_id]
        if (dependentQuestion) {
          this.dependencyQuestionsById.value = {
            ...this.dependencyQuestionsById.value,
            [rule.condition_question_id]: dependentQuestion,
          }
        }
      })
    }
  }

  toggleVariant(columnIndex, variantIndex) {
    const column = toValue(this.columns)[columnIndex]
    const variant = column.variants[variantIndex]

    if (toValue(this.skipped)) {
      this.skipped.value = false
    }

    if (toValue(column.skipped)) {
      column.skipped.value = false
    }

    if (toValue(this.multipleChoice)) {
      variant.selected.value = !variant.selected.value
    }
    else {
      column.variants.forEach((v, index) => {
        v.selected.value = index === variantIndex
      })
    }
  }

  toggleSkipColumn(columnIndex) {
    const column = toValue(this.columns)?.[columnIndex]
    column.skipped = !column.skipped
    const isSkipped = toValue(column.skipped)
    if (isSkipped) {
      this.markInteracted()
      this.selectedVariants.value.forEach((row) => {
        row[columnIndex] = [toValue(this.skipItem)]
      })
    }
    else {
      this.selectedVariants.value.forEach((row) => {
        row[columnIndex] = []
      })
    }
  }

  isRecipientVisible() {
    // First check donor visibility for rows or columns
    // If we have donor rows configuration
    let hasSelectedDonorRows = false
    let hasSelectedDonorColumns = false

    if (!toValue(this.hasDonorRows) && !toValue(this.hasDonorColumns)) {
      return true
    }

    if (toValue(this.donorRows)) {
      hasSelectedDonorRows = !!this.selectedDonorRowVariants.value?.length
    }

    // If we have donor columns configuration
    if (toValue(this.donorColumns)) {
      hasSelectedDonorColumns = !!this.selectedDonorColumnVariants.value?.length
    }

    // Then check view logic visibility
    return (hasSelectedDonorRows || hasSelectedDonorColumns) && this.isVisibleByViewLogic()
  }

  toggleSkipRow(rowIndex) {
    const row = toValue(this.rows)[rowIndex]
    row.skipped.value = !row.skipped.value
    const isSkipped = toValue(row.skipped)
    if (isSkipped) {
      this.markInteracted()
      this.selectedVariants.value[rowIndex].forEach((cell, index) => {
        this.selectedVariants.value[rowIndex][index] = [toValue(this.skipItem)]
      })
    }
    else {
      this.selectedVariants.value[rowIndex].forEach((cell, index) => {
        this.selectedVariants.value[rowIndex][index] = []
      })
    }
  }

  /**
   * Возвращает отображаемое имя для варианта реципиента
   * Если донор является древовидным классификатором, использует полный путь, если он доступен
   * @param {object} v - Объект варианта
   * @param {object} donorQuestion - вопрос донор
   * @returns {string} Отображаемое имя варианта
   */
  getRecipientVariantName(v, donorQuestion = null) {
    const donor = toValue(donorQuestion) || toValue(this.donorQuestion)
    const defaultName = v.name

    if (!donor)
      return defaultName

    // Проверяем, является ли донор классификатором и древовидным типом
    if (donor?.type === CLASSIFIER_QUESTION && toValue(donor.isTreeType)) {
      return v.path || defaultName
    }
    return defaultName
  }

  setSelectedVariantsLength() {
    const rows = toValue(this.rows)
    const columns = toValue(this.columns)
    const currentSelectedVariants = this.selectedVariants.value || []

    // Create new array with correct dimensions
    const newSelectedVariants = rows.map((_, rowIndex) => {
      // If row exists in current selected variants, use it or create new
      const existingRow = currentSelectedVariants[rowIndex] || []
      return columns.map((_, colIndex) => {
        // If cell exists in existing row, use it or create new empty array
        return existingRow[colIndex] || []
      })
    })

    this.selectedVariants.value = newSelectedVariants
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update matrix settings
    if (previewData.matrixSettings) {
      this.rowsAboveVariants.value = !!previewData.matrixSettings.rowsAboveVariants
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }
    if (!toValue(this.skip)) {
      this.skipped.value = false
    }

    const someSkipValuesAreChanged = previewData.skip !== toValue(this.skip)
      || previewData.skip_variant !== toValue(this.skipVariant)
      || previewData.skip_row !== toValue(this.skipRow)
      || previewData.skip_column !== toValue(this.skipColumn)

    if (previewData.skip_text !== undefined) {
      this.skipText.value = previewData.skip_text
    }
    if (previewData.skip_variant !== undefined) {
      this.skipVariant.value = previewData.skip_variant
    }
    if (previewData.skip_row !== undefined) {
      this.skipRow.value = previewData.skip_row
    }
    if (previewData.skip_column !== undefined) {
      this.skipColumn.value = previewData.skip_column
    }

    // Update variants type
    const variantsTypeChanged = previewData.variantsType !== undefined && previewData.variantsType !== toValue(this.variantsType)

    if (variantsTypeChanged) {
      this.variantsType.value = previewData.variantsType
      this.resetSelectedVariants()
    }

    // Update select placeholder text
    if (previewData.selectPlaceholder !== undefined) {
      this.selectPlaceholderText.value = previewData.selectPlaceholder
    }

    // Update show tooltips
    if (previewData.show_tooltips !== undefined) {
      this.showTooltips.value = previewData.show_tooltips
    }

    // Update comment-related fields
    if (previewData.commentEnabled !== undefined) {
      this.commentEnabled.value = previewData.commentEnabled
    }
    if (toValue(this.commentEnabled) && this.commentController) {
      this.commentController.updateFromPreview({
        enabled: toValue(this.commentEnabled),
        required: previewData.comment_required || false,
        title: previewData.comment_label || '',
        placeholderText: previewData.placeholderText || '',
        textFieldParam: {
          min: previewData.textFieldParam?.min || 0,
          max: previewData.textFieldParam?.max || 1000,
        },
      })
    }

    // Update gallery if enabled
    if (previewData.enableGallery !== undefined) {
      this.enableGallery.value = previewData.enableGallery
    }

    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: previewData.gallery || [],
      })
    }

    // Update matrix elements
    if (previewData.matrixElements) {
      const { rows, columns } = previewData.matrixElements

      // Обновляем строки
      const updatedRows = rows.map((row, index) => {
        const existingRow = toValue(this.rows).find(r => r.id === row.id)
        if (existingRow) {
          // Обновляем существующую строку
          existingRow.name = row.name
          existingRow.description = row.description
          existingRow.position = row.position
          return existingRow
        }
        else {
          // Создаем новую строку
          return this.createRow(row, index)
        }
      })

      // Обновляем столбцы
      const updatedColumns = columns.map((column, index) => {
        const existingColumn = toValue(this.columns).find(c => c.id === column.id)
        if (existingColumn) {
          // Обновляем существующий столбец
          existingColumn.name = column.name
          existingColumn.description = column.description
          existingColumn.position = column.position
          existingColumn.variants = this.getTransformedVariants(column.variants)
          return existingColumn
        }
        else {
          // Создаем новый столбец
          const variantsWithUpdatedIds = column.variants.map((variant) => {
            const id = variant.id === '0' ? variant.persistentId : variant.id
            return {
              ...variant,
              id,
            }
          })
          const variants = this.getTransformedVariants(variantsWithUpdatedIds)
          return this.createColumn(column, index, variants)
        }
      })

      // Check if column order has changed
      const currentColumnIds = toValue(this.columns).map(col => col.id)
      const newColumnIds = updatedColumns.sort(this.positionSorter).map(col => col.id)
      const hasColumnOrderChanged = !currentColumnIds.every((id, index) => id === newColumnIds[index])
      const hasColumnLengthChanged = currentColumnIds.length !== newColumnIds.length

      const currentRowIds = toValue(this.rows).map(row => row.id)
      const newRowIds = updatedRows.sort(this.positionSorter).map(row => row.id)
      const hasRowOrderChanged = !currentRowIds.every((id, index) => id === newRowIds[index])
      const hasRowLengthChanged = currentRowIds.length !== newRowIds.length

      // Сортируем строки и столбцы по полю position
      this.rows.value = updatedRows.sort(this.positionSorter)
      this.columns.value = updatedColumns.sort(this.positionSorter)
      this.setSelectedVariantsLength()

      if (hasColumnOrderChanged && !hasColumnLengthChanged) {
        this.resetSelectedVariants()
      }

      if (hasRowOrderChanged && !hasRowLengthChanged) {
        this.resetSelectedVariants()
      }

      if (someSkipValuesAreChanged) {
        this.resetSkipState()
      }
    }
  }

  createRow(row, index) {
    const _name = ref(this.getRecipientVariantName(row, this.donorRowsQuestion))
    const _description = ref(row.description)
    const self = this

    return {
      ...row,
      name: computed({
        get: () => {
          const translation = this.translations.value?.matrixElements?.[row.id]
          return translation?.name || _name.value
        },
        set: (value) => {
          _name.value = value
        },
      }),
      description: computed({
        get: () => {
          const translation = this.translations.value?.matrixElements?.[row.id]
          return translation?.description || _description.value
        },
        set: (value) => {
          _description.value = value
        },
      }),
      skipped: computed({
        get: () => {
          if (self.selectedVariants.value[index] === undefined) {
            return false
          }

          return self.selectedVariants.value[index].every(cell => cell.length === 1 && cell[0].id === '-1')
        },
        set: (value) => {
          const rowIndex = index
          if (value) {
            self.selectedVariants.value[rowIndex].forEach((cell, cellIndex) => {
              self.selectedVariants.value[rowIndex][cellIndex] = [toValue(self.skipItem)]
            })
          }
          else {
            self.selectedVariants.value[rowIndex].forEach((cell, cellIndex) => {
              self.selectedVariants.value[rowIndex][cellIndex] = []
            })
          }
        },
      }),
    }
  }

  createColumn(column, colIndex, transformedVariants) {
    const _name = ref(this.getRecipientVariantName(column, this.donorColumnsQuestion))
    const _description = ref(column.description)
    const self = this

    return {
      ...column,
      name: computed({
        get: () => {
          const translation = this.translations.value?.matrixElements?.[column.id]
          return translation?.name || _name.value
        },
        set: (value) => {
          _name.value = value
        },
      }),
      description: computed({
        get: () => {
          const translation = this.translations.value?.matrixElements?.[column.id]
          return translation?.description || _description.value
        },
        set: (value) => {
          _description.value = value
        },
      }),
      skipped: computed({
        get: () => {
          if (self.selectedVariants.value[colIndex] === undefined) {
            return false
          }

          return self.selectedVariants.value.every(row => row[colIndex].length === 1 && row[colIndex][0].id === '-1')
        },
        set: (value) => {
          if (value) {
            self.selectedVariants.value.forEach((row) => {
              row[colIndex] = [toValue(self.skipItem)]
            })
          }
          else {
            self.selectedVariants.value.forEach((row) => {
              row[colIndex] = []
            })
          }
        },
      }),
      variants: transformedVariants,
    }
  }

  createVariant(variant) {
    const _label = ref(variant.name)
    return {
      label: computed({
        get: () => {
          const translation = this.translations.value?.matrixVariants?.[variant.id]
          return translation?.name || _label.value
        },
        set: (value) => {
          _label.value = value
        },
      }),
      persistentId: variant.persistentId || variant.id,
      id: variant.id || variant.persistentId,
      value: variant.id.toString(),
    }
  }
}
