import MaskTypes from '@shared/constants/maskTypes'
import { getMonths } from '@shared/helpers/date'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, ref, shallowRef, toValue, watch } from 'vue'
import { BaseQuestion } from './BaseQuestion'
import { MaskedFieldModel } from './MaskedFieldModel'

export class QuizQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.t = store.t
    this.selectedLang = store.selectedLang

    this.months = computed(() => getMonths(this.t))
    this.maskedFields = shallowRef([])
    this.initialTranslations = toValue(this.translations)

    /**
     * Предыдущие заполненные поля
     * {
     *  fieldId: 'value'
     * }
     * @type {[key: string]: string}
     */
    this.previouslyFilledFields = {}

    try {
      this.previouslyFilledFields = JSON.parse(this.previousAnswer?.answer || '{}')
    }
    catch {
      this.previouslyFilledFields = {}
    }

    Object.values(data.values).forEach((fieldData) => {
      if (typeof fieldData.id === 'number') {
        const maskedField = this.createMaskedField(fieldData)
        this.maskedFields.value.push(maskedField)
      }
    })

    this.touched = ref(false)
    this.err = ref(null)

    this.isValid = computed(() => this.checkValidity())
    this.blocked = computed(() => this.touched.value && !this.isValid.value)

    this.maskedFields.value.forEach((field) => {
      watch(() => field.hasInteracted.value, (hasInteracted) => {
        if (hasInteracted) {
          this.markInteracted()
        }
      })
    })
  }

  createMaskedField(fieldData) {
    const maskType = Number(fieldData.maskType)

    const fieldTranslations = this.getFieldTranslations(fieldData.id, this.translations)

    const previousValue = this.previouslyFilledFields[fieldData.id]
    const parsedPreviousValue = this.parsePreviousAnswer(previousValue, maskType)

    return new MaskedFieldModel({
      id: fieldData.id,
      translations: fieldTranslations,
      months: this.months,
      type: maskType,
      previousAnswer: parsedPreviousValue,
      fieldConfig: {
        label: fieldData.label,
        minLength: fieldData.textFieldParam.min,
        maxLength: fieldData.textFieldParam.max,
        placeholderText: fieldData.placeholderText,
        value: fieldData.value,
        required: fieldData.isRequired,
        multiline: fieldData.isTextarea === 1,
      },
      config: fieldData.maskConfig,
    })
  }

  /**
   * Получение переводов для конкретного поля Анкеты
   * @param {number} fieldId - ID поля
   * @param {Array<object>} translations - Массив переводов
   * @returns {object} - Объект с переводами
   */
  getFieldTranslations(fieldId, translations) {
    const fieldTranslation = computed(() => toValue(translations)?.formLangs?.[fieldId])
    return computed(() => {
      return {
        ...toValue(this.initialTranslations),
        name: toValue(fieldTranslation)?.name,
        placeholder_text: toValue(fieldTranslation)?.placeholder,
        ...toValue(fieldTranslation),
      }
    })
  }

  validateFields() {
    this.err.value = null
    if (!this.touched.value)
      return true

    let isValid = true
    this.maskedFields.value.forEach((field) => {
      if (!field.isValid.value) {
        isValid = false
        this.err.value = this.t('Пожалуйста, заполните все обязательные поля корректно')
      }
    })

    return isValid
  }

  /**
   * Парсинг предыдущего ответа респондента
   * Ответ может быть в разных форматах, в зависимости от типа поля
   * Например, для маски "Дата (день.месяц)" ответ приходит в формате "12.05"
   * Для маски "ФИО" ответ приходит в формате {name: "Иван", surname: "Иванов", patronymic: "Иванович"}
   * А для маски "День и месяц" ответ приходит в формате "12.05"
   * @param {string} answer - Предыдущий ответ
   * @returns {object} - Объект с предыдущим ответом
   */
  parsePreviousAnswer(answer, maskType) {
    if (answer === null || answer === undefined) {
      return null
    }

    const parseDayMonth = (answer) => {
      const [day, month] = answer.split('.')
      return { day, month }
    }

    const parsePeriod = (answer) => {
      if (!answer) {
        return { from: '', to: '' }
      }
      const [from, to] = answer.split(' - ')
      return { from, to }
    }

    const tryParseValue = (answer) => {
      // Если маска дата день.месяц
      if (maskType === MaskTypes.DateMonth) {
        const { day, month } = parseDayMonth(answer)
        return { day, month }
      }
      // Если маска ФИО
      if (maskType === MaskTypes.Name) {
        return {
          name: answer?.name || '',
          surname: answer?.surname || '',
          patronymic: answer?.patronymic || '',
        }
      }
      // Если маска период
      if (maskType === MaskTypes.Period) {
        const { from, to } = parsePeriod(answer)
        return { from, to }
      }
      return { value: answer }
    }

    try {
      const parsedAnswer = JSON.parse(answer)
      return tryParseValue(parsedAnswer)
    }
    catch {
      return tryParseValue(answer)
    }
  }

  checkValidity() {
    return this.maskedFields.value.every(field => field.isValid.value)
  }

  get hasValue() {
    if (this.hasPreviousAnswer) {
      return true
    }
    return this.maskedFields.value.some(field => field.hasValue())
  }

  getData() {
    return this.maskedFields.value.reduce((acc, field) => {
      acc[field.id] = field.getData()
      return acc
    }, {})
  }

  getAnswer() {
    return this.getData()
  }

  submit() {
    this.emit('next')
  }

  validate() {
    return this.validateFields()
  }

  setAllAsTouched() {
    this.touched.value = true
    this.maskedFields.value.forEach(field => field.setAllAsTouched())
  }

  resetFields() {
    this.touched.value = false
    this.err.value = null
    this.maskedFields.value.forEach(field => field.resetFieldValues?.())
  }

  updateFromPreview(previewData) {
    super.updateFromPreview?.(previewData)

    const updatedFields = previewData.values
    const existingFieldsMap = new Map(
      this.maskedFields.value.map(field => [field.id, field]),
    )

    // Create or update fields
    const updatedMaskedFields = updatedFields.map((fieldData, index) => {
      const existingField = existingFieldsMap.get(fieldData.persistentId || fieldData.id)

      if (existingField) {
        // Update existing field
        existingField.position = index
        existingField.updateFromPreview({
          type: Number(fieldData.maskType),
          fieldConfig: {
            label: fieldData.label,
            minLength: fieldData.textFieldParam.min,
            maxLength: fieldData.textFieldParam.max,
            placeholderText: fieldData.placeholderText,
            required: fieldData.isRequired,
            multiline: fieldData.isTextarea === 1,
          },
          config: fieldData.maskConfig,
        })
        return existingField
      }
      else {
        // Create new field
        fieldData.id = fieldData.persistentId
        const maskedField = this.createMaskedField(fieldData)
        maskedField.position = index
        return maskedField
      }
    })

    // Update the maskedFields array with the new order
    this.maskedFields.value = updatedMaskedFields.sort((a, b) => a.position - b.position)
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip && this.skipped.value)
        return ''

      const fields = this.maskedFields.value.map((field) => {
        const data = field.getData()
        const fieldConfig = toValue(field.fieldConfig)
        const label = toValue(fieldConfig.label)

        const isNameMask = toValue(field.type) === MaskTypes.Name
        if (isNameMask) {
          const { name, surname, patronymic } = data
          const items = []
          if (surname) {
            items.push(surname)
          }
          if (name) {
            items.push(name)
          }
          if (patronymic) {
            items.push(patronymic)
          }
          return `${label}: ${items.join(' ')}`
        }
        return `${label}: ${data}`
      })
      return fields.join('; ')
    })
  }
}
