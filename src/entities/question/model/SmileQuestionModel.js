import { withRootUrl } from '@/shared/helpers/general'
import {
  B<PERSON><PERSON><PERSON>OR_ALWAYS,
  BEHAVIOR_MISS,
  BEHAVIOR_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { VariantsController } from '../controllers/VariantsController'
import { BaseQuestion } from './BaseQuestion'

export class SmileQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const { t } = useTranslationsStore()
    this.t = t

    this._description = ref(data.description)
    this.description = computed(() => {
      return this.translations.value.description || this._description.value
    })

    this._predefinedValue = ref(this.previousAnswer?.answer)
    this._predefinedId = ref(Number.parseInt(this._predefinedValue.value))

    this._filledCommentAnswer = ref(this.previousAnswer?.self_variant || '')

    this._skip = ref(data.skip)
    this.skip = computed(() => this._skip.value)

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => this.translations.value?.skip_text || this._skipText.value || toValue(this.t('Не готов(а) оценить')))
    this._translatedLabels = ref(null)
    this.translatedLabels = computed(() => {
      const json = toValue(this.translations.value?.labels)
      if (!json) {
        return {}
      }
      return JSON.parse(json)
    })
    this.skipped = ref(data.skipped === 1)

    this.value = ref(this._predefinedValue.value ? String(this._predefinedId.value) : '')
    this._showLabels = ref(data.showLabels || data.show_labels)
    this.showLabels = computed(() => this._showLabels.value)
    this._smileType = ref(data.smileType || data.smile_type)
    this.smileType = computed(() => this._smileType.value)

    const smiles = data.smiles || []
    this._smiles = ref(smiles.map(s => ({
      ...s,
      id: String(s.id),
      url: withRootUrl(s.url || s.smile_url),
      label: computed(() => toValue(this.translatedLabels)?.[s.id] || s.label || s.text),
    })))
    this.smiles = computed(() => this._smiles.value)

    this.labels = computed(() => {
      return this.smiles.value.map(s => s.label)
    })

    this.err = ref(null)
    this.touched = ref(false)

    watch(this.value, (newValue) => {
      if (newValue) {
        this.skipped.value = false
        this.markInteracted()
      }
      this.touched.value = true
      this.validateRating()
    })

    this._commentEnabled = ref(data.comment_enabled === 1)
    this.commentEnabled = computed(() => this._commentEnabled.value)

    this._textFieldParam = ref(data.textFieldParam || {})
    this.textFieldParam = computed(() => this._textFieldParam.value)

    this._placeholderText = ref(data.placeholderText)
    this.placeholderText = computed(() => this._placeholderText.value)

    this._isCommentRequired = ref(data.comment_required)
    this.isCommentRequired = computed(() => this._isCommentRequired.value)

    this.commentController = new CommentController({
      enabled: this.commentEnabled.value,
      required: this.isCommentRequired.value,
      skipped: this.skipped,
      value: this._filledCommentAnswer.value,
      placeholderText: this.placeholderText.value,
      minLength: this.textFieldParam.value.min || 0,
      maxLength: this.textFieldParam.value.max || 1000,
      title: data.comment_label,
      translations: this.translations,
    })

    this._commentLabel = ref(data.comment_label)
    this.commentLabel = computed(() => this._commentLabel.value || this.t('Ваш комментарий'))

    this.isValid = computed(() => {
      return this.validate()
    })

    watch(this.skipped, (val) => {
      if (val) {
        this.resetFields()
        this.markInteracted()
        this.emit('get-answer')
      }
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })

    this.selectedVariants = computed(() => {
      const smileId = this.value.value
      const index = this.smiles.findIndex(s => s.id === smileId)
      return index === -1 ? [] : [index + 1]
    })

    // Add after other class properties
    this.canMoveToNextQuestion = ref(false)

    // Add after other watchers
    watch([this.value, this.skipped], ([value, skipped]) => {
      if (skipped) {
        // If question is skipped, we can move to next question
        this.canMoveToNextQuestion.value = true
        return
      }

      const variantsEnabled = this.variantsController?.enabled?.value
      if (this.commentEnabled.value || variantsEnabled) {
        // If comment is enabled or variants are shown, don't auto-move
        this.canMoveToNextQuestion.value = false
        return
      }

      // Can move if smile is selected
      this.canMoveToNextQuestion.value = !!value
    })

    this.canHideActions = computed(() => {
      return !this.commentEnabled.value
    })

    this._enableGallery = ref(data.enableGallery || false)
    this.enableGallery = computed(() => this._enableGallery.value)
    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: this.enableGallery.value,
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    const rawDetailAnswers = (data.detail_answers || [])
      .filter(v => v?.extra_question === undefined || v?.extra_question !== 1)
    this.detailAnswers = shallowRef(rawDetailAnswers)

    this.forAllRates = ref(!!data.forAllRates)
    this._extraQuestionRateFrom = ref(data.extra_question_rate_from || 0)
    this.extraQuestionRateFrom = computed(() => this._extraQuestionRateFrom.value)

    this._extraQuestionRateTo = ref(data.extra_question_rate_to || 10)
    this.extraQuestionRateTo = computed(() => this._extraQuestionRateTo.value)

    this.extraQuestionVariants = ref(data.detail_answers?.filter(v => v.extra_question === 1))

    // Initialize questionScreenshot for file upload and screenshot functionality
    this.questionScreenshot = data.questionScreenshot || null

    this.isHaveExtra = ref(data.isHaveExtra)
    this.variantsController = this.initializeSingleVariantsController(this.data, this.extraQuestionVariants.value)
  }

  /**
   * Resolves view logic condition for smile rating question
   * @param {object} rule - The view logic rule to check
   * @returns {boolean} Whether the condition is met
   */
  resolveViewLogic(rule) {
    const smileId = this.value.value
    if (!smileId)
      return false

    const index = this.smiles.value.findIndex(s => s.id === smileId)
    if (index === -1)
      return false

    // Check if smile index (1-based) is in required variants
    return rule.variants?.includes(index + 1) ?? false
  }

  get hasGallery() {
    return toValue(this.enableGallery)
  }

  resetFields() {
    this.touched.value = false
    this.err.value = null
    this.value.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null

    if (this.variantsController) {
      this.variantsController.resetFields()
    }
  }

  setAllAsTouched() {
    this.touched.value = true

    if (this.commentEnabled.value) {
      this.commentController.touched.value = true
    }

    if (this.variantsController) {
      this.variantsController.touched.value = true
      this.variantsController.selfVariantCommentTouched.value = true
    }
  }

  checkValidity() {
    if (this.skip && this.skipped.value)
      return true
    if (this.commentEnabled.value && this.isCommentRequired.value && !this.commentController.isValid.value)
      return false
    return toValue(this.isRequired) ? !!this.value.value : true
  }

  validateRating() {
    this.err.value = null
    if (!this.touched.value)
      return true

    if (!toValue(this.isRequired)) {
      this.err.value = null
      return true
    }

    if (this.skip && this.skipped.value) {
      this.err.value = null
      return true
    }

    if (!this.value.value) {
      this.err.value = this.t('Нужно поставить оценку')
      return false
    }

    this.err.value = null
    return true
  }

  validate() {
    if (this.skip && this.skipped.value)
      return true

    this.validateRating()

    let commentValid = true
    if (this.commentEnabled.value) {
      this.commentController.validate()
      commentValid = toValue(this.commentController.isValid)
    }

    let variantsControllerValid = true
    if (this.variantsController) {
      variantsControllerValid = this.variantsController.validate()
    }

    if (toValue(this.isRequired) && !this.value.value)
      return false

    return this.checkValidity() && commentValid && variantsControllerValid
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const smileId = this.value.value
    const index = this.smiles.value.findIndex(s => s.id === smileId)
    const params = {
      answer: smileId,
      rating: index + 1,
    }

    if (this.commentEnabled.value) {
      params.comment = toValue(this.commentController.value)
    }
    const shouldAddVariantsControllerData = toValue(this.variantsController?.enabled)

    if (shouldAddVariantsControllerData) {
      const variantControllerData = this.variantsController.getData()
      const variantsType = this.variantsController.variantsType.value

      if (variantControllerData.clarifyingQuestionScreenshots) {
        params.clarifyingQuestionScreenshots = variantControllerData.clarifyingQuestionScreenshots
      }
      if (variantControllerData.clarifyingQuestionAttachmentIds) {
        params.clarifyingQuestionAttachmentIds = variantControllerData.clarifyingQuestionAttachmentIds
      }
      if (variantControllerData.clarifyingQuestionScreenshotIds) {
        params.clarifyingQuestionScreenshotIds = variantControllerData.clarifyingQuestionScreenshotIds
      }

      switch (variantsType) {
        case 0:
          if (variantControllerData.self_variant) {
            params.detail_item = { self_variant: variantControllerData.self_variant }
          }
          else if (variantControllerData.detail_item?.length) {
            params.detail_item = variantControllerData.detail_item
          }
          break
        case 1:
          params.detail_item = []
          if (variantControllerData.detail_item?.length) {
            params.detail_item = [...variantControllerData.detail_item.filter(v => v !== 'is_self_answer')]
          }
          if (variantControllerData.self_variant) {
            params.detail_item.push({ self_variant: variantControllerData.self_variant })
          }
          break
        case 2:
          if (variantControllerData.textAnswer) {
            params.detail_item = { text_answer: variantControllerData.textAnswer }
          }
          break

        default:
          break
      }
    }
    return params
  }

  getAnswer() {
    const smileId = this.value.value
    const smile = this.smiles.value.find(s => s.id === smileId)

    if (!smile)
      return ''

    return smile.text || `Смайл ${this.selectedVariants.value[0]}`
  }

  get hasValue() {
    if (this.hasPreviousAnswer) {
      return true
    }
    if (this.skip.value && this.skipped.value)
      return true
    if (!this.isValid.value)
      return false

    const hasVariantsControllerValue = this.variantsController?.hasValue

    return !!this.value.value || hasVariantsControllerValue
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Get current value from reactive value variable
    const val = Number(this.value.value) || 0
    const rating = this.smiles.value.findIndex(s => Number.parseInt(s.id) === val) + 1

    switch (condition.behavior) {
      case BEHAVIOR_MISS:
        // Check if question was skipped or has no rating
        return this.skipped.value || rating <= 0

      case BEHAVIOR_SELECT:
        // Check if current rating is in selected variants
        return condition.variants.includes(rating)

      case BEHAVIOR_UNSELECT:
        // Check if current rating is NOT in selected variants
        return !condition.variants.includes(rating)

      case BEHAVIOR_ALWAYS:
        return true

      default:
        return false
    }
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      const smileId = this.value.value
      const smile = this.smiles.value.find(s => s.id === smileId)
      if (!smile)
        return ''

      const index = this.smiles.value.indexOf(smile)
      switch (this.smiles.value.length) {
        case 2: return index === 0 ? '0' : '1'
        case 3: return `${index + 1}`
        case 5: return `${index + 1}`
        default: return `${index + 1}`
      }
    })
  }

  initializeSingleVariantsController(data, variants) {
    const previousSelectedItems = this.previousAnswer?.detail_item || []
    const previousSelfVariantValue = this.previousAnswer?.self_variant
    const previousTextAnswerValue = this.previousAnswer?.textAnswer

    const previousSelectedItemsEntries = Object.entries(previousSelectedItems).filter(([key]) => key !== 'self_variant')

    return new VariantsController({
      enabled: computed(() => {
        if (!this.isHaveExtra.value) {
          return false
        }

        const smileId = this.value.value
        if (!smileId)
          return false

        const rating = this.smiles.value.findIndex(s => s.id === smileId) + 1
        const enabled = toValue(this.forAllRates) || (rating >= toValue(this.extraQuestionRateFrom) && rating <= toValue(this.extraQuestionRateTo))

        return enabled
      }),
      isRequired: data.extra_required === 1,
      variants,
      variantsWithFiles: data.variants_with_files,
      previousAnswerHasSelfVariant: !!previousSelfVariantValue,
      previousAnswerItems: previousSelectedItemsEntries.map(([_, value]) => value),
      textFieldValue: previousTextAnswerValue || previousSelfVariantValue || '',
      variantsType: data.variantsType || 0,
      textFieldParam: data.textFieldParam || {},
      placeholderText: data.placeholderText,
      selfVariantPlaceholderText: data.placeholderText,
      translations: this.translations,
      skipped: this.skipped,
      label: data.answerText,
      hasCustomField: data.isHaveCustomField,
      dropdown: data.dropdownVariants,
      selfVariantText: data.self_variant_text,
      selfVariantNothing: data.self_variant_nothing === 1,
      selfVariantCommentRequired: data.self_variant_comment_required === 1,
      selfVariantMinLength: data.textFieldParam?.min || 0,
      selfVariantMaxLength: data.textFieldParam?.max || 1000,
      selfVariantFile: data.self_variant_file,
      minChooseVariants: data.min_choose_extra_variants,
      maxChooseVariants: data.max_choose_extra_variants,
      questionScreenshot: this.questionScreenshot,
      questionId: this.questionId,
      isPreviewMode: this.isPreviewMode,
      previousAnswer: this.previousAnswer,
    })
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update extra question type and related settings
    if (previewData.extra_question_type) {
      this._extraQuestionType.value = previewData.extra_question_type
    }

    if (previewData.clarifyingQuestion?.forRates) {
      this._extraQuestionRateFrom.value = previewData.clarifyingQuestion.forRates[0]
      this._extraQuestionRateTo.value = previewData.clarifyingQuestion.forRates[1]
    }

    this.forAllRates.value = previewData.clarifyingQuestion.forAllRates

    // Split variants into main variants and extra question variants
    const clarifyingQuestion = previewData.clarifyingQuestion || {}

    this.isHaveExtra.value = !!clarifyingQuestion.enabled
    if (this.variantsController) {
      this.variantsController.updateFromPreview({
        isRequired: clarifyingQuestion.required,
        variants: clarifyingQuestion.variants,
        variantsType: clarifyingQuestion.variantsType,
        textFieldParam: {
          min: clarifyingQuestion.customAnswerRange?.[0] || 0,
          max: clarifyingQuestion.customAnswerRange?.[1] || 250,
        },
        placeholderText: clarifyingQuestion.customAnswerPlaceholder || previewData.placeholderText || '',
        label: clarifyingQuestion.text,
        hasCustomField: clarifyingQuestion.customAnswerEnabled,
        dropdown: clarifyingQuestion.dropdownVariants,
        variantsWithFiles: clarifyingQuestion.enableFile,
        selfVariantText: clarifyingQuestion.customAnswerLabel,
        selfVariantPlaceholderText: clarifyingQuestion.customAnswerPlaceholder,
        selfVariantMinLength: clarifyingQuestion.customAnswerRange?.[0] || 0,
        selfVariantMaxLength: clarifyingQuestion.customAnswerRange?.[1] || 250,
        selfVariantFile: clarifyingQuestion.customAnswerFile,
        minChooseVariants: Number.parseInt(clarifyingQuestion.minСhooseVariants),
        maxChooseVariants: Number.parseInt(clarifyingQuestion.maxСhooseVariants),
        questionScreenshot: previewData.questionScreenshot,
      })
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this._skip.value = previewData.skip
    }
    if (!this.skip.value) {
      this.skipped.value = false
    }
    if (previewData.skip_text !== undefined) {
      this._skipText.value = previewData.skip_text
    }

    // Update smile-specific fields
    if (previewData.smile_type !== undefined) {
      this._smileType.value = previewData.smile_type
    }

    if (previewData.show_labels !== undefined) {
      this._showLabels.value = previewData.show_labels
    }

    if (previewData.smiles) {
      this._smiles.value = previewData.smiles.map((s) => {
        const rawUrl = s.url || s.smile_url
        const isDataImageUrl = rawUrl?.startsWith('data:image')
        const url = isDataImageUrl ? rawUrl : withRootUrl(rawUrl)
        const smile = {
          ...s,
          id: String(s.id),
          url,
          label: computed(() => toValue(this.translatedLabels)?.[s.id] || s.label || s.text),
        }
        return smile
      })
    }

    // Update comment if enabled
    if (previewData.commentEnabled !== undefined) {
      this._commentEnabled.value = previewData.commentEnabled === 1
    }
    if (this.commentEnabled.value && this.commentController) {
      this.commentController.updateFromPreview({
        enabled: toValue(this.commentEnabled),
        required: previewData.comment_required || false,
        title: previewData.comment_label || '',
        placeholderText: previewData.placeholderText || '',
        textFieldParam: {
          min: previewData.textFieldParam?.min || 0,
          max: previewData.textFieldParam?.max || 250,
        },
      })
    }

    // Update gallery if enabled
    if (previewData.enableGallery !== undefined) {
      this._enableGallery.value = previewData.enableGallery
    }

    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: previewData.gallery || [],
      })
    }
  }
}
