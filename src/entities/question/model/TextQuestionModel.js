import { BEHAVIOR_ALWAYS } from '@shared/constants/logic'
import MaskTypes from '@shared/constants/maskTypes'
import { getMonths } from '@shared/helpers/date'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, nextTick, ref, toValue, watch } from 'vue'
import { BaseQuestion } from './BaseQuestion'
import { MaskedFieldModel } from './MaskedFieldModel'

export class TextQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.t = store.t

    this.isMultiline = ref(data.variantsType === 1)
    this.months = computed(() => getMonths(this.t))
    this.isRequired = ref(!!data.isRequired)

    this.maskType = ref(Number.parseInt(data.maskType))
    this.parsedPreviousAnswer = this.parsePreviousAnswer(this.previousAnswer?.answer)

    this.maskedField = new MaskedFieldModel({
      translations: this.translations,
      months: this.months,
      type: toValue(this.maskType),
      previousAnswer: this.parsedPreviousAnswer,
      fieldConfig: {
        minLength: data.textFieldParam?.min,
        maxLength: data.textFieldParam?.max,
        placeholderText: data.placeholderText,
        value: data.textFieldValue || data.value || data.comment,
        dateValue: data.value,
        required: toValue(this.isRequired),
        multiline: toValue(this.isMultiline),
      },
      config: data.maskConfig,
    })

    this.skip = ref(data.skip)

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || this.t('Затрудняюсь ответить')
    })
    this.skipped = ref(data.skipped === 1)

    this.touched = ref(false)
    this.err = ref(null)

    this.isValid = computed(() => {
      return this.validate()
    })
    this.blocked = computed(() => {
      // this.maskedField.blocked.value
      return toValue(this.touched) && !toValue(this.isValid)
    })

    watch(() => this.maskedField.hasInteracted.value, (hasInteracted) => {
      if (hasInteracted) {
        this.markInteracted()
      }
    })

    watch(() => this.skipped.value, (newSkipped) => {
      if (newSkipped) {
        this.maskedField.resetFieldValues()
      }
    })

    this.canMoveToNextQuestion = ref(false)

    watch(() => this.skipped.value, (newValue) => {
      if (newValue) {
        this.resetFields()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })
  }

  /**
   * Парсинг предыдущего ответа респондента
   * Ответ может быть в разных форматах, в зависимости от типа поля
   * Например, для маски "Дата (день.месяц)" ответ приходит в формате "12.05"
   * Для маски "ФИО" ответ приходит в формате {name: "Иван", surname: "Иванов", patronymic: "Иванович"}
   * А для маски "День и месяц" ответ приходит в формате "12.05"
   * @param {string} answer - Предыдущий ответ
   * @returns {object} - Объект с предыдущим ответом
   */
  parsePreviousAnswer(answer) {
    if (answer === null || answer === undefined) {
      return null
    }

    const parseDayMonth = (answer) => {
      const [day, month] = answer.split('.')
      return { day, month }
    }

    const parsePeriod = (answer) => {
      if (!answer) {
        return { from: '', to: '' }
      }
      const [from, to] = answer.split(' - ')
      return { from, to }
    }

    const tryParseValue = (answer) => {
      // Если маска дата день.месяц
      if (this.maskType.value === MaskTypes.DateMonth) {
        const { day, month } = parseDayMonth(answer)
        return { day, month }
      }
      // Если маска ФИО
      if (this.maskType.value === MaskTypes.Name) {
        return {
          name: answer?.name || '',
          surname: answer?.surname || '',
          patronymic: answer?.patronymic || '',
        }
      }
      // Если маска период
      if (this.maskType.value === MaskTypes.Period) {
        const { from, to } = parsePeriod(answer)
        return { from, to }
      }
      return { value: answer }
    }

    try {
      const parsedAnswer = JSON.parse(answer)
      return tryParseValue(parsedAnswer)
    }
    catch {
      return tryParseValue(answer)
    }
  }

  validateField() {
    this.err.value = null
    if (!this.touched.value)
      return true

    if (!toValue(this.isRequired)) {
      this.err.value = null
      return true
    }

    if (!this.maskedField.isValid.value) {
      this.err.value = this.maskedField.error.value
      return false
    }

    this.err.value = null
    return true
  }

  checkValidity() {
    if (this.skip.value && this.skipped.value)
      return true

    return this.maskedField.isValid.value
  }

  get hasValue() {
    if (toValue(this.skip) && toValue(this.skipped))
      return true
    if (this.hasPreviousAnswer) {
      return true
    }
    return this.maskedField.hasValue()
  }

  getData() {
    if (toValue(this.skip) && toValue(this.skipped)) {
      return { skipped: 1 }
    }

    const value = this.maskedField.getData()
    return { answer: value }
  }

  getAnswer() {
    if (toValue(this.skip) && toValue(this.skipped))
      return this.t('Пропущено')

    const value = this.maskedField.getData()
    return value.trim()
  }

  submit() {
    this.emit('next')
  }

  validate() {
    this.err.value = null
    if (this.skip.value && this.skipped.value)
      return true

    this.validateField()
    return this.checkValidity()
  }

  setAllAsTouched() {
    this.touched.value = true
    this.maskedField.setAllAsTouched()
  }

  resetFields() {
    this.touched.value = false
    this.err.value = null
    this.maskedField.resetFieldValues()
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (toValue(this.skip) && toValue(this.skipped))
        return ''

      const value = this.maskedField.getData()
      const isNameMask = this.maskType.value === MaskTypes.Name
      if (isNameMask) {
        const { name, surname, patronymic } = value
        const items = [
        ]
        if (surname) {
          items.push(surname)
        }
        if (name) {
          items.push(name)
        }
        if (patronymic) {
          items.push(patronymic)
        }
        return items.join(' ')
      }
      if (typeof value === 'string') {
        return value.trim()
      }
      else if (typeof value === 'number') {
        return value.toString()
      }

      return value?.answer?.trim?.() || ''
    })
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Text questions only support BEHAVIOR_ALWAYS
    return condition.behavior === BEHAVIOR_ALWAYS
  }

  updateFromPreview(previewData) {
    super.updateFromPreview?.(previewData)

    const maskTypeChanged = this.maskType.value !== Number.parseInt(previewData.maskType)

    this.isRequired.value = previewData.isRequired
    this.isMultiline.value = previewData.variantsType === 1
    this.maskType.value = Number.parseInt(previewData.maskType)

    let minLength = previewData.textFieldParam?.min
    let maxLength = previewData.textFieldParam?.max

    const maskWithLimits = [MaskTypes.NoMask, MaskTypes.Email, MaskTypes.Site, MaskTypes.Number]

    // Если изменился тип маски, то сбрасываем все поля
    if (maskTypeChanged) {
      this.resetFields()
    }

    // Если изменился тип маски и это маска, у которой нет ограничений на длину символов,
    // то сбрасываем ограничения на длину символов до стандартных
    if (maskTypeChanged && !maskWithLimits.includes(this.maskType.value)) {
      minLength = 0
      maxLength = 250
    }

    // Update maskedField with new configuration
    this.maskedField.updateFromPreview({
      type: this.maskType.value,
      fieldConfig: {
        minLength,
        maxLength,
        placeholderText: previewData.placeholderText,
        required: this.isRequired.value,
        multiline: this.isMultiline.value,
      },
      config: previewData.maskConfig,
    })

    if (previewData.textFieldParam.skip !== undefined)
      this.skip.value = previewData.textFieldParam.skip

    if (previewData.textFieldParam.skip_text !== undefined)
      this._skipText.value = previewData.textFieldParam.skip_text
  }
}
