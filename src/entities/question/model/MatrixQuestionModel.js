import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, nextTick, ref, shallowRef, toValue, triggerRef, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { VariantsController } from '../controllers/VariantsController'
import { BaseQuestion } from './BaseQuestion'

export class MatrixQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    /**
     * @type {[key: string]: string}
     * @description Распаршенный предыдущий ответ на вопрос матрицы
     * Возможные структуры:
     * 1. Для обычной матрицы:
     *    {
     *      "название строки": ["col1", "col2"] // массив выбранных колонок
     *      // ИЛИ
     *      "название строки": "null" // если строка была пропущена
     *    }
     * 2. Для матрицы с донором:
     *    {
     *      "id варианта донора": ["col1", "col2"] // массив выбранных колонок
     *      // ИЛИ
     *      "id варианта донора": "null" // если строка была пропущена
     *    }
     */
    this.parsedPreviousAnswer = {}

    try {
      this.parsedPreviousAnswer = JSON.parse(this.previousAnswer?.answer || '{}')
    }
    catch {
      this.parsedPreviousAnswer = {}
    }

    /**
     * @type {[key: string]: object}
     * @description Предыдущие ответы на уточняющие вопросы для каждой строки матрицы
     * Возможные структуры:
     * 1. Объект с ответами:
     *    {
     *      "название строки или id донора": {
     *        "answer": "текстовый ответ",
     *        "self_variant": "свой вариант ответа"
     *      }
     *    }
     * 2. Массив выбранных вариантов:
     *    {
     *      "название строки или id донора": ["id1", "id2"] // массив id выбранных вариантов
     *    }
     */
    this.previouslySetVariants = this.previousAnswer?.detail_item || {}

    this.matrixSettings = ref(data.matrixSettings || {})

    /**
     * @type {object[]} Массив, в котором хранятся варианты ответов
     * Тут хранятся варианты ответов для Уточняющего вопроса
     * А также варианты ответов донора
     */
    this.detailAnswers = ref(data.detail_answers || [])

    /**
     * Реактивный объект, который хранит id варианта донора по индексу
     * Зачем он нужен? А просто в некоторых случаях с сервера приходит не название строки, а id варианта
     * (не знаю почему бы изначально не сохранять id варианта, вместо строк)
     * @type {import('vue').Ref<Record<number, string>>}
     */
    this.donorRowsIdsByIndex = ref({})

    this.cols = ref(this.matrixSettings.value.cols || [])
    this.minRowsReq = ref(Number.parseInt(this.matrixSettings.value.minRowsReq) || 0)

    this.multipleChoice = ref(this.matrixSettings.value.multiple_choice === '1')
    this.isStandartType = ref(this.matrixSettings.value.type === 'standart')
    this.randomVariantsOrder = ref(data.random_variants_order === 1)
    this.labels = computed(() => {
      const labelsFromTranslation = JSON.parse(this.translations.value.labels || '{}')
      return labelsFromTranslation
    })

    this.skip = ref(!!data.skip)

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || toValue(this.t('Затрудняюсь ответить'))
    })
    this.skipped = ref(this.previousAnswer?.skipped === 1)
    this.skipVariant = ref(data.skip_variant === 1)

    this.extraQuestion = ref(this.matrixSettings.value.extra_question || {})

    this.isExtraRequired = ref(data.extra_required === 1)
    this.variantsType = ref(data.variantsType)
    this.textFieldParam = ref(data.textFieldParam || {})
    this.placeholderText = ref(data.placeholderText)
    this.extraQuestionLabel = ref(data.answerText)
    this.dropdownVariants = ref(!!data.dropdownVariants)
    this.selfVariantText = ref(data.self_variant_text)
    this.selfVariantNothing = ref(data.self_variant_nothing === 1)
    this.selfVariantCommentRequired = ref(data.self_variant_comment_required === 1)
    this.selfVariantMinLength = ref(data.textFieldParam?.min || 0)
    this.selfVariantMaxLength = ref(data.textFieldParam?.max || 1000)
    this.isCustomFieldChecked = ref(data.isCustomFieldChecked)
    this.isHaveCustomField = ref(data.isHaveCustomField)
    this.textFieldValue = ref('')

    this.clarifyingQuestionEnabled = ref(true)
    this.clarifyingQuestionText = ref(data.answerText)
    this.clarifyingQuestionIsRequired = ref(data.extra_required)

    this._selectPlaceholderText = ref(data.selectPlaceholderText)
    this.selectPlaceholderText = computed(() => {
      const translated = toValue(this.translations)?.select_placeholder_text
      return translated || this._selectPlaceholderText.value
    })

    this.extraQuestionVariants = computed(() => this.detailAnswers.value.filter(v => v.is_deleted === 0 && v.extra_question === 1))

    this.transformedVariants = computed(() => {
      return this.detailAnswers.value
        .filter(v => v.is_deleted === 0 && v.extra_question === 1)
        .map((v) => {
          const updatedVariant = {
            ...v,
            stars: ref(0),
            error: ref(null),
            touched: ref(false),
            skipped: ref(false),
            text: computed(() => {
              const langId = toValue(this.selectedLang)?.id
              if (langId) {
                const translation = v.detailLangs?.find?.(l => l.foquz_poll_lang_id === langId)
                return translation?.question || v.variant || v.question
              }

              return v.variant || v.question
            }),
          }

          return updatedVariant
        })
    })

    this.translatedCols = computed(() => this.cols.value.map((col, colIndex) => {
      return toValue(this.labels)?.cols?.[colIndex] || col
    }))

    this.rows = shallowRef(this.hasDonor ? [] : this.prepareRows())

    watch(this.selectedDonorVariants, (newDonorVariants) => {
      if (!this.hasDonor)
        return

      const filteredDonorAnswers = []

      const donorSelfAnswer = (data.assessmentVariants || []).find(v => v.id === -1)

      newDonorVariants.forEach((v) => {
        if (v.id === 'is_self_answer' && donorSelfAnswer) {
          // Handle self answer variant
          donorSelfAnswer.donorVariantId = '-1'
          donorSelfAnswer.question = computed(() => toValue(v.alternativeVariantLabel))
          donorSelfAnswer.rawTitle = toValue(v.alternativeRawLabel)
          filteredDonorAnswers.push(donorSelfAnswer)
          return
        }

        const donorVariant = this.detailAnswers.value
          .find(answer => answer.dictionary_element_id === v.id || answer.question_detail_id === v.id || answer.id === v.id)

        if (donorVariant) {
          donorVariant.donorVariantId = v.id
          filteredDonorAnswers.push(donorVariant)
        }
      })

      const newRows = this.prepareRows(filteredDonorAnswers)
      const existingRows = this.rows.value || []
      const preservedRows = []

      newRows.forEach((newRow) => {
        const existingRow = existingRows.find(r =>
          (newRow.donorVariantId && r.donorVariantId === newRow.donorVariantId)
          || (!newRow.donorVariantId && toValue(r.rawTitle) === toValue(newRow.rawTitle)),
        )

        if (existingRow) {
          preservedRows.push(existingRow)
        }
        else {
          preservedRows.push(newRow)
        }
      })

      this.rows.value = this.sortRows(preservedRows)
    }, { immediate: true })

    watch(this.rows, (rows) => {
      rows.forEach((row) => {
        watch(row.selectedCols, (cols) => {
          if (cols.length > 0) {
            this.markInteracted()
          }
          this.validate()

          // Auto-advance when:
          // 1. No comment field
          // 2. No clarifying question
          // 3. Only one row with one selected column
          const singleRowWithSingleSelection = rows.length === 1 && cols.length === 1

          if (
            !this.commentEnabled.value
            && !this.clarifyingQuestionText.value
            && singleRowWithSingleSelection
          ) {
            this.canMoveToNextQuestion.value = true
          }
        }, { deep: true })

        watch(row.skipped, (newValue) => {
          if (newValue) {
            this.markInteracted()
          }
        })
      })
    }, { immediate: true })

    this.touched = ref(false)
    this.error = ref(null)
    this.commentEnabled = ref(data.comment_enabled)

    this.commentController = new CommentController({
      enabled: this.commentEnabled.value,
      required: data.comment_required === 1,
      skipped: this.skipped,
      value: this.previousAnswer?.self_variant || '',
      placeholderText: data.placeholderText,
      minLength: this.textFieldParam.value.min || 0,
      maxLength: this.textFieldParam.value.max || 1000,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.necessaryRowsSelected = computed(() => {
      const isSkipped = this.skipped.value
      if (isSkipped)
        return true

      const rowsWithSelectedCols = toValue(this.rows).filter(row => row.selectedCols.value.length > 0 || row.skipped.value)

      let minRowsReq = toValue(this.minRowsReq)

      if (this.hasDonor && minRowsReq >= toValue(this.rows)?.length) {
        minRowsReq = toValue(this.rows).length
      }

      return rowsWithSelectedCols.length >= minRowsReq
    })

    this.isValid = computed(() => {
      if (this.skip.value && this.skipped.value)
        return true
      return this.validate()
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    toValue(this.rows).forEach((row) => {
      watch(row.selectedCols, (cols) => {
        if (cols.length > 0) {
          this.markInteracted()
        }
        this.validate()

        // Auto-advance when:
        // 1. No comment field
        // 2. No clarifying question
        // 3. Only one row with one selected column
        const singleRowWithSingleSelection = this.rows.length === 1 && cols.length === 1

        if (
          !this.commentEnabled.value
          && !this.clarifyingQuestionText.value
          && singleRowWithSingleSelection
        ) {
          this.canMoveToNextQuestion.value = true
        }
      }, { deep: true })

      watch(row.skipped, (newValue) => {
        if (newValue) {
          this.markInteracted()
        }
      })
    })

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: true,
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })
  }

  get hasGallery() {
    return toValue(this.enableGallery)
  }

  /**
   * Создает контроллер вариантов для уточняющего вопроса строки матрицы
   * @param {object} row - Объект строки матрицы
   * @returns {VariantsController|null} Контроллер вариантов или null если уточняющий вопрос не нужен
   * @description Создает контроллер для управления уточняющими вопросами в строке матрицы.
   * Обрабатывает различные форматы предыдущих ответов:
   * 1. Текстовый ответ в поле self_variant
   * 2. Массив id выбранных вариантов
   * 3. Объект с ответами для донора
   * Контроллер создается только если для данной строки и выбранных колонок
   * настроен показ уточняющего вопроса
   */
  createVariantsController(row) {
    const rawTitle = toValue(row.rawTitle)
    const previouslySetVariants = this.previouslySetVariants[rawTitle]

    let parsedVariantsAnswer = {}

    try {
      parsedVariantsAnswer = JSON.parse(previouslySetVariants)
    }
    catch {
      parsedVariantsAnswer = previouslySetVariants
    }

    const parsedVariantsTextValue = parsedVariantsAnswer?.answer || parsedVariantsAnswer?.self_variant || ''
    let parsedVariantEntries = Object.entries(parsedVariantsAnswer || {})

    // remove self_variant entry
    parsedVariantEntries = parsedVariantEntries.filter(([key]) => key !== 'self_variant')

    const parsedVariantsValues = parsedVariantEntries.map(([_, value]) => value)

    const previousAnswerItems = []

    if (Array.isArray(parsedVariantsAnswer)) {
      previousAnswerItems.push(...parsedVariantsValues)
    }
    else if (Number.isInteger(parsedVariantsAnswer)) {
      previousAnswerItems.push(parsedVariantsAnswer)
    }
    else {
      previousAnswerItems.push(...parsedVariantsValues)
    }

    return new VariantsController({
      enabled: computed(() => {
        const variantsEnabled = !this.commentEnabled.value
          && this.clarifyingQuestionEnabled.value
          && this.clarifyingQuestionText.value

        if (!variantsEnabled) {
          return false
        }

        const extraQuestionRows = this.extraQuestion.value?.rows || []
        const extraQuestionCols = this.extraQuestion.value?.cols || []
        const selectedCols = row.selectedCols.value

        const rowTitle = toValue(row.rawTitle)

        // Проверяем, нужно ли отображать Уточняющий вопрос для данной строки
        const isRowEnabled = extraQuestionRows.includes(rowTitle)

        // Проверяем, нужно ли отображать Уточняющий вопрос для выбранных колонок
        const isEnabledForColumns = selectedCols.some(col => extraQuestionCols.includes(this.cols.value[col]))

        return isRowEnabled && isEnabledForColumns
      }),
      previousAnswerItems,
      previousAnswerHasSelfVariant: !!parsedVariantsTextValue,
      textFieldValue: parsedVariantsTextValue,
      skipped: computed(() => this.skipped.value || row.skipped.value),
      label: this.extraQuestionLabel.value,
      isRequired: this.isExtraRequired.value,
      variants: this.transformedVariants.value,
      variantsType: this.variantsType.value,
      hasCustomField: this.isHaveCustomField.value,
      selfVariantText: this.selfVariantText.value,
      selfVariantNothing: this.selfVariantNothing.value,
      selfVariantCommentRequired: true,
      selfVariantMinLength: this.selfVariantMinLength.value,
      selfVariantMaxLength: this.selfVariantMaxLength.value,
      placeholderText: this.placeholderText.value,
      selfVariantPlaceholderText: this.placeholderText.value,
      translations: this.translations,
    })
  }

  resetFields() {
    this.touched.value = false
    this.error.value = null
    toValue(this.rows).forEach((row) => {
      row.selectedCols.value = []
      row.error.value = null
      row.touched.value = false
      row.skipped.value = false
      if (row.variantsController) {
        row.variantsController.resetFields()
      }
    })
    if (this.commentEnabled.value) {
      this.commentController.value.value = ''
      this.commentController.touched.value = false
      this.commentController.error.value = null
    }
  }

  setAllAsTouched() {
    this.touched.value = true
    toValue(this.rows).forEach((row) => {
      row.touched.value = true
      if (row.variantsController) {
        row.variantsController.touched.value = true
        row.variantsController.selfVariantCommentTouched.value = true
      }
    })
    if (this.commentEnabled.value) {
      this.commentController.touched.value = true
    }
  }

  validateRow(row) {
    row.error.value = null
    let valid = true
    let variantsValid = true
    if (!row.touched.value)
      return true
    if ((this.skip.value && this.skipped.value) || row.skipped.value)
      return true
    const isRequired = toValue(this.isRequired)
    if (isRequired && !this.necessaryRowsSelected.value && row.selectedCols.value.length === 0) {
      row.error.value = this.t('Нужно выбрать вариант')
      valid = false
    }

    const variantsController = row.variantsController
    if (variantsController) {
      variantsValid = variantsController.validate()
    }

    if (valid) {
      row.error.value = null
    }

    return valid && variantsValid
  }

  validate() {
    this.error.value = null
    let valid = true
    let commentValid = true

    if (this.skip.value && this.skipped.value)
      return true
    if (!this.touched.value)
      return true

    const isRequired = toValue(this.isRequired)

    const rowsLength = toValue(this.rows).length
    toValue(this.rows).forEach(row => this.validateRow(row))
    const eachRowValid = toValue(this.rows).every(row => this.validateRow(row))

    if (isRequired && !this.necessaryRowsSelected.value) {
      const minRowsReq = toValue(this.minRowsReq)

      const defaultErrorMessage = rowsLength === 1
        ? this.t(`Нужно поставить оценку`)
        : this.t(`Нужно поставить все оценки`)
      const errorMessage = rowsLength <= minRowsReq
        ? defaultErrorMessage
        : this.t(`Необходимо ответить хотя бы на {number}`, { number: minRowsReq })
      this.error.value = errorMessage
      valid = false
    }

    if (this.commentEnabled.value) {
      commentValid = this.commentController.validate()
    }

    return valid && eachRowValid && commentValid
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const answer = {}
    const detail_item = {}

    toValue(this.rows).forEach((row, _index) => {
      // @NOTE: в matrixRows title может быть на английском, поэтому достаем из rows
      const rowTitle = toValue(row.rawTitle)
      let rowId = rowTitle

      if (row.donorVariantId) {
        rowId = row.donorVariantId
      }

      const isRowSkipped = row.skipped.value
      let selectedCols = row.selectedCols.value.map(colIndex => this.cols.value[colIndex])
      if (selectedCols.length === 0) {
        selectedCols = ['-1']
      }

      answer[rowId] = isRowSkipped ? 'null' : selectedCols
      const variantsController = row.variantsController
      if (variantsController && variantsController.hasValue) {
        const variantControllerData = variantsController.getData()

        let detailItemId = rowTitle

        if (this.hasDonor) {
          detailItemId = row.id === '-1' ? '-1' : rowTitle
        }

        if (!detail_item[detailItemId]) {
          detail_item[detailItemId] = {}
        }

        if (variantControllerData.textAnswer) {
          detail_item[detailItemId].answer = variantControllerData.textAnswer
          return
        }

        const itemsWithoutSelfAnswer = variantControllerData.detail_item.filter(i => i !== 'is_self_answer')
        detail_item[detailItemId] = [...itemsWithoutSelfAnswer]

        if (variantControllerData.self_variant) {
          detail_item[detailItemId].self_variant = variantControllerData.self_variant
        }
      }
    })

    const data = { answer, detail_item }

    if (this.commentEnabled.value) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    return toValue(this.rows)
      .map(row => `${row.title}: ${row.selectedCols.value.map(colIndex => this.cols.value[colIndex]).join(', ')}`)
      .join('; ')
  }

  get hasValue() {
    if (this.skip.value && this.skipped.value)
      return true
    if (this.hasPreviousAnswer) {
      return true
    }
    return toValue(this.rows).some(row => row.selectedCols.value.length > 0)
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Matrix question only supports BEHAVIOR_ALWAYS
    return false
  }

  toggleCol(row, colIndex) {
    if (this.skipped.value) {
      this.skipped.value = false
    }

    if (row.skipped.value) {
      row.skipped.value = false
    }

    const index = row.selectedCols.value.indexOf(colIndex)
    if (index === -1) {
      if (this.multipleChoice.value) {
        row.selectedCols.value.push(colIndex)
      }
      else {
        row.selectedCols.value = [colIndex]
      }
    }
    else {
      row.selectedCols.value.splice(index, 1)
    }
  }

  toggleSkip(row) {
    row.skipped.value = !row.skipped.value
    if (row.skipped.value) {
      row.selectedCols.value = []
      row.error.value = null
    }
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      const answers = toValue(this.rows).map((row) => {
        const selectedCols = row.selectedCols.value
          .map(colIndex => this.cols.value[colIndex])
          .filter(Boolean)

        if (selectedCols.length === 0 && !row.skipped.value)
          return null

        const rowAnswer = row.skipped.value ? this.t('Пропущено') : selectedCols.join(', ')
        return `${toValue(row.title)}: ${rowAnswer}`
      }).filter(Boolean)

      return answers.join('; ')
    })
  }

  prepareRows(variants = []) {
    // If no donor variants, return original rows and create matrix rows
    if (!variants.length) {
      const originalRows = this.matrixSettings.value.rows || []
      const rows = originalRows
        .map((row, rowIndex) => {
          let previousRowAnswer = this.parsedPreviousAnswer[row] || []
          const hasSkippedValue = previousRowAnswer === 'null' || previousRowAnswer?.includes?.('null')

          if (Array.isArray(previousRowAnswer)) {
            previousRowAnswer = previousRowAnswer.map(colTitle => this.cols.value.indexOf(colTitle))
          }

          const _title = ref(row)
          const rowData = {
            rawTitle: row,
            title: computed({
              get: () => toValue(this.labels)?.rows?.[rowIndex] || _title.value,
              set: (value) => {
                _title.value = value
              },
            }),
            id: `row_${rowIndex}`,
            selectedCols: ref(hasSkippedValue ? [] : previousRowAnswer),
            error: ref(null),
            touched: ref(false),
            position: rowIndex,
            skipped: ref(hasSkippedValue),
          }

          rowData.variantsController = this.createVariantsController(rowData)

          return rowData
        })

      return this.sortRows(rows)
    }

    // For donor variants
    const donorVariants = variants
      .map((v, rowIndex) => {
        const rawVariantTitle = v.path || v.rawTitle || v.question
        let previousRowAnswer = this.parsedPreviousAnswer[v.donorVariantId] || []
        const hasSkippedValue = previousRowAnswer === 'null' || previousRowAnswer?.includes?.('null')

        if (Array.isArray(previousRowAnswer)) {
          previousRowAnswer = previousRowAnswer.map(colTitle => this.cols.value.indexOf(colTitle))
        }

        const rowData = {
          rawTitle: rawVariantTitle,
          title: computed(() => toValue(this.labels)?.rows?.[rowIndex] || this.getRecipientVariantName(v)),
          id: `row_${rowIndex}`,
          selectedCols: ref(hasSkippedValue ? [] : previousRowAnswer),
          donorVariantId: v.donorVariantId || null,
          error: ref(null),
          touched: ref(false),
          position: rowIndex,
          skipped: ref(hasSkippedValue),
        }

        rowData.variantsController = this.createVariantsController(rowData)
        return rowData
      })

    return this.sortRows(donorVariants)
  }

  sortRows(rows) {
    if (this.randomVariantsOrder.value) {
      return shuffle(rows)
    }

    return rows.sort((a, b) => a.position - b.position)
  }

  resolveViewLogic(rule) {
    const ruleVariants = rule.variants || []
    const skippedRows = (rule.skipped || []).map(row => row.toString())

    // Group variants by row
    const variantsByRow = ruleVariants.reduce((acc, v) => {
      acc[v.row] = acc[v.row] || []
      acc[v.row].push(v.col)
      return acc
    }, {})

    // Check each row that has conditions (both regular and skipped)
    const rowsToCheck = [...new Set([...Object.keys(variantsByRow), ...skippedRows])]

    return rowsToCheck.every((rowTitle) => {
      // Find the corresponding row in our model
      const row = toValue(this.rows).find(r => toValue(r.rawTitle) === rowTitle)
      if (!row)
        return false

      // Get allowed column values for this row
      const allowedCols = variantsByRow[rowTitle] || []
      if (!allowedCols)
        return true // No conditions for this row

      // Get selected column indices and convert them to actual column values
      const selectedCols = toValue(row.selectedCols)
        .map(colIndex => this.cols.value[colIndex])
        .filter(Boolean)

      const conditionIsMet = selectedCols.some(col => allowedCols.includes(col))
      const isUnrequiredWithoutSelected = !toValue(this.isRequired) && selectedCols.length === 0
      const isSkipped = toValue(this.skipped)
      const isVariantSkipped = toValue(row.skipped)

      if (isSkipped && skippedRows.length) {
        return true
      }

      const skippedConditionIsMet = skippedRows.includes(rowTitle)
        && (isSkipped || isVariantSkipped || isUnrequiredWithoutSelected)

      return conditionIsMet || skippedConditionIsMet
    })
  }

  clearSelectedCols() {
    toValue(this.rows).forEach((row) => {
      row.selectedCols.value = []
    })
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update matrix settings
    if (previewData.matrixSettings) {
      this.matrixSettings.value = previewData.matrixSettings || {}
      const multipleChoiceChanged = this.multipleChoice.value !== (this.matrixSettings.value.multiple_choice === 1)
      const matrixTypeChanged = this.isStandartType.value !== (this.matrixSettings.value.type === 'standart')
      const dropdownVariantsChanged = this.dropdownVariants.value !== !!previewData.dropdownVariants
      const randomVariantsOrderChanged = this.randomVariantsOrder.value !== !!previewData.random_variants_order
      const skipChanged = !!this.skip.value !== !!previewData.skip
      const skipVariantChanged = this.skipVariant.value !== previewData.skip_variant
      const minRowsReqChanged = this.minRowsReq.value !== Number.parseInt(this.matrixSettings.value.minRowsReq)
      const rowsLengthsChanged = this.rows.value.length !== this.matrixSettings.value.rows?.length

      const rowsTitlesChanged = this.rows.value.some((row, index) =>
        toValue(row.rawTitle) !== this.matrixSettings.value.rows[index],
      )
      const columnsChanged = this.cols.value.length !== this.matrixSettings.value.cols?.length
      const columnsTitlesChanged = this.cols.value.some((col, index) =>
        col !== this.matrixSettings.value.cols[index],
      )

      this.multipleChoice.value = this.matrixSettings.value.multiple_choice === 1
      this.isStandartType.value = this.matrixSettings.value.type === 'standart'
      this.minRowsReq.value = Number.parseInt(this.matrixSettings.value.minRowsReq) || 0
      this.cols.value = this.matrixSettings.value.cols || []

      const shouldClearSelectedCols = multipleChoiceChanged
        || dropdownVariantsChanged
        || randomVariantsOrderChanged
        || skipChanged
        || skipVariantChanged
        || minRowsReqChanged
        || columnsChanged
        || columnsTitlesChanged
        || rowsLengthsChanged
        || rowsTitlesChanged
        || matrixTypeChanged

      if (shouldClearSelectedCols) {
        this.clearSelectedCols()
      }

      if (rowsLengthsChanged) {
        const rows = this.prepareRows()
        this.rows.value = this.sortRows(rows)
      }
      else if (rowsTitlesChanged) {
        this.rows.value.forEach((row, index) => {
          row.rawTitle = this.matrixSettings.value.rows[index]
          row.title.value = this.matrixSettings.value.rows[index]
        })

        triggerRef(this.rows)
      }
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }
    if (!this.skip.value) {
      this.skipped.value = false
    }
    if (previewData.skip_text !== undefined) {
      this._skipText.value = previewData.skip_text
    }
    const skipVariantChanged = previewData.skip_variant !== this.skipVariant.value
    if (skipVariantChanged) {
      this.skipVariant.value = previewData.skip_variant
      this.skipped.value = false
      this.rows.value.forEach((row) => {
        row.skipped.value = false
      })
    }

    // Update random order
    if (previewData.random_variants_order !== undefined) {
      this.randomVariantsOrder.value = previewData.random_variants_order === 1
    }

    // Update dropdown settings
    if (previewData.dropdownVariants !== undefined) {
      this.dropdownVariants.value = !!previewData.dropdownVariants
    }
    if (previewData.selectPlaceholder !== undefined) {
      this._selectPlaceholderText.value = previewData.selectPlaceholder
    }

    // Update comment fields
    if (previewData.commentEnabled !== undefined) {
      this.commentEnabled.value = previewData.commentEnabled
    }
    this.commentController.updateFromPreview({
      enabled: this.commentEnabled.value,
      required: previewData.comment_required || false,
      title: previewData.comment_label || '',
      placeholderText: previewData.placeholderText || '',
      textFieldParam: previewData.textFieldParam || { min: 0, max: 1000 },
    })

    // Update gallery if enabled
    if (previewData.enableGallery !== undefined) {
      this.enableGallery.value = previewData.enableGallery
    }

    this.galleryController.updateFromPreview({
      enabled: this.enableGallery.value,
      gallery: previewData.gallery || [],
    })

    // Update clarifying question settings
    if (previewData.clarifyingQuestion) {
      this.extraQuestion.value = {
        rows: previewData.matrixSettings?.extra_question?.rows || [],
        cols: previewData.matrixSettings?.extra_question?.cols || [],
      }
      this.variantsType.value = previewData.variantsType
      this.isHaveCustomField.value = previewData.isHaveCustomField
      this.isExtraRequired.value = previewData.extra_required
      this.textFieldParam.value = previewData.textFieldParam || {}
      this.placeholderText.value = previewData.placeholderText
      this.clarifyingQuestionText.value = previewData.clarifyingQuestion.text
      this.clarifyingQuestionEnabled.value = previewData.clarifyingQuestion.enabled

      const selfVariantRange = previewData.clarifyingQuestion.customAnswerRange || []
      const selfVariantMinLength = selfVariantRange[0] || 0
      const selfVariantMaxLength = selfVariantRange[1] || 250

      this.rows.value.forEach((row) => {
        if (row.variantsController) {
          row.variantsController.updateFromPreview({
            variants: previewData.variants,
            variantsType: previewData.variantsType,
            hasCustomField: previewData.clarifyingQuestion.customAnswerEnabled,
            isRequired: previewData.clarifyingQuestion.required,
            label: previewData.clarifyingQuestion.text,
            selfVariantText: previewData.clarifyingQuestion.customAnswerLabel,
            textFieldParam: { min: selfVariantMinLength, max: selfVariantMaxLength },
            placeholderText: previewData.placeholderText,
            selfVariantPlaceholderText: previewData.clarifyingQuestion.customAnswerPlaceholder,
            selfVariantMinLength,
            selfVariantMaxLength,
          })
        }
      })
    }
  }
}
