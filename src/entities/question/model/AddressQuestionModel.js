import { useTranslationsStore } from '@/shared/store/translationsStore'
import { BEHAVIOR_ALWAYS } from '@shared/constants/logic'
import { computed, ref, toValue, watch } from 'vue'
import { BaseQuestion } from './BaseQuestion'

const MIN_LENGTH_FOR_RIGHT_ANSWER = 3

export class AddressQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    this.translationsStore = useTranslationsStore()
    this.t = this.translationsStore.t

    // Make IDs reactive
    this.arRegionsIDs = ref(data.arRegionsIDs?.map(r => r.id) || [])
    this.arDistrictsIDs = ref(data.arDistrictsIDs?.map(r => r.id) || [])
    this.arCitiesIDs = ref((data.arCityIDs || data.arCitiesIds || []).map(r => r.id))
    this.arStreetsIDs = ref(data.arStreetsIDs?.map(r => r.id) || [])
    this.isRequired = ref(data.isRequired === 1)

    this.value = ref(this.previousAnswer?.answer || '')
    this.touched = ref(false)

    // Private ref for placeholderText
    this._placeholderText = ref(data.placeholderText)
    this.placeholderText = computed(() => {
      return toValue(this.translations).placeholder_text || this._placeholderText.value
    })
    this.error = ref('')

    this.isSelected = ref(false)
    this.hasSuggestions = ref(false)
    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })

    watch(this.value, (newValue) => {
      if (newValue) {
        this.markInteracted()
      }
    })
  }

  setAllAsTouched() {
    this.touched.value = true
  }

  validate() {
    this.error.value = ''
    if (this.isRequired.value && !this.value.value) {
      this.error.value = this.t('Обязательное поле')
      return false
    }

    if (this.value.value && !this.isSelected.value && this.hasSuggestions.value) {
      this.error.value = this.t('Некорректный формат')
      return false
    }

    if (this.value.value && this.value.value.length < MIN_LENGTH_FOR_RIGHT_ANSWER) {
      this.error.value = this.t('Некорректный формат')
      return false
    }

    return true
  }

  get hasValue() {
    if (this.hasPreviousAnswer) {
      return true
    }
    return this.value.value
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip && this.skipped.value)
        return ''

      return this.value.value
    })
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Address questions only support BEHAVIOR_ALWAYS
    return condition.behavior === BEHAVIOR_ALWAYS
  }

  resetFields() {
    this.value.value = ''
    this.touched.value = false
    this.isSelected.value = false
    this.hasSuggestions.value = false
  }

  updateFromPreview(previewData) {
    // Call parent class's updateFromPreview first
    super.updateFromPreview(previewData)

    // Update address-specific fields
    if (previewData.isRequired !== undefined)
      this.isRequired.value = previewData.isRequired

    if (previewData.placeholderText !== undefined)
      this._placeholderText.value = previewData.placeholderText

    const regionIdsAreChanged = JSON.stringify(this.arRegionsIDs.value) !== JSON.stringify(previewData.arRegionsIDs)
    const districtIdsAreChanged = JSON.stringify(this.arDistrictsIDs.value) !== JSON.stringify(previewData.arDistrictsIDs)
    const citiesIdsAreChanged = JSON.stringify(this.arCitiesIDs.value) !== JSON.stringify(previewData.arCityIDs || previewData.arCitiesIds)
    const streetsIdsAreChanged = JSON.stringify(this.arStreetsIDs.value) !== JSON.stringify(previewData.arStreetsIDs)

    // Update IDs if provided
    if (Array.isArray(previewData.arRegionsIDs))
      this.arRegionsIDs.value = previewData.arRegionsIDs
    if (Array.isArray(previewData.arDistrictsIDs))
      this.arDistrictsIDs.value = previewData.arDistrictsIDs
    if (Array.isArray(previewData.arCityIDs) || Array.isArray(previewData.arCitiesIds)) {
      const citiesIds = previewData.arCityIDs || previewData.arCitiesIds || []
      this.arCitiesIDs.value = citiesIds
    }
    if (Array.isArray(previewData.arStreetsIDs))
      this.arStreetsIDs.value = previewData.arStreetsIDs

    if (regionIdsAreChanged || districtIdsAreChanged || citiesIdsAreChanged || streetsIdsAreChanged) {
      this.resetFields()
    }
  }

  getData() {
    return {
      answer: this.value.value,
    }
  }

  getAnswer() {
    const value = this.value.value
    return value.trim()
  }
}
