import { declOfNum } from '@shared/helpers/string.js'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, onBeforeUnmount, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { BaseQuestion } from './BaseQuestion'

export class FirstClickTestQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    this.isImageVisible = ref(false)
    this.clickPoints = ref(this.parsePreviousAnswer(data?.answer?.clickPoints) || [])
    this.requestSent = ref(false)

    this.checkFirstOpen = ref(false)

    this.questionId = data.question_id

    this.showExpiredMessage = ref(false)
    this.checkNextPage = ref(false)

    this.skip = ref(data.skip)
    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || toValue(this.t('Затрудняюсь ответить'))
    })
    this.skipped = ref(this.previousAnswer?.skipped === 1)

    this.mobileView = ref(data?.firstClick?.mobile_view || 0)
    this.maxClickDot = ref(data?.firstClick?.max_click || null)
    this.minClickDot = ref(data?.firstClick?.min_click || 1)
    this.allowCancelClick = ref(data?.firstClick?.allow_cancel_click || 0)

    this.imageDisplayTime = ref(data?.firstClick?.show_time || null)
    this.remainingTime = ref(null)
    this.timerInterval = ref(null)
    this.timeExpired = ref(false)
    this.timerStarted = ref(!!data?.answer?.detail_item?.time_start)
    this.serverStartTime = ref(data?.answer?.detail_item?.time_start || null)

    if (data.priorityAnswer?.time_start && this.imageDisplayTime.value) {
      this.serverStartTime.value = new Date(data.priorityAnswer.time_start * 1000)
      const elapsedSeconds = Math.floor((new Date() - this.serverStartTime.value) / 1000)
      this.remainingTime.value = Math.max(0, this.imageDisplayTime.value - elapsedSeconds)

      if (this.remainingTime.value <= 0) {
        this.timeExpired.value = true
        this.checkFirstOpen.value = true
      }

      if (this.timeExpired.value && this.clickPoints.value.length) {
        this.checkNextPage.value = true
      }

      if (this.timeExpired.value && !this.clickPoints.value.length) {
        this.showExpiredMessage.value = true
      }
    }

    this.checkMaxClickPoints = computed(() => {
      if (this.maxClickDot.value) {
        return this.clickPoints.value.length + 1 <= this.maxClickDot.value
      }
      return true
    })

    this.isRequired = ref(data.isRequired)
    this.error = ref(null)

    this.touched = ref(false)

    this.textFieldParam = data.textFieldParam || {}
    this.placeholderText = data.placeholderText
    this.hasComment = ref(data.comment_enabled === 1)

    this.commentController = new CommentController({
      enabled: this.hasComment.value,
      required: data.comment_required,
      skipped: this.skipped,
      value: data.comment || '',
      placeholderText: this.placeholderText,
      minLength: this.textFieldParam.min || 0,
      maxLength: this.textFieldParam.max || 1000,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        this.emit('get-answer')
      }
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (skipped) => {
      if (skipped) {
        this.canMoveToNextQuestion.value = true
      }
    })

    this._firstClickButtonText = ref(data?.firstClick?.button_text)
    this.firstClickButtonText = computed(() => this.translations.value?.firstClickLang?.button_text || this._firstClickButtonText.value || 'Показать изображение')

    this.image = ref(data.gallery[0] || [])

    this._description = ref(data.description)
    this._subdescription = ref(data.subdescription)

    function extractCleanText(htmlString) {
      if (htmlString) {
        return htmlString.replace(/<[^>]*>/g, '').trim()
      }
    }

    this.tooltipTitle = computed(() => extractCleanText(this.translations.value?.description) || this._description.value)
    this.tooltipContent = computed(() => extractCleanText(this.translations.value?.sub_description) || extractCleanText(this._subdescription.value))

    onBeforeUnmount(() => {
      this.stopTimer()
    })
  }

  parsePreviousAnswer(answer) {
    if (!answer || !Array.isArray(answer))
      return []

    return answer.map(point => ({
      x: point.x,
      y: point.y,
      time: point.click_time,
    }))
  }

  initTimer(serverStartTime = null) {
    // Если у нас уже есть сохраненное время из priorityAnswer, используем его
    if (this.serverStartTime.value && !serverStartTime) {
      // Пересчитываем оставшееся время на основе сохраненного времени старта
      const elapsedSeconds = Math.floor((new Date() - this.serverStartTime.value) / 1000)
      this.remainingTime.value = Math.max(0, this.imageDisplayTime.value - elapsedSeconds)

      if (this.remainingTime.value <= 0) {
        this.handleTimeExpired()
      }
      else if (!this.timerInterval.value) {
        this.startTimer()
      }
      return
    }

    // Инициализация нового таймера
    this.serverStartTime.value = serverStartTime
      ? new Date(serverStartTime * 1000)
      : new Date()

    const elapsedSeconds = Math.floor((new Date() - this.serverStartTime.value) / 1000)
    this.remainingTime.value = Math.max(0, this.imageDisplayTime.value - elapsedSeconds)

    if (this.remainingTime.value <= 0) {
      this.handleTimeExpired()
      return
    }

    this.startTimer()
  }

  handleTimeExpired() {
    if (!this.imageDisplayTime.value)
      return

    this.timeExpired.value = true
    this.stopTimer()
    this.emit('time-expired')
    this.isImageVisible.value = false
    this.checkFirstOpen.value = true

    this.showExpiredMessage.value = true

    if (this.checkNextPage.value && this.timeExpired.value && this.clickPoints.value.length > 0) {
      this.showExpiredMessage.value = false
    }
  }

  startTimer() {
    if (this.timerInterval.value || this.timeExpired.value)
      return

    this.timerInterval.value = setInterval(() => {
      const elapsedSeconds = Math.floor((new Date() - this.serverStartTime.value) / 1000)
      this.remainingTime.value = Math.max(0, this.imageDisplayTime.value - elapsedSeconds)

      if (this.remainingTime.value <= 0) {
        this.handleTimeExpired()
      }
    }, 1000)
  }

  stopTimer() {
    if (this.timerInterval.value) {
      clearInterval(this.timerInterval.value)
      this.timerInterval.value = null
    }
  }

  resetTimer() {
    this.stopTimer()
    this.remainingTime.value = null
    this.timeExpired.value = false
    this.timerStarted.value = false
    this.serverStartTime.value = null
  }

  addClickPoint(x, y) {
    const timeInSeconds = this.serverStartTime.value
      ? Math.floor((new Date() - this.serverStartTime.value) / 1000)
      : 0

    const existingPointIndex = this.clickPoints.value.findIndex((point) => {
      return Math.abs(point.x - x) < 20 && Math.abs(point.y - y) < 20
    })

    if (existingPointIndex !== -1 && this.allowCancelClick.value) {
      this.clickPoints.value.splice(existingPointIndex, 1)
      this.touched.value = true
      this.validate()
      return true
    }

    if (this.checkMaxClickPoints.value) {
      this.clickPoints.value.push({
        x,
        y,
        time: timeInSeconds,
      })
      this.touched.value = true
      this.validate()
      return false
    }

    return null
  }

  resetFields() {
    this.touched.value = false
    this.commentController.value.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null
    this.clickPoints.value = []
  }

  setAllAsTouched() {
    this.touched.value = true
    if (toValue(this.commentController.enabled)) {
      this.commentController.touched.value = true
    }
  }

  validate() {
    this.error.value = null

    if (!this.touched.value)
      return true

    if (!this.isRequired.value) {
      this.error.value = null
      return true
    }

    if (this.skip && this.skipped.value) {
      this.error.value = null
      return true
    }

    if (this.timeExpired.value)
      return true

    if (this.clickPoints.value.length < this.minClickDot.value) {
      if (this.minClickDot.value === 1) {
        this.error.value = this.t('Нужно поставить хотя бы одну точку на изображении')
      }
      else {
        const N = this.minClickDot.value
        const word = this.getAreaForm(N)
        this.error.value = this.t(`Необходимо отметить хотя бы {N} ${word}`, { N })
      }
      return false
    }

    if (this.hasComment.value)
      this.commentController.validate()

    if (this.hasComment.value && this.isCommentRequired && !this.commentController.isValid.value)
      return false

    return true
  }

  getAreaForm(n) {
    return declOfNum(n, ['точку', 'точки', 'точек'])
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {}

    this.clickPoints.value.forEach((point, index) => {
      data[`point[${index}]`] = `${point.x};${point.y};${point.time}`
    })

    if (this.timeExpired.value && !this.clickPoints.value.length) {
      data.time_expired = 1
    }

    if (this.clickPoints.value.length > 0 && this.timeExpired.value) {
      this.showExpiredMessage.value = false
    }

    if (toValue(this.commentController.enabled)) {
      data.answer = toValue(this.commentController.value)
    }

    this.checkNextPage.value = true

    return data
  }

  getAnswer() {
    const count = this.clickPoints.value.length

    if (!count)
      return ''

    return `${count} ${declOfNum(count, ['точку', 'точки', 'точек'])}`
  }

  get hasValue() {
    if (this.skip && this.skipped.value)
      return true

    if (
      (this.imageDisplayTime.value && !this.timeExpired.value && this.isRequired.value)
      || (!this.imageDisplayTime.value && !this.clickPoints.value.length && this.isRequired.value)
    ) {
      return false
    }

    const hasPreviousAnswer = !!this.previousAnswer
    return hasPreviousAnswer
  }

  checkCondition(condition) {
    if (super.checkCondition(condition))
      return true

    return false
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      const count = this.clickPoints.value.length
      if (!count)
        return ''

      const words = ['точку', 'точки', 'точек']
      const declWord = declOfNum(count, words)
      const translated = this.t(`{count} ${declWord}`, { count })
      return toValue(translated)
    })
  }

  resolveViewLogic() {
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    this.isImageVisible.value = false
    this.clickPoints.value = []
    this.touched.value = false
    this.error.value = null

    // Update comment controller
    this.commentController.updateFromPreview({
      enabled: !!previewData.commentData.galleryCommentEnabled,
      required: !!previewData.commentData.galleryCommentRequaired,
      title: previewData.commentData.galleryCommentLabel || '',
      placeholderText: previewData.commentData.galleryCommentPlaceholder || '',
      textFieldParam: {
        min: previewData.commentData.galleryCommentLengthRange.min || 0,
        max: previewData.commentData.galleryCommentLengthRange.max || 1000,
      },
    })

    this._firstClickButtonText.value = previewData.buttonText

    const skipIsChanged = this.skip.value !== previewData.skip
    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }
    if (skipIsChanged) {
      this.skipped.value = false
    }

    if (previewData.minClicks) {
      this.maxClickDot.value = previewData.minClicks
    }

    this.maxClickDot.value = previewData.maxClicks

    this.allowCancelClick.value = previewData.allowClickCancel

    if (previewData.hasTimeLimit) {
      this.resetTimer()
      if (previewData.displayTime !== this.imageDisplayTime.value) {
        this.imageDisplayTime.value = previewData.displayTime
      }
    }

    this.image.value = previewData.gallery[0]

    if (previewData.mobileDisplay === 'width') {
      this.mobileView.value = 0
    }
    else if (previewData.mobileDisplay === 'height') {
      this.mobileView.value = 1
    }

    if (!previewData.hasTimeLimit) {
      this.resetTimer()
      this.imageDisplayTime.value = null
    }

    if (previewData.skipText) {
      this._skipText.value = previewData.skipText
    }
  }
}
