import { BEHAVIOR_ALWAYS } from '@shared/constants/logic'
import MaskTypes from '@shared/constants/maskTypes'
import { getMonths } from '@shared/helpers/date'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, ref, toValue, watch } from 'vue'
import { BaseQuestion } from './BaseQuestion'
import { MaskedFieldModel } from './MaskedFieldModel'

export class DateQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const { t } = useTranslationsStore()
    this.t = t

    this.dateType = ref(data.dateType)
    this.onlyDateMonth = ref(data.only_date_month === 1)
    this.months = computed(() => getMonths(this.t))

    this.isRequired = ref(!!data.isRequired)

    this.maskType = ref(this.determineMaskType(data))

    this.parsedPreviousAnswer = this.parsePreviousAnswer(this.previousAnswer?.answer)

    this.maskedField = new MaskedFieldModel({
      translations: this.translations,
      months: this.months,
      previousAnswer: this.parsedPreviousAnswer,
      type: toValue(this.maskType),
      fieldConfig: {
        placeholderText: data.placeholderText,
        value: data.value || '',
        required: toValue(this.isRequired),
      },
    })

    this.touched = ref(false)
    this.err = ref(null)

    this.isValid = computed(() => this.maskedField.isValid.value)
    this.blocked = computed(() => this.maskedField.blocked.value)

    watch(() => this.maskedField.hasInteracted.value, (hasInteracted) => {
      if (hasInteracted) {
        this.markInteracted()
      }
    })
  }

  determineMaskType(data) {
    if (data.dateType === 0 && !data.only_date_month) {
      return MaskTypes.Date
    }
    else if (data.dateType === 0 && data.only_date_month) {
      return MaskTypes.DateMonth
    }
    else if (data.dateType === 1) {
      return MaskTypes.Time
    }
    else if (data.dateType === 2 && !data.only_date_month) {
      return MaskTypes.DateTime
    }
    else if (data.dateType === 2 && data.only_date_month) {
      return MaskTypes.DateMonthTime
    }
  }

  updateFromPreview(previewData) {
    super.updateFromPreview?.(previewData)

    this.isRequired.value = previewData.isRequired
    this.dateType.value = previewData.dateType
    this.onlyDateMonth.value = previewData.only_date_month === 1

    const newMaskType = this.determineMaskType(previewData)
    const maskTypeChanged = this.maskType.value !== newMaskType

    if (maskTypeChanged) {
      this.resetFields()
    }

    this.maskType.value = newMaskType

    this.maskedField.updateFromPreview({
      type: this.maskType.value,
      fieldConfig: {
        placeholderText: previewData.placeholderText,
        required: this.isRequired.value,
      },
    })
  }

  /**
   * Парсинг предыдущего ответа респондента
   * Ответ может быть в разных форматах, в зависимости от типа поля
   * Например, для маски "Дата (день.месяц)" ответ приходит в формате "12.05"
   * Для маски "ФИО" ответ приходит в формате {name: "Иван", surname: "Иванов", patronymic: "Иванович"}
   * А для маски "День и месяц" ответ приходит в формате "12.05"
   * @param {string} answer - Предыдущий ответ
   * @returns {object} - Объект с предыдущим ответом
   */
  parsePreviousAnswer(answer) {
    if (answer === null || answer === undefined) {
      return null
    }

    const parseDayMonth = (answer) => {
      if (!answer) {
        return { day: '', month: '' }
      }
      const [day, month] = answer.split('.')
      return { day, month }
    }

    try {
      const parsedAnswer = JSON.parse(answer)

      if (this.maskType.value === MaskTypes.Date) {
        return { date: parsedAnswer?.date || '' }
      }
      // Если маска дата день.месяц
      if (this.maskType.value === MaskTypes.DateMonth) {
        const { day, month } = parseDayMonth(parsedAnswer?.date)
        return { day, month }
      }
      if (this.maskType.value === MaskTypes.Time) {
        return { time: parsedAnswer?.time || '' }
      }
      if (this.maskType.value === MaskTypes.DateTime) {
        return { date: parsedAnswer?.date || '', time: parsedAnswer?.time || '' }
      }
      if (this.maskType.value === MaskTypes.DateMonthTime) {
        const { day, month } = parseDayMonth(parsedAnswer?.date)
        return { day, month, time: parsedAnswer?.time || '' }
      }
      return { value: parsedAnswer }
    }
    catch (e) {
      console.error('Error parsing choosedMediaVariants:', e)
      return { value: answer }
    }
  }

  validateField() {
    this.err.value = null
    if (!this.touched.value)
      return true

    if (!toValue(this.isRequired)) {
      this.err.value = null
      return true
    }

    if (!this.maskedField.isValid.value) {
      this.err.value = this.maskedField.error.value
      return false
    }

    this.err.value = null
    return true
  }

  checkValidity() {
    return this.maskedField.isValid.value
  }

  get hasValue() {
    if (this.hasPreviousAnswer) {
      return true
    }
    return this.maskedField.hasValue()
  }

  getData() {
    const value = this.maskedField.getData()

    if (this.dateType.value === 1) {
      return { time: value }
    }

    if (this.dateType.value === 2) {
      return value
    }

    return { date: value }
  }

  getAnswer() {
    const value = this.maskedField.getData()
    return value.trim()
  }

  submit() {
    this.emit('next')
  }

  validate() {
    this.validateField()
    return this.checkValidity()
  }

  setAllAsTouched() {
    this.touched.value = true
    this.maskedField.setAllAsTouched()
  }

  resetFields() {
    this.touched.value = false
    this.err.value = null
  }

  getStringifiedAnswer() {
    return computed(() => {
      const data = this.maskedField.getData()
      if (this.skip && this.skipped.value)
        return ''

      if (this.maskType.value === MaskTypes.DateTime) {
        return `${data.date} ${data.time}`
      }

      if (this.maskType.value === MaskTypes.DateMonthTime) {
        return `${data.date} ${data.time}`
      }

      return data
    })
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Date/time questions only support BEHAVIOR_ALWAYS
    return condition.behavior === BEHAVIOR_ALWAYS
  }
}
