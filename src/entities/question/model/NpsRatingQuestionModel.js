import { EXTRA_QUESTION_TYPE } from '@shared/constants'
import {
  BE<PERSON><PERSON>OR_ALWAYS,
  BE<PERSON><PERSON>OR_MISS,
  BEHAVIOR_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { NPSGradient } from '@shared/helpers/color'
import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, isRef, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { VariantsController } from '../controllers/VariantsController'
import { BaseQuestion } from './BaseQuestion'

const NPS_DESIGN = {
  COLORED: 1,
  BLACK_AND_WHITE: 2,
  CUSTOM_GRADIENT: 3,
}

export class NpsRatingQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    this.skip = ref(data.skip)
    this._skipTextInnerRef = ref(null)
    this.skipText = computed({
      get: () => this.translations.value?.skip_text || this._skipTextInnerRef.value || data.skip_text || toValue(this.t('Не готов(а) оценить')),
      set: value => this._skipTextInnerRef.value = value,
    })
    this.skipped = ref(this.previousAnswer?.skipped === 1)
    this.canMoveToNextQuestion = ref(false)

    const npsConfig = data.npsRatingSetting || {}

    this.isVariants = ref(data.set_variants)
    this.isRequired = ref(data.isRequired)
    this.randomVariantsOrder = ref(data.random_variants_order === 1)
    this.variants = shallowRef([])

    const rawDetailAnswers = (data.detail_answers || [])
      .filter(v => v?.extra_question === undefined || v?.extra_question !== 1)
    this.detailAnswers = shallowRef(rawDetailAnswers)

    this._extraQuestionType = ref(data.extra_question_type || EXTRA_QUESTION_TYPE.DISABLED)
    this.extraQuestionType = computed(() => this._extraQuestionType.value)

    this._extraQuestionRateFrom = ref(data.extra_question_rate_from || 0)
    this.extraQuestionRateFrom = computed(() => this._extraQuestionRateFrom.value)

    this._extraQuestionRateTo = ref(data.extra_question_rate_to || 10)
    this.extraQuestionRateTo = computed(() => this._extraQuestionRateTo.value)

    this.extraQuestionVariants = ref(data.detail_answers?.filter(v => v.extra_question === 1))

    this.variantsController = this.initializeSingleVariantsController(this.data, this.extraQuestionVariants.value)

    if (this.isVariants.value) {
      if (!this.hasDonor) {
        const filteredVariants = this.detailAnswers.value.filter(v => !v.is_deleted)
        this.variants.value = this.prepareVariants(filteredVariants)
      }

      watch(this.selectedDonorVariants, (newDonorVariants) => {
        if (!this.hasDonor)
          return

        const filteredDetailAnswers = []

        newDonorVariants.forEach((v) => {
          if (v.id === 'is_self_answer') {
            const selfVariantFromRecipient = this.detailAnswers.value
              .find(answer => this.isVariantRecipientSelfAnswer(answer))

            if (!selfVariantFromRecipient) {
              return
            }

            selfVariantFromRecipient.id = '-1'
            selfVariantFromRecipient.alternativeVariantLabel = computed(() => toValue(v.alternativeVariantLabel))
            filteredDetailAnswers.push(selfVariantFromRecipient)
            return
          }

          const donorVariant = this.detailAnswers.value
            .find(answer => answer.dictionary_element_id === v.id || answer.question_detail_id === v.id)

          if (donorVariant) {
            donorVariant.question = this.getRecipientVariantName(donorVariant)
            donorVariant.id = v.id
            filteredDetailAnswers.push(donorVariant)
          }
        })

        const newVariants = this.prepareVariants(filteredDetailAnswers, true)
        const existingVariants = this.variants.value || []
        const preservedVariants = []

        newVariants.forEach((newVariant) => {
          const existingVariant = existingVariants.find(v => v.id === newVariant.id)
          if (existingVariant) {
            preservedVariants.push(existingVariant)
          }
          else {
            preservedVariants.push(newVariant)
          }
        })

        this.variants.value = toValue(this.randomVariantsOrder)
          ? shuffle(preservedVariants)
          : preservedVariants.sort((a, b) => a.position - b.position)
      }, { immediate: true })

      watch(this.variants, (newVariants) => {
        newVariants.forEach((variant) => {
          watch(variant.rating, (newValue) => {
            if (newValue > -1) {
              this.skipped.value = false
            }
            variant.touched.value = true
            this.validateVariant(variant)
          })
        })
      }, { immediate: true })
    }

    const previousRating = this.previousAnswer?.rating
    this.rating = ref(previousRating === undefined ? -1 : previousRating)
    this.err = ref(null)

    this.npsDesign = ref(npsConfig.design)
    this.npsStartColor = ref(npsConfig.start_point_color)
    this.npsEndColor = ref(npsConfig.end_point_color)

    this._labelsFromTranslationsInnerRef = ref(null)
    this.labelsFromTranslations = computed({
      get: () => {
        if (this._labelsFromTranslationsInnerRef.value)
          return this._labelsFromTranslationsInnerRef.value
        const labelsJsonFromTranslations = this.translations.value?.labels || '[]'
        return JSON.parse(labelsJsonFromTranslations)
      },
      set: value => this._labelsFromTranslationsInnerRef.value = value,
    })

    this._npsStartLabelInnerRef = ref(npsConfig.start_label)
    this.npsStartLabel = computed({
      get: () => {
        const labels = this.labelsFromTranslations.value
        return labels[0] || this._npsStartLabelInnerRef.value
      },
      set: value => this._npsStartLabelInnerRef.value = value,
    })

    this._npsEndLabelInnerRef = ref(npsConfig.end_label)
    this.npsEndLabel = computed({
      get: () => {
        const labels = this.labelsFromTranslations.value
        return labels[labels.length - 1] || this._npsEndLabelInnerRef.value
      },
      set: value => this._npsEndLabelInnerRef.value = value,
    })

    this.fromOne = ref(data.from_one || data.fromOne)
    this.gradientCount = ref(11)

    this._colorsInnerRef = ref(null)
    this.colors = computed({
      get: () => {
        if (this._colorsInnerRef.value)
          return this._colorsInnerRef.value
        if (this.npsDesign.value === NPS_DESIGN.CUSTOM_GRADIENT || this.npsDesign.value === NPS_DESIGN.COLORED) {
          return NPSGradient(this.npsStartColor.value, this.npsEndColor.value)
        }
        return []
      },
      set: value => this._colorsInnerRef.value = value,
    })

    this.touched = ref(false)

    if (this.isVariants.value) {
      watch(this.variants, () => {
        this.variants.value.forEach((v) => {
          watch(v.rating, (newValue) => {
            if (newValue > -1) {
              this.skipped.value = false
              this.markInteracted()
            }

            const shouldMoveToNextQuestion = newValue > -1
              && this.variants.value.length === 1
              && !this.commentEnabled.value
            if (shouldMoveToNextQuestion) {
              this.canMoveToNextQuestion.value = true
            }
            v.touched.value = true
            this.validateVariant(v)
          })
        })
      }, { immediate: true })
    }
    else {
      watch(this.rating, (newValue) => {
        if (newValue > -1) {
          this.skipped.value = false
          this.markInteracted()
        }
        if (newValue !== -1 && !this.commentEnabled.value) {
          this.canMoveToNextQuestion.value = true
        }
        this.touched.value = true
        this.validateRating()
      })
    }

    this.isCommentRequired = ref(data.comment_required)
    this.commentEnabled = ref(data.comment_enabled)
    this.textFieldParam = ref(data.textFieldParam || {})
    this.placeholderText = ref(data.placeholderText)

    this.commentController = new CommentController({
      enabled: this.commentEnabled.value,
      required: this.isCommentRequired.value,
      skipped: this.skipped,
      value: this.previousAnswer?.self_variant || '',
      placeholderText: this.placeholderText.value,
      minLength: this.textFieldParam.value.min || 0,
      maxLength: this.textFieldParam.value.max || 1000,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: this.enableGallery.value,
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this.isValid = computed(() => this.validate())

    this.blocked = computed(() => !this.isValid.value)

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        this.canMoveToNextQuestion.value = true
      }
    })

    this.selectedVariants = computed(() => {
      if (this.isVariants.value) {
        return this.variants.value.filter(v => toValue(v.rating) > -1).map(v => toValue(v.rating))
      }
      else {
        return this.rating.value > -1 ? [this.rating.value] : []
      }
    })
  }

  prepareVariant(v, index, fromDonor = false) {
    let previousVariantsObject = {}
    try {
      previousVariantsObject = JSON.parse(this.previousAnswer?.answer || '{}')
    }
    catch {
      // Do nothing
    }

    const previousRating = Number.parseInt(previousVariantsObject[v.id])
    const hasPreviousRating = !Number.isNaN(previousRating)

    const _text = ref(v.variant || v.question || v.value)
    const variant = {
      ...v,
      persistentId: v.persistentId || v.id,
      position: v.position || index,
      hasPreviousRating,
      rating: ref(hasPreviousRating ? previousRating : -1),
      error: ref(null),
      touched: ref(false),
      needExtra: ref(v.need_extra),
      extraQuestionRateFrom: ref(v.extra_question_rate_from),
      extraQuestionRateTo: ref(v.extra_question_rate_to),
      text: computed({
        get: () => {
          const alternativeVariantLabel = toValue(v.alternativeVariantLabel)
          if (fromDonor && alternativeVariantLabel) {
            return alternativeVariantLabel
          }
          const translation = this.translations.value?.detailLangs?.[v.id]?.question
          return translation || _text.value
        },
        set: value => _text.value = value,
      }),
    }
    // Always add variants controller
    variant.variantsController = this.createVariantsControllerForVariant(
      this.data,
      variant,
      toValue(this.extraQuestionType),
    )

    return variant
  }

  prepareVariants(variants = [], fromDonor = false) {
    const transformedVariants = variants
      .map((v, index) => this.prepareVariant(v, index, fromDonor))
      .sort((a, b) => a.position - b.position)

    return toValue(this.randomVariantsOrder) ? shuffle(transformedVariants) : transformedVariants
  }

  resetFields() {
    this.touched.value = false
    if (this.isVariants.value) {
      this.variants.value.forEach((v) => {
        v.rating.value = -1
        v.error.value = null
        v.touched.value = false
        if (v.variantsController) {
          v.variantsController.resetFields()
        }
      })
    }
    else {
      if (isRef(this.rating)) {
        this.rating.value = -1
        this.err.value = null
      }
      else {
        this.rating = ref(-1)
        this.err = ref(null)
      }
    }
    this.commentController.value.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null

    // Reset variants controller if it exists
    if (this.variantsController) {
      this.variantsController.resetFields()
    }
  }

  setAllAsTouched() {
    this.touched.value = true
    if (this.isVariants.value) {
      this.variants.value.forEach((v) => {
        v.touched.value = true
        if (v.variantsController) {
          v.variantsController.touched.value = true
          v.variantsController.selfVariantCommentTouched.value = true
        }
      })
    }
    if (this.commentEnabled.value) {
      this.commentController.touched.value = true
    }
    if (this.variantsController) {
      this.variantsController.touched.value = true
      this.variantsController.selfVariantCommentTouched.value = true
    }
  }

  validateVariant(variant) {
    variant.error.value = null

    if (!variant.touched.value) {
      return true
    }

    if (!toValue(this.isRequired)) {
      return true
    }

    if (this.skip.value && this.skipped.value) {
      return true
    }

    if (toValue(this.isRequired) && variant.rating.value <= -1) {
      variant.error.value = this.t('Нужно поставить оценку')
      return false
    }

    return true
  }

  validateRating() {
    if (!this.touched.value)
      return true

    if (!toValue(this.isRequired)) {
      this.err.value = null
      return true
    }

    if (this.skip.value && this.skipped.value) {
      this.err.value = null
      return true
    }

    if (this.rating.value <= -1) {
      this.err.value = this.t('Нужно поставить оценку')
      return false
    }

    this.err.value = null
    return true
  }

  validate() {
    let isValid = true
    let commentIsValid = true
    let variantsControllerValid = true

    const isSkipped = this.skip.value && this.skipped.value
    if (!this.touched.value && isSkipped) {
      return true
    }

    if (this.isVariants.value) {
      this.variants.value.forEach((v) => {
        if (!this.validateVariant(v)) {
          isValid = false
        }
        if (toValue(v.needExtra) && v.variantsController && toValue(v.variantsController.enabled)) {
          if (!v.variantsController.validate()) {
            variantsControllerValid = false
          }
        }
      })
    }
    else {
      if (!this.validateRating()) {
        isValid = false
      }
    }

    if (this.commentEnabled.value) {
      commentIsValid = this.commentController.validate()
    }

    // Add variants controller validation

    const shouldValidateSingleVariantsController
      = this.extraQuestionType.value === EXTRA_QUESTION_TYPE.SINGLE
      && this.variantsController
      && toValue(this.variantsController.enabled)

    if (shouldValidateSingleVariantsController) {
      variantsControllerValid = this.variantsController.validate()
    }

    return isValid && commentIsValid && variantsControllerValid
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {}

    if (this.isVariants.value) {
      data.rating = {}
      this.variants.value.forEach((v) => {
        const ratingValue = toValue(v.rating)
        if (ratingValue > -1) {
          data.rating[v.id] = ratingValue
        }
        else if (toValue(this.skipped)) {
          data.rating[v.id] = -1
        }
        else {
          data.rating[v.id] = ''
        }

        if (!data.detail_item) {
          data.detail_item = []
        }

        if (toValue(v.needExtra) && toValue(v.variantsController?.enabled)) {
          data.detail_item[v.id] = {}
          const variantControllerData = v.variantsController.getData()
          if (variantControllerData.detail_item?.length) {
            variantControllerData.detail_item.forEach((id, index) => {
              data.detail_item[v.id][index] = id
            })
          }
          if (variantControllerData.textAnswer) {
            data.detail_item[v.id].answer = variantControllerData.textAnswer
          }
          if (variantControllerData.self_variant) {
            data.detail_item[v.id].self_variant = variantControllerData.self_variant
          }
        }
      })
    }
    else if (this.rating.value !== undefined || this.rating.value !== -1) {
      data.rating = this.rating.value
    }

    if (this.commentEnabled.value) {
      data.comment = toValue(this.commentController.value)
    }

    const shouldAddVariantsControllerData
      = this.extraQuestionType.value === EXTRA_QUESTION_TYPE.SINGLE
      && toValue(this.variantsController?.enabled)

    if (shouldAddVariantsControllerData) {
      const variantControllerData = this.variantsController.getData()
      if (variantControllerData.detail_item?.length) {
        data.detail_item = variantControllerData.detail_item
      }
      if (variantControllerData.textAnswer) {
        data.self_variant = variantControllerData.textAnswer
      }
      if (variantControllerData.self_variant) {
        data.self_variant = variantControllerData.self_variant
      }
    }

    return data
  }

  getAnswer() {
    let answer = ''

    if (this.isVariants.value) {
      answer = this.variants
        .value
        .map(v => toValue(v.rating) > -1 ? `${v.value}: ${toValue(v.rating)}` : '')
        .filter(Boolean)
        .join(', ')
    }
    else {
      const rating = this.rating.value
      answer = rating < 0 ? '' : `${rating}`
    }

    return answer
  }

  get hasValue() {
    if (this.skip.value && this.skipped.value)
      return true
    if (this.isVariants.value) {
      if (this.previousAnswer?.answer) {
        return true
      }
      const hasPreviousRating = this.variants.value.some(v => v.hasPreviousRating)
      return hasPreviousRating
    }
    const hasPreviousRating = this.previousAnswer?.rating !== undefined

    const hasRating = hasPreviousRating || this.rating.value > -1
    const hasVariantsControllerValue = this.extraQuestionType.value === EXTRA_QUESTION_TYPE.SINGLE && this.variantsController?.hasValue

    return hasRating || hasVariantsControllerValue
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition) && this.isVariants.value)
      return true

    // Get current rating value from reactive rating variable
    if (!this.isVariants.value) {
      const rating = this.rating.value

      switch (condition.behavior) {
        case BEHAVIOR_MISS:
        // Check if question was skipped or has no rating
          return this.skipped.value || rating <= -1

        case BEHAVIOR_SELECT:
        // Check if current rating is in selected variants
          return condition.variants.includes(rating)

        case BEHAVIOR_UNSELECT:
        // Check if current rating is NOT in selected variants
          return !condition.variants.includes(rating)

        case BEHAVIOR_ALWAYS:
          return true

        default:
          return false
      }
    }

    return false
  }

  getStringifiedAnswer() {
    return computed(() => {
      let answer = ''

      if (this.skip.value && this.skipped.value)
        return ''

      if (this.isVariants.value) {
        answer = this.variants
          .value
          .filter(v => toValue(v.rating) > -1)
          .map(v => `${v.text.value}: ${toValue(v.rating)}`)
          .join('; ')
      }
      else {
        answer = this.rating.value > -1 ? `${this.rating.value}` : ''
      }

      return answer
    })
  }

  /**
   * Resolves view logic condition for NPS rating question
   * @param {object} rule - The view logic rule to check
   * @returns {boolean} Whether the condition is met
   */
  resolveViewLogic(rule) {
    const ruleVariants = rule.variants || []
    const skippedRows = (rule.skipped || []).map(row => row.toString())

    if (this.isVariants.value === 1) {
      // Group variants by row (variant id)
      const variantsByRow = ruleVariants.reduce((acc, v) => {
        acc[v.row] = acc[v.row] || []
        acc[v.row].push(v.col)
        return acc
      }, {})

      const variantsSet = [...new Set([...Object.keys(variantsByRow), ...skippedRows])]

      // Check each required variant (row)
      return variantsSet.every((variantId) => {
        // Find the variant in our model
        const variant = this.variants.value.find(v => Number.parseInt(v.id) === Number.parseInt(variantId))
        if (!variant)
          return false

        const rating = toValue(variant.rating)

        // Check if rating matches any allowed rating for this variant
        const allowedRatings = variantsByRow[variantId] || []
        const conditionIsMet = allowedRatings.includes(rating)
        const isUnrequiredWithoutSelected = !toValue(this.isRequired) && rating === -1
        const isSkipped = toValue(this.skipped)
        const isVariantSkipped = toValue(variant.skipped)

        if (isSkipped && skippedRows.length) {
          return true
        }

        const skippedConditionIsMet = skippedRows.includes(variantId)
          && (isSkipped || isVariantSkipped || isUnrequiredWithoutSelected)

        return conditionIsMet || skippedConditionIsMet
      })
    }
    else {
      // For single rating NPS
      const rating = this.rating.value
      if (rating === -1)
        return false
      return ruleVariants.includes(rating)
    }
  }

  get hasGallery() {
    return this.enableGallery.value
  }

  initializeSingleVariantsController(data, variants) {
    const previousSelectedItems = this.previousAnswer?.detail_item || []
    const previousSelfVariantValue = this.previousAnswer?.self_variant
    const previousTextAnswerValue = this.previousAnswer?.textAnswer

    const previousSelectedItemsEntries = Object.entries(previousSelectedItems).filter(([key]) => key !== 'self_variant')

    return new VariantsController({
      enabled: computed(() => {
        if (this.extraQuestionType.value !== EXTRA_QUESTION_TYPE.SINGLE) {
          return false
        }

        const hasRatingInRange = this.variants.value.some((variant) => {
          const needExtra = toValue(variant.needExtra)
          if (!needExtra) {
            return false
          }
          const rating = toValue(variant.rating)
          const enabled = rating >= toValue(this.extraQuestionRateFrom) && rating <= toValue(this.extraQuestionRateTo)
          return enabled
        })

        return hasRatingInRange
      }),
      isRequired: data.extra_required === 1,
      variants,
      variantsWithFiles: data.variants_with_files,
      previousAnswerHasSelfVariant: !!previousSelfVariantValue,
      previousAnswerItems: previousSelectedItemsEntries.map(([_, value]) => value),
      textFieldValue: previousTextAnswerValue || previousSelfVariantValue || '',
      variantsType: data.variantsType || 0,
      textFieldParam: data.textFieldParam || {},
      placeholderText: data.placeholderText,
      selfVariantPlaceholderText: data.placeholderText,
      translations: this.translations,
      skipped: this.skipped,
      label: data.answerText,
      hasCustomField: data.isHaveCustomField,
      dropdown: data.dropdownVariants,
      selfVariantText: data.self_variant_text,
      selfVariantNothing: data.self_variant_nothing === 1,
      selfVariantCommentRequired: data.self_variant_comment_required === 1,
      selfVariantMinLength: data.textFieldParam?.min || 0,
      selfVariantMaxLength: data.textFieldParam?.max || 1000,
      selfVariantFile: data.self_variant_file,
      minChooseVariants: data.min_choose_extra_variants,
      maxChooseVariants: data.max_choose_extra_variants,
    })
  }

  createVariantsControllerForVariant(data, variant) {
    const previousSelectedItems = this.previousAnswer?.detail_item?.[variant.id] || {}

    const previousExtraItemsEntries = Object.entries(previousSelectedItems).filter(([key]) => key !== 'self_variant')
    const previousAnswerSelfVariantValue = previousSelectedItems?.self_variant
    const previousAnswerTextAnswerValue = previousSelectedItems?.answer
    let variants = []
    const extraQuestionType = this.extraQuestionType.value
    if (extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT) {
      // Get variants specific to this variant
      variants = (data.detail_answers || []).filter(v =>
        v.is_deleted === 0
        && v.extra_question === 1
        && v.question_detail_id === variant.id,
      )
    }
    else {
      // For other types, get all extra question variants
      variants = (data.detail_answers || []).filter(v =>
        v.is_deleted === 0
        && v.extra_question === 1,
      )
    }

    const getPlaceholderTextFromMainVariant = (mainVariant) => {
      if (!mainVariant) {
        return data.placeholderText
      }

      return computed(() => {
        const mainVariantTranslations = toValue(this.translations)?.detailLangs?.[mainVariant.id]
        if (mainVariantTranslations?.placeholder_text || mainVariantTranslations?.self_variant_placeholder_text) {
          return mainVariantTranslations.placeholder_text || mainVariantTranslations.self_variant_placeholder_text
        }
        return mainVariant.placeholder_text || data.placeholderText
      })
    }

    const getLabelFromMainVariant = (mainVariant) => {
      if (!mainVariant) {
        return data.answerText
      }

      return computed(() => {
        const mainVariantTranslations = toValue(this.translations)?.detailLangs?.[mainVariant.id]
        if (mainVariantTranslations?.detail_question) {
          return mainVariantTranslations.detail_question
        }

        return mainVariant.detail_question || data.answerText
      })
    }

    /**
     * Получение текста для своего варианта из основного варианта
     * В случаях когда включен разный уточняющий вопрос для каждого варианта
     * Переводы для уточняющего вопроса берутся из основного варианта
     * @param {object} mainVariant - Основной вариант
     * @returns {string | import('vue').ComputedRef<string>} - Текст для своего варианта
     */
    const getSelfVariantTextFromMainVariant = (mainVariant) => {
      if (!mainVariant) {
        return data.self_variant_text
      }
      return computed(() => {
        const mainVariantTranslations = toValue(this.translations)?.detailLangs?.[mainVariant.id]
        if (mainVariantTranslations?.self_variant_text) {
          return mainVariantTranslations.self_variant_text
        }

        return mainVariant.self_variant_text || data.self_variant_text
      })
    }

    /**
     * Получение placeholder для своего варианта из основного варианта
     * В случаях когда включен разный уточняющий вопрос для каждого варианта
     * Переводы для уточняющего вопроса берутся из основного варианта
     * @param {object} mainVariant - Основной вариант
     * @returns {string | import('vue').ComputedRef<string>} - Placeholder для своего варианта
     */
    const getSelfVariantPlaceholderTextFromMainVariant = (mainVariant) => {
      if (!mainVariant) {
        return data.self_variant_placeholder_text
      }
      return computed(() => {
        const mainVariantTranslations = toValue(this.translations)?.detailLangs?.[mainVariant.id]
        if (mainVariantTranslations?.self_variant_placeholder_text) {
          return mainVariantTranslations.self_variant_placeholder_text
        }
        return mainVariant.self_variant_placeholder_text || data.self_variant_placeholder_text
      })
    }

    const variantsType = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.variants_element_type : data.variantsType
    const variantsWithFiles = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.variants_with_files : data.variants_with_files
    const placeholderText = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? getPlaceholderTextFromMainVariant(variant) : data.placeholderText
    const selfVariantText = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? getSelfVariantTextFromMainVariant(variant) : data.self_variant_text
    const selfVariantPlaceholderText = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT
      ? getSelfVariantPlaceholderTextFromMainVariant(variant)
      : data.self_variant_placeholder_text || data.placeholderText
    const label = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? getLabelFromMainVariant(variant) : data.answerText
    const minChooseVariants = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.min_choose_extra_variants : data.min_choose_extra_variants
    const maxChooseVariants = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.max_choose_extra_variants : data.max_choose_extra_variants
    const textFieldParam = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT
      ? {
          min: variant.text_variant_minlength || 0,
          max: variant.text_variant_maxlength || 250,
        }
      : {
          min: data.textFieldParam?.min || 0,
          max: data.textFieldParam?.max || 250,
        }

    const selfVariantMinLength = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.self_variant_minlength || 0 : data.textFieldParam?.min || 0
    const selfVariantMaxLength = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.self_variant_maxlength || 250 : data.textFieldParam?.max || 250

    const isRequired = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.extra_required === 1 : data.extra_required === 1
    const hasCustomField = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT ? variant.is_self_answer : variant.need_extra === 1

    return new VariantsController({
      enabled: computed(() => {
        const extraQuestionRateFrom = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT
          ? toValue(variant.extraQuestionRateFrom)
          : toValue(this.extraQuestionRateFrom)
        const extraQuestionRateTo = extraQuestionType === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT
          ? toValue(variant.extraQuestionRateTo)
          : toValue(this.extraQuestionRateTo)

        if (!toValue(variant.needExtra)) {
          return false
        }

        if (this.extraQuestionType.value !== EXTRA_QUESTION_TYPE.COMMON_PER_VARIANT
          && this.extraQuestionType.value !== EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT) {
          return false
        }

        const rating = toValue(variant.rating)

        return rating !== -1 && rating >= extraQuestionRateFrom && rating <= extraQuestionRateTo
      }),
      isRequired,
      variants,
      variantsWithFiles,
      previousAnswerHasSelfVariant: !!previousAnswerSelfVariantValue,
      previousAnswerItems: previousExtraItemsEntries.map(([_, value]) => value),
      textFieldValue: previousAnswerTextAnswerValue || previousAnswerSelfVariantValue || '',
      variantsType,
      textFieldParam,
      placeholderText,
      translations: this.translations,
      skipped: this.skipped,
      label,
      hasCustomField,
      selfVariantText,
      selfVariantNothing: variant.variants_element_type === 2,
      selfVariantCommentRequired: variant.comment_required === 1,
      selfVariantMinLength,
      selfVariantMaxLength,
      selfVariantFile: variant.selfVariantFile || data.self_variant_file,
      selfVariantPlaceholderText,
      minChooseVariants,
      maxChooseVariants,
    })
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update NPS rating settings
    if (previewData.npsRatingSetting) {
      this.npsDesign.value = previewData.npsRatingSetting.design
      this.npsStartColor.value = previewData.npsRatingSetting.start_point_color
      this.npsEndColor.value = previewData.npsRatingSetting.end_point_color
      this.npsStartLabel.value = previewData.npsRatingSetting.start_label
      this.npsEndLabel.value = previewData.npsRatingSetting.end_label
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }
    if (!this.skip.value) {
      this.skipped.value = false
    }
    if (previewData.skip_text !== undefined) {
      this.skipText.value = previewData.skip_text
    }

    if (previewData.from_one !== undefined) {
      this.fromOne.value = previewData.from_one
    }

    if (previewData.set_variants !== undefined) {
      this.isVariants.value = previewData.set_variants
    }

    if (previewData.random_variants_order !== undefined) {
      this.randomVariantsOrder.value = previewData.random_variants_order
    }

    // Update extra question type and related settings
    if (previewData.extra_question_type !== undefined) {
      this._extraQuestionType.value = previewData.extra_question_type
    }

    if (previewData.clarifyingQuestion?.forRates !== undefined) {
      this._extraQuestionRateFrom.value = previewData.clarifyingQuestion.forRates[0]
      this._extraQuestionRateTo.value = previewData.clarifyingQuestion.forRates[1]
    }

    // Split variants into main variants and extra question variants
    const clarifyingQuestion = previewData.clarifyingQuestion || {}
    if (previewData.variants) {
      // Update extra question variants
      const extraVariants = clarifyingQuestion.variants || []

      this.extraQuestionVariants.value = extraVariants

      // Update main variants while preserving state
      const mainVariants = previewData.variants
        .map(v => ({ ...v, id: v.persistentId || v.id }))

      if (this.isVariants.value) {
        // Update existing variants while preserving their state
        const updatedVariants = mainVariants.map((newVariant, index) => {
          // Try to find matching existing variant
          const existingVariant = this.variants.value.find(v => v.id === newVariant.id)

          if (existingVariant) {
            // Update text and needExtra while preserving state
            existingVariant.text.value = newVariant.variant || newVariant.value || newVariant.question
            existingVariant.needExtra.value = newVariant.need_extra
            existingVariant.position = newVariant.position || index

            existingVariant.extraQuestionRateFrom.value = newVariant.extra_question_rate_from || clarifyingQuestion.forRates?.[0]
            existingVariant.extraQuestionRateTo.value = newVariant.extra_question_rate_to || clarifyingQuestion.forRates?.[1]

            // Update variant's controller if it exists
            if (existingVariant.variantsController) {
              const variants = this.extraQuestionType.value === EXTRA_QUESTION_TYPE.DIFFERENT_PER_VARIANT
                ? newVariant.detail_question_options || []
                : this.extraQuestionVariants.value

              let controllerUpdateData
              if (this.extraQuestionType.value === EXTRA_QUESTION_TYPE.COMMON_PER_VARIANT) {
                controllerUpdateData = {
                  isRequired: clarifyingQuestion.required,
                  variants,
                  variantsType: clarifyingQuestion.variantsType,
                  textFieldParam: {
                    min: clarifyingQuestion.customAnswerRange?.[0] || 0,
                    max: clarifyingQuestion.customAnswerRange?.[1] || 250,
                  },
                  placeholderText: clarifyingQuestion.customAnswerPlaceholder || '',
                  label: clarifyingQuestion.text || '',
                  hasCustomField: clarifyingQuestion.customAnswerEnabled,
                  selfVariantText: clarifyingQuestion.customAnswerLabel || '',
                  selfVariantPlaceholderText: clarifyingQuestion.customAnswerPlaceholder || '',
                  selfVariantNothing: clarifyingQuestion.self_variant_nothing === 1,
                  selfVariantCommentRequired: clarifyingQuestion.customAnswerCommentRequired === 1,
                  selfVariantMinLength: clarifyingQuestion.customAnswerRange?.[0] || 0,
                  selfVariantMaxLength: clarifyingQuestion.customAnswerRange?.[1] || 250,
                  selfVariantFile: clarifyingQuestion.customAnswerFile,
                  minChooseVariants: Number.parseInt(clarifyingQuestion.minChooseVariants),
                  maxChooseVariants: Number.parseInt(clarifyingQuestion.maxChooseVariants),
                  variantsWithFiles: clarifyingQuestion.enableFile,
                }
              }
              else {
                const textVariantMinLength = newVariant.text_variant_minlength || 0
                const textVariantMaxLength = newVariant.text_variant_maxlength || 250
                const selfVariantMinLength = newVariant.self_variant_minlength || 0
                const selfVariantMaxLength = newVariant.self_variant_maxlength || 250

                controllerUpdateData = {
                  isRequired: newVariant.extra_required === 1,
                  variants,
                  variantsType: newVariant.variants_element_type || 0,
                  textFieldParam: {
                    min: textVariantMinLength,
                    max: textVariantMaxLength,
                  },
                  placeholderText: newVariant.placeholder_text || '',
                  label: newVariant.detail_question || '',
                  hasCustomField: !!newVariant.is_self_answer,
                  selfVariantText: newVariant.self_variant_text || '',
                  selfVariantNothing: newVariant.variants_element_type === 2,
                  selfVariantPlaceholderText: newVariant.self_variant_placeholder_text || '',
                  selfVariantCommentRequired: newVariant.comment_required === 1,
                  selfVariantMinLength,
                  selfVariantMaxLength,
                  selfVariantFile: newVariant.selfVariantFile,
                  minChooseVariants: newVariant.min_choose_extra_variants,
                  maxChooseVariants: newVariant.max_choose_extra_variants,
                  variantsWithFiles: newVariant.variants_with_files || 0,
                }
              }

              existingVariant.variantsController.updateFromPreview(controllerUpdateData)
            }

            return existingVariant
          }

          // If no existing variant found, create a new one
          return this.prepareVariant(newVariant, newVariant.position || 0)
        })

        this.variants.value = this.randomVariantsOrder.value
          ? shuffle(updatedVariants)
          : updatedVariants.sort((a, b) => a.position - b.position)
      }
    }

    // Update single variants controller if needed
    if (this.extraQuestionType.value === EXTRA_QUESTION_TYPE.SINGLE && this.variantsController) {
      this.variantsController.updateFromPreview({

        isRequired: clarifyingQuestion.required,
        variants: this.extraQuestionVariants.value,
        variantsType: clarifyingQuestion.variantsType,
        textFieldParam: {
          min: clarifyingQuestion.customAnswerRange?.[0] || 0,
          max: clarifyingQuestion.customAnswerRange?.[1] || 250,
        },
        placeholderText: clarifyingQuestion.customAnswerPlaceholder || previewData.placeholderText || '',
        label: clarifyingQuestion.text,
        hasCustomField: clarifyingQuestion.customAnswerEnabled,
        dropdown: clarifyingQuestion.dropdownVariants,
        variantsWithFiles: clarifyingQuestion.enableFile,
        selfVariantText: clarifyingQuestion.customAnswerLabel,
        selfVariantPlaceholderText: clarifyingQuestion.customAnswerPlaceholder,
        selfVariantMinLength: clarifyingQuestion.customAnswerRange?.[0] || 0,
        selfVariantMaxLength: clarifyingQuestion.customAnswerRange?.[1] || 250,
        selfVariantFile: clarifyingQuestion.customAnswerFile,
        minChooseVariants: Number.parseInt(clarifyingQuestion.minСhooseVariants),
        maxChooseVariants: Number.parseInt(clarifyingQuestion.maxСhooseVariants),
      })
    }

    // Update comment if enabled
    if (previewData.commentEnabled !== undefined) {
      this.commentEnabled.value = previewData.commentEnabled
    }
    if (this.commentEnabled.value && this.commentController) {
      this.commentController.updateFromPreview({
        enabled: toValue(this.commentEnabled),
        required: previewData.comment_required || false,
        title: previewData.comment_label || '',
        placeholderText: previewData.placeholderText || '',
        textFieldParam: {
          min: previewData.textFieldParam?.min || 0,
          max: previewData.textFieldParam?.max || 1000,
        },
      })
    }

    // Update gallery if enabled
    if (previewData.enableGallery !== undefined) {
      this.enableGallery.value = previewData.enableGallery
    }

    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: previewData.gallery || [],
      })
    }
  }
}
