import { BEHAVIOR_ALWAYS } from '@shared/constants/logic'
import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { BaseQuestion } from './BaseQuestion'

export class PriorityQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    this.translationsStore = useTranslationsStore()
    this.t = this.translationsStore.t
    this.selectedLang = this.translationsStore.selectedLang
    this.data = data
    this.isRequired = ref(!!data.isRequired)
    this.reorderRequired = ref(!!data.reorder_required)
    this.randomVariantsOrder = ref(!!data.random_variants_order)
    this.commentEnabled = ref(!!data.comment_enabled)
    this.skip = ref(!!data.skip)

    this.description = computed(() => {
      return this.translations.value.description || data.description
    })

    this.error = ref(null)

    this.previousOrderedItems = (this.previousAnswer?.detail_item || [])
      .map(id => Number.parseInt(id))

    this.hasPreviousAnswers = this.previousOrderedItems.length > 0

    this.detailAnswers = (data.detail_answers || []).map((item) => {
      const prevIndex = this.previousOrderedItems.indexOf(item.id)
      return {
        ...item,
        position: prevIndex === -1 ? item.position : prevIndex,
      }
    })

    this.items = ref(this.hasDonor ? [] : this.prepareItems(this.detailAnswers))

    if (this.isPreviewMode && this.items.value.length === 0) {
      this.items.value = this.prepareItems([{ id: 'fake-id', name: 'fake-name' }])
    }

    this.initialOrder = ref(this.hasDonor ? [] : this.items.value.map(item => item.id))

    this.itemsInitialized = !this.hasPreviousAnswers
    this.userChangedOrder = ref(!!this.hasPreviousAnswers)

    this._showItemNumber = ref(null)
    this.showItemNumber = computed(() => {
      if (this._showItemNumber.value === false) {
        return false
      }
      if (this.items.value.length === 1) {
        return false
      }
      const touchedAndReorderNotRequired = this.touched.value && !toValue(this.reorderRequired)
      return this.userChangedOrder.value || touchedAndReorderNotRequired
    })

    // Watch donor variants changes
    watch(this.selectedDonorVariants, (newDonorVariants) => {
      if (!this.hasDonor)
        return

      const filteredDetailAnswers = []

      newDonorVariants.forEach((v) => {
        if (v.id === 'is_self_answer') {
          // Обработка варианта "Свой вариант"

          const selfVariantFromRecipient = this.detailAnswers
            .find(answer => this.isVariantRecipientSelfAnswer(answer))

          if (!selfVariantFromRecipient) {
            return
          }

          // Если респондент выбрал "Свой вариант" у донора, то нам нужно
          // в качестве текста варианта использовать значение текстового поля
          selfVariantFromRecipient.label = computed(() => toValue(v.alternativeVariantLabel))
          selfVariantFromRecipient.id = '-1'
          filteredDetailAnswers.push(selfVariantFromRecipient)
          return
        }

        const donorVariant = this.detailAnswers
          .find((answer) => {
            // @NOTE: Если донор является классификатором, то приходит dictionary_element_id
            // Если донор является вопросом "Вариант", то приходит question_detail_id
            const donorVariantId = answer.dictionary_element_id || answer.question_detail_id
            return donorVariantId === v.id
          })

        if (donorVariant) {
          donorVariant.id = v.id
          donorVariant.question = this.getRecipientVariantName(donorVariant)
          filteredDetailAnswers.push(donorVariant)
        }
      })

      if (this.itemsInitialized) {
        this.userChangedOrder.value = false
      }

      this._showItemNumber.value = false

      this.items.value = this.prepareItems(filteredDetailAnswers)
      this.initialOrder.value = (this.items.value || []).map(item => item.id)
    }, { immediate: true })

    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: !!data.comment_required,
      value: this.previousAnswer?.self_variant || '',
      placeholderText: data.placeholderText,
      minLength: data.textFieldParam?.min || 0,
      maxLength: data.textFieldParam?.max || 1000,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.isValid = computed(() => {
      return this.validate()
    })

    this.touched = ref(false)

    this.blocked = computed(() => {
      return toValue(this.touched) && !toValue(this.isValid)
    })

    /**
     * Проверка, не изменился ли порядок вариантов в вопросе
     */
    this.orderUnchanged = computed(() => {
      const isInitialOrderEmpty = this.initialOrder.value.length === 0
      const isItemsEmpty = this.items.value.length === 0
      if (isInitialOrderEmpty || isItemsEmpty)
        return true

      return this.items.value.every((item, index) => item.id === this.initialOrder.value[index])
    })

    watch(this.items, () => {
      // this.validate()
    })

    watch(this.orderUnchanged, (v) => {
      if (!v) {
        // Если порядок изменился, то отмечаем, что пользователь менял порядок
        this.userChangedOrder.value = true
      }
    })

    watch(this.userChangedOrder, (hasChanged) => {
      if (hasChanged && this.itemsInitialized) {
        // Если пользователь менял порядок, то отмечаем, что вопрос был взаимодействован
        this._showItemNumber.value = null
        this.markInteracted()
      }
    }, { immediate: true })
  }

  prepareItems(items) {
    const randomVariantsOrder = this.randomVariantsOrder.value || this.hasPoints.value
    const sortedItems = randomVariantsOrder
      ? shuffle(items)
      : items.sort((a, b) => a.position - b.position)
    const preparedItems = sortedItems
      .map((item) => {
        item.name = computed(() => {
          const label = toValue(item.label)
          if (label) {
            return label
          }
          const itemId = item.id
          const detailLang = this.translations.value?.detailLangs?.[itemId]
          if (detailLang && detailLang.question) {
            return detailLang.question
          }
          return item.variant || item.question
        })

        return item
      })

    return preparedItems
  }

  resetFields() {
    this.touched.value = false
    this.initialOrder.value = this.items.value.map(item => item.id)

    const itemsLength = this.items.value.length

    if (itemsLength <= 1) {
      // Если вариантов меньше или равно 1, то считаем, что пользователь менял порядок
      // Иначе не пропустит дальше из-за ошибки валидации
      this.userChangedOrder.value = true
    }
    else if (itemsLength > 1 && !this.itemsInitialized) {
      // Если вариантов больше 1, и нет предыдущих ответов,
      // то считаем, что пользователь не менял порядок
      this.userChangedOrder.value = true
    }
    else {
      this.userChangedOrder.value = false
    }
  }

  setAllAsTouched() {
    this.touched.value = true
    if (toValue(this.commentEnabled)) {
      this.commentController.touched.value = true
    }
  }

  validate() {
    this.error.value = null
    let valid = true
    let commentValid = true
    const isRequired = toValue(this.isRequired)

    if (!this.touched.value)
      return true

    if (isRequired && toValue(this.reorderRequired) && !this.userChangedOrder.value) {
      this.error.value = this.t('Нужно изменить порядок')
      valid = false
    }

    if (toValue(this.commentEnabled)) {
      commentValid = this.commentController.validate()
    }

    return valid && commentValid
  }

  getData() {
    const data = {
      answer: this.items.value.map((i) => {
        if (i.id === 'is_self_answer') {
          return '-1'
        }
        return i.id
      }),
    }

    if (toValue(this.commentEnabled)) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  get hasValue() {
    return this.hasPreviousAnswers
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (toValue(this.skip) && this.skipped.value)
        return ''

      return this.items.value.map((item, index) => `${index + 1}. ${toValue(item.name)}`).join('; ')
    })
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Priority questions only support BEHAVIOR_ALWAYS
    return condition.behavior === BEHAVIOR_ALWAYS
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update reorder required field
    if (previewData.reorder_required !== undefined) {
      this.reorderRequired.value = previewData.reorder_required
    }

    // Update random variants order
    if (previewData.random_variants_order !== undefined) {
      this.randomVariantsOrder.value = previewData.random_variants_order
    }

    this.randomVariantsOrder.value = previewData.randomOrder

    // Update detail answers and items
    if (previewData.variants) {
      const detailAnswers = previewData.variants.map((item, index) => ({
        ...item,
        id: item.persistentId || item.id,
        question: item.value,
        position: item.position || index,
      }))

      this.items.value = this.prepareItems(detailAnswers)
      this.initialOrder.value = this.items.value.map(item => item.id)

      // Reset user interaction state
      this.itemsInitialized = true
      this.userChangedOrder.value = false
    }

    // Update comment if enabled
    if (previewData.commentEnabled !== undefined) {
      this.commentEnabled.value = previewData.commentEnabled
    }

    this.commentController.updateFromPreview({
      enabled: toValue(this.commentEnabled),
      required: !!previewData.comment_required,
      title: previewData.comment_label || 'Ваш комментарий',
      placeholderText: previewData.placeholderText || '',
      textFieldParam: {
        min: previewData.textFieldParam?.min || 0,
        max: previewData.textFieldParam?.max || 1000,
      },
    })

    // Reset fields to ensure proper state
    this.resetFields()
  }
}
