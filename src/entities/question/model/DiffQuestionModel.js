import { doubleGradientCss } from '@shared/helpers/color'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import { computed, nextTick, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'

const FORM_TYPES = {
  RECT: 'rect',
  CIRCLE: 'circle',
}

export class DiffQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const tabletStore = useTabletStore()
    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    this.skip = ref(data.skip)

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => this.translations.value.skip_text || this._skipText.value || toValue(this.t('Не готов(а) оценить')))

    this.canMoveToNextQuestion = ref(false)

    this.skipped = ref(this.previousAnswer?.skipped === 1)
    this.skipVariant = ref(data.skip_variant === 1)

    this.previousScaleItems = null

    try {
      this.previousScaleItems = JSON.parse(this.previousAnswer?.answer || '{}')
    }
    catch {
      this.previousScaleItems = {}
    }

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: toValue(this.enableGallery),
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this.semDifSetting = ref(data.semDifSetting || {})
    this.form = computed(() => this.semDifSetting.value.form || FORM_TYPES.RECT)
    this.startPointColor = computed(() => this.semDifSetting.value.start_point_color || '#73808D')
    this.endPointColor = computed(() => this.semDifSetting.value.end_point_color || '#3F65F1')

    this.labelsFromTranslation = computed(() => {
      const translations = toValue(this.translations)
      const labelsFromTranslation = JSON.parse(translations.labels || '{}')
      return labelsFromTranslation || {}
    })

    this.differentialRows = shallowRef((data.differentialRows || []).map((row) => {
      return this.createRow(row)
    }))

    this.differentialRows.value.sort((a, b) => toValue(a.position) - toValue(b.position))

    this.error = computed(() => {
      return toValue(this.differentialRows).find(row => row.error.value)?.error.value
    })

    this.rowsCount = computed(() => this.differentialRows.value.length)

    this.isRequired = ref(data.isRequired)

    this.touched = ref(false)

    this.gradient = computed(() => doubleGradientCss({
      start: toValue(this.startPointColor),
      end: toValue(this.endPointColor),
      neutral: 'rgb(207, 216, 220)',
      count: 5,
      center: 3,
    }))

    this.commentEnabled = ref(data.comment_enabled === 1)

    this.differentialRows.value.forEach((row) => {
      watch(row.rating, (newValue) => {
        if (newValue > -1) {
          this.skipped.value = false
          this.markInteracted()
          if (this.differentialRows.value.length === 1 && !tabletStore.isTabletMode) {
            nextTick().then(() => {
              this.canMoveToNextQuestion.value = true
            })
          }
        }
        this.validateRow(row)
      })
    })

    this.isCommentRequired = ref(data.comment_required === 1)
    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: toValue(this.isCommentRequired),
      skipped: this.skipped,
      value: this.previousAnswer?.self_variant || '',
      placeholderText: data.placeholderText,
      minLength: toValue(this.textFieldParam).min || 0,
      maxLength: toValue(this.textFieldParam).max || 250,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.isValid = computed(() => {
      if (this.skip.value && this.skipped.value)
        return true
      if (toValue(this.isRequired)) {
        if (this.differentialRows.value.some(row => toValue(row.rating) === -1))
          return false
      }
      const isCommentValid = toValue(this.commentController.isValid)
      if (this.commentEnabled.value && !isCommentValid)
        return false
      return true
    })

    this.blocked = computed(() => {
      const someRowHasError = this.differentialRows.value.some(row => row.error.value)
      return someRowHasError || (this.touched.value && !this.isValid.value)
    })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })
  }

  resetFields() {
    this.touched.value = false
    this.differentialRows.value.forEach((row) => {
      row.rating.value = -1
      row.error.value = null
      row.touched.value = false
    })
    this.commentController.value.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null
  }

  setAllAsTouched() {
    this.touched.value = true
    this.differentialRows.value.forEach(row => row.touched.value = true)
    if (this.commentEnabled.value) {
      this.commentController.touched.value = true
    }
  }

  getErrorMessage() {
    if (this.rowsCount.value === 1) {
      return this.t('Нужно поставить оценку')
    }
    return this.t('Нужно поставить все оценки')
  }

  validateRow(row) {
    if (!row.touched.value)
      return true

    if (!toValue(this.isRequired)) {
      row.error.value = null
      return true
    }

    if (this.skip.value && this.skipped.value) {
      row.error.value = null
      return true
    }

    if (row.rating.value === -1) {
      row.error.value = this.getErrorMessage()
      this.error.value = this.getErrorMessage()
      return false
    }

    row.error.value = null
    return true
  }

  validate() {
    this.error.value = null
    this.differentialRows.value.forEach(row => this.validateRow(row))
    if (this.commentEnabled.value)
      this.commentController.validate()

    return this.isValid.value
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {
      answer: {},
    }

    this.differentialRows.value.forEach((row) => {
      const rating = toValue(row.rating)
      if (rating > -1) {
        data.answer[row.id] = rating
      }
      else {
        data.answer[row.id] = '-1'
      }
    })

    if (this.commentEnabled.value) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    return this.differentialRows
      .value
      .map(row => `${row.start_label || ''} - ${toValue(row.rating)} - ${row.end_label || ''}`)
      .join(', ')
  }

  get hasValue() {
    if (this.skip.value && this.skipped.value)
      return true
    if (!this.isValid.value)
      return false
    return toValue(this.differentialRows).some(row => row.hasPreviousRating || toValue(row.rating) > -1)
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Semantic Differential question only supports BEHAVIOR_ALWAYS
    return false
  }

  get hasGallery() {
    return this.enableGallery.value
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      const answers = this.differentialRows
        .value
        .map((row) => {
          const rating = toValue(row.rating)
          if (rating === -1)
            return ''

          const startLabel = toValue(row.startLabel)
          const endLabel = toValue(row.endLabel)

          if (toValue(this.differentialRows).length === 1 && !startLabel && !endLabel) {
            return `${rating}`
          }

          if (startLabel && endLabel) {
            return `${startLabel} ${rating} ${endLabel}`
          }
          if (startLabel) {
            return `${startLabel} ${rating}`
          }
          if (endLabel) {
            return `${rating} ${endLabel}`
          }

          return `${rating}`
        })

      return answers.join('; ')
    })
  }

  updateFromPreview(previewData) {
    const isRequiredChanged = this.isRequired.value !== previewData.isRequired

    // Call parent update first
    super.updateFromPreview(previewData)

    if (isRequiredChanged) {
      this.touched.value = false
      this.skipped.value = false
      this.differentialRows.value.forEach(row => row.touched.value = false)
      this.commentController.touched.value = false
      this.commentController.error.value = null
    }

    // Update semantic differential settings
    if (previewData.semDifSetting) {
      const settingsChanged = JSON.stringify(previewData.semDifSetting) !== JSON.stringify({
        form: toValue(this.form),
        start_point_color: toValue(this.startPointColor),
        end_point_color: toValue(this.endPointColor),
      })

      if (settingsChanged) {
        this.semDifSetting.value = previewData.semDifSetting
      }
    }

    // Update differential rows with state preservation
    if (previewData.differentialRows) {
      const updatedRows = previewData.differentialRows
        .map((newRow, index) => {
          // Try to find matching existing row
          const existingRow = this.differentialRows.value.find(row => row.id === (newRow.persistentId || newRow.id))

          if (existingRow) {
          // Update labels while preserving state
            existingRow.startLabel.value = newRow.start_label
            existingRow.endLabel.value = newRow.end_label
            existingRow.position.value = index
            return existingRow
          }

          newRow.position = index
          newRow.id = newRow.persistentId || newRow.id

          // If no matching row found, create a new one
          return this.createRow(newRow)
        })

      this.differentialRows.value = updatedRows
    }

    const skipIsChanged = this.skip.value !== previewData.skip

    if (skipIsChanged) {
      this.skipped.value = false
    }

    this.skip.value = previewData.skip
    this._skipText.value = previewData.skip_text

    // Update comment-related fields
    this.commentEnabled.value = previewData.isHaveComment
    this.isCommentRequired.value = previewData.comment_required

    // Update comment controller
    this.commentController.updateFromPreview({
      enabled: previewData.isHaveComment,
      required: previewData.comment_required,
      title: previewData.comment_label || this.t('Ваш комментарий'),
      placeholderText: previewData.placeholderText || '',
      textFieldParam: previewData.textFieldParam || {},
    })

    // Update gallery-related fields
    this.enableGallery.value = previewData.enableGallery

    this.galleryController.updateFromPreview({
      enabled: previewData.enableGallery,
      gallery: previewData.gallery || [],
    })
  }

  createRow(rowData) {
    const previousRating = Number.parseInt(this.previousScaleItems[rowData.id])
    const hasPreviousRating = !Number.isNaN(previousRating)
    const _startLabel = ref(rowData.start_label)
    const _endLabel = ref(rowData.end_label)
    const _position = ref(rowData.position)
    return {
      ...rowData,
      hasPreviousRating,
      rating: ref(hasPreviousRating ? previousRating : -1),
      error: ref(null),
      touched: ref(false),
      startLabel: computed({
        get: () => {
          return this.labelsFromTranslation.value[rowData.id]?.[0] || _startLabel.value
        },
        set: value => _startLabel.value = value,
      }),
      endLabel: computed({
        get: () => {
          return this.labelsFromTranslation.value[rowData.id]?.[1] || _endLabel.value
        },
        set: value => _endLabel.value = value,
      }),
      position: computed({
        get: () => {
          return _position.value
        },
        set: value => _position.value = value,
      }),
    }
  }
}
