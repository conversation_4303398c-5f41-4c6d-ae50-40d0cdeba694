import { DateFormatter, getLocalTimeZone, parseDate, today } from '@internationalized/date'
import MaskTypes from '@shared/constants/maskTypes'
import { EMAIL_REGEX, isValidHttpUrl, PHONE_REGEX, TIME_REGEX } from '@shared/constants/regex'
import { declOfNum } from '@shared/helpers/string'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { getDaysInMonth, isBeforeOrSame } from 'radix-vue/date'
import { computed, ref, shallowRef, toValue, triggerRef, watch } from 'vue'

export class MaskedFieldModel {
  constructor(data) {
    this.id = data.id
    this.type = ref(data.type || MaskTypes.NoMask)
    this.translations = data.translations
    this._label = ref(data.fieldConfig.label)
    this.label = computed(() => toValue(this.translations)?.name || toValue(this._label))
    this.previousAnswer = data.previousAnswer

    this.translationsLabels = computed(() => {
      const translations = toValue(this.translations)
      if (!translations) {
        return {}
      }

      const nameObject = translations.name
      const placeholderObject = translations.placeholder
      const placeholderTextObject = translations.placeholder_text
      let result = {}
      if (nameObject || placeholderTextObject || placeholderObject) {
        result = {
          name: nameObject || {},
          placeholder_text: placeholderTextObject || {},
          placeholder: placeholderObject || {},
        }
      }
      const labelsJson = translations.labels
      if (labelsJson && typeof labelsJson === 'string') {
        try {
          result.labels = JSON.parse(labelsJson)
        }
        catch {
          result.labels = {}
        }
      }
      return result
    })

    const fieldConfig = data.fieldConfig || {}
    this.config = data.config || {}

    this.translationsStore = useTranslationsStore()
    this.t = this.translationsStore.t

    const _fieldConfigPlaceholderText = ref(fieldConfig.placeholderText || '')
    this.fieldConfig = shallowRef({
      value: this.previousAnswer?.value || '',
      minLength: fieldConfig.minLength || 0,
      maxLength: fieldConfig.maxLength || 999,
      placeholderText: computed({
        get: () => {
          const translations = toValue(this.translations)
          return translations?.placeholder_text || _fieldConfigPlaceholderText.value || ''
        },
        set: value => _fieldConfigPlaceholderText.value = value,
      }),
      required: !!fieldConfig.required,
      multiline: fieldConfig.multiline,
      label: fieldConfig.label,
    })

    const namePlaceholderText = ref(this.config.name?.placeholderText || '')
    const nameRequired = this.config.name?.required === 'false' ? false : !!this.config.name?.required
    const nameVisible = this.config.name?.visible && this.config.name?.visible !== 'false'
    this.nameConfig = ref({
      visible: nameVisible,
      value: this.previousAnswer?.name || '',
      required: nameRequired,
      minLength: this.config.name?.minLength || 0,
      maxLength: this.config.name?.maxLength || 999,
      placeholderText: computed({
        get: () => {
          const translations = toValue(this.translationsLabels)

          const placeholderText = translations?.placeholder?.name
            || translations?.labels?.name?.placeholder
            || namePlaceholderText.value
          return placeholderText
        },
        set: (value) => {
          namePlaceholderText.value = value
        },
      }),
      label: computed(() => {
        const translations = toValue(this.translationsLabels)
        const label = translations.name?.name
          || translations.name?.name
          || translations.labels?.name?.name
          || 'Имя'
        return label
      }),
    })

    const surnamePlaceholderText = ref(this.config.surname?.placeholderText || '')
    const surnameRequired = this.config.surname?.required === 'false' ? false : !!this.config.surname?.required
    const surnameVisible = this.config.surname?.visible && this.config.surname?.visible !== 'false'
    this.surnameConfig = ref({
      visible: surnameVisible,
      value: this.previousAnswer?.surname || '',
      required: surnameRequired,
      minLength: this.config.surname?.minLength || 0,
      maxLength: this.config.surname?.maxLength || 999,
      placeholderText: computed({
        get: () => {
          const translations = toValue(this.translationsLabels)
          const placeholderText = translations?.placeholder?.surname
            || translations?.labels?.surname?.placeholder
            || surnamePlaceholderText.value
          return placeholderText
        },
        set: (value) => {
          surnamePlaceholderText.value = value
        },
      }),
      label: computed(() => {
        const translations = toValue(this.translationsLabels)
        const label = translations.name?.surname
          || translations.name?.surname
          || translations.labels?.surname?.name
          || 'Фамилия'
        return label
      }),
    })

    const patronymicPlaceholderText = ref(this.config.patronymic?.placeholderText || '')
    const patronymicRequired = this.config.patronymic?.required === 'false' ? false : !!this.config.patronymic?.required
    const patronymicVisible = this.config.patronymic?.visible && this.config.patronymic?.visible !== 'false'
    this.patronymicConfig = ref({
      visible: patronymicVisible,
      value: this.previousAnswer?.patronymic || '',
      required: patronymicRequired,
      minLength: this.config.patronymic?.minLength || 0,
      maxLength: this.config.patronymic?.maxLength || 999,
      placeholderText: computed({
        get: () => {
          const translations = toValue(this.translationsLabels)
          const placeholderText = translations?.placeholder?.patronymic
            || translations?.labels?.patronymic?.placeholder
            || patronymicPlaceholderText.value
          return placeholderText
        },
        set: (value) => {
          patronymicPlaceholderText.value = value
        },
      }),
      label: computed(() => {
        const translations = toValue(this.translationsLabels)
        const label = translations.name?.patronymic
          || translations.name?.patronymic
          || translations.labels?.patronymic?.name
          || 'Отчество'
        return label
      }),
    })

    this.touched = ref(false)
    this.nameTouched = ref(false)
    this.surnameTouched = ref(false)
    this.patronymicTouched = ref(false)

    this.error = ref(null)
    this.dateError = ref(null)
    this.timeError = ref(null)
    this.dayMonthError = ref(null)
    this.nameError = ref(null)
    this.surnameError = ref(null)
    this.patronymicError = ref(null)
    this.date = ref(null)

    if (toValue(this.fieldConfig).multiline)
      this.type.value = MaskTypes.NoMask

    this.field = ref(this.previousAnswer?.date || this.previousAnswer?.value || '')

    this.time = ref(this.previousAnswer?.time || this.previousAnswer?.value || '')
    this.timeError = ref(null)

    this.isNameMask = computed(() => toValue(this.type) === MaskTypes.Name)
    this.isPeriodMask = computed(() => toValue(this.type) === MaskTypes.Period)
    this.isDateMask = computed(() => toValue(this.type) === MaskTypes.Date)
    this.isDateMonthMask = computed(() => toValue(this.type) === MaskTypes.DateMonth)
    this.isTimeMask = computed(() => toValue(this.type) === MaskTypes.Time)
    this.isDateTimeMask = computed(() => toValue(this.type) === MaskTypes.DateTime)
    this.isDateMonthTimeMask = computed(() => toValue(this.type) === MaskTypes.DateMonthTime)
    this.isRequired = toValue(this.fieldConfig).required

    this.dateFormatter = new DateFormatter('ru-RU', {
      dateStyle: 'short',
      separator: '.',
    })

    this.from = ref('')
    this.to = ref('')
    this.period = ref('')

    // we can get previous answer in period mask in format "01.01.2024 - 01.01.2025"
    // so we need to parse it to two dates
    this.fromText = ref(this.previousAnswer?.from || '')
    this.toText = ref(this.previousAnswer?.to || '')
    this.fromError = ref(null)
    this.toError = ref(null)

    this.todayDate = today(getLocalTimeZone())
    this.months = computed(() => toValue(data.months))
    this.hasInteracted = ref(false)

    const firstMonth = toValue(this.months)[0]

    // Получаем предыдущее значение для поля типа DateMonth или DateMonthTime
    const { day: previouslySelectedDay, month: previouslySelectedMonth } = this.previousAnswer || {}
    const previousMonth = this.months.value.find(month => month.id === Number.parseInt(previouslySelectedMonth))
    const month = this.isRequired ? (previousMonth || firstMonth) : (previousMonth || null)

    const _selectedMonth = ref(month)
    this.selectedMonth = computed({
      get: () => {
        const foundMonth = toValue(this.months).find(month => month.id === _selectedMonth.value?.id)
        return foundMonth
      },
      set: value => _selectedMonth.value = value,
    })
    this.day = ref(previouslySelectedDay || '')
    this.dayError = ref(null)
    this.monthError = ref(null)
    this.monthDayDate = computed(() => {
      const opts = {}
      if (this.day.value) {
        opts.day = this.day.value
      }
      if (this.selectedMonth.value) {
        opts.month = toValue(this.selectedMonth).id
      }
      return this.todayDate.set(opts)
    })

    this.daysInMonth = computed(() => getDaysInMonth(toValue(this.monthDayDate)))

    watch(this.daysInMonth, (v) => {
      if (!this.day.value) {
        return
      }
      const n = Number.parseInt(v)
      if (Number.parseInt(this.day.value) > n) {
        this.day.value = n.toString()
      }
    })
    this.name = ref('')
    this.surname = ref('')
    this.patronymic = ref('')

    this.hasName = ref(false)
    this.hasSurname = ref(false)
    this.hasPatronymic = ref(false)

    watch(this.field, (f) => {
      if (f) {
        this.markInteracted()
      }
      this.validate()
    })

    if (toValue(this.isNameMask)) {
      this._configurateNameMask()
    }
    else if (toValue(this.isDateMask) || toValue(this.isDateTimeMask)) {
      this._configurateDateMask()
    }
    else if (toValue(this.isPeriodMask)) {
      this._configuratePeriodMask()
    }
    else if (toValue(this.isDateMonthMask) || toValue(this.isDateMonthTimeMask)) {
      this._configurateDateMonthMask()
    }
    else {
      this._configurateField()
    }

    if (toValue(this.isTimeMask) || toValue(this.isDateTimeMask) || toValue(this.isDateMonthTimeMask)) {
      this._configurateTimeMask()
    }

    this.validationObject = this._configurateValidationObject()

    this.isValid = computed(() => this.validate())
    this.blocked = computed(() => this.touched.value && !this.isValid.value)

    this.hasInteracted = ref(false)
  }

  validate() {
    this.error.value = null
    this.nameError.value = null
    this.surnameError.value = null
    this.patronymicError.value = null

    if (!this.touched.value) {
      return true
    }

    if (toValue(this.type) === MaskTypes.Name) {
      return this._validateNameMask()
    }

    if (toValue(this.type) === MaskTypes.NoMask) {
      return this._validateTextField(toValue(this.field), {
        minLength: toValue(this.fieldConfig).minLength,
        maxLength: toValue(this.fieldConfig).maxLength,
        error: this.error,
        required: toValue(this.fieldConfig).required,
      })
    }

    if (toValue(this.type) === MaskTypes.Phone) {
      return this._validatePhone(this.field.value)
    }

    if (toValue(this.type) === MaskTypes.Email) {
      return this._validateEmail(this.field.value)
    }

    if (toValue(this.type) === MaskTypes.Number) {
      return this._validateNumber(this.field.value)
    }

    if (toValue(this.type) === MaskTypes.Site) {
      return this._validateSite(this.field.value)
    }

    if (toValue(this.isDateMask)) {
      return this._validateDate(this.field.value, this.date.value)
    }

    if (toValue(this.isDateMonthMask)) {
      return this._validateDateMonthMask()
    }

    if (toValue(this.isPeriodMask)) {
      return this._validatePeriod()
    }

    if (toValue(this.isTimeMask)) {
      return this._validateTime(this.time.value)
    }

    if (toValue(this.isDateTimeMask)) {
      return this._validateDateTimeMask()
    }
    if (toValue(this.isDateMonthTimeMask)) {
      return this._validateDateMonthTimeMask()
    }

    return true
  }

  _validateDate(textField, forceRequired = false) {
    this.dateError.value = null
    const isRequired = toValue(this.fieldConfig).required || forceRequired
    if (!this.touched.value) {
      return true
    }
    if (isRequired && !textField) {
      this.dateError.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    const dateRegex = /^\d{2}\.\d{2}\.\d{4}$/

    if (textField && !dateRegex.test(textField)) {
      this.dateError.value = this.translationsStore.t('Неверный формат')
      return false
    }

    return true
  }

  _validateDateTimeMask() {
    const timeIsNotEmpty = !!this.time.value
    const dateIsNotEmpty = !!this.field.value
    const dateValid = this._validateDate(this.field.value, timeIsNotEmpty)
    const timeValid = this._validateTime(this.time.value, dateIsNotEmpty)

    return dateValid && timeValid
  }

  _validateDateMonthMask(forceRequired = false) {
    this.dayError.value = null
    this.error.value = null
    this.monthError.value = null
    let dayValid = true
    let monthValid = true
    if (!this.touched.value) {
      return true
    }
    const day = Number.parseInt(this.day.value)
    const selectedMonth = toValue(this.selectedMonth)
    const isRequired = toValue(this.fieldConfig).required || forceRequired
    const isRequiredAndDayEmpty = isRequired && !day
    const isRequiredAndMonthEmpty = isRequired && !selectedMonth
    const isNotRequiredAndDaySelected = !isRequired && !selectedMonth && day
    const isNotRequiredAndMonthSelected = !isRequired && selectedMonth && !day
    if (isRequiredAndDayEmpty || isNotRequiredAndMonthSelected) {
      this.dayError.value = this.translationsStore.t('Обязательное поле')
      dayValid = false
    }
    if (isRequiredAndMonthEmpty || isNotRequiredAndDaySelected) {
      this.error.value = this.translationsStore.t('Обязательное поле')
      this.monthError.value = this.translationsStore.t('Обязательное поле')
      monthValid = false
    }
    if (day < 1 || day > 31) {
      this.dayError.value = this.translationsStore.t('Некорректное значение')
      this.error.value = this.translationsStore.t('Некорректное значение')
      dayValid = false
    }

    return dayValid && monthValid
  }

  _validateDateMonthTimeMask() {
    const timeIsNotEmpty = !!this.time.value
    const dayIsNotEmpty = !!this.day.value
    const monthIsNotEmpty = !!this.selectedMonth.value
    const dateValid = this._validateDateMonthMask(timeIsNotEmpty)
    const timeValid = this._validateTime(this.time.value, dayIsNotEmpty || monthIsNotEmpty)
    return dateValid && timeValid
  }

  _validateNumber(value) {
    const isRequired = toValue(this.fieldConfig).required
    if (!this.touched.value) {
      return true
    }
    if (isRequired && !value) {
      this.error.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    const NUMBER_REGEX = /^[0-9.,]+$/

    if (!isRequired && value && !NUMBER_REGEX.test(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    if (isRequired && !NUMBER_REGEX.test(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    return this._validateTextField(value, {
      minLength: toValue(this.fieldConfig).minLength,
      maxLength: toValue(this.fieldConfig).maxLength,
      error: this.error,
      required: toValue(this.fieldConfig).required,
    })
  }

  _validateSite(value) {
    const isRequired = toValue(this.fieldConfig).required
    if (!this.touched.value) {
      return true
    }
    if (isRequired && !value) {
      this.error.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    if (!isRequired && value && !isValidHttpUrl(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    if (isRequired && !isValidHttpUrl(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    return this._validateTextField(value, {
      minLength: toValue(this.fieldConfig).minLength,
      maxLength: toValue(this.fieldConfig).maxLength,
      error: this.error,
      required: toValue(this.fieldConfig).required,
    })
  }

  _validateEmail(value) {
    const isRequired = toValue(this.fieldConfig).required
    if (!this.touched.value) {
      return true
    }
    if (isRequired && !value) {
      this.error.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    if (isRequired && !EMAIL_REGEX.test(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    if (!isRequired && value && !EMAIL_REGEX.test(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    return this._validateTextField(value, {
      minLength: toValue(this.fieldConfig).minLength,
      maxLength: toValue(this.fieldConfig).maxLength,
      error: this.error,
      required: toValue(this.fieldConfig).required,
    })
  }

  _validatePhone(value) {
    const isRequired = toValue(this.fieldConfig).required
    if (!this.touched.value) {
      return true
    }
    if (isRequired && !value) {
      this.error.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    if (isRequired && !PHONE_REGEX.test(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    if (!isRequired && value && !PHONE_REGEX.test(value)) {
      this.error.value = this.translationsStore.t('Неверный формат')
      return false
    }

    return this._validateTextField(value, {
      minLength: toValue(this.fieldConfig).minLength,
      maxLength: toValue(this.fieldConfig).maxLength,
      error: this.error,
      required: toValue(this.fieldConfig).required,
    })
  }

  setAllAsTouched() {
    this.touched.value = true
    this.nameTouched.value = true
    this.surnameTouched.value = true
    this.patronymicTouched.value = true
  }

  _configurateValidationObject() {
    if (this.isNameMask) {
      return computed(() => ({
        name: this.name.value,
        surname: this.surname.value,
        patronymic: this.patronymic.value,
      }))
    }

    if (this.isPeriodMask) {
      return this.period
    }

    return this.field
  }

  _configurateDateMonthMask() {
    watch(this.day, (day) => {
      if (day) {
        this.markInteracted()
      }
      this._validateDateMonthMask()
    })
    watch(this.selectedMonth, () => {
      this._validateDateMonthMask()
    })
  }

  _configurateDateMask() {
    watch(this.field, (dateString) => {
      if (!dateString) {
        return
      }
      this.markInteracted()

      const parsed = typeof dateString === 'object' ? dateString?.date?.split?.('.') : dateString?.split?.('.')
      // check if parsed date contains 00.00.0000 format by regex
      const dateRegex = /^\d{2}\.\d{2}\.\d{4}$/
      if (dateRegex.test(dateString)) {
        this.date.value = parseDate(`${parsed[2]}-${parsed[1]}-${parsed[0]}`)
      }
    }, {
      immediate: true,
    })
    watch(this.date, (v) => {
      if (v && v.toDate) {
        this.markInteracted()
        this.field.value = this.dateFormatter.format(v.toDate())
      }
    })
  }

  _configuratePeriodMask() {
    watch(this.fromText, (dateString) => {
      if (dateString) {
        this.markInteracted()
      }
      const parsed = dateString.split('.')
      // check if parsed date contains 00.00.0000 format by regex
      const dateRegex = /^\d{2}\.\d{2}\.\d{4}$/
      if (dateRegex.test(dateString)) {
        this.from.value = parseDate(`${parsed[2]}-${parsed[1]}-${parsed[0]}`)
      }
      this._validatePeriod()
    }, {
      immediate: true,
    })
    watch(this.toText, (dateString) => {
      if (dateString) {
        this.markInteracted()
      }
      const parsed = dateString.split('.')
      // check if parsed date contains 00.00.0000 format by regex
      const dateRegex = /^\d{2}\.\d{2}\.\d{4}$/
      if (dateRegex.test(dateString)) {
        this.to.value = parseDate(`${parsed[2]}-${parsed[1]}-${parsed[0]}`)
      }
      this._validatePeriod()
    }, {
      immediate: true,
    })

    watch(this.from, (v) => {
      if (v && v.toDate) {
        this.markInteracted()
        this.fromText.value = this.dateFormatter.format(v.toDate())
      }
    })
    watch(this.to, (v) => {
      if (v && v.toDate) {
        this.markInteracted()
        this.toText.value = this.dateFormatter.format(v.toDate())
      }
    })
  }

  _validatePeriod() {
    this.fromError.value = null
    this.toError.value = null
    this.error.value = null
    let periodValid = true
    let fromValid = true
    let toValid = true
    const isRequired = toValue(this.fieldConfig).required
    if (!this.touched.value) {
      return true
    }

    if (isRequired) {
      if (!this.fromText.value) {
        this.fromError.value = toValue(this.translationsStore.t('Обязательное поле'))
        fromValid = false
      }
      if (!this.toText.value) {
        this.toError.value = toValue(this.translationsStore.t('Обязательное поле'))
        toValid = false
      }
      if (!this.from.value) {
        this.fromError.value = toValue(this.translationsStore.t('Обязательное поле'))
        fromValid = false
      }
      if (!this.to.value) {
        this.toError.value = toValue(this.translationsStore.t('Обязательное поле'))
        toValid = false
      }
    }

    if (this.fromText.value && !this.from.value) {
      this.fromError.value = toValue(this.translationsStore.t('Некорректное значение'))
      fromValid = false
    }

    if (this.toText.value && !this.to.value) {
      this.toError.value = toValue(this.translationsStore.t('Некорректное значение'))
      toValid = false
    }

    if (this.from.value && !this.to.value) {
      this.error.value = toValue(this.translationsStore.t('Некорректный период'))
      periodValid = false
    }

    if (this.to.value && !this.from.value) {
      this.error.value = toValue(this.translationsStore.t('Некорректный период'))
      periodValid = false
    }

    const dateRegex = /^\d{2}\.\d{2}\.\d{4}$/

    if (this.from.value && this.to.value) {
      if (!isBeforeOrSame(this.from.value, this.to.value)) {
        this.error.value = toValue(this.translationsStore.t('Некорректный период'))
        periodValid = false
      }
      if (!dateRegex.test(this.fromText.value) || !dateRegex.test(this.toText.value)) {
        this.error.value = toValue(this.translationsStore.t('Некорректный период'))
        periodValid = false
      }
    }

    return periodValid && fromValid && toValid
  }

  _configurateField() {
    if (this.isDateMonthMask) {
      return
    }

    watch(this.field, () => {
      this.validate()
    })
  }

  getSymbolWordForm(n) {
    return declOfNum(n, ['символ', 'символа', 'символов'])
  }

  _configurateNameMask() {
    if (this.config.name?.visible) {
      this.hasName.value = true
      this.name.value = this.config.name.value
    }

    if (this.config.surname?.visible) {
      this.hasSurname.value = true
      this.surname.value = this.config.surname.value
    }

    if (this.config.patronymic?.visible) {
      this.hasPatronymic.value = true
      this.patronymic.value = this.config.patronymic.value
    }

    watch([
      () => this.nameConfig.value.value,
      () => this.surnameConfig.value.value,
      () => this.patronymicConfig.value.value,
    ], ([name, surname, patronymic]) => {
      if (name || surname || patronymic) {
        this.markInteracted()
      }
      this._validateNameMask()
    })
  }

  _validateNameMask() {
    let isValid = true
    const nameConfig = this.nameConfig.value
    const surnameConfig = this.surnameConfig.value
    const patronymicConfig = this.patronymicConfig.value

    if (!this.touched.value) {
      return true
    }

    if (nameConfig.visible) {
      const nameValid = this._validateTextField(this.nameConfig.value.value, {
        minLength: nameConfig.minLength,
        maxLength: nameConfig.maxLength,
        error: this.nameError,
        required: nameConfig.required,
      })
      isValid = isValid && nameValid
    }

    if (surnameConfig.visible) {
      const surnameValid = this._validateTextField(this.surnameConfig.value.value, {
        minLength: surnameConfig.minLength,
        maxLength: surnameConfig.maxLength,
        error: this.surnameError,
        required: surnameConfig.required,
      })
      isValid = isValid && surnameValid
    }

    if (patronymicConfig.visible) {
      const patronymicValid = this._validateTextField(this.patronymicConfig.value.value, {
        minLength: patronymicConfig.minLength,
        maxLength: patronymicConfig.maxLength,
        error: this.patronymicError,
        required: patronymicConfig.required,
      })
      isValid = isValid && patronymicValid
    }

    return isValid
  }

  _validateTextField(value, config) {
    config.error.value = null
    if (!this.touched.value) {
      return true
    }

    if (toValue(config.required) && !value) {
      config.error.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    const minLength = Number.parseInt(toValue(config.minLength))
    if (minLength && !!value && value.length < minLength) {
      const count = minLength
      const word = this.getSymbolWordForm(count)
      const characters = toValue(this.translationsStore.t(`{count} ${word}`, { count }))
      config.error.value = this.translationsStore.t(`Должно быть введено хотя бы {characters}`, { characters })
      return false
    }

    const maxLength = toValue(config.maxLength)
    if (maxLength && !!value && value.length > maxLength) {
      const count = maxLength
      const word = this.getSymbolWordForm(count)
      const characters = toValue(this.translationsStore.t(`{count} ${word}`, { count }))
      config.error.value = this.translationsStore.t(`Должно быть введено не более {characters}`, { characters })
      return false
    }

    return true
  }

  _configurateTimeMask() {
    watch(this.time, (newValue) => {
      if (newValue) {
        this.markInteracted()
      }
      this._validateTime(newValue)
    })
  }

  _validateTime(value, forceRequired = false) {
    this.timeError.value = null

    const isRequired = toValue(this.fieldConfig).required || forceRequired

    if (!this.touched.value) {
      return true
    }

    if (isRequired && !value) {
      this.timeError.value = this.translationsStore.t('Обязательное поле')
      return false
    }

    if (value && !TIME_REGEX.test(value)) {
      this.timeError.value = this.translationsStore.t('Некорректный формат')
      return false
    }

    return true
  }

  hasValue() {
    if (toValue(this.isNameMask)) {
      const hasName = this.name.value || this.nameConfig.value.value
      const hasSurname = this.surname.value || this.surnameConfig.value.value
      const hasPatronymic = this.patronymic.value || this.patronymicConfig.value.value
      return hasName || hasSurname || hasPatronymic
    }
    if (toValue(this.isPeriodMask)) {
      return this.from.value && this.to.value
    }
    if (toValue(this.isDateMonthMask)) {
      return this.day.value && this.selectedMonth.value
    }
    if (toValue(this.type) === MaskTypes.DateTime) {
      return this.field.value && this.time.value
    }
    if (toValue(this.isDateMonthTimeMask)) {
      return this.day.value && this.selectedMonth.value && this.time.value
    }
    if (toValue(this.isTimeMask)) {
      return !!this.time.value
    }
    return !!this.field.value
  }

  getData() {
    if (toValue(this.isNameMask)) {
      const name = toValue(this.nameConfig)?.value
      const surname = toValue(this.surnameConfig)?.value
      const patronymic = toValue(this.patronymicConfig)?.value

      const hasValue = name || surname || patronymic

      if (!hasValue) {
        return ''
      }

      return {
        name,
        surname,
        patronymic,
      }
    }
    if (toValue(this.isPeriodMask)) {
      if (!this.fromText.value || !this.toText.value) {
        return ''
      }
      return `${this.fromText.value} - ${this.toText.value}`
    }
    if (toValue(this.isDateMonthMask)) {
      if (this.monthDayDate.value && this.day.value) {
        const dateString = this.dateFormatter.format(this.monthDayDate.value.toDate())
        // remove yyyy from 'dd.mm.yyyy'
        return dateString.split('.').slice(0, 2).join('.')
      }
      return ''
    }
    if (toValue(this.isTimeMask)) {
      return this.time.value
    }
    if (toValue(this.isDateTimeMask)) {
      return {
        date: this.field.value,
        time: this.time.value,
      }
    }
    if (toValue(this.isDateMonthTimeMask)) {
      const dateMonthDefined = this.monthDayDate.value && this.day.value
      const dateString = dateMonthDefined ? this.dateFormatter.format(this.monthDayDate.value.toDate()) : ''

      const formattedDateString = dateMonthDefined ? dateString.split('.').slice(0, 2).join('.') : ''
      return {
        date: formattedDateString,
        time: this.time.value,
      }
    }
    return this.field.value
  }

  markInteracted() {
    this.hasInteracted.value = true
  }

  resetFieldValues() {
    this.field.value = ''
    this.time.value = ''
    this.error.value = null
    this.nameError.value = null
    this.surnameError.value = null
    this.patronymicError.value = null
    this.touched.value = false
    this.nameTouched.value = false
    this.surnameTouched.value = false
    this.patronymicTouched.value = false
    this.hasInteracted.value = false
    this.dateError.value = null
    this.fromError.value = null
    this.toError.value = null
    this.dayError.value = null
    this.monthError.value = null
    this.surnameConfig.value.value = null
    this.nameConfig.value.value = null
    this.patronymicConfig.value.value = null
    this.day.value = null
    this.from.value = null
    this.to.value = null

    if (!toValue(this.fieldConfig).required) {
      this.selectedMonth.value = null
    }
  }

  updateFromPreview(data) {
    const requiredChanged = !!toValue(this.fieldConfig)?.required !== !!data.fieldConfig?.required
    const originalType = this.type.value
    const newType = data.type

    const maskTypeChanged = originalType !== newType

    if (maskTypeChanged || requiredChanged) {
      this.resetFieldValues()
      this.type.value = data.type
    }

    const placeholderText = data.fieldConfig.placeholderText !== undefined ? data.fieldConfig.placeholderText : ''

    const DEFAULT_MIN_LENGTH = 0
    const DEFAULT_MAX_LENGTH = 250

    const masksWithLimits = [MaskTypes.NoMask, MaskTypes.Email, MaskTypes.Site, MaskTypes.Number]

    const currentMaskHasLimits = masksWithLimits.includes(this.type.value)

    // Update field config
    if (data.fieldConfig) {
      const minLength = currentMaskHasLimits ? (data.fieldConfig.minLength || DEFAULT_MIN_LENGTH) : DEFAULT_MIN_LENGTH
      const maxLength = currentMaskHasLimits ? (data.fieldConfig.maxLength || DEFAULT_MAX_LENGTH) : DEFAULT_MAX_LENGTH
      this._label.value = data.fieldConfig.label

      this.fieldConfig.value.label = data.fieldConfig.label
      this.fieldConfig.value.placeholderText.value = placeholderText
      this.fieldConfig.value.required = !!data.fieldConfig.required
      this.fieldConfig.value.multiline = data.fieldConfig.multiline
      this.fieldConfig.value.minLength = minLength
      this.fieldConfig.value.maxLength = maxLength
      this.fieldConfig.value.multiline = data.fieldConfig.multiline

      triggerRef(this.fieldConfig)
    }

    // Update name mask config if provided
    if (data.config) {
      if (data.config.name) {
        this.nameConfig.value.visible = data.config.name.visible || false
        this.nameConfig.value.required = data.config.name.required || false
        this.nameConfig.value.minLength = data.config.name.minLength || 0
        this.nameConfig.value.maxLength = data.config.name.maxLength || 999
        this.nameConfig.value.placeholderText = data.config.name.placeholderText || ''
      }

      if (data.config.surname) {
        this.surnameConfig.value.visible = data.config.surname.visible || false
        this.surnameConfig.value.required = data.config.surname.required || false
        this.surnameConfig.value.minLength = data.config.surname.minLength || 0
        this.surnameConfig.value.maxLength = data.config.surname.maxLength || 999
        this.surnameConfig.value.placeholderText = data.config.surname.placeholderText || ''
      }

      if (data.config.patronymic) {
        this.patronymicConfig.value.visible = data.config.patronymic.visible || false
        this.patronymicConfig.value.required = data.config.patronymic.required || false
        this.patronymicConfig.value.minLength = data.config.patronymic.minLength || 0
        this.patronymicConfig.value.maxLength = data.config.patronymic.maxLength || 999
        this.patronymicConfig.value.placeholderText = data.config.patronymic.placeholderText || ''
      }
    }

    // Reset validation state
    this.touched.value = false
    this.nameTouched.value = false
    this.surnameTouched.value = false
    this.patronymicTouched.value = false
    this.error.value = null
    this.nameError.value = null
    this.surnameError.value = null
    this.patronymicError.value = null
    this.dayError.value = null
    this.monthError.value = null
    this.dateError.value = null
    this.fromError.value = null
    this.toError.value = null

    // Reconfigure based on new type
    if (toValue(this.isNameMask)) {
      this._configurateNameMask()
    }
    else if (toValue(this.isDateMask) || toValue(this.isDateTimeMask)) {
      this._configurateDateMask()
    }
    else if (toValue(this.isPeriodMask)) {
      this._configuratePeriodMask()
    }
    else if (toValue(this.isDateMonthMask) || toValue(this.isDateMonthTimeMask)) {
      this._configurateDateMonthMask()
    }
    else {
      this._configurateField()
    }

    if (toValue(this.isTimeMask) || toValue(this.isDateTimeMask) || toValue(this.isDateMonthTimeMask)) {
      this._configurateTimeMask()
    }

    // Reset interaction state
    this.hasInteracted.value = false
  }
}
