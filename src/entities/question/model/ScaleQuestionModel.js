import {
  BEHAVIOR_MISS,
  BEHAVIOR_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'

export class ScaleQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    this.skip = ref(data.skip)
    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || toValue(this.t('Не готов(а) оценить'))
    })

    this.previousAnswerItems = JSON.parse(this.previousAnswer?.answer || '{}')

    this.skipped = ref(this.previousAnswer?.skipped === 1)

    this._scaleConfig = ref(data.scaleRatingSetting || {})
    this.scaleConfig = computed(() => this._scaleConfig.value)

    this._isVariants = ref(data.set_variants === 1)
    this.isVariants = computed(() => this._isVariants.value)

    this._isSkipVariant = ref(data.skip_variant === 1)
    this.isSkipVariant = computed(() => this._isSkipVariant.value)

    this.isRequired = ref(data.isRequired)

    this._randomVariantsOrder = ref(data.random_variants_order === 1)
    this.randomVariantsOrder = computed(() => this._randomVariantsOrder.value)
    this.error = ref(null)
    this.detailAnswers = data.detail_answers || []
    this.variants = shallowRef([])
    this.scaleStart = computed(() => this.scaleConfig.value.start)
    this.scaleEnd = computed(() => this.scaleConfig.value.end)
    this.scaleStep = computed(() => this.scaleConfig.value.step)

    if (this.isVariants.value && !this.hasDonor) {
      const preparedVariants = this.getPreparedVariants(this.detailAnswers)
      this.variants.value = preparedVariants
      this.variants.value.forEach((variant) => {
        this.setupVariantWatchers(variant)
      })
    }

    this.touched = ref(false)

    if (this.isVariants.value) {
      watch(this.selectedDonorVariants, (newDonorVariants) => {
        if (!this.hasDonor)
          return

        const filteredDetailAnswers = []

        newDonorVariants.forEach((v) => {
          if (v.id === 'is_self_answer') {
            // Handle self answer variant
            // copy variant to filteredDetailAnswers
            const recipientSelfVariant = this.detailAnswers
              .find(answer => this.isVariantRecipientSelfAnswer(answer))

            recipientSelfVariant.label = computed(() => toValue(v.alternativeVariantLabel))
            recipientSelfVariant.id = '-1'
            filteredDetailAnswers.push(recipientSelfVariant)
            return
          }

          const donorVariant = this.detailAnswers
            .find(answer => answer.dictionary_element_id === v.id || answer.question_detail_id === v.id)

          if (donorVariant) {
            donorVariant.id = v.id
            donorVariant.question = this.getRecipientVariantName(donorVariant)
            filteredDetailAnswers.push(donorVariant)
          }
        })

        const newVariants = this.getPreparedVariants(filteredDetailAnswers, true)
        const existingVariants = this.variants.value || []
        const preservedVariants = []

        newVariants.forEach((newVariant) => {
          const existingVariant = existingVariants.find(v => v.id === newVariant.id)

          if (existingVariant) {
            preservedVariants.push(existingVariant)
          }
          else {
            preservedVariants.push(newVariant)
            this.setupVariantWatchers(newVariant)
          }
        })

        this.variants.value = this.sortVariants(preservedVariants)
      }, { immediate: true })
    }
    this.rating = ref([this?.previousAnswer?.rating || this.scaleStart.value])
    watch(this.rating, () => {
      if (!toValue(this.isVariants)) {
        this.touched.value = true
        this.markInteracted()
        this.validateRating()
      }
    })

    this.isCommentRequired = data.comment_required
    this.commentEnabled = ref(data.comment_enabled)
    this.textFieldParam = data.textFieldParam || {}
    this.placeholderText = data.placeholderText

    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: this.isCommentRequired,
      skipped: this.skipped,
      value: data.textFieldValue || '',
      placeholderText: this.placeholderText,
      minLength: this.textFieldParam.min || 0,
      maxLength: this.textFieldParam.max || 1000,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.isValid = computed(() => {
      const isCommentValid = toValue(this.commentController.isValid)
      if (this.commentEnabled.value && !isCommentValid)
        return false
      return true
    })

    this.blocked = computed(() => {
      const someVariantHasError = this.variants.value.some(v => v.error.value)
      return someVariantHasError || (this.touched.value && !this.isValid.value)
    })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        this.emit('get-answer')
      }
    })

    this.selectedVariants = computed(() => {
      if (this.isVariants.value) {
        return this.variants.value.filter(v => toValue(v.rating)[0] !== this.scaleStart.value).map(v => toValue(v.rating)[0])
      }
      else {
        return this.rating.value[0] !== this.scaleStart.value ? [this.rating.value[0]] : []
      }
    })

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: toValue(this.enableGallery),
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (skipped) => {
      if (skipped) {
        // If question is skipped, we can move to next question
        this.canMoveToNextQuestion.value = true
      }
    })
  }

  setupVariantWatchers(variant) {
    watch(variant.rating, () => {
      variant.touched.value = true
      this.markInteracted()
    })
    watch(variant.skipped, (v) => {
      if (v) {
        variant.rating.value = [this.scaleStart.value]
        variant.error.value = null
        this.markInteracted()
      }
    })
  }

  /**
   * Подготавливает варианты для отображения в шкале
   * @param {object[]} rawVariants необработанные варианты.
   * @param {boolean} fromDonor признак того, что варианты берутся из донора
   * @returns {object[]} Массив подготовленных вариантов
   */
  getPreparedVariants(rawVariants = [], fromDonor = false) {
    const filteredVariants = rawVariants.filter(v => fromDonor ? true : v.is_deleted === 0)
    const transformedVariants = filteredVariants.map(v => this.prepareVariant(v, fromDonor))
    return this.sortVariants(transformedVariants)
  }

  /**
   * Подготавливает один вариант для отображения
   * @param {object} variant необработанный вариант
   * @param {boolean} fromDonor признак того, что вариант берется из донора
   * @returns {object} Подготовленный вариант
   */
  prepareVariant(variant, fromDonor = false) {
    const _text = ref(variant.variant || variant.value || variant.question)
    const transformedVariant = {
      ...variant,
      rating: ref([this.scaleStart.value]),
      error: ref(null),
      touched: ref(false),
      skipped: ref(false),
      text: computed({
        get: () => {
          const labelFromDonor = toValue(variant.label)
          if (fromDonor && labelFromDonor) {
            return labelFromDonor
          }
          const translation = toValue(this.translations).detailLangs?.[variant.id]
          return translation?.question || _text.value
        },
        set: (value) => {
          _text.value = value
        },
      }),
    }

    if (this.previousAnswerItems[variant.id]) {
      const previousAnswer = this.previousAnswerItems[variant.id]
      const numRating = Number(previousAnswer)
      transformedVariant.rating.value = [numRating || this.scaleStart.value]

      if (previousAnswer === 'null') {
        transformedVariant.skipped.value = true
      }
    }

    return transformedVariant
  }

  /**
   * Сортирует варианты по позиции
   * @param {object[]} variants варианты для сортировки
   * @returns {object[]} Отсортированные варианты
   */
  sortVariants(variants) {
    if (this.randomVariantsOrder.value) {
      return shuffle(variants)
    }
    return variants.sort((a, b) => a.position - b.position)
  }

  resetFields() {
    this.touched.value = false
    if (this.isVariants.value) {
      this.variants.value.forEach((v) => {
        v.rating.value = [this.scaleStart.value]
        v.error.value = null
        v.touched.value = false
      })
    }
    else {
      this.rating.value = [this.scaleStart.value]
      this.error.value = null
    }
    this.commentController.value.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null
  }

  setAllAsTouched() {
    this.touched.value = true
    if (this.isVariants.value) {
      this.variants.value.forEach(v => v.touched.value = true)
    }
    if (this.commentEnabled.value) {
      this.commentController.touched.value = true
    }
  }

  validateVariant(variant) {
    variant.error.value = null
    if (!variant.touched.value)
      return true

    if (variant.rating.value[0] === undefined) {
      variant.error.value = this.t('Нужно выбрать значение')
      return false
    }

    return true
  }

  validateRating() {
    if (!this.touched.value)
      return true

    if (!toValue(this.isRequired)) {
      this.error.value = null
      return true
    }

    if (this.skip.value && this.skipped.value) {
      this.error.value = null
      return true
    }

    if (this.rating.value[0] === undefined) {
      this.error.value = this.t('Нужно выбрать значение')
      return false
    }

    this.error.value = null
    return true
  }

  validate() {
    if (this.isVariants.value) {
      this.variants.value.forEach(v => this.validateVariant(v))
    }
    else {
      this.validateRating()
    }
    if (this.commentEnabled.value) {
      this.commentController.validate()
    }

    return this.isValid.value
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {}

    if (this.isVariants.value) {
      data.rating = {}
      this.variants.value.forEach((v) => {
        const ratingValue = toValue(v.rating)[0]
        const isSkipped = toValue(v.skipped)
        data.rating[v.id] = isSkipped ? 'null' : ratingValue
      })
    }
    else {
      data.rating = this.rating.value[0]
    }

    if (this.commentEnabled.value) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    if (this.isVariants.value) {
      return this.variants.value
        .map(v => toValue(v.rating) !== this.scaleStart.value ? `${v.value}: ${toValue(v.rating)}` : '')
        .filter(Boolean)
        .join(', ')
    }

    const rating = this.rating.value
    return rating[0] === this.scaleStart.value ? '' : `${rating[0]}`
  }

  get hasValue() {
    if (this.isVariants.value) { // Для вариантов
      const hasPreviousAnswer = !!this.previousAnswer
      return hasPreviousAnswer
    }
    else { // Для одной шкалы
      const hasPreviousAnswer = !!this.previousAnswer
      return hasPreviousAnswer
    }
  }

  checkCondition(condition) {
    if (super.checkCondition(condition))
      return true

    const scaleValue = this.rating.value[0]
    const variants = condition.variants

    let minMax = []

    if (variants.length === 2) {
      minMax = variants
    }

    if (condition.behavior === BEHAVIOR_MISS) {
      return scaleValue === this.scaleStart.value
    }

    if (condition.behavior === BEHAVIOR_SELECT && minMax.length === 2) {
      return scaleValue >= minMax[0] && scaleValue <= minMax[1]
    }

    if (condition.behavior === BEHAVIOR_UNSELECT && minMax.length === 2) {
      return scaleValue < minMax[0] || scaleValue > minMax[1]
    }

    return false
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      if (this.isVariants.value) {
        return this.variants.value
          .map(v => `${v.text.value}: ${toValue(v.rating)[0]}`)
          .join('; ')
      }
      else {
        const rating = toValue(this.rating)?.[0]
        return rating
      }
    })
  }

  /**
   * Resolves view logic condition for scale question
   * @param {object} rule - The view logic rule to check
   * @returns {boolean} Whether the condition is met
   */
  resolveViewLogic(rule) {
    if (this.isVariants.value) {
      const ruleVariants = rule.variants || []
      const skippedRows = (rule.skipped || []).map(row => row.toString())

      // Group variants by row
      const variantsByRow = ruleVariants.reduce((acc, v) => {
        acc[v.row] = acc[v.row] || []
        const range = Array.isArray(v.col) ? v.col : [v.col]

        acc[v.row].push(range)
        return acc
      }, {})

      // Check each row that has conditions (both regular and skipped)
      const rowsToCheck = [...new Set([...Object.keys(variantsByRow), ...skippedRows])]

      return rowsToCheck.every((variantRowId) => {
        // Find the corresponding row in our model
        const variant = this.variants.value.find(v => Number.parseInt(v.id) === Number.parseInt(variantRowId))
        if (!variant)
          return false

        const range = variantsByRow[variantRowId]

        const rating = variant.rating.value[0]

        const conditionIsMet = range ? range.some(r => rating >= r[0] && rating <= r[1]) : false
        const skippedConditionIsMet = skippedRows.includes(variantRowId) && (toValue(variant.skipped) || toValue(this.skipped))

        return conditionIsMet || skippedConditionIsMet
      })
    }
    else {
      const rating = this.rating.value[0]
      const minMax = rule.variants
      if (minMax.length === 2) {
        return rating >= minMax[0] && rating <= minMax[1]
      }
      return false
    }
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update scale configuration
    if (previewData.scaleRatingSetting) {
      const scaleSettingsChanged = JSON.stringify(this._scaleConfig.value) !== JSON.stringify(previewData.scaleRatingSetting)
      this._scaleConfig.value = previewData.scaleRatingSetting

      if (scaleSettingsChanged) {
        this.resetFields()
      }
    }

    const skipIsChanged = this.skip.value !== previewData.skip
    const skipVariantIsChanged = this.isSkipVariant.value !== !!previewData.skip_variant

    this._isVariants.value = previewData.set_variants === 1
    this._isSkipVariant.value = this.isVariants.value ? previewData.skip_variant === 1 : false
    this._randomVariantsOrder.value = previewData.random_variants_order

    // Update variants with state preservation
    if (previewData.variants) {
      const updatedVariants = previewData.variants.map((newVariant, index) => {
        // Try to find matching existing variant by persistentId
        const existingVariant = this.variants.value.find(v =>
          v.id === newVariant.persistentId,
        )

        if (existingVariant) {
          // Update text while preserving state
          existingVariant.text.value = newVariant.value
          existingVariant.position = newVariant.position || index
          return existingVariant
        }

        newVariant.id = newVariant.persistentId

        // If no matching variant found, prepare a new one
        return this.prepareVariant(newVariant)
      })

      this.variants.value = this.sortVariants(updatedVariants)

      this.setupVariantWatchers(this.variants.value)
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }
    if (skipIsChanged || skipVariantIsChanged) {
      this.skipped.value = false
      this.variants.value.forEach(v => v.skipped.value = false)
    }

    if (previewData.skip_text) {
      this._skipText.value = previewData.skip_text
    }

    // Update variants configuration
    this.commentEnabled.value = previewData.commentEnabled
    // Update comment controller
    this.commentController.updateFromPreview({
      enabled: previewData.commentEnabled,
      required: previewData.comment_required,
      title: previewData.comment_label || '',
      placeholderText: previewData.placeholderText || '',
      textFieldParam: previewData.textFieldParam || { min: 0, max: 1000 },
    })

    this.enableGallery.value = previewData.enableGallery === 1

    // Update gallery controller
    this.galleryController.updateFromPreview({
      enabled: previewData.enableGallery === 1,
      gallery: previewData.gallery || [],
    })
  }
}
