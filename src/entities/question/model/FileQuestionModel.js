import { getBaseAssetsUrl } from '@/shared/api'
import { ALLOWED_BUT_NOT_SUPPORTED, ALLOWED_FILE_TYPES } from '@/shared/constants/files'
import { usePollStore } from '@entities/poll/model/store'
import { BEHAVIOR_ALWAYS } from '@shared/constants/logic'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, nextTick, ref, toValue, watch } from 'vue'
import { uploadFiles } from '../../poll/api'
import { CommentController } from '../controllers/CommentController'
import { BaseQuestion } from './BaseQuestion'

const DEFAULT_FILE_TYPES = ['image', 'video', 'audio']
const DEFAULT_FILES_LENGTH = 4

export class FileQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    this.filesizeLimit = usePollStore().maxFileSize
    this.filesizeLimitInBytes = this.filesizeLimit * 1024 * 1024 // in bytes

    const { t } = useTranslationsStore()
    this.t = t
    this.data = data

    const fileTypes = (data.fileTypes || []).length > 0 ? data.fileTypes : DEFAULT_FILE_TYPES
    this.filesLength = ref(data.filesLength || DEFAULT_FILES_LENGTH)
    this.fileTypes = ref(fileTypes)

    this.supportsImages = computed(() => this.fileTypes.value.includes('image'))
    this.supportsVideos = computed(() => this.fileTypes.value.includes('video'))
    this.supportsAudios = computed(() => this.fileTypes.value.includes('audio'))

    const previoulslyLoadedFiles = data.answer?.files || data.loadedFiles || []
    this.files = ref(this.transformFiles(previoulslyLoadedFiles))

    this.questionId = data.question_id
    this.error = ref(null)
    // default | too-large | too-many
    this.errorKind = ref('default')
    this.touched = ref(false)

    this.isUploading = computed(() => this.files.value.some(file => toValue(file.isUploading)))

    this.commentEnabled = ref(data.isHaveCustomField || false)

    this.commentController = new CommentController({
      enabled: this.commentEnabled.value,
      required: data.comment_required === 1,
      value: data.comment || '',
      placeholderText: data.placeholderText,
      minLength: data.textFieldParam?.min || 0,
      maxLength: data.textFieldParam?.max || 1000,
      title: data.comment_label || 'Ваш комментарий',
      translations: this.translations,
    })

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return !this.isValid.value || this.isUploading.value
    })

    // Add watcher for interaction tracking
    watch(() => this.files.value.length, (filesCount) => {
      if (filesCount > 0) {
        this.markInteracted()
      }
    }, { immediate: true })
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    const fileTypes = previewData.fileTypes?.length > 0 ? previewData.fileTypes : DEFAULT_FILE_TYPES
    const filesLength = Number.parseInt(previewData.filesLength) || DEFAULT_FILES_LENGTH
    const filesLengthChanged = filesLength !== this.filesLength.value

    const fileTypesChanged = fileTypes.length !== this.fileTypes.value.length

    // Update file configuration
    if (previewData.filesLength !== undefined) {
      this.filesLength.value = filesLength
    }
    if (previewData.fileTypes) {
      this.fileTypes.value = fileTypes
    }

    if (filesLengthChanged || fileTypesChanged) {
      this.files.value = []
    }

    if (previewData.isHaveCustomField !== undefined) {
      this.commentEnabled.value = previewData.isHaveCustomField
    }

    // Update comment controller if present
    this.commentController.updateFromPreview({
      enabled: this.commentEnabled.value,
      required: previewData.comment_required,
      value: previewData.comment || '',
      placeholderText: previewData.placeholderText,
      textFieldParam: previewData.textFieldParam,
      title: previewData.comment_label,
    })
  }

  async removeFile(authKey, index) {
    const file = this.files.value[index]
    if (file) {
      // Remove file from the array without making API call
      this.files.value.splice(index, 1)
    }
  }

  // returns 'image', 'video' or 'audio' or null
  getFileType(file) {
    const regex = /\.[0-9a-z]+$/i
    const fileExtension = file.name.match(regex)?.[0]?.slice(1).toLowerCase()

    // Check in ALLOWED_FILE_TYPES
    for (const [fileType, extensions] of Object.entries(ALLOWED_FILE_TYPES)) {
      if (extensions.includes(fileExtension)) {
        return fileType
      }
    }

    // Check in ALLOWED_BUT_NOT_SUPPORTED
    for (const [fileType, extensions] of Object.entries(ALLOWED_BUT_NOT_SUPPORTED)) {
      if (extensions.includes(fileExtension)) {
        return fileType
      }
    }

    return null
  }

  isFileTypeAllowed(file) {
    const fileType = this.getFileType(file)
    if (!fileType) {
      return false
    }
    return this.fileTypes.value.includes(fileType)
  }

  showTemporaryError(message, kind = 'default') {
    this.error.value = message
    this.errorKind.value = kind
    this.touched.value = false
    setTimeout(() => {
      if (this.errorKind.value === kind) {
        this.error.value = null
      }
    }, 3000)
  }

  validateFiles() {
    this.error.value = null
    if (!this.touched.value)
      return true

    if (toValue(this.isRequired) && this.files.value.length === 0) {
      this.error.value = this.t('Загрузите файл')
      this.errorKind.value = 'default'
      return false
    }

    return true
  }

  validate() {
    if (!this.touched.value)
      return true

    let isValid = true
    let commentIsValid = true
    isValid = this.validateFiles()
    if (this.commentController.enabled.value) {
      commentIsValid = this.commentController.validate()
    }

    if (isValid) {
      this.error.value = null
    }

    return isValid && commentIsValid
  }

  validateSize(file) {
    if (file.size > this.filesizeLimitInBytes) {
      this.showTemporaryError(
        this.t(
          `Файл «{file}» слишком большой. Размер не должен превышать {filesizeLimit} МБ.`,
          { file: file.name, filesizeLimit: this.filesizeLimit },
        ),
      )
      return false
    }
    return true
  }

  renameFile(originalFile, newName) {
    return new File([originalFile], newName, {
      type: originalFile.type,
      lastModified: originalFile.lastModified,
    })
  }

  async uploadFiles({ authKey, files }) {
    this.error.value = null
    await nextTick()
    const questionId = this.questionId
    const invalidFilesNames = []
    const previousFiles = this.files.value
    const existingFileNames = new Set(previousFiles.map(file => file.name))

    const validFiles = files.filter((file) => {
      if (!this.isFileTypeAllowed(file)) {
        this.showTemporaryError(this.t('Недопустимый тип файла'))
        return false
      }
      if (file.size > this.filesizeLimitInBytes) {
        invalidFilesNames.push(file.name)
        return false
      }
      return true
    }).slice(0, this.filesLength.value).map((file) => {
      let newName = file.name
      let hasDuplicate = false
      let counter = 1

      while (existingFileNames.has(newName)) {
        const nameParts = file.name.split('.')
        const extension = nameParts.pop()
        const baseName = nameParts.join('.')
        newName = `${baseName}-${counter}.${extension}`
        counter++
        hasDuplicate = true
      }

      if (hasDuplicate) {
        return this.renameFile(file, newName)
      }

      return file
    })

    if (invalidFilesNames.length > 0) {
      const firstTranslation = this.t('Файл слишком большой')
      const secondTranslation = this.t('Размер файла не должен превышать {size} Мб', { size: this.filesizeLimit })
      const message = computed(() => `${firstTranslation.value}: «${invalidFilesNames.join('», «')}». ${secondTranslation.value}`)

      this.showTemporaryError(message, 'too-large')
    }

    if (validFiles.length === 0) {
      return
    }

    const allowedFilesCount = this.filesLength.value - this.files.value.length

    if (allowedFilesCount <= 0) {
      return
    }

    const filesToUpload = validFiles.slice(0, Math.max(0, allowedFilesCount))

    this.files.value = [...this.files.value, ...filesToUpload].slice(0, this.filesLength.value)

    if (filesToUpload.length === 0) {
      return
    }

    filesToUpload.forEach((file) => {
      file.isUploading = true
    })

    try {
      const { files: uploadedFiles } = await uploadFiles({
        authKey: this.isPreviewMode ? 'preview' : authKey,
        files: filesToUpload,
        questionId,
        isPreview: this.isPreviewMode,
      })

      const transformedFiles = this.transformFiles(uploadedFiles).slice(0, this.filesLength.value)

      // find uploading files and replace them with uploaded
      const finalFiles = this.files.value.map((file) => {
        const uploadedFile = transformedFiles.find(f => f.name?.toLowerCase?.() === file.name?.toLowerCase?.())
        if (uploadedFile) {
          return uploadedFile
        }
        return file
      })

      this.files.value = finalFiles
    }
    catch (error) {
      // Handle 413 error specifically
      if (error.status === 413) {
        // Remove all files that are still uploading
        this.files.value = this.files.value.filter(file => !file.isUploading)
        // Show error message
        this.showTemporaryError(this.t('Произошла ошибка, попробуйте ещё раз'))
      }
      else {
        // For other errors, just show generic error
        this.showTemporaryError(this.t('Произошла ошибка при загрузке файлов'))
        // Remove uploading files
        this.files.value = this.files.value.filter(file => !file.isUploading)
      }
      console.error('File upload error:', error)
    }
  }

  transformFiles(files) {
    return files.map((file) => {
      const isImage = file.type === 'picture' || file.type === 'image'

      let picture = file.image ? `${getBaseAssetsUrl()}${file.image}` : null

      if (isImage) {
        picture = file.link ? `${getBaseAssetsUrl()}${file.link}` : null
      }

      const link = file.link ? `${getBaseAssetsUrl()}${file.link}` : null

      return {
        ...file,
        id: file.id,
        name: file.origin_name || file.name,
        originalName: file.origin_name || file.name,
        type: file.type,
        link,
        picture,
        isUploading: false,
      }
    })
  }

  getData() {
    const files = toValue(this.files)
    const data = {
      detail_item: files?.map?.(file => file.id),
    }

    if (this.commentController.enabled) {
      data.answer = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    return this.files.value.map(file => file.name).join(', ')
  }

  get hasValue() {
    if (this.hasPreviousAnswer) {
      return true
    }
    return false
  }

  setAllAsTouched() {
    this.touched.value = true
    if (this.commentController.enabled) {
      this.commentController.touched.value = true
    }
  }

  resetFields() {
    this.touched.value = false
    this.error.value = null
    this.files.value = []
    if (this.commentController.enabled) {
      this.commentController.touched.value = false
      this.commentController.error.value = null
    }
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip && this.skipped.value)
        return ''

      return this.files.value.map(file => file.name).join(', ')
    })
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // File upload questions only support BEHAVIOR_ALWAYS
    return condition.behavior === BEHAVIOR_ALWAYS
  }
}
