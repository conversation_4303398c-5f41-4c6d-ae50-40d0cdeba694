import {
  BE<PERSON><PERSON><PERSON>_ALWAYS,
  BEHAVIOR_MISS,
  BEHAVIOR_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, nextTick, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { VariantsController } from '../controllers/VariantsController'
import { BaseQuestion } from './BaseQuestion'
import { CLASSIFIER_QUESTION, VARIANTS_QUESTION } from './types'

export class VariantsQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.t = store.t
    this.selectedLang = store.selectedLang
    this.data = data

    this.previousAnswerItems = JSON.parse(this.previousAnswer?.detail_item || '[]')
    this.hasPreviousSelfVariant = this.previousAnswer?.is_self_variant === 1
    this.previousSelfVariantValue = this.previousAnswer?.self_variant || ''
    this.commentRequiredForAllVariants = ref(data.comment_required === 1)
    this.selfVariantCommentRequired = ref(data.self_variant_comment_required === 1)
    this.commentRequiredError = ref(null)
    this.detailAnswers = ref(data.detail_answers || [])
    this.variantsWithFiles = ref(!!data.variants_with_files)

    this.description = computed(() => {
      return this.translations.value.description || data.description
    })

    this.skip = ref(data.skip)

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || this.t('Затрудняюсь ответить')
    })
    this.skipped = ref(data.skipped === 1)

    this.hasComment = ref(data.comment_enabled === 1)
    this.isCommentRequired = ref(data.comment_required === 1)

    this.variantsType = ref(data.variantsType)
    this.textFieldParam = ref(data.textFieldParam || {})
    this.placeholderText = ref(data.placeholderText)
    this.hasCustomField = ref(data.isHaveCustomField)
    this.dropdown = ref(data.dropdownVariants === 1)
    this.showTooltips = ref(data.show_tooltips === 1)
    this.selfVariantText = ref(data.self_variant_text)
    this.selfVariantNothing = ref(data.self_variant_nothing === 1)
    this.selfVariantCommentRequired = ref(data.self_variant_comment_required === 1)
    this.selfVariantMinLength = ref(data.selfVariantParam?.min || 0)
    this.selfVariantMaxLength = ref(data.selfVariantParam?.max || 1000)
    this.selfVariantPlaceholderText = ref(data.selfVariantPlaceholderText || '')
    this.selfVariantDescription = ref(data.self_variant_description || '')
    this.minChooseVariants = ref(data.min_choose_variants)
    this.maxChooseVariants = ref(data.max_choose_variants)
    this.randomVariantsOrder = ref(data.random_variants_order === 1)

    if (!this.hasDonor) {
      this.variantsController = new VariantsController({
        isRequired: toValue(this.isRequired),
        enabled: computed(() => !this.skipped.value),
        label: this.label,
        variants: toValue(this.detailAnswers),
        variantsType: toValue(this.variantsType),
        variantsWithFiles: toValue(this.variantsWithFiles),
        previousAnswerItems: this.previousAnswerItems,
        previousAnswerHasSelfVariant: this.hasPreviousSelfVariant,
        textFieldValue: this.previousSelfVariantValue,
        textFieldParam: toValue(this.textFieldParam),
        placeholderText: toValue(this.placeholderText),
        translations: this.translations,
        hasCustomField: toValue(this.hasCustomField),
        dropdown: toValue(this.dropdown),
        showTooltips: toValue(this.showTooltips),
        selfVariantText: toValue(this.selfVariantText),
        selfVariantNothing: toValue(this.selfVariantNothing),
        selfVariantCommentRequired: toValue(this.selfVariantCommentRequired),
        selfVariantMinLength: toValue(this.selfVariantMinLength),
        selfVariantMaxLength: toValue(this.selfVariantMaxLength),
        selfVariantPlaceholderText: toValue(this.selfVariantPlaceholderText),
        selfVariantDescription: toValue(this.selfVariantDescription),
        isCustomFieldChecked: data.isCustomFieldChecked,
        skipped: this.skipped,
        minChooseVariants: toValue(this.minChooseVariants),
        maxChooseVariants: toValue(this.maxChooseVariants),
        randomVariantsOrder: toValue(this.randomVariantsOrder),
      })
    }
    else {
      // Initialize with empty variants for donor case
      this.variantsController = new VariantsController({
        isRequired: toValue(this.isRequired),
        enabled: computed(() => !this.skipped.value),
        label: this.label,
        variants: [], // Start with empty variants
        variantsType: toValue(this.variantsType),
        variantsWithFiles: toValue(this.variantsWithFiles),
        previousAnswerItems: this.previousAnswerItems,
        previousAnswerHasSelfVariant: this.hasPreviousSelfVariant,
        textFieldValue: this.previousSelfVariantValue,
        textFieldParam: toValue(this.textFieldParam),
        placeholderText: toValue(this.placeholderText),
        translations: this.translations,
        hasCustomField: toValue(this.hasCustomField),
        dropdown: toValue(this.dropdown),
        showTooltips: toValue(this.showTooltips),
        selfVariantText: toValue(this.selfVariantText),
        selfVariantNothing: toValue(this.selfVariantNothing),
        selfVariantCommentRequired: toValue(this.selfVariantCommentRequired),
        selfVariantMinLength: toValue(this.selfVariantMinLength),
        selfVariantMaxLength: toValue(this.selfVariantMaxLength),
        selfVariantPlaceholderText: toValue(this.selfVariantPlaceholderText),
        selfVariantDescription: toValue(this.selfVariantDescription),
        isCustomFieldChecked: data.isCustomFieldChecked,
        skipped: toValue(this.skipped),
        minChooseVariants: toValue(this.minChooseVariants),
        maxChooseVariants: toValue(this.maxChooseVariants),
        randomVariantsOrder: toValue(this.randomVariantsOrder),
      })

      const donorVariantsInitialized = ref(false)
      // Watch donor variants changes
      watch(this.selectedDonorVariants, (newDonorVariants) => {
        if (!this.hasDonor)
          return

        let transformedVariants = []

        if (this.donorQuestion.value?.type === VARIANTS_QUESTION) {
          transformedVariants = this.getTransformedDonorVariants(newDonorVariants)
        }
        else if (this.donorQuestion.value?.type === CLASSIFIER_QUESTION) {
          transformedVariants = this.getClassifierDonorVariants(newDonorVariants)
        }

        this.variantsController.variants.value = this.variantsController.getTransformedVariants(transformedVariants)

        let currentAnswers = this.variantsController.preparePreviousAnswers()

        if (donorVariantsInitialized.value) {
          currentAnswers = this.variantsController.answers.value
        }

        const filteredAnswers = this.filterAnswersWithAvailableVariants(currentAnswers, transformedVariants)
        this.variantsController.answers.value = filteredAnswers
        donorVariantsInitialized.value = true
      }, { immediate: true })
    }

    this.variantsWithCommentRequired = computed(() => {
      return this.variantsController.variantsWithCommentRequired.value
    })

    this.isDropdown = toValue(this.dropdown)
    this.canSelectMultiple = computed(() => toValue(this.variantsType) === 1)

    this.commentController = new CommentController({
      enabled: toValue(this.hasComment),
      required: computed(() => {
        if (toValue(this.commentRequiredForAllVariants))
          return true
        const answers = toValue(this.variantsController.answers)
        if (!Array.isArray(answers)) {
          const variant = toValue(this.variantsController.variants).find(v => v.id === answers)
          return toValue(variant?.comment_required)
        }
        const variants = toValue(this.variantsController.variants)
        const activeVariants = answers.map(a => variants.find(v => v.id === a.id))
        return activeVariants.some(v => !!v && toValue(v?.comment_required))
      }),
      skipped: this.skipped,
      value: this.previousAnswer?.answer || '',
      placeholderText: data.placeholderText,
      minLength: data.textFieldParam?.min || 0,
      maxLength: data.textFieldParam?.max || 1000,
      title: data.comment_label,
      translations: this.translations,
    })

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return toValue(this.touched) && !toValue(this.isValid)
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    this.selectedVariants = computed(() => {
      return this.variantsController.answers.value
    })

    watch(this.selectedVariants, (variants) => {
      const normalizedVariants = Array.isArray(variants) ? variants : [variants]

      if (!this.canSelectMultiple.value && normalizedVariants.length >= 1 && !toValue(this.hasComment)) {
        if (!this.variantsController.isSelfVariantChecked.value) {
          this.canMoveToNextQuestion.value = true
        }
      }
    })

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: true,
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    // Add watchers for interaction tracking
    watch([this.skipped, this.selectedVariants], ([skipped, variants]) => {
      const hasSelectedVariants = Array.isArray(variants) ? variants.length > 0 : !!variants
      const hasSelfVariant = this.variantsController.isSelfVariantChecked.value

      if (skipped || hasSelectedVariants || hasSelfVariant) {
        this.markInteracted()
      }
    }, { immediate: true })
  }

  /**
   * Filters answers to only include those that have corresponding variants
   * Handles answers in different formats safely
   * @param {*} currentAnswers - Current answers (can be array, single value, objects with id, etc.)
   * @param {Array} availableVariants - Array of available variants with id property
   * @returns {*} Filtered answers in the same format as input
   */
  filterAnswersWithAvailableVariants(currentAnswers, availableVariants) {
    if (!currentAnswers || !availableVariants) {
      return currentAnswers
    }

    const availableVariantIds = new Set(
      availableVariants.map((variant) => {
        if (typeof variant === 'object') {
          return variant.id
        }
        return variant
      }).filter(id => id !== undefined && id !== null),
    )

    if (Array.isArray(currentAnswers)) {
      return currentAnswers.filter((answer) => {
        if (!answer)
          return false

        const answerId = typeof answer === 'object' ? answer.id : answer
        return availableVariantIds.has(answerId)
      })
    }

    if (currentAnswers) {
      const answerId = typeof currentAnswers === 'object' ? currentAnswers.id : currentAnswers
      return availableVariantIds.has(answerId) ? currentAnswers : null
    }

    return currentAnswers
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Get current answers from variants controller
    const answers = toValue(this.variantsController.answers)

    // Convert to array if not already
    const normalizedAnswers = Array.isArray(answers) ? answers : [answers]

    // Handle dropdown case where answers are objects
    const selectedIds = normalizedAnswers.map(answer =>
      typeof answer === 'object' ? answer.id : answer,
    ).filter(id => !!id) // Filter out empty/null values

    // Special handling for self variant (comes as 0 in condition.variants)
    const hasSelfVariant = this.variantsController.isSelfVariantChecked.value

    switch (condition.behavior) {
      case BEHAVIOR_MISS:
        // Check if question was skipped or has no selection
        return this.skipped.value || selectedIds.length === 0

      case BEHAVIOR_SELECT:
        // Check if any selected variant is in condition variants
        // Handle self variant case (0 in condition.variants means self variant)
        return selectedIds.some(id => condition.variants.includes(id))
          || (hasSelfVariant && condition.variants.includes(0))

      case BEHAVIOR_UNSELECT:
        // Check if NO selected variant is in condition variants
        // Handle self variant case
        return !selectedIds.some(id => condition.variants.includes(id))
          && !(hasSelfVariant && condition.variants.includes(0))

      case BEHAVIOR_ALWAYS:
        return true

      default:
        return false
    }
  }

  get hasGallery() {
    return toValue(this.enableGallery)
  }

  resetFields() {
    this.variantsController.resetFields()
    if (toValue(this.hasComment)) {
      this.commentController.value.value = ''
      this.commentController.touched.value = false
      this.commentController.error.value = null
    }
    this.variantsController.selfVariantCommentTouched.value = false
  }

  setAllAsTouched() {
    this.touched.value = true
    this.variantsController.touched.value = true
    this.variantsController.selfVariantCommentTouched.value = true
    if (toValue(this.hasComment)) {
      this.commentController.touched.value = true
    }
  }

  checkValidity() {
    if (this.skip && this.skipped.value)
      return true
    return this.variantsController.isValid.value
  }

  validate() {
    this.commentRequiredError.value = null

    if (this.skip && this.skipped.value)
      return true
    if (!this.touched.value)
      return true
    const variantsValid = this.variantsController.validate()
    let commentValid = true
    let answersIds = toValue(this.variantsController.answersIds)
    answersIds = Array.isArray(answersIds) ? answersIds : [answersIds]
    if (toValue(this.hasComment) && !toValue(this.commentRequiredForAllVariants) && answersIds.length > 0) {
      const variants = toValue(this.variantsController.variants)
      const activeVariants = answersIds.map(id => variants.find(v => v.id === id))
      const hasCommentRequired = activeVariants.some(v => v?.commentRequired)
      const commentHasValue = toValue(this.commentController.hasValue)

      if (hasCommentRequired && !commentHasValue) {
        const msg = this.t('Обязательное поле при выборе вариантов')
        this.commentRequiredError.value = `${toValue(msg)}:`
        commentValid = false
      }
      else {
        this.commentRequiredError.value = null
      }
    }

    if (toValue(this.hasComment) && commentValid) {
      commentValid = this.commentController.validate()
    }

    return variantsValid && commentValid
  }

  getData() {
    if (toValue(this.skip) && toValue(this.skipped)) {
      return { skipped: 1 }
    }

    const data = this.variantsController.getData()

    if (toValue(this.hasComment)) {
      data.answer = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    if (toValue(this.skip) && toValue(this.skipped))
      return this.t('Пропущено')

    const selectedVariants = this.variantsController.variants.value.filter(v => v.isChecked.value)
    return selectedVariants.map(v => v.text).join(', ')
  }

  get hasValue() {
    if (toValue(this.skip) && toValue(this.skipped))
      return true
    if (this.hasPreviousAnswer) {
      return true
    }
    return this.variantsController.hasValue
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (toValue(this.skip) && toValue(this.skipped))
        return ''

      if (toValue(this.variantsController.isTextType)) {
        return toValue(this.variantsController.textFieldValue)
      }

      let answersIds = toValue(this.variantsController.answersIds)
      answersIds = Array.isArray(answersIds) ? answersIds : [answersIds]
      const selectedVariants = this.variantsController.variants.value.filter(v => answersIds.includes(v.id))
      return selectedVariants.map((v) => {
        if (v.id === 'is_self_answer') {
          return this.variantsController.selfVariantComment.value
        }
        return toValue(v.label) || toValue(v.alternativeVariantLabel)
      }).join(', ')
    })
  }

  resolveViewLogic(rule) {
    const answers = toValue(this.variantsController.answers)
    if (!answers)
      return false

    // Convert answers to array of IDs
    const normalizedAnswers = Array.isArray(answers) ? answers : [answers]
    const selectedIds = normalizedAnswers.map(answer =>
      typeof answer === 'object' ? answer.id : answer,
    ).filter(id => !!id)

    // Check for self variant (represented as 0 in variants array)
    const hasSelfVariant = this.variantsController.isSelfVariantChecked.value

    // Check if any selected variant is in the required variants
    // or if self variant is selected and 0 is in required variants
    return selectedIds.some(id => rule.variants.includes(id))
      || (hasSelfVariant && rule.variants.includes(0))
  }

  setFieldsFromDonorVariantsController(donorVariantsController, position) {
    this.variantsController.selfVariantCommentRequired.value = donorVariantsController.selfVariantCommentRequired
    this.variantsController.selfVariantDescription.value = donorVariantsController.selfVariantDescription
    this.variantsController.selfVariantPlaceholderText.value = donorVariantsController.selfVariantPlaceholderText
    this.variantsController.selfVariantNothing.value = donorVariantsController.selfVariantNothing
    this.variantsController.hasCustomField.value = true
    this.variantsController.customVariantName.value = computed(() => toValue(donorVariantsController.selfVariantComment))
    if (position) {
      this.variantsController.selfVariantPosition.value = position
    }
  }

  clearFieldsForVariantsController() {
    this.variantsController.customVariantName.value = ''
    this.variantsController.hasCustomField.value = false
    this.variantsController.selfVariantCommentRequired.value = false
    this.variantsController.selfVariantDescription.value = ''
    this.variantsController.selfVariantPlaceholderText.value = ''
    this.variantsController.selfVariantNothing.value = false
  }

  getTransformedDonorVariants(donorVariants) {
    const donorVariantsController = this.donorQuestion.value?.variantsController

    const transformedVariants = donorVariants.map((v) => {
      // Find matching variant in detailAnswers
      const variant = this.detailAnswers.value.find(
        answer => answer.question_detail_id === v.id || answer.dictionary_element_id === v.id,
      )

      if (variant) {
        variant.id = v.id
      }

      const hasVariantDescription = toValue(variant?.description) !== null || toValue(variant?.description) !== undefined
      if (variant && !hasVariantDescription) {
        variant.description = computed(() => {
          return toValue(v.description)
        })
      }
      return variant || null
    }).filter(Boolean)

    const donorVariantsSelfAnswer = donorVariants.find(v => v.id === 'is_self_answer' || v.id === '-1')
    if (donorVariantsSelfAnswer) {
      const recipientSelfVariant = this.detailAnswers.value.find(v => this.isVariantRecipientSelfAnswer(v))

      if (recipientSelfVariant) {
        recipientSelfVariant.id = '-1'

        // @NOTE: Это поле используется в Variants.vue для определения своего варианта-реципиента
        // В случае с отображением вариантов с медиа, мы должны отображать этот вариант в самом конце
        recipientSelfVariant.isRecipientSelfAnswer = true

        if (recipientSelfVariant) {
          recipientSelfVariant.question = computed(() =>
            toValue(donorVariantsController.selfVariantComment)
            || toValue(donorVariantsSelfAnswer.label)
            || toValue(donorVariantsSelfAnswer.question),
          )
          const description = recipientSelfVariant.description

          // Получаем подсказку для своего варианта-реципиента из разных источников
          // 1. Из самого варианта-реципиента
          // 2. Из настроек донора "Подсказка для своего варианта"
          // 3. Из варианта "Свой вариант" у донора. Такой кейс возможен, если донор сам является реципиентом
          recipientSelfVariant.description = computed(() =>
            toValue(description)
            || toValue(donorVariantsController.selfVariantDescription)
            || toValue(donorVariantsSelfAnswer.description),
          )
          this.clearFieldsForVariantsController()
          transformedVariants.push(recipientSelfVariant)
        }
      }
    }

    const donorHasMedia = !!donorVariantsController.variantsWithFiles.value

    if (donorHasMedia) {
      this.variantsController.variantsWithFiles.value = true
    }

    return transformedVariants
  }

  getClassifierDonorVariants(donorVariants) {
    return donorVariants.map((v) => {
      // Find matching variant in detailAnswers
      const variant = this.detailAnswers.value.find(
        answer => answer.dictionary_element_id === v.id || answer.question_detail_id === v.id,
      )

      const hasVariantDescription = toValue(variant?.description) !== null || toValue(variant?.description) !== undefined
      if (!hasVariantDescription) {
        variant.description = computed(() => {
          return toValue(v.description)
        })
      }
      if (variant) {
        variant.id = v.id
        variant.question = this.getRecipientVariantName(variant)
      }
      return variant || null
    })
      .filter(Boolean)
      .sort((a, b) => a.position - b.position)
  }

  updateFromPreview(data) {
    if (!data)
      return

    super.updateFromPreview(data)

    const donorChanged = data.donor && data.donor !== this._previewDonor

    if (data.donor) {
      // Флаг для отслеживания id донора
      // Если id донора изменился, то мы должны сбросить выбранные варианты и поля
      this._previewDonor = data.donor
    }

    // Update base properties
    if (data.enabled !== undefined)
      this.enabled.value = data.enabled

    if (data.isRequired !== undefined)
      this.isRequired.value = data.isRequired

    if (data.label !== undefined)
      this.label.value = data.label

    if (data.skip !== undefined)
      this.skip.value = data.skip

    if (data.skip_text !== undefined)
      this._skipText.value = data.skip_text

    this.hasComment.value = !!data.comment_enabled
    this.commentRequiredForAllVariants.value = !!data.comment_required

    this.commentController.updateFromPreview({
      enabled: !!data.comment_enabled,
      title: data.comment_label,
      placeholderText: data.placeholderText,
      textFieldParam: {
        min: data.comment_minlength,
        max: data.comment_maxlength,
      },
    })

    if (data.enableGallery !== undefined)
      this.enableGallery.value = data.enableGallery

    this.galleryController.updateFromPreview({
      gallery: data.gallery,
      enabled: data.enableGallery,
    })

    // Use variantsController.updateFromPreview for variants controller updates
    this.variantsController.updateFromPreview({
      isRequired: data.isRequired,
      variants: data.detail_answers,
      variantsType: data.variantsType,
      hasCustomField: data.isHaveCustomField,
      variantsWithFiles: data.variants_with_files,
      selfVariantNothing: data.self_variant_nothing,
      dropdown: data.dropdownVariants,
      minChooseVariants: data.min_choose_variants,
      maxChooseVariants: data.max_choose_variants,
      randomVariantsOrder: data.random_variants_order,
      selfVariantMinLength: data.textFieldParam?.min,
      selfVariantMaxLength: data.textFieldParam?.max,
      selfVariantCommentRequired: data.selfVariantCommentRequired,
      textFieldParam: data.textFieldParam,
      placeholderText: data.placeholderText,
      selfVariantPlaceholderText: data.selfVariantPlaceholderText,
      selfVariantPosition: data.selfVariantPosition,
      selfVariantText: data.self_variant_text,
      selfVariantDescription: data.self_variant_description,
      showTooltips: data.show_tooltips,
    })

    if (donorChanged) {
      this.variantsController.resetAnswers()
      this.variantsController.resetFields()
    }
  }
}
