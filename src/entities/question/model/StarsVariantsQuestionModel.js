import { declOfNum } from '@shared/helpers/string'
import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, nextTick, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { VariantsController } from '../controllers/VariantsController'
import { BaseQuestion } from './BaseQuestion'

export class StarsVariantsQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.t = store.t
    this.selectedLang = store.selectedLang
    this.data = data
    this.previousRatings = JSON.parse(this.previousAnswer?.answer || '{}')
    this.previousExtra = this.previousRatings.extra || {}
    this._isExtraRequired = ref(data.extra_required === 1)
    this.isExtraRequired = computed(() => this._isExtraRequired.value)

    this._variantsType = ref(data.variantsType)
    this.variantsType = computed(() => this._variantsType.value)

    this._textFieldParam = ref(data.textFieldParam || {})
    this.textFieldParam = computed(() => this._textFieldParam.value)

    this._placeholderText = ref(data.placeholderText)
    this.placeholderText = computed(() => this._placeholderText.value)

    this._isHaveCustomField = ref(data.isHaveCustomField)
    this.isHaveCustomField = computed(() => this._isHaveCustomField.value)

    this._dropdownVariants = ref(data.dropdownVariants)
    this.dropdownVariants = computed(() => this._dropdownVariants.value)

    this._selfVariantText = ref(data.self_variant_text)
    this.selfVariantText = computed(() => this._selfVariantText.value)

    this._selfVariantNothing = ref(data.self_variant_nothing === 1)
    this.selfVariantNothing = computed(() => this._selfVariantNothing.value)

    this._selfVariantCommentRequired = ref(data.self_variant_comment_required === 1)
    this.selfVariantCommentRequired = computed(() => this._selfVariantCommentRequired.value)

    this.detailAnswers = shallowRef(data.detail_answers || [])

    this.skip = ref(data.skip)

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || this.t('Не готов(а) оценить')
    })
    this.skipped = ref(this.previousAnswer?.skipped === 1)
    this._commentEnabled = ref(data.comment_enabled === 1)
    this.commentEnabled = computed(() => this._commentEnabled.value)

    this.starsConfig = ref(data.starRatingOptions || {})

    this._color = ref(this.starsConfig.value.color || '')
    this.color = computed(() => this._color.value)

    this._count = ref(this.starsConfig.value.count || 5)
    this.count = computed(() => this._count.value)

    this._size = ref(this.starsConfig.value.size || 'md')
    this.size = computed(() => this._size.value)

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: this.enableGallery.value,
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this._labels = ref(this.starsConfig.value.labelsArray || [])
    this.labels = computed(() => {
      const labelsFromTranslation = JSON.parse(this.translations.value.labels || '[]')
      const labelsFromData = this._labels.value || []
      const allEmpty = labelsFromTranslation.every(label => !label)
      if (allEmpty) {
        return labelsFromData
      }

      const merged = []

      // Check if labelsFromTranslation is empty
      if (labelsFromTranslation.length === 0) {
        return labelsFromData
      }

      // merge labelsFromTranslation and labelsFromData. If item in labelsFromTranslation is empty, use item from labelsFromData
      for (let i = 0; i < this.count.value; i++) {
        merged.push(labelsFromTranslation[i] || labelsFromData[i])
      }

      return merged
    })
    this._size = ref(this.starsConfig.value.size || 'md')
    this.size = computed(() => this._size.value)

    this._forAllRates = ref(data.forAllRates === 1)
    this.forAllRates = computed(() => this._forAllRates.value)

    this._extraQuestionRateFrom = ref(this.starsConfig.value.extra_question_rate_from || -Infinity)
    this.extraQuestionRateFrom = computed(() => this._extraQuestionRateFrom.value)

    this._extraQuestionRateTo = ref(this.starsConfig.value.extra_question_rate_to || +Infinity)
    this.extraQuestionRateTo = computed(() => this._extraQuestionRateTo.value)

    this._showLabels = ref(data.showLabels || data.show_labels)
    this.showLabels = computed(() => this._showLabels.value)

    this._showNumbers = ref(data.showNumbers || data.show_numbers)
    this.showNumbers = computed(() => this._showNumbers.value)

    this._randomVariantsOrder = ref(data.random_variants_order)
    this.randomVariantsOrder = computed(() => this._randomVariantsOrder.value)

    this._clarifyingQuestionText = ref(data.answerText)
    this.clarifyingQuestionText = computed(() => this._clarifyingQuestionText.value)

    this.clarifyingQuestionIsEnabled = ref(true)

    this._clarifyingQuestionIsRequired = ref(data.extra_required === 1)
    this.clarifyingQuestionIsRequired = computed(() => this._clarifyingQuestionIsRequired.value)

    if (this.count.value > 5)
      this._showLabels.value = false

    this._extraQuestionVariants = shallowRef(this.detailAnswers.value.filter(v => v.is_deleted === 0 && v.extra_question === 1))
    this.extraQuestionVariants = computed(() => this._extraQuestionVariants.value)

    this.variants = shallowRef([])

    if (!this.hasDonor) {
      const filteredVariants = this.detailAnswers.value.filter(v => v.is_deleted === 0 && v.extra_question === 0)
      this.variants.value = this.prepareVariants(filteredVariants)
    }

    // Watch donor variants changes
    watch(this.selectedDonorVariants, (newDonorVariants) => {
      if (!this.hasDonor)
        return

      const filteredDetailAnswers = []

      newDonorVariants.forEach((v) => {
        if (v.id === 'is_self_answer') {
          const selfVariant = this.detailAnswers
            .value
            .find(answer => this.isVariantRecipientSelfAnswer(answer))
          if (selfVariant) {
            selfVariant.alternativeVariantLabel = computed(() => toValue(v.alternativeVariantLabel))
            selfVariant.id = '-1'
            filteredDetailAnswers.push(selfVariant)
          }
          return
        }

        const donorVariant = this.detailAnswers
          .value
          .find(answer => answer.dictionary_element_id === v.id || answer.question_detail_id === v.id)

        if (donorVariant) {
          donorVariant.id = v.id
          filteredDetailAnswers.push(donorVariant)
          donorVariant.question = this.getRecipientVariantName(donorVariant)
        }
      })

      const existingVariants = this.variants.value || []
      const newVariants = []

      filteredDetailAnswers.forEach((detailAnswer) => {
        const existingVariant = existingVariants.find(v => v.id === detailAnswer.id)

        if (existingVariant) {
          existingVariant.text.value = detailAnswer.variant || detailAnswer.question || detailAnswer.value
          existingVariant.needExtra.value = detailAnswer.need_extra

          if (detailAnswer.alternativeVariantLabel) {
            existingVariant.alternativeVariantLabel = detailAnswer.alternativeVariantLabel
          }

          newVariants.push(existingVariant)
        }
        else {
          newVariants.push(this.prepareVariant(detailAnswer, true))
        }
      })

      this.variants.value = this.sortVariants(newVariants)
    }, { immediate: true })

    // Move the existing variants watch logic after this

    this._isSkipVariant = ref(data.skip_variant === 1)
    this.isSkipVariant = computed(() => this._isSkipVariant.value)

    this.touched = ref(false)

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    this.canMoveToNextQuestion = ref(false)

    this._isCommentRequired = ref(data.comment_required)
    this.isCommentRequired = computed(() => this._isCommentRequired.value)
    this._textFieldParam = ref(data.textFieldParam || {})
    this.textFieldParam = computed(() => this._textFieldParam.value)
    this._placeholderText = ref(data.placeholderText)
    this.placeholderText = computed(() => this._placeholderText.value)

    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: toValue(this.isCommentRequired),
      skipped: toValue(this.skipped),
      value: data.textFieldValue || '',
      placeholderText: toValue(this.placeholderText),
      minLength: this.textFieldParam.value.min || 0,
      maxLength: this.textFieldParam.value.max || 1000,
      title: toValue(data.comment_label) || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })
  }

  prepareVariant(v, fromDonor = false) {
    const previousRatingRaw = this.previousRatings[v.id]
    const previousRating = Number.parseInt(previousRatingRaw)
    const hasPreviousRating = !Number.isNaN(previousRating)

    const _text = ref(v.variant || v.question || v.value)
    const variant = {
      ...v,
      persistentId: v.persistentId || v.id,
      hasPreviousRating,
      stars: ref(hasPreviousRating ? previousRating : -1),
      error: ref(null),
      touched: ref(false),
      skipped: ref(previousRatingRaw === 'null'),
      needExtra: ref(v.need_extra),
      text: computed({
        get: () => {
          const alternativeVariantLabel = toValue(v.alternativeVariantLabel)
          if (fromDonor && alternativeVariantLabel) {
            return alternativeVariantLabel
          }
          // For regular variants, use translations
          const id = toValue(v.question_detail_id || v.id)

          const translation = this.translations.value.detailLangs?.[id]

          return translation?.question || toValue(_text)
        },
        set: (value) => {
          _text.value = value
        },
      }),
    }

    // Setup watchers for stars and skipped
    this.setupStarsWatch(variant)
    this.setupSkippedWatch(variant)

    const previousExtraItems = this.previousExtra?.[v.id] || []
    let previousExtraItemsEntries = []

    const previousAnswerSelfVariantValue = previousExtraItems.self_variant

    // if is object with answers
    if (typeof previousExtraItems === 'object') {
      previousExtraItemsEntries = Object.entries(previousExtraItems).filter(([key]) => key !== 'is_self_answer')
    }

    variant.variantsController = new VariantsController({
      enabled: computed(() => {
        const variantsEnabled = !toValue(this.commentEnabled)
          && toValue(this.clarifyingQuestionText)
          && toValue(this.clarifyingQuestionIsEnabled)
          && toValue(variant.needExtra)

        if (!variantsEnabled) {
          return false
        }

        const stars = toValue(variant.stars)

        if (stars === 0 || stars === -1) {
          return false
        }

        if (toValue(this.forAllRates)) {
          return true
        }

        const ratingInTheAllowedRange = stars >= toValue(this.extraQuestionRateFrom) && stars <= toValue(this.extraQuestionRateTo)

        return ratingInTheAllowedRange
      }),
      isRequired: toValue(this.isExtraRequired),
      variants: this.extraQuestionVariants.value,
      previousAnswerHasSelfVariant: !!previousAnswerSelfVariantValue,
      previousAnswerItems: previousExtraItemsEntries.map(([_, value]) => value),
      textFieldValue: previousAnswerSelfVariantValue || previousExtraItems?.answer || '',
      variantsType: toValue(this.variantsType),
      textFieldParam: this.textFieldParam.value,
      placeholderText: this.placeholderText.value,
      translations: this.translations,
      skipped: computed(() => toValue(variant.skipped)),
      label: this.clarifyingQuestionText.value,
      hasCustomField: this.isHaveCustomField.value,
      dropdown: this.dropdownVariants.value,
      selfVariantText: this.selfVariantText.value,
      selfVariantNothing: this.selfVariantNothing.value,
      selfVariantCommentRequired: this.selfVariantCommentRequired.value,
      selfVariantMinLength: this.textFieldParam.value.min || 0,
      selfVariantMaxLength: this.textFieldParam.value.max || 1000,
      isCustomFieldChecked: this.isCustomFieldChecked,
    })

    return variant
  }

  prepareVariants(variants = [], fromDonor = false) {
    const transformedVariants = variants.map(v => this.prepareVariant(v, fromDonor))
    return this.sortVariants(transformedVariants)
  }

  sortVariants(variants) {
    if (!this.randomVariantsOrder.value) {
      return variants.sort((a, b) => a.position - b.position)
    }
    return shuffle(variants)
  }

  setupStarsWatch(variant) {
    watch(variant.stars, (newValue) => {
      if (newValue > 0) {
        variant.skipped.value = false
        this.skipped.value = false
        this.markInteracted()
      }
      const shouldMoveToNextQuestion = newValue > 0
        && this.variants.length === 1
        && !toValue(this.commentEnabled)
      if (shouldMoveToNextQuestion) {
        this.canMoveToNextQuestion.value = true
      }
      variant.touched.value = true
      this.validateVariant(variant)
    })
  }

  setupSkippedWatch(variant) {
    watch(variant.skipped, (newValue) => {
      if (newValue) {
        variant.stars.value = 0
        variant.touched.value = true
        variant.error.value = null
        this.markInteracted()
        this.validateVariant(variant)
      }
    })
  }

  resetFields() {
    this.touched.value = false
    this.variants.value.forEach((v) => {
      v.stars.value = 0
      v.error.value = null
      v.touched.value = false
      v.skipped.value = false
      if (v.variantsController) {
        v.variantsController.resetFields()
      }
    })
    this.commentController.value.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null
  }

  updateFromPreview(previewData) {
    const isRequiredChanged = this.isRequired.value !== previewData.isRequired

    super.updateFromPreview(previewData)

    if (isRequiredChanged) {
      this.resetFields()
    }

    // Update stars configuration and other fields as before
    if (previewData.starRatingOptions) {
      this._color.value = previewData.starRatingOptions.color || 'rgb(248, 205, 28)'
      this._count.value = previewData.starRatingOptions.count || 5
      this._size.value = previewData.starRatingOptions.size || 'md'
      this._extraQuestionRateFrom.value = previewData.starRatingOptions.extra_question_rate_from
      this._extraQuestionRateTo.value = previewData.starRatingOptions.extra_question_rate_to
      this._forAllRates.value = previewData.forAllRates === 1
      this._labels.value = previewData.starRatingOptions.labelsArray
    }

    this._randomVariantsOrder.value = !!previewData.random_variants_order

    // Update variants with state preservation
    if (previewData.variants) {
      // Split variants into star variants and extra variants
      const extraVariants = previewData.variants
        .filter(v => v.extra_question)
        .map(v => ({ ...v, is_deleted: 0 }))

      const starVariants = previewData.variants
        .filter(v => !v.extra_question)
        .map(v => ({ ...v, is_deleted: 0 }))

      // Update extra question variants
      this._extraQuestionVariants.value = extraVariants

      // Update other configuration fields
      this._clarifyingQuestionIsRequired.value = previewData.clarifyingQuestionRequired
      this.clarifyingQuestionIsEnabled.value = previewData.isHaveExtra
      this._clarifyingQuestionText.value = previewData.answerText

      const skipVariantChanged = previewData.skip_variant !== this.isSkipVariant.value
      if (skipVariantChanged) {
        this._isSkipVariant.value = previewData.skip_variant
        this.skipped.value = false
        this.variants.value.forEach((v) => {
          v.skipped.value = false
        })
      }

      this._isHaveCustomField.value = previewData.isHaveCustomField
      this._variantsType.value = previewData.variantsType

      // Update main variants while preserving state
      const updatedVariants = starVariants.map((newVariant) => {
        // Try to find matching existing variant by persistentId
        const existingVariant = this.variants.value.find(v =>
          v.id === newVariant.persistentId,
        )

        if (existingVariant) {
          // Update text and needExtra refs while preserving state
          existingVariant.text.value = newVariant.value
          existingVariant.needExtra.value = newVariant.need_extra

          // Update variants controller with new extra variants
          if (existingVariant.variantsController) {
            existingVariant.variantsController.updateFromPreview({
              isRequired: previewData.clarifyingQuestionRequired,
              variants: this.extraQuestionVariants.value,
              variantsType: toValue(this.variantsType),
              textFieldParam: previewData.textFieldParam || {},
              placeholderText: previewData.placeholderText,
              selfVariantPlaceholderText: previewData.placeholderText,
              label: this.clarifyingQuestionText.value,
              hasCustomField: this.isHaveCustomField.value,
              dropdown: this.dropdownVariants.value,
              selfVariantText: previewData.self_variant_text,
              selfVariantNothing: this.selfVariantNothing.value,
              selfVariantCommentRequired: this.selfVariantCommentRequired.value,
              selfVariantMinLength: previewData.textFieldParam?.min || 0,
              selfVariantMaxLength: previewData.textFieldParam?.max || 1000,
            })
          }

          return existingVariant
        }

        newVariant.id = newVariant.persistentId

        return this.prepareVariant(newVariant)
      })

      this.variants.value = this.sortVariants(updatedVariants)
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }
    if (!this.skip.value) {
      this.skipped.value = false

      this.variants.value.forEach((v) => {
        v.skipped.value = false
      })
    }
    if (previewData.skip_text !== undefined) {
      this._skipText.value = previewData.skip_text || 'Не готов(а) оценить'
    }

    // Update comment fields
    if (previewData.commentEnabled !== undefined) {
      this._commentEnabled.value = previewData.commentEnabled
    }
    if (toValue(this.commentEnabled) && this.commentController) {
      this.commentController.updateFromPreview({
        enabled: toValue(this.commentEnabled),
        required: previewData.comment_required || false,
        title: previewData.comment_label || '',
        placeholderText: previewData.placeholderText || '',
        textFieldParam: previewData.textFieldParam || { min: 0, max: 1000 },
      })
    }

    // Update gallery if enabled
    this.enableGallery.value = previewData.enableGallery || false
    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: previewData.gallery || [],
      })
    }
  }

  setAllAsTouched() {
    this.touched.value = true
    this.variants.value.forEach((v) => {
      v.touched.value = true
      if (v.variantsController) {
        v.variantsController.touched.value = true
        v.variantsController.selfVariantCommentTouched.value = true
      }
    })
    if (toValue(this.commentEnabled)) {
      this.commentController.touched.value = true
    }
  }

  validateVariant(variant) {
    if (!variant.touched.value)
      return true
    if (variant.skipped.value || this.skipped.value) {
      return true
    }

    if (!toValue(this.isRequired)) {
      variant.error.value = null
      return true
    }

    if (this.isSkipVariant && variant.skipped.value) {
      variant.error.value = null
      return true
    }

    if (variant.stars.value <= 0) {
      variant.error.value = this.t('Нужно поставить оценку')
      return false
    }

    variant.error.value = null
    return true
  }

  validate() {
    let isValid = true
    let isCommentValid = true

    if (!this.touched.value || (this.skip.value && this.skipped.value))
      return true

    this.variants.value.forEach((v) => {
      const isValidVariant = this.validateVariant(v)
      const isValidVariantsController = !v.variantsController || v.variantsController.validate()
      if (!isValidVariant || !isValidVariantsController) {
        isValid = false
      }
    })

    if (toValue(this.commentEnabled))
      isCommentValid = this.commentController.validate()

    return isValid && isCommentValid
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {
      detail_item: { extra: {} },
    }

    this.variants.value.forEach((v) => {
      if (v.skipped.value) {
        data.detail_item[v.id] = 'null'
      }
      else {
        data.detail_item[v.id] = toValue(v.stars) || 0
        if (v.variantsController && v.variantsController.hasValue) {
          const variantControllerData = v.variantsController.getData()
          data.detail_item.extra[v.id] = {}
          if (variantControllerData.textAnswer) {
            data.detail_item.extra[v.id].answer = variantControllerData.textAnswer
            return
          }

          const itemsWithoutSelfAnswer = variantControllerData.detail_item.filter(i => i !== 'is_self_answer')
          data.detail_item.extra[v.id] = [...itemsWithoutSelfAnswer]

          if (variantControllerData.self_variant) {
            data.detail_item.extra[v.id].self_variant = variantControllerData.self_variant
          }
        }
      }
    })

    if (toValue(this.commentEnabled)) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    return this.variants
      .map((v) => {
        if (v.skipped.value)
          return `${v.text.value}: Пропущено`
        const rating = v.stars.value
        return rating > 0 ? `${v.text.value}: ${rating} ${declOfNum(rating, ['звезда', 'звезды', 'звезд'])}` : ''
      })
      .filter(Boolean)
      .join(', ')
  }

  get hasValue() {
    if (this.skip.value && this.skipped.value)
      return true

    const normalizedPreviousRatings = Object.values(this.previousRatings)
    if (normalizedPreviousRatings && Array.isArray(normalizedPreviousRatings)) {
      return normalizedPreviousRatings.length > 0
    }
    return this.variants.some(v => v.hasPreviousRating || v.stars.value > 0 || v.skipped.value)
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Stars Variants question only supports BEHAVIOR_ALWAYS
    return false
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      const variants = toValue(this.variants)
      return variants
        .filter(v => !toValue(v.skipped))
        .map(v => `${v.text.value}: ${toValue(v.stars)}`)
        .join('; ')
    })
  }

  get hasGallery() {
    return toValue(this.enableGallery)
  }

  resolveViewLogic(rule) {
    const ruleVariants = (rule.variants || []).filter(v => this.variants.value.find(variant => variant.id === v.row))
    const skippedVariants = (rule.skipped || []).filter(id => this.variants.value.find(variant => variant.id === Number.parseInt(id)))

    // Group variants by row (variant id)
    const variantsByRow = ruleVariants.reduce((acc, v) => {
      acc[v.row] = acc[v.row] || []
      // col can be either a single value or an array with min/max range
      const rating = Number.parseInt(v.col)
      acc[v.row].push(rating)
      return acc
    }, {})
    const variantsKeys = Object.keys(variantsByRow)
    const variantsIds = variantsKeys.map(k => Number.parseInt(k))

    // Check each required variant (row)
    const rowsToCheck = [...new Set([...variantsIds, ...skippedVariants])]

    return rowsToCheck.every((variantId) => {
      // Find the variant in our model
      const variant = this.variants.value.find(v => v.id === variantId)

      if (!variant)
        return false

      const ruleVariantRatings = variantsByRow[variantId] || []

      if (!ruleVariantRatings)
        return true // No conditions for this variant

      const rating = toValue(variant.stars)

      const conditionIsMet = ruleVariantRatings.includes(rating)
      const isSkipped = toValue(this.skipped)
      const isVariantSkipped = toValue(variant.skipped)
      const isUnrequiredWithoutSelected = !toValue(this.isRequired) && (rating === 0 || rating === -1)

      if (isSkipped && skippedVariants.length) {
        return true
      }

      const skipConditionIsMet = skippedVariants.includes(variantId)
        && (isSkipped || isVariantSkipped || isUnrequiredWithoutSelected)

      return conditionIsMet || skipConditionIsMet
    })
  }
}
