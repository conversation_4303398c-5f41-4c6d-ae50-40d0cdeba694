import {
  B<PERSON>HA<PERSON><PERSON>_ALWAYS,
  BEHAVIOR_MISS,
  BE<PERSON>VIOR_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { prepareQuestionPreviewData } from '@shared/helpers/previewDataPreparator'
import { declOfNum } from '@shared/helpers/string'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import { computed, nextTick, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { VariantsController } from '../controllers/VariantsController'
import { BaseQuestion } from './BaseQuestion'

export class RatingScaleQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const tabletStore = useTabletStore()

    this.isTabletMode = tabletStore.isTabletMode

    const { t } = useTranslationsStore()
    this.t = t
    this._dataRef = ref(data)

    this._descriptionRef = ref('')
    this.description = computed({
      get: () => this.translations.value.description || this._dataRef.value.description,
      set: (value) => { this._descriptionRef.value = value },
    })

    this._skipRef = ref(data.skip)
    this.skip = computed({
      get: () => this._skipRef.value,
      set: (value) => { this._skipRef.value = value },
    })

    this._skipTextRef = ref(data.skip_text || '')
    this.skipText = computed({
      get: () => this.translations.value.skip_text || this._skipTextRef.value || t('Не готов(а) оценить'),
      set: (value) => { this._skipTextRef.value = value },
    })

    this.skipped = ref(this.previousAnswer?.skipped === 1)

    const starsConfig = data.starRatingOptions || {}

    this._colorRef = ref(starsConfig.color || '')
    this.color = computed({
      get: () => this._colorRef.value,
      set: (value) => { this._colorRef.value = value },
    })

    this._countRef = ref(starsConfig.count || 5)
    this.count = computed({
      get: () => this._countRef.value,
      set: (value) => { this._countRef.value = value },
    })

    this._labelsRef = ref(starsConfig.labelsArray || [])
    this.labels = computed({
      get: () => {
        const labelsFromTranslation = JSON.parse(this.translations.value.labels || '[]')
        const labelsFromData = this._labelsRef.value
        const merged = []

        if (labelsFromTranslation.length === 0)
          return labelsFromData

        for (let i = 0; i < this.count.value; i++)
          merged.push(labelsFromTranslation[i] || labelsFromData[i])

        return merged
      },
      set: (value) => { this._labelsRef.value = value },
    })

    this._extraQuestionRateFromRef = ref(starsConfig.extra_question_rate_from || -Infinity)
    this.extraQuestionRateFrom = computed({
      get: () => this._extraQuestionRateFromRef.value,
      set: (value) => { this._extraQuestionRateFromRef.value = value },
    })

    this._extraQuestionRateToRef = ref(starsConfig.extra_question_rate_to || +Infinity)
    this.extraQuestionRateTo = computed({
      get: () => this._extraQuestionRateToRef.value,
      set: (value) => { this._extraQuestionRateToRef.value = value },
    })

    this.err = ref(null)
    this.stars = ref(Number.parseInt(this.previousAnswer?.rating) || 0)
    this.variantsType = ref(data.variantsType)

    this.touched = ref(false)

    this._textFieldParamRef = ref(data.textFieldParam || {})
    this.textFieldParam = computed({
      get: () => this._textFieldParamRef.value,
      set: (value) => { this._textFieldParamRef.value = value },
    })

    this._placeholderTextRef = ref(data.placeholderText)
    this.placeholderText = computed({
      get: () => this._placeholderTextRef.value,
      set: (value) => { this._placeholderTextRef.value = value },
    })

    this._forAllRatesRef = ref(data.forAllRates)
    this.forAllRates = computed({
      get: () => this._forAllRatesRef.value,
      set: (value) => { this._forAllRatesRef.value = value },
    })

    this._clarifyingQuestionTextRef = ref(data.answerText)
    this.clarifyingQuestionText = computed({
      get: () => this._clarifyingQuestionTextRef.value,
      set: (value) => { this._clarifyingQuestionTextRef.value = value },
    })

    this._clarifyingQuestionIsRequiredRef = ref(!!data.extra_required)
    this.clarifyingQuestionIsRequired = computed({
      get: () => this._clarifyingQuestionIsRequiredRef.value,
      set: (value) => { this._clarifyingQuestionIsRequiredRef.value = value },
    })

    // Флаг, который отвечает за то, включен ли уточняющий вопрос
    // @NOTE: Используется исключительно для предпросмотра
    // В обычном режиме мы проверяем есть ли заголовок УВ (answerText)
    this.isClarifyingQuestionEnabled = ref(true)

    this._isCommentRequiredRef = ref(data.comment_required)
    this.isCommentRequired = computed({
      get: () => this._isCommentRequiredRef.value,
      set: (value) => { this._isCommentRequiredRef.value = value },
    })

    this.comment = ref(this.previousAnswer?.answer || '')

    this._hasCommentRef = ref(data.comment_enabled === 1)
    this.hasComment = computed({
      get: () => this._hasCommentRef.value,
      set: (value) => { this._hasCommentRef.value = value },
    })

    this._commentLabelRef = ref(data.comment_label || 'Ваш комментарий')
    this.commentLabel = computed({
      get: () => this._commentLabelRef.value,
      set: (value) => { this._commentLabelRef.value = value },
    })

    this._detailAnswersRef = ref(data.detail_answers || [])
    this.detailAnswers = computed({
      get: () => this._detailAnswersRef.value,
      set: (value) => { this._detailAnswersRef.value = value },
    })

    this._enableGalleryRef = ref(data.enableGallery || false)
    this.enableGallery = computed({
      get: () => this._enableGalleryRef.value,
      set: (value) => { this._enableGalleryRef.value = value },
    })

    this.canMoveToNextQuestion = ref(false)

    // Keep existing computed properties and watchers as they are
    this.starsValidation = computed(() => {
      if (this.skip.value && this.skipped.value)
        return true
      return toValue(this.isRequired) ? this.stars.value > 0 : true
    })

    this.isClarifyingQuestionVisible = computed(() => {
      if (!this.clarifyingQuestionText.value || !this.isClarifyingQuestionEnabled.value)
        return false
      if (this.skip.value && this.skipped.value)
        return false
      const stars = this.stars.value
      if (!stars)
        return false
      if (this.forAllRates.value || (stars >= this.extraQuestionRateFrom.value && stars <= this.extraQuestionRateTo.value))
        return true
      return false
    })

    this.isValid = computed(() => {
      if (this.skip.value && this.skipped.value)
        return true
      if (toValue(this.isRequired) && this.stars.value <= 0)
        return false
      if (this.hasComment.value && !toValue(this.commentController.isValid))
        return false
      if (this.isClarifyingQuestionVisible.value && !toValue(this.variantsController.isValid))
        return false
      return true
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })

    this.selectedVariants = computed(() => {
      return [this.stars.value]
    })

    // Initialize questionScreenshot for file upload and screenshot functionality
    this.questionScreenshot = data.questionScreenshot || null

    // Initialize controllers with reactive properties
    this.initializeControllers(data)

    // Keep existing watchers
    this.setupWatchers()
  }

  // Move controller initialization to separate method for clarity
  initializeControllers(data) {
    this.commentController = new CommentController({
      enabled: this.hasComment.value,
      required: this.isCommentRequired.value,
      skipped: this.skipped.value,
      value: this.previousAnswer?.answer || '',
      placeholderText: this.placeholderText.value,
      minLength: this.textFieldParam.value?.min || 0,
      maxLength: this.textFieldParam.value?.max || 1000,
      title: data.comment_label,
      translations: this.translations,
    })

    this.variantsController = new VariantsController({
      isRequired: this.clarifyingQuestionIsRequired.value,
      enabled: computed(() => this.isClarifyingQuestionVisible.value),
      label: computed(() => this.clarifyingQuestionText.value),
      variants: this.detailAnswers.value.map(v => ({
        ...v,
        isChecked: false,
      })),
      previousAnswerItems: JSON.parse(this.previousAnswer?.detail_item || '[]'),
      previousAnswerHasSelfVariant: this.previousAnswer?.is_self_variant === 1,
      textFieldValue: this.previousAnswer?.answer || this.previousAnswer?.self_variant || '',
      variantsType: this.variantsType.value,
      textFieldParam: this.textFieldParam.value,
      placeholderText: this.placeholderText.value,
      selfVariantPlaceholderText: this.placeholderText.value,
      translations: this.translations,
      hasCustomField: data.isHaveCustomField,
      dropdown: data.dropdownVariants,
      selfVariantText: data.self_variant_text,
      selfVariantNothing: data.self_variant_nothing === 1,
      selfVariantCommentRequired: data.self_variant_comment_required === 1,
      selfVariantMinLength: data.textFieldParam?.min || 0,
      selfVariantMaxLength: data.textFieldParam?.max || 1000,
      isCustomFieldChecked: data.isCustomFieldChecked,
      skipped: this.skipped.value,
      questionScreenshot: this.questionScreenshot,
      questionId: this.questionId,
      isPreviewMode: this.isPreviewMode,
      previousAnswer: this.previousAnswer,
    })

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: this.enableGallery.value,
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped.value,
      translations: this.translations,
    })
  }

  // Keep existing methods as they are...
  setupWatchers() {
    watch(this.stars, (newValue) => {
      if (newValue > 0) {
        this.skipped.value = false
        this.markInteracted()
      }
      this.touched.value = true
      this.validateRating()
    })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    watch([
      this.stars,
      this.isClarifyingQuestionVisible,
      this.variantsController.selectedVariants,
    ], ([rating, isClarifyingQuestionVisible]) => {
      if (this.hasComment.value) {
        // If comment is enabled, don't auto-move
        this.canMoveToNextQuestion.value = false
        return
      }

      // For simple rating without clarifying question and comments
      if (!isClarifyingQuestionVisible) {
        this.canMoveToNextQuestion.value = rating > 0
      }
      else {
        this.canMoveToNextQuestion.value = false
      }
    })

    watch(this.variantsController.answers, (answers) => {
      const isSelfVariantChecked = Array.isArray(answers) ? answers.includes('is_self_answer') : answers === 'is_self_answer'
      if (this.variantsController.isSingle.value && !isSelfVariantChecked && !this.isTabletMode) {
        this.canMoveToNextQuestion.value = true
      }
    })

    watch(this.stars, (newValue) => {
      this.emit('change-answer')
      if (newValue > 0 && this.skip.value) {
        this.skipped.value = false
      }
      if (this.hasComment.value)
        return
      if (this.clarifyingQuestionText.value) {
        if (newValue < 5)
          return
        if (this.forAllRates)
          return
      }

      this.emit('get-answer')
    })
  }

  resetFields() {
    this.touched.value = false
    this.err.value = null
    this.stars.value = 0
    this.comment.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null
  }

  setAllAsTouched() {
    this.touched.value = true

    if (this.hasComment.value) {
      this.commentController.touched.value = true
    }
    if (this.isClarifyingQuestionVisible.value) {
      this.variantsController.touched.value = true
      if (this.variantsController.isSelfVariantChecked.value) {
        this.variantsController.selfVariantCommentTouched.value = true
      }
    }
  }

  checkValidity() {
    if (this.skip.value && this.skipped.value)
      return true
    if (this.hasComment.value && this.isCommentRequired.value && !this.commentController.isValid.value)
      return false
    if (!this.starsValidation.value)
      return false

    if (this.isClarifyingQuestionVisible.value && !this.variantsController.isValid.value)
      return false
    return true
  }

  validateRating() {
    this.err.value = null
    if (!this.touched.value)
      return true

    if (!toValue(this.isRequired)) {
      this.err.value = null
      return true
    }

    if (this.skip.value && this.skipped.value) {
      this.err.value = null
      return true
    }

    if (this.stars.value <= 0) {
      this.err.value = this.t('Нужно поставить оценку')
      return false
    }

    if (this.stars.value > 0) {
      this.err.value = null
      return true
    }
  }

  validate() {
    this.validateRating()
    if (this.hasComment.value)
      this.commentController.validate()
    if (this.isClarifyingQuestionVisible.value)
      this.variantsController.validate()

    return this.checkValidity()
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {}

    if (this.stars.value > 0) {
      data.rating = this.stars.value
    }

    if (this.hasComment.value) {
      data.answer = toValue(this.commentController.value)
    }

    if (this.isClarifyingQuestionVisible.value) {
      const variantsData = this.variantsController.getData()
      data.detail_item = variantsData.detail_item
      if (variantsData.self_variant) {
        data.self_variant = variantsData.self_variant
      }
      if (variantsData.textAnswer) {
        data.answer = variantsData.textAnswer
      }
      if (variantsData.clarifyingQuestionScreenshots) {
        data.clarifyingQuestionScreenshots = variantsData.clarifyingQuestionScreenshots
      }
      if (variantsData.clarifyingQuestionAttachmentIds) {
        data.clarifyingQuestionAttachmentIds = variantsData.clarifyingQuestionAttachmentIds
      }
      if (variantsData.clarifyingQuestionScreenshotIds) {
        data.clarifyingQuestionScreenshotIds = variantsData.clarifyingQuestionScreenshotIds
      }
    }

    return data
  }

  getAnswer() {
    const count = this.stars.value

    if (!count)
      return ''

    return `${count} ${declOfNum(count, ['звезда', 'звезды', 'звезд'])}`
  }

  get hasValue() {
    if (this.skip.value && this.skipped.value)
      return true
    const hasPreviousRating = this.previousAnswer?.rating !== undefined
    return hasPreviousRating || this.stars.value > 0
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Get current rating value from reactive stars variable
    const rating = this.stars.value

    switch (condition.behavior) {
      case BEHAVIOR_MISS:
        // Check if question was skipped or has no rating
        return this.skipped.value || rating <= 0

      case BEHAVIOR_SELECT:
        // Check if current rating is in selected variants
        return condition.variants.includes(rating)

      case BEHAVIOR_UNSELECT:
        // Check if current rating is NOT in selected variants
        return !condition.variants.includes(rating)

      case BEHAVIOR_ALWAYS:
        return true

      default:
        return false
    }
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      const rating = this.stars.value
      return rating > 0 ? `${rating}` : ''
    })
  }

  /**
   * Resolves view logic condition for rating scale question
   * @param {object} rule - The view logic rule to check
   * @returns {boolean} Whether the condition is met
   */
  resolveViewLogic(rule) {
    const rating = this.stars.value
    if (!rating)
      return false

    // Check if current rating is in the required variants
    return rule.variants?.includes(rating) ?? false
  }

  get hasGallery() {
    return toValue(this.enableGallery)
  }

  updateFromPreview(previewData) {
    const preparedData = this.preparePreviewData(previewData)

    // Call parent update first
    super.updateFromPreview(preparedData)

    // Update stars configuration
    if (preparedData.starRatingOptions) {
      this._extraQuestionRateFromRef.value = preparedData.starRatingOptions.extra_question_rate_from
      this._extraQuestionRateToRef.value = preparedData.starRatingOptions.extra_question_rate_to
      this._colorRef.value = preparedData.starRatingOptions.color
      this._countRef.value = preparedData.starRatingOptions.count
      this._labelsRef.value = preparedData.starRatingOptions.labelsArray || []
    }

    // Update clarifying question fields
    if (preparedData.clarifyingQuestionRequired !== undefined) {
      this._clarifyingQuestionIsRequiredRef.value = preparedData.clarifyingQuestionRequired
    }
    if (preparedData.clarifyingQuestionEnabled !== undefined) {
      this.isClarifyingQuestionEnabled.value = preparedData.clarifyingQuestionEnabled
    }
    if (preparedData.answerText !== undefined) {
      this._clarifyingQuestionTextRef.value = preparedData.answerText
    }

    if (preparedData.forAllRates !== undefined) {
      this._forAllRatesRef.value = preparedData.forAllRates
    }

    // Update skip-related fields
    if (preparedData.skip !== undefined) {
      this._skipRef.value = preparedData.skip
    }
    if (!this.skip.value) {
      this.skipped.value = false
    }
    if (preparedData.skip_text !== undefined) {
      this._skipTextRef.value = preparedData.skip_text
    }

    // Update comment fields
    if (preparedData.comment?.enabled !== undefined) {
      this._hasCommentRef.value = preparedData.comment.enabled
    }
    if (this.hasComment.value && this.commentController) {
      this.commentController.updateFromPreview(preparedData.comment || {})
    }

    const ratingIsSet = this.stars.value > 0
    const countIsLessThanStars = this.count.value < this.stars.value
    if (countIsLessThanStars && ratingIsSet) {
      this.stars.value = this.count.value
    }

    // Update gallery if enabled
    this._enableGalleryRef.value = preparedData.enableGallery

    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: preparedData.gallery || [],
      })
    }

    if (preparedData.variantsType !== undefined) {
      this.variantsType.value = preparedData.variantsType
    }

    if (preparedData.clarifyingQuestionRequired !== undefined) {
      this.clarifyingQuestionIsRequired.value = preparedData.clarifyingQuestionRequired
    }

    // Update variants controller if present
    if (this.variantsController) {
      this.variantsController.updateFromPreview({
        isRequired: this.clarifyingQuestionIsRequired.value,
        variantsType: preparedData.variantsType,
        hasCustomField: preparedData.isHaveCustomField,
        variants: preparedData.variants || [],
        textFieldParam: preparedData.textFieldParam || { min: 0, max: 999 },
        placeholderText: preparedData.placeholderText,
        dropdown: preparedData.dropdownVariants,
        label: preparedData.answerText,
        selfVariantText: preparedData.self_variant_text,
        selfVariantPlaceholderText: preparedData?.comment?.placeholderText,
        questionScreenshot: preparedData.questionScreenshot,
      })
    }
  }

  preparePreviewData(previewData) {
    return prepareQuestionPreviewData(previewData)
  }
}
