import {
  BEHAVIOR_MISS,
  BEHAVIOR_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'

export class DistributionScaleQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    this.skip = ref(data.skip)
    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || toValue(this.t('Затрудняюсь ответить'))
    })

    this.previousAnswerItems = JSON.parse(this.previousAnswer?.answer || '{}')

    this.skipped = ref(this.previousAnswer?.skipped === 1)

    this._scaleConfig = ref(data.scaleRatingSetting || {})
    this.scaleConfig = computed(() => this._scaleConfig.value)

    this._isSkipVariant = ref(data.skip_variant === 1)
    this.isSkipVariant = computed(() => this._isSkipVariant.value)

    this.isRequired = ref(data.isRequired)

    this.withoutRest = ref(data.variantsType === 1)

    this.error = ref(null)

    this._randomVariantsOrder = ref(data.random_variants_order === 1)
    this.randomVariantsOrder = computed(() => this._randomVariantsOrder.value)
    this._requiredForAllRates = ref(data.forAllRates === 1)
    this.requiredForAllRates = computed(() => this._requiredForAllRates.value)
    this.error = ref(null)
    this.detailAnswers = data.detail_answers || []
    this.variants = shallowRef([])
    this.scaleEnd = computed(() => this.scaleConfig.value.end || 100)

    this._indicatorText = ref(data.self_variant_text)
    this.indicatorText = computed(() => this.translations.value.self_variant_text || this._indicatorText.value || '')

    if (this.isPreviewMode && !this.detailAnswers.length) {
      this.detailAnswers = [{ id: '0', value: '', is_deleted: 0 }]
    }
    const preparedVariants = this.getPreparedVariants(this.detailAnswers)

    this.variants.value = preparedVariants
    this.variants.value.forEach((variant) => {
      this.setupVariantWatchers(variant)
    })

    this.touched = ref(false)

    this.textFieldParam = data.textFieldParam || {}
    this.placeholderText = data.placeholderText

    this.commentController = new CommentController({
      enabled: data.comment_enabled,
      required: data.comment_required,
      skipped: this.skipped,
      value: data.textFieldValue || '',
      placeholderText: this.placeholderText,
      minLength: this.textFieldParam.min || 0,
      maxLength: this.textFieldParam.max || 1000,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.isOverflowed = computed(() => this.countVariants.value > this.scaleEnd.value)

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      const someVariantHasError = this.variants.value.some(v => v.error.value)
      return someVariantHasError || (this.touched.value && !this.isValid.value)
    })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        this.emit('get-answer')
      }
    })

    this.selectedVariants = computed(() => {
      return this.variants.value.filter(v => toValue(v.rating)[0] !== 0).map(v => toValue(v.rating)[0])
    })

    this.countVariants = computed(() => {
      return this.selectedVariants.value.reduce((sum, current) => sum + current, 0)
    })

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: toValue(this.enableGallery),
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (skipped) => {
      if (skipped) {
        // If question is skipped, we can move to next question
        this.canMoveToNextQuestion.value = true
      }
    })
  }

  setupVariantWatchers(variant) {
    watch(variant.rating, () => {
      this.markInteracted()
    })
    watch(variant.skipped, (v) => {
      if (v) {
        variant.rating.value = [0]
        variant.error.value = null
        this.markInteracted()
      }
    })
  }

  /**
   * Подготавливает варианты для отображения в шкале
   * @param {object[]} rawVariants необработанные варианты.
   * @returns {object[]} Массив подготовленных вариантов
   */
  getPreparedVariants(rawVariants = []) {
    const filteredVariants = rawVariants.filter(v => v.is_deleted === 0)
    const transformedVariants = filteredVariants.map(v => this.prepareVariant(v))
    return this.sortVariants(transformedVariants)
  }

  /**
   * Подготавливает один вариант для отображения
   * @param {object} variant необработанный вариант
   * @returns {object} Подготовленный вариант
   */
  prepareVariant(variant) {
    const _text = ref(variant.variant || variant.value || variant.question)
    const transformedVariant = {
      ...variant,
      rating: ref([0]),
      error: ref(null),
      invalid: ref(false),
      skipped: ref(false),
      text: computed({
        get: () => {
          const translation = toValue(this.translations).detailLangs?.[variant.id]
          return translation?.question || _text.value
        },
        set: (value) => {
          _text.value = value
        },
      }),
    }

    if (this.previousAnswerItems[variant.id]) {
      const previousAnswer = this.previousAnswerItems[variant.id]
      const numRating = Number(previousAnswer)
      transformedVariant.rating.value = [numRating || 0]

      if (previousAnswer === 'null') {
        transformedVariant.skipped.value = true
      }
    }

    return transformedVariant
  }

  /**
   * Сортирует варианты по позиции
   * @param {object[]} variants варианты для сортировки
   * @returns {object[]} Отсортированные варианты
   */
  sortVariants(variants) {
    if (this.randomVariantsOrder.value) {
      return shuffle(variants)
    }
    return variants.sort((a, b) => a.position - b.position)
  }

  resetFields() {
    this.touched.value = false
    this.variants.value.forEach((v) => {
      v.rating.value = [0]
      v.error.value = null
    })
    this.commentController.value.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null
  }

  setAllAsTouched() {
    this.touched.value = true
    if (toValue(this.commentController.enabled)) {
      this.commentController.touched.value = true
    }
  }

  validate() {
    this.commentController.error.value = null
    this.error.value = null
    this.variants.value.forEach(v => v.error.value = null)
    this.variants.value.forEach(v => v.invalid.value = false)

    if (this.skip.value && this.skipped.value)
      return true

    if (!this.touched.value)
      return true

    this.commentController.validate()
    const commentIsValid = toValue(this.commentController.isValid)

    if (!this.isRequired.value && !this.countVariants.value) {
      return commentIsValid
    }
    else {
      let variantsIsValid = true

      if (this.requiredForAllRates.value) {
        this.variants.value.forEach((v) => {
          if (v.rating.value[0] === 0) {
            v.error.value = this.t('Нужно выбрать значение')
            variantsIsValid = false
          }
        })
      }
      else if (!this.countVariants.value) {
        this.error.value = this.t('Нужно поставить оценку хотя бы одному варианту')
        variantsIsValid = false
      }

      if (!variantsIsValid) {
        return false
      }
      else {
        const withoutRest = toValue(this.withoutRest)
        const withRest = this.countVariants.value < this.scaleEnd.value
        const isOverflowed = toValue(this.isOverflowed)
        if (withoutRest && withRest) {
          this.error.value = this.t('Нужно распределить {count} баллов', { count: this.scaleEnd.value })
          return false
        }
        else if (isOverflowed) {
          this.error.value = this.t('Нужно распределить {count} баллов', { count: this.scaleEnd.value })
          this.variants.value.forEach(v => v.invalid.value = true)
          return false
        }
      }
      return commentIsValid
    }
  }

  getData() {
    if (this.skip.value && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {}

    data.rating = {}
    this.variants.value.forEach((v) => {
      const ratingValue = toValue(v.rating)[0]
      const isSkipped = toValue(v.skipped)
      data.rating[v.id] = isSkipped ? 'null' : ratingValue
    })

    if (toValue(this.commentController.enabled)) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  getAnswer() {
    return this.variants.value
      .map(v => toValue(v.rating) !== 0 ? `${v.value}: ${toValue(v.rating)}` : '')
      .filter(Boolean)
      .join(', ')
  }

  get hasValue() {
    const hasPreviousAnswer = !!this.previousAnswer
    return hasPreviousAnswer
  }

  checkCondition(condition) {
    if (super.checkCondition(condition))
      return true

    const scaleValue = this.rating.value[0]
    const variants = condition.variants

    let minMax = []

    if (variants.length === 2) {
      minMax = variants
    }

    if (condition.behavior === BEHAVIOR_MISS) {
      return scaleValue === 0
    }

    if (condition.behavior === BEHAVIOR_SELECT && minMax.length === 2) {
      return scaleValue >= minMax[0] && scaleValue <= minMax[1]
    }

    if (condition.behavior === BEHAVIOR_UNSELECT && minMax.length === 2) {
      return scaleValue < minMax[0] || scaleValue > minMax[1]
    }

    return false
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip.value && this.skipped.value)
        return ''

      return this.variants.value
        .map(v => `${v.text.value}: ${toValue(v.rating)[0]}`)
        .join('; ')
    })
  }

  /**
   * Resolves view logic condition for scale question
   * @param {object} rule - The view logic rule to check
   * @returns {boolean} Whether the condition is met
   */
  resolveViewLogic(rule) {
    const ruleVariants = rule.variants || []
    const skippedRows = (rule.skipped || []).map(row => row.toString())

    // Group variants by row
    const variantsByRow = ruleVariants.reduce((acc, v) => {
      acc[v.row] = acc[v.row] || []
      const range = Array.isArray(v.col) ? v.col : [v.col]

      acc[v.row].push(range)
      return acc
    }, {})

    // Check each row that has conditions (both regular and skipped)
    const rowsToCheck = [...new Set([...Object.keys(variantsByRow), ...skippedRows])]

    return rowsToCheck.every((variantRowId) => {
      // Find the corresponding row in our model
      const variant = this.variants.value.find(v => Number.parseInt(v.id) === Number.parseInt(variantRowId))
      if (!variant)
        return false

      const range = variantsByRow[variantRowId]

      const rating = variant.rating.value[0]

      const conditionIsMet = range ? range.some(r => rating >= r[0] && rating <= r[1]) : false
      const skippedConditionIsMet = skippedRows.includes(variantRowId) && (toValue(variant.skipped) || toValue(this.skipped))

      return conditionIsMet || skippedConditionIsMet
    })
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    // Update comment controller
    this.commentController.updateFromPreview({
      enabled: !!previewData.commentEnabled,
      required: !!previewData.comment_required,
      title: previewData.comment_label || '',
      placeholderText: previewData.placeholder_text || '',
      textFieldParam: {
        min: previewData.textFieldParam?.min || 0,
        max: previewData.textFieldParam?.max || 1000,
      },
    })

    // Update scale configuration
    if (previewData.scaleRatingSetting) {
      const scaleSettingsChanged = JSON.stringify(this._scaleConfig.value) !== JSON.stringify(previewData.scaleRatingSetting)
      this._scaleConfig.value = previewData.scaleRatingSetting

      if (scaleSettingsChanged) {
        this.resetFields()
      }
    }

    this.withoutRest.value = previewData.variantsType === 1
    this._indicatorText.value = previewData.self_variant_text

    const skipIsChanged = this.skip.value !== previewData.skip
    const skipVariantIsChanged = this.isSkipVariant.value !== !!previewData.skip_variant

    this._isSkipVariant.value = !!previewData.skip_variant
    this._randomVariantsOrder.value = previewData.random_variants_order
    this._requiredForAllRates.value = !!previewData.requiredForAll

    // Update variants with state preservation
    const updatedVariants = previewData.variants.map((newVariant, index) => {
      // Try to find matching existing variant by persistentId
      const existingVariant = this.variants.value.find(v =>
        v.id === newVariant.persistentId,
      )

      if (existingVariant) {
        // Update text while preserving state
        existingVariant.text.value = newVariant.value
        existingVariant.position = newVariant.position || index
        return existingVariant
      }

      newVariant.id = newVariant.persistentId

      // If no matching variant found, prepare a new one
      return this.prepareVariant(newVariant)
    })

    this.variants.value = this.sortVariants(updatedVariants)

    this.setupVariantWatchers(this.variants.value)

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }
    if (skipIsChanged || skipVariantIsChanged) {
      this.skipped.value = false
      this.variants.value.forEach(v => v.skipped.value = false)
    }

    if (previewData.skip_text) {
      this._skipText.value = previewData.skip_text
    }

    this.enableGallery.value = !!previewData.enableGallery

    // Update gallery controller
    this.galleryController.updateFromPreview({
      enabled: !!previewData.enableGallery,
      gallery: previewData.gallery || [],
    })
  }
}
