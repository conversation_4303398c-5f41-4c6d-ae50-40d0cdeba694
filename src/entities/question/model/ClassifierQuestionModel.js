import {
  <PERSON><PERSON>HA<PERSON><PERSON>_ALWAYS,
  BE<PERSON>VIOR_MISS,
  B<PERSON><PERSON><PERSON><PERSON>_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, nextTick, ref, shallowRef, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'
import { FILIALS_QUESTION } from './types'

export class ClassifierQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const store = useTranslationsStore()
    this.selectedLang = store.selectedLang
    this.t = store.t
    this.data = data

    // Convert basic fields to refs
    this._skip = ref(data.skip)
    this._skipText = ref(data.skip_text || toValue(this.t('Затрудняюсь ответить')))
    this._showTooltips = ref(data.show_tooltips === 1)
    this._dictionaryListType = ref(data.dictionary_list_type)
    this._sortType = ref(data.dictionary_sort)
    this._disableSelectCategory = ref(data.disable_select_category)
    this._isCommentRequired = ref(data.comment_required)
    this._commentEnabled = ref(data.isHaveComment)
    this._enableGallery = ref(data.enableGallery || false)

    // New refs
    this._isDropdown = ref(data.dropdownVariants)
    this.isFilials = ref(data.type === FILIALS_QUESTION)

    this._isMultipleChoice = ref(this.isFilials.value ? false : data.assessmentVariantsType)
    this._maxChooseVariants = ref(data.max_choose_variants || Infinity)
    this._textFieldParam = ref(data.textFieldParam || {})
    this._placeholderText = ref(data.placeholderText)
    this.dictionaryTree = shallowRef(data.dictionaryTree || {})

    // Convert to computed getters/setters
    // Convert to computed properties
    this.skip = computed({
      get: () => this._skip.value,
      set: (value) => { this._skip.value = value },
    })

    this.skipText = computed({
      get: () => this.translations.value?.skip_text || this._skipText.value,
      set: value => this._skipText.value = value,
    })

    this.showTooltips = computed({
      get: () => this._showTooltips.value,
      set: (value) => { this._showTooltips.value = value },
    })

    this.sortType = computed({
      get: () => this._sortType.value,
      set: (value) => { this._sortType.value = value },
    })

    this.disableSelectCategory = computed({
      get: () => this._disableSelectCategory.value,
      set: (value) => { this._disableSelectCategory.value = value },
    })

    this.isCommentRequired = computed({
      get: () => this._isCommentRequired.value,
      set: (value) => { this._isCommentRequired.value = value },
    })

    this.commentEnabled = computed({
      get: () => this._commentEnabled.value,
      set: (value) => { this._commentEnabled.value = value },
    })

    this.isDropdown = computed({
      get: () => this._isDropdown.value,
      set: (value) => { this._isDropdown.value = value },
    })

    this.dictionaryListType = computed({
      get: () => this._dictionaryListType.value,
      set: (value) => { this._dictionaryListType.value = value },
    })

    this.isMultipleChoice = computed({
      get: () => this._isMultipleChoice.value,
      set: (value) => { this._isMultipleChoice.value = value },
    })

    this.maxChooseVariants = computed({
      get: () => this._maxChooseVariants.value,
      set: (value) => { this._maxChooseVariants.value = value },
    })

    this.textFieldParam = computed({
      get: () => this._textFieldParam.value,
      set: (value) => { this._textFieldParam.value = value },
    })

    this.placeholderText = computed({
      get: () => this._placeholderText.value,
      set: (value) => { this._placeholderText.value = value },
    })

    // @NOTE: Если это филиалы с выпадающим списком, то указываем тип как "древовидный"
    this.items = shallowRef(this.transformDictionaryTree(this.dictionaryTree.value, this.dictionaryListType.value))

    this.skipped = ref(this.previousAnswer?.skipped === 1)

    this.previousItems = (this.previousAnswer?.detail_item || []).map(id => Number.parseInt(id))

    this.error = ref(null)
    this.canMoveToNextQuestion = ref(false)

    this.commentController = new CommentController({
      enabled: this.commentEnabled.value,
      required: this.isCommentRequired.value,
      skipped: this.skipped,
      value: this.previousAnswer?.self_variant || '',
      placeholderText: this.placeholderText.value,
      minLength: this.textFieldParam.value.min || 0,
      maxLength: this.textFieldParam.value.max || 1000,
      title: data.comment_label || toValue(this.t('Ваш комментарий')),
      translations: this.translations,
    })

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: toValue(this.enableGallery),
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    // can be "default" or "alphabetter" or "random"
    this.isListType = computed(() => this.dictionaryListType.value === 'list')
    this.isTreeType = computed(() => this.dictionaryListType.value === 'tree')

    /**
     * @var {boolean}
     * @description Если это выпадающий список, древовидный, с множественным выбором
     */
    this.isMultipleSelectTrees = computed(() =>
      this.isDropdown.value && this.isTreeType.value && this.isMultipleChoice.value,
    )

    /**
     * @var {boolean}
     * @description Если это множественный выбор и опция "Max кол-во вариантов" равно 0
     * Значит можно выбирать категории через чекбоксы
     */
    this.isSelectableCategories = computed(() => {
      return this.isMultipleChoice.value && this.maxChooseVariants.value === Infinity
    })

    this.items.value = this.sortTree(toValue(this.items))

    this.selectedTrees = ref(this.getPreviousSelectedTrees())

    if (this.isDropdown.value && this.isTreeType.value) {
      watch(() => this.selectedTrees.value, () => {
        this.validate()
      })
      this.selectedTrees.value.forEach((tree) => {
        watch(() => tree.selectedItems, () => {
          this.validate()
        }, { deep: true })
      })
    }

    this.selectedDropdownListValue = ref(this.getPreviousSelectedItems())
    this.selectListTypeOptions = computed(() => {
      if (this.isDropdown.value) {
        const getDisabledComputed = item => computed(() => {
          if (!this.isMultipleChoice.value) {
            return false
          }

          const dropdownListValue = this.selectedDropdownListValue.value
          const isSelected = dropdownListValue.some(selectedItem => selectedItem.id === item.id)
          const reachedLimit = dropdownListValue.length >= this.maxChooseVariants.value
          return !isSelected && reachedLimit
        })

        return this.items.value.map(item => ({
          id: item.id,
          value: item.id,
          label: toValue(item.title),
          description: toValue(item.description),
          disabled: toValue(getDisabledComputed(item)),
        }))
      }
      return []
    })

    this.selectedItems = ref(this.getPreviousSelectedItems())

    this.touched = ref(false)

    this.reachedMaxVariants = computed(() => {
      if (!this.isMultipleChoice.value || this.maxChooseVariants.value === 0) {
        return false
      }
      const leafItems = this.selectedItems.value.filter(item => item.children === null)
      return leafItems.length >= this.maxChooseVariants.value
    })

    watch(this.selectedItems, () => {
      this.validateSelection()
    }, { immediate: true })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetSelection(true)
        this.touched.value = false
        this.commentController.value.value = ''
        this.commentController.touched.value = false
        this.commentController.error.value = null
        this.error.value = null
        this.selectedDropdownListValue.value = []

        // leave only one tree
        this.selectedTrees.value = [this.selectedTrees.value[0]]
        this.selectedTrees.value[0].selectedItems = []
        this.selectedTrees.value[0].error = null
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    watch(
      () => this.selectedDropdownListValue.value,
      (selected) => {
        if (
          this.isDropdown.value
          && this.isListType.value
          && !this.isMultipleChoice.value
          && !this.commentEnabled.value
          && selected?.id
        ) {
          this.canMoveToNextQuestion.value = true
        }
      },
      { deep: true },
    )

    watch(
      () => this.selectedTrees.value,
      (trees) => {
        const firstTreeSelectedItems = trees?.[0]?.selectedItems

        const firstTreeContainsLeaf = firstTreeSelectedItems
          && firstTreeSelectedItems?.some(item => item.children === null)

        if (
          this.isDropdown.value
          && this.isTreeType.value
          && !this.isMultipleChoice.value
          && !this.commentEnabled.value
          && firstTreeContainsLeaf
        ) {
          this.canMoveToNextQuestion.value = true
        }
      },
      { deep: true },
    )

    watch(
      () => this.selectedItems.value,
      (items) => {
        if (
          !this.isDropdown.value
          && !this.isMultipleChoice.value
          && !this.commentEnabled.value
          && items.length === 1
          && (!this.isTreeType.value || items[0].children === null) // For tree type, make sure it's a leaf node
        ) {
          this.canMoveToNextQuestion.value = true
        }
      },
    )

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })

    // Add watchers for interaction tracking
    watch([this.skipped, this.selectedItems], ([skipped, items]) => {
      const hasSelectedItems = items.length > 0

      if (skipped || hasSelectedItems) {
        this.markInteracted()
      }
    }, { immediate: true })

    // For dropdown list type
    watch([this.skipped, this.selectedDropdownListValue], ([skipped, selected]) => {
      const hasSelection = Array.isArray(selected) ? selected.length > 0 : !!selected

      if (skipped || hasSelection) {
        this.markInteracted()
      }
    }, { immediate: true })

    // For dropdown tree type
    watch([this.skipped, () => this.selectedTrees.value], ([skipped, trees]) => {
      const hasSelectedLeafs = trees.some((tree) => {
        const selectedItems = tree.selectedItems
        return selectedItems.some(item => !item.children || item.children.length === 0)
      })

      if (skipped || hasSelectedLeafs) {
        this.markInteracted()
      }
    }, { deep: true })

    /**
     * Возвращет список выбранных и не выбранных узлов
     * Обработаны все виды классификатора: выпадающий список, древовидный, множественный выбор
     * @type {import('vue').ComputedRef<{selected: Array, unselected: Array}>}
     */
    this.selectedAndUnselectedNodes = computed(() => {
      const selected = []
      const unselected = []

      if (this.isDropdown.value) {
        if (this.isListType.value) {
          // Выпадающий список - линейный справочник
          const selectedValue = this.selectedDropdownListValue.value
          const selectedIds = new Set(
            Array.isArray(selectedValue)
              ? selectedValue.map(item => item.id)
              : selectedValue ? [selectedValue.id] : [],
          )
          this.items.value.forEach((item) => {
            if (selectedIds.has(item.id)) {
              selected.push(item)
            }
            else {
              unselected.push(item)
            }
          })
        }
        else if (this.isTreeType.value) {
          // Выпадающий список - древовидный справочник
          this.selectedTrees.value.forEach((tree) => {
            tree.selectedItems.forEach((item) => {
              if (item.children === null) { // Only include leaf nodes
                selected.push(item)
              }
            })
          })

          // Находим все узлы без детей (leaf nodes)
          const processNode = (node) => {
            if (node.children === null) {
              if (!selected.some(item => item.id === node.id)) {
                unselected.push(node)
              }
            }
            else {
              (node.children || []).forEach(processNode)
            }
          }
          this.items.value.forEach(processNode)
        }
      }
      else {
        // Обычный классификатор (без выпадающего списка)
        // Линейный, древовидный

        /**
         * Обработка узла дерева. Добавляет узлы в selected или unselected
         * @param {*} node
         * @returns {void}
         */
        const processNode = (node) => {
          if (node.children === null) { // Leaf node
            if (node.isSelected.value) {
              selected.push(node)
            }
            else {
              unselected.push(node)
            }
          }
          else {
            (node.children || []).forEach(processNode)
          }
        }

        this.items.value.forEach(processNode)
      }

      return { selected, unselected }
    })

    watch(this.translations, () => {
      this.selectedDropdownListValue.value = []
      this.selectedTrees.value = [this.createTreeNode()]
    })
  }

  /**
   * Find all parent nodes for the given item id including the search item
   * @param {number} itemId
   * @returns {Array} - Array of parent nodes
   */
  getAllParentNodes(itemId) {
    const parentNodes = []

    const nodeContainsItem = (node, id) => {
      if (node.id === id)
        return true
      if (node.children) {
        const found = node.children.some(child => nodeContainsItem(child, id))
        if (found)
          return found
      }
      return false
    }

    const traverseTree = (nodes) => {
      nodes.forEach((node) => {
        if (nodeContainsItem(node, itemId)) {
          parentNodes.push(node)
        }
        if (node.children) {
          traverseTree(node.children)
        }
      })
    }
    traverseTree(this.items.value)

    return parentNodes
  }

  getPreviousSelectedTrees() {
    if (!this.previousItems || this.previousItems.length === 0) {
      return [this.createTreeNode()]
    }

    const selectedTrees = this.previousItems.map((item) => {
      const selectedItems = ref(this.getAllParentNodes(item))
      const itemData = {
        nodes: this.items.value,
        selectedItems,
        error: ref(null),
      }

      itemData.computedSelectedItems = computed(() => {
        return toValue(itemData.selectedItems).map((item) => {
          const translations = toValue(this.translations)
          const translation = translations?.detailLangs?.[item.id]

          return {
            ...item,
            label: translation?.question || toValue(item.rawTitle),
            description: this.showTooltips.value ? (translation?.description || toValue(item.rawDescription)) : null,
          }
        })
      })
      return itemData
    })

    return selectedTrees
  }

  createTreeNode() {
    const selectedItems = ref([])

    const nodeData = {
      nodes: this.items.value,
      selectedItems,
      error: ref(null),
    }

    nodeData.computedSelectedItems = computed(() => {
      return toValue(nodeData.selectedItems).map((item) => {
        const translations = toValue(this.translations)
        const translation = translations?.detailLangs?.[item.id]
        const description = this.showTooltips.value ? (translation?.description || item.rawDescription) : null
        return {
          ...item,
          label: translation?.question || item.rawTitle || item.label,
          description,
        }
      })
    })

    return nodeData
  }

  addTreeNode() {
    this.selectedTrees.value.push(this.createTreeNode())
  }

  removeTreeNode(index) {
    if (this.selectedTrees.value.length > 1) {
      this.selectedTrees.value.splice(index, 1)
    }
  }

  getPreviousSelectedItems() {
    if (!this.previousItems || this.previousItems.length === 0) {
      return []
    }

    const nodeHasDescendantSelectedItems = (node) => {
      if (node.children === null) {
        return this.previousItems.includes(node.id)
      }
      return node.children.some(child => nodeHasDescendantSelectedItems(child))
    }

    const selectedItems = []
    const findAndSetSelected = (items) => {
      for (const item of items) {
        if (nodeHasDescendantSelectedItems(item)) {
          item.isSelected.value = true
          item.isOpen.value = true
          item.disabled.value = false
          selectedItems.push(item)
        }
        if (item.children) {
          findAndSetSelected(item.children)
        }
      }
    }

    findAndSetSelected(this.items.value)
    return selectedItems
  }

  /**
   * Преобразует древовидную структуру `dictionaryTree` в массив с реактивными значениями.
   * Также сортирует массв в зависимости от типа сортировки.
   * @param {Array} dictionaryTree - Древовидная структура справочника.
   * @param {'tree' | 'list'} dictionaryListType - Тип списка.
   * @returns {Array} - Возвращает отсортированный массив с реактивными значениями.
   */
  transformDictionaryTree(dictionaryTree = {}, dictionaryListType = 'tree') {
    const isListType = dictionaryListType === 'list'

    const createItemObject = (item, children) => {
      // Private refs for mutable values
      const _rawTitle = ref(item.title)
      const _rawDescription = ref(item.description)
      const _isSelected = ref(false)
      const _isOpen = ref(false)

      const itemObject = {
        id: item.id,
        value: item.id,
        isCategory: item.isCategory,
        children,

        // Convert direct values to computed with getters/setters
        rawTitle: computed({
          get: () => _rawTitle.value,
          set: value => _rawTitle.value = value,
        }),

        rawDescription: computed({
          get: () => _rawDescription.value,
          set: value => _rawDescription.value = value,
        }),

        position: item.position,

        // Convert isSelected to computed with getter/setter
        isSelected: computed({
          get: () => _isSelected.value,
          set: value => _isSelected.value = value,
        }),

        // Convert isOpen to computed with getter/setter
        isOpen: computed({
          get: () => _isOpen.value,
          set: value => _isOpen.value = value,
        }),

        // Keep existing computed properties
        title: computed(() => {
          const translations = toValue(this.translations)
          const translation = translations?.detailLangs?.[item.id]
          return translation?.question || toValue(_rawTitle)
        }),

        description: computed(() => {
          if (!toValue(this.showTooltips)) {
            return null
          }
          const translation = toValue(this.translations)?.detailLangs?.[item.id]
          return translation?.description || toValue(_rawDescription)
        }),

        // Keep existing disabled computed
        disabled: toValue(this.isDropdown)
          ? computed(() => {
            const isCategory = toValue(item.children)?.length > 0
            if (!toValue(this.isMultipleSelectTrees) || isCategory) {
              return false
            }

            const selectedTrees = toValue(this.selectedTrees)
            const isDisabled = selectedTrees.some(tree =>
              tree.selectedItems.some(selectedItem => selectedItem.id === item.id),
            )
            return isDisabled
          })
          : ref(false),
      }

      // Add label computed that depends on title
      itemObject.label = computed(() => toValue(itemObject.title))

      return itemObject
    }

    const transformTree = (tree, isListType) => {
      if (!tree || !Array.isArray(tree) || tree.length === 0) {
        return null
      }

      if (isListType) {
        const flattenTree = (items) => {
          return items.reduce((acc, item) => {
            if (!item.children || item.children.length === 0) {
              acc.push(createItemObject(item, null))
            }
            else {
              acc.push(...flattenTree(Object.values(item.children)))
            }
            return acc
          }, [])
        }
        return flattenTree(tree)
      }

      return tree.reduce((acc, item) => {
        const transformedChildren = transformTree(Object.values(item.children || []), isListType)
        acc.push(createItemObject(item, transformedChildren))
        return acc
      }, [])
    }

    const transformedTree = transformTree(Object.values(dictionaryTree), isListType)

    if (!transformedTree) {
      return []
    }

    return transformedTree
  }

  // Add this new method
  sortTree(tree) {
    let sorter
    if (toValue(this.sortType) === 'alphabetter') {
      sorter = this.alphabetSorter
    }
    else if (toValue(this.sortType) === 'random') {
      sorter = this.randomSorter
    }
    else {
      sorter = this.defaultSorter
    }
    return this.sortNodes(tree, sorter)
  }

  // Move these sorter methods outside of transformDictionaryTree
  defaultSorter(a, b) {
    // First, sort by whether the item is a category
    if (a.isCategory && !b.isCategory)
      return -1
    if (!a.isCategory && b.isCategory)
      return 1

    return a.position - b.position
  }

  alphabetSorter(a, b) {
    if (a.isCategory && !b.isCategory)
      return -1
    if (!a.isCategory && b.isCategory)
      return 1
    const aTitle = toValue(a.title)
    const bTitle = toValue(b.title)

    const firstCharacterIsNumberRegex = /^\d+/
    if (firstCharacterIsNumberRegex.test(aTitle) && firstCharacterIsNumberRegex.test(bTitle)) {
      const aNumber = Number.parseInt(aTitle[0])
      const bNumber = Number.parseInt(bTitle[0])

      if (aNumber < bNumber) {
        return -1
      }
      else if (aNumber > bNumber) {
        return 1
      }
      else {
        return 0
      }
    }

    return aTitle.localeCompare(bTitle)
  }

  randomSorter(a, b) {
    // First, sort by whether the item is a category
    if (a.isCategory && !b.isCategory)
      return -1
    if (!a.isCategory && b.isCategory)
      return 1
    return Math.random() - 0.5
  }

  sortNodes(nodes, sorter) {
    if (!nodes) {
      return null
    }

    const sortedNodes = nodes
      .sort((a, b) => sorter(a, b))
      .map((node) => {
        if (node.children) {
          node.children = this.sortNodes(node.children, sorter)
        }
        return node
      })

    return sortedNodes
  }

  toggleNode(node) {
    if (node.disabled.value) {
      return
    }

    this.skipped.value = false

    if (toValue(this.isMultipleChoice)) {
      this.toggleMultipleSelection(node)
    }
    else {
      this.selectSingleNode(node)
    }
  }

  toggleOpenNode(node) {
    const hasChildren = node.children !== null
    if (hasChildren) {
      node.isOpen.value = !node.isOpen.value
    }
  }

  toggleMultipleSelection(node) {
    const isSelected = node.isSelected.value
    const isLeaf = node.children === null

    const disableSelectCategory = toValue(this.disableSelectCategory)

    const allChildrenAreOpen = (node) => {
      if (node.children === null) {
        return true
      }

      const isOpen = node.isOpen.value

      return isOpen && (node.children || []).every(allChildrenAreOpen)
    }

    if (disableSelectCategory && !isLeaf && !node.isSelected.value && !allChildrenAreOpen(node)) {
      this.toggleOpenNode(node)
      return
    }

    node.isSelected.value = !isSelected

    if (node.children !== null) {
      this.toggleChildrenSelection(node, !isSelected)
    }

    this.updateSelectedItems()
  }

  toggleChildrenSelection(node, isSelected) {
    if (node.children === null) {
      node.isSelected.value = isSelected
    }
    else {
      (node.children || []).forEach((child) => {
        child.isSelected.value = isSelected
        this.toggleChildrenSelection(child, isSelected)
      })
    }
  }

  selectSingleNode(node) {
    this.resetSelection()
    node.isSelected.value = true
    this.selectedItems.value = [node]
  }

  updateSelectedItems() {
    const selected = []
    let selectedLeafsCount = 0

    const updateNode = (node) => {
      const isLeaf = node.children === null || node.children?.length === 0
      const isSelected = node.isSelected.value
      if (isSelected && !isLeaf) {
        selected.push(node)
      }

      if (isSelected && isLeaf && selectedLeafsCount < toValue(this.maxChooseVariants)) {
        selected.push(node)
        selectedLeafsCount++
      }
      else if (isSelected && isLeaf && selectedLeafsCount >= toValue(this.maxChooseVariants)) {
        return
      }
      if (node.children !== null) {
        (node.children || []).forEach(updateNode)
      }
    }

    this.items.value.forEach(updateNode)

    this.selectedItems.value = selected
  }

  resetSelection(closeAllNodes = false) {
    const resetNode = (node) => {
      node.isSelected.value = false
      node.disabled.value = false
      if (closeAllNodes) {
        node.isOpen.value = false
      }
      if (node.children) {
        (node.children || []).forEach(resetNode)
      }
    }

    if (toValue(this.isListType)) {
      toValue(this.items).forEach(resetNode)
    }
    else {
      toValue(this.items).forEach(resetNode)
    }

    this.selectedItems.value = []
  }

  validateSelection() {
    const selectedLeafNodes = this.selectedItems.value.filter(item => item.children === null)
    const selectedLeafNodesIds = selectedLeafNodes.map(item => item.id)

    const validateNode = (node) => {
      const isLeaf = node.children === null
      if (!isLeaf) {
        // Check if this node contains any selected leaf nodes
        let containsSelectedLeaf = false;
        (node.children || []).forEach((child) => {
          if (selectedLeafNodesIds.includes(child.id) || validateNode(child)) {
            containsSelectedLeaf = true
          }
        })
        if (containsSelectedLeaf) {
          node.disabled.value = false
          node.isSelected.value = true
          return true
        }
        else {
          node.disabled.value = this.reachedMaxVariants.value
          node.isSelected.value = false
          return false
        }
      }
      else {
        // Leaf node
        if (selectedLeafNodesIds.includes(node.id)) {
          node.disabled.value = false
          node.isSelected.value = true
          return true
        }
        else {
          node.disabled.value = this.reachedMaxVariants.value
          node.isSelected.value = false
          return false
        }
      }
    }

    this.items.value.forEach(validateNode)
  }

  setAllAsTouched() {
    this.touched.value = true
    if (toValue(this.commentEnabled)) {
      this.commentController.touched.value = true
    }
  }

  validate() {
    let valid = true
    let commentValid = true
    this.error.value = null

    if (!this.touched.value || this.skipped.value) {
      return true
    }

    const isRequired = toValue(this.isRequired)

    if (toValue(this.isDropdown) && isRequired) {
      const isListType = toValue(this.dictionaryListType) === 'list'
      if (isListType) {
        const selected = toValue(this.selectedDropdownListValue)
        if (Array.isArray(selected) && selected.length === 0) {
          this.error.value = this.t('Нужно выбрать один из вариантов')
          valid = false
        }
        else if (selected === null || selected === undefined) {
          this.error.value = this.t('Нужно выбрать один из вариантов')
          valid = false
        }
      }
      else {
        let everyTreeHasSelectedLeafs = true
        toValue(this.selectedTrees).forEach((tree) => {
          tree.error = null
          const selectedLeafs = tree.selectedItems.filter(item => !item.children || item.children.length === 0)
          const isInvalid = selectedLeafs.length === 0

          if (isInvalid) {
            tree.error = toValue(this.t('Нужно выбрать один из вариантов'))
            everyTreeHasSelectedLeafs = false
          }
        })

        if (!everyTreeHasSelectedLeafs) {
          valid = false
        }
      }
    }
    else if (isRequired && this.selectedItems.value.length === 0) {
      valid = false
      this.error.value = this.t('Нужно выбрать один из вариантов')
    }

    if (toValue(this.commentEnabled)) {
      commentValid = this.commentController.validate()
    }

    return valid && commentValid
  }

  getData() {
    if (toValue(this.skip) && toValue(this.skipped)) {
      return { skipped: 1 }
    }
    let data = {}

    if (toValue(this.isDropdown) && toValue(this.isListType)) {
      const selected = toValue(this.selectedDropdownListValue)
      data = {
        detail_item: Array.isArray(selected) ? selected.map(item => item.id) : [selected.id],
      }
    }

    else if (toValue(this.isDropdown) && toValue(this.isTreeType)) {
      const selectedTrees = toValue(this.selectedTrees)
      const leafsIds = selectedTrees.reduce((acc, tree) => {
        const selectedItems = tree.selectedItems
        const leafs = selectedItems.filter(item => item.children === null)
        return [...acc, ...leafs.map(item => item.id)]
      }, [])
      data = {
        detail_item: leafsIds,
      }
    }
    else {
      const selectedLeafItems = toValue(this.selectedItems).filter(item => item.children === null)

      data = {
        detail_item: selectedLeafItems.map(item => item.id),
      }
    }

    if (toValue(this.commentEnabled)) {
      data.comment = toValue(this.commentController.value.value)
    }

    return data
  }

  getAnswer() {
    return toValue(this.selectedItems).map(item => item.value).join(', ')
  }

  get hasValue() {
    if (toValue(this.skip) && toValue(this.skipped)) {
      return true
    }

    const previousAnswerExists = !!toValue(this.previousAnswer)
    return previousAnswerExists
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (toValue(this.skip) && toValue(this.skipped))
        return ''

      let selectedItems = []

      if (toValue(this.isDropdown) && toValue(this.isListType)) {
        selectedItems = toValue(this.selectedDropdownListValue)
      }
      else if (toValue(this.isDropdown) && toValue(this.isTreeType)) {
        selectedItems = toValue(this.selectedTrees).flatMap((tree) => {
          const items = tree.selectedItems
          return items.map(item => toValue(item.title)).join(' / ')
        })
      }
      else {
        selectedItems = toValue(this.selectedItems).filter(item => item.children === null)
      }

      if (toValue(this.isTreeType)) {
        const result = selectedItems.map(item => this.getFullPath(item)).join(', ')
        return result
      }
      else {
        const normalizedItems = Array.isArray(selectedItems) ? selectedItems : [selectedItems]
        return normalizedItems.map(item => toValue(item?.title) || toValue(item?.label) || '').join(', ')
      }
    })
  }

  getFullPath(item) {
    const path = []
    let currentItem = item

    while (currentItem) {
      path.unshift(toValue(currentItem.title))
      currentItem = this.findParent(currentItem)
    }

    return path.join(' / ')
  }

  findParent(item) {
    const findParentInTree = (tree, target, parent = null) => {
      for (const node of tree) {
        if (node.id === target.id)
          return parent
        if (node.children) {
          const result = findParentInTree(node.children, target, node)
          if (result)
            return result
        }
      }
      return null
    }

    return findParentInTree(this.items.value, item)
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Get selected IDs based on view type
    let selectedIds = []

    if (toValue(this.isDropdown)) {
      if (toValue(this.isListType)) {
        // Handle dropdown list type
        const selected = toValue(this.selectedDropdownListValue)
        if (Array.isArray(selected)) {
          // Multiple selection case
          selectedIds = selected.map(item => item.id)
        }
        else if (selected) {
          // Single selection case
          selectedIds = [selected.id]
        }
      }
      else if (toValue(this.isTreeType)) {
        // Handle dropdown tree type
        const selectedTrees = toValue(this.selectedTrees)
        selectedIds = selectedTrees.reduce((acc, tree) => {
          // Get only leaf nodes (items without children)
          const leafs = tree.selectedItems.filter(item => !item.children || item.children.length === 0)
          return [...acc, ...leafs.map(item => item.id)]
        }, [])
      }
    }
    else {
      // Handle regular (non-dropdown) selection
      const selectedItems = toValue(this.selectedItems)
      // Filter only leaf nodes
      const leafItems = selectedItems.filter(item => item.children === null)
      selectedIds = leafItems.map(item => item.id)
    }

    // Filter out any falsy values
    selectedIds = selectedIds.filter(id => !!id)

    switch (condition.behavior) {
      case BEHAVIOR_MISS:
        // Check if question was skipped or has no selection
        return this.skipped.value || selectedIds.length === 0

      case BEHAVIOR_SELECT:
        // Check if any selected item is in condition variants
        return selectedIds.some(id => condition.variants.includes(id))

      case BEHAVIOR_UNSELECT:
        // Check if NO selected item is in condition variants
        return !selectedIds.some(id => condition.variants.includes(id))

      case BEHAVIOR_ALWAYS:
        return true

      default:
        return false
    }
  }

  resolveViewLogic(rule) {
    // Get selected IDs based on view type
    let selectedIds = []

    if (toValue(this.isDropdown)) {
      if (toValue(this.isListType)) {
        // Handle dropdown list type
        const selected = toValue(this.selectedDropdownListValue)
        if (Array.isArray(selected)) {
          // Multiple selection case
          selectedIds = selected.map(item => item.id)
        }
        else if (selected) {
          // Single selection case
          selectedIds = [selected.id]
        }
      }
      else if (toValue(this.isTreeType)) {
        // Handle dropdown tree type
        const selectedTrees = toValue(this.selectedTrees)
        selectedIds = selectedTrees.reduce((acc, tree) => {
          // Get only leaf nodes (items without children)
          const leafs = tree.selectedItems.filter(item => !item.children || item.children.length === 0)
          return [...acc, ...leafs.map(item => item.id)]
        }, [])
      }
    }
    else {
      // Handle regular (non-dropdown) selection
      const selectedItems = toValue(this.selectedItems)
      // Filter only leaf nodes
      const leafItems = selectedItems.filter(item => item.children === null)
      selectedIds = leafItems.map(item => item.id)
    }

    // Filter out any falsy values
    selectedIds = selectedIds.filter(id => !!id)
    const variants = rule.variants.map(id => Number.parseInt(id)).filter(id => !!id)

    // Check if any selected item is in rule variants
    return selectedIds.some(id => variants.includes(id))
  }

  updateFromPreview(previewData) {
    if (!previewData) {
      return
    }

    // Call parent update first
    super.updateFromPreview(previewData)

    // Update basic fields
    if (previewData.isRequired !== undefined) {
      this.isRequired.value = previewData.isRequired
    }

    if (previewData.show_tooltips !== undefined) {
      this.showTooltips.value = previewData.show_tooltips
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }

    if (!this.skip.value) {
      this.skipped.value = false
    }

    if (previewData.skip_text !== undefined) {
      this.skipText.value = previewData.skip_text || toValue(this.t('Затрудняюсь ответить'))
    }

    // Update comment fields
    if (previewData.commentEnabled !== undefined) {
      this.commentEnabled.value = previewData.commentEnabled
    }

    // Update comment controller
    this.commentController.updateFromPreview({
      enabled: previewData.commentEnabled,
      required: previewData.comment_required,
      title: previewData.comment_label || toValue(this.t('Ваш комментарий')),
      placeholderText: previewData.placeholderText,
      textFieldParam: previewData.textFieldParam || { min: 0, max: 250 },
    })

    // Update gallery
    if (previewData.enableGallery !== undefined) {
      this.enableGallery.value = previewData.enableGallery
    }

    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: previewData.gallery || [],
      })
    }

    const multiple = Number.parseInt(previewData.variantsType) === 1
    const maxChooseVariants = Number.parseInt(previewData.max_choose_variants) || Infinity

    const dictionaryListTypeChanged = toValue(this.dictionaryListType) !== previewData.dictionary_list_type
    const dictionarySortChanged = toValue(this.sortType) !== previewData.dictionary_sort
    const disableSelectCategoryChanged = toValue(this.disableSelectCategory) !== previewData.disable_select_category
    const variantsTypeChanged = toValue(this.isMultipleChoice) !== multiple
    const dropdownVariantsChanged = toValue(this.isDropdown) !== previewData.dropdownVariants
    const maxVariantsCountChanged = toValue(this.maxChooseVariants) !== maxChooseVariants

    const shouldResetSelection = dictionaryListTypeChanged
      || disableSelectCategoryChanged
      || variantsTypeChanged
      || dropdownVariantsChanged
      || maxVariantsCountChanged

    // Update dictionary configuration
    if (previewData.dictionary_list_type !== undefined) {
      this.dictionaryListType.value = previewData.dictionary_list_type
    }

    if (previewData.dictionary_sort !== undefined) {
      this.sortType.value = previewData.dictionary_sort
    }

    if (previewData.disable_select_category !== undefined) {
      this.disableSelectCategory.value = previewData.disable_select_category
    }

    if (previewData.variantsType !== undefined) {
      this.isMultipleChoice.value = multiple
    }

    if (previewData.dropdownVariants !== undefined) {
      this.isDropdown.value = previewData.dropdownVariants
    }

    if (previewData.max_choose_variants !== undefined) {
      this.maxChooseVariants.value = maxChooseVariants
    }

    // Handle dictionary tree updates
    if (previewData.selectedDictionaryTree) {
      const newTreeSize = this.getTreeSize(previewData.selectedDictionaryTree)
      const currentTreeSize = this.getTreeSize(toValue(this.dictionaryTree))
      const treeStructureChanged = newTreeSize !== currentTreeSize

      const shouldSortTree = !treeStructureChanged && !shouldResetSelection && dictionarySortChanged

      // @NOTE: Если размер дерева не изменился, но поменялся порядок сортировки, то сортируем дерево.
      if (shouldSortTree) {
        this.items.value = this.sortTree(toValue(this.items))
        return
      }

      // @NOTE: Если размер дерева не изменился, то нет смысла инициализировать новое дерево.
      if (!treeStructureChanged && !shouldResetSelection) {
        return
      }

      // Full reinitialization
      this.dictionaryTree.value = previewData.selectedDictionaryTree

      this.items.value = this.transformDictionaryTree(toValue(this.dictionaryTree), this.dictionaryListType.value)
      this.items.value = this.sortTree(toValue(this.items))

      if (treeStructureChanged || shouldResetSelection) {
        this.resetSelection()
        this.selectedTrees.value = [this.createTreeNode()]
        this.selectedDropdownListValue.value = []
      }
    }
  }

  /**
   * Получает размер дерева.
   * @param {object} tree - Дерево для подсчета узлов
   * @returns {number} Общее количество узлов в дереве
   * @example
   * getTreeSize({
   *   id: 1,
   *   children: {
   *     id: 2,
   *     children: {
   *       id: 3,
   *     },
   *   },
   * })
   * // Output: 3
   */
  getTreeSize(tree) {
    let size = 0

    const countNodes = (node) => {
      size++
      if (node.children) {
        Object.values(node.children).forEach(countNodes)
      }
    }

    Object.values(tree).forEach(countNodes)
    return size
  }
}
