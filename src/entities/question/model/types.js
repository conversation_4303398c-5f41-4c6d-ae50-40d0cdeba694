export const RATE_QUESTION = 0 // оценка
export const VARIANTS_QUESTION = 1 // варианты ответов
export const TEXT_QUESTION = 2 // текстовый ответ
export const DATE_QUESTION = 3 // дата и время
export const ADDRESS_QUESTION = 4 // адрес
export const FILE_QUESTION = 5 // выбор файла
export const QUIZ_QUESTION = 6 // анкета
export const STAR_VARIANTS_QUESTION = 7 // звездный рейтинг для вариантов
export const PRIORITY_QUESTION = 8 // приоритет
export const MEDIA_VARIANTS_QUESTION = 9 // выбор изображения/видео
export const GALLERY_QUESTION = 10 // рейтинг галереи
export const SMILE_QUESTION = 11 // смайл-рейтинг
export const NPS_QUESTION = 12 // nps-рейтинг
export const MATRIX_QUESTION = 13 // простая матрица
export const DIFF_QUESTION = 14 // семантический дифференциал
export const STARS_QUESTION = 15 // звездный рейтинг
export const FILIALS_QUESTION = 17 // выбор филиала
export const RATING_QUESTION = 18 // рейтинг
export const CLASSIFIER_QUESTION = 19 // классификатор
export const SCALE_QUESTION = 20 // шкала
export const MATRIX_3D_QUESTION = 21 // 3D матрица
export const CARD_SORTING_QUESTION = 22 // 3D матрица
export const DISTRIBUTION_SCALE_QUESTION = 23 // Распределительная шкала
export const FIRST_CLICK_TEST_QUESTION = 24 // Тест первого клика

export const INTER_BLOCK = 16 // промежуточный блок

export const typesSet = [
  {
    id: RATING_QUESTION,
    label: 'Рейтинг',
    name: 'rating',
  },
  {
    id: STARS_QUESTION,
    label: 'Звездный рейтинг',
    name: 'stars',
  },

  {
    id: VARIANTS_QUESTION,
    label: 'Варианты ответов',
    name: 'variants',
  },
  {
    id: TEXT_QUESTION,
    label: 'Текстовый ответ',
    name: 'text',
  },
  {
    id: DATE_QUESTION,
    label: 'Дата/время',
    name: 'date',
  },
  {
    id: ADDRESS_QUESTION,
    label: 'Адрес',
    name: 'address',
  },
  {
    id: FILE_QUESTION,
    label: 'Загрузка файла',
    name: 'file',
  },
  {
    id: QUIZ_QUESTION,
    label: 'Анкета',
    name: 'quiz',
  },
  {
    id: PRIORITY_QUESTION,
    label: 'Приоритет',
    name: 'priority',
  },
  {
    id: SCALE_QUESTION,
    label: 'Шкала',
    name: 'scale',
  },
  {
    id: DISTRIBUTION_SCALE_QUESTION,
    label: 'Распределительная шкала',
    name: 'distribution-scale',
  },
  {
    id: MEDIA_VARIANTS_QUESTION,
    label: 'Выбор изображения/видео',
    name: 'media-variants',
  },

  {
    id: GALLERY_QUESTION,
    label: 'Рейтинг фото/видео галереи',
    name: 'gallery',
  },
  {
    id: SMILE_QUESTION,
    label: 'Смайл-рейтинг',
    name: 'smile',
  },
  {
    id: NPS_QUESTION,
    label: 'Рейтинг NPS',
    name: 'nps',
  },
  {
    id: MATRIX_QUESTION,
    label: 'Простая матрица',
    name: 'matrix',
  },
  {
    id: MATRIX_3D_QUESTION,
    label: '3D матрица',
    name: 'matrix-3d',
  },
  {
    id: STAR_VARIANTS_QUESTION,
    label: 'Звездный рейтинг для вариантов',
    name: 'star-variants',
  },
  {
    id: DIFF_QUESTION,
    label: 'Семантический дифференциал',
    name: 'diff',
  },
  {
    id: RATE_QUESTION,
    label: 'Оценка',
    name: 'rate',
  },
  {
    id: FILIALS_QUESTION,
    label: 'Выбор филиала',
    name: 'filials',
  },
  {
    id: CLASSIFIER_QUESTION,
    label: 'Классификатор ',
    name: 'classifier',
  },
  {
    id: CARD_SORTING_QUESTION,
    label: 'Закрытая карточная сортировка ',
    name: 'card-sorting',
  },

  {
    id: INTER_BLOCK,
    label: 'Промежуточный блок',
    name: 'inter',
  },
]

// Дизайн NPS
export const RATING_NPS_DESIGN = {
  COLORED: 1,
  BLACK_AND_WHITE: 2,
  CUSTOM: 3,
}

// Варианты у поля "Текстовый ответ"
export const TEXT_VARIANT_TYPES = {
  INPUT: 0,
  TEXTAREA: 1,
}

export const ASSESMENT_VARIANT_TYPES = {
  SINGLE: 0,
  MULTIPLE: 1,
  TEXT: 2,
}

export const VARIANT_TYPES = {
  SINGLE: 0,
  MULTIPLE: 1,
}

export const VARIANT_ITEM_TYPES = {
  DEFAULT: 0,
  REMOVE_OTHERS: 1,
}

// Типы блоков промежуточного экрана
export const INTERMEDIATE_BLOCK_TYPES = {
  INTERMEDIATE: 1,
  START: 2,
  END: 3,
  FIVE_SECOND_TEST: 4,
}

export const MASK_TYPES = {
  NONE: 0,
  PHONE: 1,
  EMAIL: 2,
  NUMBER: 3,
  WEBSITE: 4,
  FIO: 5,
  DATE: 6,
  DATE_RANGE: 7,
  DATE_MONTH: 8,
}
