import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, nextTick, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'

export class GalleryQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const translationsStore = useTranslationsStore()
    this.t = translationsStore.t
    this.data = data

    this.gallery = data.gallery || []
    this.isRequired = ref(data.isRequired)
    this.answer = data.answer || {}
    // 0 - single, 1 - multiple

    // 'image' | 'video'
    this.chooseType = ref(data.chooseType)

    this.variantsType = ref(data.variantsType)
    this.maxChooseVariants = ref(data.max_choose_variants || 0)
    this.randomVariantsOrder = ref(data.random_variants_order === 1)
    this.isCommentRequired = ref(data.comment_required)
    this.commentEnabled = ref(data.isHaveComment && !data.answerText)
    this.textFieldParam = ref(data.textFieldParam || {})
    this.placeholderText = ref(data.placeholderText)

    this.skip = ref(data.skip)
    this._skipText = ref(data.skip_text)

    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || toValue(this.t('Не готов(а) оценить'))
    })
    this.skipped = ref(this.previousAnswer?.skipped === 1)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: true,
      randomVariantsOrder: this.randomVariantsOrder,
      gallery: this.gallery,
      selectable: true,
      multiple: this.variantsType === 1,
      type: 'rating',
      isRating: true,
      isRequired: toValue(this.isRequired),
      skipped: this.skipped,
      translations: this.translations,
      maxChooseVariants: toValue(this.maxChooseVariants),
    })

    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: toValue(this.isCommentRequired),
      skipped: this.skipped,
      value: this.answer.comment || this.answer.self_variant || '',
      placeholderText: toValue(this.placeholderText),
      minLength: toValue(this.textFieldParam).min || 0,
      maxLength: toValue(this.textFieldParam).max || 250,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.touched = ref(false)

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return !this.isValid.value
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (v) => {
      if (v) {
        this.galleryController.error.value = null
        this.galleryController.touched.value = false
        this.galleryController.selectedItems.value = []
        this.markInteracted()

        if (this.commentEnabled) {
          this.commentController.touched.value = false
          this.commentController.error.value = null
        }

        this.galleryController.scrollToFirstSlide()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })

    watch(
      () => this.galleryController.gallery.value,
      (gallery) => {
        if (gallery.some(item => item.rating > 0)) {
          this.markInteracted()
        }

        if (
          !toValue(this.commentEnabled) // No comment field
          && gallery.length === 1 // Only one gallery item
          && gallery[0].rating > 0 // Rating is set for that item
        ) {
          this.canMoveToNextQuestion.value = true
        }
      },
      { deep: true },
    )
  }

  updateFromPreview(previewData) {
    const isRequiredChanged = this.isRequired.value !== previewData.isRequired

    // Call parent update first
    super.updateFromPreview(previewData)

    if (isRequiredChanged) {
      this.touched.value = false
      this.skipped.value = false
      this.galleryController.touched.value = false
      this.galleryController.error.value = null
    }

    // Update basic fields
    if (previewData.isRequired !== undefined) {
      this.isRequired.value = previewData.isRequired
    }

    if (previewData.random_variants_order !== undefined) {
      this.randomVariantsOrder.value = previewData.random_variants_order
    }

    // Update skip-related fields
    if (previewData.skip !== undefined) {
      this.skip.value = previewData.skip
    }

    if (previewData.skip_text !== undefined) {
      this._skipText.value = previewData.skip_text
    }

    if (!toValue(this.skip)) {
      this.skipped.value = false
    }

    // Update gallery controller with new media items
    this.galleryController.updateFromPreview({
      enabled: true,
      randomVariantsOrder: toValue(this.randomVariantsOrder),
      gallery: previewData.gallery,
      selectable: true,
      multiple: toValue(this.variantsType) === 1,
      type: 'rating',
      isRating: true,
      isRequired: toValue(this.isRequired),
      maxChooseVariants: toValue(this.maxChooseVariants),
    })

    // Update comment-related fields
    if (previewData.isHaveComment !== undefined) {
      this.commentEnabled.value = previewData.isHaveComment
    }

    // Update comment controller if enabled
    this.commentController.updateFromPreview({
      enabled: toValue(this.commentEnabled),
      required: !!previewData.comment_required,
      title: previewData.comment_label || toValue(this.t('Ваш комментарий')),
      placeholderText: previewData.placeholderText || '',
      textFieldParam: {
        min: previewData.textFieldParam?.min || 0,
        max: previewData.textFieldParam?.max || 1000,
      },
    })
  }

  validate() {
    if (!this.touched.value || this.skipped.value)
      return true

    let galleryValid = true
    let commentValid = true

    galleryValid = this.galleryController.validate()

    if (toValue(this.commentEnabled))
      commentValid = this.commentController.validate()

    return galleryValid && commentValid
  }

  get hasValue() {
    return this.galleryController.hasValue || this.commentController?.hasValue
  }

  setAllAsTouched() {
    this.touched.value = true
    if (toValue(this.commentEnabled))
      this.commentController.touched.value = true
    this.galleryController.setAllAsTouched()
  }

  getData() {
    const data = {
      answer: [],
    }

    if (toValue(this.skip) && this.skipped.value) {
      return { skipped: 1 }
    }

    if (toValue(this.commentEnabled))
      data.comment = this.commentController.value.value

    const galleryData = this.galleryController.getData()
    const rating = galleryData.rating
    data.answer = rating.reduce((acc, item) => {
      acc[item.id] = toValue(item.rating) || '0'
      return acc
    }, {})
    return data
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (toValue(this.skip) && this.skipped.value)
        return ''

      const data = this.galleryController.getData()
      const ratings = data.rating
      const fileWord = toValue(this.t('Файл'))

      return Object.entries(ratings)
        .map(([_, rating]) => `${fileWord} ${rating.position}: ${rating.rating}`)
        .join('; ')
    })
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Gallery question only supports BEHAVIOR_ALWAYS
    return false
  }
}
