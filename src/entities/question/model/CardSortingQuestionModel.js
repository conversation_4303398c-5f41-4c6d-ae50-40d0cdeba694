import { BEHAVIOR_ALWAYS } from '@shared/constants/logic'
import { declOfNum } from '@shared/helpers/string.js'
import { useTranslationsStore } from '@shared/store/translationsStore'
import shuffle from 'lodash.shuffle'
import { computed, nextTick, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { BaseQuestion } from './BaseQuestion'

export class CardSortingQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    this.translationsStore = useTranslationsStore()
    this.t = this.translationsStore.t
    this.selectedLang = this.translationsStore.selectedLang
    this.data = data
    this.isRequired = ref(!!data.isRequired)
    this.randomVariantsOrder = ref(!!data.random_variants_order)
    this.randomCategoriesOrder = ref(!!data.random_categories_order)
    this.minChooseVariants = ref(data.min_choose_variants)
    this.maxChooseVariants = ref(data.max_choose_variants)
    this.commentEnabled = ref(!!data.comment_enabled)
    this.skip = ref(!!data.skip)

    this._colNameCards = ref(data.card_column_text)
    this._colNameCategories = ref(data.category_column_text)
    this.colNameCards = computed(() => {
      return this.translations.value.card_column_text || this._colNameCards.value
    })
    this.colNameCategories = computed(() => {
      return this.translations.value.category_column_text || this._colNameCategories.value
    })

    this._skipText = ref(data.skip_text)
    this.skipText = computed(() => {
      return this.translations.value.skip_text || this._skipText.value || this.t('Затрудняюсь ответить')
    })
    this.skipped = ref(!!data.skipped)

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: toValue(this.enableGallery),
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this.description = computed(() => {
      return this.translations.value.description || data.description
    })

    this.error = ref(null)
    this.success = ref(null)
    this.touched = ref(false)

    this.detailAnswers = data.detail_answers || []

    this.cardSortingCategories = data.cardSortingCategories || []

    this.initItems = this.prepareItems(this.detailAnswers)
    this.items = ref([...this.initItems])
    this.initItemsCount = this.initItems.length
    this.itemsCategories = ref(this.prepareCategories(this.cardSortingCategories))

    this.hasPreviousAnswers = false
    if (this.previousAnswer?.answer) {
      this.hasPreviousAnswers = true
      const answer = JSON.parse(this.previousAnswer?.answer)
      Object.keys(answer).forEach((key) => {
        const category = this.itemsCategories.value.find(({ id }) => String(id) === key)
        answer[key].forEach((answerId) => {
          const card = this.items.value.find(({ id }) => String(id) === answerId)
          this.items.value = this.items.value.filter(({ id }) => String(id) !== answerId)
          category.cards.push(card)
        })
      })
    }
    if (this.previousAnswer?.skipped) {
      this.hasPreviousAnswers = true
    }

    if (this.isPreviewMode && this.items.value.length === 0) {
      this.items.value = this.prepareItems([{ id: 'fake-id', name: 'fake-name' }])
    }

    if (this.isPreviewMode && this.itemsCategories.value.length === 0) {
      this.itemsCategories.value = this.prepareCategories([{ id: 'fake-id', name: 'fake-name' }])
    }

    this.commentController = new CommentController({
      enabled: toValue(this.commentEnabled),
      required: !!data.comment_required,
      value: this.previousAnswer?.self_variant || '',
      placeholderText: data.placeholderText,
      minLength: data.textFieldParam?.min || 0,
      maxLength: data.textFieldParam?.max || 1000,
      title: data.comment_label || this.t('Ваш комментарий'),
      translations: this.translations,
    })

    this.isValid = computed(() => {
      return this.validate()
    })

    this.blocked = computed(() => {
      return toValue(this.touched) && !toValue(this.isValid)
    })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
      }
    })

    this.canMoveToNextQuestion = ref(false)

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        nextTick().then(() => {
          this.canMoveToNextQuestion.value = true
        })
      }
    })
  }

  prepareCategories(items) {
    const randomCategoriesOrder = this.randomCategoriesOrder.value
    const sortedItems = randomCategoriesOrder
      ? shuffle(items)
      : items.sort((a, b) => a.position - b.position)
    const preparedItems = sortedItems
      .map(({ name, ...item }) => {
        item.cards = ref([])
        item.name = computed(() => {
          const label = toValue(item.label)
          if (label) {
            return label
          }
          const itemId = item.id
          const detailLang = this.translations.value?.cardSortingCategorylangs?.find(({ category_id }) => category_id === itemId)
          if (detailLang && detailLang.name) {
            return detailLang.name
          }
          return name
        })

        return item
      })

    return preparedItems
  }

  prepareItems(items) {
    const randomVariantsOrder = this.randomVariantsOrder.value
    const sortedItems = randomVariantsOrder
      ? shuffle(items)
      : items.sort((a, b) => a.position - b.position)
    const preparedItems = sortedItems
      .map(({ name, ...item }) => {
        item.name = computed(() => {
          const label = toValue(item.label)
          if (label) {
            return label
          }
          const itemId = item.id
          const detailLang = this.translations.value?.detailLangs?.[itemId]
          if (detailLang && detailLang.question) {
            return detailLang.question
          }
          return item.variant || item.question
        })

        return item
      })

    return preparedItems
  }

  resetFields() {
    this.itemsCategories.value.forEach((category) => {
      category.cards = []
    })
    this.items.value = this.initItems
    this.error.value = null
    this.success.value = null
  }

  setAllAsTouched() {
    this.touched.value = true
    if (toValue(this.commentEnabled)) {
      this.commentController.touched.value = true
    }
  }

  validate() {
    this.error.value = null
    this.success.value = null

    if (this.skip.value && this.skipped.value)
      return true

    let valid = true
    let commentValid = true
    const currentItemsCount = this.items.value.length

    if (this.maxChooseVariants.value && currentItemsCount) {
      const minRest = this.initItemsCount - this.maxChooseVariants.value

      if (minRest === currentItemsCount) {
        this.success.value = this.t('Использовано максимальное количество карточек')
      }
      else {
        this.success.value = null
      }
    }

    if (!this.touched.value)
      return true

    const isRequired = toValue(this.isRequired)
    const touched = this.initItemsCount > currentItemsCount
    if ((isRequired || touched) && this.minChooseVariants.value) {
      const maxRest = this.initItemsCount - this.minChooseVariants.value

      if (maxRest < currentItemsCount) {
        valid = false
        const symbolWordForm = this.t(this.getVariantsWordForm(this.minChooseVariants.value))
        this.error.value = this.t(`Нужно переместить хотя бы {count} {card}`, { count: toValue(this.minChooseVariants), card: symbolWordForm.value })
      }
    }
    else if (isRequired && !touched) {
      valid = false
      this.error.value = this.t('Нужно переместить карточки')
    }

    if (toValue(this.commentEnabled)) {
      commentValid = this.commentController.validate()
    }

    return valid && commentValid
  }

  getData() {
    if (toValue(this.skip) && toValue(this.skipped)) {
      return { skipped: 1 }
    }

    const data = {
      answer: {},
    }

    this.itemsCategories.value.map((i) => {
      return data.answer[i.id] = i.cards.map(({ id }) => id)
    })

    if (toValue(this.commentEnabled)) {
      data.comment = toValue(this.commentController.value)
    }

    return data
  }

  get hasValue() {
    return this.hasPreviousAnswers
  }

  getVariantsWordForm(n) {
    return declOfNum(n, ['карточку', 'карточки', 'карточек'])
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (toValue(this.skip) && this.skipped.value)
        return ''

      // TODO
      return this.items.value.map((item, index) => `${index + 1}. ${toValue(item.name)}`).join('; ')
    })
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Priority questions only support BEHAVIOR_ALWAYS
    return condition.behavior === BEHAVIOR_ALWAYS
  }

  updateFromPreview(previewData) {
    // Call parent update first
    super.updateFromPreview(previewData)

    this.error.value = null
    this.success.value = null
    this.touched.value = false

    this.enableGallery.value = !!previewData.enableGallery
    this.skip.value = !!previewData.skip
    this._skipText.value = previewData.skip_text
    // Update random variants order
    this.randomVariantsOrder.value = !!previewData.random_variants_order
    this.randomCategoriesOrder.value = !!previewData.random_categories_order
    this.minChooseVariants.value = previewData.min_choose_variants
    this.maxChooseVariants.value = previewData.max_choose_variants
    this._colNameCards.value = previewData.card_column_text
    this._colNameCategories.value = previewData.category_column_text

    // Update detail answers and items
    if (previewData.variants) {
      const detailAnswers = previewData.variants.map((item, index) => ({
        ...item,
        id: item.persistentId || item.id,
        question: item.value,
        position: item.position || index,
      }))

      this.initItems = this.prepareItems(detailAnswers)
      this.initItemsCount = this.initItems.length
      this.items.value = [...this.initItems]
    }

    if (previewData.cardSortingCategories) {
      const cardSortingCategories = previewData.cardSortingCategories.map((item, index) => ({
        ...item,
        id: item.persistentId || item.id,
        name: item.value,
        position: item.position || index,
      }))
      this.itemsCategories.value = this.prepareCategories(cardSortingCategories)
    }

    // Update gallery
    if (previewData.enableGallery !== undefined) {
      this.enableGallery.value = previewData.enableGallery
    }

    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: previewData.gallery || [],
      })
    }

    this.commentEnabled.value = !!previewData.comment_enabled

    this.commentController.updateFromPreview({
      enabled: toValue(this.commentEnabled),
      required: !!previewData.comment_required,
      title: previewData.comment_label || 'Ваш комментарий',
      placeholderText: previewData.placeholder_text || '',
      textFieldParam: {
        min: previewData.comment_minlength || 0,
        max: previewData.comment_maxlength || 1000,
      },
    })

    // Reset fields to ensure proper state
    this.resetFields()
  }
}
