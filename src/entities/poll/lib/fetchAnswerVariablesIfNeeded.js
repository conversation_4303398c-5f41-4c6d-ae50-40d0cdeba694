import { FILIALS_QUESTION, MASK_TYPES, TEXT_QUESTION } from '@entities/question/model/types'
import { fetchAnswerVariables } from '../api'

/**
 * Проверяет, требуют ли какие-либо вопросы на странице получения обновленных переменных
 * @param {Array} questions - Массив вопросов на странице
 * @returns {boolean} True, если необходимо получить переменные
 */
function shouldFetchVariables(questions) {
  return questions.some((question) => {
    // Проверяем, есть ли вопрос с выбором филиала с выбранными филиалами
    if (question.type === FILIALS_QUESTION) {
      const data = question.getData()
      return data?.detail_item?.length > 0
    }

    // Проверяем, есть ли вопрос с текстовым полем для ввода номера телефона или email
    const maskType = question.maskType?.value
    const phoneOrEmailMaskType = maskType === MASK_TYPES.PHONE || maskType === MASK_TYPES.EMAIL
    if (question.type === TEXT_QUESTION && phoneOrEmailMaskType) {
      const data = question.getData()
      return !!data?.answer
    }

    return false
  })
}

/**
 * Получает переменные при необходимости в зависимости от типов вопросов и ответов
 * @param {Array} questions - Массив вопросов на текущей странице
 * @param {string} authKey - Ключ авторизации
 * @returns {Promise<object|null>} Полученные переменные или null, если получение не требуется
 */
export async function fetchAnswerVariablesIfNeeded(questions, authKey) {
  if (!shouldFetchVariables(questions)) {
    return null
  }

  try {
    return await fetchAnswerVariables(authKey)
  }
  catch (error) {
    console.error('Failed to fetch answer variables:', error)
    return null
  }
}
