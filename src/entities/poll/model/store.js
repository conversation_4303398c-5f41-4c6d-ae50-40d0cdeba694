import { useThemeStore } from '@/features/custom-themes/model/themeStore'
import { fetchPollData, setupPollTimer, updatePollStatus } from '@entities/poll/api'
import { createModelByType } from '@entities/question'
import { usePageController } from '@entities/question/controllers/PageController'
import { INTER_BLOCK } from '@entities/question/model/types'
import { usePointsStore } from '@features/points/store/pointsStore'

// #if VITE_USE_SENTRY
import * as Sentry from '@sentry/vue'
// #endif
import { POLL_NAVIGATION_TYPE } from '@shared/constants'
import { getDefaultDesignSettings } from '@shared/helpers/design'
import { serialize } from '@shared/helpers/general'
import { prepareQuestionPreviewData } from '@shared/helpers/previewDataPreparator'
import { usePreviewStore } from '@shared/store/previewStore'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import shuffle from 'lodash.shuffle'
import { defineStore, storeToRefs } from 'pinia'
import { computed, markRaw, nextTick, ref, shallowRef, toValue, triggerRef } from 'vue'
import { INTERMEDIATE_BLOCK_TYPES } from '../../question/model/types'
import { saveAnswer } from '../api'
import { fetchAnswerVariablesIfNeeded } from '../lib/fetchAnswerVariablesIfNeeded'

let timerWorker = null

/**
 * Обогащает объект страницы вычисляемыми свойствами и реактивной функциональностью.
 * Эта функция добавляет различные вычисляемые свойства для отслеживания состояния вопросов на странице,
 * включая видимость, валидацию, блокировку и логику отображения в навигации.
 *
 * @param {object} page - Объект страницы для обогащения
 * @param {string} page.id - Уникальный идентификатор страницы
 * @param {Array} page.questions - Массив объектов вопросов на странице
 * @param {string} [page.name] - Опциональное название/заголовок страницы
 * @param {string} [page.type] - Тип страницы ('start', 'end', 'intermediate' или 'default')
 * @param {boolean} [page.randomOrder] - Нужно ли отображать вопросы в случайном порядке
 * @param {boolean} isNonFunctionalPreview - Отображается ли страница в режиме нефункционального предпросмотра
 * @param {import('vue').Ref<Array<string>>} pageIdsSkippedByLogic - Массив ID страниц, которые должны быть пропущены согласно логике
 * @param {boolean} isEditMode - Флаг режима редактирования
 * @param {Array} globalRandomExclusion - Массив ID вопросов, которые нужно исключить из рандомизации глобально
 * @returns {object} Обогащенный объект страницы с дополнительными вычисляемыми свойствами:
 * @property {import('vue').ShallowRef} questions - Реактивная ссылка на вопросы страницы
 * @property {import('vue').ComputedRef} visibleQuestions - Вопросы, которые должны быть видимы согласно логике
 * @property {import('vue').ComputedRef} blocked - Заблокирован ли хотя бы один видимый вопрос
 * @property {import('vue').ComputedRef} isValid - Валидны ли все видимые вопросы
 * @property {import('vue').ComputedRef} isVisible - Есть ли на странице видимые вопросы и не пропущена ли она логикой
 * @property {import('vue').ComputedRef} skipped - Пропущены ли все видимые вопросы
 * @property {import('vue').ComputedRef} visibleInNavigation - Должна ли страница отображаться в навигации
 * @property {import('vue').ComputedRef} name - Отображаемое имя страницы
 */
function enrichPageObject(page, isNonFunctionalPreview = false, pageIdsSkippedByLogic = [], isEditMode = false, globalRandomExclusion = []) {
  // Store questions in a reactive variable
  const questions = shallowRef(page.questions)

  const forceVisible = ref(false)

  if (page.randomOrder && !isNonFunctionalPreview) {
    questions.value = shuffleItems(questions.value, globalRandomExclusion || [], isNonFunctionalPreview, 'question')
  }

  const visibleQuestions = computed(() => {
    // Use the reactive questions variable instead of page.questions
    return toValue(questions).filter((question) => {
      if (isNonFunctionalPreview) {
        return true
      }

      if (forceVisible.value) {
        return true
      }

      // In edit mode, always show all questions
      if (isEditMode) {
        return true
      }

      const isVisibleByViewLogic = toValue(question.isVisibleByViewLogic())
      const donorHasLogicLeadingToEnd = toValue(question.donorHasLogicLeadingToEnd())

      // вопрос-реципиент отображается, если он в доноре выбраны нужные варианты
      // и у донора нет логики, которая ведет на конец опроса
      const isRecipientVisible = toValue(question.isRecipientVisible()) && !donorHasLogicLeadingToEnd

      return isRecipientVisible && isVisibleByViewLogic
    })
  })

  const hasMultipleQuestions = computed(() => toValue(visibleQuestions).length > 1)
  return {
    ...page,
    questions, // Return the reactive questions array
    forceVisible,
    visibleQuestions,
    // Страница заблокирована, если хотя бы один вопрос на ней заблокирован
    blocked: computed(() => {
      const isBlocked = toValue(visibleQuestions).some(q => toValue(q.blocked))
      return isBlocked
    }),
    // Страница валидна, если все вопросы на ней валидны
    isValid: computed(() => {
      return toValue(visibleQuestions).every(q => toValue(q.isValid))
    }),
    // Страница видима, если на ней есть хотя бы один видимый вопрос
    isVisible: computed(() => {
      if (isNonFunctionalPreview) {
        return true
      }

      // In edit mode, always show all pages
      if (isEditMode) {
        return true
      }

      if (forceVisible.value) {
        return true
      }

      const idsToSkip = toValue(pageIdsSkippedByLogic)

      const isVisible = toValue(visibleQuestions).length > 0

      if (!Array.isArray(idsToSkip)) {
        return isVisible
      }

      const isSkippedByLogic = idsToSkip.includes(page.id)
      return isVisible && !isSkippedByLogic
    }),
    skipped: computed(() => {
      return toValue(visibleQuestions).every(q => toValue(q.skipped))
    }),

    // Видимость страницы в навигации
    // Страница может не отображаться в навигации,
    // если первый вопрос на ней не должен отображаться в навигации
    visibleInNavigation: computed(() => hasMultipleQuestions.value ? true : toValue(questions.value?.[0]?.showNumber)),
    name: computed(() => toValue(page.name) || toValue(questions.value?.[0]?.name)),
  }
}

/**
 * Подготовка страниц опроса на основе параметров
 * Важно: в данной функции перед инициализацией инстансов вопросов,
 * мы используем функцию markRaw, чтобы vue не делал их глубоко реактивными
 * @see https://vuejs.org/api/reactivity-advanced.html#markraw
 * @param {object} params - Параметры для подготовки страниц.
 * @param {Array} params.questions - Массив вопросов.
 * @param {boolean} params.isPagesModeEnabled - Флаг, указывающий, что режим страниц включен.
 * @param {object} params.displaySetting - Настройки отображения страниц.
 * @param {Array} params.displayPages - Массив страниц отображения.
 * @param {import('vue').Ref<Array<string>>} params.pageIdsSkippedByLogic - Массив id страниц, которые были пропущены по логике.
 * @param {Function} params.t - Функция перевода.
 * @param {boolean} params.isPreview - Флаг режима предпросмотра.
 * @param {boolean} params.isWidgetPreview - Флаг режима превью виджета.
 * @param {boolean} params.isNonFunctionalPreview - Флаг режима нефункционального превью.
 * @param {string | null} params.previewType - Тип превью (если null, то превью не активно).
 * @param {boolean} params.isEditMode - Флаг режима редактирования.
 * @returns {Array} Подготовленные страницы.
 */
function preparePages({
  questions,
  isPagesModeEnabled,
  displaySetting,
  displayPages,
  pageIdsSkippedByLogic = [],
  t,
  isPreview = false,
  isWidgetPreview = false,
  isNonFunctionalPreview = false,
  previewType = null,
  isEditMode = false,
}) {
  const preparedPages = []

  // Является ли страница начальным экраном
  const isStartScreen = q => q.intermediateBlock?.screen_type === 2 || toValue(q.screenType) === 2

  // Является ли страница текстовым (промежуточным) экраном
  const isTextScreen = q => q.intermediateBlock?.screen_type === 1 || toValue(q.screenType) === 1

  // Является ли страница конечным экраном
  const isEndScreen = q => q.intermediateBlock?.screen_type === 3 || toValue(q.screenType) === 3

  // Логика добавления начальных страниц с дополнительными свойствами
  const startPages = questions
    .filter(q => isStartScreen(q))
    .map(q => enrichPageObject({
      id: q.question_id,
      questions: [markRaw(createModelByType(q.type, q, previewType))],
      name: q.name,
      type: 'start',
    }, isNonFunctionalPreview, pageIdsSkippedByLogic))

  preparedPages.push(...startPages)

  let contentPages = []

  if (!isPagesModeEnabled) {
    contentPages = questions
      .filter(q => !isStartScreen(q) && !isEndScreen(q))
      .map((q) => {
        const isText = isTextScreen(q)
        if (isPreview || isWidgetPreview) {
          q.answer = null
        }
        const enrichedPage = enrichPageObject({
          id: q.question_id,
          questions: [markRaw(createModelByType(q.type, q, previewType))],
          type: isText ? 'intermediate' : 'default',
        }, isNonFunctionalPreview, pageIdsSkippedByLogic, isEditMode, displaySetting?.random_exclusion || [])
        return enrichedPage
      })
  }
  else {
    contentPages = displayPages.map((page) => {
      const pageQuestions = page.questions.map((q) => {
        const questionData = questions.find(fullQ => fullQ.question_id === q.id)
        if (!questionData) {
          return null
        }
        if (isPreview || isWidgetPreview) {
          questionData.answer = null
        }
        return markRaw(createModelByType(questionData.type, questionData, previewType))
      }).filter(Boolean)

      const isText = pageQuestions?.length === 1 && isTextScreen(pageQuestions[0])
      return enrichPageObject({
        id: page.id,
        type: isText ? 'intermediate' : 'default',
        randomOrder: page.random_order === 1,
        randomExclusion: page.random_exclusion,
        questions: pageQuestions,
        name: page.name,
      }, isNonFunctionalPreview, pageIdsSkippedByLogic, isEditMode, displaySetting?.random_exclusion || [])
    })
  }

  if (displaySetting?.random_order === 1) {
    let exclusionIds = displaySetting?.random_exclusion || []
    if (Array.isArray(displayPages) && displayPages.length > 0) {
      exclusionIds = displayPages
        .map(p => p.random_exclusion === 1 ? p.id : null)
        .filter(Boolean)
    }
    contentPages = shuffleItems(contentPages, exclusionIds, isNonFunctionalPreview, 'page')
  }

  preparedPages.push(...contentPages)

  // Логика добавления конечных страниц с дополнительными свойствами
  const endPages = questions
    .filter(q => isEndScreen(q))
    .map(q => enrichPageObject({
      id: q.question_id,
      questions: [markRaw(createModelByType(q.type, q, previewType))],
      name: q.name,
      type: 'end',
    }, isNonFunctionalPreview, pageIdsSkippedByLogic, isEditMode))

  preparedPages.push(...endPages)

  // Логика добавления конечной страницы по умолчанию
  preparedPages.push(enrichPageObject({
    id: 'end-default',
    questions: [markRaw(createModelByType(INTER_BLOCK, {
      id: 'question-end-default',
      type: INTER_BLOCK,
      name: 'end',
      question_id: 'end-default',
      variants: [],
      values: [],
      chooseMedia: [],
      images: [],
      gallery: [],
      endScreenImages: [],
      isRequired: 1,
      intermediateBlock: {
        text: computed(() => `<div class="end-page-text">${toValue(t('Опрос успешно пройден!'))}</div>`),
        screen_type: 3,
      },
    }, previewType),
    )],
    name: 'end',
    type: 'end',
  }, isNonFunctionalPreview, pageIdsSkippedByLogic, isEditMode))

  return preparedPages
}

/**
 * Перемешивает массив элементов (может быть страницами или вопросами), учитывая исключения и специальные типы страниц.
 * @param {Array} items - Массив элементов для перемешивания.
 * @param {Array} exclusionIds - Массив идентификаторов элементов, которые нужно исключить из перемешивания.
 * @param {boolean} isNonFunctionalPreview - Флаг режима нефункционального превью.
 * @param {'page' | 'question'} type - Тип элементов для определения какое свойство ID использовать.
 * @returns {Array} - Перемешанный массив элементов.
 */
function shuffleItems(items, exclusionIds = [], isNonFunctionalPreview = false, type = 'page') {
  // Если в режиме нефункционального превью, то возвращаем элементы как есть
  if (isNonFunctionalPreview) {
    return items
  }

  const regularItems = items

  const normalizedExclusionIds = Array.isArray(exclusionIds) ? exclusionIds : [exclusionIds]

  // Определяем функцию для получения ID в зависимости от типа
  const getItemId = (item) => {
    return type === 'question' ? item.questionId : item.id
  }

  // Определяем элементы для перемешивания и фиксированные элементы
  const fixedItems = regularItems.filter(item => normalizedExclusionIds.includes(getItemId(item)))
  const shufflableItems = regularItems.filter(item => !normalizedExclusionIds.includes(getItemId(item)))

  // Перемешиваем элементы, подлежащие перемешиванию
  const shuffledItems = shuffle(shufflableItems)

  // Возвращаем фиксированные элементы на их исходные позиции
  fixedItems.forEach((item) => {
    const originalIndex = regularItems.findIndex(p => getItemId(p) === getItemId(item))
    shuffledItems.splice(originalIndex, 0, item)
  })

  return shuffledItems
}

/**
 * Хук для получения состояния опроса.
 * @returns {object} Стор, содержащий состояние и методы для управления опросом.
 */
export const usePollStore = defineStore('poll', () => {
  const translationsStore = useTranslationsStore()
  const t = translationsStore.t

  const previewStore = usePreviewStore()
  const pointsStore = usePointsStore()
  const simplifiedStore = useSimplifiedStore()
  const tabletStore = useTabletStore()
  const themeStore = useThemeStore()

  /**
   * @type {import('vue').Ref<null | HTMLElement>}
   *
   * Ссылка на корневой элемент приложения
   */
  const appRootRef = ref(null)

  /**
   * @type {import('vue').Ref<object>}
   *
   * Настройки дизайна опроса
   */
  const design = ref({})

  const questionsById = shallowRef({})
  const allQuestions = shallowRef([])

  const poll = ref(null)

  /**
   * @type {import('vue').Ref<null | string>}
   *
   * Ключ опроса. Берем из URL, например /F66f562df8bc37
   */
  const pollKey = ref(null)

  /**
   * Статус опроса
   *
   * open - опрос открыт
   * in-progress - опрос в процессе
   * done - опрос завершен
   */
  const pollStatus = ref('open')

  /**
   * Флаг, указывающий, что опрос инициализирован
   */
  const isPollInitialized = ref(false)

  /**
   * Очередь действий, которые нужно выполнить после инициализации опроса
   * @NOTE: В режиме предпросмотра опроса, сообщения от родительского окна приходят до того, как опрос инициализируется.
   * Поэтому все действия, которые нужно выполнить после инициализации опроса, нужно добавлять в эту очередь.
   */
  const pendingPreviewActions = ref([])

  /**
   * @typedef {object} Jump
   * @property {number} fromPageIndex - индекс страницы, с которой осуществлен переход.
   * @property {string} fromPageId - id страницы, с которой осуществлен переход.
   * @property {object} to - объект вопроса, на который осуществлен переход.
   * @property {string} to.id - id вопроса или страницы, на который осуществлен переход.
   * @property {'question' | 'page'} to.type - тип вопроса или страницы, на который осуществлен переход.
   */

  /**
   * @type {import('vue').Ref<Array<Jump>>}
   *
   * Массив последних переходов.
   * Используется для отслеживания логики переходов при возвращении назад.
   * Если у страницы/вопроса включена логика перехода, то можно прыгать от вопроса к вопросу.
   * Например:
   * В опросе есть:
   * - Вопрос А
   * - Вопрос Б
   * - Вопрос В
   *
   * 1. Проходим вопрос А (�� логикой перехода на В)
   * 2. Переходим на вопрос В
   * 3. Нажимаем на кнопку "Назад"
   * 4. Возвращаемся на вопрос А
   *
   * После выполнения пункта 2 в currentJumps будет массив вида:
   * [{ from: 0, fromPageId: 'page-a', to: { id: 'question-b', type: 'question' } }]
   */
  const currentJumps = ref([])

  /**
   * Флаг, указывающий, что опрос загружается
   */
  const isLoading = ref(false)

  /**
   * Ошибка при загрузке опроса
   */
  const fetchPollError = ref(null)

  /**
   * @type {import('vue').Ref<Array>}
   *
   * Массив страниц опроса. Содержит странцы со всеми вопросами. Включая:
   * - начальные
   * - конечные
   * - промежуточные
   */
  const pages = ref([])

  /**
   * Определяем, связан ли опрос с клиентом
   * Если нет, значит опрос является анонимным и общедоступным
   */
  const hasContact = ref(false)

  /**
   * Флаг, указывающий, что в дизане опроса включена опция "Выбор языка опроса"
   */
  const languageSelectEnabled = ref(false)

  /**
   * Заголовок и описание опроса
   * Добавляется в head страницы
   */
  const pollTitle = ref('')
  const pollDescription = ref('')

  /**
   * В задаче 4795 добавились настройки выравнивания для заголовков вопроса
   * Для обратной совместимости (раньше выравнивание было по дефолту по середине)
   */
  const isOldTitleFormat = ref(true)

  /**
   * Максимальный размер файла для загрузки в Мб
   * @type {import('vue').Ref<number>}
   */
  const maxFileSize = ref(null)

  /**
   * @type {import('vue').Ref<null | 1 | 2>}
   *
   * Режим отображения навигации (Опция "Отображение процесса" в настройках опроса)
   * null - нет навигации
   * 1 - постраничная навигация
   * 2 - постраничная навигация с прогресс-баром
   */
  const showNavigationType = ref(POLL_NAVIGATION_TYPE.NUMBERS)

  /**
   * Режим тестового прохождения опроса
   * Включен, если опрос не опубликован
   * @type {import('vue').Ref<null | 1 | 2>}
   */
  const testMode = ref(null)

  const withPoints = ref(false)

  /**
   * Настройки отображения страниц
   * Объект приходит с сервера если для опроса включена "Логика"
   * @type {import('vue').Ref<null | object>}
   */
  const displaySettings = ref(null)

  /**
   * Флаг, указывающий, что опрос использует постраничное отображение
   * Его можно включить/отключить через раздел "Логика" в настройках опроса
   * Важно: в коде мы всегда используем структуру: pages[pageIndex].questions[questionIndex]
   * Просто если режим отключен, то pages будет содержать ВСЕГДА один вопрос
   * При включенном постраничном отображении pages может содержать несколько вопросов
   * @type {import('vue').Ref<boolean>}
   */
  const isPagesModeEnabled = ref(false)

  /**
   * Если для опроса в разделе Логика включена опция Случайный порядок страниц,
   * то страницы в прохождении опроса нужно показывать в рандомном порядке.
   * @type {import('vue').Ref<boolean>}
   */
  const randomOrder = ref(false)

  const activePageIndex = ref(0)
  const showPrevButton = ref(false)
  const finishLink = ref('')
  const authKey = ref(null)
  const variables = ref({})
  const pageController = shallowRef(null)
  const navigationDirection = ref('next')
  const showFoquzLink = ref(false)
  const showFoquzLabel = ref(false)
  /**
   * Таймер опроса
   * @type {import('vue').Ref<object>}
   */
  const timer = ref({
    enabled: false,
    status: 'not-started', // 'not-started' | 'active' | 'finished'
    duration: 0, // общее время на прохождение в секундах
    timeLeft: 0, // сколько времени осталось в секундах
  })

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что превышен лимит указанный в настройках
   * Используется дя отображения экрана с "Опрос недоступен"
   */
  const isAnswersLimitsOver = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что превышен лимит тестового режима
   * Используется для отображения экрана с
   * "К сожалению, лимит для прохождения опроса в тестовом режиме исчерпан."
   */
  const isTestModeLimitOver = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что лимит ответов превышен для компании.
   * Если лимит ответов у компании превышен, то в прохождении опросов этой компании показывается информационный экран.
   */
  const isCompanyAnswersLimitsOver = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что период опроса истек
   * Если опрос опубликован, включена опция "Ограничить период доступности опроса"
   * и текущее время прохождения не входит в этот период, то показать информационный экран.
   */
  const isPollPeriodOver = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что опрос активен
   * Опубликованный опрос можно выключить через кнопку Выключить опрос в шапке опроса.
   */
  const isPollActive = ref(true)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что нужно показывать рекламный баннер "Создайте свой опрос в FOQUZ"
   */
  const showAdvertisementBanner = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, нужно показывать баннер с согласием
   * пользователя на обработку персональных данных
   */
  const showUserConsentBanner = ref(false)

  /**
   * @type {import('vue').Ref<Array<number>>}
   *
   * Массив ID вопросов с ограничениями по квотам
   * Если в предыдущем переходе были такие вопросы, кнопка "Назад" должна быть скрыта
   */
  const quoteRestrictedQuestionIds = ref([])

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что в опросе есть вопросы с ограничениями по квотам
   */
  const hasQuoteRestrictedQuestions = computed(() => {
    return quoteRestrictedQuestionIds.value.length > 0
  })

  /**
   * Флаг, указывающий, что опрос находится в архиве
   * Пользователь может создать опрос, выложить на него ссылку, а потом перенести в Архив.
   * В этом случае отображается информационный экран с баннером.
   */
  const isPollArchived = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   * Флаг, указывающий, что произошла ошибка при сохранении ответа
   */
  const saveAnswerError = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что нужно показывать информационный экран квотафул
   * Показывается когда endScreen === null (стандартный инфо экран квотафул)
   */
  const showQuoteFullInfoScreen = ref(false)

  /**
   * @type {import('vue').Ref<number | null>}
   *
   * ID конечного экрана при превышении лимитa квоты (достигли статуса "Квотафул")
   * (Лимит может быть превышен по ссылке или по ответам)
   *
   * ИЛИ ID конечного экрана для анкеты, которая подошла под квоту, но лимиты не превышены
   *
   * null – если конечный экран не установлен
   * 0 – если конечный экран установлен на стандартное завершение
   * number – если конечный экран установлен на конкретный экран
   */
  const quotaEndScreen = ref(null)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг для отображения конечного экрана при превышении лимитa квоты (достигли статуса "Квотафул")
   */
  const showQuotaEndScreen = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   *
   * Флаг, указывающий, что истекло Время для заполнения анкеты от момента отправки респонденту
   * Если время истекло, то в прохождении опроса показывается информационный экран.
   */
  const isResponseTimeExpired = ref(false)

  /**
   * Флаг, указывающий, что происходит сохранение ответа
   */
  const isSavingAnswer = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   * Флаг, указывающий, что можно редактировать ответы после завершения опроса
   */
  const allowEditAfterDone = ref(false)

  /**
   * @type {import('vue').Ref<boolean>}
   * Флаг, указывающий, что опрос находится в режиме редактирования
   */
  const isEditMode = ref(false)

  /**
   * @type {import('vue').Ref<null | string>}
   * ID вопроса, который нужно отобразить. Может быть использован в режиме редактирования анкеты (при edit: 1 и view: N)
   */
  const viewQuestionId = ref(null)

  const {
    selectedLang,
  } = storeToRefs(translationsStore)

  const backButtonText = computed(() => {
    return selectedLang.value?.back_text || toValue(design.value)?.back_text || t('Вернуться')
  })
  const nextButtonText = computed(() => {
    return selectedLang.value?.next_text || toValue(design.value)?.next_text || t('Далее')
  })
  const finishButtonText = computed(() => {
    return selectedLang.value?.finish_text || toValue(design.value)?.finish_text || t('Завершить')
  })

  const explicitlySetUnrequiredText = ref(null)
  const unrequiredText = computed(() => {
    const unrequiredTextFromLang = selectedLang.value?.unrequired_text
    const unrequiredTextFromDesign = toValue(design.value)?.unrequired_text

    if (explicitlySetUnrequiredText.value !== null) {
      return explicitlySetUnrequiredText.value
    }

    // Метка необязательного вопроса из переводов
    if (unrequiredTextFromLang !== undefined && unrequiredTextFromLang !== null) {
      return unrequiredTextFromLang
    }

    // Метка необязательного вопроса из настроек дизайна
    if (unrequiredTextFromDesign !== undefined && unrequiredTextFromDesign !== null) {
      return unrequiredTextFromDesign
    }

    return ''
  })

  const activePage = computed(() => pages.value[activePageIndex.value])
  const activePageId = computed(() => activePage.value?.id)

  const forcePrevButtonToBeDisabled = ref(false)
  const forceNextButtonToBeDisabled = ref(false)

  /**
   * Флаг, указывающий, что нужно отключить автопрокрутку вопросов
   */
  const disableQuestionAutoscroll = ref(false)

  /**
   * Флаг, указывающий, что активная страница содержит несколько вопросов
   * Используется для отображения дополнительных кнопок навигации в зависимости от количества вопросов на странице
   */
  const activePageContainsMultipleQuestions = computed(() => {
    if (!isPagesModeEnabled.value) {
      return false
    }
    return toValue(activePage.value.visibleQuestions).length > 1
  })

  const defaultPages = computed(() => {
    return pages.value.filter(p => p.type !== 'start' && p.type !== 'end')
  })

  const isLastDefaultPage = computed(() => {
    const lastPage = defaultPages.value[defaultPages.value.length - 1]
    if (!lastPage) {
      return false
    }
    return lastPage.id === activePageId.value
  })

  // Add these to the existing store
  const currentPageIsValid = computed(() => {
    if (!activePage.value || !activePage.value.questions)
      return true
    return toValue(activePage.value.visibleQuestions).every(q => toValue(q.isValid))
  })

  /**
   * Страницы, которые не являются стартовой и конечной, и которые имеют хотя бы один видимый вопрос
   */
  const defaultVisiblePages = computed(() => {
    return pages.value?.filter?.(p => p.type !== 'start' && p.type !== 'end' && toValue(p.visibleQuestions).length) || []
  })

  const isFirstPage = computed(() => {
    const visiblePages = defaultVisiblePages.value
    const first = visiblePages[0]
    if (!first) {
      return false
    }
    return first.id === activePageId.value
  })

  const isLastPage = computed(() => {
    const visiblePages = defaultVisiblePages.value
    const last = visiblePages[visiblePages.length - 1]
    if (!last) {
      return false
    }
    return last.id === activePageId.value
  })

  /**
   * Определяет, имеет ли активная страница логику, которая ведет к концу опроса
   * @type {import('vue').ComputedRef<boolean>}
   */
  const activePageHasLogicLeadingToEnd = computed(() => {
    const page = activePage.value

    // @NOTE: В режиме нефункционального предпросмотра не учитываем логику перехода
    if (!page || previewStore.isNonFunctionalPreview) {
      return false
    }

    const visibleQuestions = toValue(page.visibleQuestions)

    const firstLogicJump = visibleQuestions
      .map(q => q.getNextLogicItemId?.())
      .find(jump => jump)

    return firstLogicJump?.type === 'end'
  })

  /**
   * Определяет, следует ли показывать кнопку "Завершить" вместо "Далее"
   * @type {import('vue').ComputedRef<boolean>}
   * Отображаем кнопку "Завершить", если:
   * 1. Это последняя видимая страница (нет видимых страниц после нее)
   * 2. Есть логика, которая ведет к концу опроса
   */
  const activePageLeadsToEnd = computed(() => {
    const page = activePage.value
    if (!page) {
      return false
    }

    // Проверяем, есть ли видимые страницы после текущей
    const pagesAfterCurrent = pages.value.slice(activePageIndex.value + 1)
    const visiblePagesAfterCurrent = pagesAfterCurrent.filter(p => toValue(p.isVisible) && p.type !== 'start' && p.type !== 'end')

    if (visiblePagesAfterCurrent.length === 0) {
      return true
    }

    // Проверяем, есть ли логика, которая ведет к концу опроса
    return toValue(activePageHasLogicLeadingToEnd)
  })

  const shouldShowFinishButton = computed(() => {
    // Определяем, следует ли показывать кнопку "Завершить"
    return isLastDefaultPage.value || activePageLeadsToEnd.value
  })

  const forwardButtonText = computed(() =>
    shouldShowFinishButton.value ? finishButtonText.value : nextButtonText.value,
  )

  const isStartPage = computed(() => {
    return activePage.value?.type === 'start'
  })

  const isEndPage = computed(() => {
    return activePage.value?.type === 'end'
  })

  /**
   * Массив объектов, содержащий id страниц и массив id вопросов на каждой странице
   * @example
   * [{ id: 123, questionIds: ['q1', 'q2'] }]
   */
  const defaultPagesIdsWithQuestionIds = computed(() => {
    return pages.value
      .map(p =>
        ({
          id: p.id,
          questionIds: toValue(p.questions).map(q => q.questionId),
        }),
      )
  })

  const pageIdsSkippedByLogic = computed(() => {
    const idsWithQuestionIds = defaultPagesIdsWithQuestionIds.value
    const currentIndex = activePageIndex.value
    const jumps = currentJumps.value

    // Track which pages are not skipped (either source or destination of jumps)
    const notSkippedPageIds = new Set()

    // Add pages involved in jumps to not skipped set
    jumps.forEach((jump) => {
      // Add source page
      notSkippedPageIds.add(jump.fromPageId)

      if (jump.to.type === 'question') {
        // Add destination page (find page containing the target question)
        const targetPage = idsWithQuestionIds.find(page =>
          page.questionIds.includes(jump.to.id),
        )
        if (targetPage) {
          notSkippedPageIds.add(targetPage.id)
        }
      }
      else if (jump.to.type === 'page') {
        notSkippedPageIds.add(jump.to.id)
      }
    })

    // Check current page's logic jumps
    const activePage = pages.value[activePageIndex.value]

    if (activePage) {
      // Add current page to not skipped set
      notSkippedPageIds.add(activePage.id)

      const visibleQuestions = toValue(activePage.visibleQuestions)

      // Get all logic next question IDs from current page
      let logicNextIds = visibleQuestions
        .map(q => q.getNextLogicItemId?.())
        .filter(Boolean)
        .map(jump => ({ id: jump.id, type: jump.type }))

      // leave only first item if logicNextIds is array
      if (logicNextIds.length) {
        logicNextIds = [logicNextIds[0]]
      }

      // if logicNextIds is empty, then we don't need to skip any pages
      if (!logicNextIds.length) {
        const pagesAfterCurrent = pages.value.slice(currentIndex + 1)
        pagesAfterCurrent.forEach((page) => {
          notSkippedPageIds.add(page.id)
        })
      }

      // Add pages containing logic next questions to not skipped
      logicNextIds.forEach(({ id, type }) => {
        if (type === 'page') {
          notSkippedPageIds.add(id)
          return
        }
        if (type === 'end') {
          return
        }
        const targetPage = idsWithQuestionIds.find(page =>
          page.questionIds.includes(id),
        )
        if (targetPage) {
          notSkippedPageIds.add(targetPage.id)
          const pagesAfterTarget = pages.value.slice(
            pages.value.findIndex(p => p.id === targetPage.id) + 1,
          )
          pagesAfterTarget.forEach((page) => {
            notSkippedPageIds.add(page.id)
          })
        }
      })

      // Find range of skipped pages between current and target
      logicNextIds.forEach(({ id, type }) => {
        let targetIndex
        if (type === 'page') {
          targetIndex = pages.value.findIndex(p => p.id === id)
        }
        else if (type === 'end') {
          return
        }
        else {
          const targetPage = idsWithQuestionIds.find(page =>
            page.questionIds.includes(id),
          )
          if (targetPage) {
            targetIndex = pages.value.findIndex(p => p.id === targetPage.id)
          }
        }

        if (targetIndex !== -1) {
          // Mark all pages after target as not skipped
          for (let i = targetIndex; i < pages.value.length; i++) {
            const pageId = pages.value[i]?.id
            if (pageId) {
              notSkippedPageIds.add(pageId)
            }
          }

          // Mark pages between current and target as skipped
          const start = Math.min(currentIndex, targetIndex)
          const end = Math.max(currentIndex, targetIndex)

          for (let i = start + 1; i < end; i++) {
            const pageId = pages.value[i]?.id
            if (pageId && !notSkippedPageIds.has(pageId)) {
              notSkippedPageIds.delete(pageId)
            }
          }
        }
      })
    }

    // Return all page IDs that are not in the notSkipped set
    return idsWithQuestionIds
      .map(page => page.id)
      .filter(id => !notSkippedPageIds.has(id))
  })

  /**
   * Все видимые вопросы на всех страницах
   * Вопросы, у которых отключена опция "Отображать номер вопроса",
   * не будут отображаться в навигации
   * Такая опция может быть у промежуточных экранов
   */
  const visibleQuestionsInNavigation = computed(() => {
    return pages.value.reduce((acc, page) => {
      // Пропускаем страницы со стартовым и конечным экранами
      if (page.type === 'start' || page.type === 'end') {
        return acc
      }

      // Добавляем видимые вопросы с этой страницы
      const pageVisibleQuestions = toValue(page.visibleQuestions) || []

      // Фильтруем вопросы, которые имеют номер вопроса
      const pageVisibleQuestionsInNavigation = pageVisibleQuestions
        .filter(question => toValue(question?.showNumber))

      return [...acc, ...pageVisibleQuestionsInNavigation]
    }, [])
  })

  const visibleQuestionsCount = computed(() => {
    return visibleQuestionsInNavigation.value.length
  })

  const getPageIndex = (pId, type) => {
    return pages.value.findIndex((p) => {
      if (type && p.type !== type)
        return false
      return p.id === pId
    })
  }

  /**
   * Questions that user has interacted with
   */
  const interactedQuestionsCount = computed(() => {
    return visibleQuestionsInNavigation.value.filter(question => toValue(question.hasInteracted)).length
  })

  /**
   * Progress percentage (0-100)
   */
  const progressPercentage = computed(() => {
    const total = visibleQuestionsCount.value
    if (!total)
      return 0

    if (activePageHasLogicLeadingToEnd.value && !activePageContainsMultipleQuestions.value) {
      return 100
    }
    const completed = interactedQuestionsCount.value
    return Math.round((completed / total) * 100)
  })

  /**
   * Проверяет, должна ли кнопка "Назад" быть скрыта из-за ограничений по квотам
   * Если предыдущий переход содержал вопросы с ограничениями по квотам, кнопка скрывается
   */
  const shouldHideBackButton = ref(false)

  /**
   * Обновляет значение shouldHideBackButton на основе текущего состояния
   * Вызывается после сохранения ответа и при навигации
   */
  const updateShouldHideBackButton = () => {
    // Проверяем, есть ли вопросы с ограничениями по квотам
    if (!quoteRestrictedQuestionIds.value || quoteRestrictedQuestionIds.value.length === 0) {
      shouldHideBackButton.value = false
      return
    }

    // Получаем последний переход
    const lastJump = currentJumps.value[currentJumps.value.length - 1]
    if (!lastJump) {
      shouldHideBackButton.value = false
      return
    }

    // Получаем страницу, с которой был сделан переход
    const fromPage = pages.value[lastJump.fromPageIndex]
    if (!fromPage) {
      shouldHideBackButton.value = false
      return
    }

    // Проверяем, есть ли на странице вопросы с ограничениями по квотам
    const pageQuestionIds = toValue(fromPage.questions).map(q => q.questionId)
    const hasRestrictedQuestions = pageQuestionIds.some(id =>
      quoteRestrictedQuestionIds.value.includes(id),
    )
    shouldHideBackButton.value = hasRestrictedQuestions
  }

  /**
   * Показывать ли таймер
   */
  const showTimer = computed(() => timer.value.enabled)

  /**
   * Общее время на прохождение в секундах
   */
  const timeToPass = computed(() => timer.value.duration)

  // Actions

  const resetToStart = () => {
    // refresh the page
    window.location.reload()
  }

  /**
   * Выполняет валидацию текущей страницы опроса.
   * @returns {void}
   */
  const validateCurrentPage = () => {
    const page = activePage.value
    if (page.type === 'start' || page.type === 'end') {
      currentPageIsValid.value = true
      return
    }

    if (!page || !page.questions)
      return

    toValue(activePage.value.visibleQuestions).forEach((question) => {
      if (question.setAllAsTouched) {
        question.setAllAsTouched()
      }
      if (question.validate) {
        question.validate()
      }
      question.blocked.value = !currentPageIsValid.value
    })
  }

  /**
   * Отмечает все вопросы на странице как взаимодействованные
   * @param {object} page - Страница с вопросами
   * @returns {void}
   */
  const markPageQuestionsAsInteracted = (page) => {
    const visibleQuestions = toValue(page.visibleQuestions)
    visibleQuestions.forEach((question) => {
      question.markInteracted?.()
    })
  }

  /**
   * Сохраняет ответ на текущей странице
   * Если на странице несколько вопросов, то сохраняет все ответы
   * Если на странице один вопрос, то сохраняет ответ на этот вопрос
   * @param {object} page - Страница с вопросами
   * @param {string} questionId - ID вопроса
   * @param {string[]} skippedQuestionsIdsBetweenCurrentAndNext - ID вопросов, которые были пропущены между текущей и следующей страницей
   * @returns {Promise<void>}
   */
  async function _saveCurrentAnswer(page, questionId, skippedQuestionsIdsBetweenCurrentAndNext = []) {
    // Не сохраняем ответы в режиме превью, просто возвращаем true
    if (previewStore.isPreviewMode || simplifiedStore.isPreviewMode) {
      return true
    }

    isSavingAnswer.value = true
    saveAnswerError.value = false // Сбрасываем ошибку перед сохранением

    try {
      const visibleQuestions = toValue(page.visibleQuestions)

      // Определяем, содержит ли страница несколько вопросов
      const containsMultipleQuestions = visibleQuestions.length > 1

      // Получаем ID языка опроса
      const langId = selectedLang.value?.poll_lang_id

      let result = null

      if (containsMultipleQuestions) {
        const questionData = {}
        visibleQuestions.forEach((question) => {
          const data = question.getData?.()
          if (langId && data) {
            data.lang = langId
          }
          if (data) {
            // Убеждаемся, что questionId существует перед присваиванием
            if (question.questionId) {
              questionData[question.questionId] = serialize(data)
            }
            else {
              console.warn('Question is missing questionId during multi-save:', question)
            }
          }
        })

        if (Object.keys(questionData).length > 0) {
          result = await saveAnswer({
            authKey: authKey.value,
            items: questionData,
            skippedQuestionsIds: skippedQuestionsIdsBetweenCurrentAndNext,
            editMode: isEditMode.value,
            tabletMode: tabletStore.isTabletMode,
          })
          if (result?.error) {
            saveAnswerError.value = true
            console.error('Error saving answer (multiple):', result.error)
            // #if VITE_USE_SENTRY
            Sentry.captureException(new Error('Failed to save answer (multiple)'), {
              extra: { error: result.error },
            })
            // #endif
          }
        }
      }
      else {
        const firstQuestion = visibleQuestions[0]
        const questionData = firstQuestion?.getData?.() // Use optional chaining

        if (langId && questionData) {
          questionData.lang = langId
        }

        if (questionData) {
          // Use the passed questionId if available, otherwise derive it from the first visible question
          const currentQuestionId = questionId || firstQuestion?.questionId
          if (currentQuestionId) {
            result = await saveAnswer({
              authKey: authKey.value,
              questionId: currentQuestionId,
              item: serialize(questionData),
              skippedQuestionsIds: skippedQuestionsIdsBetweenCurrentAndNext,
              editMode: isEditMode.value,
              tabletMode: tabletStore.isTabletMode,
            })
            if (result?.error) {
              saveAnswerError.value = true
              console.error('Error saving answer (single):', result.error)
              // #if VITE_USE_SENTRY
              Sentry.captureException(new Error('Failed to save answer (single)'), {
                extra: { error: result.error, questionId: currentQuestionId },
              })
              // #endif
            }
          }
          else {
            console.warn('Could not determine questionId for saving single answer.')
          }
        }
      }

      // Обрабатываем результат с учетом endScreen
      if (result && result.isQuoteLimitsOver) {
        if (result.endScreen === null) {
          // null - стандартный информационный экран квотафул
          showQuoteFullInfoScreen.value = true
        }
        else {
          // endScreen установлен - показываем соответствующий конечный экран
          quotaEndScreen.value = result.endScreen
          showQuotaEndScreen.value = true
          return showEndScreen(result.endScreen)
        }
      }

      return true
    }
    catch (error) {
      saveAnswerError.value = true
      console.error('Error in _saveCurrentAnswer:', error)
      // #if VITE_USE_SENTRY
      Sentry.captureException(error)
      // #endif
      return false
    }
    finally {
      isSavingAnswer.value = false
    }
  }

  /**
   * Переходит к следующей странице опроса, выполняя валидацию и сохранение ответов.
   * @returns {Promise<void>}
   */
  const goNext = async () => {
    navigationDirection.value = 'next'
    const pageIndex = activePageIndex.value
    const page = pages.value[pageIndex]
    const firstQuestion = toValue(page.visibleQuestions)[0]
    const questionId = firstQuestion.questionId

    // Start timer if transitioning from start page and timer is enabled
    const currentPageType = page.type
    if ((currentPageType === 'start' || currentPageType === 'intermediate') && timer.value.enabled) {
      const nextPage = pages.value[pageIndex + 1]

      const isNextPageContainsTextBlock = nextPage?.type === 'start'
        || nextPage?.type === 'end'
        || nextPage?.type === 'intermediate'

      if (nextPage && !isNextPageContainsTextBlock) {
        setupTimerIfNecessary()
      }
    }

    // Выполняем валидацию текущей страницы
    validateCurrentPage()

    const sendMessageToParent = () => {
      forceNextButtonToBeDisabled.value = true
      window.parent.postMessage({
        type: 'PREVIEW_NAVIGATION',
        data: { direction: 'next' },
      }, '*')
      setTimeout(() => {
        forceNextButtonToBeDisabled.value = false
      }, 1000)
    }

    // In preview mode, just notify parent
    if (previewStore.isNonFunctionalPreview && currentPageIsValid.value && !isLastDefaultPage.value) {
      sendMessageToParent()
      return
    }

    // Если мы находимся врежиме предпросмотра и текущая страница валидна,
    // и это последняя страница, то переходим к стандартной странице завершения
    if (previewStore.isNonFunctionalPreview && currentPageIsValid.value && isLastDefaultPage.value) {
      const customEndPageIndex = pages.value.findIndex(p => p.id !== 'end-default' && p.type === 'end')

      if (customEndPageIndex !== -1) {
        sendMessageToParent()
        return
      }

      const defaultEndPageIndex = pages.value.findIndex(p => p.id === 'end-default')
      if (defaultEndPageIndex !== -1) {
        setActivePageIndex(defaultEndPageIndex)
      }

      return
    }

    if (currentPageIsValid.value) { // Если текущая страница валидна
      // Если это последняя страница и есть ссылка на завершение,
      // то отправляем пользователя на неё
      const shouldRedirectToFinishLink = isLastDefaultPage.value && finishLink.value

      // Отмечаем все вопросы на странице как взаимодействованные
      markPageQuestionsAsInteracted(page)

      // Если это последняя страница и есть ссылка на завершение, то отправляем пользователя на неё
      if (shouldRedirectToFinishLink) {
        const link = document.createElement('a')
        link.href = finishLink.value
        link.setAttribute('target', '_blank')
        document.body.appendChild(link)
        link.click()
      }

      // Получаем индекс следующей страницы с логикой
      const nextPageIndex = getNextPageIndexWithLogic(pageIndex, 'block')

      // Determine skipped questions BEFORE setting active page index or saving
      const pagesBetweenCurrentAndNext = pages.value.slice(pageIndex + 1, nextPageIndex === 'end' ? pages.value.length : nextPageIndex)
      let skippedQuestionsIdsBetweenCurrentAndNext = []
      if (pagesBetweenCurrentAndNext.length) {
        skippedQuestionsIdsBetweenCurrentAndNext = pagesBetweenCurrentAndNext.reduce((acc, p) => {
          const questions = toValue(p.questions)
          return [...acc, ...questions.map(q => q.questionId)]
        }, [])

        // Отмечаем все вопросы на страницах между текущей и следующей как взаимодействованные
        pagesBetweenCurrentAndNext.forEach((p) => {
          markPageQuestionsAsInteracted(p)
        })
      }

      // Call the extracted save function
      const isAnswerSavedCorrectly = await _saveCurrentAnswer(page, questionId, skippedQuestionsIdsBetweenCurrentAndNext)

      if (!isAnswerSavedCorrectly) {
        return
      }

      // Check if we need to fetch updated variables
      const newVariables = await fetchAnswerVariablesIfNeeded(toValue(page.visibleQuestions), authKey.value)
      if (newVariables) {
        variables.value = { ...variables.value, ...newVariables }
      }

      updateShouldHideBackButton()

      // Устанавливаем индекс активной страницы ПОСЛЕ сохранения ответов
      const finalNextIndex = nextPageIndex === 'end'
        ? pages.value.findIndex(p => p.type === 'end' && p.id !== 'end-default') // Find specific end page first
        : nextPageIndex

      // Если конкретная конечная страница не найдена или nextPageIndex не был 'end', ищем стандартный конечный экран
      // Обрабатываем случай, когда nextPageIndex может быть строкой 'end' или числом
      const targetIndex = (finalNextIndex === -1 || typeof finalNextIndex === 'string')
        ? pages.value.findIndex(p => p.id === 'end-default')
        : finalNextIndex

      // Обрабатываем кейс, при котором targetIndex может быть невалидным
      // и в таком случае устанавливаем последнюю страницу
      const validTargetIndex = (targetIndex !== -1 && targetIndex < pages.value.length) ? targetIndex : pages.value.length - 1

      const isFinishPage = pages?.value[validTargetIndex]?.type === 'end'
      const isPreviewMode = previewStore.isPreviewMode || simplifiedStore.isPreviewMode

      if (isFinishPage && simplifiedStore.closeByFinishButton) {
        if (!isPreviewMode) {
          await markDone()
        }
        simplifiedStore.closeWidget()
        return
      }

      const hasQuotaEndScreen = quotaEndScreen.value !== null && quotaEndScreen.value !== undefined
      if (hasQuotaEndScreen && isFinishPage) {
        return showEndScreen(quotaEndScreen.value)
      }
      else {
        setActivePageIndex(validTargetIndex)
      }

      // Триггер события прокрутки (не уверен, нужно ли оно, но было в старом прохождении)
      const event = new Event('scroll')
      window.dispatchEvent(event)

      if (isFinishPage && !isPreviewMode) {
        await markDone()
      }
    }

    const isPageControllerInitialized = pageController.value?.initialized?.value
    if (isPageControllerInitialized && activePageContainsMultipleQuestions.value) {
      pageController.value.scrollToFirstVisibleError()
    }
  }

  async function markOpen() {
    try {
      await updatePollStatus({ authKey: authKey.value, status: 'open' })
    }
    catch (error) {
      console.error('Error marking open:', error)
    }
  }

  /**
   * Отмечает опрос как завершенный.
   * @returns {Promise<void>}
   */
  async function markDone() {
    try {
      await updatePollStatus({ authKey: authKey.value, status: 'done' })
    }
    catch (error) {
      console.error('Error marking done:', error)
    }
  }

  /**
   * Устанавливает данные опроса и форматирует страницы.
   * @param {Array} questionsData - Массив данных вопросов.
   * @param {Array} pagesData - Массив данных страниц.
   * @param {number|null} _activeQuestionId - ID вопроса, который нужно отобразить.
   */
  const setData = (questionsData, pagesData, _activeQuestionId = null) => {
    pages.value = pagesData

    // Create a map of questions indexed by their IDs from visible questions in pages
    const questionsMap = {}
    const all = []

    pages.value.forEach((page) => {
      toValue(page.questions).forEach((question) => {
        questionsMap[question.questionId] = question
        all.push(question)
      })
    })

    questionsById.value = questionsMap
    allQuestions.value = all

    // @NOTE: Убираем страницы с промежуточными экранами
    const nonIntermediatePages = pages.value.filter(page =>
      toValue(page.questions).some(q => q.type !== INTER_BLOCK),
    )

    // Create stringified answers map
    const stringifiedAnswers = {}

    let number = 0
    nonIntermediatePages.forEach((page) => {
      const questions = toValue(page.questions)

      questions.forEach((question) => {
        if (question?.type !== INTER_BLOCK) {
          number += 1
          stringifiedAnswers[number] = question.getStringifiedAnswer()
        }
      })
    })

    // Set variables for questions
    pages.value.forEach((page) => {
      toValue(page.questions).forEach((question) => {
        // Добавляем переменные
        question.addVariables(variables, stringifiedAnswers)

        // Добавляем зависимости
        question.addDependencies(questionsById.value)
      })
    })

    if (quotaEndScreen.value && showQuotaEndScreen.value) {
      initStartPageIndex(quotaEndScreen.value)
      return
    }
    else if (pollStatus.value === 'screen-out' && showQuotaEndScreen.value) {
      showEndScreen(quotaEndScreen.value)
      return
    }

    initStartPageIndex(_activeQuestionId)
  }

  const scrollToTop = () => {
    if (appRootRef.value) {
      window.scrollTo({
        top: 0,
        // behavior: 'smooth',
      })
    }
  }

  /**
   * Scroll to the top of the page.
   */
  function onChange() {
    scrollToTop()
  }

  /**
   * Set the active page index.
   * @param {number} newIndex - The new index of the active page.
   */
  function setActivePageIndex(newIndex) {
    const page = pages.value[newIndex]

    if (!page)
      return

    // --- Tablet Inactivity Timer Logic ---
    if (tabletStore.isTabletMode) {
      const isQuestionPage = page.type !== 'start' && page.type !== 'intermediate' && page.type !== 'end'
      const isFinalScreenPage = page.type === 'end'
      let timeoutDuration = 0

      if (isQuestionPage) {
        timeoutDuration = tabletStore.questionTimeout
      }
      else if (isFinalScreenPage) {
        timeoutDuration = tabletStore.finalScreenTimeout || tabletStore.questionTimeout
      }

      if (timeoutDuration > 0 && (isQuestionPage || isFinalScreenPage)) {
        tabletStore.startOrResetInactivityTimer(timeoutDuration)
      }
      else {
        tabletStore.startOrResetInactivityTimer(0)
      }
    }
    // --- End Tablet Inactivity Timer Logic ---

    const questions = toValue(page.visibleQuestions)

    if (questions.length) {
      activePageIndex.value = newIndex
      return
    }

    // Find next page with visible questions
    const nextPageIndex = pages.value
      .slice(newIndex + 1)
      .findIndex((page) => {
        return toValue(page.visibleQuestions).length
      })

    if (nextPageIndex === -1) {
      onChange()
      return
    }

    // Add newIndex + 1 to get the actual index in the full pages array
    activePageIndex.value = newIndex + nextPageIndex + 1
  }

  /**
   * Определяет индекс следующей страницы с учетом логики перехода.
   * @param {number} currentPageIndex - Индекс текущей страницы.
   * @returns {number|string} Индекс следующей страницы или 'end'.
   */
  function getNextPageIndexWithLogic(currentPageIndex) {
    if (!pages.value[currentPageIndex]?.visibleQuestions) {
      return 0
    }

    const currentPage = pages.value[currentPageIndex]
    const visibleQuestions = toValue(currentPage.visibleQuestions)

    // Find first question with valid logic jump
    const firstValidLogicJump = visibleQuestions.reduce((result, question) => {
      if (result)
        return result // Return if we already found a valid jump

      const logicJump = question.getNextLogicItemId?.()
      if (logicJump) {
        return {
          jump: logicJump,
          question,
        }
      }
      return null
    }, null)

    // @NOTE: В режиме нефункционального предпросмотра не учитываем логику перехода
    if (firstValidLogicJump && !previewStore.isNonFunctionalPreview) {
      const { jump: logicJump } = firstValidLogicJump

      // Track the jump
      currentJumps.value.push({
        fromPageIndex: currentPageIndex,
        fromPageId: currentPage.id,
        to: logicJump,
      })

      if (logicJump.type === 'end') {
        const endPageIndex = pages.value.findIndex(p =>
          p.type === 'end' && p.id === logicJump.id,
        )
        return endPageIndex !== -1 ? endPageIndex : 'end'
      }

      if (logicJump.type === 'question') {
        const targetPageIndex = pages.value.findIndex(page =>
          toValue(page.visibleQuestions).some(q =>
            q.questionId === logicJump.id,
          ),
        )
        if (targetPageIndex !== -1) {
          return targetPageIndex
        }
      }
      else if (logicJump.type === 'page') {
        return getPageIndex(logicJump.id)
      }
    }

    // If no logic jumps apply, find next visible page
    const allPages = pages.value
    const nextVisiblePages = allPages.slice(currentPageIndex + 1)
      .filter(p => toValue(p.isVisible))

    if (nextVisiblePages.length) {
      const nextPageIndex = allPages.findIndex(p =>
        p.id === nextVisiblePages[0].id,
      )

      currentJumps.value.push({
        fromPageIndex: currentPageIndex,
        fromPageId: currentPage.id,
        to: {
          id: nextVisiblePages[0].id,
          type: nextVisiblePages[0].type,
        },
      })
      return nextPageIndex
    }

    return 'end'
  }

  /**
   * Находит индекс конечного экрана, следующей после текущей страницы, по логике перехода.
   * @param {object} startPage - Стартовая страница
   * @param {Set<number>} visited - Множество, в котором хранятся индексы посещенных страниц
   * @returns {number|null} Индекс конечного экрана или null
   */
  const findEndScreenByFollowingJumps = (startPage, visited = new Set()) => {
    if (!toValue(startPage.isVisible))
      return null
    if (visited.has(startPage.id))
      return null // Prevent infinite loops
    visited.add(startPage.id)

    const visibleQuestions = toValue(startPage.visibleQuestions)
    for (const question of visibleQuestions) {
      const logicJump = question.getNextLogicItemId?.()
      if (!logicJump)
        continue

      const { type, id } = logicJump

      if (type === 'end') {
        const endPage = pages.value.find(p =>
          p.type === 'end' && p.id === id && toValue(p.isVisible),
        )
        if (endPage) {
          return pages.value.indexOf(endPage)
        }
      }
      else if (type === 'question') {
        // Find page containing the target question
        const nextPage = pages.value.find(p =>
          toValue(p.visibleQuestions).some(q => q.questionId === id),
        )
        if (nextPage) {
          const result = findEndScreenByFollowingJumps(nextPage, visited)
          if (result !== null)
            return result
        }
      }
      else if (type === 'page') {
        const nextPage = pages.value.find(p => p.id === id)
        if (nextPage) {
          const result = findEndScreenByFollowingJumps(nextPage, visited)
          if (result !== null)
            return result
        }
      }
    }
    return null
  }

  /**
   * Отмечает все вопросы на страницах как взаимодействованные
   * @param {Array} pages - Массив страниц
   */
  const markPagesAsInteracted = (pages) => {
    pages.forEach((page) => {
      markPageQuestionsAsInteracted(page)
    })
  }

  /**
   * Переходит к первой неотвеченной странице опроса.
   * Если режим "Киоска" выключен или эта анкета привязана к респонденту,
   * тогда нам нужно учитывать предыдущие ответы респондента
   *
   * @NOTE:
   * - Если опрос завершен ('done'), переходит к конечной странице
   * - Если опрос открыт ('open'), переходит к начальной странице
   */
  const toFirstUnansweredPage = async () => {
    await nextTick()
    if (pollStatus.value === 'done' && !previewStore.isPreviewMode) {
      // Try to find end page by following jumps from each page
      for (const page of pages.value) {
        const endPageIndex = findEndScreenByFollowingJumps(page)
        if (endPageIndex !== null) {
          setActivePageIndex(endPageIndex)
          return
        }
      }

      // If no end page found through jumps, find first visible end or end-default
      const defaultEndIndex = pages.value.findIndex(p =>
        (p.type === 'end' || p.type === 'end-default') && toValue(p.isVisible),
      )

      if (defaultEndIndex !== -1) {
        setActivePageIndex(defaultEndIndex)
        return
      }

      // If no end page found at all, show default end screen
      const lastPageIndex = pages.value.length - 1
      setActivePageIndex(lastPageIndex)
      return
    }

    if (pollStatus.value === 'open') {
      const startPageIndex = pages.value.findIndex(p => p.type === 'start')
      if (startPageIndex !== -1) {
        setActivePageIndex(startPageIndex)
        return
      }
    }

    if (timer.value.enabled && activePage.value.type !== 'intermediate') {
      setupTimerIfNecessary()
    }

    // Clear any existing jumps history when finding first unanswered
    currentJumps.value = []

    // @NOTE: Remove start pages
    const nonStartPages = pages.value.filter(p => p.type !== 'start')

    // Helper to check if we should jump based on page's logic
    const checkPageLogicJump = (page) => {
      const visibleQuestions = toValue(page.visibleQuestions)
      for (const question of visibleQuestions) {
        const logicJump = question.getNextLogicItemId?.()
        if (logicJump) {
          const { id, type } = logicJump
          if (type === 'end') {
            return nonStartPages.findIndex(p =>
              p.type === 'end' && p.id === id,
            )
          }

          if (logicJump.type === 'question') {
            return nonStartPages.findIndex(p =>
              toValue(p.visibleQuestions).some(q =>
                q.questionId === id,
              ),
            )
          }

          if (type === 'page') {
            return getPageIndex(id)
          }
        }
      }
      return null
    }

    // Traverse pages considering logic jumps
    for (let i = 0; i < nonStartPages.length; i++) {
      const page = nonStartPages[i]
      const visibleQuestions = toValue(page.visibleQuestions)
      const previousPages = nonStartPages.slice(0, i)

      if (page.type === 'end' && toValue(page.isVisible)) {
        activePageIndex.value = pages.value.indexOf(page)
        markPagesAsInteracted(previousPages)
        return
      }

      if (visibleQuestions.length === 0) {
        continue
      }

      if (page.type === 'default') {
        const unansweredQuestion = visibleQuestions
          .filter(q => !q.isTextScreen)
          .find((q) => {
            if (previewStore.isPreviewMode || simplifiedStore.isPreviewMode) {
              // @NOTE: В режиме предпросмотра всегда возвращаем true,
              // чтобы показать все вопросы. Мы не учитываем
              // предыдущие ответы респондента в данном режиме
              return true
            }

            // В обычном режиме возвращаем вопросы, на которые не был дан ответ
            return !toValue(q.hasValue)
          })

        // Проверяем, есть ли на странице вопросы, на которые был дан ответ
        const pageHasAnsweredQuestions = visibleQuestions.some(q => toValue(q.hasValue))

        // Проверяем, есть ли логика перехода на другие страницы
        // при ответе на вопросы данной страницы
        const logicJumpIndex = checkPageLogicJump(page)
        if (logicJumpIndex !== null && pageHasAnsweredQuestions) {
          const { id } = nonStartPages[logicJumpIndex]

          // Отслеживаем переход
          currentJumps.value.push({
            fromPageIndex: pages.value.indexOf(page),
            fromPageId: page.id,
            to: {
              id,
              type: 'page',
            },
          })

          i = logicJumpIndex - 1 // -1, так как цикл увеличит i на 1
          continue
        }

        if (unansweredQuestion) {
          // Нашли вопрос, на который не был дан ответ
          activePageIndex.value = pages.value.indexOf(page)
          markPagesAsInteracted(previousPages)
          return
        }
        else {
          // Переходим к следующей странице
          const nextPage = nonStartPages[i + 1]
          if (nextPage) {
            // Отслеживаем переход
            currentJumps.value.push({
              fromPageIndex: pages.value.indexOf(page),
              fromPageId: page.id,
              to: {
                id: page.id,
                type: page.type,
              },
            })
          }
        }
      }
      else if (page.type === 'intermediate') {
        const isQuestionEmpty = (q) => {
          return !toValue(q.hasValue)
        }

        // Find first non-empty page after current, considering logic
        let nextIndex = i + 1
        while (nextIndex < nonStartPages.length) {
          const nextPage = nonStartPages[nextIndex]
          const questions = toValue(nextPage.visibleQuestions)

          if (!questions.every(isQuestionEmpty)) {
            // Found non-empty page
            break
          }

          // Check for logic jumps
          const logicJumpIndex = checkPageLogicJump(nextPage)
          if (logicJumpIndex !== null) {
            nextIndex = logicJumpIndex
            continue
          }

          nextIndex++
        }

        if (nextIndex < nonStartPages.length) {
          i = nextIndex - 1 // -1 because loop will increment
          continue
        }
        else {
          // No more non-empty pages, show current intermediate
          activePageIndex.value = pages.value.indexOf(page)
          markPagesAsInteracted(previousPages)
          return
        }
      }
    }

    // If we've gone through all pages without finding unanswered questions
    setActivePageIndex(pages.value.length - 1)
  }

  /**
   * Инициализирует индекс начальной страницы опроса.
   */
  function initStartPageIndex(activeQuestionId = null) {
    if (pages.value.length === 0) {
      return
    }

    const pageThatContainsQuestion = activeQuestionId
      ? pages.value.find(page =>
        toValue(page.visibleQuestions).some(q => Number.parseInt(q.questionId) === Number.parseInt(activeQuestionId)),
      )
      : null

    if (activeQuestionId && pageThatContainsQuestion) {
      const pageIndex = getPageIndex(pageThatContainsQuestion.id)
      setActivePageIndex(pageIndex)
    }
    else {
      toFirstUnansweredPage()
    }
  }

  /**
   * Загружает данные опроса с сервера.
   * @param {string} key - Ключ опроса.
   * @param {string} lang - Код языка.
   * @param {string} activeQuestionId - ID активного вопроса.
   * @param {string} pollId - ID опроса.
   * @returns {Promise<void>}
   */

  const fetchPoll = async (key, lang, activeQuestionId = null, pollId = null, editMode = false, viewQuestionNumber = null) => {
    isLoading.value = true
    pollKey.value = key
    translationsStore.pollKey = key
    translationsStore.pollId = pollId
    fetchPollError.value = null
    const isPreview = previewStore.isPreviewMode
    const isWidgetPreviewMode = previewStore.isWidgetPreviewMode

    try {
      const pollData = await fetchPollData(key, {
        lang,
        isPreview: isPreview || isWidgetPreviewMode,
        pollId,
        edit: editMode ? 1 : undefined,
        view: viewQuestionNumber,
        tabletMode: tabletStore.isTabletMode,
      })

      poll.value = pollData.poll
      pollStatus.value = pollData.answer?.status || 'open'

      // Determine the preset name based on customThemes.templateId
      const themeConfig = pollData.customThemes || { templateId: 0 }
      const presetName = themeConfig.templateId === 1 ? 'theme-1' : null

      // Initialize design: use preset if available, otherwise fallback to pollData.design or default
      //
      design.value = pollData.design || getDefaultDesignSettings(presetName)

      themeStore.initializeTheme(themeConfig)

      authKey.value = pollData.answer.auth_key
      isOldTitleFormat.value = ('isOldTitleFormat' in pollData.poll)
        ? !!pollData.poll.isOldTitleFormat
        : true
      maxFileSize.value = pollData.filesizeLimit || 15
      hasContact.value = pollData.answer.hasContact
      showFoquzLink.value = pollData.showFoquzLink
      showFoquzLabel.value = pollData.showFoquzLabel || false
      isAnswersLimitsOver.value = pollData.poll?.answerLimitIsOver
      isTestModeLimitOver.value = pollData.poll?.testModeLimitIsOver
      isCompanyAnswersLimitsOver.value = pollData.isAnswersLimitsOver
      isPollPeriodOver.value = pollData.pollPeriodIsOver
      isPollArchived.value = !pollData.isActive
      isPollActive.value = pollData.isActive

      isResponseTimeExpired.value = pollData.isResponseTimeExpired || false
      isEditMode.value = pollData.staffEdit || false
      showAdvertisementBanner.value = pollData.showAdv
      showUserConsentBanner.value = pollData.poll.showUserConsentBanner

      // Set initial values based on pollData
      displaySettings.value = pollData.poll.displaySetting || null
      // Disable pages mode if tablet mode is enabled
      isPagesModeEnabled.value = displaySettings.value?.type === 2 && !tabletStore.isTabletMode
      randomOrder.value = displaySettings.value?.random_order === 1
      withPoints.value = pollData.poll.point_system === 1
      testMode.value = !pollData.poll.is_published
      variables.value = pollData.variables || {}
      showPrevButton.value = !!design.value?.show_prev_button
      finishLink.value = pollData.design?.finish_link
      showNavigationType.value = design.value?.show_process !== undefined ? design.value?.show_process : POLL_NAVIGATION_TYPE.NUMBERS
      pollTitle.value = pollData.poll.title || ''
      pollDescription.value = pollData.poll.goal_text || ''
      disableQuestionAutoscroll.value = !!pollData.design?.disable_question_autoscroll
      languageSelectEnabled.value = design.value?.choose_language === 1
      allowEditAfterDone.value = pollData.allowEditAfterDone || false
      viewQuestionId.value = Number.parseInt(pollData.variables?.editQuestionId) || null
      quoteRestrictedQuestionIds.value = pollData.quoteRestrictedQuestionIds || []

      if (pollData.isQuoteLimitsOver) {
        if (pollData.endScreen === null) {
          // null - стандартный квотафул (показываем информационный экран)
          showQuoteFullInfoScreen.value = true
        }
        else {
          // endScreen установлен - будем показывать соответствующий конечный экран
          quotaEndScreen.value = pollData.endScreen
          showQuotaEndScreen.value = true
        }
      }
      else {
        showQuoteFullInfoScreen.value = false
        quotaEndScreen.value = pollData.endScreen
      }

      if (previewStore.isPreviewMode || simplifiedStore.isPreviewMode) {
        timer.value = {
          enabled: pollData.timer?.enabled,
          elapsedTime: 0,
          duration: 0,
          status: 'not-started',
        }
      }
      else if (pollData.timer) {
        timer.value = pollData.timer
      }

      translationsStore.initializeLanguages(pollData)

      // Initialize points store if point system is enabled
      if (!previewStore.isPreviewMode && !simplifiedStore.isPreviewMode) {
        pointsStore.initialize(pollData)
      }

      // Use the updated preparePages function
      const preparedPages = preparePages({
        questions: pollData.questions,
        isPagesModeEnabled: isPagesModeEnabled.value,
        displaySetting: displaySettings.value,
        displayPages: pollData.poll.displayPages,
        pageIdsSkippedByLogic: computed(() => toValue(pageIdsSkippedByLogic.value)),
        isNonFunctionalPreview: previewStore.isNonFunctionalPreview,
        isPreview: previewStore.isPreviewMode,
        isWidgetPreview: previewStore.isWidgetPreviewMode,
        previewType: previewStore.getPreviewType(),
        isEditMode: isEditMode.value,
        t: translationsStore.t, // Pass the translation function
      })

      let activeId = activeQuestionId

      if (isEditMode.value && viewQuestionId.value !== null) {
        activeId = viewQuestionId.value
      }

      setData(pollData.questions, preparedPages, activeId)

      const isPreviewMode = previewStore.isPreviewMode || simplifiedStore.isPreviewMode
      if (pollStatus.value === 'open' && !isPreviewMode) {
        markOpen()
      }

      if (isPagesModeEnabled.value) {
        pageController.value = usePageController()
        pageController.value.initialize({
          activePage,
          disableAutoscroll: disableQuestionAutoscroll,
        })
      }

      // After all initialization is complete
      isPollInitialized.value = true

      // --- Initialize Tablet Inactivity Timer ---
      const shouldInitializeTabletInactivityTimer = tabletStore.isTabletMode
        && !previewStore.isPreviewMode
        && !simplifiedStore.isPreviewMode

      if (shouldInitializeTabletInactivityTimer) {
        const qTimeout = pollData?.poll?.timeToRestart ?? 0
        const fTimeout = pollData?.poll?.timeToRestartScreenTypeEnd ?? 0

        if (qTimeout > 0 || fTimeout > 0) {
          const firstPage = pages.value?.[0]
          tabletStore.initializeInactivityTimer(
            { questionTimeout: qTimeout, finalScreenTimeout: fTimeout, firstPage: toValue(firstPage) },
            resetToStart,
          )
        }
        else {
          // console.log('Tablet inactivity timer disabled (timeouts are zero or less).')
        }
      }
      // --- End Tablet Inactivity Timer Init ---

      // Process any pending preview actions
      if (isPreview) {
        const lastAction = pendingPreviewActions.value.pop()
        if (lastAction) {
          const { type, data } = lastAction
          processPreviewMessage(type, data)
        }
        pendingPreviewActions.value = []
      }

      themeStore.applyCustomStyles({
        type: pollData.poll?.css_self_type,
        style: pollData.poll?.css_self_type === 0 ? pollData.poll?.css_self_url : pollData.poll?.css_self_text,
      })
    }
    catch (err) {
      fetchPollError.value = err
      const originalMessage = err.message
      if (originalMessage.includes('key') || originalMessage.includes('не найден')) {
        fetchPollError.value.message = 'Опрос не найден'
      }
      design.value = getDefaultDesignSettings(null) // Fallback to base default
      console.error('Error fetching poll:', err)
      // #if VITE_USE_SENTRY
      Sentry.captureException(err)
      // #endif
    }
    finally {
      previewStore.sendAppReadyMessage()
      simplifiedStore.initializeIframe()

      const shouldAddDelay = previewStore.isPreviewMode

      setTimeout(() => {
        isLoading.value = false
      }, shouldAddDelay ? 600 : 0)
    }
  }

  /**
   * Переходит к предыдущей странице опроса с учетом логики перехода.
   */
  const goPrev = () => {
    navigationDirection.value = 'prev'

    // Передаем навигацию родительскому окну в режиме предпросмотра
    if (previewStore.isNonFunctionalPreview) {
      forcePrevButtonToBeDisabled.value = true
      window.parent.postMessage({
        type: 'PREVIEW_NAVIGATION',
        data: { direction: 'prev' },
      }, '*')
      setTimeout(() => {
        forcePrevButtonToBeDisabled.value = false
      }, 1000)
      return
    }

    const currentIndex = activePageIndex.value

    // Получаем последний переход
    const lastJump = currentJumps.value[currentJumps.value.length - 1]

    let newIndex
    if (lastJump && lastJump.to.type === 'question' && lastJump.to.id === activePageId.value) {
      // Если мы пришли сюда через переход, возвращаемся к исходной странице
      newIndex = lastJump.fromPageIndex
    }
    else {
      // No jump to this page, just go to previous page
      newIndex = currentIndex - 1
    }

    if (lastJump) {
      setActivePageIndex(lastJump.fromPageIndex) // Use setActivePageIndex
      setTimeout(() => {
        currentJumps.value.pop()
        updateShouldHideBackButton()
      }, 10)

      return
    }

    // Skip pages with no visible questions
    while (newIndex >= 0) {
      const page = pages.value[newIndex]
      const questions = toValue(page.visibleQuestions)
      if (questions.length > 0)
        break
      newIndex--
    }

    if (newIndex >= 0) {
      activePageIndex.value = newIndex
    }

    onChange()
  }

  /**
   * Обновляет страницу в режиме предпросмотра
   */
  const refreshPreview = () => {
    window.location.reload()
  }

  const updatePreview = async (previewData) => {
    previewStore.setPreviewMode(true)

    // Затем обновляем активный вопрос и устанавливаем активную страницу
    if (previewData.activeQuestion) {
      // Ищем страницу, содержащую активный вопрос
      const pageIndex = pages.value.findIndex(page =>
        page.questions.some(q => q.questionId === previewData.currentQuestionId),
      )

      if (pageIndex !== -1) {
        const page = pages.value[pageIndex]
        const questionToUpdate = page.questions.find(q =>
          q.questionId === previewData.currentQuestionId,
        )

        if (questionToUpdate) {
          markPagesAsInteracted(pages.value.slice(0, activePageIndex.value))

          const isQuestionToUpdateIntermediate = toValue(questionToUpdate.type) === INTER_BLOCK
          const screenTypeChanged = isQuestionToUpdateIntermediate && previewData.activeQuestion?.intermediateBlock?.screen_type !== toValue(questionToUpdate.screenType)

          // Особая обработка для промежуточного блока
          if (previewData.activeQuestion.type === INTER_BLOCK && screenTypeChanged) {
            const screenType = previewData.activeQuestion.intermediateBlock?.screen_type
            const activeQuestionData = previewData.activeQuestion
            activeQuestionData.question_id = previewData.activeQuestion.question_id || previewData.activeQuestion.id
            const newQuestion = createModelByType(
              previewData.activeQuestion.type,
              activeQuestionData,
              previewStore.getPreviewType(),
            )

            // Удаляем текущую страницу
            pages.value.splice(pageIndex, 1)

            const typeToString = {
              [INTERMEDIATE_BLOCK_TYPES.START]: 'start',
              [INTERMEDIATE_BLOCK_TYPES.END]: 'end',
              [INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE]: 'intermediate',
              [INTERMEDIATE_BLOCK_TYPES.FIVE_SECOND_TEST]: 'five-second-test',
            }

            const newPage = enrichPageObject({
              id: previewData.activeQuestion.id,
              questions: [markRaw(newQuestion)],
              name: previewData.activeQuestion.name,
              type: typeToString[screenType],
            }, previewStore.isNonFunctionalPreview, pageIdsSkippedByLogic.value)

            if (screenType === INTERMEDIATE_BLOCK_TYPES.START) {
              // Добавляем стартовый экран в начало
              pages.value.unshift(newPage)
              activePageIndex.value = 0
            }
            else if (screenType === INTERMEDIATE_BLOCK_TYPES.END) {
              // Добавляем конечный экран в конец
              pages.value.push(newPage)
              setActivePageIndex(pages.value.length - 1)
            }
            else {
              // Заменяем на той же позиции для промежуточных экранов
              pages.value.splice(pageIndex, 0, newPage)
              activePageIndex.value = pageIndex
            }

            return
          }

          // Проверяем, отличаются ли типы вопросов
          const typeChanged = questionToUpdate.type !== previewData.activeQuestion.type
          const isFirstUpdate = !previewStore.previewData
          const activeQuestionChanged = previewData.currentQuestionId !== previewStore.previewData?.currentQuestionId

          const shouldScrollToQuestion = (typeChanged || isFirstUpdate || activeQuestionChanged) && pageController.value?.initialized.value

          if (typeChanged) {
            // Создаем новую модель с подготовленными данными
            const preparedQuestionPreviewData = prepareQuestionPreviewData(previewData.activeQuestion)

            const newQuestion = createModelByType(
              previewData.activeQuestion.type,
              preparedQuestionPreviewData,
              previewStore.getPreviewType(),
            )

            const isInterBlock = toValue(previewData?.activeQuestion?.type) === INTER_BLOCK

            // Обычное изменение типа вопроса
            const questionIndex = page.questions.findIndex(q =>
              q.questionId === previewData.currentQuestionId,
            )

            if (questionIndex !== -1) {
              const screenTypeToPageType = {
                [INTERMEDIATE_BLOCK_TYPES.START]: 'start',
                [INTERMEDIATE_BLOCK_TYPES.END]: 'end',
              }

              if (isInterBlock) {
                const pageType = screenTypeToPageType[toValue(previewData?.activeQuestion?.intermediateBlock?.screen_type)]
                const isStartPage = pageType === 'start'
                const isEndPage = pageType === 'end'

                if ((isStartPage || isEndPage) && isPagesModeEnabled.value) {
                  const newPage = preparePages({
                    questions: [newQuestion],
                    isPagesModeEnabled: isPagesModeEnabled.value,
                    displaySetting: null,
                    displayPages: [],
                    pageIdsSkippedByLogic: computed(() => []),
                    isNonFunctionalPreview: previewStore.isNonFunctionalPreview,
                    isPreview: previewStore.isPreviewMode,
                    type: pageType,
                    t: translationsStore.t,
                  })[0]

                  // remove question from current page
                  const filteredQuestions = pages.value[pageIndex].questions.filter(q => q.questionId !== previewData.currentQuestionId)
                  pages.value[pageIndex].questions = filteredQuestions
                  triggerRef(pages.value[pageIndex])

                  if (isStartPage) {
                    pages.value.unshift(newPage)
                    activePageIndex.value = 0
                  }

                  else if (isEndPage) {
                    pages.value.push(newPage)
                    activePageIndex.value = pages.value.length - 1
                  }

                  triggerRef(pages.value)

                  return
                }

                // @NOTE: Нужно установить тип страницы и сделать форс
                // апдейт для превью, чтобы стартовый и конечный экраны отображались корректно
                else if (pageType) {
                  pages.value[pageIndex].type = pageType
                  triggerRef(pages.value[pageIndex])
                }
              }
              // Обновляем вопрос в массиве
              pages.value[pageIndex].questions[questionIndex] = markRaw(newQuestion)

              // Принудительно обновляем реактивность
              pages.value[pageIndex].questions = [...pages.value[pageIndex].questions]
              activePageIndex.value = pageIndex

              // Scroll to question if needed
              if (shouldScrollToQuestion) {
                setTimeout(() => {
                  const questionIndex = toValue(page.visibleQuestions).findIndex(q => q.questionId === previewData.currentQuestionId)
                  if (questionIndex !== -1) {
                    pageController.value.scrollToQuestion(questionIndex)
                  }
                }, 100)
              }
            }

            nextTick().then(() => {
              // @NOTE: Нужно сделать форс апдейт для превью, чтобы вопросы отображались корректно
              updatePreview(previewData)
            })
          }
          else {
            // Типы совпадают, просто обновляем существующий вопрос
            questionToUpdate.updateFromPreview(previewData.activeQuestion)
            activePageIndex.value = pageIndex

            // Scroll to question if needed
            if (shouldScrollToQuestion) {
              setTimeout(() => {
                const questionIndex = toValue(page.visibleQuestions).findIndex(q => q.questionId === previewData.currentQuestionId)
                if (questionIndex !== -1) {
                  pageController.value.scrollToQuestion(questionIndex)
                }
              }, 100)
            }
          }
        }
      }
      else {
        // If page not found, first check if question belongs to any page in displayPages
        let targetPageId = null
        if (Array.isArray(previewData.displayPages)) {
          const pageWithQuestion = previewData.displayPages.find(page =>
            page.questions.some(q => q.id === Number(previewData.currentQuestionId)),
          )
          if (pageWithQuestion) {
            targetPageId = pageWithQuestion.id
          }
        }

        if (targetPageId) {
          // Find or create page with matching ID
          let pageIndex = pages.value.findIndex(p => p.id === targetPageId)
          const preparedQuestionPreviewData = prepareQuestionPreviewData(previewData.activeQuestion)

          if (pageIndex === -1) {
            // Create new page if it doesn't exist
            const newPage = preparePages({
              questions: [preparedQuestionPreviewData],
              isPagesModeEnabled: true,
              displaySetting: null,
              displayPages: [{
                id: targetPageId,
                questions: [{ id: previewData.currentQuestionId }],
              }],
              pageIdsSkippedByLogic: computed(() => []),
              isNonFunctionalPreview: previewStore.isNonFunctionalPreview,
              isPreview: previewStore.isPreviewMode,
              t: translationsStore.t,
            })[0]

            pages.value = [...pages.value, newPage]
            pageIndex = pages.value.length - 1
          }
          else {
            // Add question to existing page
            const newQuestion = createModelByType(
              previewData.activeQuestion.type,
              preparedQuestionPreviewData,
              previewStore.getPreviewType(),
            )
            pages.value[pageIndex].questions = [...pages.value[pageIndex].questions, markRaw(newQuestion)]
          }

          // Force reactivity update
          pages.value[pageIndex].questions = [...pages.value[pageIndex].questions]
          activePageIndex.value = pageIndex

          // Scroll to the question if page controller is initialized
          if (pageController.value?.initialized.value) {
            setTimeout(() => {
              const questionIndex = toValue(pages.value[pageIndex].visibleQuestions)
                .findIndex(q => q.questionId === previewData.currentQuestionId)
              if (questionIndex !== -1) {
                pageController.value.scrollToQuestion(questionIndex)
              }
            }, 100)
          }
        }
        else {
          // Fallback to creating new standalone page if no matching page found
          const preparedQuestionPreviewData = prepareQuestionPreviewData(previewData.activeQuestion)

          const newQuestionData = {
            type: previewData.activeQuestion.type,
            ...preparedQuestionPreviewData,
          }

          const newPage = preparePages({
            questions: [newQuestionData],
            isPagesModeEnabled: false,
            displaySetting: null,
            displayPages: [],
            pageIdsSkippedByLogic: computed(() => []),
            isNonFunctionalPreview: previewStore.isNonFunctionalPreview,
            isPreview: previewStore.isPreviewMode,
            t: translationsStore.t,
          })[0]

          pages.value = [...pages.value, newPage]
          pages.value[pages.value.length - 1].questions = [...pages.value[pages.value.length - 1].questions]
          activePageIndex.value = pages.value.length - 1

          if (pageController.value?.initialized.value) {
            setTimeout(() => {
              const questionIndex = toValue(newPage.visibleQuestions)
                .findIndex(q => q.questionId === previewData.currentQuestionId)
              if (questionIndex !== -1) {
                pageController.value.scrollToQuestion(questionIndex)
              }
            }, 100)
          }
        }
        nextTick().then(() => {
          updatePreview(previewData)
        })
      }
    }

    previewStore.updatePreviewData(previewData)
  }

  const updateStyles = (styles) => {
    // Handle collected styles
    if (styles.collectedStyles) {
      design.value = {
        ...design.value,
        ...styles.collectedStyles[':root'],
      }
    }

    // Handle logo settings
    if (styles.logo) {
      design.value = {
        ...design.value,
        logo_link: styles.logo.link,
        logo_type: styles.logo.type,
        logo_image: styles.logo.logo,
        logo_text: styles.logo.logoText,
        logo_height: styles.logo.logoHeight_,
        logo_margins: styles.logo.logoMargin_,
        logo_text_size: styles.logo.logoFont_,
        logo_text_bold: styles.logo.logoBold_,
        logo_text_italic: styles.logo.logoItalic_,
        small_header_mobile: styles.logo.smallHeaderMobile_,
        logo_position: styles.logo.logoPosition,
      }
    }

    // Handle text customizations
    if (styles.texts) {
      design.value = {
        ...design.value,
        back_text: styles.texts.back,
        next_text: styles.texts.next,
        finish_text: styles.texts.finish,
        unrequired_text: styles.texts.unrequired,
        finish_link: styles.texts.finishLink,
      }
    }

    // Handle settings
    if (styles.settings) {
      design.value = {
        ...design.value,
        show_process: styles.settings.paginatorMode,
        show_prev_button: styles.settings.showBackButton,
        choose_language: styles.settings.showLang,
      }
    }
  }

  const updateActions = (actions) => {
    // Update any action-related state
    showPrevButton.value = actions.backButton
    // ... other action updates
  }

  const updateTestMode = (testModeEnabled) => {
    testMode.value = testModeEnabled
  }

  function initializePollPreview() {
    const previewStore = usePreviewStore()
    previewStore.setPreviewMode(true)

    // Listen for messages from parent window
    window.addEventListener('message', (event) => {
      const { type, data } = event.data

      if (!isPollInitialized.value) {
        // Queue the action if poll is not initialized yet
        if (type !== 'SET_SIZE') { // We can process size changes immediately
          pendingPreviewActions.value.push({ type, data })
          return
        }
        else {
          // Process size changes immediately
          processPreviewMessage(type, data)
        }
      }

      // Process the message immediately if poll is initialized
      processPreviewMessage(type, data)
    })

    // Notify parent window that preview is ready to receive data
    window.parent.postMessage({ type: 'PREVIEW_READY' }, '*')
  }

  // New function to process preview messages
  function processPreviewMessage(type, data) {
    const handle = () => {
      switch (type) {
        case 'PREVIEW_DATA':
          updatePreview(data)
          break
        case 'PREVIEW_DESIGN':
          updateDesign(data)
          break
        case 'PREVIEW_REFRESH':
          refreshPreview()
          break
        case 'PREVIEW_ACTIONS':
          updateActions(data)
          break
        case 'PREVIEW_TEST_MODE':
          updateTestMode(false)
          break
        case 'SET_SIZE':
          if (data.width && data.height) {
            setTimeout(() => {
              document.body.style.width = `${data.width}px`
              document.body.style.height = `${data.height}px`
            }, 10)
          }
          break
        default:
          break
      }
    }

    nextTick().then(handle)
  }

  /**
   * Запускает таймер опроса, если он включен в настройках опроса
   * Если таймер уже запущен, ничего не делаем
   */
  async function setupTimerIfNecessary() {
    const shouldShowTimer = !timer.value.enabled || timer.value.status === 'finished' || previewStore.isPreviewMode || !tabletStore.isTabletMode
    if (!shouldShowTimer)
      return

    try {
      // Initialize worker
      if (!timerWorker) {
        timerWorker = new Worker(
          new URL('@shared/workers/pollTimer.worker.js', import.meta.url),
          { type: 'module' },
        )

        timerWorker.onmessage = (e) => {
          const { type, timeLeft } = e.data

          switch (type) {
            case 'WORKER_READY':
              // Start timer with current timeLeft value when worker is ready
              timerWorker.postMessage({
                type: 'START',
                payload: { timeLeft: timer.value.timeLeft || 0 },
              })
              break
            case 'TICK':
              timer.value.timeLeft = timeLeft
              break
            case 'FINISHED':
              timer.value.status = 'finished'
              stopTimerWorker()
              break
          }
        }

        timerWorker.onerror = (error) => {
          console.error('Worker error:', error)
          // #if VITE_USE_SENTRY
          Sentry.captureException(error)
          // #endif
        }
      }

      function stopTimerWorker() {
        if (timerWorker) {
          timerWorker.postMessage({ type: 'STOP' })
          timerWorker.terminate()
          timerWorker = null
        }
      }

      // Cleanup when page unloads
      window.addEventListener('beforeunload', stopTimerWorker)

      if (timer.value.status === 'not-started') {
        await setupPollTimer(authKey.value)
        timer.value.status = 'active'
      }
    }
    catch (error) {
      console.error('Error setting up timer:', error)
      // #if VITE_USE_SENTRY
      Sentry.captureException(error)
      // #endif
    }
  }

  function updateDesign(previewData) {
    if (!previewData)
      return

    // Преобразуем поля, которые требуют преобразования
    const mappedDesign = {
      // Поля, связанные с логотипом
      logo_link: previewData.logoLink,
      logo_type: previewData.logoType,
      logo_image: previewData.logo,
      logo_text: previewData.logoText,
      logo_height: previewData.logo_height,
      logo_margins: previewData.logo_margins,
      logo_text_size: previewData.logo_text_size,
      logo_text_bold: previewData.logo_text_bold,
      logo_text_italic: previewData.logo_text_italic,
      logo_position: previewData.logo_position,
      logo_font_family: previewData.logoFontFamily,
      logo_color: previewData.logoColor,

      // Текст кнопок
      back_text: previewData.backText,
      next_text: previewData.nextText,
      finish_text: previewData.finishText,
      unrequired_text: previewData.unrequiredText,
      finish_link: previewData.finishLink,

      // обложка
      in_use_cover: previewData.in_use_cover,
      cover_full_width: previewData.cover_full_width,
      cover_image: previewData.cover_image,
      cover_only_first_page: previewData.cover_only_first_page,
      cover_position: previewData.cover_position,

      // Настройки навигации
      show_process: previewData.show_process,
      show_prev_button: previewData.show_prev_button,
      choose_language: previewData.chooseLanguage,

      // Настройки заголовка
      is_use_header: previewData.isUseHeader,
      header_color: previewData.headerColor,
      small_header_mobile: previewData.small_header_mobile,

      // Цвета
      main_color: previewData.mainColor,
      main_place_color: previewData.main_place_color,
      background_color: previewData.bgColor,
      text_on_bg: previewData.textOnBg,
      text_on_place: previewData.textOnPlace,
      link_color: previewData.linkColor,

      // Фон
      background_image: previewData.backgroundImage,
      mobile_background_image: previewData.mobileBackgroundImage,
      darkening_background: previewData.darkening_background,
      place_under_buttons: previewData.placeUnderButtons,

      // Шрифты
      font_family: previewData.fontFamily,
      font_size: previewData.fontSize,
      title_font_size: previewData.titleFontSize,

      // Стили кнопок
      next_button_radius: previewData.next_button_radius,
      next_button_background_color: previewData.next_button_background_color,
      next_button_text_color: previewData.next_button_text_color,
      next_button_stroke_color: previewData.next_button_stroke_color,

      back_button_radius: previewData.back_button_radius,
      back_button_background_color: previewData.back_button_background_color,
      back_button_text_color: previewData.back_button_text_color,
      back_button_stroke_color: previewData.back_button_stroke_color,

      start_button_radius: previewData.start_button_radius,
      start_button_background_color: previewData.start_button_background_color,
      start_button_text_color: previewData.start_button_text_color,
      start_button_stroke_color: previewData.start_button_stroke_color,

      // Другие настройки
      disable_question_autoscroll: previewData.disable_question_autoscroll,
    }

    // Обновляем состояние дизайна
    design.value = {
      ...design.value,
      ...mappedDesign,
    }

    if (design.value.show_process !== undefined) {
      showNavigationType.value = design.value.show_process
    }

    if (design.value.show_prev_button !== undefined) {
      showPrevButton.value = !!design.value.show_prev_button
    }

    if (design.value.choose_language !== undefined) {
      languageSelectEnabled.value = !!design.value.choose_language
    }

    if (design.value.disable_question_autoscroll !== undefined) {
      disableQuestionAutoscroll.value = !!design.value.disable_question_autoscroll
    }

    if (design.value.unrequired_text !== undefined) {
      explicitlySetUnrequiredText.value = design.value.unrequired_text
    }
  }

  /**
   * Показывает конечный экран опроса по ID.
   * Если ID равен 0, показывает стандартный конечный экран.
   * @param {number} id - ID конечного экрана или 0 для стандартного.
   */
  function showEndScreen(id) {
    let targetIndex = -1

    if (id === 0) {
      // Find the index of the default end screen page
      targetIndex = pages.value.findIndex(p => p.id === 'end-default' && p.type === 'end')
    }
    else {
      // Find the index of the specific end screen page by its question ID
      targetIndex = pages.value.findIndex(p =>
        p.type === 'end' && toValue(p.questions).some(q => q.questionId === id),
      )
    }

    if (targetIndex !== -1) {
      const page = pages.value[targetIndex]
      page.forceVisible = true
      activePageIndex.value = targetIndex
    }
    else {
      console.warn(`End screen with id ${id} or default end screen not found. Falling back to last page.`)
    }
  }

  return {
    disableQuestionAutoscroll,
    appRootRef,
    design,
    poll,
    activePage,
    activePageContainsMultipleQuestions,
    activePageId,
    isStartPage,
    isEndPage,
    isPagesModeEnabled,
    pageController,
    testMode,
    isLoading,
    fetchPollError,
    languageSelectEnabled,
    pages,
    lang: selectedLang,
    allQuestions,
    showFoquzLink,
    activePageIndex,
    backButtonText,
    forwardButtonText,
    nextButtonText,
    navigationDirection,
    shouldShowFinishButton,
    authKey,
    pollKey,
    hasContact,
    finishLink,
    showPrevButton,
    isFirstPage,
    isLastPage,
    isLastDefaultPage,
    finishButtonText,
    unrequiredText,
    showNavigationType,
    pollTitle,
    pollDescription,
    isAnswersLimitsOver,
    isTestModeLimitOver,
    isCompanyAnswersLimitsOver,
    isPollPeriodOver,
    isPollArchived,
    isPollActive,
    isOldTitleFormat,
    maxFileSize,
    pollStatus,
    showAdvertisementBanner,
    showUserConsentBanner,
    currentPageIsValid,
    visibleQuestionsCount,
    interactedQuestionsCount,
    progressPercentage,
    forcePrevButtonToBeDisabled,
    forceNextButtonToBeDisabled,
    allowEditAfterDone,
    showFoquzLabel,
    saveAnswerError,
    isSavingAnswer,
    isPollInitialized,
    timer,
    showTimer,
    timeToPass,
    showQuoteFullInfoScreen,
    quotaEndScreen,
    showQuotaEndScreen,
    isResponseTimeExpired,
    isEditMode,
    shouldHideBackButton,
    hasQuoteRestrictedQuestions,

    // Actions
    setData,
    goNext,
    goPrev,
    fetchPoll,
    validateCurrentPage,
    updatePreview,
    updateStyles,
    updateActions,
    initializePollPreview,
    updateDesign,
    setupTimerIfNecessary,
    processPreviewMessage,
    updateShouldHideBackButton,
  }
})
