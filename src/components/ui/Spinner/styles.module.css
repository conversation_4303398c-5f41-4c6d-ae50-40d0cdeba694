.fc-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.fc-spinner-icon {
  width: 100%;
  height: 100%;
  position: relative;
  animation-name: fc-spinner-rotation;
  animation-duration: 2000ms;
  animation-iteration-count: infinite;
  animation-timing-function: steps(8);
}

.fc-spinner-icon path {
  fill: currentColor;
}

@keyframes fc-spinner-rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}