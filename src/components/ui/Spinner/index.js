import van from "vanjs-core";
import styles from "./styles.module.css";

const { div } = van.tags;
const { path, svg } = van.tags("http://www.w3.org/2000/svg");

export default function Spinner({
  color = "rgba(0, 0, 0, 0.4)",
  size = "1em",
  ...attrs
}) {
  return div(
    {
      ...attrs,
      style: `width: ${size}; height: ${size}; color: ${color};`,
      class: () =>
        `${styles["fc-spinner"]}${attrs.class ? " " + attrs.class : ""}`,
    },
    svg(
      {
        class: styles["fc-spinner-icon"],
        width: size,
        height: size,
        style: `color: ${color};`,
        viewBox: "0 0 22 22",
      },
      path({
        d: "M13.06 2.05a2.1 2.1 0 0 0-.6-1.46c-.43-.38-.9-.6-1.46-.6a2 2 0 0 0-1.46.6c-.43.43-.6.9-.6 1.46 0 .6.17 1.08.6 1.46.39.43.86.6 1.46.6.56 0 1.03-.17 1.46-.6.39-.38.6-.86.6-1.46ZM11 17.87c.56 0 1.03.21 1.46.6.39.43.6.9.6 1.46 0 .6-.21 1.07-.6 1.46-.43.43-.9.6-1.46.6-.6 0-1.07-.17-1.46-.6-.43-.39-.6-.86-.6-1.46 0-.56.17-1.03.6-1.46.39-.39.86-.6 1.46-.6Zm8.94-8.94c.56 0 1.03.21 1.46.6.39.43.6.9.6 1.46 0 .6-.21 1.07-.6 1.46-.43.43-.9.6-1.46.6-.6 0-1.08-.17-1.46-.6-.43-.39-.6-.86-.6-1.46 0-.56.17-1.03.6-1.46.38-.39.86-.6 1.46-.6ZM4.12 10.99c0 .6-.21 1.07-.6 1.46-.43.43-.9.6-1.46.6-.6 0-1.07-.17-1.46-.6A1.8 1.8 0 0 1 0 11c0-.56.17-1.03.6-1.46.39-.39.86-.6 1.46-.6.56 0 1.03.21 1.46.6.39.43.6.9.6 1.46Zm.56 4.25c.56 0 1.03.22 1.46.6.4.44.6.9.6 1.47 0 .6-.2 1.07-.6 1.46-.43.43-.9.6-1.46.6-.6 0-1.07-.17-1.46-.6-.43-.39-.6-.86-.6-1.46 0-.56.17-1.03.6-1.46.39-.4.86-.6 1.46-.6Zm12.64 0c.55 0 1.03.22 1.46.6.38.44.6.9.6 1.47a2 2 0 0 1-.6 1.46c-.43.43-.9.6-1.46.6-.6 0-1.08-.17-1.46-.6-.43-.39-.6-.86-.6-1.46 0-.56.17-1.03.6-1.46.38-.4.85-.6 1.46-.6ZM4.68 2.61c.56 0 1.03.22 1.46.6.4.43.6.9.6 1.46 0 .6-.2 1.08-.6 1.46-.43.43-.9.6-1.46.6-.6 0-1.07-.17-1.46-.6a1.8 1.8 0 0 1-.6-1.46c0-.55.17-1.03.6-1.46a2 2 0 0 1 1.46-.6Z",
        fill: color,
      }),
    ),
  );
}
