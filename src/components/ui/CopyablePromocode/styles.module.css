/* src/components/ui/CopyablePromocode/styles.module.css */
.copyable-promocode {
  display: inline-flex;
  align-items: center;
  background-color: var(--fqz-widget-main-place-color);
  padding: 5px 15px 5px 20px;
  margin: 0;
  min-height: 45px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
  align-self: center;
  gap: 20px;
}

.copyable-promocode:before {
  content: "";
  border: 1px solid var(--fqz-widget-text-on-place);
  border-radius: 3px;
  width: 100%;
  height: 100%;
  position: absolute;
  pointer-events: none;
  left: 0;
  top: 0;
  transition: opacity 0.3s;
  opacity: 0.3;
}


.copyable-promocode:focus {
    outline: none;
}


.copyable-promocode-text {
  font-family: var(--fqz-widget-font-family);
  color: var(--fqz-widget-text-on-place);
  font-size: 18px;
  line-height: 1 !important;
}

.copyable-promocode-icon {
    display: block;
    opacity: 0.5;
    transition: opacity 0.3s;
}

.copyable-promocode-icon svg {
    display: block;
}

.copyable-promocode-icon svg path {
    stroke: var(--fqz-widget-text-on-place);
    stroke-width: 2px;
}

.copyable-promocode-button:hover {
  background-color: var(--fqz-widget-main-color-hover, var(--fqz-widget-main-color));
}

.copyable-promocode:hover::before {
    opacity: 0.6;
}

.copyable-promocode:hover .copyable-promocode-icon {
    opacity: 0.8;
}