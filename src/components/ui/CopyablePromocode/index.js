// src/components/ui/CopyablePromocode/index.js
import van from "vanjs-core";
import styles from "./styles.module.css";

const { button, span } = van.tags;
const { svg, path } = van.tags("http://www.w3.org/2000/svg");

export default function CopyablePromocode({ promocode }) {
  const copyStatus = van.state("Copy");

  const copyToClipboard = () => {
    navigator.clipboard.writeText(promocode).then(() => {
      copyStatus.val = "Copied!";
      setTimeout(() => {
        copyStatus.val = "Copy";
      }, 2000);
    });
  };

  return button(
    {
      class: styles["copyable-promocode"],
      "data-testid": "copyable-promocode",
      onclick: copyToClipboard,
    },
    span({ class: styles["copyable-promocode-text"] }, promocode),
    span(
      {
        class: styles["copyable-promocode-icon"],
      },
      svg(
        {
          width: "19",
          height: "21",
          viewBox: "0 0 19 21",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg",
        },
        path({
          d: "M14 13H16C17.1046 13 18 12.1046 18 11V3C18 1.89543 17.1046 1 16 1H10C8.89543 1 8 1.89543 8 3V5M3 20H9C10.1046 20 11 19.1046 11 18V10C11 8.89543 10.1046 8 9 8H3C1.89543 8 1 8.89543 1 10V18C1 19.1046 1.89543 20 3 20Z",
        }),
      ),
    ),
  );
}
