import van from "vanjs-core";
import styles from "./styles.module.css";
import { canPreviewImage } from "@/helpers/fileTypeHelpers";
import RemoveButton from "./RemoveButton";
import ScreenshotPlaceholder from "./ScreenshotPlaceholder";
import PreviewTrigger from "./PreviewTrigger";
import FileName from "./FileName";

const { div } = van.tags;

export default function FilePreview({
  previewUrl,
  fullUrl,
  type = "file",
  name,
  isUploading = false,
  onRemove,
  view = "default",
}) {
  const isScreenshot = type === "screenshot";
  const isImage = type === "image" && !isScreenshot;

  // Use previewUrl if available, otherwise fallback to fullUrl for images
  const imageUrl = previewUrl || (isImage ? fullUrl : null);

  const hasPreview = van.derive(() => {
    if (!imageUrl || isUploading) return false;
    if (isScreenshot) return false;
    if (isImage && name) {
      return canPreviewImage(name);
    }
    return false;
  });

  const containerClasses = van.derive(() =>
    [
      styles["fc-file-preview"],
      styles[`fc-file-preview--${view}`],
      isUploading && styles["fc-file-preview--uploading"],
      isScreenshot && styles["fc-file-preview--screenshot"],
      isImage && styles["fc-file-preview--image"],
      !hasPreview.val && styles["fc-file-preview--no-preview"],
    ]
      .filter(Boolean)
      .join(" "),
  );

  const backgroundStyle = van.derive(() =>
    hasPreview.val
      ? `background-image: url(${imageUrl}); background-size: cover; background-position: center;`
      : "",
  );

  return div(
    { class: () => containerClasses.val },
    div(
      { class: styles["fc-file-preview__inner"] },
      PreviewTrigger({
        backgroundStyle: () => backgroundStyle.val,
        isUploading,
        children: [
          () => (isScreenshot ? ScreenshotPlaceholder() : null),
          () =>
            isImage && !hasPreview.val
              ? div(
                  { class: styles["fc-file-preview__placeholder"] },
                  div({ class: styles["fc-file-preview__icon"] }),
                )
              : null,
        ],
      }),
      FileName({ name }),
      RemoveButton({ onRemove }),
    ),
  );
}
