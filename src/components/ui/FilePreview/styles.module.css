.fc-file-preview {
  position: relative;
  flex: 0 0 auto;
  width: 100px;
}

.fc-file-preview--uploading .fc-file-preview__placeholder {
  opacity: 0;
}

.fc-file-preview--uploading .fc-file-preview__close {
  opacity: 0;
  pointer-events: none;
}

.fc-file-preview__inner {
  position: relative;
}

.fc-file-preview__trigger {
  border-radius: 8px;
  height: 100px;
  width: 100px;
  overflow: hidden;
  position: relative;
  background-color: white;
  display: block;
}

.fc-file-preview__spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.fc-file-preview__trigger:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  transition: opacity 0.3s;
  box-sizing: border-box;
  opacity: 0;
  pointer-events: none;
}

.fc-file-preview__trigger--loading-overlay::before {
  opacity: 1;
}

.fc-file-preview__screenshot-placeholder,
.fc-file-preview__placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  color: rgba(0, 0, 0, 1);
}

.fc-file-preview__screenshot-placeholder svg {
  margin-top: -1px;
}

.fc-file-preview__name {
  font-size: 11px;
  line-height: 1.2;
  color: var(--fqz-widget-text-color, #000);
  margin-top: 10px;
  word-break: break-word;
}

.fc-file-preview__name-text {
  overflow-wrap: anywhere;
}

.fc-file-preview__extension {
  display: inline;
}

.fc-file-preview__close {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #000;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 16px;
  padding: 0;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s;
  font-weight: bold;
}

.fc-file-preview__close:hover {
  opacity: 0.8;
}

.fc-file-preview__close svg {
  display: block;
}