import van from "vanjs-core";
import styles from "./styles.module.css";

const { button } = van.tags;
const { svg, path } = van.tags("http://www.w3.org/2000/svg");

export default function RemoveButton({ onRemove }) {
  const handleClick = (e) => {
    e.stopPropagation();
    if (onRemove) onRemove();
  };

  return button(
    {
      class: styles["fc-file-preview__close"],
      type: "button",
      onclick: handleClick,
    },
    svg(
      {
        width: "8",
        height: "8",
        viewBox: "0 0 8 8",
        fill: "none",
      },
      path({
        d: "M2.83 3.7 1.19 2.06A.94.94 0 0 1 2.52.73l1.64 1.64L5.88.64a1.12 1.12 0 0 1 1.6 0c.45.45.45 1.16 0 1.6L5.76 3.98 7.4 5.6a.94.94 0 0 1-1.33 1.33L4.43 5.3 2.71 7.02c-.45.45-1.16.45-1.6 0a1.12 1.12 0 0 1 0-1.6L2.83 3.7Z",
        fill: "currentColor",
        "fill-rule": "evenodd",
        "clip-rule": "evenodd",
      }),
    ),
  );
}
