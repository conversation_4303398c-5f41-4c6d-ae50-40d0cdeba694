import van from "vanjs-core";
import styles from "./styles.module.css";

const { div } = van.tags;
const { svg, path } = van.tags("http://www.w3.org/2000/svg");

export default function ScreenshotPlaceholder() {
  return div(
    { class: styles["fc-file-preview__screenshot-placeholder"] },
    svg(
      {
        class: styles["fc-file-preview__icon"],
        width: "48",
        height: "49",
        viewBox: "0 0 48 49",
        fill: "none",
      },
      path({
        d: "m43 33.9-5.9-4.6c-.4-.4-1-.5-1.6-.5-.6 0-1.2.1-1.6.5l-7.6 6-12.2-11.8c-.4-.3-1-.5-1.6-.5-.6 0-1.2.2-1.6.5l-5.9 6M11 2H1v10m0 26v10h10M37 2h10v10m0 26v10H37m-7-27a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z",
        stroke: "currentColor",
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-opacity": ".4",
        "stroke-width": "2",
      }),
    ),
  );
}
