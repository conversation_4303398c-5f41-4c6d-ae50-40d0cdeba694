import van from "vanjs-core";
import styles from "./styles.module.css";
import Spinner from "../Spinner";

const { div } = van.tags;

export default function PreviewTrigger({
  backgroundStyle,
  isUploading,
  children,
}) {
  const triggerClasses = van.derive(() =>
    [
      styles["fc-file-preview__trigger"],
      isUploading ? styles["fc-file-preview__trigger--loading-overlay"] : "",
    ]
      .filter(Boolean)
      .join(" "),
  );

  return div(
    {
      class: () => triggerClasses.val,
      style: backgroundStyle,
    },
    children,
    () =>
      isUploading
        ? Spinner({
            size: "20px",
            class: styles["fc-file-preview__spinner"],
          })
        : null,
  );
}
