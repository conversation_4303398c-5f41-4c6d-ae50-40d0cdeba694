import van from "vanjs-core";
import styles from "./styles.module.css";
import {
  getNameWithoutExtension,
  getFileExtension,
} from "@/helpers/fileTypeHelpers";

const { div, span } = van.tags;

export default function FileName({ name }) {
  if (!name) return null;

  const nameWithoutExtension = getNameWithoutExtension(name);
  const extension = getFileExtension(name);

  const truncatedName =
    nameWithoutExtension?.length > 60
      ? `${nameWithoutExtension.slice(0, 60)}...`
      : nameWithoutExtension;

  const displayExtension = extension ? `.${extension}` : "";

  return div(
    { class: styles["fc-file-preview__name"] },
    span({ class: styles["fc-file-preview__name-text"] }, truncatedName),
    span({ class: styles["fc-file-preview__extension"] }, displayExtension),
  );
}
