.fc-q-attachments {
  width: 100%;
  margin-top: 20px;
}

.fc-q-attachments__description {
  font-size: var(--fqz-widget-text-font-size, 16px);
  line-height: 1.3;
  margin: 0 0 15px 0;
}

@media (max-width: 679px) {
  .fc-q-attachments__description {
    font-size: 14px;
  }
}

.fc-q-attachments__content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.fc-q-attachments__items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px 20px;
  min-height: 0;
  transition: opacity 0.3s;
}

.fc-q-attachments__items--empty {
  display: none;
}

.fc-q-attachments__actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.fc-q-attachments__button {
  all: unset;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  align-self: flex-start;
  padding: 0 15px;
  border-radius: var(--fqz-widget-next-button-radius, var(--fqz-widget-button-radius, 18px));
  width: 100%;
  cursor: pointer;
  height: 36px;
  line-height: 1.3;
  color: #000;
  font-size: 12px;
  font-family: var(--fqz-widget-font-family);
  transition: opacity 0.3s;
  gap: 10px;
  box-sizing: border-box;
  flex-shrink: 0;
  position: relative;
}

.fc-q-attachments__button:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--fqz-widget-next-button-radius, var(--fqz-widget-button-radius, 18px));
  border: 1px solid #000;
  box-sizing: border-box;
  opacity: 0.2;
  width: 100%;
  height: 100%;
}

.fc-q-attachments__button:hover {
  opacity: 0.7;
}

.fc-q-attachments__button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed;
}

.fc-q-attachments__button-icon {
  flex: 0 0 auto;
}

.fc-q-attachments__button--upload .fc-q-attachments__button-icon,
.fc-q-attachments__button--screenshot .fc-q-attachments__button-icon {
  margin-top: -1px;
}

.fc-q-attachments__button-text {
  white-space: nowrap;
}

/* Modal view styles */
.fc-q-attachments--view-large .fc-q-attachments__button {
  width: fit-content;
}