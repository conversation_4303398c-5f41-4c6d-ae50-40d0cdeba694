import van from "vanjs-core";
import styles from "./styles.module.css";

const { button, span } = van.tags;
const { svg, path } = van.tags("http://www.w3.org/2000/svg");

export default function ScreenshotButton({
  disabled,
  onClick,
  screenshotButtonText = "Сделать скриншот",
}) {
  return button(
    {
      class: [
        styles["fc-q-attachments__button"],
        styles["fc-q-attachments__button--screenshot"],
      ].join(" "),
      type: "button",
      disabled,
      onclick: onClick,
    },
    svg(
      {
        class: styles["fc-q-attachments__button-icon"],
        width: "12",
        height: "13",
        viewBox: "0 0 12 13",
        fill: "none",
      },
      path({
        stroke: "currentColor",
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "2",
        opacity: ".4",
        d: "M4 2H1v3m0 4v3h3M8 2h3v3m0 4v3H8",
      }),
    ),
    span(
      { class: styles["fc-q-attachments__button-text"] },
      screenshotButtonText,
    ),
  );
}
