import van from "vanjs-core";
import styles from "./styles.module.css";

const { button, div } = van.tags;
const { svg, path } = van.tags("http://www.w3.org/2000/svg");

export default function UploadButton({
  disabled,
  onClick,
  buttonText = "Прикрепить файл",
}) {
  return button(
    {
      class: `${styles["fc-q-attachments__button"]} ${styles["fc-q-attachments__button--upload"]}`,
      type: "button",
      disabled,
      onclick: onClick,
    },
    svg(
      {
        class: styles["fc-q-attachments__button-icon"],
        width: "14",
        height: "14",
        viewBox: "0 0 14 14",
        fill: "none",
      },
      path({
        stroke: "currentColor",
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
        "stroke-width": "1.5",
        opacity: ".4",
        d: "m12.58 6.61-5.35 5.35a3.5 3.5 0 0 1-4.95-4.94l5.35-5.35a2.34 2.34 0 0 1 3.3 3.3L5.57 10.3a1.17 1.17 0 0 1-1.65-1.64l4.95-4.94",
      }),
    ),
    div({ class: styles["fc-q-attachments__button-text"] }, buttonText),
  );
}
