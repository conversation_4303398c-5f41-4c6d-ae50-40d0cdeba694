import van from "vanjs-core";
import styles from "./styles.module.css";
import FormError from "@components/ui/form/FormError";
import Transition from "@components/ui/Transition";
import { TRANSITION_NAMES } from "@/constants";
import AttachmentsList from "./AttachmentsList";
import UploadButton from "./UploadButton";
import ScreenshotButton from "./ScreenshotButton";

const { div, input, p } = van.tags;

export default function QuestionAssessmentAttachments({
  attachments = van.state([]),
  error = van.state(null),
  isScreenshotLoading = van.state(false),
  shouldShowScreenshotButton = false,
  buttonText = "Прикрепить файл",
  screenshotButtonText = "Сделать скриншот",
  description = null,
  maxFiles = 0,
  uploadEnabled = false,
  onFileUpload,
  onScreenshotRequest,
  onRemove,
  view = "default",
}) {
  const isMaxFilesReached = van.derive(() => {
    return attachments.val.length >= maxFiles;
  });

  const shouldShowUploadButton = uploadEnabled;

  const handleFileInputChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0 && onFileUpload) {
      onFileUpload(files);
    }
    // Reset file input
    e.target.value = "";
  };

  const handleScreenshotRequest = () => {
    if (
      !isMaxFilesReached.val &&
      !isScreenshotLoading.val &&
      onScreenshotRequest
    ) {
      onScreenshotRequest();
    }
  };

  const handleRemove = (index) => {
    if (onRemove) {
      onRemove(index);
    }
  };

  const containerClasses = [
    styles["fc-q-attachments"],
    error.val && styles["fc-q-attachments--has-error"],
    shouldShowScreenshotButton &&
      styles["fc-q-attachments--screenshots-enabled"],
    view === "large" && styles["fc-q-attachments--view-large"],
  ]
    .filter(Boolean)
    .join(" ");

  // Create the file input element
  const fileInputElement = input({
    type: "file",
    multiple: true,
    accept: "image/*",
    style: "display: none",
    onchange: handleFileInputChange,
  });

  const handleFileUpload = () => {
    if (!isMaxFilesReached.val) {
      fileInputElement.click();
    }
  };

  return div(
    { class: containerClasses },
    description &&
      p({ class: styles["fc-q-attachments__description"] }, description),
    div(
      { class: styles["fc-q-attachments__content"] },
      AttachmentsList({
        attachments,
        view,
        onRemove: handleRemove,
      }),
      (shouldShowUploadButton || shouldShowScreenshotButton) &&
        div(
          { class: styles["fc-q-attachments__actions"] },
          shouldShowUploadButton
            ? UploadButton({
                disabled: () => isMaxFilesReached.val,
                onClick: handleFileUpload,
                buttonText,
              })
            : null,
          shouldShowScreenshotButton
            ? ScreenshotButton({
                disabled: () =>
                  isMaxFilesReached.val || isScreenshotLoading.val,
                onClick: handleScreenshotRequest,
                screenshotButtonText,
              })
            : null,
        ),
      Transition({
        show: error,
        name: TRANSITION_NAMES.FADE,
        style: "margin-top: 10px;",
        children: () => FormError({ error }),
      }),
    ),
    fileInputElement,
  );
}
