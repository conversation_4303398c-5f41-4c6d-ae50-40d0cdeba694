import van from "vanjs-core";
import styles from "./styles.module.css";
import FilePreview from "../FilePreview";
import { getFileType } from "@/helpers/fileTypeHelpers";

const { div } = van.tags;

export default function AttachmentsList({ attachments, view, onRemove }) {
  const getAttachmentType = (attachment) => {
    if (attachment.type === "screenshot") return "screenshot";
    if (attachment.type) return attachment.type;
    return getFileType(attachment.file || attachment.name || "");
  };

  const itemsContainer = div({
    class: () =>
      [
        styles["fc-q-attachments__items"],
        attachments.val.length === 0 &&
          styles["fc-q-attachments__items--empty"],
      ]
        .filter(Boolean)
        .join(" "),
  });

  // Use van.derive to create reactive binding
  van.derive(() => {
    itemsContainer.innerHTML = "";
    attachments.val.forEach((attachment, index) => {
      van.add(
        itemsContainer,
        FilePreview({
          file: attachment,
          isUploading: attachment.isUploading || false,
          name: attachment.name,
          type: getAttachmentType(attachment),
          previewUrl: attachment.previewUrl || "",
          fullUrl: attachment.fullUrl || "",
          view: view,
          onRemove: () => onRemove(index),
        }),
      );
    });
  });

  return itemsContainer;
}
