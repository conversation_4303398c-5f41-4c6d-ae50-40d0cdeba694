import van from "vanjs-core";
import styles from "./styles.module.css";

const { div } = van.tags;
const { path, svg } = van.tags("http://www.w3.org/2000/svg");

export function StarRatingItem({ thin, color }) {
  const emptyThinIcon = svg(
    {
      width: 50,
      height: 48,
      viewBox: "0 0 50 48",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
    },
    path({
      d: "M39.6875 45.9376L39.6884 45.9468L39.6895 45.956C39.7543 46.4994 39.381 46.9367 38.9393 46.9929L38.9391 46.9929C38.772 47.0142 38.6032 46.9826 38.4526 46.9L38.4526 46.9L38.4443 46.8955L25.4724 39.9356L24.9996 39.6819L24.5268 39.9356L11.5557 46.8955L11.5557 46.8955L11.5482 46.8996C11.3845 46.9893 11.2013 47.0189 11.0249 46.9883C10.5822 46.9104 10.2281 46.4531 10.3158 45.9137L10.3164 45.9099L12.6879 30.9683L12.7666 30.4727L12.4163 30.1135L2.03701 19.4664C2.03694 19.4664 2.03687 19.4663 2.0368 19.4662C1.90269 19.3282 1.81121 19.1459 1.78118 18.9443L1.78092 18.9426C1.69948 18.4024 2.05801 17.9512 2.50052 17.879C2.5009 17.879 2.50127 17.8789 2.50164 17.8788L16.9466 15.5911L17.4629 15.5093L17.6905 15.0388L24.239 1.49864C24.2392 1.49836 24.2393 1.49807 24.2394 1.49779C24.3279 1.3162 24.4659 1.17678 24.6278 1.09213C25.0257 0.885177 25.5355 1.03516 25.7605 1.49745C25.7606 1.49773 25.7607 1.49801 25.7609 1.49829L32.3096 15.0388L32.5372 15.5093L33.0534 15.5911L47.4988 17.8789C47.4991 17.879 47.4993 17.879 47.4995 17.879C47.6719 17.907 47.8367 17.9919 47.969 18.1301C48.3202 18.4979 48.3165 19.1036 47.9641 19.4654C47.9641 19.4654 47.964 19.4655 47.964 19.4655L37.5837 30.1135L37.2335 30.4727L37.3122 30.9683L39.6837 45.9099L39.6836 45.91L39.685 45.9184C39.6857 45.9221 39.6866 45.9289 39.6875 45.9376Z", // Ignored as per the request
      stroke: "currentColor",
      "stroke-width": 2,
    }),
  );

  const emptyNormalIcon = svg(
    {
      viewBox: "0 0 18 18",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      width: 18,
      height: 18,
    },
    path({
      "fill-rule": "evenodd",
      "clip-rule": "evenodd",
      d: "M13.1914 15.7689L9.28779 13.6753C9.08893 13.5681 8.85175 13.5681 8.65289 13.6753L4.74923 15.7689L5.46491 11.2738C5.50116 11.0454 5.42797 10.8131 5.26916 10.6505L2.14195 7.44371L6.48476 6.76457C6.70606 6.73047 6.89767 6.58662 6.99814 6.37953L8.97016 2.30432L10.9425 6.37953C11.043 6.58662 11.2343 6.73047 11.4559 6.76457L15.7984 7.44371L12.6715 10.6505C12.5124 10.8131 12.4395 11.0454 12.4754 11.2738L13.1914 15.7689ZM14.1775 17.7644C14.5545 17.7165 14.8227 17.3605 14.7761 16.9695C14.7751 16.9592 14.7737 16.9485 14.772 16.9382L13.894 11.4067L17.7369 7.46467C18.0069 7.18761 18.0086 6.73616 17.7414 6.45626C17.6378 6.34792 17.5039 6.27688 17.3585 6.25344L12.0097 5.40629L9.58504 0.392992C9.41415 0.0409887 9.00055 -0.100736 8.66117 0.0761536C8.52929 0.145062 8.42226 0.255885 8.35563 0.392992L5.93099 5.40629L0.582146 6.25344C0.206522 6.31418 -0.0503384 6.67861 0.00835283 7.06791C0.0307936 7.21852 0.0994969 7.3574 0.20376 7.46467L4.04666 11.4067L3.1687 16.9382C3.10553 17.3268 3.35824 17.6944 3.73283 17.7601C3.88163 17.7861 4.03457 17.7605 4.16818 17.6873L8.97016 15.1107L13.7725 17.6873C13.8968 17.7555 14.038 17.7822 14.1775 17.7644Z",
      fill: "currentColor",
    }),
  );

  const filledThinIcon = svg(
    {
      width: 18,
      height: 18,
      viewBox: "0 0 230 227",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
    },
    path({
      d: "M181.756 226.806C186.589 226.194 190.028 221.65 189.431 216.657C189.418 216.526 178.122 145.634 178.122 145.634L227.389 95.3049C230.85 91.7676 230.872 86.0036 227.446 82.43C226.118 81.0469 224.401 80.1399 222.538 79.8405L153.965 69.0246L122.881 5.01751C120.69 0.523323 115.388 -1.28614 111.037 0.972288C109.346 1.85208 107.974 3.267 107.12 5.01751L76.0358 69.0246L7.46316 79.8405C2.64763 80.616 -0.645342 85.2689 0.107084 90.2393C0.394776 92.1622 1.27556 93.9353 2.61222 95.3049L51.8784 145.634L40.623 216.258C39.8131 221.219 43.0529 225.913 47.8552 226.752C49.7628 227.083 51.7235 226.757 53.4364 225.822L114.998 192.925L176.564 225.822C178.158 226.693 179.968 227.033 181.756 226.806Z", // Ignored as per the request
      fill: "currentColor",
    }),
  );

  const filledNormalIcon = svg(
    {
      viewBox: "0 0 18 18",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      width: 18,
      height: 18,
    },
    path({
      "fill-rule": "evenodd",
      "clip-rule": "evenodd",
      d: "M14.1743 17.7605C14.5513 17.7126 14.8195 17.3567 14.7729 16.9657C14.7718 16.9555 13.891 11.4042 13.891 11.4042L17.733 7.46303C18.0029 7.18604 18.0047 6.73468 17.7375 6.45484C17.634 6.34653 17.5 6.2755 17.3547 6.25207L12.007 5.4051L9.58294 0.392906C9.41208 0.0409797 8.99857 -0.100714 8.65927 0.0761368C8.52742 0.145031 8.42042 0.255829 8.3538 0.392906L5.92969 5.4051L0.582019 6.25207C0.206477 6.31279 -0.0503274 6.67715 0.008351 7.06636C0.0307869 7.21693 0.0994751 7.35579 0.203716 7.46303L4.04577 11.4042L3.16801 16.9345C3.10484 17.323 3.35751 17.6906 3.73201 17.7563C3.88078 17.7822 4.03369 17.7566 4.16727 17.6834L8.9682 15.1074L13.7695 17.6834C13.8937 17.7516 14.0349 17.7783 14.1743 17.7605Z", // Ignored as per the request
      fill: "currentColor",
    }),
  );

  const icons = [];

  if (thin) {
    icons.push(emptyThinIcon, filledThinIcon);
  } else {
    icons.push(emptyNormalIcon, filledNormalIcon);
  }

  return div(
    {
      class: styles["fc-widget-star-rating-item-icon"],
      style: `color: ${color}; display: flex; justify-content: center`,
    },
    ...icons,
  );
}
