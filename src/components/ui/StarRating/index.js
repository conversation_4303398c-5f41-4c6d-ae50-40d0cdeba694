import van from "vanjs-core";
import Transition from "@components/ui/Transition";
import { StarRatingItem } from "./StarRatingItem";
import styles from "./styles.module.css";
import { TRANSITION_NAMES } from "@/constants";

const { div } = van.tags;

export default function StarRating({
  max = 5,
  color = "#F8CD1C",
  thin = false,
  size,
  alwaysShowLabels = false,
  interactive = true,
  labels,
  vModel,
  onChange,
  isDisabled,
  showNumbers = false,
  /**
   * @type {"default" | "large"}
   */
  view = "default",
}) {
  const stars = Array(max)
    .fill()
    .map((_, i) => ({
      index: i + 1,
      label: labels && labels[i],
    }));

  const currentValue = van.derive(() => vModel?.val || null);

  const prevCurrentValue = van.state(null);

  const handleClick = (index) => {
    // if (isDisabled.val) return;
    const prevVal = currentValue.val;
    if (currentValue.val === index) {
      currentValue.val = null;
    } else {
      currentValue.val = index;
    }
    if (vModel?.val !== undefined) {
      vModel.val = currentValue.val;
    }
    if (typeof onChange === "function") {
      onChange(index);
    }
    prevCurrentValue.val = prevVal;
  };

  const classes = van.derive(
    () =>
      `${styles["fc-widget-star-rating"]} ${
        alwaysShowLabels ? styles["fc-widget-star-rating--with-labels"] : ""
      } ${showNumbers ? styles["fc-widget-star-rating--with-numbers"] : ""}
      ${isDisabled.val ? styles["fc-widget-star-rating--disabled"] : ""}
      ${view === "large" ? styles["fc-widget-star-rating--view-large"] : ""}`,
  );

  const activeLabel = van.derive(() => {
    if (currentValue.val === null) {
      return "";
    }
    return labels[currentValue.val - 1];
  });

  const prevActiveLabel = van.derive(() => {
    if (prevCurrentValue.val === null) {
      return "";
    }
    return labels[prevCurrentValue.val - 1];
  });

  return div(
    { class: styles["fc-widget-star-rating-container"] },
    div(
      {
        class: () => classes.val,
        "data-size": size,
        "data-testid": "fc-widget-star-rating",
        style: () =>
          `--fqz-widget-star-rating-stars-count: ${max}; --fqz-widget-star-rating-bg-color: ${color}`,
      },
      div(
        { class: styles["fc-widget-star-rating-wrapper"] },
        stars.map((star) =>
          div(
            {
              "data-testid": "fc-widget-star-rating-item",
              class: () =>
                `${styles["fc-widget-star-rating-item"]} ${
                  star.index === currentValue.val && interactive
                    ? styles["fc-widget-star-rating-item--final"]
                    : ""
                } ${
                  star.index <= currentValue.val && interactive
                    ? styles["fc-widget-star-rating-item--active"]
                    : ""
                }`,
              onclick: () => handleClick(star.index),
            },
            showNumbers
              ? div(
                  {
                    class: styles["fc-widget-star-rating-index"],
                    "data-testid": "star-number",
                  },
                  star.index,
                )
              : null,
            StarRatingItem({ thin, color }),
            alwaysShowLabels
              ? div(
                  {
                    class: styles["fc-widget-star-rating-label"],
                    "data-testid": "star-label",
                  },
                  star.label,
                )
              : null,
          ),
        ),
      ),
      !alwaysShowLabels
        ? Transition({
            show: activeLabel,
            name: TRANSITION_NAMES.FADE_IN,
            children: () =>
              div(
                {
                  class: styles["fc-widget-star-rating-active-label"],
                  "data-testid": "active-label",
                },
                () => {
                  return activeLabel.val || prevActiveLabel.val;
                },
              ),
          })
        : null,
    ),
  );
}
