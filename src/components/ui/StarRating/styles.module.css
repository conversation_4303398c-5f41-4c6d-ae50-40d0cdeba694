.fc-widget-star-rating {
  --fqz-widget-star-rating-stars-count: 5;
  --fqz-widget-star-rating-spacing: 16px;
  --fqz-widget-star-size: 25px;
  --fqz-widget-star-rating-bg-color: #fff;

  max-width: 100%;
  width: calc(
    var(--fqz-widget-star-rating-stars-count) * var(--fqz-widget-star-size) +
      var(--fqz-widget-star-rating-spacing) *
      (var(--fqz-widget-star-rating-stars-count) - 1)
  );
  transition: opacity 0.3s;
}

.fc-widget-star-rating-container {
  display: flex;
  justify-content: center;
}

.fc-widget-star-rating-wrapper {
  display: flex;
  justify-content: space-between !important;
}

.fc-widget-star-rating-item {
  cursor: pointer;
  position: relative;
  margin-right: 2px;
}

.fc-widget-star-rating-item:not(:last-child) {
  margin-right: 2px;
}

.fc-widget-star-rating-item svg {
  width: var(--fqz-widget-star-size);
  height: var(--fqz-widget-star-size);
  display: block;
  transition: opacity 0.3s;
}

.fc-widget-star-rating-item-icon {
  position: relative;
}

.fc-widget-star-rating-item svg:last-of-type {
  opacity: 0;
  z-index: 1;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s;
}

.fc-widget-star-rating-item--active svg:last-of-type {
  opacity: 1;
}

.fc-widget-star-rating-label {
  font-size: 10px;
  font-weight: 700;
  margin-top: 15px;
  overflow: hidden;
  width: 54px;
  text-align: center;
  position: relative;
}

.fc-widget-star-rating-active-label {
  font-size: 14px;
  font-weight: 700;
  margin-top: 20px;
  line-height: 1.1;
  overflow: hidden;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  position: relative;
}

.fc-widget-star-rating-label:empty {
  margin-top: 0;
}

.fc-widget-star-rating-label::after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 15px;
  background: linear-gradient(
    to left,
    var(--fqz-widget-main-place-color),
    transparent
  );
}

.fc-widget-star-rating-index {
  text-align: center;
  font-size: 10px;
  font-weight: 700;
  margin-bottom: 10px;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.fc-widget-star-rating-item--final .fc-widget-star-rating-index {
  opacity: 1;
}

.fc-widget-star-rating-index--active {
  opacity: 1;
}

.fc-widget-star-rating[data-size="sm"] {
  --fqz-widget-star-rating-spacing: 15px;
  --fqz-widget-star-size: 26px;
}

.fc-widget-star-rating[data-size="md"] {
  --fqz-widget-star-rating-spacing: 15px;
  --fqz-widget-star-size: 26px;
}

.fc-widget-star-rating[data-size="lg"] {
  --fqz-widget-star-rating-spacing: 20px;
  --fqz-widget-star-size: 48px;
}

.fc-widget-star-rating--with-labels {
  width: auto;
}

.fc-widget-star-rating--disabled {
  cursor: default;
  opacity: 0.5;
}

.fc-widget-star-rating-icon {
  position: relative;
  display: flex;
  justify-content: center;
}

@media screen and (min-width: 768px) {
  .fc-widget-star-rating--view-large .fc-widget-star-rating-index {
    font-size: var(--fqz-widget-text-font-size);
    line-height: 1.172;
    margin-bottom: 15px;
  }

  .fc-widget-star-rating--view-large .fc-widget-star-rating-label {
    width: 100px;
    font-size: 13px;
    line-height: 1.172;
  }

  .fc-widget-star-rating--view-large .fc-widget-star-rating-active-label {
    font-size: var(--fqz-widget-rating-scale-font-size, var(--fqz-widget-text-font-size));
    line-height: 1.1;
    margin-top: 20px;
  }

  .fc-widget-star-rating--view-large .fc-widget-star-rating-item {
    margin-right: 0;
  }

  .fc-widget-star-rating--view-large[data-size="sm"] {
    --fqz-widget-star-rating-spacing: 10px;
    --fqz-widget-star-size: 18px;
  }

  .fc-widget-star-rating--view-large[data-size="md"] {
    --fqz-widget-star-rating-spacing: 20px;
    --fqz-widget-star-size: 32px;
  }

  .fc-widget-star-rating--view-large[data-size="md"].fc-widget-star-rating--with-labels {
    --fqz-widget-star-rating-spacing: 10px;
  }

  .fc-widget-star-rating--view-large[data-size="md"].fc-widget-star-rating--with-labels
    .fc-widget-star-rating-item {
    margin-right: var(--fqz-widget-star-rating-spacing);
  }

  .fc-widget-star-rating--view-large[data-size="md"].fc-widget-star-rating--with-labels
    .fc-widget-star-rating-item:last-child {
    margin-right: 0;
  }
}