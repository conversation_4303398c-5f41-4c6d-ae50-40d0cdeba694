import van from "vanjs-core";

/**
 * Компонент для рендеринга элементов вне их родительского элемента
 * @param {Object} props - Параметры компонента
 * @param {Boolean} props.show - Показывать ли элемент
 * @param {HTMLElement} props.to - Родительский элемент, в который нужно добавить элемент (по умолчанию document.body)
 * @param {Number} props.removeDelay - Задержка перед удалением элемента
 */
export default function Portal({
  show,
  to = document.body,
  removeDelay = 0,
  renderChildren,
}) {
  let children = null;

  van.derive(() => {
    if (show.val) {
      children = renderChildren();
      van.add(to, children);
    } else {
      if (children) {
        setTimeout(() => {
          children.remove();
        }, removeDelay);
      }
    }
  });
}
