import van from "vanjs-core";
import styles from "./styles.module.css";
import Transition from "../Transition";

const { div, img } = van.tags;

export default function ImgRating({
  type,
  value,
  icons,
  size,
  alwaysShowLabels,
  labelTransitionName,
  isDisabled,
  vModel,
  onChange,
  hasScrollVisible,
  /**
   * @type {"default" | "large"}
   */
  view = "default",
}) {
  const ratingValue = van.derive(() => vModel?.val || value || null);

  const prevRatingValue = van.state(null);
  const isSmall = size === "sm";

  const labelsById = icons.reduce((acc, icon) => {
    acc[icon.id] = icon.label;
    return acc;
  }, {});

  const handleClick = (id) => {
    const prevVal = ratingValue.val;
    if (ratingValue.val === id) {
      ratingValue.val = null;
    } else {
      ratingValue.val = id;
    }

    if (vModel?.val !== undefined) {
      vModel.val = ratingValue.val;
    }

    if (typeof onChange === "function") {
      onChange(id);
    }

    prevRatingValue.val = prevVal;
  };

  const isInited = van.derive(() => ratingValue.val !== null);

  const classes = van.derive(() => {
    let cls = styles["fc-img-rating"];
    if (isSmall) {
      cls += ` ${styles["fc-img-rating--sm"]}`;
    }

    if (isDisabled?.val) {
      cls += ` ${styles["fc-img-rating--disabled"]}`;
    }

    if (isInited.val) {
      cls += ` ${styles["fc-img-rating--inited"]}`;
    }

    if (alwaysShowLabels) {
      cls += ` ${styles["fc-img-rating--labels"]}`;
    }

    if (view === "large") {
      cls += ` ${styles["fc-img-rating--view-large"]}`;
    }

    if (hasScrollVisible?.val) {
      cls += ` ${styles["fc-img-rating--has-scroll"]}`;
    }

    return cls;
  });

  const activeLabel = van.derive(() => {
    if (ratingValue.val === null) {
      return "";
    }
    return labelsById[ratingValue.val];
  });

  const prevActiveLabel = van.derive(() => {
    if (prevRatingValue.val === null) {
      return "";
    }
    return labelsById[prevRatingValue.val];
  });

  return div(
    {
      class: () => classes.val,
      "data-type": type,
      "data-icons": icons.length,
      "data-testid": "img-rating",
    },
    div(
      { class: styles["fc-img-rating__items"] },
      icons.map((icon) =>
        div(
          {
            class: () =>
              `${styles["fc-img-rating-item"]} ${
                ratingValue.val === icon.id ? styles["active"] : ""
              }`,
            "data-id": icon.id,
            "data-testid": "img-rating-item",
            onclick: () => handleClick(icon.id),
          },
          icon.url &&
            div(
              { class: styles["fc-img-rating-item__icon"] },
              img({
                src: icon.url,
                alt: icon.label || "",
              }),
            ),
          alwaysShowLabels && icon.label
            ? div(
                {
                  class: styles["fc-img-rating-item__label"],
                  "data-testid": "img-rating-item-label",
                },
                icon.label,
              )
            : null,
        ),
      ),
    ),
    !alwaysShowLabels
      ? Transition({
          show: activeLabel,
          name: labelTransitionName,
          children: () =>
            div({ class: styles["fc-img-rating-item__active-label"] }, () => {
              return activeLabel.val || prevActiveLabel.val;
            }),
        })
      : null,
  );
}
