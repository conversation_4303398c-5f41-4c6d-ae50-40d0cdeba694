// src/components/ui/Button/index.js

import van from "vanjs-core";
import styles from "./styles.module.css";
import { toValue } from "../../../helpers";

const { button, a } = van.tags;

export default function Button({
  children,
  onClick,
  type = "button",
  disabled = false,
  primary = true,
  variant = "outlined",
  style = "",
  href,
  target,
  rel,
  ...attrs
}) {
  const buttonClasses = van.derive(() => {
    let classes = styles["fc-widget-button"];
    classes += ` ${styles[`fc-widget-button--${variant}`]}`;
    if (primary) classes += ` ${styles["fc-widget-button-primary"]}`;
    if (toValue(disabled))
      classes += ` ${styles["fc-widget-button--disabled"]}`;
    if (attrs["class"]) {
      classes += ` ${attrs["class"]}`;
    }
    return classes;
  });

  const attrsWithoutClass = { ...attrs };
  delete attrsWithoutClass["class"];

  const commonProps = {
    class: buttonClasses,
    style: () => toValue(style),
    onclick: onClick,
    disabled,
    ...attrsWithoutClass,
  };

  if (href) {
    return a(
      {
        ...commonProps,
        href: () => toValue(href),
        target: () => toValue(target),
        rel: () => toValue(rel),
      },
      children,
    );
  }

  return button(
    {
      ...commonProps,
      type: () => toValue(type),
    },
    children,
  );
}
