.fc-widget-button {
  --button-font-weight: 500;
  --button-background-color: transparent;
  --button-text-color: var(--fqz-widget-text-on-place);
  --button-border-color: var(--fqz-widget-text-on-place);
  --button-border-radius: 100px;

  flex-grow: 1;
  border-radius: var(--button-border-radius);
  color: var(--button-text-color);
  font-weight: var(--button-font-weight);
  min-height: 36px;
  padding-top: 4px;
  padding-bottom: 5px;
  box-sizing: border-box;
  font-size: var(--fqz-widget-button-font-size);
  cursor: pointer;
  font-family: var(--fqz-widget-font-family);
  transition: opacity 0.3s, background-color 0.3s, color 0.3s;
  line-height: 1.172;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.fc-widget-button--outlined {
  background-color: var(--button-background-color);
  border: 2px solid var(--button-border-color);
}

.fc-widget-button--filled {
  --button-background-color: var(--fqz-widget-text-on-place);
  --button-text-color: var(--fqz-widget-main-place-color);

  background-color: var(--button-background-color);
  border: 2px solid var(--button-border-color);
  color: var(--button-text-color);
}

.fc-widget-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.fc-widget-button:focus {
  outline: none;
  text-decoration: none;
}

.fc-widget-button:hover {
  text-decoration: none;
  color: var(--button-text-color);
}

.fc-widget-button-primary.fc-widget-button--outlined {
  --button-border-color: var(--fqz-widget-main-color);
  border-color: var(--button-border-color);
}