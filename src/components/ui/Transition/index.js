import van from "vanjs-core";
import { toValue } from "@/helpers";

const { div } = van.tags;
const TRANSITION = "transition";
const ANIMATION = "animation";

export const vtcKey = Symbol("_vtc-van");

export default function Transition({
  name,
  calculateHeight,
  children,
  show,
  onTransitionEnd,
  beforeEnter,
  entered,
  afterEnter,
  beforeLeave,
  left,
  afterLeave,
  style,
  ...attrs
}) {
  const enterFromClass = van.derive(() => `${toValue(name)}-enter-from`);
  const enterActiveClass = van.derive(() => `${toValue(name)}-enter-active`);
  const enterToClass = van.derive(() => `${toValue(name)}-enter-to`);
  const leaveFromClass = van.derive(() => `${toValue(name)}-leave-from`);
  const leaveActiveClass = van.derive(() => `${toValue(name)}-leave-active`);
  const leaveToClass = van.derive(() => `${toValue(name)}-leave-to`);
  const wrapper = div({ style: `display: none; ${style || ""}`, ...attrs });
  let childrenEl = null;

  const finishEnter = (el) => {
    removeTransitionClass(el, enterToClass.val);
    removeTransitionClass(el, enterActiveClass.val);
    entered && entered();
    afterEnter && afterEnter();
  };

  const finishLeave = (el, done) => {
    removeTransitionClass(el, leaveFromClass.val);
    removeTransitionClass(el, leaveToClass.val);
    removeTransitionClass(el, leaveActiveClass.val);
    done && done();
    left && left();
    afterLeave && afterLeave(wrapper);
    onTransitionEnd && onTransitionEnd();
  };

  const removeAllTransitionClasses = (el) => {
    removeTransitionClass(el, enterFromClass.val);
    removeTransitionClass(el, enterActiveClass.val);
    removeTransitionClass(el, enterToClass.val);
    removeTransitionClass(el, leaveFromClass.val);
    removeTransitionClass(el, leaveActiveClass.val);
    removeTransitionClass(el, leaveToClass.val);
  };

  van.derive(() => {
    if (show.val) {
      // remove all classes from wrapper
      // to ensure the same animation can't be run twice
      removeAllTransitionClasses(wrapper);

      if (wrapper.style.display !== "none") {
        return;
      }
      if (wrapper.children.length > 0 && childrenEl) {
        childrenEl.remove();
      }
      childrenEl = children();
      wrapper.appendChild(childrenEl);
      nextFrame(() => {
        wrapper.style.display = "block";
        if (calculateHeight) {
          const height = childrenEl?.offsetHeight || wrapper.offsetHeight;

          // Если высота элемента равна 0, то не устанавливаем display: block
          // и пропускаем добавление анимаций/классов.
          // Если мы используем Transition внутри другого Transition, может возникнуть ситуация,
          // когда высота элемента равна 0
          if (height === 0) {
            wrapper.style.display = "block";
            return;
          }
          wrapper.style.setProperty("--fc-widget-el-height", `${height}px`);
        }
        beforeEnter && beforeEnter(wrapper);
        addTransitionClass(wrapper, enterFromClass.val);
        addTransitionClass(wrapper, enterActiveClass.val);

        nextFrame(() => {
          removeTransitionClass(wrapper, enterFromClass.val);
          addTransitionClass(wrapper, enterToClass.val);
          whenTransitionEnds(wrapper, null, null, () =>
            finishEnter(wrapper, false),
          );
        });
      });
    } else {
      if (!childrenEl) {
        return;
      }
      removeAllTransitionClasses(wrapper);
      beforeLeave && beforeLeave();
      addTransitionClass(wrapper, leaveFromClass.val);
      addTransitionClass(wrapper, leaveActiveClass.val);

      // add *-leave-active class before reflow so in the case of a cancelled enter transition
      // the css will not get the final state (#10677)
      forceReflow();
      nextFrame(() => {
        if (calculateHeight) {
          const height = wrapper.clientHeight || childrenEl?.clientHeight;
          wrapper.style.setProperty("--fc-widget-el-height", `${height}px`);
        }
        removeTransitionClass(wrapper, leaveFromClass.val);
        addTransitionClass(wrapper, leaveToClass.val);
        whenTransitionEnds(wrapper, null, null, () => {
          finishLeave(wrapper, false);
          if (wrapper.contains(childrenEl)) {
            wrapper.removeChild(childrenEl);
          } else {
            childrenEl.remove();
          }

          childrenEl = null;

          wrapper.style.display = "none";
        });
      });
    }
  });

  return wrapper;
}

export function addTransitionClass(el, cls) {
  cls.split(/\s+/).forEach((c) => c && el.classList.add(c));
  (el[vtcKey] || (el[vtcKey] = new Set())).add(cls);
}

export function removeTransitionClass(el, cls) {
  cls.split(/\s+/).forEach((c) => c && el.classList.remove(c));
  const _vtc = el[vtcKey];
  if (_vtc) {
    _vtc.delete(cls);
    if (!_vtc.size) {
      el[vtcKey] = undefined;
    }
  }
}

function nextFrame(cb) {
  requestAnimationFrame(() => {
    requestAnimationFrame(cb);
  });
}

let endId = 0;

function whenTransitionEnds(el, expectedType, explicitTimeout, resolve) {
  const id = (el._endId = ++endId);
  const resolveIfNotStale = () => {
    if (id === el._endId) {
      resolve();
    }
  };

  if (explicitTimeout) {
    return setTimeout(resolveIfNotStale, explicitTimeout);
  }

  const { type, timeout, propCount } = getTransitionInfo(el, expectedType);
  if (!type) {
    return resolve();
  }

  const endEvent = type + "end";
  let ended = 0;
  const end = () => {
    el.removeEventListener(endEvent, onEnd);
    resolveIfNotStale();
  };
  const onEnd = (e) => {
    if (e.target === el && ++ended >= propCount) {
      end();
    }
  };
  setTimeout(() => {
    if (ended < propCount) {
      end();
    }
  }, timeout + 1);
  el.addEventListener(endEvent, onEnd);
}

export function getTransitionInfo(el, expectedType) {
  const styles = window.getComputedStyle(el);

  // JSDOM may return undefined for transition properties
  const getStyleProperties = (key) => (styles[key] || "").split(", ");
  const transitionDelays = getStyleProperties(`${TRANSITION}Delay`);
  const transitionDurations = getStyleProperties(`${TRANSITION}Duration`);
  const transitionTimeout = getTimeout(transitionDelays, transitionDurations);
  const animationDelays = getStyleProperties(`${ANIMATION}Delay`);
  const animationDurations = getStyleProperties(`${ANIMATION}Duration`);
  const animationTimeout = getTimeout(animationDelays, animationDurations);

  let type = null;
  let timeout = 0;
  let propCount = 0;
  /* istanbul ignore if */
  if (expectedType === TRANSITION) {
    if (transitionTimeout > 0) {
      type = TRANSITION;
      timeout = transitionTimeout;
      propCount = transitionDurations.length;
    }
  } else if (expectedType === ANIMATION) {
    if (animationTimeout > 0) {
      type = ANIMATION;
      timeout = animationTimeout;
      propCount = animationDurations.length;
    }
  } else {
    timeout = Math.max(transitionTimeout, animationTimeout);
    type =
      timeout > 0
        ? transitionTimeout > animationTimeout
          ? TRANSITION
          : ANIMATION
        : null;
    propCount = type
      ? type === TRANSITION
        ? transitionDurations.length
        : animationDurations.length
      : 0;
  }
  const hasTransform =
    type === TRANSITION &&
    /\b(transform|all)(,|$)/.test(
      getStyleProperties(`${TRANSITION}Property`).toString(),
    );
  return {
    type,
    timeout,
    propCount,
    hasTransform,
  };
}

function getTimeout(delays, durations) {
  while (delays.length < durations.length) {
    delays = delays.concat(delays);
  }
  return Math.max(...durations.map((d, i) => toMs(d) + toMs(delays[i])));
}

// Old versions of Chromium (below 61.0.3163.100) formats floating pointer
// numbers in a locale-dependent way, using a comma instead of a dot.
// If comma is not replaced with a dot, the input will be rounded down
// (i.e. acting as a floor function) causing unexpected behaviors
function toMs(s) {
  // #8409 default value for CSS durations can be 'auto'
  if (s === "auto") return 0;
  return Number(s.slice(0, -1).replace(",", ".")) * 1000;
}

// synchronously force layout to put elements into a certain state
export function forceReflow() {
  return document.body.offsetHeight;
}
