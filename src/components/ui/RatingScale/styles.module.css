.fc-widget-rating-scale {
  --fqz-widget-rating-bg-color: var(--fqz-widget-main-color);
  --fqz-widget-rating-scale-offset: 5px;
  --fqz-widget-rating-scale-height: 36px;
  margin: auto;
  box-sizing: border-box;
  transition: opacity 0.3s;
  max-width: 580px;
  width: 100%;
  margin: 0 auto;
}

.fc-widget-rating-scale--disabled {
  cursor: default;
  opacity: 0.5;
}

.fc-widget-rating-scale .fc-widget-rating-scale__wrapper {
  display: flex;
  gap: 5px;
  align-items: flex-start;
}
.fc-widget-rating-scale .fc-widget-rating-scale__item {
  box-sizing: border-box;
  border: 2px solid var(--fqz-widget-rating-scale-color);
  border-radius: 4px;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--fqz-widget-rating-scale-height);
  cursor: pointer;
  transition: all 0.3s;
  flex: 1 1 0;
}
.fc-widget-rating-scale .fc-widget-rating-scale__item.active {
  background-color: var(--fqz-widget-rating-scale-color);
  color: white;
}

.fc-widget-rating-scale__active-label {
  font-size: 14px;
  font-weight: 700;
  margin-top: 20px;
  overflow: hidden;
  line-height: 1.1;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  position: relative;
}

@media screen and (min-width: 768px) {
  .fc-widget-rating-scale--view-large .fc-widget-rating-scale__wrapper {
    gap: 10px;
  }

  .fc-widget-rating-scale--view-large .fc-widget-rating-scale__active-label {
    font-size: var(--fqz-widget-text-font-size);
    line-height: 1.1;
    margin-top: 20px;
  }

  .fc-widget-rating-scale--view-large .fc-widget-rating-scale__item {
    font-size: var(--fqz-widget-rating-scale-font-size, var(--fqz-widget-text-font-size));
  }

}