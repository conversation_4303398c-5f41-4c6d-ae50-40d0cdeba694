import van from "vanjs-core";
import styles from "./styles.module.css"; // Assuming you have a styles file
import Transition from "../Transition";

const { div, span } = van.tags;

export default function RatingScale({
  count = 5,
  color = "#02ff2d",
  vModel,
  labels,
  labelTransitionName,
  onChange,
  isDisabled,
  /**
   * @type {"default" | "large"}
   */
  view = "default",
}) {
  const items = Array(count)
    .fill()
    .map((_, i) => ({
      index: i + 1,
    }));

  const currentValue = van.derive(() => vModel?.val || null);
  const prevCurrentValue = van.state(null);

  const handleClick = (index) => {
    const prevVal = currentValue.val;
    if (currentValue.val === index) {
      currentValue.val = null;
    } else {
      currentValue.val = index;
    }
    if (vModel?.val !== undefined) {
      vModel.val = currentValue.val;
    }
    if (typeof onChange === "function") {
      onChange(index);
    }
    prevCurrentValue.val = prevVal;
  };

  const classes = van.derive(
    () =>
      `${styles["fc-widget-rating-scale"]} ${
        isDisabled?.val ? styles["fc-widget-rating-scale--disabled"] : ""
      } ${view === "large" ? styles["fc-widget-rating-scale--view-large"] : ""}`,
  );

  const activeLabel = van.derive(() => {
    if (currentValue.val === null) {
      return "";
    }
    return labels[currentValue.val - 1];
  });

  const prevActiveLabel = van.derive(() => {
    if (prevCurrentValue.val === null) {
      return "";
    }
    return labels[prevCurrentValue.val - 1];
  });

  return div(
    {
      class: () => classes.val,
      style: () => `--fqz-widget-rating-scale-color: ${color}`,
      "data-testid": "rating-scale",
    },
    div(
      { class: styles["fc-widget-rating-scale__wrapper"] },
      items.map((star) =>
        div(
          {
            class: () =>
              `${styles["fc-widget-rating-scale__item"]} ${
                star.index === currentValue.val ? styles["active"] : ""
              }`,
            onclick: () => handleClick(star.index),
            "data-testid": "rating-scale-item",
          },
          span(star.index),
        ),
      ),
    ),
    labels
      ? Transition({
          show: activeLabel,
          name: labelTransitionName,
          children: () =>
            div(
              { class: styles["fc-widget-rating-scale__active-label"] },
              () => {
                return activeLabel.val || prevActiveLabel.val;
              },
            ),
        })
      : null,
  );
}
