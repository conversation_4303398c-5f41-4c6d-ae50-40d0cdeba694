import van from "vanjs-core";
import styles from "./styles.module.css";

const { div, button } = van.tags;

export function FormQuestion({ text }) {
  const id = van.state(Math.random().toString(36).substr(2, 9)); // Generate a unique ID
  const showTooltip = van.state(false);

  const handleMouseEnter = () => {
    showTooltip.val = true;
  };

  const handleMouseLeave = () => {
    showTooltip.val = false;
  };

  return div(
    { class: styles["fc-widget-question"] },
    button(
      {
        id: `fc-question-btn-${id.val}`,
        class: styles["fc-widget-question-btn"],
        onmouseenter: handleMouseEnter,
        onmouseleave: handleMouseLeave,
      },
      "?",
    ),
    showTooltip.val &&
      div(
        {
          class: "fc-tooltip fc-question-tooltip",
          style: "max-width: 250px;",
        },
        text,
      ),
  );
}
