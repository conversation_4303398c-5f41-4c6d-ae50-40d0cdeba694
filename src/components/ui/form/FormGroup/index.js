import van from "vanjs-core";
import styles from "./styles.module.css";
import Transition from "../../Transition";
import { TRANSITION_NAMES } from "@/constants";
import FormError from "../FormError";

export default function FormGroup({
  label,
  children,
  error,
  scrollIntoViewOnError = true,
  size = "default",
  fullWidth = true,
  ...attrs
}) {
  const hasError = van.derive(() => error && !!error.val);
  return van.tags.div(
    {
      class:
        styles["fc-widget-form-group"] +
        " " +
        (size === "large" ? styles["fc-widget-form-group--large"] : "") +
        " " +
        (fullWidth ? styles["fc-widget-form-group--full-width"] : ""),
      ...attrs,
    },
    label
      ? () =>
          van.tags.label(
            {
              class: styles["fc-widget-form-group-label"],
              for: label?.for,
            },
            label?.text,
          )
      : null,
    children,
    () =>
      Transition({
        name: TRANSITION_NAMES.FADE_IN,
        children: () =>
          FormError({ error, scrollIntoViewOnAppear: scrollIntoViewOnError }),
        show: hasError,
      }),
  );
}
