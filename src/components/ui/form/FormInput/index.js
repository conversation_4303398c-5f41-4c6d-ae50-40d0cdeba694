import van from "vanjs-core";
import styles from "./styles.module.css";
import { toValue } from "@/helpers";

const { div, input } = van.tags;

export default function FormInput({
  vModel,
  invalid,
  valid,
  isDisabled,
  onChange,
  ref,
  icon,
  iconPosition = "left",
  size = "default",
  fullWidth = true,
  ...attrs
}) {
  const inputValue = van.derive(() => toValue(vModel) || "");

  const classes = van.derive(() => {
    let cls = styles["fc-widget-input"];
    if (toValue(invalid)) {
      cls += ` ${styles["fc-widget-input--invalid"]}`;
    }
    if (toValue(valid)) {
      cls += ` ${styles["fc-widget-input--valid"]}`;
    }
    if (toValue(isDisabled)) {
      cls += ` ${styles["fc-widget-input--disabled"]}`;
    }
    if (icon) {
      cls += ` ${styles[`fc-widget-input--icon-${iconPosition}`]}`;
    }

    if (size === "large") {
      cls += ` ${styles["fc-widget-input--large"]}`;
    }

    if (fullWidth) {
      cls += ` ${styles["fc-widget-input--full-width"]}`;
    }

    return cls;
  });

  const inputElement = input({
    ...attrs,
    class: styles["fc-widget-input__field"],
    value: () => toValue(inputValue),
    oninput: (e) => {
      inputValue.val = e.target.value;
      if (vModel) {
        vModel.val = e.target.value;
      }
      if (onChange && typeof onChange === "function") {
        onChange(e.target.value);
      }
    },
  });

  if (ref !== undefined) {
    ref.val = inputElement;
  }

  return div(
    {
      class: () => classes.val,
    },
    div(
      {
        class: styles["fc-widget-input__wrapper"],
      },
      inputElement,
    ),
  );
}
