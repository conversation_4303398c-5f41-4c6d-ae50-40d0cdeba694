.fc-widget-input {
  display: block;
  font-size: 16px;
  width: 100%;
}

.fc-widget-input {
  position: relative;
  border-radius: 4px;
  padding: 0;
}

.fc-widget-input__field,
.fc-widget-input__count,
.fc-widget-input__valid {
  position: relative;
  z-index: 1;
}

.fc-widget-input__wrapper {
  position: relative;
}

.fc-widget-input__field {
  padding: 0 15px;
  height: 45px;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  font-size: 16px;
  border: 1px solid rgb(207 216 220 / 100%);
  border-radius: 4px;
  font-family: var(--fqz-widget-font-family) !important;
  color: black;
  background-color: #fff;
  transition: border-color 0.3s;
  width: 100%;
}

.fc-widget-input__field:focus {
  outline: none;
  border-color: var(--fqz-widget-main-color);
}

.fc-widget-input__field::placeholder {
  color: rgb(115 128 141 / 100%);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.3;
  font-family: var(--fqz-widget-font-family);
}

.fc-widget-input__count {
  position: absolute;
  z-index: 1;
  bottom: 8px;
  right: 12px;
  font-size: 11px;
  color: var(--f-color-text-inactive);
}

.fc-widget-input--invalid .fc-widget-input__field {
  border-color: var(--fqz-widget-error-color);
}

.fc-widget-input--valid .fc-widget-input__field {
  border-color: var(--fqz-widget-success-color);
}

.fc-widget-input--disabled .fc-widget-input__field {
  opacity: 0.5;
}

@media screen and (min-width: 768px) {
  .fc-widget-input--large .fc-widget-input__field {
    height: 45px;
    padding: 0 15px;
    font-size: 16px;
  }

  .fc-widget-input--large .fc-widget-input__field::placeholder {
    font-size: var(--fqz-widget-text-font-size);
  }
}