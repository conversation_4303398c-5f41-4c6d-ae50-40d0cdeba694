.fc-widget-textarea {
  --textarea-padding: 15px;
  --textarea-font-size: 16px;
  display: block;
  font-size: 14px;
  width: 100%;
  scroll-margin-top: 100px;
  scroll-margin-bottom: 100px;
}

.fc-widget-textarea--scrollable {
  overflow-y: scroll;
}

.fc-textarea__wrapper {
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding: 0;
  min-height: 48px;
}

.fc-textarea__field,
.fc-textarea__count,
.fc-textarea__valid {
  position: relative;
  z-index: 1;
}

.fc-textarea__field {
  min-height: 100px;
  padding: var(--textarea-padding);
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  font-size: 16px;
  line-height: 1.1;
  resize: none !important;
  border: 1px solid rgb(207 216 220 / 100%);
  border-radius: 4px;
  font-family: var(--fqz-widget-font-family) !important;
  color: black;
  background-color: #fff;
  transition: border-color 0.3s;
}

.fc-textarea__field:focus {
  outline: none;
  border-color: var(--fqz-widget-main-color);
}

.fc-textarea__field::placeholder {
  color: rgb(115 128 141 / 100%);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.1;
  font-family: var(--fqz-widget-font-family);
  padding-bottom: 20px;
  display: block;
}

.fc-textarea__count {
  position: absolute;
  z-index: 1;
  bottom: 8px;
  right: 12px;
  font-size: 11px;
  color: var(--f-color-text-inactive);
}

.fc-textarea__view {
  position: absolute;
  inset: 0;
  background: #fff;
  border: 1px solid var(--fqz-widget-text-on-place);
  border-radius: inherit;
  pointer-events: none;
  font: inherit;
  color: var(--f-color-text);
  z-index: 0;
}

.fc-textarea__field:focus ~ .fc-textarea__view {
  border-color: var(--fqz-widget-text-on-place);
  box-shadow: 0 0 5px rgb(63 101 241 / 50%);
}

.fc-widget-textarea--invalid .fc-textarea__view {
  border-color: var(--fqz-widget-text-on-place);
  box-shadow: none;
}

.fc-widget-textarea--invalid .fc-textarea__field {
  border-color: var(--fqz-widget-error-color);
}

.fc-textarea__valid {
  display: none;
  position: absolute;
  right: 12px;
  bottom: 8px;
}

.fc-widget-textarea--valid .fc-textarea__view {
  border-color: var(--fqz-widget-success-color);
  box-shadow: none;
}

.fc-widget-textarea--valid .fc-textarea__valid {
  display: block;
}

.fc-widget-textarea--valid .fc-textarea__count {
  display: none;
}

.fc-widget-textarea--disabled .fc-textarea__field {
  opacity: 0.5;
}

.fc-widget-textarea--disabled .fc-textarea__view {
  background-color: #f2f5f6;
}

.fc-widget-textarea-reader {
  font-size: 14px;
}

@media screen and (min-width: 768px) {
  .fc-textarea__field {
    padding: var(--textarea-padding);
    font-size: var(--textarea-font-size);
  }

  .fc-textarea__field::placeholder {
    font-size: var(--textarea-font-size);
  }
}
