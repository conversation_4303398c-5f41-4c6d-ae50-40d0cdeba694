import van from "vanjs-core";
import { throttle, toValue } from "@/helpers";
import styles from "./styles.module.css";

const { div, textarea } = van.tags;

/**
 * Adjust textarea height based on content
 * @param {*} element - textarea element
 * @param {*} maxHeight - maximum height of the textarea
 */
const useAutoHeight = (element, maxHeight) => {
  const onInput = () => {
    const elem = element.val;
    if (!elem) return;

    // Reset the height to auto to recalculate scrollHeight
    elem.style.height = "auto";

    const minValue = Math.min(elem.scrollHeight, maxHeight);
    elem.style.height = `${minValue}px`;

    elem.scrollIntoView();
    // add scroll if content is bigger than the textarea
    if (minValue === maxHeight) {
      elem.style.overflow = "auto";
    } else {
      elem.style.overflow = "hidden";
    }
  };

  const throttledInput = throttle(onInput, 300);

  const setupListeners = () => {
    element.val.addEventListener("input", throttledInput);
  };

  // @TODO: Remove listeners on teardown!!
  // const teardownListeners = () => {
  //   element.val.removeEventListener("input", debouncedInput);
  // };

  van.derive(() => {
    if (element.val) {
      setupListeners();
    }
  });
};

export default function FormTextarea({
  value,
  height,
  maxHeight = 250,
  maxlength = 999,
  counter,
  invalid,
  valid,
  isDisabled,
  readMode,
  onChange,
  autofocus = false,
  autofocusDelay = 0,
  scrollIntoView = false,
  scrollIntoViewDelay = 0,
  ref,
  style = "",
  ...attrs
}) {
  const textareaValue = van.state(value);
  const textareaHeight = van.state(height);
  const textareaCounter = van.state(counter);
  const textareaReadMode = van.state(readMode);
  const innerRef = van.state(null);

  useAutoHeight(innerRef, maxHeight);

  const count = van.derive(() => {
    const val = toValue(value) || "";
    return maxlength - val.length;
  });

  const classes = van.derive(() => {
    let cls = styles["fc-widget-textarea"];
    if (toValue(invalid)) {
      cls += ` ${styles["fc-widget-textarea--invalid"]}`;
    }
    if (toValue(valid)) {
      cls += ` ${styles["fc-widget-textarea--valid"]}`;
    }
    if (toValue(isDisabled)) {
      cls += ` ${styles["fc-widget-textarea--disabled"]}`;
    }
    return cls;
  });

  let styleString = `min-height: ${toValue(textareaHeight) || 100}px;`;

  if (style) {
    styleString += ` ${style}`;
  }

  const textareaElement = textarea({
    ...attrs,
    class: styles["fc-textarea__field"],
    value: () => toValue(value),
    style: styleString,
    maxLength: maxlength,
    oninput: (e) => {
      if (value) {
        value.val = e.target.value;
      }
      if (onChange && typeof onChange === "function") {
        onChange(e.target.value);
      }
    },
  });

  innerRef.val = textareaElement;
  if (ref !== undefined) {
    ref.val = textareaElement;
  }

  setTimeout(() => {
    // autofocus with certain delay and scrollIntoView with certain delay
    if (textareaElement && autofocus) {
      setTimeout(() => {
        textareaElement.focus();
      }, autofocusDelay);
    }

    if (textareaElement && scrollIntoView) {
      setTimeout(() => {
        textareaElement.scrollIntoView({ behavior: "smooth", block: "center" });
      }, scrollIntoViewDelay);
    }
  }, 10);

  return div(
    {
      class: () => classes.val,
    },
    () =>
      textareaReadMode.val
        ? div(
            {
              class: styles["fc-widget-textarea-reader"],
            },
            toValue(textareaValue) || "–",
          )
        : div(
            {
              class: styles["fc-textarea__wrapper"],
            },
            () =>
              textareaCounter.val &&
              div(
                {
                  class: styles["fc-textarea__count"],
                },
                toValue(count),
              ),
            textareaElement,
          ),
  );
}
