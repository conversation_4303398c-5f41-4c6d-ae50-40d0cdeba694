.fc-widget-file-check {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  border-radius: 8px;
  border: 1px solid rgba(231, 235, 237, 1);
  overflow: hidden;
  transition: border-color 0.3s, opacity 0.3s;
  height: 100%; /* Make the container take full height */
}

.fc-widget-file-check.disabled {
  opacity: 0.4;
  cursor: not-allowed !important;
}

.fc-widget-file-check.inactive {
    opacity: 0.5;
}

.fc-widget-file-check.disabled .fc-widget-file-check__box {
    cursor: not-allowed;
}

.fc-widget-file-check.checked {
  border-color: var(--fqz-widget-main-color);
}

.fc-widget-file-check__container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.fc-widget-file-check__container--inactive {
    opacity: 1 !important;
}

.fc-widget-file-check__container--disabled {
    opacity: 1 !important;
}

.fc-widget-file-check__label {
    padding: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    text-align: left;
    cursor: pointer;
}

.fc-widget-file-check__label-text {
    opacity: 0 !important;
}

.fc-widget-file-check__text {
    font-size: 15px;
    opacity: 1;
    line-height: 1.1;
    padding: 10px 10px 11px 10px;
    cursor: pointer;
    text-align: left;
    width: 100%;
    box-sizing: border-box;
    flex-grow: 1; /* Allow text to grow */
    display: flex;
}

.fc-widget-file-check__box {
    position: absolute;
    z-index: 2;
    top: 9px;
    left: 8.5px;
    width: 24px;
    height: 24px;
    background-color: #fff;
    cursor: pointer;
}

.fc-widget-file-preview {
  width: 100%;
  height: 216px; /* Fixed height for preview */
  overflow: hidden;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background-color: #eceff1;
  flex-shrink: 0; /* Prevent shrinking */
}

.fc-widget-file-preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
}

.fc-widget-file-preview-icon {
  width: 92px;
  height: 92px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s;
}

.fc-widget-file-preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  position: absolute;
  top: 0;
  left: 0;
  /* plus icon */
}

.fc-widget-file-preview-image:hover .fc-widget-file-preview-icon {
  opacity: 0.5;
}

.fc-widget-full-image-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999999999999;
}

.fc-widget-full-image {
  max-width: 90%;
  max-height: 90%;
}

.fc-widget-full-video {
  max-width: 90%;
  max-height: 90%;
}

.no-label-text .fc-widget-file-check__text {
    display: none;
}

@media screen and (min-width: 768px) {
  .fc-widget-file-check.large .fc-widget-file-check__text {
    font-size: var(--fqz-widget-text-font-size);
  }
}