import van from "vanjs-core"; // Ensure VanJS is imported
import styles from "./styles.module.css"; // Import the CSS module
import FormCheck from "../Check"; // Import the FormCheck component

const { div, img } = van.tags;

export default function FormFileCheck({
  vModel,
  previewUrl,
  //   fileUrl,
  isDisabled,
  isInactive,
  size = "default",
  ...attrs
}) {
  const isChecked = van.state(false);

  const noLabelText = !attrs.labelText && !attrs.children;

  const checkClasses = van.derive(() => {
    let classes = styles["fc-widget-file-check"];

    if (isChecked.val) {
      classes += ` ${styles["checked"]}`;
    }

    if (isDisabled.val) {
      classes += ` ${styles["disabled"]}`;
    }

    if (size === "large") {
      classes += ` ${styles["large"]}`;
    }

    if (isInactive.val) {
      classes += ` ${styles["inactive"]}`;
    }

    if (noLabelText) {
      classes += ` ${styles["no-label-text"]}`;
    }

    return classes;
  });

  return div(
    { class: () => checkClasses.val, ...attrs },
    div(
      {
        class: `${styles["fc-widget-file-preview"]}`,
      },
      img({
        src: previewUrl,
        class: styles["fc-widget-file-preview-image"],
        alt: "Preview",
      }),
    ),
    div({ class: styles["fc-widget-file-check__text"] }, attrs["labelText"]),
    FormCheck({
      vModel,
      vModelChecked: isChecked,
      classes: {
        "check-label": styles["fc-widget-file-check__label"],
        "check-box": styles["fc-widget-file-check__box"],
        "check-text": styles["fc-widget-file-check__label-text"],
        "check-container": styles["fc-widget-file-check__container"],
        "check--inactive": styles["fc-widget-file-check__container--inactive"],
        "check--disabled": styles["fc-widget-file-check__container--disabled"],
      },
      inactive: isInactive,
      disabled: isDisabled,
      ...attrs,
    }),
  );
}
