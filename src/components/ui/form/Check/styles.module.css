.fc-widget-check {
  display: block;
  transition: opacity 0.3s;
  --box-background: white;
  --text-font-size: 14px;
}

.fc-widget-check-label {
  display: flex;
  line-height: 1;
  cursor: pointer;
  text-align: left;
}

.fc-widget-check-input {
  display: block !important;
  opacity: 0;
  position: absolute;
  clip: rect(0, 0, 0, 0);
}

.fc-widget-check-box {
  display: block;
  width: 22px;
  height: 22px;
  box-sizing: border-box;
  border: 1px solid rgba(207, 216, 220, 1);
  background-color: var(--box-background);
  margin-right: 14px;
  border-radius: 3px;
  position: relative;
  flex-shrink: 0;
  color: var(--fqz-widget-main-color);
  transition: background-color 0.3s, border-color 0.3s;
}

.fc-widget-check-marker {
  display: block;
  opacity: 0;
  position: absolute;
  inset: 0;
  margin: auto;
  transition: opacity 0.3s;
}

.fc-widget-check-text {
  color: var(--fqz-widget-text-on-place);
  display: flex;
  align-items: center;
  font-size: var(--text-font-size);
  line-height: 1.1;
  margin-top: 1px;
  opacity: 1;
  transition: opacity 0.3s;
}

.fc-widget-check-checkbox .fc-widget-check-marker {
  width: 10px;
  height: 8px;
}

.fc-widget-check-checkbox .fc-widget-check-marker svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.fc-widget-check-radio .fc-widget-check-box {
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

.fc-widget-check-radio .fc-widget-check-marker {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
}

.fc-widget-check-checked .fc-widget-check-box .fc-widget-check-marker {
  opacity: 1;
}

.fc-widget-check-checked .fc-widget-check-box {
    color: white;
    background-color: var(--fqz-widget-main-color);
    border-color: var(--fqz-widget-main-color);
}

.fc-widget-check-checked .fc-widget-check-text {
  opacity: 1;
}

.fc-widget-check-input:checked ~ .fc-widget-check-box .fc-widget-check-marker {
  opacity: 1;
}

.fc-widget-check-input:checked ~ .fc-widget-check-text {
  color: var(--fqz-widget-text-on-place);
}

.fc-widget-check-input:checked ~ .fc-widget-check-box  {
    background-color: var(--fqz-widget-main-color);
}

.fc-widget-check-input:focus ~ .fc-widget-check-box {
  box-shadow: 0 0 5px rgb(63 101 241 / 50%);
  border-color: var(--fqz-widget-text-on-place);
}

.fc-widget-check-disabled {
  opacity: 0.7;
}

.fc-widget-check-disabled .fc-widget-check-label {
  cursor: not-allowed;
}

.fc-widget-check-inactive {
  opacity: 0.7;
  cursor: pointer;
}

.fc-widget-check-inactive .fc-widget-check-label {
  cursor: pointer;
}

.fc-widget-check-sm .fc-widget-check-box {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}

.fc-widget-check-sm .fc-widget-check-text {
  min-height: 19px;
  font-size: 12px;
}

.fc-widget-check-sm.fc-widget-check-radio .fc-widget-check-marker {
  width: 6px;
  height: 6px;
}

.fc-widget-check-checkbox.fc-widget-check-partial .fc-widget-check-marker {
  opacity: 1;
  width: 8px;
  height: 8px;
  background: #3f65f1;
  box-shadow: 0 7px 64px rgb(0 0 0 / 7%);
  border-radius: 2px;
}

@media screen and (min-width: 768px) {
  .fc-widget-check-text {
    font-size: var(--text-font-size);
  }
}