import van from "vanjs-core"; // Ensure VanJS is imported
import styles from "./styles.module.css"; // Import the CSS module
import { FormQuestion } from "../Question";
import { toValue } from "@/helpers";

const { div, input, label, span } = van.tags;
const { svg, path } = van.tags("http://www.w3.org/2000/svg");

export default function FormCheck({
  type = "checkbox",
  labelText,
  hint,
  value,
  vModel,
  vModelChecked,
  disabled,
  inactive,
  onChange,
  size,
  name, // Add name attribute for radio buttons
  wrapperAttrs,
  classes = {},
  style = "",
  ...attrs
}) {
  const isDisabled = van.derive(() => disabled && disabled.val);
  const isInactive = van.derive(() => inactive && inactive.val);
  const isCheckbox = type === "checkbox";
  const isRadio = type === "radio";
  const prevVModel = van.state(toValue(vModel));

  const isChecked = van.derive(() => {
    if (toValue(vModel) !== undefined) {
      if (Array.isArray(vModel.val)) {
        return vModel.val.includes(value);
      }
      if (isCheckbox) {
        return vModel.val;
      } else if (isRadio) {
        return vModel.val === value;
      }
    }
  });

  van.derive(() => {
    if (vModelChecked) {
      vModelChecked.val = isChecked.val;
    }
  });

  const handleInputChange = () => {
    if (isDisabled.val) return false;

    if (toValue(vModel) !== undefined && vModel.val !== undefined) {
      prevVModel.val = vModel.val;
      if (isCheckbox) {
        if (Array.isArray(vModel.val)) {
          if (vModel.val.includes(value)) {
            vModel.val = vModel.val.filter((val) => val !== value);
          } else {
            vModel.val = [...vModel.val, value];
          }
        } else {
          vModel.val = !vModel.val;
        }
      }

      if (isRadio) {
        vModel.val = value;
      }

      if (typeof onChange === "function" && vModel.val !== prevVModel.val) {
        onChange(vModel.val);
      }
    }
  };

  const widgetCheckClasses = van.derive(() => {
    let checkClasses = styles["fc-widget-check"];
    if (size === "small") {
      checkClasses += ` ${styles["fc-widget-check-small"]}`;
    }

    if (isDisabled && isDisabled.val) {
      checkClasses += ` ${styles["fc-widget-check-disabled"]} ${classes["check--disabled"] ? classes["check--disabled"] : ""}`;
    }

    if (isInactive && isInactive.val) {
      checkClasses += ` ${styles["fc-widget-check-inactive"]} ${classes["check--inactive"] ? classes["check--inactive"] : ""}`;
    }

    if (isChecked.val) {
      checkClasses += ` ${styles["fc-widget-check-checked"]}`;
    }

    if (isCheckbox) {
      checkClasses += ` ${styles["fc-widget-check-checkbox"]}`;
    }

    if (isRadio) {
      checkClasses += ` ${styles["fc-widget-check-radio"]}`;
    }

    if (classes["check-container"]) {
      checkClasses += ` ${classes["check-container"]}`;
    }

    return checkClasses;
  });

  return div(
    {
      class: () => widgetCheckClasses.val,
      style,
      "data-testid": "check",
      "data-disabled": () => isDisabled.val,
      "data-inactive": () => isInactive.val,
      ...wrapperAttrs,
    },
    label(
      {
        class: `${styles["fc-widget-check-label"]} ${classes["check-label"] || ""}`,
        onclick: (e) => {
          e.preventDefault();
          handleInputChange();
          return true;
        },
        "data-checked": () => (isChecked.val ? "true" : "false"),
      },
      input({
        type,
        name, // Set the name attribute for radio buttons
        disabled: isDisabled.val,
        class: `${styles["fc-widget-check-input"]} ${classes["check-input"] || ""}`,
        checked: () => isChecked.val, // Ensure the checked state is managed correctly
        ...attrs,
      }),
      div(
        {
          class: `${styles["fc-widget-check-box"]} ${classes["check-box"] || ""}`,
        },
        span(
          {
            class: `${styles["fc-widget-check-marker"]} ${classes["check-marker"] || ""}`,
          },
          type === "checkbox"
            ? svg(
                {
                  width: "10",
                  height: "8",
                  viewBox: "0 0 10 8",
                  fill: "none",
                  xmlns: "http://www.w3.org/2000/svg",
                },
                path({
                  d: "M8.49373 0.758435C8.83831 0.413855 9.39698 0.413855 9.74156 0.758435C10.0861 1.10302 10.0861 1.66169 9.74156 2.00627L4.44745 7.30039C4.10287 7.64497 3.54419 7.64497 3.19961 7.30039L0.258435 4.35921C-0.0861451 4.01463 -0.0861451 3.45596 0.258435 3.11138C0.603015 2.7668 1.16169 2.7668 1.50627 3.11138L3.82353 5.42864L8.49373 0.758435Z",
                  fill: "currentColor",
                }),
              )
            : null,
        ),
      ),
      div(
        {
          class: `${styles["fc-widget-check-text"]} ${classes["check-text"] || ""}`,
        },
        div(
          labelText
            ? typeof labelText === "object"
              ? labelText
              : span(labelText)
            : null,
          hint ? FormQuestion({ text: hint }) : null,
        ),
      ),
    ),
  );
}
