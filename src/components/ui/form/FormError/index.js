import van from "vanjs-core";
import styles from "./styles.module.css";
import { toValue } from "@/helpers";

const { div } = van.tags;

export default function FormError({ error, scrollIntoViewOnAppear = true }) {
  const prevError = van.state(null);
  van.derive(() => {
    if (toValue(error)) {
      prevError.val = toValue(error);
    }
  });

  const errorElem = div(
    {
      class: styles["fc-widget-q-form-error"],
    },
    () => div(toValue(error) || toValue(prevError)),
  );

  setTimeout(() => {
    if (scrollIntoViewOnAppear && toValue(error)) {
      errorElem?.scrollIntoView?.({ behavior: "smooth", block: "end" });
    }
  }, 300);

  return errorElem;
}
