import van from "vanjs-core";
import styles from "./styles.module.css";
import { toValue } from "@/helpers";
import FormInput from "../FormInput";
import useMask from "@/composables/useMask";

const { div } = van.tags;

export default function FormMaskedInput({
  vModel,
  invalid,
  valid,
  isDisabled,
  onChange,
  icon,
  iconPosition = "left",
  maskType,
  maskOptions,
  ...attrs
}) {
  const inputValue = van.derive(() => toValue(vModel) || "");
  const inputRef = van.state(null);

  useMask(inputRef, maskType, maskOptions);

  const inputElement = FormInput({
    ...attrs,
    value: () => toValue(inputValue),
    ref: inputRef,
    icon,
    iconPosition,
    invalid,
    valid,
    vModel,
    isDisabled,
    onChange,
  });

  return div(
    {
      class: styles["fc-widget-masked-input"],
    },
    inputElement,
  );
}
