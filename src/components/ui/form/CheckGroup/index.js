import van from "vanjs-core"; // Ensure VanJS is imported
import styles from "./styles.module.css"; // Import the CSS module
import FormCheck from "../Check"; // Import the FormCheck component

const { div } = van.tags;

export default function FormCheckGroup({
  vModel,
  type = "checkbox",
  items = [],
  onChange = () => {},
  checkAttrs,
}) {
  return div(
    {
      class: styles["fc-widget-check-group"],
    },
    items.map((item) =>
      FormCheck({
        type,
        vModel,
        boxBackground: "#fff",
        onChange: () => onChange(vModel?.val),
        wrapperAttrs: checkAttrs,
        disabled: item.isDisabled,
        inactive: item.isInactive,
        labelText: item.labelText,
        value: item.value,
      }),
    ),
  );
}
