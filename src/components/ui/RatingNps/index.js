import van from "vanjs-core";
import { toValue } from "@/helpers";
import styles from "./styles.module.css";
import { RATING_NPS_DESIGN } from "@/helpers/question-helpers/questionTypes";

const { div, span } = van.tags;

export default function RatingNps({
  fromOne,
  designType,
  startColor,
  endColor,
  startLabel,
  endLabel,
  vModel,
  isDisabled,
  onChange,
  /**
   * View type of the question.
   *  @type {"default" | "large"}
   */
  view = "default",
}) {
  const RATING_NPS_COUNT = 10;
  const NORMALIZED_RATING_NPS_COUNT = fromOne
    ? RATING_NPS_COUNT
    : RATING_NPS_COUNT + 1;

  const items = Array(NORMALIZED_RATING_NPS_COUNT)
    .fill()
    .map((_, i) => (fromOne ? i + 1 : i));

  const currentValue = van.derive(() =>
    toValue(vModel) !== undefined ? toValue(vModel) : null,
  );
  const isColored =
    designType === RATING_NPS_DESIGN.COLORED ||
    designType === RATING_NPS_DESIGN.CUSTOM;
  const inited = van.derive(
    () => currentValue.val !== null && currentValue.val !== undefined,
  );

  const handleClick = (item) => {
    if (item === currentValue.val) {
      currentValue.val = null;
    } else {
      currentValue.val = item;
    }

    if (vModel?.val !== undefined || vModel?.val !== null) {
      vModel.val = currentValue.val;
    }
    if (typeof onChange === "function") {
      onChange(currentValue.val);
    }
  };

  /**
   * Преобразовать цвет в формате hex в объект RGB
   * @param {string} hex - строка цвета в формате hex
   * @returns {{r: number, g: number, b: number}}
   */
  const hexToRgb = (hex) => {
    const bigint = parseInt(hex.slice(1), 16);
    return {
      r: (bigint >> 16) & 255,
      g: (bigint >> 8) & 255,
      b: bigint & 255,
    };
  };

  /**
   * Преобразовать объект RGB в цвет в формате hex
   * @param {{r: number, g: number, b: number}} rgb - объект RGB
   * @returns {string}
   */
  const rgbToHex = ({ r, g, b }) => {
    const toHex = (value) => value.toString(16).padStart(2, "0");
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  };

  /**
   * Интерполировать между двумя числами
   * @param {number} start - начальное значение
   * @param {number} end - конечное значение
   * @param {number} factor - коэффициент интерполяции (от 0 до 1)
   * @returns {number}
   */
  const interpolate = (start, end, factor) => start + (end - start) * factor;

  /**
   * Разделить градиент на сегменты и вернуть массив цветов в формате hex
   * Использует NORMALIZED_RATING_NPS_COUNT в качестве ссылки на количество сегментов
   * @param {string} colorFrom - строка цвета в формате hex
   * @param {string} colorTo - строка цвета в формате hex
   * @returns {string[]}
   */
  const splitGradient = (colorFrom, colorTo) => {
    const isHex = (color) => color.startsWith("#");
    const isRgb = (color) => color.startsWith("rgb");

    const parseRgb = (color) => {
      const match = color.match(/\d+/g);
      return match
        ? {
            r: parseInt(match[0]),
            g: parseInt(match[1]),
            b: parseInt(match[2]),
          }
        : null;
    };

    const fromRgb = isHex(colorFrom)
      ? hexToRgb(colorFrom)
      : isRgb(colorFrom)
        ? parseRgb(colorFrom)
        : colorFrom;
    const toRgb = isHex(colorTo)
      ? hexToRgb(colorTo)
      : isRgb(colorTo)
        ? parseRgb(colorTo)
        : colorTo;

    const segments = [];

    for (let i = 0; i < NORMALIZED_RATING_NPS_COUNT; i++) {
      const factor = i / (NORMALIZED_RATING_NPS_COUNT - 1);
      const r = Math.round(interpolate(fromRgb.r, toRgb.r, factor));
      const g = Math.round(interpolate(fromRgb.g, toRgb.g, factor));
      const b = Math.round(interpolate(fromRgb.b, toRgb.b, factor));
      segments.push(rgbToHex({ r, g, b }));
    }

    return segments;
  };

  const gradientSegments = splitGradient(startColor, endColor);
  const showGradientSegments = isColored && startColor && endColor;
  const classes = van.derive(
    () =>
      `${styles["fc-widget-rating-nps"]} ${
        isDisabled?.val ? styles["fc-widget-rating-nps--disabled"] : ""
      } ${isColored ? styles["fc-widget-rating-nps--colored"] : "fc-widget-rating-nps--black-and-white"} 
      ${inited.val ? styles["fc-widget-rating-nps--inited"] : ""} ${view === "large" ? styles["fc-widget-rating-nps--view-large"] : ""}`,
  );

  return div(
    { class: styles["fc-widget-rating-nps-container"] },
    div(
      {
        class: () => classes.val,
        "data-testid": "rating-nps",
      },
      div(
        { class: styles["fc-widget-rating-nps__list"] },
        items.map((item, index) =>
          div(
            {
              class: () => {
                return `${styles["fc-widget-rating-nps__item"]} ${
                  item === currentValue.val ? styles["active"] : ""
                }`;
              },
              "data-testid": "rating-nps-item",
              style: `background-color: ${showGradientSegments ? gradientSegments[index] : ""}`,
              onclick: () => handleClick(item),
            },
            span(item),
          ),
        ),
      ),
      startLabel || endLabel
        ? div(
            { class: styles["fc-widget-rating-nps__labels"] },
            startLabel
              ? div(
                  { class: styles["fc-widget-rating-nps__label-first"] },
                  startLabel,
                )
              : null,
            endLabel
              ? div(
                  { class: styles["fc-widget-rating-nps__label-last"] },
                  endLabel,
                )
              : null,
          )
        : null,
    ),
  );
}
