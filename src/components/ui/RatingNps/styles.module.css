.fc-widget-rating-nps {
  max-width: 580px;
  margin: 0 auto;
  width: 100%;
  --labels-size: 14px;
  --item-size: 13px;
}

.fc-widget-rating-nps .fc-widget-rating-nps__list {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: opacity 0.3s;
}

.fc-widget-rating-nps--disabled .fc-widget-rating-nps__list {
  opacity: 0.4;
}

.fc-widget-rating-nps__item {
  position: relative;
  flex-grow: 1;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f1f5f6;
  color: black;
  font-size: var(--item-size);
  font-weight: 700;
  padding: 0 4px;
  border-radius: 4px;
  cursor: pointer;
  flex: 1 1 0;
  transition: opacity 0.3s;
}
.fc-widget-rating-nps__item:before,
.fc-widget-rating-nps__item-bg {
  content: "";
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 4px;
  background: inherit;
  border: 1px solid #cfd8dc;
  z-index: 1;
  transition: transform 0.3s;
}
.fc-widget-rating-nps__item span {
  z-index: 2;
}
.fc-widget-rating-nps__item:only-child {
  width: 42px;
}
.fc-widget-rating-nps--colored .fc-widget-rating-nps__item {
  color: white;
}
.fc-widget-rating-nps--colored .fc-widget-rating-nps__item:before,
.fc-widget-rating-nps--colored .fc-widget-rating-nps__item-bg {
  border: none;
}
.fc-widget-rating-nps--inited .fc-widget-rating-nps__item:not(.active) {
  opacity: 0.4;
}
.fc-widget-rating-nps--inited .fc-widget-rating-nps__item.active:before,
.fc-widget-rating-nps--inited
  .fc-widget-rating-nps__item.active
  .fc-widget-rating-nps__item-bg {
  transform: scaleY(1.58) scaleX(1.05);
}
.fc-widget-rating-nps__labels {
  margin-top: 15px;
  opacity: 0.4;
  display: flex;
  justify-content: space-between;
  font-size: var(--labels-size);
}

.fc-widget-rating-nps__labels > div {
  max-width: 135px;
  line-height: 1.1;
  overflow-x: hidden;
}

.fc-widget-rating-nps__label-first {
  text-align: left;
  margin-right: auto;
}

.fc-widget-rating-nps__label-last {
  text-align: right;
  margin-left: auto;
}

@media screen and (min-width: 768px) {
  .fc-widget-rating-nps--view-large .fc-widget-rating-nps__list {
    gap: 12px;
  }

  .fc-widget-rating-nps--view-large .fc-widget-rating-nps__item {
    font-size: var(--fqz-widget-rating-scale-font-size, var(--fqz-widget-text-font-size));
    line-height: 1.1;
  }

  .fc-widget-rating-nps--view-large.fc-widget-rating-nps--inited
    .fc-widget-rating-nps__item.fc-widget-rating-nps__item.active:before {
    transform: scaleY(1.225) scaleX(1.21);
  }

  .fc-widget-rating-nps--view-large .fc-widget-rating-nps__labels {
    font-size: var(--fqz-widget-text-font-size);
  }

  .fc-widget-rating-nps--view-large .fc-widget-rating-nps__label-first,
  .fc-widget-rating-nps--view-large .fc-widget-rating-nps__label-last {
    max-width: 280px;
  }
}