import van from "vanjs-core";
import styles from "./styles.module.css";
import Transition from "@components/ui/Transition";
import { TRANSITION_NAMES } from "@/constants";
import FormGroup from "@components/ui/form/FormGroup";
import FormTextarea from "@components/ui/form/FormTextarea";
import <PERSON><PERSON>heck from "@components/ui/form/Check";
import { toValue } from "@/helpers";
import QuestionAssessments from "@components/questions/QuestionAssessments";
const { div } = van.tags;

const QuestionHeader = ({
  description,
  descriptionStyle = "",
  subdescription,
  isUnrequired,
  unrequiredText,
}) => {
  // @TODO: Sanitize the subdescription text?
  const subdescriptionElement = toValue(subdescription)
    ? div({
        class: styles["fc-widget-q-subdescription"],
        innerHTML: () => decodeURIComponent(toValue(subdescription)),
      })
    : null;

  return div(
    { class: styles["fc-widget-q-header"] },
    div({
      class: styles["fc-widget-q-description"],
      style: descriptionStyle,
      innerHTML: () => decodeURIComponent(toValue(description)),
    }),
    subdescriptionElement,
    isUnrequired
      ? div({ class: styles["fc-widget-q-unrequired"] }, unrequiredText)
      : null,
  );
};

export default function QuestionItem({
  headerProps,
  questionId,
  questionElement,
  comment = {},
  skip = {},
  gap = 30,
  commentGap = 30,
  assessments,
  /**
   * @type {"default" | "large"}
   */
  view = "default",
  ...attrs
}) {
  const commentValueError = van.derive(() => {
    return toValue(comment?.errors?.val?.commentValue);
  });

  const assessmentsValueError = van.derive(() => {
    return toValue(assessments?.errors?.val?.assessments);
  });

  const selfVariantError = van.derive(() => {
    return toValue(assessments?.errors?.val.selfVariant);
  });

  const showCommentField = van.derive(() => {
    return comment.enabled && !comment.isSkipped?.val;
  });

  const classes = van.derive(() => {
    let cls = styles["fc-widget-q-item"];
    if (attrs["class"]) {
      cls += ` ${attrs["class"]}`;
    }

    if (view === "large") {
      cls += ` ${styles["fc-widget-q-item--view-large"]}`;
    }
    return cls;
  });

  const checkboxStyles =
    view === "large"
      ? "--text-font-size: var(--fqz-widget-text-font-size);"
      : "";

  const textareaStyles =
    view === "large" ? "--textarea-padding: 12px 15px;" : "";

  if (view === "large") {
    commentGap = 30;
  }

  return div(
    { class: () => classes.val, ...attrs },
    headerProps?.enabled ? QuestionHeader(headerProps) : null,
    questionElement,
    comment.enabled
      ? Transition({
          name: TRANSITION_NAMES.SLIDE,
          show: showCommentField,
          calculateHeight: true,
          children: () =>
            FormGroup({
              size: view === "large" ? "large" : "default",
              style: `padding-top: ${commentGap}px;`,
              "data-testid": "comment",
              label: {
                text: comment.label,
                for: `comment-${comment.questionId}`,
              },
              children: FormTextarea({
                value: comment.value,
                placeholder: comment.placeholder || "",
                invalid: commentValueError,
                name: `comment-${comment.questionId}`,
                maxlength: comment.valueMaxLength,
                style: textareaStyles,
                onChange: (value) => {
                  if (
                    comment.onCommentChange &&
                    typeof comment.onCommentChange === "function"
                  ) {
                    comment.onCommentChange(value, comment.questionId);
                  }
                },
              }),
              error: commentValueError,
            }),
        })
      : null,
    assessments?.enabled
      ? QuestionAssessments({
          view,
          type: assessments.type,
          items: assessments.variants,
          isVisible: assessments.visible,
          maxLength: assessments.maxLength,
          questionId,
          selfVariant: assessments.selfVariant,
          selfVariantError: selfVariantError,
          error: assessmentsValueError,
          value: assessments.value,
          text: assessments.text,
          onChange: assessments.onChange,
          attachments: assessments.attachments,
        })
      : null,
    skip.enabled
      ? FormGroup({
          style: `margin-top: ${gap}px;`,
          children: FormCheck({
            labelText: skip?.text || "Не готов(а) оценить",
            type: "checkbox",
            checked: false,
            vModel: skip.vModel,
            style: checkboxStyles,
            onChange: (value) => {
              if (
                skip.onSkipChange &&
                typeof skip.onSkipChange === "function"
              ) {
                skip.onSkipChange(value, comment.questionId);
              }
            },
          }),
        })
      : null,
  );
}
