.fc-widget-q-item {
}

.fc-widget-q-item--rating [data-fc-widget-component="comment"],
.fc-widget-q-item--rating-nps [data-fc-widget-component="comment"] {
  padding-top: 30px !important;
}

.fc-widget-q-header {
  margin-bottom: 20px;
}

.fc-widget-q-description {
  font-size: 21px;
  line-height: 1.2;
  overflow-wrap: break-word;
}

.fc-widget-q-subdescription {
  font-size: 14px;
  line-height: 1.2;
  margin-top: 10px;
  overflow-wrap: break-word;
}

.fc-widget-q-description * {
  padding: 0;
  margin: 0;
}

.fc-widget-q-subdescription p {
  margin: 0;
  font-size: inherit;
  line-height: inherit;
}

.fc-widget-q-unrequired {
  font-size: 12px;
  margin-top: 10px;
}

@media screen and (min-width: 768px) {
  .fc-widget-q-item--view-large .fc-widget-q-unrequired {
    font-size: 12px;
    line-height: 1.1;
    font-weight: 400;
    margin-top: 15px;
  }
  .fc-widget-q-item--view-large .fc-widget-q-header {
    margin-bottom: var(--fqz-widget-question-item-header-margin-bottom, 30px);
  }

  .fc-widget-q-item--view-large .fc-widget-q-description {
    font-size: var(--fqz-widget-title-font-size);
    font-weight: 600;
    line-height: 1.1;
  }

  .fc-widget-q-item--view-large .fc-widget-q-subdescription {
    font-size: 16px;
    font-weight: 400;
    margin-top: 15px;
    line-height: 1.1;
  }

  .fc-widget-q-item--view-large .fc-widget-q-unrequired {
    font-size: 12px;
    line-height: 1.1;
    font-weight: 400;
    margin-top: 15px;
  }
}
