import van from "vanjs-core";
import styles from "./styles.module.css";
import FormGroup from "@components/ui/form/FormGroup";
import FormInput from "@components/ui/form/FormInput";
import { translate } from "../../../helpers/translations";

const { div } = van.tags;

export default function QuestionFioFields({
  name,
  surname,
  patronym,
  isSkipped,
  errors,
  onNameChange,
  onSurnameChange,
  onPatronymicChange,
  nameInputAttrs,
  surnameInputAttrs,
  patronymInputAttrs,
  size = "default",
}) {
  const {
    visible: nameVisible,
    placeholderText: namePlaceholderText,
    maxLength: nameMaxLength,
    vModel: nameVModel,
    value: nameValue,
  } = name;

  const {
    visible: surnameVisible,
    placeholderText: surnamePlaceholderText,
    maxLength: surnameMaxLength,
    vModel: surnameVModel,
    value: surnameValue,
  } = surname;

  const {
    visible: patronymVisible,
    placeholderText: patronymPlaceholderText,
    maxLength: patronymMaxLength,
    vModel: patronymVModel,
    value: patronymValue,
  } = patronym;

  const nameError = van.derive(() => {
    return errors.val.name;
  });

  const surnameError = van.derive(() => {
    return errors.val.surname;
  });

  const patronymicError = van.derive(() => {
    return errors.val.patronymic;
  });

  return div(
    { class: styles["fc-widget-q-fio-fields"] },
    surnameVisible
      ? FormGroup({
          label: { text: surnameValue || translate("Фамилия") },
          size,
          children: FormInput({
            vModel: surnameVModel,
            invalid: surnameError,
            placeholder: surnamePlaceholderText,
            maxLength: surnameMaxLength,
            isDisabled: isSkipped,
            onChange: onSurnameChange,
            ...surnameInputAttrs,
            size,
          }),
          error: surnameError,
        })
      : null,
    nameVisible
      ? FormGroup({
          label: { text: nameValue || translate("Имя") },
          size,
          children: FormInput({
            vModel: nameVModel,
            invalid: nameError,
            placeholder: namePlaceholderText,
            maxLength: nameMaxLength,
            isDisabled: isSkipped,
            onChange: onNameChange,
            ...nameInputAttrs,
            size,
          }),
          error: nameError,
        })
      : null,
    patronymVisible
      ? FormGroup({
          label: { text: patronymValue || translate("Отчество") },
          size,
          children: FormInput({
            vModel: patronymVModel,
            invalid: patronymicError,
            placeholder: patronymPlaceholderText,
            maxLength: patronymMaxLength,
            isDisabled: isSkipped,
            onChange: onPatronymicChange,
            ...patronymInputAttrs,
            size,
          }),
          error: patronymicError,
        })
      : null,
  );
}
