import van from "vanjs-core";
import FormGroup from "@components/ui/form/FormGroup";
import FormTextarea from "@components/ui/form/FormTextarea";
import {
  MASK_TYPES,
  TEXT_VARIANT_TYPES,
} from "@/helpers/question-helpers/questionTypes";
import FormInput from "@components/ui/form/FormInput";
import FormMaskedInput from "@components/ui/form/FormMaskedInput";
import QuestionFioFields from "./QuestionFioFields";

const { div } = van.tags;

export default function QuestionText({
  questionId,
  variantType,
  maskType,
  placeholder,
  vModel,
  maxLength,
  isSkipped,
  errors,
  onChange,
  view = "default",
  fio,
}) {
  const textareaRef = van.state(null);
  const inputRef = van.state(null);
  const valueError = van.derive(() => {
    return errors.val.value;
  });

  const isTextareaVariant = variantType === TEXT_VARIANT_TYPES.TEXTAREA;
  const isInputVariant = variantType === TEXT_VARIANT_TYPES.INPUT;
  const hasNoMask = maskType === MASK_TYPES.NONE;
  const isPhoneMask = maskType === MASK_TYPES.PHONE;
  const isEmailMask = maskType === MASK_TYPES.EMAIL;
  const isNumberMask = maskType === MASK_TYPES.NUMBER;
  const isWebsiteMask = maskType === MASK_TYPES.WEBSITE;
  const isFioMask = maskType === MASK_TYPES.FIO;

  const textareaHeight = view === "large" ? 70 : 75;

  const textareaStyle =
    view === "large"
      ? "--textarea-padding: 12px 15px; --textarea-font-size: 16px;"
      : "";

  return div(
    isTextareaVariant
      ? FormGroup({
          children: () =>
            FormTextarea({
              ref: textareaRef,
              placeholder,
              value: vModel,
              maxlength: maxLength,
              invalid: valueError,
              height: textareaHeight,
              "data-testid": "textarea",
              style: textareaStyle,
              onChange: (value) => {
                if (onChange && typeof onChange === "function") {
                  onChange(value, questionId);
                }
              },
            }),
          error: valueError,
        })
      : null,
    isInputVariant && hasNoMask
      ? FormGroup({
          children: FormInput({
            ref: inputRef,
            placeholder,
            vModel,
            maxlength: maxLength,
            invalid: valueError,
            height: 75,
            size: view === "large" ? "large" : "default",
            "data-testid": "input",
            onChange: (value) => {
              if (onChange && typeof onChange === "function") {
                onChange(value, questionId);
              }
            },
          }),
          error: valueError,
        })
      : null,
    isInputVariant && isPhoneMask
      ? FormGroup({
          fullWidth: view !== "large",
          size: view === "large" ? "large" : "default",
          children: FormMaskedInput({
            placeholder,
            vModel,
            fullWidth: view !== "large",
            maxlength: maxLength,
            invalid: valueError,
            maskType: "phone",
            inputmode: "tel",
            "data-testid": "input-masked",
            size: view === "large" ? "large" : "default",
            onChange,
          }),
          error: valueError,
        })
      : null,
    isInputVariant && isEmailMask
      ? FormGroup({
          fullWidth: view !== "large",
          size: view === "large" ? "large" : "default",
          children: FormInput({
            placeholder,
            vModel,
            maxlength: maxLength,
            invalid: valueError,
            inputmode: "email",
            size: view === "large" ? "large" : "default",
            "data-testid": "input-masked",
            onChange,
          }),
          error: valueError,
        })
      : null,
    isInputVariant && isNumberMask
      ? FormGroup({
          fullWidth: view !== "large",
          size: view === "large" ? "large" : "default",
          children: FormMaskedInput({
            placeholder,
            vModel,
            maxlength: maxLength,
            invalid: valueError,
            maskType: "number",
            inputmode: "numeric",
            size: view === "large" ? "large" : "default",
            "data-testid": "input-masked",
            onChange,
          }),
          error: valueError,
        })
      : null,
    isInputVariant && isWebsiteMask
      ? FormGroup({
          children: FormInput({
            placeholder,
            vModel,
            maxlength: maxLength,
            invalid: valueError,
            inputmode: "url",
            size: view === "large" ? "large" : "default",
            "data-testid": "input-masked",
            onChange,
          }),
          error: valueError,
        })
      : null,
    isInputVariant && isFioMask
      ? QuestionFioFields({
          name: fio.name,
          surname: fio.surname,
          patronym: fio.patronymic,
          errors,
          isSkipped,
          size: view === "large" ? "large" : "default",
          nameInputAttrs: {
            "data-testid": "input-name",
          },
          surnameInputAttrs: {
            "data-testid": "input-surname",
          },
          patronymInputAttrs: {
            "data-testid": "input-patronym",
          },
          onNameChange: (value) => {
            if (onChange && typeof onChange === "function") {
              onChange(value, questionId);
            }
          },
          onSurnameChange: (value) => {
            if (onChange && typeof onChange === "function") {
              onChange(value, questionId);
            }
          },
          onPatronymicChange: (value) => {
            if (onChange && typeof onChange === "function") {
              onChange(value, questionId);
            }
          },
        })
      : null,
  );
}
