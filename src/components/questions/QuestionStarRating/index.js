import van from "vanjs-core";
import { TRANSITION_NAMES } from "@/constants";
import { toValue } from "@/helpers";
import StarRating from "@components/ui/StarRating";
import Transition from "@components/ui/Transition";
import FormError from "@components/ui/form/FormError";

const { div } = van.tags;

export default function QuestionStarRating({
  count,
  color,
  size,
  thin,
  labelsArray,
  alwaysShowLabels,
  questionId,
  onChange,
  vModel,
  errors,
  /*
   * View mode
   * default - default view
   * large - large view
   **/
  view = "default",
  interactive = true,
  isSkipped,
  isUnrequired,
  showNumbers,
}) {
  const onStarRatingChange = (value) => {
    if (onChange && typeof onChange === "function") {
      onChange(value, questionId);
    }
  };

  const labelTransitionName = isUnrequired
    ? TRANSITION_NAMES.FADE
    : TRANSITION_NAMES.FADE_IN;

  const valueError = van.derive(() => {
    return toValue(errors.val.value);
  });

  return div(
    StarRating({
      max: count,
      size,
      thin,
      color,
      vModel,
      labels: labelsArray,
      alwaysShowLabels,
      labelTransitionName,
      isDisabled: isSkipped,
      error: valueError,
      interactive,
      showNumbers,
      view,
      onChange: onStarRatingChange,
    }),
    !isUnrequired
      ? Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: valueError,
          children: () =>
            div(
              { style: "margin-top: 20px" },
              FormError({ error: valueError }),
            ),
        })
      : null,
  );
}
