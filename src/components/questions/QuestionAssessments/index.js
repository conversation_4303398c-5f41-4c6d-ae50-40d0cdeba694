import van from "vanjs-core";
import { ASSESMENT_VARIANT_TYPES } from "../../../helpers/question-helpers/questionTypes";
import styles from "./styles.module.css";
import CheckGroup from "@components/ui/form/CheckGroup";
import FormGroup from "@components/ui/form/FormGroup";
import FormTextarea from "@components/ui/form/FormTextarea";
import Transition from "../../ui/Transition";
import { TRANSITION_NAMES } from "../../../constants";
import FormError from "@components/ui/form/FormError";
import QuestionAssessmentAttachments from "@components/ui/QuestionAssessmentAttachments";

const { div } = van.tags;

export default function QuestionAssessments({
  questionId,
  items = [],
  selfVariant,
  type = ASSESMENT_VARIANT_TYPES.SINGLE,
  value,
  error,
  maxLength,
  selfVariantError,
  isVisible,
  text = "Что вам не понравилось?",
  view = "default",
  onChange,
  attachments,
}) {
  // Transform assessments.variants to the format expected by FormCheckGroup
  const normalizedItems = items.map((variant) => ({
    labelText: variant.variant,
    value: variant.id,
    checked: variant.isChecked,
    name: `assessment-${questionId}`,
  }));

  if (selfVariant.enabled) {
    normalizedItems.push({
      labelText: selfVariant.text,
      value: `is_self_answer`,
      checked: selfVariant.isChecked,
      name: `assessment-${questionId}`,
    });
  }

  const selfVariantChecked = van.derive(() => {
    if (Array.isArray(value.val)) {
      const selfVariantZeroValue = 0;
      const selfVariantValue = "is_self_answer";
      const hasSelfVariantChecked =
        value.val.includes(selfVariantZeroValue) ||
        value.val.includes(selfVariantValue);
      return hasSelfVariantChecked;
    } else {
      return value.val === 0 || value.val === "is_self_answer";
    }
  });

  const selfVariantTextareaRef = van.state(null);

  const isAssessmentVariantsSingle = type === ASSESMENT_VARIANT_TYPES.SINGLE;

  const isAssessmentVariantsMultiple =
    type === ASSESMENT_VARIANT_TYPES.MULTIPLE;

  const isAssessmentVariantsText = type === ASSESMENT_VARIANT_TYPES.TEXT;

  const assesmentVariantsCheckType = isAssessmentVariantsMultiple
    ? "checkbox"
    : "radio";

  const isAssessmentVariantsOptions =
    isAssessmentVariantsMultiple || isAssessmentVariantsSingle;

  const labelStyle =
    view === "large" ? "font-size: var(--fqz-widget-text-font-size);" : "";

  const checkboxStyle =
    view === "large"
      ? "--text-font-size: var(--fqz-widget-text-font-size);"
      : "";

  const textareaStyle =
    view === "large"
      ? "--textarea-padding: 12px 15px; --textarea-font-size: 16px;"
      : "";

  const containerClasses = [styles["fc-q-assessments"]];

  if (view === "large") {
    containerClasses.push(styles["fc-q-assessments--view-large"]);
  }

  return Transition({
    show: isVisible,
    calculateHeight: true,
    name: TRANSITION_NAMES.SLIDE,
    children: () =>
      div(
        { class: containerClasses.join(" ") },
        div(
          {
            class: styles["fc-q-assessments-label"],
            "data-testid": "assessments-label",
            style: labelStyle,
          },
          text,
        ),
        isAssessmentVariantsOptions
          ? div(
              CheckGroup({
                items: normalizedItems,
                vModel: value,
                type: assesmentVariantsCheckType,
                onChange,
                checkAttrs: {
                  "data-testid": "assessments-check",
                  style: checkboxStyle,
                },
              }),
              Transition({
                show: error,
                name: TRANSITION_NAMES.FADE,
                style: `padding-top: 15px;`,
                children: () => FormError({ error }),
              }),
            )
          : null,
        isAssessmentVariantsText
          ? FormGroup({
              children: FormTextarea({
                value: value,
                invalid: error,
                placeholder: selfVariant?.placeholder,
                maxlength: maxLength,
                style: textareaStyle,
                height: 70,
                name: `assessment-${questionId}`,
                "data-testid": "assessments-text",
                onChange: (value) => {
                  if (onChange && typeof onChange === "function") {
                    onChange(value, questionId);
                  }
                },
              }),
              error,
            })
          : null,
        selfVariant.enabled
          ? Transition({
              name: TRANSITION_NAMES.SLIDE,
              show: selfVariantChecked,
              calculateHeight: true,
              children: () =>
                FormGroup({
                  style: `padding-top: 15px;`,
                  children: FormTextarea({
                    value: selfVariant.value,
                    placeholder: selfVariant.placeholder,
                    invalid: selfVariantError,
                    name: `self-variant-${questionId}`,
                    "data-testid": "assessments-self-variant",
                    height: 75,
                    autofocus: true,
                    autofocusDelay: 610,
                    scrollIntoView: true,
                    scrollIntoViewDelay: 302,
                    ref: selfVariantTextareaRef,
                    maxlength: selfVariant.fieldLength.max,
                    onChange: (value) => {
                      if (
                        selfVariant.onChange &&
                        typeof selfVariant.onChange === "function"
                      ) {
                        selfVariant.onChange(value, questionId);
                      }
                    },
                  }),
                  error: selfVariantError,
                }),
            })
          : null,
        attachments?.enabled
          ? QuestionAssessmentAttachments({
              attachments: attachments.attachments,
              error: attachments.error,
              errorKind: attachments.errorKind,
              isScreenshotLoading: attachments.handler?.isScreenshotLoading,
              shouldShowScreenshotButton: attachments.showScreenshotButton,
              buttonText: attachments.buttonText,
              screenshotButtonText: attachments.screenshotButtonText,
              description: attachments.description,
              maxFiles: attachments.maxFiles,
              uploadEnabled: attachments.showUploadButton,
              view,
              onFileUpload: (files) => {
                if (attachments.handler) {
                  attachments.handler.uploadFiles(files);
                }
              },
              onScreenshotRequest: async () => {
                if (attachments.handler) {
                  await attachments.handler.captureScreenshot();
                }
              },
              onRemove: (index) => {
                if (attachments.handler) {
                  attachments.handler.removeAttachment(index);
                }
              },
            })
          : null,
      ),
  });
}
