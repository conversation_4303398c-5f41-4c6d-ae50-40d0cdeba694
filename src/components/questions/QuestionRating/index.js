import van from "vanjs-core";
import { TRANSITION_NAMES } from "@/constants";
import { toValue } from "@/helpers";
import Transition from "@components/ui/Transition";
import FormError from "@components/ui/form/FormError";
import RatingScale from "@components/ui/RatingScale";

const { div } = van.tags;

export default function QuestionRating({
  count,
  color,
  labelsArray,
  questionId,
  onChange,
  vModel,
  errors,
  isSkipped,
  isUnrequired,
  /**
   * @type {"default" | "large"}
   */
  view = "default",
}) {
  const onRatingChange = (value) => {
    if (onChange && typeof onChange === "function") {
      onChange(value, questionId);
    }
  };

  const labelTransitionName = isUnrequired
    ? TRANSITION_NAMES.FADE
    : TRANSITION_NAMES.FADE_IN;

  const valueError = van.derive(() => {
    return toValue(errors.val.value);
  });

  return div(
    RatingScale({
      count,
      color,
      vModel,
      labels: labelsArray,
      labelTransitionName,
      isDisabled: isSkipped,
      valueError,
      view,
      onChange: onRatingChange,
    }),
    !isUnrequired
      ? Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: valueError,
          children: () =>
            div(
              { style: "margin-top: 20px" },
              FormError({ error: valueError }),
            ),
        })
      : null,
  );
}
