.intermediate-block {
  text-align: center;
  font-family: var(--fqz-widget-font-family);
  color: var(--fqz-widget-text-on-place);
  gap: 20px;
  display: flex;
  flex-direction: column;
}

.intermediate-block--start,
.intermediate-block--end {
  min-height: 170px;
  justify-content: center;
}

.intermediate-block--intermediate {
  min-height: 104px;
}

.intermediate-block--start > button,
.intermediate-block--end > button,
.intermediate-block--start > a,
.intermediate-block--end > a {
  flex-grow: 0;
  flex-shrink: 0;
  width: 100%;
}

.intermediate-block--end-default {
  min-height: 170px;
  padding: 0px;
  align-items: center;
  justify-content: center;
}

.intermediate-block--without-content {
  margin-bottom: -30px;
}

.intermediate-block-content {
  /* Reset for all elements */
  font-family: var(--fqz-widget-font-family);
  color: var(--fqz-widget-text-on-place);
  width: 100%;
  line-height: 1.2;
  font-size: 14px;
}

.intermediate-block-content * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  text-align: left;
}

.intermediate-block-content p,
.intermediate-block-content div,
.intermediate-block-content span {
  margin-bottom: 0;
}

.intermediate-block-content h1,
.intermediate-block-content h2,
.intermediate-block-content h3,
.intermediate-block-content h4,
.intermediate-block-content h5,
.intermediate-block-content h6 {
  font-weight: bold;
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 0;
}

.intermediate-block-content h1 {
  font-size: 2em;
}
.intermediate-block-content h2 {
  font-size: 1.5em;
}
.intermediate-block-content h3 {
  font-size: 1.17em;
}
.intermediate-block-content h4 {
  font-size: 1em;
}
.intermediate-block-content h5 {
  font-size: 0.83em;
}
.intermediate-block-content h6 {
  font-size: 0.67em;
}

.intermediate-block-content ul,
.intermediate-block-content ol {
  margin-left: 1.5em;
  margin-bottom: 0;
}

.intermediate-block-content ul {
  list-style-type: disc;
}

.intermediate-block-content ol {
  list-style-type: decimal;
  list-style-position: inside;
}

.intermediate-block-content li {
  margin-bottom: 0;
  display: list-item;
}

.intermediate-block-content a {
  color: var(--fqz-widget-link-color);
  text-decoration: underline;
}

.intermediate-block-content a:visited {
  color: var(--fqz-widget-link-color);
}

.intermediate-block-content a:hover,
.intermediate-block-content a:focus {
  text-decoration: none;
}

.intermediate-block-content blockquote {
  margin: 0 40px;
  padding-left: 1em;
  border-left: 3px solid #ccc;
  font-style: italic;
}

.intermediate-block-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f5f5;
  padding: 1em;
  margin-bottom: 0;
  overflow: auto;
}

.intermediate-block-content strong,
.intermediate-block-content b {
  font-weight: bold;
}

.intermediate-block-content em,
.intermediate-block-content i {
  font-style: italic;
}

.intermediate-block-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 0;
}

.intermediate-block-content th,
.intermediate-block-content td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.intermediate-block-content th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.intermediate-block-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 0;
}

.intermediate-block-images {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  width: 100%;
}

.intermediate-block-image-wrapper {
  display: inline-flex;
  overflow: hidden;
  border-radius: 8px;
  align-self: center;
  width: var(--img-width);
  padding-bottom: var(--img-pb);
  position: relative;
  height: auto;
}

.intermediate-block-image-wrapper img {
  display: block;
  transition: transform 0.3s ease;
  max-width: 100%;
  max-height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.intermediate-block-image-wrapper:hover img {
  transform: scale(1.05);
}

.check-agreement-container {
  width: 100%;
}
.check-agreement-container > label {
  align-items: center;
}

.check-agreement-text {
  line-height: 1.2 !important;
  margin-top: 0 !important;
}

.check-agreement-text-html a {
  text-decoration: underline;
  color: var(--fqz-widget-text-on-place) !important;
}

.check-agreement-text-html p {
  margin: 0 !important;
}

.check-agreement-text-html span {
  margin: 0 !important;
  line-height: 1.1 !important;
  display: inline-block;
}

.intermediate-block-buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 15px;
}

.intermediate-block-end-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 15px;
}

.intermediate-block-end-buttons > button,
.intermediate-block-end-buttons > a {
  width: 100%;
}

@media screen and (min-width: 768px) {
  .intermediate-block--view-large {
    gap: 30px;
  }
  .intermediate-block--view-large > button,
  .intermediate-block--view-large > a {
    align-self: center;
  }
  .intermediate-block--view-large .intermediate-block-content {
    font-size: var(--fqz-widget-text-font-size);
  }

  .intermediate-block--view-large .intermediate-block-end-buttons {
    flex-direction: row;
    gap: 10px;
  }
}
