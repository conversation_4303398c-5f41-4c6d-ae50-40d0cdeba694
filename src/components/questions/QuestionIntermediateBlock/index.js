import van from "vanjs-core";
import styles from "./styles.module.css";
import widgetFormStyles from "@/components/questions/QuestionsForm/styles.module.css";
import FormCheck from "@/components/ui/form/Check";
import { INTERMEDIATE_BLOCK_TYPES } from "../../../helpers/question-helpers/questionTypes";
import Button from "@components/ui/Button";
import { toValue } from "../../../helpers";

const { div, img, a, span } = van.tags;

const getButtonStyleString = (baseStyle) => `
--button-background-color: ${baseStyle.backgroundColor};
--button-text-color: ${baseStyle.color};
--button-border-color: ${baseStyle.borderColor};
--button-border-radius: ${baseStyle.borderRadius}px;
`;

export default function QuestionIntermediateBlock({
  type,
  content,
  vModelAgreement,
  agreement,
  agreementText,
  onAgreementChange,
  images,
  imagesPlaceBg,
  assetsRoot,
  startButtonText,
  readyButtonEnabled,
  readyButtonExternalLink,
  readyButtonText,
  closeWidgetButtonEnabled,
  closeWidgetButtonText,
  onCloseWidgetButtonClick = () => {},
  onStartButtonClick,
  canGoToNextStep,
  startButtonStyle,
  hasScrollVisible,
  view = "default",
  id,
}) {
  const assetsRootWithoutSlash = assetsRoot.replace(/\/$/, "");

  const checkboxStyles =
    view === "large"
      ? "--text-font-size: var(--fqz-widget-text-font-size);"
      : "";

  const renderStartButton = () => {
    return Button({
      style: () =>
        `display: ${hasScrollVisible.val || !isStartScreen ? "none" : "block"}; ${getButtonStyleString(startButtonStyle)}`,
      type: "button",
      children: startButtonText || "Пройти опрос",
      disabled: () => !canGoToNextStep.val,
      class:
        widgetFormStyles["fc-widget-q-form-nav-button"] +
        " " +
        widgetFormStyles["fc-widget-q-form-nav-button--start"],
      onClick: (e) => {
        e.preventDefault();
        onStartButtonClick?.();
      },
    });
  };

  const contentElem = div({
    class: styles["intermediate-block-content"],
    "data-testid": "intermediate-block-content",
    innerHTML: () => toValue(content),
  });

  const sortedImages = images.sort((a, b) => a.position - b.position);

  const htmlRegex = /<[a-z][\s\S]*>/i;
  const agreementTextContainsHtml =
    agreementText && htmlRegex.test(agreementText);

  const renderImages = () => {
    return div(
      {
        class: styles["intermediate-block-images"],
        "data-testid": "intermediate-block-images",
      },
      sortedImages.map((image) => {
        const imageSrc = image.logo
          ? `${assetsRootWithoutSlash}${image.logo}`
          : image.external_logo;
        const imgElement = img({
          src: imageSrc,
          height: () => toValue(image.height) || "auto",
          width: () => toValue(image.width) || "auto",
          alt: () => decodeURIComponent(toValue(image.description)) || "",
        });

        const aspectRatio = van.derive(() => {
          if (!toValue(image.height) || !toValue(image.width)) {
            return 1;
          }
          return toValue(image.height) / toValue(image.width);
        });

        // @TODO: aspect ratio bad browser support
        const wrapperStyle = van.derive(() => {
          return `background-color: ${imagesPlaceBg || "transparent"}; max-width: 100%; --img-width: ${toValue(image.width) || 0}px; --img-height: ${toValue(image.height) || 0}px; aspect-ratio: 1 / ${toValue(aspectRatio)};`;
        });

        const wrapperClasses = van.derive(() => {
          const classes = [styles["intermediate-block-image-wrapper"]];
          if (!toValue(image.height) || !toValue(image.width)) {
            classes.push(styles["intermediate-block-image-wrapper--no-size"]);
          }
          return classes.join(" ");
        });

        return toValue(image.link)
          ? a(
              {
                href: () => decodeURIComponent(toValue(image.link)),
                target: "_blank",
                rel: "noopener noreferrer",
                class: () => wrapperClasses.val,
                style: () => wrapperStyle.val,
              },
              imgElement,
            )
          : div(
              {
                class: () => wrapperClasses.val,
                style: () => wrapperStyle.val,
              },
              imgElement,
            );
      }),
    );
  };

  const isStartScreen = type === INTERMEDIATE_BLOCK_TYPES.START;
  const isEndScreen = type === INTERMEDIATE_BLOCK_TYPES.END;
  const isIntermediateScreen = type === INTERMEDIATE_BLOCK_TYPES.INTERMEDIATE;

  const intermediateBlockClasses = [styles["intermediate-block"]];
  if (isStartScreen) {
    intermediateBlockClasses.push(styles["intermediate-block--start"]);
  }

  if (isEndScreen) {
    intermediateBlockClasses.push(styles["intermediate-block--end"]);
  }

  if (isIntermediateScreen) {
    intermediateBlockClasses.push(styles["intermediate-block--intermediate"]);
  }

  if (id === "end") {
    intermediateBlockClasses.push(styles["intermediate-block--end-default"]);
  }

  if (view === "large") {
    intermediateBlockClasses.push(styles["intermediate-block--view-large"]);
  }

  const renderReadyButton = () => {
    return Button({
      style: () =>
        `display: ${!toValue(readyButtonEnabled) || hasScrollVisible.val || !isEndScreen ? "none" : "inline-flex"}; ${getButtonStyleString(startButtonStyle)}}`,
      href: readyButtonExternalLink,
      target: "_blank",
      rel: "noopener noreferrer",
      class:
        widgetFormStyles["fc-widget-q-form-nav-button"] +
        " " +
        widgetFormStyles["fc-widget-q-form-nav-button--ready"],
      children: () => toValue(readyButtonText),
    });
  };

  const renderCloseWidgetButton = () => {
    return Button({
      style: () =>
        `display: ${!toValue(closeWidgetButtonEnabled) || hasScrollVisible.val || !isEndScreen ? "none" : "inline-flex"}; ${getButtonStyleString(startButtonStyle)}}`,
      class:
        widgetFormStyles["fc-widget-q-form-nav-button"] +
        " " +
        widgetFormStyles["fc-widget-q-form-nav-button--close-widget"],
      children: () => toValue(closeWidgetButtonText),
      onClick: onCloseWidgetButtonClick,
    });
  };

  const agreementTextNode = span({
    innerHTML: agreementText,
    onclick: (e) => {
      e.stopPropagation();
      e.stopImmediatePropagation();
    },
  });

  const agreementTextOrNode = agreementTextContainsHtml
    ? agreementTextNode
    : agreementText || "Я согласен/согласна на обработку персональных данных";

  const shouldRenderEndButtons =
    toValue(closeWidgetButtonEnabled) || toValue(readyButtonEnabled);

  return div(
    {
      "data-testid": "question-intermediate-block",
      class: intermediateBlockClasses.join(" "),
    },
    // @TODO: sanitize
    toValue(content) ? contentElem : null,
    agreement === 1
      ? FormCheck({
          type: "checkbox",
          labelText: agreementTextOrNode,
          vModel: vModelAgreement,
          style: checkboxStyles,
          onChange: onAgreementChange,
          classes: {
            "check-container": styles["check-agreement-container"],
            "check-text":
              styles["check-agreement-text"] +
              " " +
              styles["check-agreement-text-html"],
          },
        })
      : null,
    images && images.length > 0 ? renderImages() : null,
    renderStartButton(),
    shouldRenderEndButtons
      ? div(
          {
            class: styles["intermediate-block-end-buttons"],
          },
          renderReadyButton(),
          toValue(closeWidgetButtonEnabled) ? renderCloseWidgetButton() : null,
        )
      : null,
  );
}
