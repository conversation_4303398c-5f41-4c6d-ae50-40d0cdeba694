import van from "vanjs-core";
import { TRANSITION_NAMES } from "../../../constants";
import { normalizeUrl, toValue } from "../../../helpers";
import ImgRating from "../../ui/ImgRating";
import Transition from "../../ui/Transition";
import FormError from "../../ui/form/FormError";

const { div } = van.tags;

export default function QuestionSmileRating({
  questionId,
  alwaysShowLabels,
  smiles,
  count,
  smileType,
  vModel,
  showLabels,
  isUnrequired,
  isSkipped,
  errors,
  onChange,
  assetsRoot,
  hasScrollVisible,
  /**
   * @type {"default" | "large"}
   */
  view = "default",
}) {
  const onRatingChange = (value) => {
    if (onChange && typeof onChange === "function") {
      onChange(value, questionId);
    }
  };

  const labelTransitionName = isUnrequired
    ? TRANSITION_NAMES.FADE
    : TRANSITION_NAMES.FADE_IN;

  const valueError = van.derive(() => {
    return toValue(errors.val.value);
  });

  const smilesWithFullUrlls = smiles.map((smile) => ({
    ...smile,
    url: normalizeUrl(`${assetsRoot}${smile.smile_url}`),
  }));

  return div(
    ImgRating({
      count,
      type: smileType,
      icons: smilesWithFullUrlls,
      vModel,
      showLabels,
      alwaysShowLabels,
      labelTransitionName,
      isDisabled: isSkipped,
      view,
      hasScrollVisible,
      onChange: onRatingChange,
    }),
    !isUnrequired
      ? Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: valueError,
          children: () =>
            div(
              { style: "margin-top: 20px" },
              FormError({ error: valueError }),
            ),
        })
      : null,
  );
}
