.fc-widget-finish-screen-wrapper {
    position: relative;
    z-index: 2;
}
.fc-widget-finish-screen {
    width: 100%;
    height: 170px;
    left: 0;
    top: 0;
    padding: 0 20px;
    box-sizing: border-box;
    text-align: center;
    font-family: var(--fqz-widget-font-family);
    z-index: 4;
    position: relative;
}

.fc-widget-finish-screen-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--fqz-widget-text-on-place);
}

.fc-widget-finish-screen-title {
    font-weight: 700;
    font-size: 19px;
    margin: 10px 0 0 0;
    line-height: 1.1;
}

.fc-widget-finish-screen-text {
    font-size: 14px;
    line-height: 1.3;
    font-weight: 400;
    padding-left: 10px;
    padding-right: 10px;
}