.fc-widget-q-variants-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
}

.fc-widget-q-variants-item {
    width: 100%;
}

.fc-widget-q-variants-min-choose-error-container {
    margin-top: 20px;
    text-align: left;
}

@media screen and (min-width: 768px) {
    .fc-widget-q-variants-list--view-large {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 15px 10px;
    }

    .fc-widget-q-variants-list--view-large .fc-widget-q-variants-item {
        width: calc(50% - 5px);
    }

    .fc-widget-q-variants-list--view-large .fc-widget-q-variants-item--self-variant {
        width: 100%;
        margin-top: 15px;
    }

    .fc-widget-q-variants-list--view-large .fc-widget-q-variants-item--self-variant-file {
        margin-top: 0;
    }
}