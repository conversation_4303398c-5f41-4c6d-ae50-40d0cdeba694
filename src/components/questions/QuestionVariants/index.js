import van from "vanjs-core";
import { VARIANT_TYPES } from "@/helpers/question-helpers/questionTypes";
import styles from "./styles.module.css";
import FormCheckGroup from "@components/ui/form/CheckGroup";
import FormGroup from "@components/ui/form/FormGroup";
import Transition from "../../ui/Transition";
import { TRANSITION_NAMES } from "../../../constants";
import FormTextarea from "../../ui/form/FormTextarea";
import FormFileCheck from "../../ui/form/FileCheck";
import FormCheck from "../../ui/form/Check";
import FormError from "../../ui/form/FormError";
import shuffle from "lodash.shuffle";

const { div } = van.tags;

export default function QuestionVariants({
  type = VARIANT_TYPES.SINGLE,
  vModel,
  variants = [],
  variantsWithFiles = 0,
  selfVariant,
  maxChooseVariants = 0,
  randomVariantsOrder = 0,
  isSkipped,
  assetsRoot,
  errors,
  removeOthersItemId,
  view = "default",
  onChange,
}) {
  const checkboxType = type === VARIANT_TYPES.MULTIPLE ? "checkbox" : "radio";

  const isVariantDisabled = (variant) => {
    if (checkboxType === "radio") {
      return false;
    }

    const isSelected = vModel.val?.includes?.(variant.id);

    return (
      !isSelected &&
      maxChooseVariants &&
      vModel.val?.length >= maxChooseVariants
    );
  };

  const isVariantInactive = (variant) => {
    if (checkboxType === "radio") {
      return false;
    }
    const isRemoveOthersSelected = removeOthersItemId.val !== null;
    return (
      isSkipped.val ||
      (isRemoveOthersSelected && variant.id !== removeOthersItemId.val)
    );
  };

  let normalizedVariants = variants.map((variant) => {
    const isDisabled = van.derive(() => isVariantDisabled(variant));
    const isInactive = van.derive(() => isVariantInactive(variant));
    // remove starting slash from file_url and preview_url
    variant.file_url = variant.file_url?.replace(/^\//, "");
    variant.preview_url = variant.preview_url?.replace(/^\//, "");
    return {
      ...variant,
      value: variant.id,
      labelText: variant.variant,
      checked: variant.isChecked,
      file_url: variant.file_url ? `${assetsRoot}${variant.file_url}` : null,
      preview_url: variant.preview_url
        ? `${assetsRoot}${variant.preview_url}`
        : null,
      isDisabled,
      isInactive,
    };
  });

  if (randomVariantsOrder) {
    const shuffledVariants = shuffle(normalizedVariants);
    const exclusionedVariants = shuffledVariants.filter(
      ({ random_exclusion }) => !!random_exclusion,
    );
    if (exclusionedVariants.length) {
      const sortedExclusionedVariants = exclusionedVariants.sort(
        (a, b) => a.position - b.position,
      );
      const unexclusionedVariants = shuffledVariants.filter(
        ({ random_exclusion }) => !random_exclusion,
      );
      sortedExclusionedVariants.forEach((variant) => {
        const { position } = variant;
        unexclusionedVariants.splice(position - 1, 0, variant);
      });
      normalizedVariants = unexclusionedVariants;
    } else {
      normalizedVariants = shuffledVariants;
    }
  } else {
    normalizedVariants = normalizedVariants.sort(
      (a, b) => a.position - b.position,
    );
  }

  if (selfVariant?.enabled) {
    normalizedVariants.push({
      value: "is_self_answer",
      labelText: selfVariant.text || `Свой вариант`,
      checked: selfVariant.isChecked,
      isDisabled: van.derive(() => isVariantDisabled({ id: "is_self_answer" })),
      isInactive: van.derive(() => isVariantInactive({ id: "is_self_answer" })),
    });
  }

  const valueError = van.derive(() => {
    return errors.val.value;
  });

  const minChooseVariantsError = van.derive(() => {
    return errors.val.minChooseVariants;
  });

  const selfVariantError = van.derive(() => {
    return errors.val.selfVariant;
  });

  const selfVariantChecked = van.derive(() => {
    if (Array.isArray(vModel.val)) {
      const hasSelfVariantChecked =
        vModel.val.includes(0) || vModel.val.includes("is_self_answer");
      return hasSelfVariantChecked;
    } else {
      return vModel.val === 0 || vModel.val === "is_self_answer";
    }
  });

  const selfVariantTextareaRef = van.state(null);

  const isFileVariants = variantsWithFiles > 0;

  const renderDefaultVariants = () => {
    return FormCheckGroup({
      vModel,
      items: normalizedVariants,
      type: checkboxType,
      onChange,
      checkAttrs: {
        style: checkboxStyles,
      },
    });
  };

  const renderFileVariants = () => {
    return div(
      {
        class: `${styles["fc-widget-q-variants-list"]} ${view === "large" ? styles["fc-widget-q-variants-list--view-large"] : ""}`,
        "data-testid": "question-variants",
      },
      normalizedVariants.map((variant) => {
        if (variant.value === 0 || variant.value === "is_self_answer") {
          return div(
            {
              class: `${styles["fc-widget-q-variants-item"]} ${styles["fc-widget-q-variants-item--self-variant"]} ${styles["fc-widget-q-variants-item--self-variant-file"]}`,
            },
            FormCheck({
              vModel,
              value: variant.value,
              type: checkboxType,
              disabled: variant.isDisabled,
              inactive: variant.isInactive,
              labelText: variant.labelText,
              style: checkboxStyles,
              onChange,
            }),
          );
        }

        return div(
          {
            class: styles["fc-widget-q-variants-item"],
          },
          FormFileCheck({
            vModel,
            value: variant.value,
            previewUrl: variant.preview_url,
            fileUrl: variant.file_url,
            isDisabled: variant.isDisabled,
            isInactive: variant.isInactive,
            type: checkboxType,
            labelText: variant.labelText,
            size: view === "large" ? "large" : "default",
            onChange,
          }),
        );
      }),
    );
  };

  const checkboxStyles =
    view === "large"
      ? "--text-font-size: var(--fqz-widget-text-font-size);"
      : "";

  return div(
    {
      "data-testid": "question-variants",
    },
    FormGroup({
      style: "gap: 20px;",
      children: isFileVariants ? renderFileVariants() : renderDefaultVariants(),
      scrollIntoViewOnError: true,
      error: valueError,
      size: view === "large" ? "large" : "default",
    }),
    selfVariant?.enabled
      ? Transition({
          show: selfVariantChecked,
          calculateHeight: true,
          name: TRANSITION_NAMES.SLIDE,
          children: () =>
            FormGroup({
              style: "padding-top: 10px;",
              children: FormTextarea({
                value: selfVariant.value,
                ref: selfVariantTextareaRef,
                placeholder: selfVariant.placeholder || "",
                height: 75,
                onChange: selfVariant.onChange,
                invalid: selfVariantError,
                maxlength: selfVariant.fieldLength.max,
                autofocus: true,
                autofocusDelay: 610,
                scrollIntoView: true,
                scrollIntoViewDelay: 302,
                "data-testid": "textarea",
              }),
              error: selfVariantError,
            }),
        })
      : null,
    () =>
      Transition({
        name: TRANSITION_NAMES.FADE_IN,
        class: styles["fc-widget-q-variants-min-choose-error-container"],
        children: () =>
          FormError({
            error: minChooseVariantsError,
            scrollIntoViewOnAppear: true,
          }),
        show: minChooseVariantsError,
      }),
  );
}
