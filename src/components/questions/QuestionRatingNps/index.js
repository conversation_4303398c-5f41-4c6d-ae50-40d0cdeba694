import van from "vanjs-core";
import RatingNps from "@components/ui/RatingNps";
import Transition from "@components/ui/Transition";
import { TRANSITION_NAMES } from "@/constants";
import FormError from "@components/ui/form/FormError";

const { div } = van.tags;

export default function QuestionRatingNps({
  questionId,
  fromOne,
  npsRatingSetting: { design, startColor, endColor, startLabel, endLabel },
  vModel,
  isSkipped,
  errors,
  isUnrequired,
  onChange,
  /**
   * View type of the question.
   *  @type {"default" | "large"}
   */
  view = "default",
}) {
  const valueError = van.derive(() => {
    return errors?.val?.value;
  });

  return div(
    RatingNps({
      fromOne,
      designType: design,
      startColor,
      endColor,
      startLabel,
      endLabel,
      vModel,
      isDisabled: isSkipped,
      view,
      onChange: (value) => {
        if (onChange && typeof onChange === "function") {
          onChange(value, questionId);
        }
      },
    }),
    !isUnrequired
      ? Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: valueError,
          children: () =>
            div(
              { style: "margin-top: 20px" },
              FormError({ error: valueError }),
            ),
        })
      : null,
  );
}
