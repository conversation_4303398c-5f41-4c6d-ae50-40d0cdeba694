.fc-widget-q-form {
  /* display: flex;
  flex-direction: column;
  min-height: 169px;
  justify-content: center; */
}

/* .fc-widget-q-form--intermediate .fc-widget-q-form-nav-buttons {
  padding: 0;
} */

.fc-widget-q-form-error {
  color: var(--fqz-widget-error-color);
  font-size: 12px;
  line-height: 1.1;
}

.fc-widget-q-form-scroll-container {
  position: relative;
  width: 100%;
  padding-right: 0;
  box-sizing: border-box;
  /* overflow: hidden; */
}

.fc-widget-q-form-scroll {
  overflow: visible;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  padding: 0;
  width: 100%;
  padding-right: 20px;
}

.fc-widget-q-form-scroll-content {
  display: block;
}

.fc-widget-q-form-scroll--overflow {
  max-height: var(--fc-widget-q-form-max-height, 475px);
  overflow-x: hidden;
}

.fc-widget-q-form-scroll--overflow :global(._fc-sb-9m4nv18fd-content) > div {
  overflow: hidden;
}

.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow) {
  padding: 0;
}

.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow)
  :global(._fc-sb-9m4nv18fd-placeholder) {
  display: none !important;
}

.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow)
  :global(._fc-sb-9m4nv18fd-wrapper) {
  overflow: visible !important;
}

.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow)
  :global(._fc-sb-9m4nv18fd-content-wrapper) {
  overflow: visible !important;
}

.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow)
  :global(._fc-sb-9m4nv18fd-mask) {
  overflow: visible !important;
  position: static;
}

.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow)
  :global(._fc-sb-9m4nv18fd-offset) {
  position: static !important;
}

.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow)
  :global(._fc-sb-9m4nv18fd-track) {
  display: none;
}
.fc-widget-q-form-scroll:not(.fc-widget-q-form-scroll--overflow)
  :global(._fc-sb-9m4nv18fd-content) {
  padding: 0 !important;
}

.fc-widget-q-form-scroll--overflow:not(
    .fc-widget-q-form-scroll--custom-native
  ) {
  overflow: visible;
}

.fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--with-shadows:before,
.fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--with-shadows:after,
.fc-widget-q-form-scroll-container:has(
    .fc-widget-q-form-scroll--with-shadows
  )::before,
.fc-widget-q-form-scroll-container:has(
    .fc-widget-q-form-scroll--with-shadows
  )::after {
  content: "";
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 15px;
  z-index: 1;
  pointer-events: none;
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0) 0%,
    var(--fqz-widget-main-place-color) 100%
  );
  transition: opacity 0.3s;
}

.fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--with-shadows-hide-top:before,
.fc-widget-q-form-scroll-container:has(
    .fc-widget-q-form-scroll--with-shadows-hide-top
  )::before {
  opacity: 0;
}

.fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--with-shadows-hide-bottom:after,
.fc-widget-q-form-scroll-container:has(
    .fc-widget-q-form-scroll--with-shadows-hide-bottom
  )::after {
  opacity: 0;
}

.fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--with-shadows:after,
.fc-widget-q-form-scroll-container:has(
    .fc-widget-q-form-scroll--with-shadows
  )::after {
  top: auto;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    var(--fqz-widget-main-place-color) 100%
  );
}

.fc-widget-q-form-scroll-container:has(.fc-widget-q-form-scroll--overflow) {
  width: calc(100% + 20px);
}

.fc-widget-q-form-scroll--finished {
  height: 170px;
  max-height: 170px;
  overflow: hidden;
}

.fc-widget-q-form-scroll--custom-native {
  padding-right: 10px;
  overflow: visible;
}

.fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--custom-native {
  overflow: auto;
  overflow-x: hidden;
  padding-right: 20px !important;
}

.fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--custom-native > div {
  overflow-x: hidden;
}

.fc-widget-q-form-scroll--custom-native:before,
.fc-widget-q-form-scroll--custom-native:after {
  display: none !important;
}

.fc-widget-q-form-nav-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  padding-top: 30px;
}

.fc-widget-q-form-nav-buttons__spacer {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.fc-widget-q-form-nav-buttons__spacer > button {
  width: 100%;
}

.fc-widget-q-form-nav-buttons > button {
  width: 50%;
}

.fc-widget-q-form-nav-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.fc-widget-q-form-nav-button:focus {
  outline: none;
}

.fc-widget-q-form-nav-button-primary {
  border-color: var(--fqz-widget-main-color);
}

@media screen and (min-width: 768px) {
  .fc-widget-q-form--view-large .fc-widget-q-form-scroll--custom-native {
    padding: 0 !important;
  }
  .fc-widget-q-form--view-large .fc-widget-q-form-scroll--overflow.fc-widget-q-form-scroll--custom-native {
    width: calc(100% + 30px);
    padding-right: 40px !important;
    box-sizing: border-box;
  }
  .fc-widget-q-form--view-large .fc-widget-q-form-nav-buttons {
    gap: 15px;
  }
  .fc-widget-q-form--view-large .fc-widget-q-form-nav-button {
    max-width: 240px;
    min-width: 155px;
    width: auto;
    flex-grow: 0;
    flex-shrink: 1;
    min-height: 48px;
    line-height: 1.15;
    font-size: var(--fqz-widget-text-font-size);
    padding-left: 25px;
    padding-right: 25px;
  }

  .fc-widget-q-form--view-large .fc-widget-q-form-nav-button--ready {
    min-width: 112px;
    padding-left: 30px;
    padding-right: 30px;
  }

  .fc-widget-q-form--view-large .fc-widget-q-form-nav-button--start {
    min-width: 165px;
  }

  .fc-widget-q-form--view-large .fc-widget-q-form-nav-button--close-widget {
    min-width: 110px;
    padding-left: 25px;
    padding-right: 25px;
  } 

}