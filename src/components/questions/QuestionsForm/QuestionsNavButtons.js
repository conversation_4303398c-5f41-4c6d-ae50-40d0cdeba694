// src/components/questions/QuestionsForm/QuestionsNavButtons.js

import van from "vanjs-core";
import styles from "./styles.module.css";
import Button from "@components/ui/Button";
import { toValue } from "../../../helpers";

const { div } = van.tags;

export function QuestionsNavButtons({
  isLastStep,
  isFirstStep,
  onFinishClick,
  showPrevButton,
  prevButtonText,
  nextButtonText,
  finishButtonText,
  onPrevClick,
  onNextClick,
  canGoToNextStep,
  isStartScreen,
  isEndScreen,
  nextButtonStyle,
  backButtonStyle,
  startButtonText,
  onStartButtonClick,
  readyButtonEnabled,
  readyButtonText,
  readyButtonExternalLink,
  closeWidgetButtonEnabled,
  closeWidgetButtonText,
  onCloseWidgetButtonClick,
  hasScrollVisible,
  startButtonStyle,
  /**
   * @param {"default" | "large"} view
   * @default "default"
   */
  view = "default",
  ...attrs
}) {
  const shouldShowPrevButton = van.derive(() => {
    return showPrevButton && !isFirstStep.val && !isEndScreen.val;
  });

  const getButtonStyleString = (baseStyle) => `
    --button-background-color: ${baseStyle.backgroundColor};
    --button-text-color: ${baseStyle.color};
    --button-border-color: ${baseStyle.borderColor};
    --button-border-radius: ${baseStyle.borderRadius}px;
  `;

  const renderStartButton = () => {
    return Button({
      style: () =>
        `display: ${hasScrollVisible.val && isStartScreen.val ? "inline-flex" : "none"}; ${getButtonStyleString(startButtonStyle)}`,
      type: "button",
      children: () => toValue(startButtonText) || "Пройти опрос",
      disabled: () => !canGoToNextStep.val,
      class:
        styles["fc-widget-q-form-nav-button"] +
        " " +
        styles["fc-widget-q-form-nav-button--start"],
      onClick: (e) => {
        e.preventDefault();
        onStartButtonClick?.();
      },
    });
  };

  const renderReadyButton = () => {
    return Button({
      href: () => toValue(readyButtonExternalLink),
      children: () => toValue(readyButtonText) || "Готово",
      style: () => `
          display: ${toValue(readyButtonEnabled) && isEndScreen.val && hasScrollVisible.val ? "inline-flex" : "none"};
          ${getButtonStyleString(startButtonStyle)}
        `,
      type: "button",
      class:
        styles["fc-widget-q-form-nav-button"] +
        " " +
        styles["fc-widget-q-form-nav-button--ready"],
    });
  };

  const renderCloseWidgetButton = () => {
    const enabled = toValue(closeWidgetButtonEnabled);
    return Button({
      style: () =>
        `display: ${enabled && hasScrollVisible.val && isEndScreen.val ? "inline-flex" : "none"}; ${getButtonStyleString(startButtonStyle)}}`,
      type: "button",
      class:
        styles["fc-widget-q-form-nav-button"] +
        " " +
        styles["fc-widget-q-form-nav-button--close-widget"],
      children: () => toValue(closeWidgetButtonText),
      onClick: onCloseWidgetButtonClick,
    });
  };

  return div(
    {
      class: `${styles["fc-widget-q-form-nav-buttons"]} ${view === "large" ? styles["fc-widget-q-form-nav-buttons--view-large"] : ""}`,
      "data-testid": "form-nav-buttons",
      ...attrs,
    },
    Button({
      style: () => `
        display: ${shouldShowPrevButton.val ? "inline-flex" : "none"};
        ${getButtonStyleString(backButtonStyle)}
      `,
      class: styles["fc-widget-q-form-nav-button"],
      primary: false,
      onClick: (e) => {
        e.preventDefault();
        onPrevClick();
      },
      variant: "outlined",
      children: prevButtonText,
    }),
    renderStartButton(),
    renderReadyButton(),
    Button({
      style: () => `
        display: ${!isLastStep.val && !isStartScreen.val && !isEndScreen.val ? "inline-flex" : "none"};
        ${getButtonStyleString(nextButtonStyle)}
      `,
      type: () => (isLastStep.val ? "button" : "submit"),
      class: styles["fc-widget-q-form-nav-button"],
      onClick: (e) => {
        e.preventDefault();
        if (canGoToNextStep.val) {
          onNextClick();
        }
      },
      disabled: () => !canGoToNextStep.val,
      primary: true,
      children: () => toValue(nextButtonText) || "Далее",
    }),
    Button({
      style: () =>
        `display: ${isLastStep.val && !isEndScreen.val ? "inline-flex" : "none"}; ${getButtonStyleString(nextButtonStyle)}`,
      type: () => (isLastStep.val ? "submit" : "button"),
      class: styles["fc-widget-q-form-nav-button"],
      onClick: (e) => {
        e.preventDefault();
        if (canGoToNextStep.val) {
          onFinishClick();
        }
      },
      disabled: () => !canGoToNextStep.val,
      primary: true,
      children: () => toValue(finishButtonText) || "Завершить",
    }),
    renderCloseWidgetButton(),
  );
}
