import useElementScroll from "@/composables/useElementScroll";
import { useQuestionsNavigation } from "@/composables/useQuestionsNavigation";
import useResizeObserver from "@/composables/useResizeObserver";
import { TRANSITION_NAMES } from "@/constants";
import {
  QUESTION_TYPES,
  getAnswerStateByType,
  hasTransparency,
  parseBoolean,
} from "@/helpers";
import {
  MASK_TYPES,
  RATING_NPS_TYPES,
} from "@/helpers/question-helpers/questionTypes";
import resetByType from "@/helpers/question-helpers/resetByType";
import saveAnswerByType from "@/helpers/question-helpers/saveAnswerByType";
import Transition from "@components/ui/Transition";
import van from "vanjs-core";
import QuestionIntermediateBlock from "../QuestionIntermediateBlock";
import QuestionItem from "../QuestionItem";
import questionItemStyles from "../QuestionItem/styles.module.css";
import QuestionRating from "../QuestionRating";
import QuestionRatingNps from "../QuestionRatingNps";
import QuestionSmileRating from "../QuestionSmileRating";
import QuestionStarRating from "../QuestionStarRating";
import QuestionText from "../QuestionText";
import QuestionVariants from "../QuestionVariants";
import { QuestionsNavButtons } from "./QuestionsNavButtons";
import styles from "./styles.module.css";
import { INTERMEDIATE_BLOCK_TYPES } from "@/helpers/question-helpers/questionTypes";
import replacePlaceholders from "@/helpers/replacePlaceholders";
import getTranslatedPollData from "@/helpers/getTranslatedPollData";
import { initializeTranslations, LANGUAGE_IDS } from "@/helpers/translations";
import {
  SHORTCODE_TO_LANGUAGE_ID,
  translate,
} from "../../../helpers/translations";
import { useWindowSize } from "@/composables/useWindowSize";

const { div, span, form } = van.tags;

/**
 * Компонент формы вопросов, отвечающий за отображение и обработку вопросов в опросе.
 * Отвечает за следующие основные функции:
 * - Отображение вопросов с различными типами ответов (текстовый, рейтинг, варианты и т.д.)
 * - Валидация ответов на вопросы
 * - Сохранение ответов на вопросы
 * - Навигация между вопросами (переход к следующему/предыдущему вопросу)
 * - Отображение промежуточных и финальных экранов опроса
 * - Управление состоянием скроллбара формы в зависимости от высоты контента + дополнительных опций
 *
 * @param {object} pollData - Данные опроса, включающие вопросы, дизайн и другие настройки.
 * @param {function} onFormSuccess - Функция, вызываемая при успешном завершении опроса.
 * @param {object} config - Конфигурация API для сохранения ответов.
 * @param {object} config.additionalParams - Дополнительные параметры. Передаются при инициализации виджета в объекте `params`
 * @param {function} initCustomScrollbar - Функция для инициализации кастомного скроллбара.
 * @returns {HTMLElement} - Корневой элемент формы вопросов.
 */
export default function QuestionsForm(
  pollData,
  {
    onFormSuccess,
    config,
    initCustomScrollbar,
    widgetKey,
    /**
     * @param {string} view - Вид опроса.
     * @default "default"
     * @example "large"
     */
    view = "default",
    onCloseWidgetButtonClick,
    closeByFinishButton,
  },
) {
  const langShortCodeFromParams = config?.additionalParams?.lang;

  const langIdFromShortCode =
    SHORTCODE_TO_LANGUAGE_ID?.[langShortCodeFromParams];

  let defaultLang = pollData.lang || null;

  let langFromShortcode = null;
  if (
    langIdFromShortCode &&
    pollData.lang?.poll_lang_id === langIdFromShortCode
  ) {
    langFromShortcode = pollData.lang;
  } else if (!langIdFromShortCode && pollData.lang) {
    langFromShortcode = pollData.lang;
  }

  if (defaultLang?.id) {
    const langMessages = defaultLang.messages || {};
    initializeTranslations({ [defaultLang.id]: langMessages }, defaultLang.id);
  } else {
    initializeTranslations({}, LANGUAGE_IDS.ru_RU);
  }

  const translatedPollData = getTranslatedPollData(pollData, langFromShortcode);

  const questions = translatedPollData.questions;

  questions.push({
    type: QUESTION_TYPES.INTERMEDIATE_BLOCK,
    id: "end",
    question_id: "end",
    answer: {
      isEmpty: false,
    },
    intermediateBlock: {
      screen_type: INTERMEDIATE_BLOCK_TYPES.END,
      text: `<p style="font-size: 19px; line-height: 1.2; font-weight: 700; text-align: center;">${translate("Опрос успешно пройден!")}<p>`,
    },
    questionViewLogic: [],
    starRatingOptions: {},
  });

  const authKey = pollData.answer.auth_key;
  const apiConfig = { authKey, pollData, ...config };
  const scrollRef = van.state(null);
  const scrollContentRef = van.state(null);
  const customScrollRef = van.state(null);

  const customOrOriginalScrollRef = van.derive(() => {
    return customScrollRef.val || scrollRef.val;
  });

  const { height: windowHeight } = useWindowSize();

  const maxHeightPadding = view === "large" ? 200 : 160;

  const updateMaxHeight = () => {
    const root = document.documentElement;
    const maxHeight = Math.min(475, windowHeight.val - maxHeightPadding); // Subtracting 100px for potential padding/margins
    root.style.setProperty("--fc-widget-q-form-max-height", `${maxHeight}px`);
  };

  van.derive(() => {
    windowHeight.val;
    updateMaxHeight();
  });

  let scrollbar = null;
  let customScrollbarObserverEnabled = false;

  const mainPlaceColor = pollData.design.main_place_color;
  const backgroundColor = pollData.design.background_color;
  const backgroundImage = pollData.design.background_image;
  const mobileBackgroundImage = pollData.design.mobile_background_image;

  // Проверка на наличие прозрачности в цветах
  const mainPlaceColorHasTransparency =
    mainPlaceColor.includes("rgba") && hasTransparency(mainPlaceColor);

  const backgroundColorHasTransparency =
    backgroundColor.includes("rgba") && hasTransparency(backgroundColor);

  // Показываем тени у скролла, если:
  // 1. Цвет подложки не прозрачный
  // ИЛИ:
  // 1. У цвета фона нет прозрачности
  // 2. Нет фонового изображения
  // 3. Нет мобильного фонового изображения

  const shouldEnableScrollShadows =
    !mainPlaceColorHasTransparency ||
    (!backgroundColorHasTransparency &&
      !backgroundImage &&
      !mobileBackgroundImage);

  const scrollShadowsEnabled = van.state(shouldEnableScrollShadows);

  const { scrollState, forceUpdate } = useElementScroll(
    customOrOriginalScrollRef,
    scrollShadowsEnabled,
  );

  const scrollResizeObserverEnabled = true;

  const scrollRefSize = useResizeObserver(
    scrollRef,
    scrollResizeObserverEnabled,
  );
  const scrollContentRefSize = useResizeObserver(
    scrollContentRef,
    scrollResizeObserverEnabled,
  );

  const hasScrollVisible = van.state(false);

  // set default ids as question keys in questions array
  let answers = van.state(
    Object.keys(questions).reduce((acc, key) => {
      const question = questions[key];
      const id = question.id;
      acc[id] = getAnswerStateByType(question, acc, apiConfig);
      return acc;
    }, {}),
  );

  const {
    isFirstStep,
    isLastStep,
    showPrevButton,
    prevButtonText,
    nextButtonText,
    finishButtonText,
    goToNextStep,
    canGoToNextStep,
    isStartScreen,
    isEndScreen,
    isIntermediateBlock,
    startButtonText,
    readyButtonEnabled,
    readyButtonText,
    readyButtonExternalLink,
    closeWidgetButtonEnabled,
    closeWidgetButtonText,
    onStartButtonClick,
    onStartOverButtonClick,
    onNextClick,
    onFinishClick,
    onPrevClick,
    shouldShowNavButtons,
    validateFields,
    debouncedValidateFields,
    nextButtonStyle,
    backButtonStyle,
    startButtonStyle,
    currentQuestionId,
  } = useQuestionsNavigation({
    widgetKey,
    questions,
    answers,
    pollData: translatedPollData,
    onFormSuccess,
    saveAnswerByType,
    apiConfig,
    hasScrollVisible,
    defaultLang,
    onCloseWidgetButtonClick,
    closeByFinishButton,
  });

  const getCommonQuestionItemProps = (question, answer) => {
    const headerProps = getHeaderProps(question);
    const isSkipped = answer.isSkipped;

    const getCommentProps = () => {
      if (answer.comment?.enabled?.val) {
        return {
          enabled: question.comment_enabled === 1,
          value: answer.comment.value,
          valueMaxLength: question.textFieldParam?.max,
          required: question.comment_required === 1,
          label: question.comment_label,
          placeholder: question.placeholderText,
          errors: answer.errors,
          isSkipped,
          onCommentChange: () => {
            const commentValueIsDirty = answer.comment.isDirty.val;
            if (commentValueIsDirty) {
              debouncedValidateFields(["commentValue"]);
            }
          },
        };
      } else {
        return { enabled: false };
      }
    };

    const getAssessmentsProps = () => {
      if (answer.assessments?.enabled) {
        return {
          enabled: answer.assessments?.enabled,
          value: answer.assessments.value,
          variants: question.detail_answers?.filter(
            (variant) => !variant.is_deleted || variant.is_deleted === 0,
          ),
          type: question.assessmentVariantsType,
          visible: answer.assessments.visible,
          text: question.answerText,
          maxLength: question?.textFieldParam?.max,
          errors: answer.errors,
          onChange: () => {
            debouncedValidateFields();
          },
          selfVariant: {
            enabled: !!question.isHaveCustomField,
            text: answer.assessments.selfVariant.text,
            required: answer.assessments.selfVariant.required,
            placeholder: question.placeholderText,
            value: answer.assessments.selfVariant.value,
            visible: answer.assessments.selfVariant.visible,
            fieldLength: question.textFieldParam,
            onChange: () => {
              debouncedValidateFields();
            },
          },
          attachments: answer.assessments.attachments,
        };
      }
      return { enabled: false };
    };

    const getSkipProps = () => {
      return {
        enabled: question.skip === 1,
        text: question.skip_text,
        vModel: answer.isSkipped,
        onSkipChange: () => {
          resetByType(question, answer);
          if (!shouldShowNavButtons.val) {
            setTimeout(() => {
              goToNextStep();
            }, 10);
            return;
          }
        },
      };
    };

    return {
      headerProps,
      view,
      comment: {
        ...getCommentProps(),
      },
      assessments: {
        ...getAssessmentsProps(),
      },
      skip: {
        ...getSkipProps(),
      },
    };
  };

  const getHeaderProps = ({ description_html, subdescription, isRequired }) => {
    const isUnrequired = !isRequired || isRequired === 0;
    const normalizedDescriptionHtml = description_html
      ?.replace(/&nbsp;/g, " ")
      ?.replace(/\u00A0/g, " ");

    const normalizedSubdescription = subdescription
      ?.replace(/&nbsp;/g, " ")
      ?.replace(/\u00A0/g, " ");

    const processedDescription = van.derive(() => {
      const replaced = replacePlaceholders(
        normalizedDescriptionHtml,
        answers,
        pollData,
        questions,
      );
      return replaced;
    });
    const processedSubdescription = van.derive(() =>
      replacePlaceholders(
        normalizedSubdescription,
        answers,
        pollData,
        questions,
      ),
    );
    return {
      description: processedDescription,
      subdescription: processedSubdescription,
      isUnrequired,
      unrequiredText:
        translatedPollData.design.unrequired_text || "Необязательный",
      enabled: true,
    };
  };

  /**
   * Возвращает CSS-классы для отображения теней контейнера скроллбара в зависимости от состояния скролла.
   * @param {boolean} enabled - Флаг, указывающий, включены ли тени скроллбара.
   * @param {object} scrollState - Объект, содержащий информацию о текущем состоянии скролла.
   * @returns {string} - Строка с CSS-классами для теней скроллбара.
   */
  function getScrollShadowClasses(enabled, scrollState) {
    let shadowClasses = [];
    if (scrollShadowsEnabled.val) {
      shadowClasses.push(styles["fc-widget-q-form-scroll--with-shadows"]);
      if (scrollState.val.reachedTop) {
        // Если мы находимся вверху
        shadowClasses.push(
          styles["fc-widget-q-form-scroll--with-shadows-hide-top"],
        );
      }

      if (scrollState.val.reachedBottom) {
        // Если мы находимся внизу
        shadowClasses.push(
          styles["fc-widget-q-form-scroll--with-shadows-hide-bottom"],
        );
      }
    }
    return shadowClasses;
  }

  const renderQuestionsList = () => {
    const items = questions.map((question) => {
      const answer = answers.val[question.id];
      const isVisible = van.derive(
        () => currentQuestionId.val === question.id && answer.isActive.val,
      );
      const isUnrequired = !question.isRequired || question.isRequired === 0;
      const isSkipped = answer.isSkipped;

      const commonProps = getCommonQuestionItemProps(question, answer);

      if (question.type === QUESTION_TYPES.STAR_RATING) {
        // Звездный рейтинг
        return Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              questionElement: QuestionStarRating({
                questionId: question.question_id,
                view,
                count: question.starRatingOptions.count,
                size: question.starRatingOptions.size,
                thin: question.starRatingOptions.size === "lg",
                labelsArray: question.starRatingOptions.labelsArray || [],
                color: question.starRatingOptions.color,
                alwaysShowLabels: question.show_labels === 1,
                vModel: answer.value,
                showNumbers: question.show_numbers === 1,
                showLabels: question.show_labels === 1,
                isUnrequired,
                isSkipped,
                skip: question.skip === 1,
                skipText: question.skip_text,
                errors: answer.errors,
                onChange: () => {
                  if (!shouldShowNavButtons.val && !isSkipped.val) {
                    goToNextStep();
                    return;
                  }
                  validateFields(["value"]);
                  if (isSkipped.val) {
                    isSkipped.val = false;
                  }
                },
              }),
            }),
        });
      } else if (question.type === QUESTION_TYPES.SMILE_RATING) {
        // Смайл рейтинг
        return Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              questionElement: QuestionSmileRating({
                view,
                questionId: question.question_id,
                assetsRoot: config.assetsRoot,
                labelsArray: question.starRatingOptions?.labelsArray || [],
                color: question.starRatingOptions.color,
                alwaysShowLabels: question.show_labels === 1,
                smiles: question.smiles,
                count: question.smiles?.length,
                smileType: question.smileType,
                vModel: answer.value,
                showNumbers: question.show_numbers === 1,
                showLabels: question.show_labels === 1,
                isUnrequired,
                isSkipped,
                skip: question.skip === 1,
                skipText: question.skip_text,
                errors: answer.errors,
                hasScrollVisible,
                onChange: () => {
                  // Если отсутствует нижний блок навигации, то переходим к следующему вопросу
                  if (!shouldShowNavButtons.val && !isSkipped.val) {
                    setTimeout(() => {
                      goToNextStep();
                    }, 600);
                    return;
                  }
                  validateFields(["value"]);
                  if (isSkipped.val) {
                    isSkipped.val = false;
                  }
                },
              }),
            }),
        });
      } else if (question.type === QUESTION_TYPES.RATING) {
        // Рейтинг
        return Transition({
          class: questionItemStyles["fc-widget-q-item--rating"],
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              questionElement: QuestionRating({
                view,
                questionId: question.question_id,
                count: question.starRatingOptions.count,
                labelsArray: question.starRatingOptions.labelsArray || [],
                color: question.starRatingOptions.color,
                vModel: answer.value,
                showLabels: question.show_labels === 1,
                isUnrequired,
                isSkipped,
                errors: answer.errors,
                onChange: () => {
                  validateFields(["value"]);
                  if (!shouldShowNavButtons.val && !isSkipped.val) {
                    goToNextStep();
                    return;
                  }
                  if (isSkipped.val) {
                    isSkipped.val = false;
                  }
                },
              }),
            }),
        });
      } else if (question.type === QUESTION_TYPES.RATING_NPS) {
        // Рейтинг NPS
        return Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              questionElement: QuestionRatingNps({
                questionId: question.question_id,
                view,
                fromOne: question.fromOne,
                vModel: answer.value,
                isUnrequired,
                isSkipped,
                errors: answer.errors,
                variantType:
                  question.set_variants === 1
                    ? RATING_NPS_TYPES.VARIANTS
                    : RATING_NPS_TYPES.DEFAULT,
                npsRatingSetting: {
                  design: question.npsRatingSetting.design,
                  startColor: question.npsRatingSetting.start_point_color,
                  endColor: question.npsRatingSetting.end_point_color,
                  startLabel: question.npsRatingSetting.start_label,
                  endLabel: question.npsRatingSetting.end_label,
                },
                onChange: () => {
                  validateFields(["value"]);
                  if (!shouldShowNavButtons.val && !isSkipped.val) {
                    goToNextStep();
                    return;
                  }
                  if (isSkipped.val) {
                    isSkipped.val = false;
                  }
                },
              }),
            }),
        });
      } else if (question.type === QUESTION_TYPES.TEXT) {
        // Текстовый ответ
        return Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              questionElement: QuestionText({
                view,
                questionId: question.question_id,
                vModel: answer.value,
                minLength: question.textFieldParam?.min,
                maxLength: question.textFieldParam?.max,
                placeholder: question.placeholderText,
                variantType: question.variantsType,
                maskType: question.maskType,
                dateType: question.dateType,
                isUnrequired,
                isSkipped,
                errors: answer.errors,
                fio: {
                  enabled: question.maskType === MASK_TYPES.FIO,
                  name: {
                    visible: parseBoolean(question.maskConfig?.name?.visible),
                    required: parseBoolean(question.maskConfig?.name?.required),
                    placeholderText: question.maskConfig?.name?.placeholderText,
                    minLength: parseInt(question.maskConfig?.name?.minLength),
                    maxLength: parseInt(question.maskConfig?.name?.maxLength),
                    value: question.maskConfig?.name?.value,
                    vModel: answer.fio?.name?.value,
                  },
                  surname: {
                    visible: parseBoolean(
                      question.maskConfig?.surname?.visible,
                    ),
                    required: parseBoolean(
                      question.maskConfig?.surname?.required,
                    ),
                    placeholderText:
                      question.maskConfig?.surname?.placeholderText,
                    minLength: parseInt(
                      question.maskConfig?.surname?.minLength,
                    ),
                    maxLength: parseInt(
                      question.maskConfig?.surname?.maxLength,
                    ),
                    value: question.maskConfig?.surname?.value,
                    vModel: answer.fio?.surname?.value,
                  },
                  patronymic: {
                    visible: parseBoolean(
                      question.maskConfig?.patronymic?.visible,
                    ),
                    required: parseBoolean(
                      question.maskConfig?.patronymic?.required,
                    ),
                    placeholderText:
                      question.maskConfig?.patronymic?.placeholderText,
                    minLength: parseInt(
                      question.maskConfig?.patronymic?.minLength,
                    ),
                    maxLength: parseInt(
                      question.maskConfig?.patronymic?.maxLength,
                    ),
                    value: question.maskConfig?.patronymic?.value,
                    vModel: answer.fio?.patronymic?.value,
                  },
                },
                onChange: () => {
                  debouncedValidateFields(["value"]);
                  if (isSkipped.val) {
                    isSkipped.val = false;
                  }
                },
              }),
              skip: {
                ...commonProps.skip,
                text: question.skip_text || "Затрудняюсь ответить",
                onSkipChange: (value) => {
                  answer.errors.val = {};
                  answer.value.val = "";

                  if (question.maskType === MASK_TYPES.FIO && answer.fio) {
                    if (answer.fio.name?.value) {
                      answer.fio.name.value.val = "";
                    }
                    if (answer.fio.surname?.value) {
                      answer.fio.surname.value.val = "";
                    }
                    if (answer.fio.patronymic?.value) {
                      answer.fio.patronymic.value.val = "";
                    }
                  }
                  answer.isSkipped.val = value;

                  if (!shouldShowNavButtons.val) {
                    setTimeout(() => {
                      goToNextStep();
                    }, 10);
                    return;
                  }
                },
              },
            }),
        });
      } else if (question.type === QUESTION_TYPES.VARIANTS) {
        // Варианты ответов
        return Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              skip: {
                ...commonProps.skip,
                text: question.skip_text || "Затрудняюсь ответить",
              },
              commentGap: 20,
              questionElement: QuestionVariants({
                view,
                vModel: answer.value,
                minLength: question.textFieldParam?.min,
                maxLength: question.textFieldParam?.max,
                placeholder: question.placeholderText,
                removeOthersItemId: answer.removeOthersItemId,
                type: question.variantsType,
                isUnrequired,
                isSkipped,
                errors: answer.errors,
                variants:
                  question.detail_answers?.filter(
                    (variant) =>
                      !variant.is_deleted || variant.is_deleted === 0,
                  ) || [],
                variantsWithFiles: question.variants_with_files,
                randomVariantsOrder: question.random_variants_order,
                maxChooseVariants: question.max_choose_variants,
                assetsRoot: config.assetsRoot,
                selfVariant: {
                  enabled: question.isHaveCustomField === 1,
                  value: answer.selfVariant.value,
                  text: question.self_variant_text,
                  isDirty: answer.selfVariant.isDirty,
                  placeholder: question.selfVariantPlaceholderText || "",
                  fieldLength: {
                    min: question.selfVariantParam?.min,
                    max: question.selfVariantParam?.max,
                  },
                  onChange: () => {
                    debouncedValidateFields();
                  },
                },
                onChange: () => {
                  debouncedValidateFields(["value"]);

                  if (!shouldShowNavButtons.val) {
                    setTimeout(() => {
                      goToNextStep();
                    }, 10);
                    return;
                  }

                  // if (
                  //   answer.selfVariant?.isDirty.val &&
                  //   !answer.selfVariant.value.val
                  // ) {
                  //   answer.selfVariant.isDirty.val = false;
                  // }
                  if (isSkipped.val) {
                    isSkipped.val = false;
                  }
                },
              }),
            }),
        });
      } else if (question.type === QUESTION_TYPES.INTERMEDIATE_BLOCK) {
        const processedText = van.derive(() => {
          const replaced = replacePlaceholders(
            question.intermediateBlock?.text,
            answers,
            pollData,
            questions,
            question,
          );
          return replaced;
        });
        const images = answer.endScreenImages || [];
        const processedImages = images.map((img) => {
          const processedLink = van.derive(() =>
            replacePlaceholders(
              img.link,
              answers,
              pollData,
              questions,
              question,
            ),
          );
          const processedDescription = van.derive(() =>
            replacePlaceholders(
              img.description,
              answers,
              pollData,
              questions,
              question,
            ),
          );
          return {
            ...img,
            description: processedDescription,
            link: processedLink,
          };
        });
        // Промежуточный блок
        return Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              headerProps: {
                ...commonProps.headerProps,
                enabled: false,
              },
              questionElement: QuestionIntermediateBlock({
                view,
                type: question.intermediateBlock?.screen_type,
                id: question.id,
                content: processedText,
                vModelAgreement: answer.agreement.value,
                agreement: question.intermediateBlock?.agreement,
                agreementText: question.intermediateBlock?.agreement_text,
                errors: answer.errors,
                fio: pollData.variables?.fio,
                promocode: pollData.variables?.codes?.[question.question_id],
                images: processedImages || [],
                imagesPlaceBg: question.intermediateBlock?.logos_backcolor,
                assetsRoot: config.assetsRoot,
                hasScrollVisible,
                startButtonText: startButtonText,
                onStartButtonClick: onStartButtonClick,
                readyButtonEnabled:
                  question.intermediateBlock?.ready_button === 1,
                readyButtonExternalLink:
                  question.intermediateBlock?.external_link,
                readyButtonText:
                  question.intermediateBlock?.ready_button_text ||
                  readyButtonText,
                closeWidgetButtonEnabled: closeWidgetButtonEnabled,
                closeWidgetButtonText: closeWidgetButtonText,
                onCloseWidgetButtonClick: onCloseWidgetButtonClick,
                canGoToNextStep: canGoToNextStep,
                startButtonStyle: startButtonStyle,
                shouldShowNavButtons,
                onAgreementChange: () => {
                  validateFields();
                },
              }),
              skip: {
                ...commonProps.skip,
                enabled: false, // Typically, intermediate blocks can't be skipped
              },
            }),
        });
      } else {
        return Transition({
          name: TRANSITION_NAMES.FADE_IN,
          show: isVisible,
          children: () =>
            QuestionItem({
              ...commonProps,
              questionElement: div(
                { class: styles["fc-widget-q-content"] },
                span("Question type not supported"),
              ),
              errors: answers.val[question.id].errors,
            }),
        });
      }
    });

    return div(
      {
        class: styles["fc-widget-q-form-scroll-content"],
      },
      items,
    );
  };

  /**
   * Основной контент формы. Содержит в себе все вопросы
   * @type {HTMLElement}
   **/
  const scrollElem = div(renderQuestionsList());

  let lastScrollVisibleChange = 0;
  const COOLDOWN_PERIOD = 350;

  const deboucedSetScrollClasses = () => {
    const now = Date.now();
    let cls = [styles["fc-widget-q-form-scroll"]];
    const currentHeight =
      scrollContentRef.val?.height || scrollRefSize.val.height;
    const maxHeight = parseInt(
      getComputedStyle(document.documentElement).getPropertyValue(
        "--fc-widget-q-form-max-height",
      ),
    );

    const shouldShowScroll = currentHeight >= maxHeight;

    scrollElem.classList.add(...cls);

    // Only update if we're outside the cooldown period or if the scroll state has changed
    // const timePassed = now - lastScrollVisibleChange;
    if (
      now - lastScrollVisibleChange > COOLDOWN_PERIOD &&
      shouldShowScroll !== hasScrollVisible.val
    ) {
      if (shouldShowScroll) {
        scrollElem.classList.add(styles["fc-widget-q-form-scroll--overflow"]);
        if (scrollbar && !customScrollbarObserverEnabled) {
          scrollbar.recalculate();
          scrollbar.resizeObserver.observe(scrollbar.el);
          scrollbar.resizeObserver.observe(scrollbar.contentEl);
          customScrollbarObserverEnabled = true;
        }

        if (shouldEnableScrollShadows && !scrollShadowsEnabled.val) {
          scrollShadowsEnabled.val = true;
          setTimeout(() => {
            forceUpdate();
          }, 100);
        }

        if (!hasScrollVisible.val) {
          hasScrollVisible.val = true;
          lastScrollVisibleChange = now;
        }
      } else {
        scrollElem.classList.remove(
          styles["fc-widget-q-form-scroll--overflow"],
        );
        if (shouldEnableScrollShadows && scrollShadowsEnabled.val) {
          scrollShadowsEnabled.val = false;
        }
        if (scrollbar && customScrollbarObserverEnabled) {
          scrollbar.resizeObserver.disconnect();
          customScrollbarObserverEnabled = false;
        }

        if (hasScrollVisible.val) {
          hasScrollVisible.val = false;
          lastScrollVisibleChange = now;
        }
      }
    }
  };

  van.derive(() => {
    if (scrollResizeObserverEnabled) {
      scrollContentRefSize.val;
      deboucedSetScrollClasses();
    }
  });

  van.derive(() => {
    if (!scrollResizeObserverEnabled) {
      return;
    }

    if (!hasScrollVisible.val) {
      // remove all shadow classes first
      scrollElem.classList.remove(
        styles["fc-widget-q-form-scroll--with-shadows"],
        styles["fc-widget-q-form-scroll--with-shadows-hide-top"],
        styles["fc-widget-q-form-scroll--with-shadows-hide-bottom"],
      );
      scrollShadowsEnabled.val = false;
      return;
    }

    if (shouldEnableScrollShadows && !scrollShadowsEnabled.val) {
      scrollShadowsEnabled.val = true;
    }
    const shadowClasses = getScrollShadowClasses(
      scrollShadowsEnabled.val,
      scrollState,
    );
    // remove all shadow classes first
    scrollElem.classList.remove(
      styles["fc-widget-q-form-scroll--with-shadows"],
      styles["fc-widget-q-form-scroll--with-shadows-hide-top"],
      styles["fc-widget-q-form-scroll--with-shadows-hide-bottom"],
    );
    scrollShadowsEnabled.val = true;
    scrollElem.classList.add(...shadowClasses);
  });

  if (!scrollRef.val) {
    scrollRef.val = scrollElem;
    if (initCustomScrollbar && typeof initCustomScrollbar === "function") {
      setTimeout(() => {
        scrollbar = initCustomScrollbar(scrollElem, {
          disableObserver: true,
        });
        customScrollRef.val = scrollbar.getScrollElement();
      }, 200);
    } else {
      scrollElem?.classList?.add(
        styles["fc-widget-q-form-scroll--custom-native"],
      );
    }

    setTimeout(() => {
      scrollContentRef.val = scrollElem.querySelector(
        `.${styles["fc-widget-q-form-scroll-content"]}`,
      );
    }, 200);
  }

  van.derive(() => {
    if (!scrollResizeObserverEnabled) {
      return;
    }
    if (currentQuestionId.val && customScrollRef?.val) {
      // scroll to top
      setTimeout(() => {
        customScrollRef.val.scrollTo(0, 0);
      }, 100);
    }
  });

  const formClasses = van.derive(() => {
    const classes = [];

    if (isIntermediateBlock.val) {
      classes.push(styles["fc-widget-q-form--intermediate"]);
    }

    if (view === "large") {
      classes.push(styles["fc-widget-q-form--view-large"]);
    }

    classes.push(styles["fc-widget-q-form"]);

    return classes.join(" ");
  });

  return form(
    {
      onsubmit(e) {
        e.preventDefault();
      },
      class: () => formClasses.val,
    },
    div({ class: styles["fc-widget-q-form-scroll-container"] }, scrollElem),
    Transition({
      show: shouldShowNavButtons,
      name: TRANSITION_NAMES.SLIDE,
      calculateHeight: true,
      style: "width: 100%;",
      children: () =>
        QuestionsNavButtons({
          isFirstStep,
          isLastStep,
          view,
          showPrevButton,
          prevButtonText,
          nextButtonText,
          finishButtonText,
          closeByFinishButton,
          canGoToNextStep,
          onPrevClick,
          onNextClick,
          onFinishClick,
          onStartOverButtonClick,
          isStartScreen,
          isEndScreen,
          nextButtonStyle,
          backButtonStyle,
          hasScrollVisible,
          readyButtonExternalLink,
          readyButtonEnabled,
          readyButtonText,
          closeWidgetButtonEnabled,
          closeWidgetButtonText,
          onCloseWidgetButtonClick,
          startButtonText,
          startButtonStyle,
          onStartButtonClick: goToNextStep,
        }),
    }),
  );
}
