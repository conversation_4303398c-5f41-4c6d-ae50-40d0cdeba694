import van from "vanjs-core";
import styles from "./styles.module.css";
import "@/styles/global.css";
import QuestionsForm from "@components/questions/QuestionsForm";
import { getCssVariables } from "@/helpers";
import { getCookie, setCookie } from "@/helpers/cookies";
import { QUESTION_TYPES } from "@/helpers/question-helpers/questionTypes";
import QuestionItem from "@components/questions/QuestionItem";
import QuestionRating from "@components/questions/QuestionRating";
import QuestionRatingNps from "@components/questions/QuestionRatingNps";
import QuestionSmileRating from "@components/questions/QuestionSmileRating";
import QuestionStarRating from "@components/questions/QuestionStarRating";
import api from "@/api";
import {
  WIDGET_APPEARANCE,
  WIDGET_BUTTON_POSITION,
  WIDGET_FORM,
  WIDGET_VIEW,
} from "./constants";
import { INTERMEDIATE_BLOCK_TYPES } from "@/helpers/question-helpers/questionTypes";
import saveAnswerByType from "@/helpers/question-helpers/saveAnswerByType";
import { debounce, getAnswerStateByType } from "@/helpers";
import useBodyScrollLock from "@/composables/useBodyScrollLock";
import merge from "lodash.merge";
import {
  getDefaultDesignSettings,
  getScrollDepthTrigger,
  setupScrollDepthTrigger,
  destroyExistingWidget,
} from "./utils";

/**
 * @typedef {import('./constants').WIDGET_APPEARANCE} WIDGET_APPEARANCE
 * @typedef {import('./constants').WIDGET_FORM} WIDGET_FORM
 * @typedef {import('./constants').WIDGET_BUTTON_TYPE} WIDGET_BUTTON_TYPE
 * @typedef {import('./constants').WIDGET_POSITION} WIDGET_POSITION
 */
const { div, span, button, a } = van.tags;
const { path, svg } = van.tags("http://www.w3.org/2000/svg");

class FQZWidgetClass {
  static getScrollDepthTrigger = getScrollDepthTrigger;
  static setupScrollDepthTrigger = setupScrollDepthTrigger;
  static instance = null;

  constructor(widgetData, config, initCustomScrollbar) {
    this.widgetData = widgetData;
    this.widgetKey = widgetData?.key;
    this.closeByFinishButton =
      widgetData?.widget?.close_by_finish_button === "1";

    this.pollData = van.state(null);
    this.config = config;
    this.isActive = van.state(false);
    this.isCssLoaded = van.state(false);
    this.isLoadingPollData = van.state(false);
    this.transitionDuration = 500; // in milliseconds
    this.initCustomScrollbar = initCustomScrollbar;
    this.scrollDepthCleanup = null;

    this.isModalActive = van.state(false);

    useBodyScrollLock(this.isModalActive);

    this.handleClientUUID();
    this.loadCSS();

    this.initialize(widgetData);

    // Start tracking URL changes
    if (this.isWidgetTriggeredByTrackHistory(widgetData)) {
      this.trackUrlChanges();
    }

    FQZWidgetClass.instance = this;

    this.dispatchCustomEvent("foquz::loaded");
    this.debouncedReinitialize = debounce(this.reinitialize.bind(this), 300);
  }

  initWidgetAppearance(widgetData) {
    /** @type {WIDGET_APPEARANCE} */
    this.appearance = parseInt(widgetData.widget.appearance);

    /** @type {WIDGET_FORM} */
    this.form = parseInt(widgetData.widget.form);

    /** @type {WIDGET_BUTTON_TYPE} */
    this.buttonType = parseInt(widgetData.widget.button_type);

    /** @type {WIDGET_VIEW} */
    this.view =
      this.appearance === WIDGET_APPEARANCE.WITHOUT_CLICK
        ? WIDGET_VIEW.DEFAULT
        : WIDGET_VIEW.MODAL;

    // Позиция виджета (для отображения Hello-board) ИЛИ позиция кнопки триггера
    // Для при появлении виджета "По клику"
    this.position = parseInt(widgetData.widget.position);

    /** @type {string} */
    this.buttonText = widgetData.widget.button_text || "Пройти опрос";

    /** @type {{
     *   font: string,
     *   fontSize: string,
     *   bold: boolean,
     *   italic: boolean,
     *   textColor: string,
     *   backgroundColor: string,
     *   stroke: boolean
     * }} */
    this.buttonStyle = {
      font: widgetData.widget.font,
      fontSize: widgetData.widget.font_size,
      bold: widgetData.widget.bold === "1",
      italic: widgetData.widget.italic === "1",
      textColor: widgetData.widget.text_color || "inherit",
      backgroundColor: widgetData.widget.background_color,
      stroke: widgetData.widget.stroke === "1",
    };

    this.widgetView =
      this.appearance === WIDGET_APPEARANCE.WITHOUT_CLICK ? "default" : "modal";
  }

  dispatchCustomEvent(eventName) {
    const event = new CustomEvent(eventName, { detail: this });
    setTimeout(() => {
      document.dispatchEvent(event);
    }, 10);
  }

  trackUrlChanges() {
    let lastUrl = window.location.href;
    let isInitialLoad = true;

    // Обработчик для SPA-переходов
    const handleSPANavigation = () => {
      if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        // Определяем, мобильное ли устройство
        const isMobile = window.screen.width < 600 ? 1 : 0;
        const isHistory = 1;

        // Fetch new widget data and reinitialize if needed
        api
          .fetchWidgetData(
            lastUrl,
            this.config.widgetCode,
            document.cookie,
            this.config.root,
            this.config.additionalParams,
            null,
            isMobile,
            isHistory,
          )
          .then((newWidgetData) => {
            if (newWidgetData.link) {
              if (this.isWidgetTriggeredByTrackHistory(newWidgetData)) {
                this.debouncedReinitialize(newWidgetData);
              }
            }
          })
          .catch((error) => {
            console.error("Failed to fetch widget data on URL change:", error);
          });
      }
    };

    // Перехват методов history API
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function () {
      originalPushState.apply(this, arguments);
      handleSPANavigation();
    };

    history.replaceState = function () {
      originalReplaceState.apply(this, arguments);
      handleSPANavigation();
    };

    // Обработчик для обычных переходов и якорных ссылок
    const handleRegularNavigation = () => {
      if (window.location.href !== lastUrl) {
        const [baseUrl, hash] = window.location.href.split("#");
        const [lastBaseUrl, lastHash] = lastUrl.split("#");

        if (baseUrl !== lastBaseUrl || hash !== lastHash) {
          lastUrl = window.location.href;
          if (!isInitialLoad) {
            // Определяем, мобильное ли устройство
            const isMobile = window.screen.width < 600 ? 1 : 0;
            const isHistory = 1;

            // Fetch new widget data and reinitialize if needed
            api
              .fetchWidgetData(
                lastUrl,
                this.config.widgetCode,
                document.cookie,
                this.config.root,
                this.config.additionalParams,
                null,
                isMobile,
                isHistory,
              )
              .then((newWidgetData) => {
                if (newWidgetData.link) {
                  if (this.isWidgetTriggeredByTrackHistory(newWidgetData)) {
                    this.debouncedReinitialize(newWidgetData);
                  }
                }
              })
              .catch((error) => {
                console.error(
                  "Failed to fetch widget data on URL change:",
                  error,
                );
              });
          }
        }
      }
    };

    // Add event listener for regular navigation
    window.addEventListener("popstate", handleRegularNavigation);
    window.addEventListener("hashchange", handleRegularNavigation);

    // Set initial load flag to false after first load
    isInitialLoad = false;
  }

  triggerEvent(eventName, eventParams = {}) {
    if (!eventName) {
      console.error("Event name is required");
      return;
    }

    const mergedParams = merge({}, this.config.additionalParams, eventParams);

    // Determine if the device is mobile based on screen width
    const isMobile = window.screen.width < 600 ? 1 : 0;

    api
      .fetchWidgetData(
        window.location.href,
        this.config.widgetCode,
        document.cookie,
        this.config.root,
        mergedParams,
        eventName,
        isMobile,
      )
      .then((newWidgetData) => {
        if (newWidgetData.link) {
          if (this.isWidgetTriggeredByEvent(newWidgetData)) {
            const time = this.getEventTime(newWidgetData);

            setTimeout(() => {
              this.debouncedReinitialize(newWidgetData);
            }, time * 1000);
          } else {
            this.debouncedReinitialize(newWidgetData);
          }
        }
      })
      .catch((error) => {
        console.error("Failed to fetch widget data:", error);
      });
  }

  /**
   * Переинициализация виджета
   * @param {WIDGET_DATA} newWidgetData - Новые данные виджета
   * @returns {void}
   */
  reinitialize(newWidgetData) {
    this.destroy();
    this.widgetData = newWidgetData;
    this.widgetKey = newWidgetData?.key;
    this.closeByFinishButton =
      newWidgetData?.widget?.close_by_finish_button === "1";
    this.initialize(newWidgetData);
  }

  loadCSS() {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = this.config.stylesPath;
    link.onload = () => {
      this.isCssLoaded.val = true;
    };
    document.head.appendChild(link);
  }

  async setPollDataWithDelay(pollData) {
    if (this.config.pollLoadDelay > 0) {
      await new Promise((resolve) =>
        setTimeout(resolve, this.config.pollLoadDelay),
      );
    }

    if (pollData && !pollData.design) {
      pollData.design = getDefaultDesignSettings();
    }

    this.pollData.val = pollData;
  }

  async fetchPollData() {
    this.isLoadingPollData.val = true;
    const langFromParams = this.config.additionalParams?.lang;

    if (this.widgetData.link && !this.pollData.val) {
      try {
        const pollData = await api.fetchPollData(
          this.widgetData,
          this.config.assetsRoot,
          this.config.additionalParams,
        );

        await this.setPollDataWithDelay(pollData);

        const jsonParamsFromData = pollData.answer?.custom_fields;
        let parsedParams = {};

        if (jsonParamsFromData) {
          try {
            parsedParams = JSON.parse(jsonParamsFromData);
          } catch {
            parsedParams = {};
          }
        }

        this.config.additionalParams = {
          ...this.config.additionalParams,
          ...parsedParams,
        };

        if (langFromParams && !parsedParams.lang) {
          this.config.additionalParams.lang = langFromParams;
        }
      } catch (error) {
        console.error("Failed to fetch poll data:", error);
      } finally {
        this.isLoadingPollData.val = false;
      }
    } else {
      this.isLoadingPollData.val = false;
    }
  }

  /**
   * Обрабатывает UUID клиента.
   * Проверяет наличие существующего UUID в куках.
   * Если UUID отсутствует, устанавливает новый UUID из данных виджета.
   */
  handleClientUUID() {
    const cookieName = "foquz_client_uuid";
    const existingUUID = getCookie(cookieName);

    if (!existingUUID && this.widgetData.clientUUID) {
      const clientUUID = this.widgetData.clientUUID;
      if (clientUUID) {
        setCookie(cookieName, clientUUID);
      }
    }
  }
  appendButton(settings) {
    const config = this.config;
    const position = settings.position || 0;
    const buttonStyle = settings.buttonStyle || {};
    const buttonType = settings.buttonType;
    const buttonText = settings.buttonText;
    const self = this;
    const el = `<span type="button" class="fc-widget-preview-button" style="font-family: ${buttonStyle.font}; font-size: ${buttonStyle.fontSize}px; color: ${buttonStyle.textColor}; background-color: ${buttonStyle.backgroundColor}; ${buttonStyle.stroke == 1 ? "border-width: 1px; border-style: solid; border-color: currentcolor;" : "border: none;"} font-weight: ${buttonStyle.bold == 1 ? "bold" : "normal"}; font-style: ${buttonStyle.italic == 1 ? "italic" : "normal"};border-radius: 4px;display: flex;align-items: center;line-height: 1;min-height: 35px;padding: ${buttonType == 0 ? "0px 25px" : "0"};white-space: nowrap;cursor: pointer;">
    <div class="fc-widget-preview-text">
      <span style="${buttonType != 0 ? "display: none" : ""}">${buttonText || "Пройти опрос"}</span>
      <div style="display: none">
        <svg>
          <symbol id="foquz-logo-xs-icon" class="quiz" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.31852 17.9999H2.72741C2.26927 17.9999 1.8299 17.8208 1.50595 17.502C1.18199 17.1832 1 16.7508 1 16.2999V10.3499C1 9.89907 1.18199 9.46668 1.50595 9.14787C1.8299 8.82906 2.26927 8.64995 2.72741 8.64995H5.31852M11.3645 6.94996V3.54998C11.3645 2.87369 11.0915 2.22509 10.6055 1.74687C10.1196 1.26866 9.46055 1 8.77334 1L5.31852 8.64995V17.9999H15.0611C15.4777 18.0045 15.882 17.8608 16.1994 17.5953C16.5168 17.3298 16.7261 16.9603 16.7885 16.5549L17.9804 8.90495C18.018 8.6613 18.0013 8.41253 17.9315 8.17587C17.8617 7.9392 17.7404 7.72031 17.5761 7.53435C17.4118 7.34838 17.2084 7.1998 16.9799 7.0989C16.7514 6.99799 16.5034 6.94717 16.253 6.94996H11.3645Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </symbol>
          <symbol id="foquz-logo-xs" width="43" height="9" viewBox="0 0 43 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.12512 1.78826C5.0963 1.90718 4.98932 1.99098 4.86632 1.99098H1.99566V3.75228H4.66213L4.23531 5.45327H1.99566V8.77079H0V0.229676H5.16483C5.33709 0.229676 5.46399 0.389902 5.42363 0.556438L5.12512 1.78826ZM14.9658 4.46358C14.9658 7.16585 13.0292 8.99953 10.4077 8.99953C7.798 8.99953 5.86139 7.16585 5.86139 4.46358C5.86139 1.72511 7.798 0 10.4077 0C13.0292 0 14.9658 1.72511 14.9658 4.46358ZM12.8049 4.46368C12.8049 2.96777 11.8248 1.85791 10.4078 1.85791C8.99072 1.85791 8.02242 2.96777 8.02242 4.46368C8.02242 6.00783 9.00253 7.11769 10.4078 7.11769C11.813 7.11769 12.8049 6.00783 12.8049 4.46368ZM25.7192 8.50559C25.7192 8.6518 25.6 8.77032 25.4529 8.77032H20.6296C17.9727 8.77032 16.166 7.18998 16.166 4.39119C16.166 1.79749 18.1144 0 20.7005 0C23.2984 0 25.1996 1.78543 25.1996 4.355C25.1996 5.65788 24.5619 6.65917 23.9597 7.12966V7.16585L25.4474 7.13525C25.5966 7.13218 25.7192 7.25158 25.7192 7.39993V8.50559ZM23.0623 4.41566C23.0623 2.93182 22.094 1.87022 20.7006 1.87022C19.3544 1.87022 18.3389 2.93182 18.3389 4.41566C18.3389 5.94775 19.3072 7.00936 20.6888 7.00936C22.0586 7.00936 23.0623 5.94775 23.0623 4.41566ZM34.1106 5.54978C34.1106 7.60061 32.6936 9 30.509 9C28.3126 9 26.9428 7.60061 26.9428 5.54978V0.494414C26.9428 0.348203 27.062 0.229676 27.209 0.229676H28.684C28.8311 0.229676 28.9503 0.348203 28.9503 0.494414V5.38088C28.9503 6.29773 29.3872 7.14219 30.5208 7.14219C31.6662 7.14219 32.0913 6.29773 32.0913 5.38088V0.494414C32.0913 0.348203 32.2105 0.229676 32.3576 0.229676H33.8444C33.9914 0.229676 34.1106 0.348203 34.1106 0.494414V5.54978ZM42.2308 8.50605C42.2308 8.65226 42.1116 8.77079 41.9646 8.77079H35.9078C35.7607 8.77079 35.6416 8.65226 35.6416 8.50605V7.07736C35.6416 7.01777 35.6618 6.95993 35.6989 6.91318L39.2801 2.40783C39.4181 2.23424 39.2938 1.97891 39.0713 1.97891H36.0022C35.8552 1.97891 35.736 1.86039 35.736 1.71418V0.494414C35.736 0.348203 35.8552 0.229676 36.0022 0.229676H41.8819C42.0289 0.229676 42.1481 0.348203 42.1481 0.494414V1.79009C42.1481 1.84987 42.1278 1.90789 42.0904 1.9547L38.4169 6.55601C38.2784 6.7295 38.4026 6.98536 38.6254 6.98536H41.9646C42.1116 6.98536 42.2308 7.10389 42.2308 7.2501V8.50605Z" fill="currentColor" />
          </symbol>
          <symbol id="foquz-logo-sm" width="12" height="19" viewBox="0 0 12 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3238 3.44366C11.2601 3.7064 11.0238 3.89155 10.752 3.89155H4.40935V7.7831H10.3008L9.3578 11.5414H4.40935V18.8713H0V0H11.4115C11.7921 0 12.0725 0.354016 11.9834 0.721973L11.3238 3.44366Z" fill="currentColor"></path>
          </symbol>
        </svg>
      </div>
      <svg class="fc-widget-preview-logo fc-widget-preview-logo--lg" ${buttonType != 0 ? 'width="19" height="19"' : 'width="43" height="9"'} style="${buttonType != 0 ? "margin-left: 8px;margin-right: 8px;" : "margin-left: 12px;"}">
        ${buttonType != 0 ? '<use xlink:href="#foquz-logo-xs-icon" href="#foquz-logo-xs-icon"></use>' : position == 0 ? '<use xlink:href="#foquz-logo-xs" href="#foquz-logo-xs"></use>' : '<use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>'}
      </svg>
    </div>
    </span>`;
    const elLeft = `<span type="button" class="fc-widget-preview-button" style="font-family: ${buttonStyle.font}; font-size: ${buttonStyle.fontSize}px; color: ${buttonStyle.textColor}; background-color: ${buttonStyle.backgroundColor}; ${buttonStyle.stroke == 1 ? "border-width: 1px; border-style: solid; border-color: currentcolor;" : "border: none"} font-weight: ${buttonStyle.bold == 1 ? "bold" : "normal"}; font-style: ${buttonStyle.italic == 1 ? "italic" : "normal"};border-radius: 0 0 6px 6px;overflow: hidden; box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25); display: flex;align-items: center;line-height: 1;min-height: 35px;padding: ${buttonType == 0 ? "0px 15px" : "0"};white-space: nowrap;cursor: pointer;">
    <div class="fc-widget-preview-text" style="display: flex;align-items: center;">
      <svg class="fc-widget-preview-logo fc-widget-preview-logo--lg" ${buttonType != 0 ? 'width="19" height="19"' : 'width="12" height="19"'} style="transform: rotate(90deg); ${buttonType == 0 ? "margin-right: 12px" : "margin-left: 8px;margin-right: 8px;"}">
        ${buttonType != 0 ? '<use xlink:href="#foquz-logo-xs-icon" href="#foquz-logo-xs-icon"></use>' : position == 0 ? '<use xlink:href="#foquz-logo-xs" href="#foquz-logo-xs"></use>' : '<use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>'}
      </svg>
      <span style="${buttonType != 0 ? "display: none" : ""}">${buttonText || "Пройти опрос"}</span>
      <div style="display: none">
        <svg>
          <symbol id="foquz-logo-xs-icon" class="quiz" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.31852 17.9999H2.72741C2.26927 17.9999 1.8299 17.8208 1.50595 17.502C1.18199 17.1832 1 16.7508 1 16.2999V10.3499C1 9.89907 1.18199 9.46668 1.50595 9.14787C1.8299 8.82906 2.26927 8.64995 2.72741 8.64995H5.31852M11.3645 6.94996V3.54998C11.3645 2.87369 11.0915 2.22509 10.6055 1.74687C10.1196 1.26866 9.46055 1 8.77334 1L5.31852 8.64995V17.9999H15.0611C15.4777 18.0045 15.882 17.8608 16.1994 17.5953C16.5168 17.3298 16.7261 16.9603 16.7885 16.5549L17.9804 8.90495C18.018 8.6613 18.0013 8.41253 17.9315 8.17587C17.8617 7.9392 17.7404 7.72031 17.5761 7.53435C17.4118 7.34838 17.2084 7.1998 16.9799 7.0989C16.7514 6.99799 16.5034 6.94717 16.253 6.94996H11.3645Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </symbol>
          <symbol id="foquz-logo-xs" width="43" height="9" viewBox="0 0 43 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.12512 1.78826C5.0963 1.90718 4.98932 1.99098 4.86632 1.99098H1.99566V3.75228H4.66213L4.23531 5.45327H1.99566V8.77079H0V0.229676H5.16483C5.33709 0.229676 5.46399 0.389902 5.42363 0.556438L5.12512 1.78826ZM14.9658 4.46358C14.9658 7.16585 13.0292 8.99953 10.4077 8.99953C7.798 8.99953 5.86139 7.16585 5.86139 4.46358C5.86139 1.72511 7.798 0 10.4077 0C13.0292 0 14.9658 1.72511 14.9658 4.46358ZM12.8049 4.46368C12.8049 2.96777 11.8248 1.85791 10.4078 1.85791C8.99072 1.85791 8.02242 2.96777 8.02242 4.46368C8.02242 6.00783 9.00253 7.11769 10.4078 7.11769C11.813 7.11769 12.8049 6.00783 12.8049 4.46368ZM25.7192 8.50559C25.7192 8.6518 25.6 8.77032 25.4529 8.77032H20.6296C17.9727 8.77032 16.166 7.18998 16.166 4.39119C16.166 1.79749 18.1144 0 20.7005 0C23.2984 0 25.1996 1.78543 25.1996 4.355C25.1996 5.65788 24.5619 6.65917 23.9597 7.12966V7.16585L25.4474 7.13525C25.5966 7.13218 25.7192 7.25158 25.7192 7.39993V8.50559ZM23.0623 4.41566C23.0623 2.93182 22.094 1.87022 20.7006 1.87022C19.3544 1.87022 18.3389 2.93182 18.3389 4.41566C18.3389 5.94775 19.3072 7.00936 20.6888 7.00936C22.0586 7.00936 23.0623 5.94775 23.0623 4.41566ZM34.1106 5.54978C34.1106 7.60061 32.6936 9 30.509 9C28.3126 9 26.9428 7.60061 26.9428 5.54978V0.494414C26.9428 0.348203 27.062 0.229676 27.209 0.229676H28.684C28.8311 0.229676 28.9503 0.348203 28.9503 0.494414V5.38088C28.9503 6.29773 29.3872 7.14219 30.5208 7.14219C31.6662 7.14219 32.0913 6.29773 32.0913 5.38088V0.494414C32.0913 0.348203 32.2105 0.229676 32.3576 0.229676H33.8444C33.9914 0.229676 34.1106 0.348203 34.1106 0.494414V5.54978ZM42.2308 8.50605C42.2308 8.65226 42.1116 8.77079 41.9646 8.77079H35.9078C35.7607 8.77079 35.6416 8.65226 35.6416 8.50605V7.07736C35.6416 7.01777 35.6618 6.95993 35.6989 6.91318L39.2801 2.40783C39.4181 2.23424 39.2938 1.97891 39.0713 1.97891H36.0022C35.8552 1.97891 35.736 1.86039 35.736 1.71418V0.494414C35.736 0.348203 35.8552 0.229676 36.0022 0.229676H41.8819C42.0289 0.229676 42.1481 0.348203 42.1481 0.494414V1.79009C42.1481 1.84987 42.1278 1.90789 42.0904 1.9547L38.4169 6.55601C38.2784 6.7295 38.4026 6.98536 38.6254 6.98536H41.9646C42.1116 6.98536 42.2308 7.10389 42.2308 7.2501V8.50605Z" fill="currentColor" />
          </symbol>
          <symbol id="foquz-logo-sm" width="12" height="19" viewBox="0 0 12 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3238 3.44366C11.2601 3.7064 11.0238 3.89155 10.752 3.89155H4.40935V7.7831H10.3008L9.3578 11.5414H4.40935V18.8713H0V0H11.4115C11.7921 0 12.0725 0.354016 11.9834 0.721973L11.3238 3.44366Z" fill="currentColor"></path>
          </symbol>
        </svg>
      </div>
    </div>
    </span>`;
    const elRight = `<span type="button" class="fc-widget-preview-button" style="font-family: ${buttonStyle.font}; font-size: ${buttonStyle.fontSize}px; color: ${buttonStyle.textColor}; background-color: ${buttonStyle.backgroundColor}; ${buttonStyle.stroke == 1 ? "border-width: 1px; border-style: solid; border-color: currentcolor;" : "border: none"} font-weight: ${buttonStyle.bold == 1 ? "bold" : "normal"}; font-style: ${buttonStyle.italic == 1 ? "italic" : "normal"};border-radius: 0 0 6px 6px;overflow: hidden; box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25); display: flex;align-items: center;line-height: 1;min-height: 35px;padding: ${buttonType == 0 ? "0px 15px" : "0"};white-space: nowrap;cursor: pointer;">
    <div class="fc-widget-preview-text" style="display: flex;align-items: center;">
      <span style="${buttonType != 0 ? "display: none" : ""}">${buttonText || "Пройти опрос"}</span>
      <div style="display: none">
        <svg>
          <symbol id="foquz-logo-xs-icon" class="quiz" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.31852 17.9999H2.72741C2.26927 17.9999 1.8299 17.8208 1.50595 17.502C1.18199 17.1832 1 16.7508 1 16.2999V10.3499C1 9.89907 1.18199 9.46668 1.50595 9.14787C1.8299 8.82906 2.26927 8.64995 2.72741 8.64995H5.31852M11.3645 6.94996V3.54998C11.3645 2.87369 11.0915 2.22509 10.6055 1.74687C10.1196 1.26866 9.46055 1 8.77334 1L5.31852 8.64995V17.9999H15.0611C15.4777 18.0045 15.882 17.8608 16.1994 17.5953C16.5168 17.3298 16.7261 16.9603 16.7885 16.5549L17.9804 8.90495C18.018 8.6613 18.0013 8.41253 17.9315 8.17587C17.8617 7.9392 17.7404 7.72031 17.5761 7.53435C17.4118 7.34838 17.2084 7.1998 16.9799 7.0989C16.7514 6.99799 16.5034 6.94717 16.253 6.94996H11.3645Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </symbol>
          <symbol id="foquz-logo-xs" width="43" height="9" viewBox="0 0 43 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.12512 1.78826C5.0963 1.90718 4.98932 1.99098 4.86632 1.99098H1.99566V3.75228H4.66213L4.23531 5.45327H1.99566V8.77079H0V0.229676H5.16483C5.33709 0.229676 5.46399 0.389902 5.42363 0.556438L5.12512 1.78826ZM14.9658 4.46358C14.9658 7.16585 13.0292 8.99953 10.4077 8.99953C7.798 8.99953 5.86139 7.16585 5.86139 4.46358C5.86139 1.72511 7.798 0 10.4077 0C13.0292 0 14.9658 1.72511 14.9658 4.46358ZM12.8049 4.46368C12.8049 2.96777 11.8248 1.85791 10.4078 1.85791C8.99072 1.85791 8.02242 2.96777 8.02242 4.46368C8.02242 6.00783 9.00253 7.11769 10.4078 7.11769C11.813 7.11769 12.8049 6.00783 12.8049 4.46368ZM25.7192 8.50559C25.7192 8.6518 25.6 8.77032 25.4529 8.77032H20.6296C17.9727 8.77032 16.166 7.18998 16.166 4.39119C16.166 1.79749 18.1144 0 20.7005 0C23.2984 0 25.1996 1.78543 25.1996 4.355C25.1996 5.65788 24.5619 6.65917 23.9597 7.12966V7.16585L25.4474 7.13525C25.5966 7.13218 25.7192 7.25158 25.7192 7.39993V8.50559ZM23.0623 4.41566C23.0623 2.93182 22.094 1.87022 20.7006 1.87022C19.3544 1.87022 18.3389 2.93182 18.3389 4.41566C18.3389 5.94775 19.3072 7.00936 20.6888 7.00936C22.0586 7.00936 23.0623 5.94775 23.0623 4.41566ZM34.1106 5.54978C34.1106 7.60061 32.6936 9 30.509 9C28.3126 9 26.9428 7.60061 26.9428 5.54978V0.494414C26.9428 0.348203 27.062 0.229676 27.209 0.229676H28.684C28.8311 0.229676 28.9503 0.348203 28.9503 0.494414V5.38088C28.9503 6.29773 29.3872 7.14219 30.5208 7.14219C31.6662 7.14219 32.0913 6.29773 32.0913 5.38088V0.494414C32.0913 0.348203 32.2105 0.229676 32.3576 0.229676H33.8444C33.9914 0.229676 34.1106 0.348203 34.1106 0.494414V5.54978ZM42.2308 8.50605C42.2308 8.65226 42.1116 8.77079 41.9646 8.77079H35.9078C35.7607 8.77079 35.6416 8.65226 35.6416 8.50605V7.07736C35.6416 7.01777 35.6618 6.95993 35.6989 6.91318L39.2801 2.40783C39.4181 2.23424 39.2938 1.97891 39.0713 1.97891H36.0022C35.8552 1.97891 35.736 1.86039 35.736 1.71418V0.494414C35.736 0.348203 35.8552 0.229676 36.0022 0.229676H41.8819C42.0289 0.229676 42.1481 0.348203 42.1481 0.494414V1.79009C42.1481 1.84987 42.1278 1.90789 42.0904 1.9547L38.4169 6.55601C38.2784 6.7295 38.4026 6.98536 38.6254 6.98536H41.9646C42.1116 6.98536 42.2308 7.10389 42.2308 7.2501V8.50605Z" fill="currentColor" />
          </symbol>
          <symbol id="foquz-logo-sm" width="12" height="19" viewBox="0 0 12 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3238 3.44366C11.2601 3.7064 11.0238 3.89155 10.752 3.89155H4.40935V7.7831H10.3008L9.3578 11.5414H4.40935V18.8713H0V0H11.4115C11.7921 0 12.0725 0.354016 11.9834 0.721973L11.3238 3.44366Z" fill="currentColor"></path>
          </symbol>
        </svg>
      </div>
      <svg class="fc-widget-preview-logo fc-widget-preview-logo--lg" ${buttonType != 0 ? 'width="19" height="19"' : 'width="12" height="19"'} style="transform: rotate(-90deg); ${buttonType == 0 ? "margin-left: 12px" : "margin-left: 8px;margin-right: 8px;"}">
        ${buttonType != 0 ? '<use xlink:href="#foquz-logo-xs-icon" href="#foquz-logo-xs-icon"></use>' : position == 0 ? '<use xlink:href="#foquz-logo-xs" href="#foquz-logo-xs"></use>' : '<use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>'}
      </svg>
    </div>
    </span>`;
    const elWrapper = document.createElement("button");
    elWrapper.style.border = "none";
    elWrapper.style.padding = 0;
    elWrapper.style.outline = "none";
    elWrapper.style.background = "transparent";
    if (position != 0) {
      elWrapper.style.position = "fixed";
      elWrapper.style.zIndex = "10000";
      elWrapper.style.top = "50%";
      elWrapper.style.position = "fixed";
      elWrapper.style.transform = "translateY(-50%)";
      if (position == 1) {
        elWrapper.style.left = "0px";
        elWrapper.style.transform = "translateY(150%) rotate(-90deg)";
        elWrapper.style.transformOrigin = "left top";
      } else {
        elWrapper.style.right = "0px";
        elWrapper.style.transform = "translateY(150%) rotate(90deg)";
        elWrapper.style.transformOrigin = "right top";
      }
    }
    elWrapper.addEventListener("click", function (e) {
      e.preventDefault();
      if (self.appearance === WIDGET_APPEARANCE.WITH_CLICK) {
        self.renderModalWidget();
      } else {
        self.isActive.val = true;
      }
    });
    if (position == WIDGET_BUTTON_POSITION.STATIC) {
      elWrapper.innerHTML = el;
    }
    if (position == WIDGET_BUTTON_POSITION.FIXED_LEFT) {
      elWrapper.innerHTML = elLeft;
    }

    if (position == WIDGET_BUTTON_POSITION.FIXED_RIGHT) {
      elWrapper.innerHTML = elRight;
    }

    const isStaticButtonPosition = position == WIDGET_BUTTON_POSITION.STATIC;
    const time = this.widgetData.time || 0;
    if (settings.show) {
      setTimeout(() => {
        if (config.id_parent_element && isStaticButtonPosition) {
          const target = document.getElementById(config.id_parent_element);
          if (target) {
            target.innerHTML = "";
            target.appendChild(elWrapper);
          } else {
            document.body.appendChild(elWrapper);
          }
        } else {
          document.body.appendChild(elWrapper);
        }
      }, time * 1000);
    }
  }

  getWidgetPositionClasses() {
    const positionMap = {
      1: styles["fc-widget-pos-bottom-left"],
      2: styles["fc-widget-pos-bottom-right"],
    };

    return positionMap[this.position] || positionMap[1];
  }

  async updatePollStatus() {
    const pollData = this.pollData.val;
    const pollStatus = pollData.answer?.status;

    if (pollStatus && pollStatus !== "open") {
      return;
    }

    try {
      await api.updatePollStatus({
        authKey: pollData.answer.auth_key,
        key: this.widgetKey,
        status: "open",
        assetsRoot: this.config.assetsRoot,
      });
    } catch (error) {
      console.error("Failed to update poll status:", error);
    }
  }

  handleRatingChange(value, questionId, answer) {
    const pollData = this.pollData.val;
    const config = this.config;
    const authKey = pollData.answer.auth_key;
    const configWithAuthKey = {
      ...config,
      authKey,
    };
    const questionIndex = this.pollData.val.questions.findIndex(
      (question) => question.id === questionId,
    );

    this.pollData.val.questions[questionIndex]._answer = value;
    answer.value.val = value;
    this.renderModalWidget();
    saveAnswerByType(
      this.pollData.val.questions[questionIndex],
      answer,
      configWithAuthKey,
    );
  }

  appendRatingScale() {
    const self = this;

    const questionsWithoutStartBlock = this.pollData.val.questions.filter(
      (question) => {
        if (
          question.type === QUESTION_TYPES.INTERMEDIATE_BLOCK &&
          question.intermediateBlock?.screen_type ===
            INTERMEDIATE_BLOCK_TYPES.START
        ) {
          return false;
        }
        return true;
      },
    );

    const isRatingQuestion = (question) =>
      question.type === QUESTION_TYPES.STAR_RATING ||
      question.type === QUESTION_TYPES.SMILE_RATING ||
      question.type === QUESTION_TYPES.RATING ||
      question.type === QUESTION_TYPES.RATING_NPS;

    const firstRatingQuestion = questionsWithoutStartBlock?.[0];

    if (!firstRatingQuestion || !isRatingQuestion(firstRatingQuestion)) return;

    const firstRatingQuestionId = firstRatingQuestion.id;

    const answer = getAnswerStateByType(firstRatingQuestion);

    let ratingComponent;
    const commonProps = {
      questionId: firstRatingQuestionId,
      vModel: answer.value,
      isUnrequired: true,
      isSkipped: answer.isSkipped,
      isDisabled: van.state(false),
      errors: answer.errors,
      view: "large",
      assessments: {
        enabled: false,
      },
      comment: {
        enabled: false,
      },
      onChange: (value) => {
        // render modal widget
        this.handleRatingChange(value, firstRatingQuestionId, answer);

        setTimeout(() => {
          answer.value.val = null;
        }, 1000);
      },
    };

    const alwaysShowLabels = firstRatingQuestion.show_labels === 1;

    // if type is smile-rating, map smiles and remove the labelsArray
    if (
      firstRatingQuestion.type === QUESTION_TYPES.SMILE_RATING &&
      !alwaysShowLabels
    ) {
      firstRatingQuestion.smiles = firstRatingQuestion.smiles.map((smile) => ({
        ...smile,
        label: null,
      }));
    }

    switch (firstRatingQuestion.type) {
      case QUESTION_TYPES.STAR_RATING:
        ratingComponent = QuestionStarRating({
          ...commonProps,
          count: firstRatingQuestion.starRatingOptions.count,
          size: "md",
          thin: false,
          labelsArray: alwaysShowLabels
            ? firstRatingQuestion.starRatingOptions.labelsArray || []
            : [],
          color: firstRatingQuestion.starRatingOptions.color,
          alwaysShowLabels,
          showNumbers: firstRatingQuestion.show_numbers === 1,
        });
        break;
      case QUESTION_TYPES.SMILE_RATING:
        ratingComponent = QuestionSmileRating({
          ...commonProps,
          assetsRoot: this.config.assetsRoot,
          smiles: firstRatingQuestion.smiles,
          count: firstRatingQuestion.smiles?.length,
          smileType: firstRatingQuestion.smileType,
          showLabels: alwaysShowLabels,
          alwaysShowLabels,
        });
        break;
      case QUESTION_TYPES.RATING:
        ratingComponent = QuestionRating({
          ...commonProps,
          count: firstRatingQuestion.starRatingOptions.count,
          labelsArray: [],
          color: firstRatingQuestion.starRatingOptions.color,
        });
        break;
      case QUESTION_TYPES.RATING_NPS:
        ratingComponent = QuestionRatingNps({
          ...commonProps,
          fromOne: firstRatingQuestion.fromOne,
          npsRatingSetting: {
            design: firstRatingQuestion.npsRatingSetting.design,
            startColor: firstRatingQuestion.npsRatingSetting.start_point_color,
            endColor: firstRatingQuestion.npsRatingSetting.end_point_color,
            startLabel: firstRatingQuestion.npsRatingSetting.start_label,
            endLabel: firstRatingQuestion.npsRatingSetting.end_label,
          },
        });
        break;
    }

    const questionItemStyleString = `font-family: ${this.buttonStyle.font}; color: ${this.buttonStyle.textColor}; --fqz-widget-text-font-size: ${this.buttonStyle.fontSize}px; --fqz-widget-title-font-size: ${this.buttonStyle.fontSize}px; --fqz-widget-rating-scale-font-size: 15px; --fqz-widget-question-item-header-margin-bottom: 20px;`;

    const descriptionStyle = {
      "font-weight": self.buttonStyle.bold ? "bold" : "normal",
      "font-style": self.buttonStyle.italic ? "italic" : "normal",
    };

    // render descriptionStyle as a string
    const descriptionStyleString = Object.entries(descriptionStyle)
      .map(([key, value]) => `${key}: ${value}`)
      .join("; ");

    const questionItem = QuestionItem({
      headerProps: {
        description: firstRatingQuestion.description_html,
        subdescription: "",
        isUnrequired: !firstRatingQuestion.isRequired,
        unrequiredText: null,
        enabled: true,
        descriptionStyle: descriptionStyleString,
      },
      questionElement: ratingComponent,
      view: "large",
    });

    const container = van.tags.div(
      {
        class: styles["fc-widget-rating-scale-container"],
        style: questionItemStyleString,
      },
      questionItem,
    );

    let targetElement = document.body;

    if (
      this.config.id_parent_element &&
      document.getElementById(this.config.id_parent_element)
    ) {
      targetElement = document.getElementById(this.config.id_parent_element);
    }

    if (targetElement) {
      van.add(targetElement, container);
    }
  }

  /**
   * Gets the scroll depth trigger configuration from widget data
   * @param {Object} widgetData - The widget data object
   * @returns {Object|null} - The scroll depth trigger configuration or null if not configured
   */
  getScrollDepthTrigger(widgetData) {
    return FQZWidgetClass.getScrollDepthTrigger(widgetData);
  }

  /**
   * Sets up the scroll depth trigger
   * @param {Object} trigger - The trigger configuration
   * @param {Function} onTrigger - The callback to execute when the trigger condition is met
   * @returns {Function} - Cleanup function to remove listeners
   */
  setupScrollDepthTrigger(trigger, onTrigger) {
    return FQZWidgetClass.setupScrollDepthTrigger(trigger, onTrigger);
  }

  async initialize(widgetData) {
    if (!widgetData.link) {
      return;
    }

    this.initWidgetAppearance(widgetData);

    // Get scroll depth trigger configuration

    const scrollDepthTrigger = this.getScrollDepthTrigger(widgetData);

    if (this.appearance === WIDGET_APPEARANCE.WITHOUT_CLICK) {
      // Without click
      await this.fetchPollData();

      if (this.pollData.val) {
        if (scrollDepthTrigger) {
          // Setup scroll depth trigger
          this.scrollDepthCleanup = this.setupScrollDepthTrigger(
            scrollDepthTrigger,
            () => {
              if (this.form === WIDGET_FORM.HELLO_BOARD) {
                this.renderHelloBoardWidget();
              } else if (this.form === WIDGET_FORM.SIMPLIFIED_PAGE_STOP) {
                this.renderModalWidget();
              }
              this.updatePollStatus();
              if (this.scrollDepthCleanup) {
                this.scrollDepthCleanup();
              }
            },
          );
        } else {
          if (this.form === WIDGET_FORM.HELLO_BOARD) {
            this.renderHelloBoardWidget();
          } else if (this.form === WIDGET_FORM.SIMPLIFIED_PAGE_STOP) {
            this.renderModalWidget();
          }
          this.updatePollStatus();
        }
      }
    } else {
      await this.fetchPollData();

      if (scrollDepthTrigger) {
        // Setup scroll depth trigger for click mode
        this.scrollDepthCleanup = this.setupScrollDepthTrigger(
          scrollDepthTrigger,
          () => {
            if (this.form === WIDGET_FORM.RATING_SCALE) {
              this.appendRatingScale();
            } else {
              this.appendButton({
                position: this.position,
                appearance: this.appearance,
                buttonType: this.buttonType,
                buttonText: this.buttonText,
                widgetData: this.widgetData,
                buttonStyle: this.buttonStyle,
                show: true,
              });
            }

            // Send show status if time is defined
            if (this.widgetData.time) {
              this.updatePollStatus();
            }
          },
        );
      } else {
        setTimeout(() => {
          // With click
          if (this.form === WIDGET_FORM.RATING_SCALE) {
            this.appendRatingScale();
          } else {
            this.appendButton({
              position: this.position,
              appearance: this.appearance,
              buttonType: this.buttonType,
              buttonText: this.buttonText,
              widgetData: this.widgetData,
              buttonStyle: this.buttonStyle,
              show: !!this.widgetData.link,
            });
          }
        }, 10);
      }
    }
  }

  async renderModalWidget() {
    const modalContainerElem = document.querySelector(
      `.${styles["fc-widget-modal-container"]}`,
    );

    if (modalContainerElem) {
      modalContainerElem.style.display = "block";

      setTimeout(() => {
        modalContainerElem.classList.remove(
          styles["fc-widget-modal-container--hidden"],
        );

        this.dispatchCustomEvent("foquz::shown");
      }, 10);

      return;
    }
    const pollData = this.pollData;

    const config = this.config;

    const widgetClasses = van.derive(() => {
      const classes = [styles["fc-widget"], styles["fc-widget--modal"]];
      if (!pollData.val) {
        classes.push(styles["fc-widget--loading"]);
      }
      return classes.join(" ");
    });

    const widgetStyles = van.derive(() =>
      pollData.val
        ? getCssVariables(pollData.val.design, config.assetsRoot)
        : "",
    );

    const modalContainer = div(
      {
        class: `${styles["fc-widget-modal-container"]} ${styles["fc-widget-modal-container--hidden"]}`,
      },
      div(
        {
          class: styles["fc-widget-modal-container__scroll-area"],
        },
        div(
          { class: styles["fc-widget-modal-container__scroll-area-inner"] },
          div({
            class: styles["fc-widget-modal-overlay"],
            onclick: () => this.closeModalWidget(),
          }),
          div(
            {
              class: `${styles["fc-widget-modal"]} ${styles["fc-widget-modal--hidden"]}`,
            },
            div(
              {
                class: () => widgetClasses.val,
                style: () => widgetStyles.val,
                "data-testid": "fc-widget",
              },
              this.renderWidgetCloseButton(() => this.closeModalWidget()),
              this.renderModalWidgetContent(),
              this.renderBackgroundElements(),
              this.renderWidgetCopyright(),
              this.renderWidgetLoader(),
            ),
          ),
        ),
      ),
    );

    van.add(document.body, modalContainer);

    setTimeout(() => {
      modalContainer.classList.remove(
        styles["fc-widget-modal-container--hidden"],
      );
      modalContainer
        .querySelector(`.${styles["fc-widget-modal"]}`)
        .classList.remove(styles["fc-widget-modal--hidden"]);
      this.dispatchCustomEvent("foquz::shown");
    }, 10);
  }

  renderQuestionsForm() {
    const pollData = this.pollData;
    const config = this.config;
    const widgetView = this.widgetView;
    const initCustomScrollbar = this.initCustomScrollbar;

    if (!this.questionsFormElement) {
      this.questionsFormElement = QuestionsForm(pollData.val, {
        widgetView,
        initCustomScrollbar,
        widgetKey: this.widgetKey,
        view: "large",
        config,
        closeByFinishButton: this.closeByFinishButton,
        onCloseWidgetButtonClick: () => this.closeModalWidget(),
        onFormSuccess: () => {
          this.dispatchCustomEvent("foquz::completed");
        },
      });
    }

    return this.questionsFormElement;
  }

  renderModalWidgetContent() {
    const pollData = this.pollData;
    const isCssLoaded = this.isCssLoaded;

    return div(
      {
        class: styles["fc-widget-content"],
      },
      div({ class: styles["fc-widget-content__wrapper"] }, () =>
        pollData.val && isCssLoaded.val
          ? this.renderQuestionsForm()
          : div({ style: "display: none" }),
      ),
    );
  }

  renderWidgetLoader() {
    const pollData = this.pollData;

    const loaderClasses = van.derive(() => {
      const classes = [styles["fc-widget-loader"]];

      if (pollData.val) {
        classes.push(styles["fc-widget-loader--hidden"]);
      }

      return classes.join(" ");
    });

    const loaderElem = div({ class: () => loaderClasses.val }, span());

    van.derive(() => {
      if (pollData.val) {
        setTimeout(() => {
          loaderElem.remove();
        }, this.transitionDuration);
      }
    });

    return loaderElem;
  }

  closeModalWidget() {
    const modalContainer = document.querySelector(
      `.${styles["fc-widget-modal-container"]}`,
    );
    if (modalContainer) {
      modalContainer.classList.add(styles["fc-widget-modal-container--hidden"]);

      this.isModalActive.val = false;

      setTimeout(() => {
        modalContainer.style.display = "none";
        this.dispatchCustomEvent("foquz::hidden");
      }, this.transitionDuration);
    }
  }

  isWidgetTriggeredByEvent(widgetData) {
    const triggers = widgetData.widget?.triggers;
    const isActive = triggers?.event.time?.active === "1";
    return isActive;
  }

  isWidgetTriggeredByTrackHistory(widgetData) {
    return widgetData.history;
  }

  getEventTime(widgetData) {
    const triggers = widgetData.widget?.triggers;
    const time = triggers?.event.time?.value;
    return time ? parseInt(time) : 0;
  }

  renderHelloBoardWidget() {
    const pollData = this.pollData.val;
    const positionClassName = this.getWidgetPositionClasses();
    const renderWidget = (children) =>
      div(
        {
          class: () =>
            `${styles["fc-widget"]}${this.isActive.val && this.isCssLoaded.val ? " " + styles["shown"] : ""} ${positionClassName}`,
          style: () => getCssVariables(pollData.design, this.config.assetsRoot),
          "data-testid": "fc-widget",
        },
        children,
      );

    let time = this.widgetData.time || 0;

    setTimeout(
      () => {
        this.isActive.val = true;
        this.dispatchCustomEvent("foquz::shown");
      },
      10 + time * 1000,
    );

    const onWidgetCloseClick = (e) => {
      e?.preventDefault?.();
      this.isActive.val = false;
      setTimeout(() => {
        document.body.removeChild(
          document.querySelector(`.${styles["fc-widget"]}`),
        );
        this.dispatchCustomEvent("foquz::hidden");
      }, this.transitionDuration);
    };

    let widgetContent = div(
      {
        class: () => styles["fc-widget-content"],
      },
      QuestionsForm(pollData, {
        widgetKey: this.widgetKey,
        view: "default",
        closeByFinishButton: this.closeByFinishButton,
        initCustomScrollbar: this.initCustomScrollbar,
        config: this.config,
        onFormSuccess: () => {
          this.dispatchCustomEvent("foquz::completed");
        },
        onCloseWidgetButtonClick: onWidgetCloseClick,
      }),
    );

    const initialWidgetTree = div(
      this.renderWidgetCloseButton(onWidgetCloseClick),
      widgetContent,
      ...this.renderBackgroundElements(),
      this.renderWidgetCopyright(),
    );

    this.widgetElement = renderWidget(initialWidgetTree);
    van.add(document.body, this.widgetElement);
  }

  renderPageStop() {
    // Implement Page-stop rendering logic here
    return div("Page-stop content");
  }

  renderSimplifiedPageStop() {
    // Implement Simplified Page-stop rendering logic here
    return div("Simplified Page-stop content");
  }

  onWidgetCloseClick() {
    // this.close();
  }

  renderWidgetCopyright() {
    const widgetView = this.widgetView;
    const pollData = this.pollData.val;

    if (pollData?.showFoquzLabel === false) {
      return null;
    }

    const copyrightLink = (svgContent) =>
      a(
        {
          href: "https://foquz.ru/",
          target: "_blank",
          rel: "noopener noreferrer",
          class: styles["fc-widget-copyright-link"],
        },
        svgContent,
      );

    const defaultCopyrightElem = copyrightLink(
      svg(
        {
          class: styles["fc-widget-copyright"],
          width: 45,
          height: 10,
          viewBox: "0 0 47 12",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg",
        },
        path({
          d: "M5.96783 4.5931H3.62651V3.67158H6.02539C6.4819 3.67158 6.87857 3.35784 6.98371 2.9136L7.20902 1.96169C7.35541 1.34319 6.88628 0.750061 6.25069 0.750061H1H0.5V1.25006V10.5681V11.0681H1H3.12651H3.62651V10.5681V7.44882H5.51303H5.90528L5.99865 7.06784L6.45346 5.21212L6.60517 4.5931H5.96783ZM12.09 11.3182C13.609 11.3182 14.9541 10.7724 15.9203 9.80225C16.8869 8.83163 17.447 7.46374 17.447 5.86961C17.447 4.25631 16.8871 2.90476 15.9152 1.95657C14.9453 1.01033 13.6002 0.5 12.09 0.5C10.5855 0.5 9.24358 1.0107 8.27548 1.95689C7.30543 2.90498 6.74556 4.25634 6.74556 5.86961C6.74556 7.46372 7.30558 8.83142 8.27036 9.80193C9.23471 10.772 10.5767 11.3182 12.09 11.3182ZM12.09 3.52681C13.2695 3.52681 14.1443 4.45616 14.1443 5.86961C14.1443 7.34036 13.2528 8.26505 12.09 8.26505C10.9302 8.26505 10.0482 7.34384 10.0482 5.86961C10.0482 4.45269 10.9135 3.52681 12.09 3.52681ZM27.9207 11.0681C28.4646 11.0681 28.9055 10.6272 28.9055 10.0833V9.27333C28.9055 8.72133 28.4519 8.27712 27.9 8.28875L27.6165 8.29472C28.0406 7.6395 28.3519 6.76609 28.3519 5.75116C28.3519 2.6633 26.0937 0.5 23.0578 0.5C20.0368 0.5 17.7259 2.67413 17.7259 5.79064C17.7259 7.42501 18.2438 8.75844 19.1937 9.68274C20.1413 10.6048 21.4656 11.0681 22.9823 11.0681H27.9207ZM23.0578 3.53997C23.6627 3.53997 24.1555 3.77248 24.5015 4.15624C24.851 4.54383 25.0744 5.11338 25.0744 5.81696C25.0744 7.26181 24.1816 8.1466 23.0452 8.1466C22.4482 8.1466 21.9598 7.91542 21.6152 7.52687C21.2669 7.13395 21.0412 6.5507 21.0412 5.81696C21.0412 4.43129 21.9406 3.53997 23.0578 3.53997ZM33.5097 11.3182C34.7851 11.3182 35.8777 10.8978 36.6532 10.1306C37.4288 9.36319 37.8475 8.28801 37.8475 7.05411V1.73487C37.8475 1.19097 37.4066 0.750061 36.8627 0.750061H35.6806C35.1367 0.750061 34.6958 1.19097 34.6958 1.73487V6.86985C34.6958 7.30499 34.5958 7.66635 34.4163 7.90621C34.2536 8.12383 33.9899 8.29137 33.5223 8.29137C33.0623 8.29137 32.7974 8.12482 32.632 7.90507C32.4508 7.66416 32.3487 7.30237 32.3487 6.86985V1.73487C32.3487 1.19097 31.9078 0.750061 31.3639 0.750061H30.1944C29.6505 0.750061 29.2096 1.19097 29.2096 1.73487V7.05411C29.2096 8.28317 29.6125 9.35829 30.3774 10.1277C31.143 10.8978 32.2281 11.3182 33.5097 11.3182ZM45.5152 11.0681C46.0591 11.0681 46.5 10.6272 46.5 10.0833V9.10508C46.5 8.56119 46.0591 8.12028 45.5152 8.12028H42.5947L46.2037 3.49203L45.8094 3.18457L46.2037 3.49202C46.3386 3.319 46.4119 3.10587 46.4119 2.88645V1.73487C46.4119 1.19097 45.971 0.750061 45.4271 0.750061H39.5642C39.0203 0.750061 38.5794 1.19097 38.5794 1.73487V2.67362C38.5794 3.21751 39.0203 3.65842 39.5642 3.65842H42.198L38.6856 8.18245C38.5515 8.3552 38.4787 8.56768 38.4787 8.78638V10.0833C38.4787 10.6272 38.9196 11.0681 39.4635 11.0681H45.5152Z",
        }),
      ),
    );

    const largeCopyrightElem = copyrightLink(
      svg(
        {
          class: styles["fc-widget-copyright"],
          width: 55,
          height: 12,
          viewBox: "0 0 57 14",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg",
        },
        path({
          fill: "black",
          "fill-opacity": "0.3",
          stroke: "white",
          d: "M7.07179 5.50268H4.09907V4.15416H7.14214C7.64859 4.15416 8.08865 3.80609 8.2053 3.31326L8.48068 2.14981C8.64309 1.46365 8.12263 0.80563 7.41751 0.80563H1H0.5V1.30563V12.6944V13.1944H1H3.59907H4.09907V12.6944V8.77078H6.51592H6.90818L7.00155 8.3898L7.55742 6.1217L7.70913 5.50268H7.07179ZM47.148 9.84669L47.5429 10.1533L47.148 9.84669C46.9992 10.0383 46.9184 10.2741 46.9184 10.5167V12.1018C46.9184 12.7052 47.4076 13.1944 48.011 13.1944H55.4075C56.0109 13.1944 56.5 12.7052 56.5 12.1018V10.9062C56.5 10.3028 56.0109 9.81367 55.4075 9.81367H51.8C51.7229 9.81367 51.6796 9.725 51.727 9.66423L56.1614 3.97749C56.3111 3.78553 56.3923 3.54908 56.3923 3.30567V1.89817C56.3923 1.29478 55.9032 0.80563 55.2998 0.80563H48.134C47.5306 0.80563 47.0415 1.29478 47.0415 1.89817V3.04553C47.0415 3.64892 47.5306 4.13807 48.134 4.13807H51.391C51.468 4.13807 51.5113 4.22656 51.4641 4.28736L47.148 9.84669ZM14.5544 13.5C16.3838 13.5 17.9988 12.8431 19.1572 11.6799C20.3161 10.5163 20.9908 8.87344 20.9908 6.95174C20.9908 5.0065 20.3162 3.38441 19.1521 2.24867C17.9899 1.11489 16.3751 0.5 14.5544 0.5C12.7409 0.5 11.1299 1.11526 9.96991 2.24899C8.80797 3.38463 8.13346 5.00652 8.13346 6.95174C8.13346 8.87342 8.80813 10.516 9.9648 11.6796C11.121 12.8427 12.7322 13.5 14.5544 13.5ZM14.5544 3.97721C16.0695 3.97721 17.1764 5.17564 17.1764 6.95174C17.1764 8.79686 16.05 9.99062 14.5544 9.99062C13.0619 9.99062 11.9478 8.80033 11.9478 6.95174C11.9478 5.17216 13.0424 3.97721 14.5544 3.97721ZM33.9031 13.1944C34.5065 13.1944 34.9956 12.7052 34.9956 12.1018V11.1119C34.9956 10.4995 34.4924 10.0067 33.8801 10.0196L33.3188 10.0314C33.8852 9.23218 34.319 8.11925 34.319 6.80697C34.319 3.09616 31.6106 0.5 27.9595 0.5C24.3262 0.5 21.5539 3.10991 21.5539 6.85523C21.5539 8.82886 22.1788 10.4277 23.3143 11.5326C24.4474 12.6352 26.036 13.1944 27.8672 13.1944H33.9031ZM27.9595 3.9933C28.7294 3.9933 29.362 4.29015 29.8066 4.78322C30.2547 5.28012 30.5353 6.00394 30.5353 6.8874C30.5353 8.70368 29.4049 9.84584 27.9441 9.84584C27.1836 9.84584 26.556 9.55032 26.1133 9.05099C25.6667 8.5473 25.3837 7.80685 25.3837 6.8874C25.3837 5.14199 26.5236 3.9933 27.9595 3.9933ZM40.734 13.5C42.2682 13.5 43.5739 12.9948 44.4979 12.0806C45.4222 11.1663 45.9247 9.88196 45.9247 8.39946V1.89817C45.9247 1.29478 45.4355 0.80563 44.8321 0.80563H43.3874C42.784 0.80563 42.2948 1.29478 42.2948 1.89817V8.17426C42.2948 8.72053 42.1697 9.18865 41.9312 9.50747C41.7093 9.80407 41.3527 10.0228 40.7494 10.0228C40.1553 10.0228 39.7974 9.80506 39.5726 9.50634C39.3319 9.18646 39.204 8.71792 39.204 8.17426V1.89817C39.204 1.29478 38.7149 0.80563 38.1115 0.80563H36.6821C36.0787 0.80563 35.5895 1.29477 35.5895 1.89817V8.39946C35.5895 9.87712 36.0735 11.1614 36.9845 12.0777C37.8962 12.9948 39.1924 13.5 40.734 13.5Z",
        }),
      ),
    );

    return widgetView === "default" ? defaultCopyrightElem : largeCopyrightElem;
  }

  renderBackgroundElements() {
    const pollData = this.pollData;
    const widgetView = this.widgetView;

    const secondaryBgClasses = van.derive(() => {
      if (!pollData.val) {
        return "";
      }

      const pollDataValue = pollData.val;
      const design = pollDataValue.design;
      const isDarkened = design?.darkening_background === 1;
      const withBg = design?.background_image;
      const withMobileBg = design?.mobile_background_image;
      let cls = styles["fc-widget__secondary-bg"];

      if (isDarkened) {
        cls += ` ${styles["fc-widget__secondary-bg--darkened"]}`;
      }

      if (widgetView === "modal") {
        if (withBg) {
          cls += ` ${styles["fc-widget__secondary-bg--with-bg"]}`;
        }
      } else {
        if (withMobileBg) {
          cls += ` ${styles["fc-widget__secondary-bg--with-mobile-bg"]}`;
        } else if (withBg) {
          cls += ` ${styles["fc-widget__secondary-bg--with-bg"]}`;
        }
      }

      return cls;
    });
    return [
      div({
        class: styles["fc-widget__main-bg"],
        "aria-hidden": true,
        "data-testid": "fc-widget-main-bg",
      }),
      div({
        class: () => secondaryBgClasses.val,
        "aria-hidden": true,
        "data-testid": "fc-widget-secondary-bg",
      }),
    ];
  }

  renderWidgetCloseButton(onClose = () => {}) {
    return button(
      {
        class: styles["fc-widget-close"],
        onclick: onClose,
        "data-testid": "fc-widget-close",
      },
      svg(
        {
          width: 10,
          height: 10,
          viewBox: "0 0 12 12",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg",
        },
        path({
          d: "M11 1L1 11M11 11L1 1",
          "stroke-width": 2,
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
        }),
      ),
    );
  }

  open() {}

  removeButtonTriggers() {
    const buttonTriggers = document.querySelectorAll(
      ".fc-widget-preview-button",
    );

    if (buttonTriggers.length > 0) {
      buttonTriggers.forEach((el) => el.remove());
    }
  }

  removeModalWidget() {
    const modalWidgets = document.querySelectorAll(
      `.${styles["fc-widget-modal-container"]}`,
    );
    if (modalWidgets && modalWidgets.length > 0) {
      modalWidgets.forEach((el) => el.remove());
    }
  }

  removeHelloBoardWidget() {
    const helloBoardWidget = document.querySelectorAll(
      `.${styles["fc-widget"]}`,
    );
    if (helloBoardWidget && helloBoardWidget.length > 0) {
      helloBoardWidget.forEach((el) => el.remove());
    }
  }

  destroy() {
    // @TODO: remove rating scales trigger when implemented
    // Remove any button triggers if they exist
    this.removeButtonTriggers();

    // Remove modal widget if exists
    this.removeModalWidget();

    // Remove hello board widget if exists
    this.removeHelloBoardWidget();

    // Cleanup scroll depth trigger if exists
    if (typeof this.scrollDepthCleanup === "function") {
      this.scrollDepthCleanup();
      this.scrollDepthCleanup = null;
    }

    // Clear pollData
    this.pollData.val = null;

    // Reset other properties
    this.isActive.val = false;
    this.isModalActive.val = false;
    this.isLoadingPollData.val = false;

    // Remove the instance reference
    FQZWidgetClass.instance = null;

    // Dispatch the 'foquz::destroyed' event
    this.dispatchCustomEvent("foquz::destroyed");
  }
}

export default function initializeWidget(
  widgetData,
  pollData,
  config,
  initCustomScrollbar,
) {
  destroyExistingWidget();
  if (FQZWidgetClass.instance) {
    FQZWidgetClass.instance.destroy();
  }

  window.FQZWidget = new FQZWidgetClass(
    widgetData,
    pollData,
    config,
    initCustomScrollbar,
  );
}
