import { SCROLL_DEPTH_TYPES } from "./constants";
import { throttle } from "@/helpers";
import styles from "./styles.module.css";

/**
 * Возвращает объект с настройками дизайна по умолчанию для опроса.
 * В некоторых случаях, бэкенд может возвращать ошибку,
 * поэтому нам нужно возвращать какие-то настройки по умолчанию
 * @returns {object} Объект с настройками дизайна
 */
export function getDefaultDesignSettings() {
  const settings = {
    logo_image: ``,
    logo_height: 13,
    logo_link: "https://foquz.ru/",
    font_family: "Arial, Helvetica, sans-serif",
    background_image: `/img/themes/background4.jpg`,
    disabled: false,
    logo_type: "image",
    mobile_background_image: null,
    main_color: "rgb(53, 75, 155)",
    background_color: "rgba(207, 216, 220, 1)",
    header_color: "rgba(0, 0, 0, 0.5)",
    is_use_header: 1,
    main_place_color: "rgba(255, 255, 255, 1)",
    text_on_bg: "rgba(255, 255, 255, 1)",
    text_on_place: "rgba(0, 0, 0, 1)",
    link_color: "rgba(255, 255, 255, 1)",
    logo_text: null,
    choose_language: 1,
    show_prev_button: true,
    unrequired_text: "Необязательный",
  };

  return settings;
}

/**
 * Gets the scroll depth trigger configuration from widget data
 * @param {Object} widgetData - The widget data object
 * @returns {Object|null} - The scroll depth trigger configuration or null if not configured
 */
export function getScrollDepthTrigger(widgetData) {
  const widgetSettings = widgetData?.widget;
  if (!widgetSettings?.triggers?.scroll_depth) {
    return null;
  }

  const result = {};

  if (widgetSettings.triggers.scroll_depth.vertical) {
    const { type, value } = widgetSettings.triggers.scroll_depth.vertical;
    result.vertical = {
      type:
        type === "1" ? SCROLL_DEPTH_TYPES.PIXELS : SCROLL_DEPTH_TYPES.PERCENT,
      value: parseInt(value, 10),
    };
  }

  if (widgetSettings.triggers.scroll_depth.horizontal) {
    const { type, value } = widgetSettings.triggers.scroll_depth.horizontal;
    result.horizontal = {
      type:
        type === "1" ? SCROLL_DEPTH_TYPES.PIXELS : SCROLL_DEPTH_TYPES.PERCENT,
      value: parseInt(value, 10),
    };
  }

  return Object.keys(result).length ? result : null;
}

/**
 * Sets up the scroll depth trigger
 * @param {Object} trigger - The trigger configuration
 * @param {Function} onTrigger - The callback to execute when the trigger condition is met
 * @returns {Function} - Cleanup function to remove listeners
 */
export function setupScrollDepthTrigger(trigger, onTrigger) {
  if (!trigger) return () => {};

  let triggered = false;
  let lastDocHeight = Math.max(
    document.body.scrollHeight,
    document.documentElement.scrollHeight,
  );
  let lastDocWidth = Math.max(
    document.body.scrollWidth,
    document.documentElement.scrollWidth,
  );

  // Track if individual conditions are met
  let verticalConditionMet = !trigger.vertical; // Default to true if not specified
  let horizontalConditionMet = !trigger.horizontal; // Default to true if not specified

  const checkScrollDepth = () => {
    if (triggered) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft =
      window.pageXOffset || document.documentElement.scrollLeft;
    const docHeight = Math.max(
      document.body.scrollHeight,
      document.documentElement.scrollHeight,
    );
    const docWidth = Math.max(
      document.body.scrollWidth,
      document.documentElement.scrollWidth,
    );
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;

    // Update last known document dimensions
    lastDocHeight = docHeight;
    lastDocWidth = docWidth;

    // Check vertical trigger
    if (trigger.vertical && !verticalConditionMet) {
      if (trigger.vertical.type === SCROLL_DEPTH_TYPES.PIXELS) {
        if (scrollTop >= trigger.vertical.value) {
          verticalConditionMet = true;
        }
      } else {
        // percent
        const scrollPercent = (scrollTop / (docHeight - windowHeight)) * 100;
        if (scrollPercent >= trigger.vertical.value) {
          verticalConditionMet = true;
        }
      }
    }

    // Check horizontal trigger
    if (trigger.horizontal && !horizontalConditionMet) {
      if (trigger.horizontal.type === SCROLL_DEPTH_TYPES.PIXELS) {
        if (scrollLeft >= trigger.horizontal.value) {
          horizontalConditionMet = true;
        }
      } else {
        // percent
        const scrollPercent = (scrollLeft / (docWidth - windowWidth)) * 100;
        if (scrollPercent >= trigger.horizontal.value) {
          horizontalConditionMet = true;
        }
      }
    }

    // Trigger if both conditions are met
    if (verticalConditionMet && horizontalConditionMet && !triggered) {
      triggered = true;
      onTrigger();
    }
  };

  // Check on scroll with throttling
  const scrollHandler = throttle(() => {
    requestAnimationFrame(checkScrollDepth);
  }, 200);

  // Check if ResizeObserver is supported
  const isResizeObserverSupported = typeof ResizeObserver !== "undefined";

  let resizeObserver = null;

  if (isResizeObserverSupported) {
    // Modern approach: Use ResizeObserver for dynamic content changes
    resizeObserver = new ResizeObserver(() => {
      const docHeight = Math.max(
        document.body.scrollHeight,
        document.documentElement.scrollHeight,
      );
      const docWidth = Math.max(
        document.body.scrollWidth,
        document.documentElement.scrollWidth,
      );

      // Only recheck if document dimensions have changed
      if (docHeight !== lastDocHeight || docWidth !== lastDocWidth) {
        requestAnimationFrame(checkScrollDepth);
      }
    });

    // Observe body for dynamic content changes
    resizeObserver.observe(document.body);
  }

  // Initial check
  checkScrollDepth();

  // Add scroll listener
  window.addEventListener("scroll", scrollHandler, { passive: true });

  // Return cleanup function
  return () => {
    window.removeEventListener("scroll", scrollHandler);

    if (isResizeObserverSupported && resizeObserver) {
      resizeObserver.disconnect();
    }
  };
}

/**
 * Удаляет все существующие виджеты на странице
 * @returns {void}
 */
export function destroyExistingWidget() {
  const buttonTriggers = document.querySelectorAll(".fc-widget-preview-button");
  if (buttonTriggers.length > 0) {
    buttonTriggers.forEach((el) => {
      const buttonWrapper = el.closest("button");
      if (buttonWrapper) {
        buttonWrapper.remove();
      } else {
        el.remove();
      }
    });
  }

  const modalWidgets = document.querySelectorAll(
    `.${styles["fc-widget-modal-container"]}`,
  );
  if (modalWidgets && modalWidgets.length > 0) {
    modalWidgets.forEach((el) => el.remove());
  }

  const helloBoardWidget = document.querySelectorAll(`.${styles["fc-widget"]}`);
  if (helloBoardWidget && helloBoardWidget.length > 0) {
    helloBoardWidget.forEach((el) => el.remove());
  }

  const ratingScaleContainers = document.querySelectorAll(
    `.${styles["fc-widget-rating-scale-container"]}`,
  );
  if (ratingScaleContainers.length > 0) {
    ratingScaleContainers.forEach((el) => el.remove());
  }
}
