.fc-widget {
  position: fixed;
  inset: 0;
  z-index: 9999;
  pointer-events: none;
  left: initial;
  top: initial;
  bottom: initial;
  right: initial;
  max-width: 330px;
  width: 100%;
  transform: translateY(100%);
  transition: transform 500ms ease-in-out;
  padding-top: 30px;
  padding-bottom: 30px;
  box-sizing: border-box;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 4px 20px 0 rgb(0 15 71 / 30%);
  overflow: hidden;

  --fqz-widget-main-color: #3f65f1;
  --fqz-widget-background-color: rgba(207, 216, 220, 1);
  --fqz-widget-error-color: rgba(255, 0, 0, 1);
  --fqz-widget-success-color: green;
  --fqz-widget-main-place-color: #fff;
  --fqz-widget-text-on-bg: #000;
  --fqz-widget-text-on-place: #000;
  --fqz-widget-scrollbar-thumb-bg: rgba(0, 0, 0, 0.6);
  --fqz-widget-scrollbar-thumb-bg-hover: rgba(0, 0, 0, 0.6);
  --fqz-widget-scrollbar-track-bg: rgba(0, 0, 0, 0.6);
  --fqz-widget-font-family: system-ui, -apple-system, blinkmacsystemfont,
    "Segoe UI", roboto, "Helvetica Neue", arial, sans-serif;
  --fqz-widget-text-font-size: 14px;
  --fqz-widget-title-font-size: 21px;
  --fqz-widget-button-font-size: 15px;
  --fqz-widget-button-font-weight: 500;
}

.fc-widget * {
  scroll-behavior: auto !important;
}

.fc-widget.shown {
  pointer-events: all;
  transform: translateY(0);
}

.fc-widget-pos-bottom-left {
  bottom: 0;
  left: 23px !important;
}

.fc-widget-pos-bottom-right {
  bottom: 0;
  right: 23px !important;
}

@media screen and (max-width: 768px) {
  .fc-widget, .fc-widget-pos-bottom-left, .fc-widget-pos-bottom-right {
    left: 50% !important;
    right: 0 !important;
    transform: translateY(100%) translateX(-50%) !important;
  }

  .fc-widget.fc-widget.shown {
    transform: translateY(0) translateX(-50%) !important;
  }
}

.fc-widget-close {
  position: absolute;
  top: 9px;
  right: 10px;
  cursor: pointer;
  z-index: 5;
  transition: opacity 0.3s;
  opacity: 0.6;
  background-color: transparent;
  border: none;
  color: var(--fqz-widget-text-on-place);
}

.fc-widget-close :global(svg) {
  stroke: currentcolor;
}

.fc-widget-close:hover {
  opacity: 1;
}

.fc-widget-close:focus {
  outline: none !important;
}

.fc-widget-content {
  position: relative;
  max-width: 100%;
  height: 100%;
  margin: auto;
  font-family: var(--fqz-widget-font-family);
  color: var(--fqz-widget-text-on-place);
  font-size: var(--fqz-widget-text-font-size);
  padding: 0 20px 0 20px;
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px 8px 0 0;
  z-index: 2;
  transition: opacity 0.3s, background-color 0.3s, color 0.3s;
}

.fc-widget-content--hidden > form {
  display: none;
}

.fc-widget__main-bg, .fc-widget__secondary-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: background-color 0.3s;
}

.fc-widget__main-bg {
  background-color: var(--fqz-widget-main-place-color);
  z-index: 1;
}

.fc-widget__secondary-bg {
  background-color: var(--fqz-widget-background-color);
}

.fc-widget__secondary-bg--with-mobile-bg, .fc-widget__secondary-bg--with-bg {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.fc-widget__secondary-bg--with-mobile-bg {
  background-image: var(--fqz-widget-mobile-bg-url);
}

.fc-widget__secondary-bg--with-bg {
  background-image: var(--fqz-widget-bg-url);
}

.fc-widget__secondary-bg--darkened:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);

}

.fc-widget-copyright {
  position: absolute;
  top: 15px;
  left: 16px;
  z-index: 10;
}

.fc-widget-copyright path {
  fill: black;
  fill-opacity: 0.3;
  stroke: white;
}

.fc-widget--loading {
  height: 270px;
}

.fc-widget-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10005;
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.fc-widget-modal-container__scroll-area {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  /* padding: 10px; */
  z-index: 1;
  box-sizing: border-box;
  position: relative;
}

.fc-widget-modal-container__scroll-area-inner {
  width: 100%;
  min-height: 100%;
  padding: 10px;
  position: relative;
  z-index: 1;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.fc-widget-modal-container--hidden {
  opacity: 0;
  pointer-events: none;
}

.fc-widget-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.fc-widget-modal {
  position: relative;
  background-color: transparent;
  transform: scale(1);
  opacity: 1;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  width: 100%;
}


.fc-widget-modal--hidden {
  transform: scale(0.9);
  opacity: 0;
}

.fc-widget-modal .fc-widget {
  overflow: hidden;
  position: relative;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  right: 0 !important;
  transform: none;
  border-radius: 8px 8px 8px 8px;
  margin: auto;
  pointer-events: all;
  padding: 40px 50px 50px;
  max-width: 680px;
  /* min-height: 230px; */
}

.fc-widget-modal .fc-widget-content {
  padding: 0;
}

.fc-widget-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  z-index: 2;
  justify-content: center;
}

.fc-widget-loader span {
  display: inline-block;
  width: 46px;
  height: 46px;
  transform: translateY(-4px) translateX(-6px);
}

.fc-widget-loader span::after {
  content: "";
  display: block;
  width: 32px;
  height: 32px;
  margin: 8px;
  border: none;
  background-image: url("data:image/svg+xml, %3Csvg viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M23.75 3.75C23.75 2.73438 23.3594 1.875 22.6562 1.09375C21.875 0.390625 21.0156 0 20 0C18.9062 0 18.0469 0.390625 17.3438 1.09375C16.5625 1.875 16.25 2.73438 16.25 3.75C16.25 4.84375 16.5625 5.70312 17.3438 6.40625C18.0469 7.1875 18.9062 7.5 20 7.5C21.0156 7.5 21.875 7.1875 22.6562 6.40625C23.3594 5.70312 23.75 4.84375 23.75 3.75ZM20 32.5C21.0156 32.5 21.875 32.8906 22.6562 33.5938C23.3594 34.375 23.75 35.2344 23.75 36.25C23.75 37.3438 23.3594 38.2031 22.6562 38.9062C21.875 39.6875 21.0156 40 20 40C18.9062 40 18.0469 39.6875 17.3438 38.9062C16.5625 38.2031 16.25 37.3438 16.25 36.25C16.25 35.2344 16.5625 34.375 17.3438 33.5938C18.0469 32.8906 18.9062 32.5 20 32.5ZM36.25 16.25C37.2656 16.25 38.125 16.6406 38.9062 17.3438C39.6094 18.125 40 18.9844 40 20C40 21.0938 39.6094 21.9531 38.9062 22.6562C38.125 23.4375 37.2656 23.75 36.25 23.75C35.1562 23.75 34.2969 23.4375 33.5938 22.6562C32.8125 21.9531 32.5 21.0938 32.5 20C32.5 18.9844 32.8125 18.125 33.5938 17.3438C34.2969 16.6406 35.1562 16.25 36.25 16.25ZM7.5 20C7.5 21.0938 7.10938 21.9531 6.40625 22.6562C5.625 23.4375 4.76562 23.75 3.75 23.75C2.65625 23.75 1.79688 23.4375 1.09375 22.6562C0.3125 21.9531 0 21.0938 0 20C0 18.9844 0.3125 18.125 1.09375 17.3438C1.79688 16.6406 2.65625 16.25 3.75 16.25C4.76562 16.25 5.625 16.6406 6.40625 17.3438C7.10938 18.125 7.5 18.9844 7.5 20ZM8.51562 27.7344C9.53125 27.7344 10.3906 28.125 11.1719 28.8281C11.875 29.6094 12.2656 30.4688 12.2656 31.4844C12.2656 32.5781 11.875 33.4375 11.1719 34.1406C10.3906 34.9219 9.53125 35.2344 8.51562 35.2344C7.42188 35.2344 6.5625 34.9219 5.85938 34.1406C5.07812 33.4375 4.76562 32.5781 4.76562 31.4844C4.76562 30.4688 5.07812 29.6094 5.85938 28.8281C6.5625 28.125 7.42188 27.7344 8.51562 27.7344ZM31.4844 27.7344C32.5 27.7344 33.3594 28.125 34.1406 28.8281C34.8438 29.6094 35.2344 30.4688 35.2344 31.4844C35.2344 32.5781 34.8438 33.4375 34.1406 34.1406C33.3594 34.9219 32.5 35.2344 31.4844 35.2344C30.3906 35.2344 29.5312 34.9219 28.8281 34.1406C28.0469 33.4375 27.7344 32.5781 27.7344 31.4844C27.7344 30.4688 28.0469 29.6094 28.8281 28.8281C29.5312 28.125 30.3906 27.7344 31.4844 27.7344ZM8.51562 4.76562C9.53125 4.76562 10.3906 5.15625 11.1719 5.85938C11.875 6.64062 12.2656 7.5 12.2656 8.51562C12.2656 9.60938 11.875 10.4688 11.1719 11.1719C10.3906 11.9531 9.53125 12.2656 8.51562 12.2656C7.42188 12.2656 6.5625 11.9531 5.85938 11.1719C5.07812 10.4688 4.76562 9.60938 4.76562 8.51562C4.76562 7.5 5.07812 6.64062 5.85938 5.85938C6.5625 5.15625 7.42188 4.76562 8.51562 4.76562Z' fill='%233f65f1' /%3E%3C/svg%3E");
  background-size: contain;
  animation-name: fc-spinner-rotation;
  animation-duration: 2000ms;
  animation-iteration-count: infinite;
  animation-timing-function: steps(8);
}

.fc-widget-loader--hidden {
  opacity: 0;
}

@keyframes fc-spinner-rotation {
  from {
    transform: rotate(0);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.fc-widget-rating-scale-container {
  width: 100%;
  /* max-width: 580px; */
  text-align: center;
  margin: 0 auto;
}

@media screen and (max-width: 768px) {
  .fc-widget-modal .fc-widget {
    max-width: 100%;
    transform: none !important;
    padding: 30px 20px;
  }

  .fc-widget-modal .fc-widget-pos-bottom-left {
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    top: auto !important;
  }

  .fc-widget-modal .fc-widget-pos-bottom-right {
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    top: auto !important;
  }

  .fc-widget-modal .fc-widget-close {
    top: 15px !important;
    right: 10px !important;
  }
}
