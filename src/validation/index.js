/**
 * Валидирует поле формы с заданными валидаторами
 * @param {*} data - Значение поля формы
 * @param {Function[]} validators - Массив функций-валидаторов
 * @returns {string|null} - Текст ошибки валидации или null, если ошибок нет
 */
export function validateField(data, validators) {
  for (let validator of validators) {
    const error = validator(data);
    if (error) {
      return error;
    }
  }
  return null;
}

/**
 *  Проверяет форму с заданными валидаторами
 * @param {Object} formData - Данные формы
 * @param {Object} formValidators - Валидаторы для полей формы
 * @returns {Object} - Объект с ошибками для каждого поля
 */
export function validateForm(formData, formValidators) {
  const errors = {};
  for (let fieldName in formValidators) {
    const error = validateField(formData[fieldName], formValidators[fieldName]);
    if (error) {
      errors[fieldName] = error;
    }
  }
  return errors;
}
