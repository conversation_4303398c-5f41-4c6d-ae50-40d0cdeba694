// src/validation/validators.js
import { declOfNum } from "../helpers";
import { translate } from "../helpers/translations";

export function isRequired(value, message = translate("Обязательное поле")) {
  const isFalsy = value !== 0 && !value;
  return isFalsy ? message : null;
}

export function isPhoneNumber(value, message = translate("Неверный формат")) {
  if (!value) {
    return translate("Обязательное поле");
  }

  const parsedInt = parseInt(value.replace(/\D/g, ""));
  if (!parsedInt || parsedInt === 7) {
    return translate("Обязательное поле");
  }
  const phonePattern = /^\+7 \(\d{3}\) \d{3} - \d{4}$/;

  return phonePattern.test(value) ? null : message;
}

export function isEmail(value, message = translate("Неверный формат")) {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(value) ? null : message;
}

export function minLength(value, length = 6, message) {
  if (!message) {
    const word = declOfNum(length, ["символ", "символа", "символов"]);
    const translatedSymbolsCount = translate(`{count} ${word}`, {
      count: length,
    });

    message = translate("Должно быть введено хотя бы {characters}", {
      characters: translatedSymbolsCount,
    });
  }
  return value.length >= length ? null : message;
}

export function isWebsite(value, message = translate("Неверный формат")) {
  const websitePattern = /^(http|https):\/\/[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/;
  return websitePattern.test(value) ? null : message;
}

export function maxLength(value, length = 6, message) {
  if (!message) {
    const word = declOfNum(length, ["символа", "символов", "символов"]);
    const translatedSymbolsCount = translate(`{count} ${word}`, {
      count: length,
    });

    message = translate("Должно быть введено не более {characters}", {
      characters: translatedSymbolsCount,
    });
  }
  return value.length <= length ? null : message;
}
