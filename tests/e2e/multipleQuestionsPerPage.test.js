import { expect, test } from '@playwright/test'

/**
 * Кейсы для проверки работы нескольких вопросов на одной странице
 * Включить данный сценарий в опросе можно через раздел "Логика"
 */
test.describe('Несколько вопросов на одной странице', () => {
  test('Корректно работают несколько вопросов на одной странице', async ({ page }) => {
    // Переходим на страницу с несколькими вопросами
    await page.goto('/multiple-questions-per-page')

    // Проверяем начальное состояние и делаем скриншот
    const stars = await page.getByTestId('star-rating')
    await expect(stars).toHaveCount(2) // Два вопроса "Звездный рейтинг"
    await expect(page).toHaveScreenshot('multiple-questions-initial.png')

    // Навигация по вопросам с помощью кнопок навигации
    const navigationButtons = await page.locator('.survey-actions__questions-navigation-btn')
    const downButton = navigationButtons.nth(1)

    // Нажимаем кнопку "вниз" несколько раз с задержкой
    for (let i = 0; i < 3; i++) {
      await downButton.click()
      await page.waitForTimeout(300)
    }

    await expect(page).toHaveScreenshot('multiple-questions-scrolled-bottom.png')

    // Нажимаем кнопку "Далее"
    const nextButton = await page.getByText('Далее')
    await nextButton.click()

    // Проверяем наличие ошибок
    const errors = await page.getByText('Нужно поставить оценку').locator('visible=true')
    await expect(errors).toHaveCount(2) // Два вопроса "Звездный рейтинг" с ошибкой

    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-errors.png')

    // Заполняем все обязательные поля
    // Первый вопрос "Звездный рейтинг"
    const firstStarRating = stars.first()
    await firstStarRating.getByTestId('star-rating-item').nth(2).click()

    // Второй вопрос "Звездный рейтинг" с комментарием
    const secondStarRating = stars.nth(1)
    await secondStarRating.getByTestId('star-rating-item').nth(3).click()
    const commentTextarea = await page.getByTestId('comment').locator('textarea')
    await commentTextarea.fill('Test comment')

    // Первый вопрос "Рейтинг"
    const ratingScales = await page.getByTestId('rating-scale')
    await ratingScales.first().getByTestId('rating-scale-item').nth(2).click()
    await page.getByText('Вариант 1').click()

    // Второй вопрос "Рейтинг"
    await ratingScales.last().getByTestId('rating-scale-item').nth(2).click()
    const assessmentText = await page.getByTestId('assessments-text')
    await assessmentText.fill('Test assessment')

    // Прокручиваем к верху и делаем скриншот
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-filled-top.png')

    // Прокручиваем к низу и делаем скриншот
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-filled-bottom.png')

    // Нажимаем кнопку "Далее" и проверяем, что перешли на следующую страницу
    await nextButton.click()
    await expect(stars.first()).not.toBeVisible()
  })

  test('Вторая страница с медиа и текстовым вопросами работает корректно', async ({ page }) => {
    // Первый переходим на страницу и заполняем первый этап вопросов
    await page.goto('/multiple-questions-per-page')

    // Заполняем первый этап вопросов быстро, чтобы перейти на вторую страницу
    const stars = await page.getByTestId('star-rating')
    await stars.first().getByTestId('star-rating-item').nth(2).click()
    await stars.nth(1).getByTestId('star-rating-item').nth(3).click()
    const commentTextarea = await page.getByTestId('comment').locator('textarea')
    await commentTextarea.fill('Test comment')

    // Переходим на вторую страницу
    const nextButton = await page.getByText('Далее')
    await nextButton.click()

    // Делаем скриншот начального состояния второй страницы
    await expect(page).toHaveScreenshot('multiple-questions-second-page-initial.png')

    // Проверяем вопрос "Медиа варианты множественного выбора"
    const selectButtons = await page.locator('.gallery-item__select-button')
    await selectButtons.first().click()
    const mediaComment = await page.locator('.survey-questions__comment-form-group textarea')
    await mediaComment.fill('Comment for media selection')

    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-media-filled.png')

    // Проверяем поля "Фамилия", "Имя", "Отчество"
    const surnameInput = await page.getByTestId('input-surname')
    const nameInput = await page.getByTestId('input-name')
    const patronymInput = await page.getByTestId('input-patronym')

    await surnameInput.fill('Иванов')
    await nameInput.fill('Иван')
    await patronymInput.fill('Иванович')

    await surnameInput.scrollIntoViewIfNeeded()
    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-fio-filled.png')

    // Проверяем поле "Многострочное текстовое поле"
    const multilineText = await page.getByTestId('text-question-field')
    await multilineText.fill(
      'This is a test response for the multiline text field. Adding some more text to make it multiple lines.',
    )

    // Прокручиваем к верху и делаем скриншот
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-second-page-filled-top.png')

    // Прокручиваем к низу и делаем скриншот
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-second-page-filled-bottom.png')

    // Нажимаем кнопку "Далее" и проверяем, что перешли на следующую страницу
    await nextButton.click()

    // Проверяем, что перешли на следующую страницу, проверяя, что вопрос "Медиа варианты множественного выбора" больше не виден
    const mediaVariants = await page.locator('.media-variants-question')
    await expect(mediaVariants).not.toBeVisible()
  })

  test('Корректно отображает третью страницу с несколькими вопросами на одной странице', async ({ page }) => {
    // Переходим на страницу с несколькими вопросами
    await page.goto('/multiple-questions-per-page')

    // Быстро заполняем первую страницу для перехода к третьей
    const stars = await page.getByTestId('star-rating')
    await stars.first().getByTestId('star-rating-item').nth(2).click()
    await stars.nth(1).getByTestId('star-rating-item').nth(3).click()
    await page.getByTestId('comment').locator('textarea').fill('Test comment')

    const nextButton = await page.getByText('Далее')
    await nextButton.click()

    // Заполняем поля второй страницы
    const selectButtons = await page.locator('.gallery-item__select-button')
    await selectButtons.first().click()

    const mediaVariantsCommentTextarea = await page.locator('textarea').first()
    await mediaVariantsCommentTextarea.fill('Comment for media selection')
    const surnameInput = await page.getByTestId('input-surname')
    const nameInput = await page.getByTestId('input-name')
    const patronymicInput = await page.getByTestId('input-patronym')
    await surnameInput.fill('Иванов')
    await nameInput.fill('Иван')
    await patronymicInput.fill('Иванович')

    const multilineText = await page.getByTestId('text-question-field')
    await multilineText.fill(
      'This is a test response for the multiline text field. Adding some more text to make it multiple lines.',
    )

    await nextButton.click()

    // Делаем скриншот начального состояния третьей страницы
    await expect(page).toHaveScreenshot('multiple-questions-third-page-initial.png')

    // scroll to the bottom
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await page.waitForTimeout(300)

    await nextButton.click()

    await page.waitForTimeout(300)
    // Проверяем, что произошел скролл до ошибки
    await expect(page).toHaveScreenshot('multiple-questions-third-page-error.png')

    // заполняем галерею
    const galleryItems = await page.locator('.gallery-item')

    // click gallery item rating
    await galleryItems.nth(0).getByTestId('star-rating-item').nth(2).click()
    // click gallery item rating
    await galleryItems.nth(1).getByTestId('star-rating-item').nth(2).click()
    // click gallery item rating
    await galleryItems.nth(2).getByTestId('star-rating-item').nth(2).click()

    // click smile rating item
    const smileRatingItem = await page.getByTestId('smile-rating-item').nth(2)
    await smileRatingItem.click()

    await expect(page).toHaveScreenshot('multiple-questions-third-page-filled.png')

    // Проверяем, что перешли на следующую страницу
    await nextButton.click()
    await expect(galleryItems.first()).not.toBeVisible()
  })

  test('Корректно отображает четвертую страницу с несколькими вопросами на одной странице', async ({ page }) => {
    await page.goto('/multiple-questions-per-page')

    // Быстро заполняем первую страницу для перехода к третьей
    const stars = await page.getByTestId('star-rating')
    await stars.first().getByTestId('star-rating-item').nth(2).click()
    await stars.nth(1).getByTestId('star-rating-item').nth(3).click()
    await page.getByTestId('comment').locator('textarea').fill('Test comment')

    const nextButton = await page.getByText('Далее')
    const finishButton = await page.getByText('Завершить')
    await nextButton.click()

    // Заполняем поля второй страницы
    const selectButtons = await page.locator('.gallery-item__select-button')
    await selectButtons.first().click()

    const mediaVariantsCommentTextarea = await page.locator('textarea').first()
    await mediaVariantsCommentTextarea.fill('Comment for media selection')
    const surnameInput = await page.getByTestId('input-surname')
    const nameInput = await page.getByTestId('input-name')
    const patronymicInput = await page.getByTestId('input-patronym')
    await surnameInput.fill('Иванов')
    await nameInput.fill('Иван')
    await patronymicInput.fill('Иванович')

    const multilineText = await page.getByTestId('text-question-field')
    await multilineText.fill(
      'This is a test response for the multiline text field. Adding some more text to make it multiple lines.',
    )

    await nextButton.click()

    // Заполняем поля третьей страницы
    const galleryItems = await page.locator('.gallery-item')
    await galleryItems.nth(0).getByTestId('star-rating-item').nth(2).click()
    await galleryItems.nth(1).getByTestId('star-rating-item').nth(2).click()
    await galleryItems.nth(2).getByTestId('star-rating-item').nth(2).click()
    const smileRatingItem = await page.getByTestId('smile-rating-item').nth(2)
    await smileRatingItem.click()

    await nextButton.click()
    await expect(galleryItems.first()).not.toBeVisible()

    // Делаем скриншот начального состояния четвертой страницы
    await expect(page).toHaveScreenshot('multiple-questions-fourth-page-initial.png')

    await finishButton.click()

    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-fourth-page-error-scrolled.png')

    const npsRatings = await page.getByTestId('rating-nps')
    const commentTextarea = await page.locator('textarea')
    const matrixQuestionScales = await page.locator('.matrix-question__scale')
    const matrix3dQuestionTriggers = await page.locator('.matrix-3d-question__variant-select-trigger')

    // Проверяем, что на отображаются 4 NPS рейтинга
    // 1 Стандартный и 3 рейтинга "NPS для вариантов"
    await expect(npsRatings).toHaveCount(4)

    // Заполняем первый NPS рейтинг
    await npsRatings.first().getByTestId('rating-nps-item').nth(2).click()
    await commentTextarea.first().fill('Test comment')

    // Заполняем второй NPS рейтинг с вариантами
    await npsRatings.nth(1).getByTestId('rating-nps-item').nth(2).click()
    await npsRatings.nth(2).getByTestId('rating-nps-item').nth(3).click()
    await npsRatings.nth(3).getByTestId('rating-nps-item').nth(6).click()
    await commentTextarea.nth(1).fill('Test comment')

    // Заполняем матрицу
    await matrixQuestionScales.first().locator('button').nth(2).click()
    await matrixQuestionScales.nth(1).locator('button').nth(3).click()
    await matrixQuestionScales.nth(2).locator('button').nth(2).click()

    // Заполняем 3D матрицу
    await matrix3dQuestionTriggers.first().click()

    await page.locator('.select-item__label-text').filter({ hasText: 'Плохо' }).first().click()

    await matrix3dQuestionTriggers.nth(1).click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Умеренно' }).first().click()

    await matrix3dQuestionTriggers.nth(2).click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Хорошо' }).first().click()

    await matrix3dQuestionTriggers.nth(3).click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Дешево' }).first().click()

    // пропускаем третью строку
    await page.locator('.matrix-3d-question__skip-row').nth(2).click()

    // заполняем Комментарий
    await commentTextarea.last().fill('Test comment')

    // Скроллим к верху
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-fourth-page-filled.png')

    // Скроллим к низу
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot('multiple-questions-fourth-page-filled-scrolled.png')

    await finishButton.click()
    await page.waitForTimeout(500)
    await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
  })
})
