import { expect, test } from "@playwright/test";

async function answerAllQuestions(page, answers) {
  const nextButton = await page.getByText("Далее");
  const finishButton = await page.getByText("Завершить");

  if (answers.starRating) {
    const stars = await page.getByTestId("fc-widget-star-rating-item");
    await stars.nth(answers.starRating - 1).click();
    await nextButton.click();
  }

  if (answers.rating) {
    const ratingItems = await page.getByTestId("rating-scale-item");
    await ratingItems.nth(answers.rating - 1).click();
    await nextButton.click();
  }

  if (answers.nps) {
    const npsItems = await page.getByTestId("rating-nps-item");
    await npsItems.nth(answers.nps - 1).click();
    await nextButton.click();
  }
  if (answers.smile) {
    const smiles = await page.getByTestId("img-rating-item");
    await smiles.nth(answers.smile - 1).click();
    await nextButton.click();
  }

  if (answers.text) {
    const textarea = await page.getByTestId("textarea");
    await textarea.fill(answers.text);
    await nextButton.click();
  }

  if (answers.variants) {
    const checks = await page.getByTestId("check");
    const textarea = await page.getByTestId("textarea");
    for (const variant of answers.variants) {
      if (variant.startsWith("Свой вариант")) {
        const [checkText, selfVariantValue] = variant.split(" – ");
        const check = await checks.filter({ hasText: checkText });
        await check.click();

        await page.waitForTimeout(100);
        await textarea.fill(selfVariantValue);
      } else {
        const check = await checks.filter({ hasText: variant });
        await check.click();
      }
    }

    await finishButton.click();
  }
}

test.describe("Логика отображения - Конечные экраны", () => {
  test("Первый конечный экран виден, когда выбрано пять звезд", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 5,
      rating: 4,
      nps: 8,
      smile: 4,
      text: "Это тестовый ответ",
      variants: ["Раз", "Свой вариант – Привет"],
    });

    const firstEndScreen = await page.getByText("End screen 1");
    await expect(firstEndScreen).toBeVisible();
  });

  test("Первый конечный экран не виден, когда выбрано меньше пяти звезд", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 4,
      rating: 3,
      nps: 7,
      smile: 3,
      text: "Другой тестовый ответ",
      variants: ["Три"],
    });

    const firstEndScreen = await page.getByText("End screen 1");
    await expect(firstEndScreen).not.toBeVisible();
  });
  test("Второй конечный экран скрыт, когда рейтинг равен 1", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 5,
      smile: 3,
      text: "Тестовый ответ для проверки скрытия второго экрана",
      variants: ["Три"],
    });

    const secondEndScreen = await page.getByText("End screen 2");
    await expect(secondEndScreen).not.toBeVisible();
  });

  test("Второй конечный экран виден, когда рейтинг не равен 1", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 2,
      nps: 5,
      smile: 3,
      text: "Тестовый ответ для проверки отображения второго экрана",
      variants: ["Три"],
    });

    const secondEndScreen = await page.getByText("End screen 2");
    await expect(secondEndScreen).toBeVisible();
  });
  test("Третий конечный экран виден для премиум пользователя", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test-userType-premium");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ для премиум пользователя",
      variants: ["Три"],
    });

    const thirdEndScreen = await page.getByText("End screen 3");
    await expect(thirdEndScreen).toBeVisible();
  });

  test("Третий конечный экран не виден для обычного пользователя", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test"); // Using default mock data
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ для обычного пользователя",
      variants: ["Три"],
    });

    const thirdEndScreen = await page.getByText("End screen 3");
    await expect(thirdEndScreen).not.toBeVisible();
  });
  test("Четвертый конечный экран скрыт для пользователя с коротким именем", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test-username-short");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ для пользователя с коротким именем",
      variants: ["Три"],
    });

    const fourthEndScreen = await page.getByText("End screen 4");
    await expect(fourthEndScreen).not.toBeVisible();
  });

  test("Четвертый конечный экран виден для пользователя с длинным именем", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test-username-long");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ для пользователя с длинным именем",
      variants: ["Три"],
    });

    const fourthEndScreen = await page.getByText("End screen 4");
    await expect(fourthEndScreen).toBeVisible();
  });
  test("Пятый конечный экран виден при оценке NPS 8 или выше", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 8,
      smile: 3,
      text: "Тестовый ответ для высокого NPS",
      variants: ["Три"],
    });

    const fifthEndScreen = await page.getByText("End screen 5");
    await expect(fifthEndScreen).toBeVisible();
  });

  test("Пятый конечный экран не виден при оценке NPS ниже 8", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ для низкого NPS",
      variants: ["Три"],
    });

    const fifthEndScreen = await page.getByText("End screen 5");
    await expect(fifthEndScreen).not.toBeVisible();
  });

  test("Шестой конечный экран скрыт при оценке смайликом 'грустный' и ниже", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 2,
      text: "Тестовый ответ для грустного смайлика",
      variants: ["Три"],
    });

    const sixthEndScreen = await page.getByText("End screen 6");
    await expect(sixthEndScreen).not.toBeVisible();
  });

  test("Шестой конечный экран виден при оценке смайликом выше 'грустного'", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ для нейтрального смайлика",
      variants: ["Три"],
    });

    const sixthEndScreen = await page.getByText("End screen 6");
    await expect(sixthEndScreen).toBeVisible();
  });

  test("Восьмой конечный экран виден при выборе вариантов 'Раз' и 'Свой вариант'", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 1,
      nps: 7,
      smile: 2,
      text: "Тестовый ответ",
      variants: ["Раз", "Свой вариант – Тест"],
    });

    const eighthEndScreen = await page.getByText("End screen 8");
    await expect(eighthEndScreen).toBeVisible();
  });

  test("Восьмой конечный экран не виден при выборе других вариантов", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test");
    await answerAllQuestions(page, {
      starRating: 3,
      rating: 3,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ",
      variants: ["Три"],
    });

    const eighthEndScreen = await page.getByText("End screen 8");
    await expect(eighthEndScreen).not.toBeVisible();
  });

  test("Десятый конечный экран виден при оценке звезд 4 или выше и для не нового клиента", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test-isNewCustomer-0");
    await answerAllQuestions(page, {
      starRating: 4,
      rating: 1,
      nps: 7,
      smile: 2,
      text: "Тестовый ответ для постоянного клиента",
      variants: ["Три"],
    });

    const tenthEndScreen = await page.getByText("End screen 10");
    await expect(tenthEndScreen).toBeVisible();
  });

  test("Десятый конечный экран не виден для нового клиента", async ({
    page,
  }) => {
    await page.goto("/?key=logic-test-isNewCustomer-1");
    await answerAllQuestions(page, {
      starRating: 5,
      rating: 3,
      nps: 7,
      smile: 3,
      text: "Тестовый ответ для нового клиента",
      variants: ["Три"],
    });

    const tenthEndScreen = await page.getByText("End screen 10");
    await expect(tenthEndScreen).not.toBeVisible();
  });
});
