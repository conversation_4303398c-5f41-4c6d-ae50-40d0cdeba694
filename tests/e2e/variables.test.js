import { expect, test } from '@playwright/test'

test.describe('Проверка подстановки переменных', () => {
  test('Корректно заменяет переменные', async ({ page }) => {
    await page.goto('/variables')

    const nextButton = page.getByText('Далее')

    // 1. NPS Rating
    await page.getByTestId('rating-nps-item').nth(2).click()
    await nextButton.click()

    // 2. Star rating with answer reference
    await expect(page.getByText('Ответ на первый вопрос: 2')).toBeVisible()
    await page.getByTestId('star-rating-item').nth(2).click()
    await nextButton.click()

    // 3. Variants with answer reference
    await expect(page.getByText('Почему 3 звезды?')).toBeVisible()
    await page.getByTestId('star-rating-item').nth(4).click()
    await nextButton.click()

    // 4. Text with answer reference
    await expect(page.getByText('Ответ на третий вопрос: 5 звезд')).toBeVisible()
    await page.getByTestId('star-rating-item').nth(2).click()
    await nextButton.click()

    // 5. Rating scale
    await expect(page.getByText('Почему вы поставили 3 звезды?')).toBeVisible()
    await page.getByTestId('rating-scale-item').nth(2).click()
    await nextButton.click()

    // 6. Smile Rating
    await expect(page.getByText('Насколько вы довольны, учитывая что 3?')).toBeVisible()
    await page.getByTestId('smile-rating-item').nth(1).click()
    await nextButton.click()

    // 7. Scale with variants
    await expect(page.getByText('Оцените ваш предыдущий ответ 1')).toBeVisible()
    await nextButton.click()

    // 8. NPS with variants
    await expect(page.getByText('Учитывая ваш ответ Вариант 1: 0; Вариант 2: 0; Вариант 3: 0, насколько вы готовы рекомендовать?')).toBeVisible()
    await page.getByTestId('rating-nps').nth(0).locator('.nps-scale__item').nth(2).click()
    await page.getByTestId('rating-nps').nth(1).locator('.nps-scale__item').nth(6).click()
    await page.getByTestId('rating-nps').nth(2).locator('.nps-scale__item').nth(10).click()
    await nextButton.click()

    // 9. Variants multiple choice
    await expect(page.getByText('Почему вы поставили Качество услуг: 2; Скорость обслуживания: 6; Цена: 10?')).toBeVisible()
    await page.getByText('Вариант 1', { exact: true }).click()
    await page.getByText('Другое', { exact: true }).click()
    await page.locator('textarea').fill('Свой вариант')
    await nextButton.click()

    // 10. Media variants
    await expect(page.getByText('Выберите подходящее изображение к ответу Вариант 1, Свой вариант')).toBeVisible()
    await page.locator('.gallery-item__select-button').first().click()
    await nextButton.click()

    // 11. Text question
    await expect(page.getByText('Опишите подробнее ваш ответ Видео 1')).toBeVisible()
    await page.locator('textarea').fill('answer')
    await nextButton.click()

    // 12. Quiz question
    await expect(page.getByText('Тестовый вопрос про answer')).toBeVisible()

    // Fill quiz fields
    await page.getByTestId('quiz-question-nomask-field').first().fill('Однострочное')
    await page.getByTestId('quiz-question-nomask-field').nth(1).fill('Многострочное')
    await page.getByTestId('quiz-question-phone-field').fill('3333333333')
    await page.getByTestId('quiz-question-email-field').fill('<EMAIL>')
    await page.getByTestId('quiz-question-number-field').fill('5')
    await page.getByTestId('quiz-question-site-field').fill('http://six.com')
    await page.getByTestId('quiz-question-surname-field').fill('Иванов')
    await page.getByTestId('quiz-question-name-field').fill('Иван')
    await page.getByTestId('quiz-question-patronymic-field').fill('Иванович')
    await page.getByTestId('quiz-question-date-field').fill('20.10.2024')
    await page.getByTestId('quiz-question-date-from-field').fill('10.10.2024')
    await page.getByTestId('quiz-question-date-to-field').fill('10.10.2024')
    await page.getByTestId('quiz-question-date-day-field').fill('12')
    await page.locator('.date-month-mask-container .select-trigger').click()
    await page.getByText('Январь').first().click()
    await nextButton.click()

    // 13. Gallery
    await expect(page.getByText('Выберите изображение, которое соответствует Однострочное / Без маски: Однострочное; Многострочное - необязательный: Многострочное; Телефон: +7 (333) 333 3333; Почта: <EMAIL>; Число: 5; Сайт: http://six.com; ФИО: Иванов Иван Иванович; Дата: 20.10.2024; Период: 10.10.2024 - 10.10.2024; Дата (день и месяц): 12.01')).toBeVisible()
    await page.getByTestId('star-rating').nth(0).getByTestId('star-rating-item').nth(1).click()
    await page.getByTestId('star-rating').nth(1).getByTestId('star-rating-item').nth(3).click()
    await page.getByTestId('star-rating').nth(2).getByTestId('star-rating-item').nth(4).click()
    await nextButton.click()

    // 14. Matrix
    await expect(page.getByText('Оцените следующие параметры с учетом Файл undefined: 2; Файл undefined: 4; Файл undefined: 5')).toBeVisible()
    const matrixQuestionScales = await page.locator('.matrix-question__scale')
    await matrixQuestionScales.first().locator('button').nth(2).click()
    await matrixQuestionScales.nth(1).locator('button').nth(1).click()
    await matrixQuestionScales.nth(2).locator('button').nth(0).click()
    await nextButton.click()

    // 15. Diff question
    await expect(page.getByText('Сравните варианты, учитывая Параметр 1: Хорошо; Параметр 2: Нормально; Параметр 3: Плохо')).toBeVisible()
    const diffRows = await page.locator('.diff-question-row')
    for (let i = 0; i < 2; i++) {
      await diffRows.nth(i).locator('.diff-question-button').nth(2).click()
    }
    await nextButton.click()

    // 16. Classifier
    await expect(page.getByText('Классифицируйте ответ Плохо 3 Отлично; Медленно 3 Быстро')).toBeVisible()
    await page.locator('.fc-check').first().click()
    await nextButton.click()

    // Check intermediate block with all answers
    await expect(page.getByText('Ответ на первый вопрос 2')).toBeVisible()
    await expect(page.getByText('Ответ на второй вопрос 3 звезды')).toBeVisible()
    await expect(page.getByText('Ответ на третий вопрос 5 звезд')).toBeVisible()
    await expect(page.getByText('Ответ на четвертый вопрос 3 звезды')).toBeVisible()
    await expect(page.getByText('Ответ на пятый вопрос 3')).toBeVisible()
    await expect(page.getByText('Ответ на шестой вопрос 1')).toBeVisible()
    await expect(page.getByText('Ответ на седьмой вопрос Вариант 1: 0; Вариант 2: 0; Вариант 3: 0')).toBeVisible()
    await expect(page.getByText('Ответ на восьмой вопрос Качество услуг: 2; Скорость обслуживания: 6; Цена: 10')).toBeVisible()
    await expect(page.getByText('Ответ на девятый вопрос Вариант 1, Свой вариант')).toBeVisible()
    await expect(page.getByText('Ответ на десятый вопрос Видео 1')).toBeVisible()
    await expect(page.getByText('Ответ на одиннадцатый вопрос answer')).toBeVisible()
    await expect(page.getByText('Ответ на двенадцатый вопрос Однострочное / Без маски: Однострочное; Многострочное - необязательный: Многострочное; Телефон: +7 (333) 333 3333; Почта: <EMAIL>; Число: 5; Сайт: http://six.com; ФИО: Иванов Иван Иванович; Дата: 20.10.2024; Период: 10.10.2024 - 10.10.2024; Дата (день и месяц): 12.01')).toBeVisible()
    await expect(page.getByText('Ответ на тринадцатый вопрос Файл undefined: 2; Файл undefined: 4; Файл undefined: 5')).toBeVisible()
    await expect(page.getByText('Ответ на четырнадцатый вопрос Параметр 1: Хорошо; Параметр 2: Нормально; Параметр 3: Плохо')).toBeVisible()
    await expect(page.getByText('Ответ на пятнадцатый вопрос Плохо 3 Отлично; Медленно 3 Быстро')).toBeVisible()
  })
})
