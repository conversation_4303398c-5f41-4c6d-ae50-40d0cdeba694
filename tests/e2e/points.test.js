import { expect, test } from '@playwright/test'
import { drag } from './testUtils'

test.describe('Система баллов', () => {
  test('Корректно генерирует отчет о тестировании для текстовых вопросов и для даты/времени', async ({ page, context }) => {
    await page.goto('/points-text-questions')

    // 1. Простой текстовый вопрос
    const textField = page.getByTestId('text-question-field').first()
    await textField.fill('Тестовый ответ')
    await expect(textField).toHaveValue('Тестовый ответ')

    // 2. Многострочный текстовый вопрос
    await page.getByTestId('text-question-field').nth(1).fill('Многострочный\nтестовый\nответ')

    // 3. Текстовый вопрос с URL
    await page.getByTestId('site-input').nth(0).fill('https://example.com')

    // 4. Текстовый вопрос с ФИО
    await page.getByTestId('input-surname').fill('Иванов')
    await page.getByTestId('input-name').fill('Иван')
    await page.getByTestId('input-patronym').fill('Иванович')

    // 5. Текстовый вопрос с датой
    const dateInputFirst = page.getByTestId('date-picker-input').first()
    await dateInputFirst.fill('01.01.2024')
    await dateInputFirst.press('Escape')
    // Verify date format and value
    await expect(dateInputFirst).toHaveValue('01.01.2024')

    // 6. Текстовый вопрос с днем и месяцем
    await page.locator('.date-month-mask-container__day input').first().fill('03')
    await page.locator('.date-month-mask-container .select-trigger').first().click()
    await page.getByText('Январь').first().click()

    // 7. Текстовый вопрос с периодом
    const dateInputSecond = await page.getByTestId('date-range-from').nth(0)
    await dateInputSecond.fill('01.01.2024')
    await dateInputSecond.press('Escape')

    const dateInputSecondEnd = await page.getByTestId('date-range-to').nth(0)
    await dateInputSecondEnd.fill('31.12.2024')
    await dateInputSecondEnd.press('Escape')

    // 8. Текстовый вопрос с телефоном
    await page.getByTestId('phone-input').first().fill('+79998887766')

    // 9. Текстовый вопрос с email
    await page.getByTestId('email-input').first().fill('<EMAIL>')

    // 10. Дата/время: Дата
    const dateInputThird = page.getByTestId('datepicker-input').first()
    await dateInputThird.fill('01.01.2024')
    await dateInputThird.press('Escape')
    // Verify date format and value
    await expect(dateInputThird).toHaveValue('01.01.2024')
    // Verify calendar popup is closed
    await expect(page.locator('.react-datepicker')).not.toBeVisible()

    // 11. Дата/время: День и месяц
    await page.locator('.date-month-mask-container__day input').nth(1).fill('04')
    await page.locator('.date-month-mask-container button').nth(1).click()
    await page.getByText('Январь').nth(1).click()

    // 12. Дата/время: Время
    await page.getByTestId('time-input').first().fill('14:00')

    // 13. Дата/время: Дата и время
    await page.getByTestId('datepicker-input').nth(1).fill('06.01.2024')
    await page.getByTestId('time-input').nth(1).fill('15:00')

    // 14. Дата/время: День, месяц и время
    await page.locator('.date-month-mask-container__day input').nth(2).fill('06')
    await page.locator('.date-datemonthtime-mask-container__date-month button').first().click()
    await page.getByText('Январь').nth(2).click()
    await page.getByTestId('time-input').nth(2).fill('16:00')

    // 15. Адрес (Не заполняем)
    // await page.getByTestId('text-question-field').nth(5).fill('Москва, Красная площадь')

    // Нажимаем "Завершить"
    const finishButton = await page.getByText('Завершить')
    await finishButton.click()

    // Проверяем отображение экрана с отчетом
    await expect(page.getByText('Отчет о тестировании')).toBeVisible()

    await page.getByText('Отчет о тестировании').click()

    await page.waitForTimeout(400)

    // scroll to top
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })

    await expect(page).toHaveScreenshot('points/points-report-1-top.png')

    // middle
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2)
    })

    await expect(page).toHaveScreenshot('points/points-report-1-middle.png')

    // scroll to bottom
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })

    await expect(page).toHaveScreenshot('points/points-report-1-bottom.png')

    // const pagePromise = context.waitForEvent('page')
    // await page.locator('.points-report-screen__button--print').click()

    // // @NOTE: Печатная версия открывается в новой вкладке, поэтому ниспользуем контекст для получения новой страницы
    // // Печатную версию тестируем без скриншотов, так как она по умолчанию открывает нативные настройки печати в браузере
    // const newPage = await pagePromise

    // await expect(newPage.getByText('Иванов Иван Иванович').first()).toBeVisible()
    // await expect(newPage.getByText('+79998887766').first()).toBeVisible()
    // await expect(newPage.getByText('<EMAIL>').first()).toBeVisible()

    // await newPage.close()
  })

  test('Корректно генерирует отчет о тестировании для вопросов с рейтингом', async ({ page }) => {
    await page.goto('/points-rating-questions')

    // 1. Звездный рейтинг базовый
    const firstStarRating = page.getByTestId('star-rating').first()
    await firstStarRating.getByTestId('star-rating-item').nth(2).click()

    // 2. Звездный рейтинг с комментарием
    const secondStarRating = page.getByTestId('star-rating').nth(1)
    await secondStarRating.getByTestId('star-rating-item').nth(3).click()
    await page.locator('textarea').nth(1).fill('Тестовый комментарий')

    // 3. Шкала базовая
    const scaleSlider = page.locator('.slider-thumb').first()
    await scaleSlider.dragTo(page.locator('.slider-track').first(), {
      targetPosition: { x: 50, y: 0 },
    })
    await expect(page.locator('.slider-value').first()).not.toHaveText('0')

    // 4. Шкала с вариантами
    const scaleWithVariantsSlider = page.locator('.slider-thumb').nth(1)
    await scaleWithVariantsSlider.dragTo(page.locator('.slider-track').nth(1), {
      targetPosition: { x: 70, y: 0 },
    })
    await page.locator('textarea').nth(1).fill('Комментарий к шкале')

    // 5. Шкала с пропуском
    const scaleWithSkipSlider = page.locator('.slider-thumb').nth(2)
    await scaleWithSkipSlider.dragTo(page.locator('.slider-track').nth(2), {
      targetPosition: { x: 140, y: 0 },
    })

    // 6. Смайлы базовые
    const smileRating = page.getByTestId('smile-rating').first()
    await smileRating.getByTestId('smile-rating-item').nth(3).click()

    // 7. Смайлы с комментарием
    const smileWithComment = page.getByTestId('smile-rating').nth(1)
    await smileWithComment.getByTestId('smile-rating-item').nth(4).click()
    await page.locator('textarea').nth(2).fill('Комментарий к смайлам')

    // 8. Звездный рейтинг с вариантами базовый
    const starVariantsRating = page.getByTestId('star-rating').nth(2)
    await starVariantsRating.getByTestId('star-rating-item').nth(8).click()
    await page.locator('textarea').nth(3).fill('Комментарий к звездному рейтингу с вариантами')

    // 9. Звездный рейтинг с уточняющими вопросами
    const starWithAssessments = page.getByTestId('star-rating').nth(5)
    await starWithAssessments.getByTestId('star-rating-item').nth(2).click()

    // Выбираем уточняющие вопросы для первого варианта
    const questionVariants = page.locator('.question-variants').first()
    await questionVariants.getByText('Плохое обслуживание').click()
    await questionVariants.getByText('Плохая чистота').click()
    await questionVariants.getByText('Свой вариант').click()
    await questionVariants.locator('textarea').fill('Плохое отношение к клиентам')

    // 10. NPS базовый
    const npsRating = page.getByTestId('rating-nps').first()
    await npsRating.getByTestId('rating-nps-item').nth(8).click()
    await page.locator('textarea').nth(5).fill('Комментарий к NPS')

    // 11. NPS с вариантами
    const npsWithVariants = page.getByTestId('rating-nps').nth(1)
    const npsWithVariantsSecond = page.getByTestId('rating-nps').nth(2)
    await npsWithVariants.getByTestId('rating-nps-item').nth(9).click()
    await npsWithVariantsSecond.getByTestId('rating-nps-item').nth(5).click()
    await page.locator('textarea').nth(6).fill('Комментарий к NPS с вариантами')

    // 12. Рейтинговая шкала
    const ratingScale = page.getByTestId('rating-scale').first()
    await ratingScale.getByTestId('rating-scale-item').nth(4).click()
    await page.locator('textarea').nth(7).fill('Комментарий к рейтинговой шкале')

    // 13. Рейтинговая шкала с уточняющими вопросами
    const ratingScaleWithAssessments = page.getByTestId('rating-scale').nth(1)
    await ratingScaleWithAssessments.getByTestId('rating-scale-item').nth(3).click()
    await page.getByText('Качество обслуживания').nth(1).click()
    await page.getByText('Чистота помещения').nth(1).click()

    // Заполняем уточняющие вопросы для рейтинговой шкалы
    await page.getByText('Соотношение цена/качество', { exact: true }).nth(1).click()

    // Нажимаем "Завершить"
    const finishButton = await page.getByText('Завершить')
    await finishButton.click()

    // Проверяем отображение экрана с отчетом
    await expect(page.getByText('Отчет о тестировании')).toBeVisible()

    await page.getByText('Отчет о тестировании').click()

    await page.waitForTimeout(400)

    // scroll to top
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })

    await expect(page).toHaveScreenshot('points/points-report-2-top.png')

    // middle
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2)
    })

    await expect(page).toHaveScreenshot('points/points-report-2-middle.png')

    // scroll to bottom
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })

    await expect(page).toHaveScreenshot('points/points-report-2-bottom.png')
  })

  test('Корректно генерирует отчет о тестировании для комплексных вопросов', async ({ page }) => {
    await page.goto('/points-complex-questions')

    // 1. Matrix basic
    const matrixRows = await page.locator('.matrix-question__row')
    for (let i = 0; i < 3; i++) {
      await matrixRows.nth(i).locator('.matrix-question__cell').nth(2).click()
    }

    // 2. Matrix with skip row
    const matrixWithSkipRows = await page.locator('.matrix-question__row')
    await matrixWithSkipRows.nth(3).locator('.matrix-question__cell').nth(1).click()
    await matrixWithSkipRows.nth(3).locator('.matrix-question__cell').nth(2).click()
    await matrixWithSkipRows.nth(4).locator('.matrix-question__cell').nth(1).click()

    // Fill extra questions
    const questionVariants = await page.locator('.question-variants')
    await questionVariants.first().locator('.fc-check').nth(0).click()
    await questionVariants.first().locator('.fc-check').nth(1).click()
    await questionVariants.nth(1).locator('.fc-check').nth(0).click()
    await questionVariants.nth(1).locator('.fc-check').nth(1).click()
    await questionVariants.nth(1).locator('.fc-check').last().click()
    await page.locator('textarea').first().fill('Тестовый комментарий к матрице')

    // 3. Gallery basic
    const galleryItems = await page.locator('.gallery-item')
    await galleryItems.nth(0).getByTestId('star-rating-item').nth(2).click()
    // await galleryItems.nth(1).getByTestId('star-rating-item').nth(3).click()
    await galleryItems.nth(2).getByTestId('star-rating-item').nth(4).click()

    // 4. Gallery with multiple choice (not present in complexMockData, skipping)

    // 5. Media variants single choice
    await page.locator('.gallery-item__select-button').first().click()

    // 6. Media variants multiple choice
    await page.locator('.gallery-item__select-button').nth(2).click()
    await page.locator('.gallery-item__select-button').nth(4).click()
    await page.locator('.survey-questions__comment-form-group textarea').nth(0).fill('Комментарий к медиа вариантам')

    // Нажимаем "Завершить"
    const finishButton = await page.getByText('Завершить')
    await finishButton.click()

    // Проверяем отображение экрана с отчетом
    await expect(page.getByText('Отчет о тестировании')).toBeVisible()

    await page.getByText('Отчет о тестировании').click()

    await page.waitForTimeout(400)

    // scroll to top
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })

    await expect(page).toHaveScreenshot('points/points-report-3-top.png')

    // middle
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2)
    })

    await expect(page).toHaveScreenshot('points/points-report-3-middle.png')

    // scroll to bottom
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })

    await expect(page).toHaveScreenshot('points/points-report-3-bottom.png')
  })

  test('Корректно генерирует отчет о тестировании для вопросов: Анкета, 3д матрица', async ({ page }) => {
    await page.clock.install({ time: new Date('2024-09-06T08:00:00') })
    await page.goto('/points-complex-questions-2')

    // 1. Fill first quiz question (full)
    // No mask fields
    await page.getByTestId('quiz-question-nomask-field').first().fill('Тестовый ответ')
    await page.getByTestId('quiz-question-nomask-field').nth(1).fill('Многострочный тестовый ответ')

    // FIO fields
    await page.getByTestId('quiz-question-surname-field').nth(0).fill('Иванов')
    await page.getByTestId('quiz-question-name-field').nth(0).fill('Иван')
    await page.getByTestId('quiz-question-patronymic-field').nth(0).fill('Иванович')

    // Phone field
    await page.getByTestId('quiz-question-phone-field').nth(0).fill('9991234567')

    // Email field
    await page.getByTestId('quiz-question-email-field').nth(0).fill('<EMAIL>')

    // Number field
    await page.getByTestId('quiz-question-number-field').nth(0).fill('42')

    // Site field
    await page.getByTestId('quiz-question-site-field').nth(0).fill('https://example.com')

    // Date field
    await page.getByTestId('quiz-question-date-field').nth(0).fill('01.01.2024')

    // Period (date range)
    await page.getByTestId('quiz-question-date-from-field').nth(0).fill('01.01.2024')
    await page.getByTestId('quiz-question-date-to-field').nth(0).fill('31.12.2024')

    // Day and month
    await page.getByTestId('quiz-question-date-day-field').nth(0).fill('15')
    await page.locator('.date-month-mask-container .select-trigger').nth(0).click()
    await page.getByText('Март').nth(0).click()

    // 2. Fill second quiz question (simple with skip)
    await page.getByTestId('quiz-question-nomask-field').nth(2).fill('Простой ответ')
    await page.getByTestId('quiz-question-nomask-field').nth(3).fill('Многострочный ответ')
    await page.getByTestId('quiz-question-surname-field').nth(1).fill('Петров')
    await page.getByTestId('quiz-question-name-field').nth(1).fill('Петр')
    await page.getByTestId('quiz-question-patronymic-field').nth(1).fill('Петрович')

    // 3. Fill 3D matrix question
    // Select variants for first column
    const matrix3dQuestionTriggers = await page.locator('.matrix-3d-question__variant-select-trigger')
    await matrix3dQuestionTriggers.first().click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Отлично' }).first().click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Хорошо' }).first().click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Отвратительно' }).first().click()

    // Select variants for second column
    await matrix3dQuestionTriggers.nth(1).click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Хорошо' }).first().click()

    // Skip third row
    await page.locator('.matrix-3d-question__skip-row').nth(2).click()

    // Select variants for fourth column
    await matrix3dQuestionTriggers.nth(3).click()
    await page.locator('.select-item__label-text').filter({ hasText: 'Затрудняюсь ответить' }).first().click()

    // Add comment to 3D matrix
    await page.locator('textarea').last().fill('Комментарий к 3D матрице')

    // Click finish button
    const finishButton = await page.getByText('Завершить')
    await finishButton.click()

    // Check report screen is visible
    await expect(page.getByText('Отчет о тестировании')).toBeVisible()

    await page.getByText('Отчет о тестировании').click()

    await page.waitForTimeout(400)

    // Take screenshots at different scroll positions
    // Top
    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('points/points-report-4-top.png')

    // middle
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2)
    })

    await expect(page).toHaveScreenshot('points/points-report-4-middle.png')

    // Bottom
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await expect(page).toHaveScreenshot('points/points-report-4-bottom.png')
  })

  test('Корректно генерирует отчет о тестировании для оставшихся вопросов', async ({ page }) => {
    await page.goto('/points-additional-questions')

    // 1. Inter block - just text, no input needed
    await expect(page.getByText('Это промежуточный текстовый блок между вопросами')).toBeVisible()

    // 2. Filials question (skipped)
    // await page.locator('').nth(2).click()

    // 3. Basic variants question
    await page.getByText('Телефон').click()
    await page.getByText('Email').click()
    await page.getByText('Личная встреча').click()
    await page.getByText('Свой вариант').first().click()
    await page.locator('textarea').first().fill('Свой вариант')
    await page.locator('textarea').nth(1).fill('Текст комментария к вариантам')

    // 4. Media variants question
    await page.getByTestId('assessments-file-check').first().locator('.fc-check__box').click()
    await page.getByTestId('assessments-file-check').nth(1).locator('.fc-check__box').click()
    await page.getByTestId('assessments-file-check').nth(2).locator('.fc-check__box').click()
    await page.getByText('Свой вариант').nth(1).click()
    await page.locator('textarea').nth(2).fill('Свой вариант для медиа')
    await page.locator('textarea').nth(3).fill('Текст комментария к медиа вариантам')

    // 5. Priority question
    const priorityItems = await page.locator('.priority-item')
    const firstItem = priorityItems.nth(0)
    const secondItem = priorityItems.nth(1)
    const thirdItem = priorityItems.nth(2)
    const lastItem = priorityItems.nth(3)
    await drag(page, firstItem, lastItem)
    await drag(page, thirdItem, secondItem)

    // 6. Diff questions
    const diffRows = await page.locator('.diff-question-row')

    // First diff question (306) - 3 rows
    for (let i = 0; i < 3; i++) {
      await diffRows.nth(i).locator('.diff-question-button').nth(i).click()
    }

    // Single row circles (342)
    await diffRows.nth(3).locator('.diff-question-button--circle').nth(4).click()

    // Single row with right label (343)
    await diffRows.nth(4).locator('.diff-question-button').nth(4).click()

    // Multiple rows (344) - 3 rows
    for (let i = 5; i < 8; i++) {
      const normalizedIndex = i - 5
      await diffRows.nth(i).locator('.diff-question-button').nth(normalizedIndex).click()
    }

    // Partially labeled (345) - 3 rows
    for (let i = 8; i < 11; i++) {
      const normalizedIndex = i - 8
      await diffRows.nth(i).locator('.diff-question-button').nth(normalizedIndex).click()
    }

    // Left labels only (346) - 3 rows
    for (let i = 11; i < 14; i++) {
      const normalizedIndex = i - 11
      await diffRows.nth(i).locator('.diff-question-button').nth(normalizedIndex).click()
    }

    // Circles with labels (347) - 5 rows
    for (let i = 14; i < 19; i++) {
      const normalizedIndex = i - 14
      await diffRows.nth(i).locator('.diff-question-button--circle').nth(normalizedIndex).click()
    }

    // 7. Classifier question
    // Open all categories
    await page.locator('.classifier-tree-node__toggle').nth(0).click()
    await page.locator('.classifier-tree-node__toggle').nth(1).click()
    await page.locator('.classifier-tree-node__toggle').nth(2).click()

    // Select all parent nodes
    await page.locator('.classifier-tree-node__content:has-text("Высокий приоритет") .fc-check').click()
    await page.locator('.classifier-tree-node__content:has-text("Средний приоритет") .fc-check').click()
    await page.locator('.classifier-tree-node__content:has-text("Низкий приоритет") .fc-check').click()

    // select element without parent
    await page.getByText('Задача 5').click()

    // 8. File upload question - skipped as it's unrequired

    // Click finish button
    const finishButton = await page.getByText('Завершить')
    await finishButton.click()

    // Check report screen is visible
    await expect(page.getByText('Отчет о тестировании')).toBeVisible()

    await page.getByText('Отчет о тестировании').click()

    await page.waitForTimeout(400)

    // Take screenshots at different scroll positions
    // Top
    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('points/points-report-5-top.png')

    // one third
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight / 3))
    await expect(page).toHaveScreenshot('points/points-report-5-one-third.png')

    // Middle
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight / 2))
    await expect(page).toHaveScreenshot('points/points-report-5-middle.png')

    // Bottom
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await expect(page).toHaveScreenshot('points/points-report-5-bottom.png')
  })
})
