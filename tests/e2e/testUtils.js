/**
 * Перетаскивает один элемент на другой с помощью Playwright
 * @param {import('@playwright/test').Page} page - Экземпляр страницы Playwright
 * @param {import('@playwright/test').Locator} draggable - Элемент, который нужно перетащить
 * @param {import('@playwright/test').Locator} droppable - Элемент, на который нужно перетащить
 * @returns {Promise<void>}
 */
export async function drag(page, draggable, droppable) {
  const box = await droppable.boundingBox()
  await draggable.hover()

  await page.mouse.down()

  await page.waitForTimeout(200)
  await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2, {
    steps: 5,
  })
  await page.waitForTimeout(200)
  await page.mouse.up()
}

/**
 * Ожидает стабилизации UI перед продолжением
 * @param {import('@playwright/test').Page} page - Экземпляр страницы Playwright
 * @param {number} timeout - Время ожидания в миллисекундах
 * @returns {Promise<void>}
 */
export async function waitForUIStability(page, timeout = 300) {
  await page.waitForTimeout(timeout)
}

/**
 * Возвращает массив конфигураций для различных режимов тестирования
 * @returns {Array<{
 *   name: string,
 *   urlParam: string,
 *   getScreenshotPath: (name: string) => string,
 *   viewport?: { width: number, height: number },
 *   isTablet?: boolean, // Flag to identify tablet mode
 *   setupPage?: (page: import('@playwright/test').Page) => Promise<void>
 * }>}
 */
export function getTestModes(allowedModes) {
  const modes = [
    {
      id: 'default',
      name: 'Стандартный вид',
      urlParam: '',
      getScreenshotPath: name => `${name}.png`,
    },
    {
      id: 'simple',
      name: 'Упрощенный вид',
      urlParam: '?simple=1&inFrame=1',
      getScreenshotPath: name => `simple/${name}.png`,
      viewport: { width: 800, height: 400 },
      setupPage: async (page) => {
        // Wait for DOM to be ready and set CSS variable
        await page.addInitScript(async () => {
          document.addEventListener('DOMContentLoaded', () => {
            document.documentElement.style.setProperty('--fqz-app-max-iframe-height', '100vh')
          })
        })
      },
    },
    {
      id: 'tablet',
      name: 'Планшетный вид',
      urlParam: '?tablet=1', // Activated by tablet=1 query parameter
      getScreenshotPath: name => `tablet/${name}.png`,
      viewport: { width: 1024, height: 768 },
      isTablet: true,
    },
  ];

  if (Array.isArray(allowedModes) && allowedModes.length > 0) {
    return modes.filter(mode => allowedModes.includes(mode.id));
  }
  return modes;
}
