import { expect, test } from '@playwright/test'

// Helper function to check if question is visible
async function expectQuestionToBeVisible(page, questionText) {
  await expect(page.getByText(questionText, { exact: true })).toBeVisible()
}

// Helper function to check if question is hidden
async function expectQuestionToBeHidden(page, questionText) {
  await expect(page.getByText(questionText)).not.toBeVisible()
}

test.describe('Логика отображения', () => {
  test('Отображает конечный блок при невыполнении условий у первого вопроса', async ({ page }) => {
    await page.goto('/view-logic-variant-scales')

    // set 1, 1, 1
    const starRatings = await page.getByTestId('star-rating')
    await starRatings.nth(0).getByTestId('star-rating-item').nth(0).click()
    await starRatings.nth(1).getByTestId('star-rating-item').nth(0).click()
    await starRatings.nth(2).getByTestId('star-rating-item').nth(0).click()

    await page.getByText('Завершить').click()
    await expectQuestionToBeVisible(page, 'Конечный блок')
  })
  test('Отображает конечный блок при невыполнении условий у второго вопроса', async ({ page }) => {
    await page.goto('/view-logic-variant-scales')

    const starRatings = await page.getByTestId('star-rating')
    // set 4, 5, 3
    await starRatings.nth(0).getByTestId('star-rating-item').nth(3).click()
    await starRatings.nth(1).getByTestId('star-rating-item').nth(4).click()
    await starRatings.nth(2).getByTestId('star-rating-item').nth(2).click()

    await page.getByText('Далее').click()

    // set 0, 10
    const npsRatings = await page.getByTestId('rating-nps')
    await npsRatings.nth(0).getByTestId('rating-nps-item').nth(0).click()
    await npsRatings.nth(1).getByTestId('rating-nps-item').nth(10).click()

    await page.getByText('Завершить').click()
    await expectQuestionToBeVisible(page, 'Конечный блок')
  })
  test('Отображает конечный блок при невыполнении условий у третьего вопроса', async ({ page }) => {
    await page.goto('/view-logic-variant-scales')
    const starRatings = await page.getByTestId('star-rating')
    // set 4, 5, 3
    await starRatings.nth(0).getByTestId('star-rating-item').nth(3).click()
    await starRatings.nth(1).getByTestId('star-rating-item').nth(4).click()
    await starRatings.nth(2).getByTestId('star-rating-item').nth(2).click()

    await page.getByText('Далее').click()

    // nps. set 5 and 7
    const npsRatings = await page.getByTestId('rating-nps')
    await npsRatings.nth(0).getByTestId('rating-nps-item').nth(5).click()
    await npsRatings.nth(1).getByTestId('rating-nps-item').nth(7).click()

    await page.getByText('Далее').click()

    // scale. set 10 and 90
    const scaleVariants = await page.locator('.scale-item')
    const firstScaleInput = scaleVariants.nth(0).locator('input[type="text"]')
    await firstScaleInput.fill('10')
    await firstScaleInput.blur()
    const secondScaleInput = scaleVariants.nth(1).locator('input[type="text"]')
    await secondScaleInput.fill('90')
    await secondScaleInput.blur()

    await page.getByText('Завершить').click()
    await expectQuestionToBeVisible(page, 'Конечный блок')
  })

  test('Отображает стандартный конечный экран при выполнении определенных условий в матрице', async ({ page }) => {
    await page.goto('/view-logic-variant-scales')

    const starRatings = await page.getByTestId('star-rating')
    // set 4, 5, 3
    await starRatings.nth(0).getByTestId('star-rating-item').nth(3).click()
    await starRatings.nth(1).getByTestId('star-rating-item').nth(4).click()
    await starRatings.nth(2).getByTestId('star-rating-item').nth(2).click()

    await page.getByText('Далее').click()

    // set 5, 7
    const npsRatings = await page.getByTestId('rating-nps')
    await npsRatings.nth(0).getByTestId('rating-nps-item').nth(5).click()
    await npsRatings.nth(1).getByTestId('rating-nps-item').nth(7).click()

    await page.getByText('Далее').click()

    // set 70, 30
    const scaleVariants = await page.locator('.scale-item')
    const firstScaleInput = scaleVariants.nth(0).locator('input[type="text"]')
    await firstScaleInput.fill('70')
    await firstScaleInput.blur()
    const secondScaleInput = scaleVariants.nth(1).locator('input[type="text"]')
    await secondScaleInput.fill('30')
    await secondScaleInput.blur()

    await page.getByText('Далее').click()

    const matrixRows = await page.locator('.matrix-question__row')
    await expect(matrixRows).toHaveCount(3)

    // fill 2, 3, 4 in each row
    await matrixRows.nth(0).getByRole('button').nth(1).click()
    await matrixRows.nth(1).getByRole('button').nth(2).click()
    await matrixRows.nth(2).getByRole('button').nth(3).click()

    await page.getByText('Завершить').click()

    await expectQuestionToBeVisible(page, 'Опрос успешно пройден!')
  })

  test.describe('Логика отображения: вопросы со шкалой', () => {
    test('Отображает первый вопрос (Star Rating) при user_type=premium', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&language=en&region=EU-West')
      await expectQuestionToBeVisible(page, 'Звездный рейтинг')

      // Fill star rating
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(3).click() // 4th star
      await page.getByText('Далее').click()

      // Verify next question is visible
      await expectQuestionToBeVisible(page, 'Оцените по шкале')
    })

    test('Отображает второй вопрос (Rating Scale) при высокой оценке Star Rating и region=EU', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en')

      // Fill first question
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click() // 5th star
      await page.getByText('Далее').click()

      // Verify Rating Scale is visible
      await expectQuestionToBeVisible(page, 'Оцените по шкале')

      // Fill rating scale
      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click() // Rating 8
      await page.getByText('Далее').click()

      // Verify next question is visible
      await expectQuestionToBeVisible(page, 'Оцените наш сервис (смайлы)')
    })

    test('Отображает третий вопрос (Smile Rating) при высокой оценке Rating Scale и language=en', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en')
      // Navigate through previous questions
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click()
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click() // Rating 8
      await page.getByText('Далее').click()

      // Verify Smile Rating is visible
      await expectQuestionToBeVisible(page, 'Оцените наш сервис (смайлы)')

      // Fill smile rating
      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.first().click()
      await page.getByText('Далее').click()

      // Verify next question is visible
      await expectQuestionToBeVisible(page, 'Рейтинг NPS (от 1)')
    })

    test('Отображает четвертый вопрос (NPS from 1) при выборе первого смайла', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en')
      // Navigate through previous questions
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click()
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click()
      await page.getByText('Далее').click()
      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.first().click()
      await page.getByText('Далее').click()

      // Verify NPS (from 1) is visible
      const npsItems = page.getByTestId('rating-nps-item').locator('visible=true')
      await expect(npsItems.first()).toHaveText('1')

      // Fill NPS rating
      await npsItems.nth(8).click() // Rating 9
      await page.getByText('Далее').click()

      // Verify next question is visible
      await expectQuestionToBeVisible(page, 'Рейтинг NPS (от 0)')
    })

    test('Отображает пятый вопрос (NPS from 0) при высокой оценке NPS', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en&device=desktop')
      // Navigate through previous questions
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click()
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click()
      await page.getByText('Далее').click()

      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.first().click()
      await page.getByText('Далее').click()

      const firstNpsItems = page.getByTestId('rating-nps-item')
      await firstNpsItems.nth(8).click()
      await page.getByText('Далее').click()

      // Verify NPS (from 0) is visible
      const secondNpsItems = page.getByTestId('rating-nps-item')
      await expect(secondNpsItems.first()).toHaveText('0')

      // Fill NPS rating
      await secondNpsItems.nth(6).click() // Rating 7
      await page.getByText('Далее').click()

      // Verify next question is visible
      await expectQuestionToBeVisible(page, 'Оцените по шкале')
    })

    test('Скрывает шестой вопрос (Scale) при низкой оценке NPS', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en')
      // Navigate through previous questions
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click()
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click()
      await page.getByText('Далее').click()

      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.first().click()
      await page.getByText('Далее').click()

      const firstNpsItems = page.getByTestId('rating-nps-item')
      await firstNpsItems.nth(8).click()
      await page.getByText('Далее').click()

      const secondNpsItems = page.getByTestId('rating-nps-item')
      await secondNpsItems.nth(2).click() // Low rating (2)
      await page.getByText('Завершить').click()

      // Verify Scale question is hidden
      await expectQuestionToBeHidden(page, 'Оцените по шкале')
    })

    test('Скрывает шестой вопрос (Scale) при высокой оценке NPS, но имеющемся device=mobile', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en&device=mobile')

      // Navigate through previous questions
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click()
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click()
      await page.getByText('Далее').click()

      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.first().click()
      await page.getByText('Далее').click()

      const firstNpsItems = page.getByTestId('rating-nps-item')
      await firstNpsItems.nth(8).click()
      await page.getByText('Далее').click()

      const secondNpsItems = page.getByTestId('rating-nps-item')
      await secondNpsItems.nth(8).click() // High rating (9)
      await page.getByText('Завершить').click()

      // Verify Scale question is hidden
      await expectQuestionToBeHidden(page, 'Оцените по шкале')
    })

    test('Отображает шестой вопрос (Scale) при высокой оценке NPS и device=desktop', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en&device=desktop')

      // Navigate through previous questions
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click()
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click()
      await page.getByText('Далее').click()

      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.first().click()
      await page.getByText('Далее').click()

      const firstNpsItems = page.getByTestId('rating-nps-item')
      await firstNpsItems.nth(8).click()
      await page.getByText('Далее').click()

      const secondNpsItems = page.getByTestId('rating-nps-item')
      await secondNpsItems.nth(8).click() // High rating (9)
      await page.getByText('Далее').click()

      // Verify Scale question is visible
      await expectQuestionToBeVisible(page, 'Оцените по шкале')
    })

    test('Корректно обрабатывает навигацию назад через все вопросы', async ({ page }) => {
      await page.goto('/view-logic-scales?user_type=premium&region=EU-West&language=en')
      // Navigate through all questions
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(3).click()
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(7).click()
      await page.getByText('Далее').click()
      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.first().click()
      await page.getByText('Далее').click()

      const firstNpsItems = page.getByTestId('rating-nps-item')
      await firstNpsItems.nth(8).click()
      await page.getByText('Далее').click()

      const secondNpsItems = page.getByTestId('rating-nps-item')
      await secondNpsItems.nth(8).click()
      await page.getByText('Далее').click()

      // Navigate back through all questions
      for (let i = 0; i < 5; i++) {
        await page.getByText('Вернуться').click()
        await page.waitForTimeout(100)
      }

      // Verify we're back at first question
      await expectQuestionToBeVisible(page, 'Звездный рейтинг')
      await expect(page.getByTestId('star-rating-item').first()).toBeVisible()
    })
  })
  test.describe('Логика отображения: вопросы с вариантами ответов', () => {
    test('Отображает конечный экран при user_type=premium', async ({ page }) => {
      await page.goto('/view-logic-variants?user_type=premium')

      // При user_type=premium первый вопрос должен быть скрыт
      // А так как от первого вопроса зависит отображение всех последующих, то и все они должны быть скрыты
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })

    test('Отображает вопросы при корректных условиях', async ({ page }) => {
      await page.goto('/view-logic-variants')

      // First question should be visible with premium user
      await expectQuestionToBeVisible(page, 'Варианты ответов')
      await page.getByText('Вариант 1').click()
      await page.getByText('Далее').click()

      // Second question visible after selecting self variant
      await expectQuestionToBeVisible(page, 'Варианты ответов с медиа')
      await page.locator('.fc-check').last().click()
      await page.locator('textarea').first().fill('Hello world')
      await page.getByText('Далее').click()

      // Third question (filials) visible after media selection
      await expectQuestionToBeVisible(page, 'Выбор филиала')
      await page.getByText('222').click()
      await page.getByText('Далее').click()

      // Fourth question visible after filial selection
      await expectQuestionToBeVisible(page, 'Выбор филиала / Списком')
      await page.locator('.select-trigger').last().click()
      await page.getByText('Элемент 1').click()
      await page.getByText('Далее').click()

      // Fifth question visible after filial list selection
      await expectQuestionToBeVisible(page, 'Классификатор / Простой список')
      await page.getByText('Элемент 2').click()
      await page.getByText('Далее').click()

      // Last question visible after classifier selection
      await expectQuestionToBeVisible(page, 'Классификатор / Древовидный список')

      // custom end screen visible after classifier selection
      await page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__toggle').first().click()
      await page.locator('.classifier-tree-node__content:has-text("Категория 11") .classifier-tree-node__toggle').first().click()

      await page.waitForTimeout(100)

      await page.locator('.fc-check').first().click()
      await page.getByText('Завершить').click()

      await expectQuestionToBeVisible(page, 'Конечный блок')
    })

    test('Скрывает вопросы и показывает дефолтный конечный экран при невыполнении условий', async ({ page }) => {
      await page.goto('/view-logic-variants')

      // First question visible by default
      await expectQuestionToBeVisible(page, 'Варианты ответов')
      await page.getByText('Вариант 3').click() // Select variant that doesn't meet condition for Q2
      await page.getByText('Завершить').click()

      // Should show default end screen since next questions are hidden
      await expectQuestionToBeVisible(page, 'Опрос успешно пройден!')
    })

    test('Скрывает последующие вопросы при невыполнении условия для вопроса с медиа', async ({ page }) => {
      await page.goto('/view-logic-variants')

      // Navigate to second question
      await page.getByText('Вариант 1').click()
      await page.getByText('Далее').click()

      // Second question should be visible
      await expectQuestionToBeVisible(page, 'Варианты ответов с медиа')
      await page.locator('.fc-check__box').nth(1).click() // Select variant that doesn't meet condition for Q3
      await page.getByText('Завершить').click()

      // Should show default end screen since next questions are hidden
      await expectQuestionToBeVisible(page, 'Опрос успешно пройден!')
    })

    test('Скрывает последующие вопросы при невыполнении условия для филиалов', async ({ page }) => {
      await page.goto('/view-logic-variants')

      // Navigate to third question
      await page.getByText('Вариант 1').click()
      await page.getByText('Далее').click()
      await page.locator('.fc-check__box').first().click()
      await page.getByText('Далее').click()

      // Third question (filials) should be visible
      await expectQuestionToBeVisible(page, 'Выбор филиала')
      await page.getByText('Элемент 1').click() // Select filial that doesn't meet condition for Q4
      await page.getByText('Завершить').click()

      // Should show default end screen since next questions are hidden
      await expectQuestionToBeVisible(page, 'Опрос успешно пройден!')
    })

    test('Скрывает последующие вопросы при невыполнении условия для классификатора', async ({ page }) => {
      await page.goto('/view-logic-variants')

      // Navigate to fifth question
      await page.getByText('Вариант 1').click()
      await page.getByText('Далее').click()
      await page.locator('.fc-check__box').first().click()
      await page.getByText('Далее').click()
      await page.getByText('222').click()
      await page.getByText('Далее').click()

      await page.waitForTimeout(300)
      await page.locator('.select-trigger').last().click()
      await page.getByText('Элемент 1').click()
      await page.getByText('Далее').click()

      // Fifth question (classifier) should be visible
      await expectQuestionToBeVisible(page, 'Классификатор / Простой список')
      await page.getByText('222').click() // Select element that doesn't meet condition for Q6
      await page.getByText('Завершить').click()

      // Should show default end screen since next questions are hidden
      await expectQuestionToBeVisible(page, 'Опрос успешно пройден!')
    })

    test('Скрывает последний конечный экран при невыполнении условия для последнего вопроса', async ({ page }) => {
      await page.goto('/view-logic-variants')

      // Navigate to fifth question
      await page.getByText('Вариант 1').click()
      await page.getByText('Далее').click()
      await page.locator('.fc-check__box').first().click()
      await page.getByText('Далее').click()
      await page.getByText('222').click()
      await page.getByText('Далее').click()

      await page.waitForTimeout(300)
      await page.locator('.select-trigger').last().click()
      await page.getByText('Элемент 1').click()
      await page.getByText('Далее').click()

      // Fifth question (classifier) should be visible
      await expectQuestionToBeVisible(page, 'Классификатор / Простой список')
      await page.getByText('Элемент 1').first().click() // Select element that doesn't meet condition for Q6
      await page.getByText('Далее').click()

      await expectQuestionToBeVisible(page, 'Классификатор / Древовидный список')

      await page.getByText('Элемент 3').first().click()
      await page.getByText('Завершить').click()

      // Should show default end screen since end screen condition is not met
      await expectQuestionToBeVisible(page, 'Опрос успешно пройден!')
    })
  })
  test.describe('Логика отображения: вопросы с оценкой для каждого варианта', () => {
    test('Отображает вопросы при корректных условиях', async ({ page }) => {
      await page.goto('/view-logic-variant-scales')

      // Star Rating Variants
      const starRatings = await page.getByTestId('star-rating')
      await expect(starRatings).toHaveCount(3)

      // Set ratings 4, 5, 3
      await starRatings.nth(0).getByTestId('star-rating-item').nth(3).click()
      await starRatings.nth(1).getByTestId('star-rating-item').nth(4).click()
      await starRatings.nth(2).getByTestId('star-rating-item').nth(2).click()

      await page.getByText('Далее').click()

      // NPS Rating should be visible
      await expectQuestionToBeVisible(page, 'NPS Rating')
      const npsRatings = await page.getByTestId('rating-nps')
      await expect(npsRatings).toHaveCount(2)

      // Set NPS ratings 1, 7
      await npsRatings.nth(0).getByTestId('rating-nps-item').nth(1).click()
      await npsRatings.nth(1).getByTestId('rating-nps-item').nth(7).click()

      await page.getByText('Далее').click()

      // Scale should be visible
      await expectQuestionToBeVisible(page, 'Scale')
      const scaleVariants = await page.locator('.scale-item')
      await expect(scaleVariants).toHaveCount(2)

      // Set scale values to 70 and 30
      const firstScaleInput = scaleVariants.nth(0).locator('input[type="text"]')
      await firstScaleInput.fill('70')
      await firstScaleInput.blur()
      const secondScaleInput = scaleVariants.nth(1).locator('input[type="text"]')
      await secondScaleInput.fill('30')
      await secondScaleInput.blur()

      await page.getByText('Далее').click()

      // Matrix should be visible
      await expectQuestionToBeVisible(page, 'Matrix')
      const matrixRows = await page.locator('.matrix-question__row')
      await expect(matrixRows).toHaveCount(3)

      // Click first column in each row
      for (let i = 0; i < 3; i++) {
        await matrixRows.nth(i).getByRole('button').first().click()
      }

      await page.getByText('Завершить').click()

      // End screen should be visible
      await expectQuestionToBeVisible(page, 'Конечный блок')
    })
  })
  test.describe('Логика отображения: несколько вопросов на странице', () => {
    test('При невыполнении условий все вопросы на странице 2 скрыты, переход на страницу 3', async ({ page }) => {
      await page.goto('/view-logic-multiple-questions')

      // Set ratings for questions on page 1 that don't meet any conditions for page 2
      // ЗР 1 = 2, ЗР 2 = 3, ЗР 3 = 4 (conditions require 1, 2, 3 respectively)
      const starRatings = await page.getByTestId('star-rating')
      await starRatings.nth(0).getByTestId('star-rating-item').nth(1).click() // Rating 2
      await starRatings.nth(1).getByTestId('star-rating-item').nth(2).click() // Rating 3
      await starRatings.nth(2).getByTestId('star-rating-item').nth(3).click() // Rating 4

      await page.getByText('Далее').click()

      // Verify questions on page 2 are hidden
      await expectQuestionToBeHidden(page, 'ЗР 4')
      await expectQuestionToBeHidden(page, 'ЗР 5')
      await expectQuestionToBeHidden(page, 'ЗР 6')

      // Click next and verify we're on page 3 by checking its questions are visible
      await expectQuestionToBeVisible(page, 'ЗР 7')
      await expectQuestionToBeVisible(page, 'ЗР 8')
      await expectQuestionToBeVisible(page, 'ЗР 9')
    })

    test('При выполнении условий вопросы на странице 2 отображаются', async ({ page }) => {
      await page.goto('/view-logic-multiple-questions')

      // Set ratings for questions on page 1 that meet all conditions for page 2
      // ЗР 1 = 1, ЗР 2 = 2, ЗР 3 = 3 (matching conditions)
      const starRatings = await page.getByTestId('star-rating')
      await starRatings.nth(0).getByTestId('star-rating-item').nth(0).click() // Rating 1
      await starRatings.nth(1).getByTestId('star-rating-item').nth(1).click() // Rating 2
      await starRatings.nth(2).getByTestId('star-rating-item').nth(2).click() // Rating 3

      await page.getByText('Далее').click()

      // Verify questions on page 2 are visible
      await expectQuestionToBeVisible(page, 'ЗР 4')
      await expectQuestionToBeVisible(page, 'ЗР 5')
      await expectQuestionToBeVisible(page, 'ЗР 6')
    })

    test('Последняя страница скрыта при выполнении условий hide_if', async ({ page }) => {
      await page.goto('/view-logic-multiple-questions')

      // Navigate through pages 1 and 2
      const starRatings = await page.getByTestId('star-rating')
      // Page 1: Set any ratings to proceed
      await starRatings.nth(0).getByTestId('star-rating-item').nth(4).click()
      await starRatings.nth(1).getByTestId('star-rating-item').nth(4).click()
      await starRatings.nth(2).getByTestId('star-rating-item').nth(4).click()
      await page.getByText('Далее').click()

      // Page 3: Set ratings that trigger hide conditions (1, 2, 3)
      await starRatings.nth(0).getByTestId('star-rating-item').nth(0).click() // Rating 1
      await starRatings.nth(1).getByTestId('star-rating-item').nth(1).click() // Rating 2
      await starRatings.nth(2).getByTestId('star-rating-item').nth(2).click() // Rating 3

      await page.getByText('Завершить').click()

      // Verify question 10 is hidden
      await expectQuestionToBeHidden(page, 'ЗР 10')
      // Verify we see the completion state
      await expectQuestionToBeVisible(page, 'Опрос успешно пройден!')
    })

    test('Последняя страница отображается если условия hide_if не выполнены', async ({ page }) => {
      await page.goto('/view-logic-multiple-questions')

      // Navigate through pages 1 and 2
      const starRatings = await page.getByTestId('star-rating')
      // Page 1: Set any ratings to proceed
      await starRatings.nth(0).getByTestId('star-rating-item').nth(4).click()
      await starRatings.nth(1).getByTestId('star-rating-item').nth(4).click()
      await starRatings.nth(2).getByTestId('star-rating-item').nth(4).click()
      await page.getByText('Далее').click()

      // Page 3: Set ratings that don't trigger hide conditions (4, 4, 4)
      await starRatings.nth(0).getByTestId('star-rating-item').nth(3).click() // Rating 4
      await starRatings.nth(1).getByTestId('star-rating-item').nth(3).click() // Rating 4
      await starRatings.nth(2).getByTestId('star-rating-item').nth(3).click() // Rating 4

      await page.getByText('Далее').click()

      // Verify question 10 is visible
      await expectQuestionToBeVisible(page, 'ЗР 10')
    })
  })
})
