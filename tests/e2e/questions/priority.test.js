import { expect, test } from '@playwright/test'
import { drag } from '../testUtils'

test.describe('Вопрос \'Приоритет\'', () => {
  test('отображает список приоритетов с комментарием, позволяет перейти к следующему вопросу', async ({ page }) => {
    await page.goto('/priority-question')
    const nextButton = await page.getByText('Далее')
    const priorityItems = await page.locator('.priority-item')
    const commentTextarea = await page.locator('textarea')

    await expect(priorityItems).toHaveCount(4)
    await expect(commentTextarea).toBeVisible()

    // Делаем скриншот перед переходом к следующему вопросу
    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('priority-question-default.png')

    // Переходим к следующему вопросу
    await nextButton.click()
    await expect(priorityItems.first()).not.toBeVisible()
  })

  test('отображает ошибку, когда reorder_required включен и порядок не изменен', async ({ page }) => {
    await page.goto('/priority-question-reorder-required')
    const nextButton = await page.getByText('Далее')
    const priorityItems = await page.locator('.priority-item')
    const error = await page.getByText('Нужно изменить порядок')

    await expect(priorityItems).toHaveCount(4)

    // Пытаемся перейти к следующему вопросу без изменения порядка
    await nextButton.click()
    await expect(error).toBeVisible()

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('priority-question-reorder-required-error.png')

    // Меняем порядок элементов
    const firstItem = priorityItems.nth(0)
    const lastItem = priorityItems.nth(3)

    /**
     * @NOTE У playwright есть метод dragTo для локаторов, но он не работает корректно
     * Поэтому используем ручное перетаскивание
     * @see https://github.com/microsoft/playwright/issues/20254#issuecomment-1771669110
     */
    await drag(page, firstItem, lastItem)

    await expect(error).not.toBeVisible()

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('priority-question-reorder-required-reordered.png')

    // Переходим к следующему вопросу после изменения порядка
    await nextButton.click()
    await expect(priorityItems.first()).not.toBeVisible()
  })
})
