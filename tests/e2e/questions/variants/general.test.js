import { expect, test } from '@playwright/test'
import { getTestModes } from '../../testUtils'

for (const { name, urlParam, getScreenshotPath, viewport, setupPage } of getTestModes()) {
  test.describe(`Варианты ответов (${name})`, () => {
    test.beforeEach(async ({ page }) => {
      if (viewport) {
        await page.setViewportSize(viewport)
      }
      if (setupPage) {
        await setupPage(page)
      }
    })

    test('variants-default: renders checkgroup with radios, single choice', async ({ page }) => {
      await page.goto(`/variants-default${urlParam}`)
      const checkGroup = await page.getByTestId('variants-check')
      const nextButton = await page.getByTestId('poll-action-next')
      const error = await page.getByText('Нужно выбрать один из вариантов')

      await expect(checkGroup).toHaveCount(4)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-default-initial'))

      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-default-error'))

      const firstOption = await checkGroup.first()
      await firstOption.click()
      if (name === 'Планшетный вид') {
        await expect(firstOption).not.toBeVisible()
        return
      } else {
        await expect(error).not.toBeVisible()
        await expect(page).toHaveScreenshot(getScreenshotPath('variants-default-selected'))
      }

      const selfVariant = await page.getByTestId('variants-check').last()
      await selfVariant.click()
      const textarea = await page.locator('.question-variants__self-variant-comment textarea')
      await expect(textarea).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-default-self-variant'))

      const selfVariantError = await page.getByText('Обязательное поле')
      await expect(selfVariantError).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-default-self-variant-error'))

      await textarea.fill('Some text')
      await expect(selfVariantError).not.toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-default-self-variant-filled'))
    })

    test('variants-unrequired: goes to next question when clicked next button', async ({ page }) => {
      await page.goto(`/variants-unrequired${urlParam}`)
      const nextButton = await page.getByTestId('poll-action-next')
      const question = await page.getByText('Варианты ответов').locator('visible=true')

      await nextButton.click()
      await expect(question).not.toBeVisible()
    })

    test('variants-multiple: renders checkboxes, multiple items', async ({ page }) => {
      await page.goto(`/variants-multiple${urlParam}`)
      const checkGroup = await page.getByTestId('variants-check')

      await expect(checkGroup).toHaveCount(4)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-multiple-initial'))

      // Select multiple options
      const firstOption = await checkGroup.first()
      const secondOption = await checkGroup.nth(1)
      await firstOption.click()
      await secondOption.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-multiple-selected'))
    })

    test('variants-max-choose: only 2 items can be enabled, others are disabled', async ({ page }) => {
      await page.goto(`/variants-max-choose${urlParam}`)
      const checkGroup = await page.getByTestId('variants-check')

      await expect(checkGroup).toHaveCount(4)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-max-choose-initial'))

      // Select two options
      const firstOption = await checkGroup.first()
      const secondOption = await checkGroup.nth(1)
      await firstOption.click()
      await secondOption.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-max-choose-two-selected'))

      // Try to select third option
      const thirdOption = await checkGroup.nth(2)
      await thirdOption.click()
      await expect(thirdOption).toHaveClass(/disabled/g)
    })

    test('variants-min-choose: shows different error "Необходимо выбрать хотя бы 2 варианта"', async ({ page }) => {
      await page.goto(`/variants-min-choose${urlParam}`)
      const checkGroup = await page.getByTestId('variants-check')
      const nextButton = await page.getByTestId('poll-action-next')
      const error = await page.getByText('Необходимо выбрать хотя бы 2 варианта')

      await expect(checkGroup).toHaveCount(4)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-min-choose-initial'))

      // Click next without selecting any option
      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-min-choose-error'))

      // Select one option
      const firstOption = await checkGroup.first()
      await firstOption.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-min-choose-one-selected'))

      // Select second option
      const secondOption = await checkGroup.nth(1)
      await secondOption.click()
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-min-choose-two-selected'))
    })

    test('variants-nothing: when "Ничего из вышеперечисленного" is checked or self_variant, others are unchecked', async ({ page }) => {
      await page.goto(`/variants-nothing${urlParam}`)
      const checkGroup = await page.getByTestId('variants-check')

      await expect(checkGroup).toHaveCount(5)

      // Check other options are unchecked
      const firstOption = await checkGroup.first()
      const secondOption = await checkGroup.nth(1)
      const thirdOption = await checkGroup.nth(2)

      await firstOption.click()
      await secondOption.click()
      await thirdOption.click()

      const pageTitle = await page.getByText('Варианты ответов').locator('visible=true')
      await pageTitle.scrollIntoViewIfNeeded()
      await expect(pageTitle).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-nothing-initial'))

      // Select "Ничего из вышеперечисленного"
      const nothingOption = await checkGroup.nth(3)
      await nothingOption.click()

      await expect(firstOption).not.toHaveClass(/checked/)
      await expect(secondOption).not.toHaveClass(/checked/)
      await expect(thirdOption).not.toHaveClass(/checked/)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-nothing-selected'))

      // Select self variant
      const selfVariant = await checkGroup.last()
      await selfVariant.click()
      const textarea = await page.locator('.question-variants__self-variant-comment textarea')
      await expect(textarea).toBeVisible()

      await page.waitForTimeout(300)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-nothing-self-variant'))
    })
    test('variants-skip: goes to next question when clicked next button', async ({ page }) => {
      await page.goto(`/variants-skip${urlParam}`)
      const nextButton = await page.getByTestId('poll-action-next')
      const skipButton = await page.getByText('Затрудняюсь ответить')
      const question = await page.getByText('Варианты ответов').locator('visible=true')

      await expect(question).toBeVisible()
      await skipButton.click()
      await nextButton.click()
      await expect(question).not.toBeVisible()
    })
    test('Отображает ошибку валидации комментария для определенных вариантов', async ({ page }) => {
      await page.goto(`/variants-comment-required-for-some-variants${urlParam}`)
      const nextButton = await page.getByTestId('poll-action-next')
      const question = await page.getByText('Варианты ответов').locator('visible=true')
      const checkGroup = await page.getByTestId('variants-check')
      const textarea = await page.locator('.survey-questions__comment-form-group textarea')
      const error = await page.getByText('Обязательное поле при выборе вариантов:')

      await expect(question).toBeVisible()
      await expect(checkGroup).toHaveCount(4)
      await expect(error).not.toBeVisible()

      await checkGroup.nth(1).click()

      await page.waitForTimeout(300)

      await nextButton.click()
      await expect(error).toBeVisible()

      await error.scrollIntoViewIfNeeded()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-comment-required-for-some-variants-error'))

      await textarea.fill('Some text')
      await expect(error).not.toBeVisible()
    })
  })
}
