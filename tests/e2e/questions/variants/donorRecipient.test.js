import { expect, test } from '@playwright/test'
import { getTestModes } from '../../testUtils'

for (const { name, urlParam, viewport, setupPage } of getTestModes(['default', 'simple'])) {
  test.describe(`Варинты ответов: Донор-Реципиент (${name})`, () => {
    test.beforeEach(async ({ page }) => {
      if (viewport) {
        await page.setViewportSize(viewport)
      }
      if (setupPage) {
        await page.reload()
        await setupPage(page)
      }
    })

    test('variants-donor-recipient: renders donor-recipient variants', async ({ page }) => {
      await page.goto(`/variants-donor-recipient${urlParam}`)
      const nextButton = await page.getByTestId('poll-action-next')
      const donorChecks = await page.getByTestId('variants-check')
      await donorChecks.first().click() // "вар 1"
      await donorChecks.nth(1).click() // "вар 2"

      // Select self variant and enter text
      const selfVariant = await donorChecks.last()
      await selfVariant.click()
      const textarea = await page.locator('.question-variants__self-variant-comment textarea')
      await textarea.fill('Текст своего варианта')

      await nextButton.click()

      // Step 2: Verify selected variants are visible
      await expect(page.getByText('вар 1').first()).toBeVisible()
      await expect(page.getByText('вар 2').first()).toBeVisible()
      await expect(page.getByText('Текст своего варианта').first()).toBeVisible()

      // Click next to move to recipient question
      await page.getByTestId('poll-action-next').click()

      await page.waitForTimeout(300)

      // Step 3: Select first variant in recipient and verify navigation
      const recipientChecks = await page.locator('.fc-check__box')
      await recipientChecks.first().click()
      await page.getByTestId('poll-action-next').click()

      // Step 4: Select all checkboxes and fill textarea
      const stepFourChecks = await page.locator('.fc-check__box')

      for (const check of await stepFourChecks.all()) {
        await expect(check).toBeVisible()
        await check.click()
        await page.waitForTimeout(100)
      }

      await page.getByTestId('poll-action-next').click()

      await page.waitForTimeout(300)

      // Step 5: Handle checkboxes with proper waiting
      const stepFiveChecks = await page.locator('.fc-check__box').locator('visible=true')
      await page.waitForTimeout(100)

      for (const check of await stepFiveChecks.all()) {
        await expect(check).toBeVisible()
        await check.click()
        await page.waitForTimeout(100)
      }
      const stepFiveTextarea = await page.locator('.question-variants__self-variant-comment textarea')
      await stepFiveTextarea.fill('Текст своего варианта')

      await page.getByTestId('poll-action-next').click()

      await page.waitForTimeout(300)

      // Step 6: Handle checks and hints with proper waiting
      const pageSixChecks = await page.locator('.fc-check')
      await expect(pageSixChecks).toHaveCount(4) // Explicitly wait for expected number of checks

      const pageSixHints = [
        'подсказка вар 1',
        'подсказка вар 2',
        'подсказка вар 3',
        'подсказка для своего варианта',
      ]

      const pageSixHintsElements = await page.locator('.fc-check__hint').all()

      for (const [i, check] of (await pageSixChecks.all()).entries()) {
        await expect(check).toBeVisible()
        await check.click()
        await expect(pageSixHintsElements[i]).toBeVisible()
        await pageSixHintsElements[i].hover()
        await expect(await page.getByText(pageSixHints[i]).first()).toBeVisible()
      }

      await page.getByTestId('poll-action-next').click()

      await page.waitForTimeout(300)

      // Step 7: Handle final page checks with proper waiting
      const pageSevenCheckboxes = await page.locator('.fc-check')
      await expect(pageSevenCheckboxes).toHaveCount(4)

      const pageSevenHints = [
        'подсказка вар 1123',
        'подсказка вар 2',
        'подсказка вар 3',
        'подсказка для своего варианта',
      ]

      const pageSevenHintsElements = await page.locator('.fc-check__hint').all()

      for (const [i, check] of (await pageSevenCheckboxes.all()).entries()) {
        await expect(check).toBeVisible()
        await expect(check).toBeEnabled()
        await check.click()
        await expect(pageSevenHintsElements[i]).toBeVisible()
        await pageSevenHintsElements[i].hover()
        await expect(await page.getByText(pageSevenHints[i], { exact: true }).first()).toBeVisible()
      }
    })
  })
}
