import { expect, test } from '@playwright/test'
import { getTestModes } from '../../testUtils'

for (const { name, urlParam, getScreenshotPath, viewport } of getTestModes()) {
  test.describe(`Варинты ответов: Варианты с файлами (${name})`, () => {
    test.beforeEach(async ({ page }) => {
      if (viewport) {
        await page.setViewportSize(viewport)
      }
    })

    test('variants-with-files: renders list of FileCheck items', async ({ page }) => {
      await page.goto(`/variants-with-files${urlParam}`)
      const fileChecks = await page.locator('.file-check .fc-check__box')
      const nextButton = await page.getByTestId('poll-action-next')
      const error = await page.getByText('Нужно выбрать один из вариантов')

      await expect(fileChecks).toHaveCount(3)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-initial'))

      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-error'))

      const firstFileCheck = await fileChecks.first()
      await firstFileCheck.click()
      if (name === 'Планшетный вид') {
        await expect(firstFileCheck).not.toBeVisible()
      } else {
        await expect(error).not.toBeVisible()
        await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-selected'))
      }
    })

    test('variants-with-files-multiple: renders list of FileCheck items with multiple choice', async ({ page }) => {
      await page.goto(`/variants-with-files-multiple${urlParam}`)
      const fileChecks = await page.locator('.file-check .fc-check__box')

      await expect(fileChecks).toHaveCount(3)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-multiple-initial'))

      // Select multiple file variants
      const firstFileCheck = await fileChecks.first()
      const secondFileCheck = await fileChecks.nth(1)
      await firstFileCheck.click()
      await secondFileCheck.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-multiple-selected'))
    })

    test('variants-with-files-max-choose: only 2 items can be enabled, others are disabled', async ({ page }) => {
      await page.goto(`/variants-with-files-max-choose${urlParam}`)
      const fileChecks = await page.locator('.file-check .fc-check__box')

      await expect(fileChecks).toHaveCount(3)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-max-choose-initial'))

      // Select two file variants
      const firstFileCheck = await fileChecks.first()
      const secondFileCheck = await fileChecks.nth(1)
      await firstFileCheck.click()
      await secondFileCheck.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-max-choose-two-selected'))

      // Try to select third file variant
      const thirdFileCheck = await page.locator('.file-check').nth(2)
      await expect(thirdFileCheck).toHaveClass(/disabled/)
    })

    test('variants-with-files-min-choose: shows different error "Необходимо выбрать хотя бы 2 варианта"', async ({ page }) => {
      await page.goto(`/variants-with-files-min-choose${urlParam}`)
      const fileChecks = await page.locator('.file-check .fc-check__box')
      const nextButton = await page.getByTestId('poll-action-next')
      const error = await page.getByText('Необходимо выбрать хотя бы 2 варианта')

      await expect(fileChecks).toHaveCount(3)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-min-choose-initial'))

      // Click next without selecting any option
      await nextButton.click()
      await error.scrollIntoViewIfNeeded()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-min-choose-error'))

      // Select one file variant
      const firstFileCheck = await fileChecks.first()
      await firstFileCheck.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-min-choose-one-selected'))

      // Select second file variant
      const secondFileCheck = await fileChecks.nth(1)
      await secondFileCheck.click()
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-with-files-min-choose-two-selected'))
    })
  })
}
