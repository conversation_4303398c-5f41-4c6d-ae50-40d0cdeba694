import { expect, test } from '@playwright/test'
import { getTestModes } from '../../testUtils'

for (const { name, urlParam, getScreenshotPath, viewport } of getTestModes(['default', 'simple'])) {
  test.describe(`Варинты ответов: Выпадающий список (${name})`, () => {
    test.beforeEach(async ({ page }) => {
      if (viewport) {
        await page.setViewportSize(viewport)
      }
    })

    test('variants-dropdown: renders dropdown with single choice', async ({ page }) => {
      await page.goto(`/variants-dropdown${urlParam}`)
      const selectTrigger = await page.locator('.select-trigger--full-width')
      const nextButton = await page.getByTestId('poll-action-next')
      const error = await page.getByText('Обязательное поле')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-initial'))

      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-error'))

      await selectTrigger.click()
      await page.waitForTimeout(300)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-open'))

      const input = await page.locator('.command-root__header input')
      await input.fill('1')
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-input'))

      const firstOption = await page.locator('.command-item').first()
      await firstOption.click()
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-selected'))
    })

    test('variants-dropdown-multiple: renders dropdown with multiple choice', async ({ page }) => {
      await page.goto(`/variants-dropdown-multiple${urlParam}`)
      const selectTrigger = await page.locator('.select-trigger--full-width')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-multiple-initial'))

      await selectTrigger.click()
      await page.waitForTimeout(300)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-multiple-open'))

      const firstOption = await page.locator('.command-item').first()
      const secondOption = await page.locator('.command-item').nth(1)
      await firstOption.click()
      await secondOption.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-multiple-selected'))
    })

    test('variants-dropdown-max-choose: only 2 items can be enabled, others are disabled', async ({ page }) => {
      await page.goto(`/variants-dropdown-max-choose${urlParam}`)
      const selectTrigger = await page.locator('.select-trigger--full-width')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-max-choose-initial'))

      await selectTrigger.click()
      const firstOption = await page.locator('.command-item').first()
      const secondOption = await page.locator('.command-item').nth(1)
      await firstOption.click()
      await secondOption.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-max-choose-two-selected'))

      const thirdOption = await page.locator('.command-item').nth(2)
      await expect(thirdOption).toHaveClass(/disabled/)
      const selectedOptions = await page.locator('.select-trigger--full-width .select-trigger__label')
      await expect(selectedOptions).toHaveCount(2)
    })

    test('variants-dropdown-min-choose: shows different error "Необходимо выбрать хотя бы 2 варианта"', async ({ page }) => {
      await page.goto(`/variants-dropdown-min-choose${urlParam}`)
      const selectTrigger = await page.locator('.select-trigger--full-width')
      const nextButton = await page.getByTestId('poll-action-next')
      const error = await page.getByText('Необходимо выбрать хотя бы 2 варианта')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-min-choose-initial'))

      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-min-choose-error'))

      await selectTrigger.click()
      const firstOption = await page.locator('.command-item').first()
      await firstOption.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-min-choose-one-selected'))

      const secondOption = await page.locator('.command-item').nth(1)
      await secondOption.click()
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-min-choose-two-selected'))
    })

    test('variants-dropdown-nothing: when "Ничего из вышеперечисленного" is selected, others are unchecked', async ({ page }) => {
      await page.goto(`/variants-dropdown-nothing${urlParam}`)
      const selectTrigger = await page.locator('.select-trigger--full-width')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-nothing-initial'))

      await selectTrigger.click()
      const firstOption = await page.locator('.command-item').first()
      const secondOption = await page.locator('.command-item').nth(1)
      const thirdOption = await page.locator('.command-item').nth(2)
      const nothingOption = await page.locator('.command-item').nth(3)

      await firstOption.click()
      await secondOption.click()
      await thirdOption.click()

      await nothingOption.click()
      await expect(firstOption).not.toHaveClass(/checked/)
      await expect(secondOption).not.toHaveClass(/checked/)
      await expect(thirdOption).not.toHaveClass(/checked/)
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-nothing-selected'))

      const selectedOptions = await page.locator('.select-trigger--full-width .select-trigger__label')
      await expect(selectedOptions).toHaveCount(1)
    })
  })
}
