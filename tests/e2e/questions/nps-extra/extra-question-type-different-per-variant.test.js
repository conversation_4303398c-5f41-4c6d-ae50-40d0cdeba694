import { expect, test } from '@playwright/test'

test.describe('NPS: Разный УВ для каждого варианта', () => {
  test('Разные Для Вариантов Базовый тест', async ({ page }) => {
    await page.goto('/nps-extra-questions-different-per-variant')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)
    const thirdVariant = await npsVariants.nth(2)

    // Тестируем первый вариант с оценкой 3 (дополнительный вопрос с одиночным выбором)
    await firstVariant.getByTestId('rating-nps-item').nth(3).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionFirst = page.locator('.question-variants').first()
    await expect(extraQuestionFirst).toBeVisible() // Increased timeout

    // Проверяем, что дополнительный вопрос появляется и взаимодействуем с одиночным выбором
    await extraQuestionFirst.getByText('Дополнительный вариант Б для Варианта 1').click()

    // Тестируем второй вариант с оценкой 8 (дополнительный вопрос с множественным выбором)
    await secondVariant.getByTestId('rating-nps-item').nth(8).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionSecond = page.locator('.question-variants').nth(1)
    await expect(extraQuestionSecond).toBeVisible() // Increased timeout

    // Выбираем несколько вариантов для второго варианта
    const checkboxesSecond = extraQuestionSecond.locator('.fc-check')
    await checkboxesSecond.getByText('Дополнительный вариант В для Варианта 2').click()
    await checkboxesSecond.getByText('Дополнительный вариант Г для Варианта 2').click()

    // Тестируем третий вариант с оценкой 2 (дополнительный вопрос с текстовым вводом)
    await thirdVariant.getByTestId('rating-nps-item').nth(2).click()
    const extraQuestionThird = page.locator('.question-variants').nth(2)
    await expect(extraQuestionThird).toBeVisible() // Increased timeout
    await extraQuestionThird.locator('textarea').fill('Подробный отзыв для низкой оценки')

    // scroll top
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })

    // Делаем скриншот после заполнения всех дополнительных вопросов
    await expect(page).toHaveScreenshot('nps-different-per-variant-all-filled-top-scrolled.png')

    // scroll bottom
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })

    // Делаем скриншот после заполнения всех дополнительных вопросов
    await expect(page).toHaveScreenshot('nps-different-per-variant-all-filled-bottom-scrolled.png')

    // Отправляем и проверяем навигацию
    await page.getByText('Далее').click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Разные Для Вариантов с Файлами', async ({ page }) => {
    await page.goto('/nps-extra-questions-different-per-variant-with-files')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)
    const thirdVariant = await npsVariants.nth(2)

    // Тестируем первый вариант с одиночным выбором и файлами
    await firstVariant.getByTestId('rating-nps-item').nth(5).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionFirst = page.locator('.question-variants').first()
    await expect(extraQuestionFirst).toBeVisible() // Increased timeout

    // Проверяем, что элементы галереи видны и выбираем один
    const galleryFirst = extraQuestionFirst.locator('.fc-check__box')
    await expect(galleryFirst.first()).toBeVisible()
    await galleryFirst.first().click()

    // Тестируем второй вариант с множественным выбором и файлами
    await secondVariant.getByTestId('rating-nps-item').nth(8).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionSecond = page.locator('.question-variants').nth(1)
    await expect(extraQuestionSecond).toBeVisible() // Increased timeout

    // Выбираем несколько элементов галереи
    const gallerySecond = extraQuestionSecond.locator('.fc-check__box')
    await expect(gallerySecond.first()).toBeVisible()
    await gallerySecond.nth(0).click()
    await gallerySecond.nth(1).click()

    // Тестируем третий вариант с текстовым вводом и файлами
    await thirdVariant.getByTestId('rating-nps-item').nth(2).click()
    await page.waitForTimeout(2000) // Increased delay significantly
    const extraQuestionThird = page.locator('.question-variants').nth(2)
    await expect(extraQuestionThird).toBeVisible() // Increased timeout significantly

    // Заполняем текстовое поле для третьего дополнительного вопроса
    await extraQuestionThird.locator('textarea').fill('Подробный отзыв для Варианта 3')

    await page.waitForTimeout(500)

    // прокручиваем страницу вниз
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })

    await expect(page).toHaveScreenshot('nps-different-per-variant-with-files.png')
    await page.getByText('Далее').click()
    await expect(firstVariant).not.toBeVisible()
  })
})
