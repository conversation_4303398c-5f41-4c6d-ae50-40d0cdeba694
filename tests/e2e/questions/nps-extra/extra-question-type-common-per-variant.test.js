import { expect, test } from '@playwright/test'

test.describe('NPS: Общий УВ для каждого варианта', () => {
  test('Общие Для Вариантов Одиночный Выбор', async ({ page }) => {
    await page.goto('/nps-extra-questions-common-per-variant-single-choice')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Тестируем первый вариант с оценкой 3 (должен вызвать дополнительный вопрос для диапазона 0-5)
    await firstVariant.getByTestId('rating-nps-item').nth(3).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionFirst = page.locator('.question-variants').first()
    await expect(extraQuestionFirst).toBeVisible() // Increased timeout

    // Проверяем, что дополнительный вопрос появляется и взаимодействуем с ним
    await extraQuestionFirst.getByText('Дополнительный вариант А').click()

    // Тестируем второй вариант с оценкой 8 (должен вызвать дополнительный вопрос для диапазона 6-10)
    await secondVariant.getByTestId('rating-nps-item').nth(8).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionSecond = page.locator('.question-variants').nth(1)
    await expect(extraQuestionSecond).toBeVisible() // Increased timeout

    // Проверяем, что дополнительный вопрос появляется и взаимодействуем с ним
    await extraQuestionSecond.getByText('Дополнительный вариант Б').click()

    // Проверяем опцию "Другой отзыв" с обязательным комментарием
    await extraQuestionSecond.getByText('Другой отзыв').click()
    const commentTextarea = extraQuestionSecond.locator('textarea')
    await commentTextarea.fill('Подробный отзыв для высокой оценки')

    // Делаем скриншот после заполнения обоих дополнительных вопросов
    await expect(page).toHaveScreenshot('nps-common-per-variant-single-choice-extra-filled.png')

    // Отправляем и проверяем навигацию
    await page.getByText('Далее').click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Общие Для Вариантов Множественный Выбор', async ({ page }) => {
    await page.goto('/nps-extra-questions-common-per-variant-multiple-choice')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Тестируем первый вариант с оценкой 2 (должен вызвать дополнительный вопрос для диапазона 0-5)
    await firstVariant.getByTestId('rating-nps-item').nth(2).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionFirst = page.locator('.question-variants').first()
    await expect(extraQuestionFirst).toBeVisible() // Increased timeout

    // Выбираем несколько вариантов для первого варианта
    const checkboxesFirst = extraQuestionFirst.locator('.fc-check')
    await checkboxesFirst.getByText('Дополнительный вариант А').click()
    await checkboxesFirst.getByText('Дополнительный вариант Б').click()

    // Тестируем второй вариант с оценкой 7 (должен вызвать дополнительный вопрос для диапазона 6-10)
    await secondVariant.getByTestId('rating-nps-item').nth(7).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionSecond = page.locator('.question-variants').nth(1)
    await expect(extraQuestionSecond).toBeVisible() // Increased timeout

    // Выбираем несколько вариантов и "Другое" для второго варианта
    const checkboxesSecond = extraQuestionSecond.locator('.fc-check')
    await checkboxesSecond.getByText('Дополнительный вариант А').click()
    await checkboxesSecond.getByText('Другой отзыв').click()
    const commentTextarea = extraQuestionSecond.locator('textarea')
    await commentTextarea.fill('Отзыв для множественного выбора')
    await page.waitForTimeout(500)

    await expect(page).toHaveScreenshot('nps-common-per-variant-multiple-choice-extra-filled.png')
    await page.getByText('Далее').click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Общие Для Вариантов Текстовый Ввод', async ({ page }) => {
    await page.goto('/nps-extra-questions-common-per-variant-text')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Тестируем первый вариант с оценкой 4 (должен вызвать дополнительный вопрос для диапазона 0-5)
    await firstVariant.getByTestId('rating-nps-item').nth(4).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionFirst = page.locator('.question-variants').first()
    await expect(extraQuestionFirst).toBeVisible() // Increased timeout
    await extraQuestionFirst.locator('textarea').fill('Отзыв для низкой оценки')

    // Тестируем второй вариант с оценкой 9 (должен вызвать дополнительный вопрос для диапазона 6-10)
    await secondVariant.getByTestId('rating-nps-item').nth(9).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionSecond = page.locator('.question-variants').nth(1)
    await expect(extraQuestionSecond).toBeVisible() // Increased timeout
    await extraQuestionSecond.locator('textarea').fill('Отзыв для высокой оценки')

    await expect(page).toHaveScreenshot('nps-common-per-variant-text-extra-filled.png')
    await page.getByText('Далее').click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Общие Для Вариантов Одиночный Выбор с Файлами', async ({ page }) => {
    await page.goto('/nps-extra-questions-common-per-variant-single-choice-with-files')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Тестируем первый вариант с оценкой 1 (должен вызвать дополнительный вопрос для диапазона 0-5)
    await firstVariant.getByTestId('rating-nps-item').nth(1).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionFirst = page.locator('.question-variants').first()
    await expect(extraQuestionFirst).toBeVisible() // Increased timeout

    // Проверяем, что элементы галереи видны и выбираем один
    const checkboxesFirst = extraQuestionFirst.locator('.fc-check__box')
    await expect(checkboxesFirst.first()).toBeVisible()
    await checkboxesFirst.first().click()

    // Тестируем второй вариант с оценкой 8 (должен вызвать дополнительный вопрос для диапазона 6-10)
    await secondVariant.getByTestId('rating-nps-item').nth(8).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionSecond = page.locator('.question-variants').nth(1)
    await expect(extraQuestionSecond).toBeVisible() // Increased timeout

    // Проверяем элементы галереи и выбираем с комментарием
    const checkboxesSecond = extraQuestionSecond.locator('.fc-check__box')
    await expect(checkboxesSecond.last()).toBeVisible()
    await checkboxesSecond.last().click()
    await extraQuestionSecond.locator('textarea').fill('Отзыв о выбранном элементе галереи')

    await expect(page).toHaveScreenshot('nps-common-per-variant-single-choice-with-files-extra-filled.png')
    await page.getByText('Далее').click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Общие Для Вариантов Множественный Выбор с Файлами', async ({ page }) => {
    await page.goto('/nps-extra-questions-common-per-variant-multiple-choice-with-files')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Тестируем первый вариант с оценкой 5 (должен вызвать дополнительный вопрос для диапазона 0-5)
    await firstVariant.getByTestId('rating-nps-item').nth(5).click()
    await page.waitForTimeout(2000) // Increased delay significantly
    const extraQuestionFirst = page.locator('.question-variants').first()
    await expect(extraQuestionFirst).toBeVisible() // Increased timeout significantly

    // Выбираем несколько элементов галереи
    const galleryFirst = extraQuestionFirst.locator('.fc-check__box')
    await expect(galleryFirst.first()).toBeVisible()
    await galleryFirst.nth(0).click()
    await galleryFirst.nth(1).click()

    // Тестируем второй вариант с оценкой 6 (должен вызвать дополнительный вопрос для диапазона 6-10)
    await secondVariant.getByTestId('rating-nps-item').nth(6).click()
    await page.waitForTimeout(1000) // Increased delay
    const extraQuestionSecond = page.locator('.question-variants').nth(1)
    await expect(extraQuestionSecond).toBeVisible() // Increased timeout

    // Выбираем несколько элементов галереи и добавляем комментарий
    const gallerySecond = extraQuestionSecond.locator('.fc-check__box')
    await expect(gallerySecond.first()).toBeVisible()
    await gallerySecond.nth(0).click()
    await gallerySecond.nth(1).click()
    await gallerySecond.last().click()
    await extraQuestionSecond.locator('textarea').fill('Отзыв о нескольких выбранных элементах галереи')

    await page.waitForTimeout(500)

    // прокручиваем страницу вниз
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight)
    })

    await expect(page).toHaveScreenshot('nps-common-per-variant-multiple-choice-with-files-extra-filled.png')
    await page.getByText('Далее').click()
    await expect(firstVariant).not.toBeVisible()
  })
})
