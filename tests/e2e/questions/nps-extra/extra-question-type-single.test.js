import { expect, test } from '@playwright/test'

test.describe('NPS: Общий УВ для всех вариантов', () => {
  test('Одиночный выбор', async ({ page }) => {
    await page.goto('/nps-extra-questions-single-choice')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Выбираем оценку 3 для первого варианта NPS
    await firstVariant.getByTestId('rating-nps-item').nth(3).click()
    // Проверяем, что дополнительный вопрос не отображается для первого варианта
    await expect(firstVariant.locator('.question-variants')).not.toBeVisible()

    // Выбираем оценку 6 для второго варианта NPS
    await secondVariant.getByTestId('rating-nps-item').nth(6).click()
    // Проверяем, что дополнительный вопрос отображается для второго варианта
    const extraQuestion = page.locator('.question-variants')
    await expect(extraQuestion).toBeVisible()
    // Делаем скриншот
    await expect(page).toHaveScreenshot('nps-single-choice-extra-visible.png')

    // Выбираем вариант "Другая причина" и заполняем комментарий
    const selfVariantRadio = extraQuestion.getByText('Другая причина')
    await selfVariantRadio.click()
    const commentTextarea = extraQuestion.locator('textarea')
    await commentTextarea.fill('Некоторый текст')
    // Делаем еще один скриншот
    await expect(page).toHaveScreenshot('nps-single-choice-extra-filled.png')

    // Нажимаем "Далее" и проверяем, что мы перешли к следующему вопросу
    const nextButton = await page.getByText('Далее')
    await nextButton.click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Множественный выбор', async ({ page }) => {
    await page.goto('/nps-extra-questions-single-multiple-choice')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Выбираем оценку 3 для первого варианта NPS
    await firstVariant.getByTestId('rating-nps-item').nth(3).click()
    // Проверяем, что дополнительный вопрос не отображается для первого варианта
    await expect(firstVariant.locator('.question-variants')).not.toBeVisible()

    // Выбираем оценку 6 для второго варианта NPS
    await secondVariant.getByTestId('rating-nps-item').nth(6).click()
    // Проверяем, что дополнительный вопрос отображается для второго варианта
    const extraQuestion = page.locator('.question-variants')
    await expect(extraQuestion).toBeVisible()
    // Делаем скриншот
    await expect(page).toHaveScreenshot('nps-single-multiple-choice-extra-visible.png')

    // Выбираем несколько вариантов, например, первые два, и вариант "Другая причина"
    const checkboxes = extraQuestion.locator('.fc-check')
    await checkboxes.nth(0).click()
    await checkboxes.nth(1).click()
    const selfVariantCheckbox = extraQuestion.getByText('Другая причина')
    await selfVariantCheckbox.click()
    const commentTextarea = extraQuestion.locator('textarea')
    await commentTextarea.fill('Некоторый текст')
    // Делаем еще один скриншот
    await expect(page).toHaveScreenshot('nps-single-multiple-choice-extra-filled.png')

    // Нажимаем "Далее" и проверяем, что мы перешли к следующему вопросу
    const nextButton = await page.getByText('Далее')
    await nextButton.click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Текстовый ввод', async ({ page }) => {
    await page.goto('/nps-extra-questions-single-text')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Выбираем оценку 3 для первого варианта NPS
    await firstVariant.getByTestId('rating-nps-item').nth(3).click()
    // Проверяем, что дополнительный вопрос не отображается для первого варианта
    await expect(firstVariant.locator('.question-variants')).not.toBeVisible()

    // Выбираем оценку 6 для второго варианта NPS
    await secondVariant.getByTestId('rating-nps-item').nth(6).click()
    // Проверяем, что дополнительный вопрос отображается для второго варианта
    const extraQuestion = page.locator('.question-variants')
    await expect(extraQuestion).toBeVisible()
    // Делаем скриншот
    await expect(page).toHaveScreenshot('nps-single-text-extra-visible.png')

    // Заполняем текстовое поле
    const textarea = extraQuestion.locator('textarea')
    await textarea.fill('Некоторый текст')
    // Делаем еще один скриншот
    await expect(page).toHaveScreenshot('nps-single-text-extra-filled.png')

    // Нажимаем "Далее" и проверяем, что мы перешли к следующему вопросу
    const nextButton = await page.getByText('Далее')
    await nextButton.click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Одиночный выбор с файлами', async ({ page }) => {
    await page.goto('/nps-extra-questions-single-choice-with-files')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Выбираем оценку 3 для первого варианта NPS
    await firstVariant.getByTestId('rating-nps-item').nth(3).click()
    // Проверяем, что дополнительный вопрос не отображается для первого варианта
    await expect(firstVariant.locator('.question-variants')).not.toBeVisible()

    // Выбираем оценку 6 для второго варианта NPS
    await secondVariant.getByTestId('rating-nps-item').nth(6).click()
    // Проверяем, что дополнительный вопрос отображается для второго варианта
    const extraQuestion = page.locator('.question-variants')
    await expect(extraQuestion).toBeVisible()
    // Делаем скриншот
    await expect(page).toHaveScreenshot('nps-single-choice-with-files-extra-visible.png')

    // Выбираем вариант, например, первый элемент галереи
    const galleryItemsCheckboxes = extraQuestion.locator('.fc-check__box')
    await galleryItemsCheckboxes.nth(0).click()
    // Делаем еще один скриншот
    await expect(page).toHaveScreenshot('nps-single-choice-with-files-extra-filled.png')

    // Нажимаем "Далее" и проверяем, что мы перешли к следующему вопросу
    const nextButton = await page.getByText('Далее')
    await nextButton.click()
    await expect(firstVariant).not.toBeVisible()
  })

  test('Множественный выбор с файлами', async ({ page }) => {
    await page.goto('/nps-extra-questions-single-multiple-choice-with-files')
    const npsVariants = await page.getByTestId('rating-nps')
    const firstVariant = await npsVariants.first()
    const secondVariant = await npsVariants.nth(1)

    // Выбираем оценку 3 для первого варианта NPS
    await firstVariant.getByTestId('rating-nps-item').nth(3).click()
    // Проверяем, что дополнительный вопрос не отображается для первого варианта
    await expect(firstVariant.locator('.question-variants')).not.toBeVisible()

    // Выбираем оценку 6 для второго варианта NPS
    await secondVariant.getByTestId('rating-nps-item').nth(6).click()
    // Проверяем, что дополнительный вопрос отображается для второго варианта
    const extraQuestion = page.locator('.question-variants')
    await expect(extraQuestion).toBeVisible()
    // Делаем скриншот
    await expect(page).toHaveScreenshot('nps-single-multiple-choice-with-files-extra-visible.png')

    // Выбираем несколько вариантов, например, первые два элемента галереи и вариант "Другая причина"
    const galleryItemsCheckboxes = extraQuestion.locator('.fc-check__box')
    await galleryItemsCheckboxes.nth(0).click()
    await galleryItemsCheckboxes.nth(1).click()
    await galleryItemsCheckboxes.last().click()

    const commentTextarea = extraQuestion.locator('textarea')
    await commentTextarea.fill('Некоторый текст')
    // Делаем еще один скриншот
    await expect(page).toHaveScreenshot('nps-single-multiple-choice-with-files-extra-filled.png')

    // Нажимаем "Далее" и проверяем, что мы перешли к следующему вопросу
    const nextButton = await page.getByText('Далее')
    await nextButton.click()
    await expect(firstVariant).not.toBeVisible()
  })
})
