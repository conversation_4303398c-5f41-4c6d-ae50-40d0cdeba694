import { expect, test } from "@playwright/test";

test.describe("Промежуточный блок", () => {
  test("Отображает промежуточный блок", async ({ page }) => {
    await page.goto("/?key=intermediate-block-default");
    const widget = await page.getByTestId("fc-widget");
    const scrollElem = await widget.locator(
      "._fc-sb-9m4nv18fd-content-wrapper",
    );

    // Check initial screenshot
    await expect(widget).toHaveScreenshot("intermediate-block-default.png");

    // Scroll to the bottom of the widget
    await scrollElem.evaluate((element) => {
      element.scrollTop = element.scrollHeight;
    });

    // Wait for any potential image loading
    await page.waitForLoadState("networkidle");

    // Check screenshot after scrolling
    await expect(widget).toHaveScreenshot(
      "intermediate-block-default-scrolled.png",
    );

    // Additional checks
    const agreementCheckbox = await page.getByText(
      "Я согласен/согласна на обработку персональных данных",
    );
    await expect(agreementCheckbox).toBeVisible();

    const promoCode = await page.getByText("Мой промокод: PROMO123");
    await expect(promoCode).toBeVisible();

    const name = await page.getByText("Иван Иванов");
    await expect(name).toBeVisible();

    // Check if images are visible
    const images = await widget.locator("img");
    await expect(images).toHaveCount(3);

    await agreementCheckbox.click();

    await page.waitForTimeout(200);

    // Check screenshot after clicking on the agreement checkbox
    await expect(widget).toHaveScreenshot(
      "intermediate-block-default-agreement-checked.png",
    );
  });

  test("Отображает стартовый промежуточный блок", async ({ page }) => {
    await page.goto("/?key=intermediate-block-start");
    const widget = await page.getByTestId("fc-widget");
    const nextButton = await page.getByText("Пройти опрос").nth(1);
    const agreementCheckbox = await page.getByText(
      "Я согласен/согласна на обработку персональных данных",
    );

    await page.waitForTimeout(100);
    // Check initial screenshot
    await expect(widget).toHaveScreenshot("intermediate-block-start.png");

    // Check that the next button is disabled by default
    await expect(nextButton).toBeDisabled();

    // Click the agreement checkbox
    await agreementCheckbox.click();

    // Check that the next button is now enabled
    await expect(nextButton).toBeEnabled();

    await expect(widget).toHaveScreenshot(
      "intermediate-block-start-agreement-checked.png",
    );

    // Click the next button
    await nextButton.click();

    // Check that we've moved to the next question (rating question in this case)
    const ratingQuestion = await page.getByText("Оцените наш сервис");
    await expect(ratingQuestion).toBeVisible();

    // Take a screenshot of the rating question
    await page.waitForTimeout(200);
    await expect(widget).toHaveScreenshot(
      "intermediate-block-start-next-question.png",
    );
  });

  test("Отображает стартовый промежуточный блок с маленьким текстом", async ({
    page,
  }) => {
    await page.goto("/?key=intermediate-block-start-small-text");
    const widget = await page.getByTestId("fc-widget");
    const nextButton = await page.getByText("Пройти опрос").nth(0);
    await expect(nextButton).toBeVisible();
    await page.waitForTimeout(300);
    await expect(widget).toHaveScreenshot(
      "intermediate-block-start-small-text.png",
    );
  });

  test("Отображает конечный промежуточный блок", async ({ page }) => {
    await page.goto("/?key=intermediate-block-end");
    const widget = await page.getByTestId("fc-widget");
    const nextButton = await page.getByText("Готово").nth(1);
    await expect(nextButton).toBeVisible();
    await expect(widget).toHaveScreenshot("intermediate-block-end.png");
  });

  test("Отображает конечный промежуточный блок с маленьким текстом", async ({
    page,
  }) => {
    await page.goto("/?key=intermediate-block-end-small-text");
    const widget = await page.getByTestId("fc-widget");

    await page.waitForTimeout(500);
    // Check initial screenshot
    await expect(widget).toHaveScreenshot(
      "intermediate-block-end-small-text.png",
    );
  });

  test("Отображает промежуточный блок в конце, даже если в данных он пришел в середине", async ({
    page,
  }) => {
    await page.goto("/?key=intermediate-block-in-the-middle");

    // Answer the first question
    await page.getByTestId("fc-widget-star-rating-item").nth(4).click();
    await page.getByText("Далее").click();

    // Answer the rating question
    await page.getByTestId("rating-scale-item").nth(4).click();

    // Click "Готово" button
    await page.getByText("Завершить").click();

    const endBlockText = await page.getByText("Промежуточный блок в конце.");
    const readyButton = await page.getByText("Готово").nth(0);

    await expect(endBlockText).toBeVisible();
    await expect(readyButton).toBeVisible();
  });

  test("Отображает промежуточный блок при клике на кнопку триггер", async ({
    page,
  }) => {
    await page.goto("/?key=appearance-click:intermediate-block-default");

    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();

    // Click the trigger button to show the widget
    await triggerButton.click();

    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toBeVisible();

    await page.waitForTimeout(300);
    // Take a screenshot of the entire page to show the widget in modal
    await expect(page).toHaveScreenshot("intermediate-block-default-modal.png");

    const scrollElem = await widget.locator(
      "._fc-sb-9m4nv18fd-content-wrapper",
    );

    // Scroll to the bottom of the widget
    await scrollElem.evaluate((element) => {
      element.scrollTop = element.scrollHeight;
    });

    // Wait for any potential image loading
    await page.waitForLoadState("networkidle");

    // Check screenshot after scrolling
    await expect(page).toHaveScreenshot(
      "intermediate-block-default-modal-scrolled.png",
    );

    // Additional checks
    const agreementCheckbox = await page.getByText(
      "Я согласен/согласна на обработку персональных данных",
    );
    await expect(agreementCheckbox).toBeVisible();

    const promoCode = await page.getByText("Мой промокод: PROMO123");
    await expect(promoCode).toBeVisible();

    const name = await page.getByText("Иван Иванов");
    await expect(name).toBeVisible();

    // Check if images are visible
    const images = await widget.locator("img");
    await expect(images).toHaveCount(3);

    await agreementCheckbox.click();

    await page.waitForTimeout(200);

    // Check screenshot after clicking on the agreement checkbox
    await expect(page).toHaveScreenshot(
      "intermediate-block-default-modal-agreement-checked.png",
    );

    // Click the next button
    const nextButton = await page.getByText("Завершить");
    await nextButton.click();

    // Take a screenshot of the next question
    await expect(page).toHaveScreenshot(
      "intermediate-block-default-modal-next-question.png",
    );
  });

  test("Отображает промежуточный блок с HTML в тексте согласия", async ({
    page,
  }) => {
    await page.goto("/?key=intermediate-block-agreement-html");
    const widget = await page.getByTestId("fc-widget");
    const agreementCheckbox = await page.getByText(
      "Я согласен/согласна на обработку персональных данных",
    );
    await expect(agreementCheckbox).toBeVisible();

    const link = await page.getByText("обработку персональных данных");
    await expect(link).toBeVisible();

    await expect(widget).toHaveScreenshot(
      "intermediate-block-agreement-html.png",
    );
  });
  test("Отображает промежуточный блок с HTML в тексте согласия при клике на кнопку триггер", async ({
    page,
  }) => {
    await page.goto("/?key=appearance-click:intermediate-block-agreement-html");
    const widget = await page.getByTestId("fc-widget");
    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();

    // Click the trigger button to show the widget
    await triggerButton.click();

    const agreementCheckbox = await page.getByText(
      "Я согласен/согласна на обработку персональных данных",
    );
    await expect(agreementCheckbox).toBeVisible();

    const link = await page.getByText("обработку персональных данных");
    await expect(link).toBeVisible();

    await expect(widget).toHaveScreenshot(
      "intermediate-block-agreement-html-modal.png",
    );
  });
  test("Отображает конечный блок с закрывающей кнопкой", async ({ page }) => {
    await page.goto("/?key=intermediate-block-end-close-widget-button");
    const widget = await page.getByTestId("fc-widget");
    const closeButton = await page.getByText("Завершить");
    await expect(closeButton).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "intermediate-block-end-close-widget-button.png",
    );
    await closeButton.click();
    await expect(widget).not.toBeVisible();
  });
  test("Отображает конечный блок с закрывающей кнопкой с кастомным текстом", async ({
    page,
  }) => {
    await page.goto(
      "/?key=intermediate-block-end-close-widget-button-custom-text",
    );
    const widget = await page.getByTestId("fc-widget");
    const closeButton = await page.getByText("Закроем?");
    await expect(closeButton).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "intermediate-block-end-close-widget-button-custom-text.png",
    );
  });
});
