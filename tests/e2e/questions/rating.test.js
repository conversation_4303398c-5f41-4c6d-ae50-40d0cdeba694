import { expect, test } from "@playwright/test";

test.describe("Вопрос 'Рейтинг'", () => {
  test("Отображает 5 цифр по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating");
    const widget = await page.getByTestId("fc-widget");
    const items = await page.getByTestId("rating-scale-item");
    await expect(items).toHaveText(["1", "2", "3", "4", "5"]);
    await expect(widget).toHaveScreenshot("rating.png");
    await items.first().click();
    await expect(widget).toHaveScreenshot("rating-clicked.png");
  });

  test("Отображает 2 цифры", async ({ page }) => {
    await page.goto("/?key=rating-2-items");
    const widget = await page.getByTestId("fc-widget");
    const items = await page.getByTestId("rating-scale-item");
    await expect(items).toHaveText(["1", "2"]);
    await expect(widget).toHaveScreenshot("rating-2-items.png");
  });
  test("Отображает 10 цифр", async ({ page }) => {
    await page.goto("/?key=rating-10-items");
    const widget = await page.getByTestId("fc-widget");
    const items = await page.getByTestId("rating-scale-item");
    await expect(items).toHaveText([
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
    ]);
    await expect(widget).toHaveScreenshot("rating-10-items.png");
  });
  test("Отображает метку при клике на цифру", async ({ page }) => {
    await page.goto("/?key=rating-with-labels");
    const widget = await page.getByTestId("fc-widget");
    const items = await page.getByTestId("rating-scale-item");
    const labelOne = await page.getByText("метка 1");
    await items.first().click();
    await expect(labelOne).toBeVisible();
    await expect(widget).toHaveScreenshot("rating-with-labels-clicked.png");
  });
  test("Обязателен по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating");
    const widget = await page.getByTestId("fc-widget");
    const item = await page.getByTestId("rating-scale-item").first();
    const error = await page.getByText("Нужно поставить оценку");
    const finishButton = await page.getByText("Завершить");
    await finishButton.click();

    await expect(error).toBeVisible();
    await expect(widget).toHaveScreenshot("rating-required-error.png");

    await item.click();
    await expect(error).not.toBeVisible();
    await expect(finishButton).not.toHaveClass(/disabled/);
  });
  test("Необязателен если опция 'Обязательный' выключена", async ({ page }) => {
    await page.goto("/?key=rating-unrequired");
    const rating = await page.getByTestId("rating-scale");
    const finishButton = await page.getByText("Завершить");
    const error = await page.getByText("Нужно поставить оценку");
    await expect(rating).toBeVisible();
    await finishButton.click();

    // Проверяем, что ошибка не отображается
    await expect(error).not.toBeVisible();

    // Проверяем, что мы перешли к следующему вопросу
    await expect(rating).not.toBeVisible();
  });

  test("Сразу переходит к следующему вопросу при отсутствии нижнего ряда кнопок", async ({
    page,
  }) => {
    await page.goto("/?key=rating-without-prev-button-2-questions");
    const widget = await page.getByTestId("fc-widget");
    const navButtons = await page.getByTestId("form-nav-buttons");
    const rating = await page.getByTestId("rating-scale");
    const item = await page.getByTestId("rating-scale-item").first();

    await page.waitForTimeout(100);
    await expect(widget).toHaveScreenshot("rating-without-prev-button.png");
    await expect(navButtons).toBeHidden();
    await item.click();
    await page.waitForTimeout(100);
    await expect(rating).toBeHidden();
  });
  test("Отображает чекбокс 'Пропустить вопрос' если опция включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-with-skip");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    await expect(skipCheckbox).toBeVisible();
    await expect(widget).toHaveScreenshot("rating-with-skip.png");
  });

  test("Пропускает обязательный вопрос если опция 'Пропустить вопрос' включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-with-skip-required");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    const finishButton = await page.getByText("Завершить");
    const error = await page.getByText("Нужно поставить оценку");

    await finishButton.click();
    await expect(error).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-with-skip-required-error.png",
    );

    await skipCheckbox.click();
    await finishButton.click();
    await expect(error).not.toBeVisible();
  });
  test("Скрывает комментарий если опция 'Пропустить вопрос' включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-with-skip-and-comment");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    const comment = await page.getByTestId("comment");

    await expect(comment).toBeVisible();
    await expect(widget).toHaveScreenshot("rating-with-skip-and-comment.png");
    await skipCheckbox.click();
    await expect(comment).not.toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-with-skip-and-comment-hidden.png",
    );
    await skipCheckbox.click();
    await expect(comment).toBeVisible();
    await expect(widget).toHaveScreenshot("rating-with-skip-and-comment.png");
  });

  test.describe("Комментарий", () => {
    test("Отображает комментарий", async ({ page }) => {
      await page.goto("/?key=rating-with-comment");
      const widget = await page.getByTestId("fc-widget");
      const comment = await page.getByTestId("comment");
      const label = comment.getByText("Ваш комментарий");
      const navButtons = await page.getByTestId("form-nav-buttons");

      await expect(comment).toBeVisible();
      await expect(label).toBeVisible();

      // Проверяем, что кнопки навигации присутствуют если есть комментарий
      await expect(navButtons).toBeVisible();
      await expect(widget).toHaveScreenshot("rating-with-comment.png");
    });
    test("Не обязателен по умолчанию", async ({ page }) => {
      await page.goto("/?key=rating-with-comment");
      const items = await page.getByTestId("rating-scale");
      const item = await page.getByTestId("rating-scale-item").first();
      const finishButton = await page.getByText("Завершить");

      await item.click();
      await finishButton.click();

      // Проверяем, что перешли на следующий шаг
      await expect(items).toBeHidden();
    });
    test("Обязателен если опция 'Обязателен' включена", async ({ page }) => {
      await page.goto("/?key=rating-with-comment-required");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const item = await page.getByTestId("rating-scale-item").first();
      const finishButton = await page.getByText("Завершить");
      const error = await page.getByText("Обязательное поле");
      const textarea = await page.getByTestId("comment").locator("textarea");

      await expect(error).toBeHidden();
      await finishButton.click();
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "rating-with-comment-required-error.png",
      );

      await item.click();
      await textarea.fill("some value");

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await expect(error).toBeHidden();
      await finishButton.click();
      await expect(rating).toBeHidden();
    });
    test("Комплексный пример (Обязателен + валидация длины текста)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-comment-complex");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const item = await page.getByTestId("rating-scale-item").first();
      const finishButton = await page.getByText("Завершить");
      const requiredError = await page.getByText("Обязательное поле");
      const minLengthError = await page.getByText(
        "Должно быть введено хотя бы 10 символов",
      );
      const textarea = await page.getByTestId("comment").locator("textarea");

      await expect(requiredError).toBeHidden();
      await finishButton.click();
      await expect(requiredError).toBeVisible();
      await item.click();
      await textarea.fill("text");
      await expect(minLengthError).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "rating-with-comment-complex-error.png",
      );

      await textarea.fill("text with more then 10 characters");

      await expect(minLengthError).toBeHidden();
      await expect(requiredError).toBeHidden();
      await expect(widget).toHaveScreenshot(
        "rating-with-comment-complex-no-error.png",
      );

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await finishButton.click();
      await expect(rating).toBeHidden();
    });
  });

  test.describe("Уточняющий вопрос (текстовый вариант)", () => {
    test("Отображает уточняющий вопрос при клике на звездочку", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-text");
      const widget = await page.getByTestId("fc-widget");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentsLabel = await page.getByTestId("assessments-label");
      const assessmentsField = await page.getByTestId("assessments-text");

      await expect(assessmentsLabel).toBeHidden();
      await expect(assessmentsField).toBeHidden();
      await item.click();
      await expect(assessmentsField).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-text-clicked.png",
      );
    });
    test("Уточняющий вопрос обязателен по умолчанию", async ({ page }) => {
      await page.goto("/?key=rating-with-assessments-text");
      const widget = await page.getByTestId("fc-widget");
      const finishButton = await page.getByText("Завершить");
      const rating = await page.getByTestId("rating-scale");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentsField = await page.getByTestId("assessments-text");
      const error = await page.getByText("Обязательное поле");

      await item.click();
      await finishButton.click();
      await page.waitForTimeout(100);
      await expect(rating).toBeVisible();
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-text-error.png",
      );
      await assessmentsField.fill("some valueeeeeee");
      await expect(error).toBeHidden();
      await finishButton.click();
      await expect(rating).toBeHidden();
    });

    test("Если рейтинг не выбран, уточняющий вопрос скрывается", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-text");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentsField = await page.getByTestId("assessments-text");

      await item.click();
      await expect(assessmentsField).toBeVisible();
      await item.click();
      await page.waitForTimeout(500);
      await expect(assessmentsField).toBeHidden();
    });

    test("Если extraQuestionRateFrom/To определен, отображаем уточняющий вопрос только если попали в диапазон", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-text-complex");
      const itemThree = await page.getByTestId("rating-scale-item").nth(2);
      const itemFive = await page.getByTestId("rating-scale-item").last();
      const assessmentField = await page.getByTestId("assessments-text");

      // Проверяем, что поле не отображается
      await itemFive.click();
      await expect(assessmentField).toBeHidden();

      // Проверяем, что поле отображается
      await itemThree.click();
      await expect(assessmentField).toBeVisible();
    });
  });
  test.describe("Уточняющий вопрос (единичный выбор)", () => {
    test("Отображает четыре чекбокса (три варианта и один самовариант) для УВ (с одним выбором)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-single");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const finishButton = await page.getByText("Завершить");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");

      await expect(assessmentChecks).toHaveCount(0);
      await item.click();
      await expect(assessmentChecks).toHaveCount(4);
      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-single.png",
      );

      // По умолчанию выбор УВ не обязателен
      await finishButton.click();
      await expect(rating).toBeHidden();
    });
  });
  test("Отображает УВ с одним вариантом и включенной опцией 'Обязательный'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-with-assessments-single-required");
    const widget = await page.getByTestId("fc-widget");
    const rating = await page.getByTestId("rating-scale");
    const finishButton = await page.getByText("Завершить");
    const item = await page.getByTestId("rating-scale-item").last();
    const assessmentChecks = await page.getByTestId("assessments-check");
    const firstCheck = await assessmentChecks.first();
    const error = await page.getByText(
      "Нужно выбрать хотя бы один вариант ответа",
    );

    await item.click();
    await page.waitForTimeout(300);
    await finishButton.click();
    await expect(error).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-with-assessments-single-required-error.png",
    );
    await firstCheck.click();
    await page.waitForTimeout(300);
    await expect(error).not.toBeVisible();
    await finishButton.click();
    await expect(rating).toBeHidden();
  });
  test("Отображает УВ с обязательным своим вариантом", async ({ page }) => {
    await page.goto("/?key=rating-with-assessments-single-required");
    const widget = await page.getByTestId("fc-widget");
    const rating = await page.getByTestId("rating-scale");
    const finishButton = await page.getByText("Завершить");
    const item = await page.getByTestId("rating-scale-item").last();
    const assessmentChecks = await page.getByTestId("assessments-check");
    const lastCheck = await assessmentChecks.last();
    const textarea = await page.locator("textarea");
    const requiredError = await page.getByText("Обязательное поле");
    const minLengthError = await page.getByText(
      "Должно быть введено хотя бы 10 символов",
    );

    await item.click();

    await lastCheck.click();
    await expect(textarea).toBeVisible();
    await finishButton.click();

    // Проверяем, что ошибка отображается
    await expect(requiredError).toBeVisible();
    await page.waitForTimeout(100);
    await expect(widget).toHaveScreenshot(
      "rating-with-assessments-single-selfvariant-required-error.png",
    );

    // Проверяем, что ошибка с минимальным количеством символов отображается
    await textarea.fill("text");
    await expect(minLengthError).toBeVisible();
    await page.waitForTimeout(100);
    await expect(widget).toHaveScreenshot(
      "rating-with-assessments-single-selfvariant-required-min-length-error.png",
    );

    // Проверяем, что ошибки нет и перешли на следующий шаг
    await textarea.fill("text with more then 10 characters");
    await expect(minLengthError).toBeHidden();
    await finishButton.click();
    await expect(rating).toBeHidden();
  });

  test.describe("Уточняющий вопрос (множественный выбор)", () => {
    test("Отображает четыре чекбокса (три варианта и один самовариант) для УВ (с множественным выбором)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-multiple");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const finishButton = await page.getByText("Завершить");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");

      await expect(assessmentChecks).toHaveCount(0);
      await item.click();
      await expect(assessmentChecks).toHaveCount(4);
      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-multiple.png",
      );

      // По умолчанию выбор УВ не обязателен
      await finishButton.click();
      await expect(rating).toBeHidden();
    });

    test("Отображает УВ с множественным вариантом и включенной опцией 'Обязательный'", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-multiple-required");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const finishButton = await page.getByText("Завершить");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");
      const firstCheck = await assessmentChecks.first();
      const error = await page.getByText(
        "Нужно выбрать хотя бы один вариант ответа",
      );

      await item.click();
      await page.waitForTimeout(300);
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-multiple-required-error.png",
      );
      await firstCheck.click();
      await page.waitForTimeout(500);
      await expect(error).not.toBeVisible();
      await finishButton.click();
      await expect(rating).toBeHidden();
    });
    test("Отображает УВ (множественный выбор) с обязательным своим вариантом", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-multiple-required");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const finishButton = await page.getByText("Завершить");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");
      const thirdCheck = await assessmentChecks.nth(2);
      const lastCheck = await assessmentChecks.last();
      const textarea = await page.locator("textarea");
      const requiredError = await page.getByText("Обязательное поле");
      const minLengthError = await page.getByText(
        "Должно быть введено хотя бы 10 символов",
      );

      await item.click();

      await thirdCheck.click();
      await lastCheck.click();
      await expect(textarea).toBeVisible();
      await finishButton.click();

      // Проверяем, что ошибка отображается
      await expect(requiredError).toBeVisible();
      await page.waitForTimeout(100);
      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-multiple-selfvariant-required-error.png",
      );

      // Проверяем, что ошибка с минимальным количеством символов отображается
      await textarea.fill("text");
      await expect(minLengthError).toBeVisible();
      await page.waitForTimeout(100);
      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-multiple-selfvariant-required-min-length-error.png",
      );

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await textarea.fill("text with more then 10 characters");
      await expect(minLengthError).toBeHidden();
      await finishButton.click();
      await expect(rating).toBeHidden();
    });
  });

  test("Отображает рейтинг с множественным выбором при клике на кнопку триггер", async ({
    page,
  }) => {
    await page.goto(
      "/?key=appearance-click:rating-with-assessments-multiple-required",
    );

    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();

    // Click the trigger button to show the widget
    await triggerButton.click();

    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toBeVisible();

    const ratingItem = await page.getByTestId("rating-scale-item").last();

    await expect(ratingItem).toBeVisible();
    // Take a screenshot of the entire page to show the widget in modal
    await expect(page).toHaveScreenshot(
      "rating-assessments-multiple-required-modal.png",
    );

    // Interact with the widget to ensure it's functional
    await ratingItem.click();

    const assessmentChecks = await page.getByTestId("assessments-check");
    await expect(assessmentChecks).toHaveCount(4);

    // Take another screenshot after interaction
    await expect(page).toHaveScreenshot(
      "rating-assessments-multiple-required-modal-interacted.png",
    );
  });

  test.describe("УВ и загрузка файлов/скриншотов", () => {
    test("УВ с множественным выбором и загрузкой файлов (default questionScreenshot)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-multiple-screenshot-default");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const finishButton = await page.getByText("Завершить");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");
      const uploadButton = await page.getByRole("button", { name: /прикрепить файл/i });
      const screenshotButton = await page.getByRole("button", { name: /сделать скриншот/i });

      await item.click();
      await expect(assessmentChecks).toHaveCount(3); // 2 варианта + 1 самовариант

      // Кнопка загрузки файлов должна быть видна
      await expect(uploadButton).toBeVisible();
      
      // Кнопка скриншота должна не быть видна если не включена в настройках
      await expect(screenshotButton).not.toBeVisible();

      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-multiple-screenshot-default.png",
      );

      // Проверяем, что можем пройти дальше без загрузки файлов
      await finishButton.click();
      await expect(rating).toBeHidden();
    });

    test("УВ с множественным выбором и загрузкой файлов (custom questionScreenshot)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-with-assessments-multiple-screenshot-custom");
      const widget = await page.getByTestId("fc-widget");
      const rating = await page.getByTestId("rating-scale");
      const finishButton = await page.getByText("Завершить");
      const item = await page.getByTestId("rating-scale-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");
      const uploadButton = await page.getByRole("button", { name: /прикрепить файлы \(свой текст\)/i });
      const screenshotButton = await page.getByRole("button", { name: /добавить скриншот/i });
      const description = await page.getByText(/прикрепите скриншоты или файлы/i);

      await item.click();
      await expect(assessmentChecks).toHaveCount(3); // 2 варианта + 1 самовариант

      // Кнопка загрузки файлов с кастомным текстом должна быть видна
      await expect(uploadButton).toBeVisible();
      
      // Кнопка скриншота с кастомным текстом должна быть видна в режиме simple
      await expect(screenshotButton).toBeVisible();

      // Описание должно быть видно
      await expect(description).toBeVisible();

      await expect(widget).toHaveScreenshot(
        "rating-with-assessments-multiple-screenshot-custom.png",
      );

      // Проверяем, что можем пройти дальше без загрузки файлов
      await finishButton.click();
      await expect(rating).toBeHidden();
    });
  });
});
