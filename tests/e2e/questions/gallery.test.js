import { expect, test } from '@playwright/test'

test.describe('Вопрос \'галерея с рейтингом\'', () => {
  test('Отображает галерею с рейтингом и переходит к следующему слайду после оценки', async ({ page }) => {
    // 1. Media variants with rating are rendered
    await page.goto('/gallery-rating-default')
    const nextButton = await page.getByText('Далее')
    const galleryItems = await page.locator('.gallery-item')
    const ratingStars = await page.getByTestId('star-rating')
    const errorMessage = await page.getByText('Нужно поставить все оценки')

    await expect(galleryItems).toHaveCount(3)

    // Make a screenshot of the initial state
    await expect(page).toHaveScreenshot('gallery-rating-initial.png')

    await nextButton.click()

    await expect(errorMessage).toBeVisible()

    await expect(page).toHaveScreenshot('gallery-rating-error-message.png')

    // 2. When clicked first variant rating star, it slides to the next one
    await ratingStars.getByTestId('star-rating-item').nth(4).click() // Click the 5th star (index 4) of the first item

    // Wait for the animation to complete
    await page.waitForTimeout(1000)

    // Check if it automatically moved to the next slide
    const secondGalleryItem = await galleryItems.nth(1)
    await expect(secondGalleryItem).toBeVisible()

    // 3. Make screenshots
    await expect(page).toHaveScreenshot('gallery-rating-after-first-rating.png')

    // Rate the second item
    const secondItemStars = await secondGalleryItem.getByTestId('star-rating')
    await secondItemStars.getByTestId('star-rating-item').nth(3).click() // Click the 4th star (index 3) of the second item

    // Rate the third item
    const thirdItemStars = await galleryItems.nth(2).getByTestId('star-rating')
    await thirdItemStars.getByTestId('star-rating-item').nth(2).click() // Click the 3rd star (index 2) of the third item

    await expect(errorMessage).not.toBeVisible()
    // Make a final screenshot
    await expect(page).toHaveScreenshot('gallery-rating-after-all-ratings-set.png')
  })
})
