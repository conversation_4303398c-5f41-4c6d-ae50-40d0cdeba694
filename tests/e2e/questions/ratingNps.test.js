import { expect, test } from '@playwright/test'

test.describe('Вопрос \'Рейтинг NPS\'', () => {
  test('Отображает цифры от 0 до 10', async ({ page }) => {
    await page.goto('/rating-nps')
    const items = await page.getByTestId('rating-nps-item').locator('visible=true')
    await expect(items).toHaveText([
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
    ])
    await expect(page).toHaveScreenshot('rating-nps.png')
    await items.nth(5).click()
    await expect(page).toHaveScreenshot('rating-nps-clicked.png')
  })
  test('Отображает фицры от 1 до 10', async ({ page }) => {
    await page.goto('/rating-nps-from-1')
    const items = await page.getByTestId('rating-nps-item').locator('visible=true')
    await expect(items).toHaveText([
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
    ])
    await expect(page).toHaveScreenshot('rating-nps-from-1.png')
  })

  test('Отображает дизайн со своим градиентом', async ({ page }) => {
    await page.goto('/rating-nps-custom-gradient')
    const nextButton = await page.getByText('Далее')

    await expect(nextButton).toBeVisible()
    await expect(page).toHaveScreenshot('rating-nps-custom-gradient.png')
  })
  test('Отображает черно-белый дизайн', async ({ page }) => {
    await page.goto('/rating-nps-black-and-white')
    await expect(page).toHaveScreenshot('rating-nps-black-and-white.png')
  })
  test('Отображает метки под цифрами', async ({ page }) => {
    await page.goto('/rating-nps-with-labels')
    const leftLabel = await page.getByText('Начало')
    const rightLabel = await page.getByText('Конец')

    await expect(leftLabel).toBeVisible()
    await expect(rightLabel).toBeVisible()

    await expect(page).toHaveScreenshot('rating-nps-with-labels.png')
  })
  test('Отображает чекбокс \'Пропустить вопрос\' если опция включена', async ({
    page,
  }) => {
    await page.goto('/rating-nps-with-skip')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    await expect(skipCheckbox).toBeVisible()
    await expect(page).toHaveScreenshot('rating-nps-with-skip.png')
  })

  test('Необязателен если опция \'Обязательный\' выключена', async ({ page }) => {
    await page.goto('/rating-nps-unrequired')
    const rating = await page.getByTestId('rating-nps')
    const finishButton = await page.getByText('Далее')
    const error = await page.getByText('Нужно поставить оценку')
    await expect(rating).toBeVisible()
    await finishButton.click()

    // Проверяем, что ошибка не отображается
    await expect(error).not.toBeVisible()

    // Проверяем, что мы перешли к следующему вопросу
    await expect(rating).not.toBeVisible()
  })

  test('Пропускает обязательный вопрос если опция \'Пропустить вопрос\' включена', async ({
    page,
  }) => {
    await page.goto('/rating-nps-with-skip-required')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    const finishButton = await page.getByText('Далее')
    const error = await page.getByText('Нужно поставить оценку')

    await finishButton.click()
    await expect(error).toBeVisible()
    await expect(page).toHaveScreenshot(
      'rating-nps-with-skip-required-error.png',
    )

    await skipCheckbox.click()
    await finishButton.click()
    await expect(error).not.toBeVisible()
  })

  test('Скрывает комментарий если опция \'Пропустить вопрос\' включена', async ({
    page,
  }) => {
    await page.goto('/rating-nps-with-skip-and-comment')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    const comment = await page.getByTestId('comment')

    await expect(comment).toBeVisible()
    await expect(page).toHaveScreenshot(
      'rating-nps-with-skip-and-comment.png',
    )
    await skipCheckbox.click()
    await expect(comment).not.toBeVisible()
    await expect(page).toHaveScreenshot(
      'rating-nps-with-skip-and-comment-hidden.png',
    )
    await skipCheckbox.click()
    await expect(comment).toBeVisible()
    await expect(page).toHaveScreenshot(
      'rating-nps-with-skip-and-comment.png',
    )
  })

  test.describe('Комментарий', () => {
    test('Отображает комментарий', async ({ page }) => {
      await page.goto('/rating-nps-with-comment')
      const comment = await page.getByTestId('comment')
      const label = comment.getByText('Ваш комментарий')

      await expect(comment).toBeVisible()
      await expect(label).toBeVisible()

      await expect(page).toHaveScreenshot('rating-nps-with-comment.png')
    })
    test('Не обязателен по умолчанию', async ({ page }) => {
      await page.goto('/rating-nps-with-comment')
      const items = await page.getByTestId('rating-nps')
      const item = await page.getByTestId('rating-nps-item').first()
      const finishButton = await page.getByText('Далее')

      await item.click()
      await finishButton.click()

      // Проверяем, что перешли на следующий шаг
      await expect(items).toBeHidden()
    })
    test('Обязателен если опция \'Обязателен\' включена', async ({ page }) => {
      await page.goto('/rating-nps-with-comment-required')
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText('Обязательное поле')
      const textarea = await page.getByTestId('comment').locator('textarea')

      await expect(error).toBeHidden()
      await finishButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(
        'rating-nps-with-comment-required-error.png',
      )

      await textarea.fill('Комментарий')
      await textarea.blur()
      await expect(error).toBeHidden()
      await expect(page).toHaveScreenshot(
        'rating-nps-with-comment-required.png',
      )
    })
  })

  test('Комплексный пример (Обязателен + валидация длины текста)', async ({
    page,
  }) => {
    await page.goto('/rating-nps-with-comment-complex')
    const rating = await page.getByTestId('rating-nps')
    const item = await page.getByTestId('rating-nps-item').first()
    const finishButton = await page.getByText('Далее')
    const requiredError = await page.getByText('Обязательное поле')
    const minLengthError = await page.getByText(
      'Должно быть введено хотя бы 10 символов',
    )
    const textarea = await page.getByTestId('comment').locator('textarea')

    await expect(requiredError).toBeHidden()
    await finishButton.click()
    await expect(requiredError).toBeVisible()
    await item.click()
    await textarea.fill('text')
    await expect(minLengthError).toBeVisible()
    await expect(page).toHaveScreenshot(
      'rating-nps-with-comment-complex-error.png',
    )

    await textarea.fill('text with more then 10 characters')

    await expect(minLengthError).toBeHidden()
    await expect(requiredError).toBeHidden()
    await expect(page).toHaveScreenshot(
      'rating-nps-with-comment-complex-no-error.png',
    )

    // Проверяем, что ошибки нет и перешли на следующий шаг
    await finishButton.click()
    await expect(rating).toBeHidden()
  })

  test('Отображает NPS с вариантами', async ({ page }) => {
    await page.goto('/rating-nps-with-variants')

    const variants = await page.getByTestId('rating-nps')
    // console.log(variants)
    await expect(variants).toHaveCount(3)

    const variantLabels = await page.$$eval('.nps-rating-item__label', elements => elements.map(el => el.textContent.trim()))
    await expect(variantLabels).toEqual([
      'Скорость обслуживания',
      'Качество продукции',
      'Удобство использования',
    ])

    const variantItems = await variants.all()

    for (const variant of variantItems) {
      const ratingItems = await variant.getByTestId('rating-nps-item').all()
      await expect(ratingItems).toHaveLength(11) // 0 to 10

      await ratingItems[0].click()

      const selectedItem = await variant.getByTestId('rating-nps-item').nth(0)
      await expect(selectedItem).toHaveClass(/active/)
    }

    // scroll to the top
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })

    await expect(page).toHaveScreenshot('rating-nps-with-variants.png')

    const finishButton = await page.getByText('Далее')
    await finishButton.click()

    // Check that we've moved to the next question (nps rating is no longer visible)
    await expect(await variants.first()).not.toBeVisible()
  })

  test('Проверяет обязательность NPS с вариантами', async ({ page }) => {
    await page.goto('/rating-nps-with-variants')

    const finishButton = await page.getByText('Далее')
    await finishButton.click()

    await page.waitForTimeout(100)

    const errorMessages = await page.$$eval('.question-nps-rating-error', elements => elements.map(el => el.textContent.trim()))
    expect(errorMessages).toEqual([
      'Нужно поставить оценку',
      'Нужно поставить оценку',
      'Нужно поставить оценку',
    ])

    // scroll to the top
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })

    await expect(page).toHaveScreenshot('rating-nps-with-variants-error.png')

    // Rate only one variant
    const firstVariant = await page.getByTestId('rating-nps').first()
    await firstVariant.getByTestId('rating-nps-item').nth(5).click()

    await page.waitForTimeout(410)

    // Check that errors are still visible for unrated variants
    const remainingErrorMessages = await page.$$eval('.question-nps-rating-error', elements => elements.map(el => el.textContent.trim()))
    expect(remainingErrorMessages).toEqual([
      'Нужно поставить оценку',
      'Нужно поставить оценку',
    ])

    // Rate the remaining variants
    const variants = await page.getByTestId('rating-nps').all()
    for (let i = 1; i < variants.length; i++) {
      await variants[i].getByTestId('rating-nps-item').nth(5).click()
    }

    await finishButton.click()

    // Check that we've moved to the next question (rating is no longer visible)
    await expect(variants[0]).not.toBeVisible()
  })
})
