import { expect, test } from "@playwright/test";

test.describe("Вопрос 'Рейтинг NPS'", () => {
  test("Отображает цифры от 0 до 10", async ({ page }) => {
    await page.goto("/?key=rating-nps");
    const items = await page.getByTestId("rating-nps-item");
    await expect(items).toHaveText([
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
    ]);
    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toHaveScreenshot("rating-nps.png");
    await items.nth(5).click();
    await expect(widget).toHaveScreenshot("rating-nps-clicked.png");
  });
  test("Отображает фицры от 1 до 10", async ({ page }) => {
    await page.goto("/?key=rating-nps-from-1");
    const items = await page.getByTestId("rating-nps-item");
    await expect(items).toHaveText([
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
    ]);
    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toHaveScreenshot("rating-nps-from-1.png");
  });

  test("Отображает дизайн со своим градиентом", async ({ page }) => {
    await page.goto("/?key=rating-nps-custom-gradient");
    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toHaveScreenshot("rating-nps-custom-gradient.png");
  });
  test("Отображает черно-белый дизайн", async ({ page }) => {
    await page.goto("/?key=rating-nps-black-and-white");
    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toHaveScreenshot("rating-nps-black-and-white.png");
  });
  test("Отображает метки под цифрами", async ({ page }) => {
    await page.goto("/?key=rating-nps-with-labels");
    const widget = await page.getByTestId("fc-widget");
    const leftLabel = await page.getByText("Начало");
    const rightLabel = await page.getByText("Конец");

    await expect(leftLabel).toBeVisible();
    await expect(rightLabel).toBeVisible();

    await expect(widget).toHaveScreenshot("rating-nps-with-labels.png");
  });
  test("Отображает чекбокс 'Пропустить вопрос' если опция включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-nps-with-skip");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    await expect(skipCheckbox).toBeVisible();
    await expect(widget).toHaveScreenshot("rating-nps-with-skip.png");
  });

  test("Необязателен если опция 'Обязательный' выключена", async ({ page }) => {
    await page.goto("/?key=rating-nps-unrequired");
    const rating = await page.getByTestId("rating-nps");
    const finishButton = await page.getByText("Завершить");
    const error = await page.getByText("Нужно поставить оценку");
    await expect(rating).toBeVisible();
    await finishButton.click();

    // Проверяем, что ошибка не отображается
    await expect(error).not.toBeVisible();

    // Проверяем, что мы перешли к следующему вопросу
    await expect(rating).not.toBeVisible();
  });

  test("Сразу переходит к следующему вопросу при отсутствии нижнего ряда кнопок", async ({
    page,
  }) => {
    await page.goto("/?key=rating-nps-without-prev-button-2-questions");
    const widget = await page.getByTestId("fc-widget");
    const navButtons = await page.getByTestId("form-nav-buttons");
    const rating = await page.getByTestId("rating-nps");
    const item = await page.getByTestId("rating-nps-item").first();

    await expect(widget).toHaveScreenshot("rating-nps-without-prev-button.png");
    await expect(navButtons).toBeHidden();
    await item.click();
    await expect(rating).toBeHidden();
  });

  test("Пропускает обязательный вопрос если опция 'Пропустить вопрос' включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-nps-with-skip-required");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    const finishButton = await page.getByText("Завершить");
    const error = await page.getByText("Нужно поставить оценку");

    await finishButton.click();
    await expect(error).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-nps-with-skip-required-error.png",
    );

    await skipCheckbox.click();
    await finishButton.click();
    await expect(error).not.toBeVisible();
  });

  test("Скрывает комментарий если опция 'Пропустить ��опрос' включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-nps-with-skip-and-comment");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    const comment = await page.getByTestId("comment");

    await expect(comment).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-nps-with-skip-and-comment.png",
    );
    await skipCheckbox.click();
    await expect(comment).not.toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-nps-with-skip-and-comment-hidden.png",
    );
    await skipCheckbox.click();
    await expect(comment).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-nps-with-skip-and-comment.png",
    );
  });

  test.describe("Комментарий", () => {
    test("Отображает комментарий", async ({ page }) => {
      await page.goto("/?key=rating-nps-with-comment");
      const comment = await page.getByTestId("comment");
      const label = comment.getByText("Ваш комментарий");
      const navButtons = await page.getByTestId("form-nav-buttons");

      await expect(comment).toBeVisible();
      await expect(label).toBeVisible();

      // Проверяем, что кнопки навигации присутствуют если есть комментарий
      await expect(navButtons).toBeVisible();
      const widget = await page.getByTestId("fc-widget");
      await expect(widget).toHaveScreenshot("rating-nps-with-comment.png");
    });
    test("Не обязателен по умолчанию", async ({ page }) => {
      await page.goto("/?key=rating-nps-with-comment");
      const items = await page.getByTestId("rating-nps");
      const item = await page.getByTestId("rating-nps-item").first();
      const finishButton = await page.getByText("Завершить");

      await item.click();
      await finishButton.click();

      // Проверяем, что перешли на следующий шаг
      await expect(items).toBeHidden();
    });
    test("Обязателен если опция 'Обязателен' включена", async ({ page }) => {
      await page.goto("/?key=rating-nps-with-comment-required");
      const finishButton = await page.getByText("Завершить");
      const error = await page.getByText("Обязательное поле");
      const textarea = await page.getByTestId("comment").locator("textarea");
      const widget = await page.getByTestId("fc-widget");

      await expect(error).toBeHidden();
      await finishButton.click();
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "rating-nps-with-comment-required-error.png",
      );

      await textarea.fill("Комментарий");
      await textarea.blur();
      await expect(error).toBeHidden();
      await expect(widget).toHaveScreenshot(
        "rating-nps-with-comment-required.png",
      );
    });
  });

  test("Комплексный пример (Обязателен + валидация длины текста)", async ({
    page,
  }) => {
    await page.goto("/?key=rating-nps-with-comment-complex");
    const widget = await page.getByTestId("fc-widget");
    const rating = await page.getByTestId("rating-nps");
    const item = await page.getByTestId("rating-nps-item").first();
    const finishButton = await page.getByText("Завершить");
    const requiredError = await page.getByText("Обязательное поле");
    const minLengthError = await page.getByText(
      "Должно быть введено хотя бы 10 символов",
    );
    const textarea = await page.getByTestId("comment").locator("textarea");

    await expect(requiredError).toBeHidden();
    await finishButton.click();
    await expect(requiredError).toBeVisible();
    await item.click();
    await textarea.fill("text");
    await expect(minLengthError).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "rating-nps-with-comment-complex-error.png",
    );

    await textarea.fill("text with more then 10 characters");

    await expect(minLengthError).toBeHidden();
    await expect(requiredError).toBeHidden();
    await expect(widget).toHaveScreenshot(
      "rating-nps-with-comment-complex-no-error.png",
    );

    // Проверяем, что ошибки нет и перешли на следующий шаг
    await finishButton.click();
    await expect(rating).toBeHidden();
  });

  test("Отображает рейтинг NPS с метками при клике на кнопку триггер", async ({
    page,
  }) => {
    await page.goto("/?key=appearance-click:rating-nps-with-labels");

    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();

    // Click the trigger button to show the widget
    await triggerButton.click();

    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toBeVisible();

    const ratingItems = await page.getByTestId("rating-nps-item");
    await expect(ratingItems).toHaveCount(11); // NPS usually has 11 options (0-10)

    const leftLabel = await page.getByText("Начало");
    const rightLabel = await page.getByText("Конец");

    await expect(leftLabel).toBeVisible();
    await expect(rightLabel).toBeVisible();

    // Take a screenshot of the entire page to show the widget in modal
    await expect(page).toHaveScreenshot("rating-nps-with-labels-modal.png");

    // Interact with the widget to ensure it's functional
    await ratingItems.nth(5).click(); // Click on the middle option (5)

    // Take another screenshot after interaction
    await expect(page).toHaveScreenshot(
      "rating-nps-with-labels-modal-interacted.png",
    );
  });
});
