import { expect, test } from '@playwright/test'

test.describe('Вопрос \'звездный рейтинг для вариантов\'', () => {
  test('Отображает 10 звездочек для каждого варианта', async ({ page }) => {
    await page.goto('/star-rating-variants')
    const nextButton = await page.getByText('Далее')
    const stars = await page.getByTestId('star-rating')
    const errors = await page.getByText('Нужно поставить оценку').locator('visible=true')
    await expect(stars).toHaveCount(3)

    await nextButton.click()
    await expect(errors).toHaveCount(3)

    await page.waitForTimeout(300)

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('star-rating-variants-required.png')

    for (let i = 1; i <= 3; i++) {
      const star = stars.nth(i - 1).getByTestId('star-rating-item').nth(2)
      await star.click()
    }

    await expect(errors).toHaveCount(0)
    await expect(page).toHaveScreenshot(`star-rating-variants-filled.png`)

    // Перешли на следующий вопрос
    await nextButton.click()
    await expect(stars.first()).not.toBeVisible()
  })
  test('Необязательный вопрос', async ({ page }) => {
    await page.goto('/star-rating-variants-unrequired')
    const nextButton = await page.getByText('Далее')
    const stars = await page.getByTestId('star-rating')
    const errors = await page.getByText('Нужно поставить оценку').locator('visible=true')
    await expect(stars).toHaveCount(3)
    await expect(stars.first()).toBeVisible()

    await nextButton.click()
    await expect(errors).toHaveCount(0)
    await expect(stars.first()).not.toBeVisible()
  })
  test('Отображает чекбокс с пропуском', async ({ page }) => {
    await page.goto('/star-rating-variants-skipped')
    const checkbox = await page.getByText('Не готов(а) оценить')
    const nextButton = await page.getByText('Далее')
    await expect(checkbox).toBeVisible()
    const errors = await page.getByText('Нужно поставить оценку').locator('visible=true')

    await nextButton.click()
    await expect(errors).toHaveCount(3)

    await checkbox.click()
    await expect(errors).toHaveCount(0)
  })

  test('Отображает вариант с пропуском для каждого варианта', async ({ page }) => {
    await page.goto('/star-rating-variants-skip-variant')
    const nextButton = await page.getByText('Далее')
    const stars = await page.getByTestId('star-rating')
    const checkboxes = await page.getByText('Не готов(а) оценить')

    const errors = await page.getByText('Нужно поставить оценку').locator('visible=true')
    await expect(stars).toHaveCount(3)
    await expect(checkboxes).toHaveCount(3)

    await expect(page).toHaveScreenshot('star-rating-variants-skip-variant.png')

    await nextButton.click()
    await expect(errors).toHaveCount(3)

    await checkboxes.nth(0).click()
    await expect(errors).toHaveCount(2)

    await checkboxes.nth(1).click()
    await expect(errors).toHaveCount(1)

    await checkboxes.nth(2).click()
    await expect(errors).toHaveCount(0)
  })
  test.describe('Уточняющий вопрос', () => {
    test('Отображает УВ для некоторых вариантов', async ({ page }) => {
      await page.goto('/star-rating-variants-assessments')
      const nextButton = await page.getByText('Далее')
      const starsVariants = await page.getByTestId('star-rating').locator('visible=true')
      const questionVariants = await page.locator('.question-variants')
      const errors = await page.getByText('Нужно поставить оценку')
      const questionVariantsErrors = await page.getByText('Нужно выбрать один из вариантов')

      await expect(starsVariants).toHaveCount(3)

      await starsVariants.nth(0).getByTestId('star-rating-item').nth(2).click()
      await starsVariants.nth(1).getByTestId('star-rating-item').nth(2).click()
      await starsVariants.nth(2).getByTestId('star-rating-item').nth(2).click()

      await expect(questionVariants).toHaveCount(2)

      await page.waitForTimeout(300)
      // scroll to top to make a screenshot more readable
      await page.evaluate(() => window.scrollTo(0, 0))
      await expect(page).toHaveScreenshot('star-rating-variants-assessments.png')

      await nextButton.click()
      await expect(errors).toHaveCount(0)
      await expect(questionVariantsErrors).toHaveCount(2)

      await page.evaluate(() => window.scrollTo(0, 0))
      await expect(page).toHaveScreenshot('star-rating-variants-assessments-errors.png')

      // Выбираем уточняющий вопрос для первого варианта
      await questionVariants.nth(0).getByText('Вариант 3').click()
      await expect(questionVariantsErrors).toHaveCount(1)

      // Выбираем уточняющий вопрос для второго варианта
      await questionVariants.nth(1).getByText('Свой вариант').click()
      await page.locator('textarea').nth(0).fill('Тестовый ответ')

      // После заполнения уточняющего вопроса для первого и второго варианта, ошибок не должно быть
      await expect(questionVariantsErrors).toHaveCount(0)

      await page.evaluate(() => window.scrollTo(0, 0))
      await expect(page).toHaveScreenshot('star-rating-variants-assessments-filled.png')

      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(starsVariants).toHaveCount(0)
    })
    test('Отображает УВ с текстовым полем', async ({ page }) => {
      await page.goto('/star-rating-variants-assessments-text')
      const nextButton = await page.getByText('Далее')
      const starsVariants = await page.getByTestId('star-rating').locator('visible=true')
      const requiredError = await page.getByText('Обязательное поле')
      const textarea = await page.locator('textarea')

      await expect(starsVariants).toHaveCount(3)

      await starsVariants.nth(0).getByTestId('star-rating-item').nth(2).click()
      await expect(textarea).toBeVisible()

      await page.evaluate(() => window.scrollTo(0, 0))
      await expect(page).toHaveScreenshot('star-rating-variants-assessments-text.png')

      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await expect(page).toHaveScreenshot('star-rating-variants-assessments-text-error.png')

      await textarea.fill('Тестовый вариант')

      await expect(requiredError).not.toBeVisible()

      // Заполняем другие звезды
      await starsVariants.nth(1).getByTestId('star-rating-item').nth(8).click()
      await starsVariants.nth(2).getByTestId('star-rating-item').nth(2).click()
      await nextButton.click()
      await expect(starsVariants).toHaveCount(0)
    })
    test('Отображает УВ с множественным выбором', async ({ page }) => {
      await page.goto('/star-rating-variants-assessments-multiple')
      const nextButton = await page.getByText('Далее')
      const starsVariants = await page.getByTestId('star-rating').locator('visible=true')
      const questionVariants = await page.locator('.question-variants')

      await expect(starsVariants).toHaveCount(3)

      await starsVariants.nth(0).getByTestId('star-rating-item').nth(2).click()

      await page.waitForTimeout(300)
      await page.evaluate(() => window.scrollTo(0, 0))
      await expect(page).toHaveScreenshot('star-rating-variants-assessments-multiple.png')

      await questionVariants.nth(0).getByText('Вариант 1').click()
      await questionVariants.nth(0).getByText('Вариант 3').click()
      await questionVariants.nth(0).getByText('Свой вариант').click()
      await questionVariants.nth(0).locator('textarea').fill('Тестовый вариант')

      await expect(page).toHaveScreenshot('star-rating-variants-assessments-multiple-filled.png')

      await starsVariants.nth(1).getByTestId('star-rating-item').nth(2).click()
      await questionVariants.nth(1).getByText('Вариант 1').click()

      await starsVariants.nth(2).getByTestId('star-rating-item').nth(2).click()

      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(starsVariants).toHaveCount(0)
    })
    test('Скрывает УВ если выбран вариант с пропуском', async ({ page }) => {
      await page.goto('/star-rating-variants-assessments-skip-variant')
      const nextButton = await page.getByText('Далее')
      const starsVariants = await page.getByTestId('star-rating').locator('visible=true')
      const checkboxes = await page.getByText('Не готов(а) оценить')
      const questionVariants = await page.locator('.question-variants')

      await expect(starsVariants).toHaveCount(3)

      // Нажимаем на все звезды
      for (let i = 0; i < 3; i++) {
        await starsVariants.nth(i).getByTestId('star-rating-item').nth(2).click()
      }

      await expect(questionVariants).toHaveCount(2)

      await checkboxes.nth(0).click()

      await expect(questionVariants).toHaveCount(1)
      await checkboxes.nth(1).click()

      await expect(questionVariants).toHaveCount(0)

      await expect(page).toHaveScreenshot('star-rating-variants-assessments-skipped.png')

      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(starsVariants).toHaveCount(0)
    })
  })
})
