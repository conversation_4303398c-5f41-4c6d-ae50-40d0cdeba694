import { expect, test } from '@playwright/test'

test.describe('Вопрос "семантический дифференциал"', () => {
  test('Базовый вопрос семантического дифференциала', async ({ page }) => {
    await page.goto('/diff-basic')
    const nextButton = await page.getByText('Далее')
    const diffRows = await page.locator('.diff-question-row')
    const error = await page.getByText('Нужно поставить все оценки')

    await expect(diffRows).toHaveCount(2)

    await page.waitForTimeout(100)

    await expect(page).toHaveScreenshot('diff-basic-initial.png')
    await nextButton.click()
    await expect(error).toBeVisible()

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('diff-basic-required.png')

    for (let i = 0; i < 2; i++) {
      await diffRows.nth(i).locator('.diff-question-button').nth(2).click()
    }

    await expect(error).not.toBeVisible()
    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('diff-basic-filled.png')

    await nextButton.click()
    await expect(diffRows.first()).not.toBeVisible()
  })

  test('Вопрос семантического дифференциала с возможностью пропуска', async ({ page }) => {
    await page.goto('/diff-with-skip')
    const skipButton = await page.getByText('Пропустить этот вопрос')
    const diffRows = await page.locator('.diff-question-row')
    const nextButton = await page.getByText('Далее')

    await expect(skipButton).toBeVisible()
    await skipButton.click()

    await expect(nextButton).toBeVisible()
    await expect(page).toHaveScreenshot('diff-with-skip-skipped.png')
    await nextButton.click()

    await expect(diffRows.first()).not.toBeVisible()
  })

  test('Вопрос семантического дифференциала с галереей', async ({ page }) => {
    await page.goto('/diff-with-gallery')
    const gallery = await page.locator('.gallery-container')
    const diffRows = await page.locator('.diff-question-row')

    await expect(gallery).toBeVisible()
    await expect(diffRows).toHaveCount(2)

    await expect(page).toHaveScreenshot('diff-with-gallery.png')
  })

  test('Вопрос семантического дифференциала с обязательным комментарием', async ({ page }) => {
    await page.goto('/diff-with-comment')
    const nextButton = await page.getByText('Далее')
    const diffRows = await page.locator('.diff-question-row')
    const commentField = await page.locator('textarea')

    await expect(page).toHaveScreenshot('diff-with-comment-initial.png')

    for (let i = 0; i < 2; i++) {
      await diffRows.nth(i).locator('.diff-question-button').nth(2).click()
    }

    await nextButton.click()
    await expect(page.getByText('Обязательное поле')).toBeVisible()

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('diff-with-comment-required.png')

    await commentField.fill('Тестовый комментарий')

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('diff-with-comment-filled.png')
    await nextButton.click()

    await expect(diffRows.first()).not.toBeVisible()
  })

  test('Вопрос семантического дифференциала с круговой формой', async ({ page }) => {
    await page.goto('/diff-circle-form')
    const diffRows = await page.locator('.diff-question-row')
    const circleButtons = await page.locator('.diff-question-button--circle')

    await expect(diffRows).toHaveCount(2)
    await expect(circleButtons).toHaveCount(10) // 5 buttons per row, 2 rows

    await expect(page).toHaveScreenshot('diff-circle-form.png')

    await circleButtons.first().click()
    await expect(circleButtons.first()).toHaveClass(/diff-question-button--selected/)
  })

  test('Вопрос семантического дифференциала на английском языке', async ({ page }) => {
    await page.goto('/diff-english?lang=en')
    const diffRows = await page.locator('.diff-question-row')
    const startLabels = await diffRows.locator('.diff-question-label--start')
    const endLabels = await diffRows.locator('.diff-question-label--end')

    await expect(page.getByText('Rate the following parameters')).toBeVisible()
    await expect(startLabels.first()).toHaveText('Poor')
    await expect(endLabels.first()).toHaveText('Excellent')
    await expect(startLabels.nth(1)).toHaveText('Slow')
    await expect(endLabels.nth(1)).toHaveText('Fast')

    await expect(page).toHaveScreenshot('diff-english.png')
  })
})
