import { expect, test } from "@playwright/test";

test.describe("Вопрос 'Варианты ответов'", () => {
  test.describe("Выбор одного варианта", () => {
    test("Отображает варианты", async ({ page }) => {
      await page.goto("/?key=variants-single");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");

      await expect(checks).toHaveCount(5);
      await expect(checks).toHaveText([
        "Раз",
        "Интерьер 2. Страховая сумма субъективно защищает договор.",
        "Три",
        "Четыре",
        "Свой вариант",
      ]);

      await expect(widget).toHaveScreenshot("variants-single.png");
    });
    test("Обязателен по умолчанию", async ({ page }) => {
      await page.goto("/?key=variants-single");
      const widget = await page.getByTestId("fc-widget");
      const firstCheck = await widget.getByTestId("check").first();
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText(
        "Нужно выбрать хотя бы один вариант ответа",
      );

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "variants-single-required-error.png",
      );
      await firstCheck.click();
      await page.waitForTimeout(300);
      await expect(error).not.toBeVisible();
    });
    test("Не обязателен если опция 'Обязателен' выключена", async ({
      page,
    }) => {
      await page.goto("/?key=variants-single-not-required");
      const variants = await page.getByTestId("question-variants");
      const widget = await page.getByTestId("fc-widget");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText(
        "Нужно выбрать хотя бы один вариант ответа",
      );

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).not.toBeVisible();
      await expect(variants).not.toBeVisible();
    });
    test("Отображает свой вариант", async ({ page }) => {
      await page.goto("/?key=variants-single");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText("Обязательное поле");
      const lastCheck = checks.last();

      await lastCheck.click();
      await page.waitForTimeout(660);
      const textarea = await widget.getByTestId("textarea");
      await expect(textarea).toBeVisible();
      await expect(widget).toHaveScreenshot("variants-single-selfvariant.png");

      await finishButton.click();
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "variants-single-selfvariant-error.png",
      );
    });
    test("Cтображает ошибку у своего варианта после ошибки 'Нужно выбрать хотя бы один вариант ответа'", async ({
      page,
    }) => {
      await page.goto("/?key=variants-single");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText("Обязательное поле");
      const lastCheck = checks.last();

      await finishButton.click();
      await page.waitForTimeout(300);
      await lastCheck.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
    });
  });
  test.describe("Выбор нескольких вариантов", () => {
    test("Отображает варианты", async ({ page }) => {
      await page.goto("/?key=variants-multiple");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");

      await checks.nth(0).click();
      await checks.nth(2).click();

      await expect(checks).toHaveCount(5);
      await expect(checks).toHaveText([
        "Раз",
        "Интерьер 2. Страховая сумма субъективно защищает договор.",
        "Три",
        "Четыре",
        "Свой вариант",
      ]);

      await expect(widget).toHaveScreenshot("variants-multiple.png");
    });
    test("Обязателен по умолчанию", async ({ page }) => {
      await page.goto("/?key=variants-multiple");
      const widget = await page.getByTestId("fc-widget");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText(
        "Нужно выбрать хотя бы один вариант ответа",
      );

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "variants-multiple-required-error.png",
      );
    });
    test("Отображает ошибку при опции 'Min кол-во выбранных ответов: 2'", async ({
      page,
    }) => {
      await page.goto("/?key=variants-multiple-min-choose-variants");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText(
        "Необходимо выбрать хотя бы 2 варианта",
      );

      await finishButton.click();
      await expect(error).toBeVisible();

      await checks.nth(0).click();
      await expect(error).toBeVisible();

      await checks.nth(1).click();
      await expect(error).not.toBeVisible();
    });
    test("Отключает другие варианты при  Max кол-во выбранных ответов: 2", async ({
      page,
    }) => {
      await page.goto("/?key=variants-multiple");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");
      const disabledItems = await widget.locator("[data-disabled='true']");

      await checks.nth(0).click();
      await checks.nth(2).click();

      await expect(disabledItems).toHaveCount(3);
      await expect(widget).toHaveScreenshot(
        "variants-multiple-disabled-items.png",
      );

      // undisables on uncheck
      await checks.nth(0).click();
      await expect(disabledItems).toHaveCount(0);

      await expect(widget).toHaveScreenshot(
        "variants-multiple-disabled-items-undisabled.png",
      );
    });
    test("Отключает другие варианты если выбрано вариант 'Ничего из перечисленного'", async ({
      page,
    }) => {
      await page.goto("/?key=variants-multiple");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");

      await checks.nth(0).click();
      await checks.nth(3).click();
      await expect(widget).toHaveScreenshot(
        "variants-multiple-disabled-items-nothing.png",
      );
    });
    test("Отображает свой вариант", async ({ page }) => {
      await page.goto("/?key=variants-multiple");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText("Обязательное поле");
      const lastCheck = checks.last();

      await checks.first().click();
      await lastCheck.click();
      await page.waitForTimeout(650);
      const textarea = await widget.getByTestId("textarea");
      await expect(textarea).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "variants-multiple-selfvariant.png",
      );

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "variants-multiple-selfvariant-error.png",
      );
    });
    test("Галка 'Затрудняюсь ответить' отключает все варианты", async ({
      page,
    }) => {
      await page.goto("/?key=variants-multiple-skip");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");
      const finishButton = await page.getByText("Завершить");
      const skipCheckbox = checks.last();

      await checks.first().click();
      await checks.nth(1).click();
      await skipCheckbox.click();
      await expect(widget).toHaveScreenshot(
        "variants-multiple-disabled-items-all.png",
      );

      await finishButton.click();

      // Перешли на финальный экран
      await expect(skipCheckbox).not.toBeVisible();
    });
    test("Отображает варианты с файлами", async ({ page }) => {
      await page.goto("/?key=variants-multiple-with-files");
      const widget = await page.getByTestId("fc-widget");
      const checks = await widget.getByTestId("check");

      await page.waitForLoadState("networkidle");
      await expect(checks).toHaveCount(5);
      await expect(checks).toHaveText([
        "Раз",
        "Интерьер 2. Страховая сумма субъективно защищает договор.",
        "Три",
        "Четыре",
        "Свой вариант",
      ]);

      await expect(widget).toHaveScreenshot("variants-multiple-with-files.png");
    });
    test("Отображает варианты с файлами при клике на кнопку триггер", async ({
      page,
    }) => {
      await page.goto("/?key=appearance-click:variants-multiple-with-files");

      const triggerButton = await page.locator(".fc-widget-preview-button");
      await expect(triggerButton).toBeVisible();

      // Click the trigger button to show the widget
      await triggerButton.click();

      await page.waitForTimeout(400);

      const widget = await page.getByTestId("fc-widget");
      await expect(widget).toBeVisible();

      const checks = await widget.getByTestId("check");

      await page.waitForLoadState("networkidle");
      await expect(checks).toHaveCount(5);
      await expect(checks).toHaveText([
        "Раз",
        "Интерьер 2. Страховая сумма субъективно защищает договор.",
        "Три",
        "Четыре",
        "Свой вариант",
      ]);

      // Take a screenshot of the entire page to show the widget in modal
      await expect(page).toHaveScreenshot(
        "variants-multiple-with-files-modal.png",
      );

      // Click on the first and third options
      await checks.nth(2).click();

      // Click on the "Свой вариант" option
      const lastCheck = checks.last();
      await lastCheck.click();

      await page.waitForTimeout(1000);
      const textarea = await widget.getByTestId("textarea");
      await textarea.scrollIntoViewIfNeeded();
      await expect(textarea).toBeVisible();

      // Fill in the custom variant
      await textarea.fill("Custom variant text");

      // Take a final screenshot with the custom variant
      await expect(page).toHaveScreenshot(
        "variants-multiple-with-files-modal-custom-variant.png",
      );
    });
  });
});
