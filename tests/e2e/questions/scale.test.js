import { expect, test } from '@playwright/test'

test.describe('Scale Question Tests', () => {
  test('renders single scale and takes screenshot', async ({ page }) => {
    await page.goto('/scale')
    await expect(page.locator('.scale-container')).toBeVisible()
    await expect(page.locator('.slider-container')).toBeVisible()
    await expect(page.locator('.fc-input.scale-input')).toBeVisible()

    await expect(page).toHaveScreenshot('single-scale.png')
  })

  test('renders scale with skip checkbox', async ({ page }) => {
    await page.goto('/scale-with-skip')
    await expect(page.locator('.scale-container')).toBeVisible()
    await expect(page.locator('.skip-container .fc-check')).toBeVisible()

    const skipCheckbox = page.locator('.fc-check')

    await skipCheckbox.click()

    await expect(page).toHaveScreenshot('scale-skipped.png')
  })

  test('renders multiple scales (scale-with-variants) and takes screenshot', async ({ page }) => {
    await page.goto('/scale-with-variants')
    await expect(page.locator('.scale-items-container')).toBeVisible()
    const scaleItems = page.locator('.scale-item')
    await expect(scaleItems).toHaveCount(3)

    for (let i = 0; i < 3; i++) {
      await expect(scaleItems.nth(i).locator('.scale-item__label')).toBeVisible()
      await expect(scaleItems.nth(i).locator('.slider-container')).toBeVisible()
      await expect(scaleItems.nth(i).locator('.fc-input.scale-input')).toBeVisible()
    }

    await expect(page).toHaveScreenshot('multiple-scales.png')
  })

  test('renders complex question with scale, comment, skip and interacts with it', async ({ page }) => {
    await page.goto('/scale-with-complex-comment')
    await expect(page.locator('.scale-container')).toBeVisible()
    await expect(page.locator('textarea.form-control')).toBeVisible()
    await expect(page.locator('.skip-container .fc-check--checkbox')).toBeVisible()

    await expect(page).toHaveScreenshot('complex-scale-before.png')

    // Interact with the scale
    const slider = page.locator('.slider-thumb')
    await slider.dragTo(page.locator('.slider-track'), {
      targetPosition: { x: 100, y: 0 },
    })

    // Fill in the comment
    await page.locator('textarea.form-control').fill('This is a test comment')

    await expect(page).toHaveScreenshot('complex-scale-after.png')
  })

  test('scale can be changed by handle (thumb) or by changing input field', async ({ page }) => {
    await page.goto('/scale')
    const slider = page.locator('.slider-thumb')
    const input = page.locator('.fc-input.scale-input input')
    const sliderValue = page.locator('.slider-value')

    // Change by dragging the handle
    await slider.dragTo(page.locator('.slider-track'), {
      targetPosition: { x: 50, y: 0 },
    })
    await expect(sliderValue).not.toHaveText('0')

    // Change by input field
    await input.fill('8')
    await input.blur()
    await expect(sliderValue).toHaveText('8')

    // Verify that the value changed after blur
    await input.fill('5')
    await expect(sliderValue).toHaveText('8')
    await input.blur()
    await expect(sliderValue).toHaveText('5')
  })
  test('scale with custom range', async ({ page }) => {
    await page.goto('/scale-custom-range')
    const slider = page.locator('.slider-thumb')

    await expect(slider).toBeVisible()
    await expect(page).toHaveScreenshot('scale-custom-range.png')

    const sliderValue = page.locator('.slider-value')
    await slider.dragTo(page.locator('.slider-track'), {
      targetPosition: { x: 100, y: 0 },
    })

    await expect(sliderValue).toHaveText('230')
    await expect(page).toHaveScreenshot('scale-custom-range-after.png')
  })
})
