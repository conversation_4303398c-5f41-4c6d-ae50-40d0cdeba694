import { expect, test } from "@playwright/test";

test.describe("Вопрос 'smile rating'", () => {
  test("Отображает 2 сердечка по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating-smile-heart-2");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(2);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-heart-2.png");

    await smiles.first().click();
    await expect(widget).toHaveScreenshot("smile-rating-heart-2-selected.png");
  });

  test("Отображает 2 сердечка с метками", async ({ page }) => {
    await page.goto("/?key=rating-smile-heart-2-with-labels");
    const widget = await page.getByTestId("fc-widget");
    const smile = await page.getByTestId("img-rating-item").first();
    const label = await page.getByText("mark 1");

    await smile.click();
    await expect(label).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-heart-2-with-labels.png",
    );
  });

  test("Отображает 2 сердечка с опцией 'Всегда отображать метки'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-heart-2-show-labels");
    const widget = await page.getByTestId("fc-widget");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(2);
    const labels = await page.getByTestId("img-rating-item-label");
    await expect(labels).toHaveCount(2);
    await expect(labels).toHaveText(["mark 1", "mark 2"]);
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-heart-2-show-labels.png",
    );
  });

  test("Отображает 2 лайка по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating-smile-like-2");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(2);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-like-2.png");
  });

  test("Отображает 2 лайка с метками", async ({ page }) => {
    await page.goto("/?key=rating-smile-like-2-with-labels");
    const smile = await page.getByTestId("img-rating-item").first();
    const label = await page.getByText("mark 1");
    const widget = await page.getByTestId("fc-widget");
    await smile.click();
    await expect(label).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-like-2-with-labels.png",
    );
  });

  test("Отображает 2 лайка с опцией 'Всегда отображать метки'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-like-2-show-labels");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(2);
    const labels = await page.getByTestId("img-rating-item-label");
    await expect(labels).toHaveCount(2);
    await expect(labels).toHaveText(["mark 1", "mark 2"]);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-like-2-show-labels.png",
    );
  });

  test("Отображает 3 лица по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating-smile-face-3");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(3);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-face-3.png");
    await smiles.first().click();
    await expect(widget).toHaveScreenshot("smile-rating-face-3-selected.png");
  });

  test("Отображает 3 лица с метками", async ({ page }) => {
    await page.goto("/?key=rating-smile-face-3-with-labels");
    const smile = await page.getByTestId("img-rating-item").nth(1);
    const label = await page.getByText("mark 3");
    const widget = await page.getByTestId("fc-widget");

    await smile.click();
    await expect(label).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-face-3-with-labels.png",
    );
  });

  test("Отображает 3 лица с опцией 'Всегда отображать метки'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-face-3-show-labels");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(3);
    const labels = await page.getByTestId("img-rating-item-label");
    await expect(labels).toHaveCount(3);
    await expect(labels).toHaveText(["mark 1", "mark 3", "mark 5"]);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-face-3-show-labels.png",
    );
  });

  test("Отображает 5 роботов по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating-smile-robot-5");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(5);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-robot-5.png");
    await smiles.first().click();
    await expect(widget).toHaveScreenshot("smile-rating-robot-5-selected.png");
  });

  test("Отображает 5 роботов с метками", async ({ page }) => {
    await page.goto("/?key=rating-smile-robot-5-with-labels");
    const smile = await page.getByTestId("img-rating-item").nth(1);
    const label = await page.getByText("mark 2");
    const widget = await page.getByTestId("fc-widget");

    await smile.click();
    await expect(label).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-robot-5-with-labels.png",
    );
  });

  test("Отображает 5 роботов с опцией 'Всегда отображать метки'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-robot-5-show-labels");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(5);
    const labels = await page.getByTestId("img-rating-item-label");
    await expect(labels).toHaveCount(5);
    await expect(labels).toHaveText([
      "mark 1",
      "mark 2",
      "mark 3",
      "mark 4",
      "mark 5",
    ]);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-robot-5-show-labels.png",
    );
  });

  test("Отображает 3 желтых лица по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating-smile-color_face-3");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(3);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-color_face-3.png");
    await smiles.first().click();
    await expect(widget).toHaveScreenshot(
      "smile-rating-color_face-3-selected.png",
    );
  });

  test("Отображает 3 желтых лица с метками", async ({ page }) => {
    await page.goto("/?key=rating-smile-color_face-3-with-labels");
    const smile = await page.getByTestId("img-rating-item").nth(1);
    const label = await page.getByText("mark 3");
    const widget = await page.getByTestId("fc-widget");

    await smile.click();
    await expect(label).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-color_face-3-with-labels.png",
    );
  });

  test("Отображает 3 желтых лица с опцией 'Всегда отображать метки'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-color_face-3-show-labels");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(3);
    const labels = await page.getByTestId("img-rating-item-label");
    await expect(labels).toHaveCount(3);
    await expect(labels).toHaveText(["mark 1", "mark 3", "mark 5"]);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-color_face-3-show-labels.png",
    );
  });

  test("Отображает 5 погодных иконок по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating-smile-weather-5");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(5);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-weather-5.png");
    await smiles.first().click();
    await expect(widget).toHaveScreenshot(
      "smile-rating-weather-5-selected.png",
    );
  });

  test("Отображает 5 погодных иконок с метками", async ({ page }) => {
    await page.goto("/?key=rating-smile-weather-5-with-labels");
    const smile = await page.getByTestId("img-rating-item").nth(1);
    const label = await page.getByText("mark 2");
    const widget = await page.getByTestId("fc-widget");

    await smile.click();
    await expect(label).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-weather-5-with-labels.png",
    );
  });

  test("Отображает 5 погодных иконок с опцией 'Всегда отображать метки'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-weather-5-show-labels");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(5);
    const labels = await page.getByTestId("img-rating-item-label");
    await expect(labels).toHaveCount(5);
    await expect(labels).toHaveText([
      "mark 1",
      "mark 2",
      "mark 3",
      "mark 4",
      "mark 5",
    ]);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-weather-5-show-labels.png",
    );
  });

  test("Отображает 5 разных иконок по умолчанию", async ({ page }) => {
    await page.goto("/?key=rating-smile-emoji-5");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(5);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-emoji-5.png");
    await smiles.nth(2).click();
    await expect(widget).toHaveScreenshot("smile-rating-emoji-5-selected.png");
  });

  test("Отображает 5 разных иконок с метками", async ({ page }) => {
    await page.goto("/?key=rating-smile-emoji-5-with-labels");
    const smile = await page.getByTestId("img-rating-item").nth(1);
    const label = await page.getByText("mark 2");
    const widget = await page.getByTestId("fc-widget");

    await smile.click();
    await expect(label).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-emoji-5-with-labels.png",
    );
  });

  test("Отображает 5 разных иконок с опцией 'Всегда отображать метки'", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-emoji-5-show-labels");
    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(5);
    const labels = await page.getByTestId("img-rating-item-label");
    await expect(labels).toHaveCount(5);
    await expect(labels).toHaveText([
      "mark 1",
      "mark 2",
      "mark 3",
      "mark 4",
      "mark 5",
    ]);
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-emoji-5-show-labels.png",
    );
  });
  test("Отображает чекбокс 'Пропустить вопрос' если опция включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-with-skip");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    await expect(skipCheckbox).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("smile-rating-with-skip.png");
  });

  test("Пропускает обязательный вопрос если опция 'Пропустить вопрос' включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-with-skip-required");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    const finishButton = await page.getByText("Завершить");
    const error = await page.getByText("Нужно поставить оценку");

    await page.waitForLoadState("networkidle");

    await finishButton.click();
    await expect(error).toBeVisible();

    await page.waitForTimeout(300);
    await expect(widget).toHaveScreenshot(
      "smile-rating-with-skip-required-error.png",
    );

    await skipCheckbox.click();
    await finishButton.click();
    await expect(error).not.toBeVisible();
  });

  test("Скрывает комментарий если опция 'Пропустить вопрос' включена", async ({
    page,
  }) => {
    await page.goto("/?key=rating-smile-with-skip-and-comment");
    const widget = await page.getByTestId("fc-widget");
    const skipCheckbox = await page.getByText("Пропустить вопрос");
    const comment = await page.getByTestId("comment");

    await expect(comment).toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-with-skip-and-comment.png",
    );
    await skipCheckbox.click();
    await expect(comment).not.toBeVisible();
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot(
      "smile-rating-with-skip-and-comment-hidden.png",
    );
    await skipCheckbox.click();
    await expect(comment).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "smile-rating-with-skip-and-comment.png",
    );
  });
  // Tests for rating with comment
  test.describe("Комментарий", () => {
    test("Отображает комментарий", async ({ page }) => {
      await page.goto("/?key=rating-smile-with-comment");
      const comment = await page.getByTestId("comment");
      const label = comment.getByText("Ваш комментарий");
      const navButtons = await page.getByTestId("form-nav-buttons");

      await expect(comment).toBeVisible();
      await expect(label).toBeVisible();

      // Проверяем, что кнопки навигации присутствуют если есть комментарий
      await expect(navButtons).toBeVisible();
      const widget = await page.getByTestId("fc-widget");
      await page.waitForLoadState("networkidle");
      await expect(widget).toHaveScreenshot("smile-rating-with-comment.png");
    });

    test("Не обязателен по умолчанию", async ({ page }) => {
      await page.goto("/?key=rating-smile-with-comment");
      const smiles = await page.getByTestId("img-rating");
      const smile = await page.getByTestId("img-rating-item").first();
      const finishButton = await page.getByText("Завершить");

      await smile.click();
      await finishButton.click();

      // Проверяем, что перешли на следующий шаг
      await expect(smiles).toBeHidden();
    });

    test("Обязателен если опция 'Обязателен' включена", async ({ page }) => {
      await page.goto("/?key=rating-smile-with-comment-required");
      const smiles = await page.getByTestId("img-rating");
      const smile = await page.getByTestId("img-rating-item").first();
      const finishButton = await page.getByText("Завершить");
      const error = await page.getByText("Обязательное поле");
      const textarea = await page.getByTestId("comment").locator("textarea");
      const widget = await page.getByTestId("fc-widget");

      await page.waitForLoadState("networkidle");
      await expect(error).toBeHidden();
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "smile-rating-with-comment-required-error.png",
      );

      await smile.click();
      await textarea.fill("some value");

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await expect(error).toBeHidden();
      await finishButton.click();
      await expect(smiles).toBeHidden();
    });

    test("Комплексный пример (Обязателен + валидация длины текста)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-smile-with-comment-complex");
      const widget = await page.getByTestId("fc-widget");
      const smiles = await page.getByTestId("img-rating");
      const smile = await page.getByTestId("img-rating-item").first();
      const finishButton = await page.getByText("Завершить");
      const requiredError = await page.getByText("Обязательное поле");
      const minLengthError = await page.getByText(
        "Должно быть введено хотя бы 10 символов",
      );
      const textarea = await page.getByTestId("comment").locator("textarea");

      await expect(requiredError).toBeHidden();
      await finishButton.click();
      await expect(requiredError).toBeVisible();
      await page.waitForLoadState("networkidle");
      await expect(widget).toHaveScreenshot(
        "smile-rating-with-comment-complex-error.png",
      );

      await smile.click();
      await textarea.fill("text");
      await expect(minLengthError).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "smile-rating-with-comment-complex-minlength-error.png",
      );
      await textarea.fill("text with more than 10 characters");

      await expect(minLengthError).toBeHidden();
      await expect(requiredError).toBeHidden();
      await expect(widget).toHaveScreenshot(
        "smile-rating-with-comment-complex-no-error.png",
      );

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await finishButton.click();
      await expect(smiles).toBeHidden();
    });
  });

  // Add more tests for other smile types and counts as needed

  test("Отображает smile rating при клике на кнопку триггер и переходит к следующему вопросу после клика", async ({
    page,
  }) => {
    await page.goto("/?key=appearance-click:rating-smile-face-3");

    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();

    // Click the trigger button to show the widget
    await triggerButton.click();

    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toBeVisible();

    const smilesContainer = await page.getByTestId("img-rating");
    await expect(smilesContainer).toBeVisible();

    const smiles = await page.getByTestId("img-rating-item");
    await expect(smiles).toHaveCount(3);

    // Take a screenshot of the entire page to show the widget in modal
    await expect(page).toHaveScreenshot("smile-rating-face-3-modal.png");

    // Click on the middle smile
    await smiles.nth(1).click();

    // click next button
    const nextButton = await page.getByText("Завершить");
    await nextButton.click();

    // Check that we've moved to the next question (smiles are no longer visible)
    await expect(smilesContainer).toBeHidden();

    // Take another screenshot after interaction
    await expect(page).toHaveScreenshot(
      "smile-rating-face-3-modal-next-question.png",
    );
  });

  test.describe("УВ и загрузка файлов/скриншотов", () => {
    test("УВ с множественным выбором и загрузкой файлов (default questionScreenshot)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-smile-with-assessments-multiple-screenshot-default");
      const widget = await page.getByTestId("fc-widget");
      const smiles = await page.getByTestId("img-rating");
      const finishButton = await page.getByText("Завершить");
      const smile = await page.getByTestId("img-rating-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");
      const uploadButton = await page.getByRole("button", { name: /прикрепить файл/i });
      const screenshotButton = await page.getByRole("button", { name: /сделать скриншот/i });

      await smile.click();
      await expect(assessmentChecks).toHaveCount(3); // 2 варианта + 1 самовариант

      // Кнопка загрузки файлов должна быть видна
      await expect(uploadButton).toBeVisible();
      
      // Кнопка скриншота должна не быть видна если не включена в настройках
      await expect(screenshotButton).not.toBeVisible();

      await page.waitForLoadState("networkidle");
      await expect(widget).toHaveScreenshot(
        "smile-rating-with-assessments-multiple-screenshot-default.png",
      );

      // Проверяем, что можем пройти дальше без загрузки файлов
      await finishButton.click();
      await expect(smiles).toBeHidden();
    });

    test("УВ с множественным выбором и загрузкой файлов (custom questionScreenshot)", async ({
      page,
    }) => {
      await page.goto("/?key=rating-smile-with-assessments-multiple-screenshot-custom");
      const widget = await page.getByTestId("fc-widget");
      const smiles = await page.getByTestId("img-rating");
      const finishButton = await page.getByText("Завершить");
      const smile = await page.getByTestId("img-rating-item").last();
      const assessmentChecks = await page.getByTestId("assessments-check");
      const uploadButton = await page.getByRole("button", { name: /прикрепить файлы \(свой текст\)/i });
      const screenshotButton = await page.getByRole("button", { name: /добавить скриншот/i });
      const description = await page.getByText(/прикрепите скриншоты или файлы/i);

      await smile.click();
      await expect(assessmentChecks).toHaveCount(3); // 2 варианта + 1 самовариант

      // Кнопка загрузки файлов с кастомным текстом должна быть видна
      await expect(uploadButton).toBeVisible();
      
      // Кнопка скриншота с кастомным текстом должна быть видна
      await expect(screenshotButton).toBeVisible();

      // Описание должно быть видно
      await expect(description).toBeVisible();

      await page.waitForLoadState("networkidle");
      await expect(widget).toHaveScreenshot(
        "smile-rating-with-assessments-multiple-screenshot-custom.png",
      );

      // Проверяем, что можем пройти дальше без загрузки файлов
      await finishButton.click();
      await expect(smiles).toBeHidden();
    });
  });
});
