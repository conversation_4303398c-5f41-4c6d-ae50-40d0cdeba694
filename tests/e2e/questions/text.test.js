import { expect, test } from '@playwright/test'

test.describe('Вопрос \'Текстовый ответ\'', () => {
  test.describe('Текстовое поле', () => {
    test('Отображает текстовое поле по умолчанию', async ({ page }) => {
      await page.goto('/textarea-default')
      const textarea = await page.getByTestId('text-question-field')

      await expect(textarea).toBeVisible()
      await expect(page).toHaveScreenshot('textarea-default.png')
    })

    test('Обязательное поле по умолчанию', async ({ page }) => {
      await page.goto('/textarea-default')
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText('Обязательное поле')

      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(
        'textarea-default-required-error.png',
      )
    })

    test('Необязательное поле', async ({ page }) => {
      await page.goto('/textarea-unrequired')
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText('Обязательное поле')

      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot('textarea-unrequired.png')
    })

    test('Минимальная длина текста 100 символов', async ({ page }) => {
      await page.goto('/textarea-min-100')
      const textarea = await page.getByTestId('text-question-field')
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText(
        'Должно быть введено хотя бы 100 символов',
      )

      await textarea.fill('Тест')
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('textarea-min-100-error.png')

      await textarea.fill('Тест'.repeat(25))
      await textarea.blur()
      await page.waitForTimeout(100)
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot('textarea-min-100.png')
    })
  })
  test.describe('Не маскированное однострочное поле', () => {
    test('Отображает однострочное поле по умолчанию', async ({ page }) => {
      await page.goto('/input-default')
      const input = await page.getByTestId('text-question-field')

      await expect(input).toBeVisible()
      await expect(page).toHaveScreenshot('input-default.png')
    })

    test('Обязательное поле по умолчанию', async ({ page }) => {
      await page.goto('/input-default')
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText('Обязательное поле')

      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('input-default-required-error.png')
    })

    test('Необязательное поле', async ({ page }) => {
      await page.goto('/input-unrequired')
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText('Обязательное поле')

      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot('input-unrequired.png')
    })

    test('Минимальная длина текста 100 символов', async ({ page }) => {
      await page.goto('/input-min-100')
      const input = await page.getByTestId('text-question-field')
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText(
        'Должно быть введено хотя бы 100 символов',
      )

      await input.fill('Тест')
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('input-min-100-error.png')

      await input.fill('Тест'.repeat(25))
      await input.blur()
      await page.waitForTimeout(300)
      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot('input-min-100.png')
    })
  })
  test.describe('Маскированное текстовое поле', () => {
    test('Отображает маскированное поле для телефона', async ({ page }) => {
      await page.goto('/input-mask-phone')
      const inputMasked = await page.getByTestId('phone-input')
      const finishButton = await page.getByText('Далее')
      const errorRequired = await page.getByText('Обязательное поле')
      const errorFormat = await page.getByText('Неверный формат')

      await expect(inputMasked).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-phone.png')

      // Check for required error
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorRequired).toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-phone-required-error.png',
      )

      // Fill the input with an invalid phone number and check for format error
      await inputMasked.fill('970')
      await page.waitForTimeout(300)
      await expect(errorFormat).toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-phone-format-error.png',
      )
      await inputMasked.clear()
      await inputMasked.focus()
      await inputMasked.fill('9705680304')
      await expect(errorFormat).not.toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-phone-filled.png')
    })

    test('Отображает маскированное поле для email', async ({ page }) => {
      await page.goto('/input-mask-email')
      const inputMasked = await page.getByTestId('email-input')
      const finishButton = await page.getByText('Далее')
      const errorRequired = await page.getByText('Обязательное поле')

      await expect(inputMasked).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-email.png')

      // Check for required error
      await finishButton.click()
      await page.waitForTimeout(300)
      const errorFormat = await page.getByText('Неверный формат')
      await expect(errorRequired).toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-email-required-error.png',
      )

      // Fill the input with an invalid email and check for format error
      await inputMasked.fill('invalid-email')
      await page.waitForTimeout(300)
      await expect(errorFormat).toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-email-format-error.png',
      )

      // fill correct email
      await inputMasked.fill('<EMAIL>')
      await expect(errorFormat).not.toBeVisible()
    })

    test('Отображает маскированное поле для вебсайта', async ({ page }) => {
      await page.goto('/input-mask-website')
      const inputMasked = await page.getByTestId('site-input')
      const finishButton = await page.getByText('Далее')
      const errorRequired = await page.getByText('Обязательное поле')
      const errorFormat = await page.getByText('Неверный формат')

      await expect(inputMasked).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-website.png')

      // Check for required error
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorRequired).toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-website-required-error.png',
      )

      // Fill the input with an invalid website and check for format error
      await inputMasked.fill('invalid-website')
      await expect(errorFormat).toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-website-format-error.png',
      )

      // fill correct website
      await inputMasked.fill('https://example.com')
      await expect(errorFormat).not.toBeVisible()
    })
    test('Отображает маскированное поле для ФИО', async ({ page }) => {
      await page.goto('/input-mask-fio')
      const inputName = await page.getByTestId('input-name')
      const inputSurname = await page.getByTestId('input-surname')
      const inputPatronym = await page.getByTestId('input-patronym')
      const finishButton = await page.getByText('Далее')
      const errorRequired = await page.getByText('Обязательное поле')

      await expect(inputName).toBeVisible()
      await expect(inputSurname).toBeVisible()
      await expect(inputPatronym).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-fio.png')

      // Check for required error
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorRequired).toHaveCount(3)
      await expect(page).toHaveScreenshot(
        'input-mask-fio-required-error.png',
      )

      // Fill the inputs with valid FIO data
      await inputName.fill('Иван')
      await inputSurname.fill('Иванов')
      await inputPatronym.fill('Иванович')
      await page.waitForTimeout(300)
      await expect(errorRequired).toHaveCount(0)
      await inputPatronym.blur()
      await expect(page).toHaveScreenshot('input-mask-fio-filled.png')
    })

    test('Отображает маскированное поле для ФИО (сложный)', async ({
      page,
    }) => {
      await page.goto('/input-mask-fio-complex')
      const inputName = await page.getByTestId('input-name')
      const inputSurname = await page.getByTestId('input-surname')
      const inputPatronym = await page.getByTestId('input-patronym')
      const finishButton = await page.getByText('Далее')
      const errorRequired = await page.getByText('Обязательное поле')

      await expect(inputName).not.toBeVisible() // Name is hidden
      await expect(inputSurname).toBeVisible()
      await expect(inputPatronym).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-fio-complex.png')

      // Check for required error
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorRequired).toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-fio-complex-required-error.png',
      )

      // Fill the inputs with valid FIO data
      await inputSurname.fill('Иванов')
      await inputPatronym.fill('Иванович')
      await inputPatronym.blur()
      await page.waitForTimeout(300)
      await expect(errorRequired).not.toBeVisible()
      await expect(page).toHaveScreenshot(
        'input-mask-fio-complex-filled.png',
      )
    })
  })

  test.describe('Маскированное поле для даты', () => {
    test('Отображает маскированное поле для даты и обязательно по умолчанию', async ({ page }) => {
      await page.goto('/input-mask-date-required')
      const inputMasked = await page.getByTestId('date-picker-input')
      const finishButton = await page.getByText('Далее')
      const errorRequired = await page.getByText('Обязательное поле')

      await expect(inputMasked).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-date-required.png')

      // Check for required error
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorRequired).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-date-required-error.png')
    })

    test('Показывает ошибку "Неверный формат" при неполном вводе даты', async ({ page }) => {
      await page.goto('/input-mask-date-required')
      const inputMasked = await page.getByTestId('date-picker-input')
      const finishButton = await page.getByText('Далее')
      const errorFormat = await page.getByText('Неверный формат')

      await inputMasked.fill('01.02.')
      await inputMasked.press('Escape')
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorFormat).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-date-format-error.png')
    })

    test('Отображает календарь при фокусе на поле ввода', async ({ page }) => {
      await page.clock.install({ time: new Date('2024-10-06T08:00:00') })
      await page.goto('/input-mask-date-required')
      const inputMasked = await page.getByTestId('date-picker-input')
      const calendar = await page.locator('.date-picker-content')

      await inputMasked.focus()
      await page.waitForTimeout(300)
      await expect(calendar).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-date-calendar-visible.png')
    })

    test('Выбирает дату в календаре при вводе корректной даты', async ({ page }) => {
      await page.clock.install({ time: new Date('2024-10-06T08:00:00') })
      await page.goto('/input-mask-date-required')
      const inputMasked = await page.getByTestId('date-picker-input')
      const calendar = await page.locator('.date-picker-content')
      const selectedDate = await page.locator('.CalendarCellTrigger[data-value="2024-09-09"]')

      await inputMasked.fill('09.09.2024')
      await page.waitForTimeout(300)
      await expect(calendar).toBeVisible()
      await expect(selectedDate).toHaveAttribute('data-selected', 'true')
      await expect(page).toHaveScreenshot('input-mask-date-selected.png')
    })

    test('Позволяет выбрать год и месяц из выпадающих списков в календаре', async ({ page }) => {
      await page.clock.install({ time: new Date('2024-09-06T08:00:00') })
      await page.goto('/input-mask-date-required')
      const inputMasked = await page.getByTestId('date-picker-input')
      const monthSelect = await page.locator('.Calendar__MonthSelect')
      const yearSelect = await page.locator('.Calendar__YearSelect')

      await inputMasked.focus()
      await page.waitForTimeout(300)

      // Select month
      await monthSelect.click()
      await page.getByText('Март').click()
      await page.waitForTimeout(300)

      // Select year
      await yearSelect.click()
      await page.getByText('2025').click()
      await page.waitForTimeout(300)

      // click on the date cell
      const dateCell = page.locator('.CalendarCellTrigger').nth(10)
      await dateCell.click()
      await page.waitForTimeout(300)

      await expect(page).toHaveScreenshot('input-mask-date-month-year-selected.png')
    })
  })

  test.describe('Маскированное поле для диапазона дат (период)', () => {
    test('Отображает поля для ввода диапазона дат', async ({ page }) => {
      await page.goto('/input-mask-range-required')
      const fromInput = await page.getByTestId('date-range-from')
      const toInput = await page.getByTestId('date-range-to')

      await expect(fromInput).toBeVisible()
      await expect(toInput).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-date-range.png')
    })

    test('Поля диапазона дат обязательны по умолчанию', async ({ page }) => {
      await page.goto('/input-mask-range-required')
      const finishButton = await page.getByText('Далее')
      const errorRequired = await page.getByText('Обязательное поле')

      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorRequired).toHaveCount(2)
      await expect(page).toHaveScreenshot('input-mask-date-range-required-error.png')
    })

    test('Показывает ошибку при вводе некорректного диапазона дат', async ({ page }) => {
      await page.goto('/input-mask-range-required')
      const fromInput = await page.getByTestId('date-range-from')
      const toInput = await page.getByTestId('date-range-to')
      const finishButton = await page.getByText('Далее')
      const errorFormat = await page.getByText('Некорректный период')

      // Test when first date is later than second date
      await fromInput.fill('10.09.2024')
      await toInput.fill('05.09.2024')
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(errorFormat).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-date-range-incorrect-format-1.png')

      // Test when second date is earlier than first date
      await fromInput.fill('01.09.2024')
      await toInput.fill('31.08.2024')
      await page.keyboard.press('Escape')
      await expect(errorFormat).toBeVisible()
      await expect(page).toHaveScreenshot('input-mask-date-range-incorrect-format-2.png')

      // Test when dates are correct next question is shown
      await fromInput.fill('01.09.2024')
      await toInput.fill('10.09.2024')
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(fromInput).not.toBeVisible()
      await expect(toInput).not.toBeVisible()
    })
  })
})
