import { expect, test } from "@playwright/test";

test.describe("Вопрос 'Текстовый ответ'", () => {
  test.describe("Текстовое поле", () => {
    test("Отображает текстовое поле по умолчанию", async ({ page }) => {
      await page.goto("/?key=textarea-default");
      const widget = await page.getByTestId("fc-widget");
      const textarea = await widget.getByTestId("textarea");

      await expect(textarea).toBeVisible();
      await expect(widget).toHaveScreenshot("textarea-default.png");
    });

    test("Обязательное поле по умолчанию", async ({ page }) => {
      await page.goto("/?key=textarea-default");
      const widget = await page.getByTestId("fc-widget");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText("Обязательное поле");

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "textarea-default-required-error.png",
      );
    });

    test("Необязательное поле", async ({ page }) => {
      await page.goto("/?key=textarea-unrequired");
      const widget = await page.getByTestId("fc-widget");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText("Обязательное поле");

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).not.toBeVisible();
      await expect(widget).toHaveScreenshot("textarea-unrequired.png");
    });

    test("Минимальная длина текста 100 символов", async ({ page }) => {
      await page.goto("/?key=textarea-min-100");
      const widget = await page.getByTestId("fc-widget");
      const textarea = await widget.getByTestId("textarea");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText(
        "Должно быть введено хотя бы 100 символов",
      );

      await textarea.fill("Тест");
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot("textarea-min-100-error.png");

      await textarea.fill("Тест".repeat(25));
      await textarea.blur();
      await page.waitForTimeout(100);
      await expect(error).not.toBeVisible();
      await expect(widget).toHaveScreenshot("textarea-min-100.png");
    });
  });
  test.describe("Не маскированное однострочное поле", () => {
    test("Отображает однострочное поле по умолчанию", async ({ page }) => {
      await page.goto("/?key=input-default");
      const widget = await page.getByTestId("fc-widget");
      const input = await widget.getByTestId("input");

      await expect(input).toBeVisible();
      await expect(widget).toHaveScreenshot("input-default.png");
    });

    test("Обязательное поле по умолчанию", async ({ page }) => {
      await page.goto("/?key=input-default");
      const widget = await page.getByTestId("fc-widget");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText("Обязательное поле");

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot("input-default-required-error.png");
    });

    test("Необязательное поле", async ({ page }) => {
      await page.goto("/?key=input-unrequired");
      const widget = await page.getByTestId("fc-widget");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText("Обязательное поле");

      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).not.toBeVisible();
      await expect(widget).toHaveScreenshot("input-unrequired.png");
    });

    test("Минимальная длина текста 100 символов", async ({ page }) => {
      await page.goto("/?key=input-min-100");
      const widget = await page.getByTestId("fc-widget");
      const input = await widget.getByTestId("input");
      const finishButton = await page.getByText("Завершить");
      const error = await widget.getByText(
        "Должно быть введено хотя бы 100 символов",
      );

      await input.fill("Тест");
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(error).toBeVisible();
      await expect(widget).toHaveScreenshot("input-min-100-error.png");

      await input.fill("Тест".repeat(25));
      await input.blur();
      await page.waitForTimeout(300);
      await expect(error).not.toBeVisible();
      await expect(widget).toHaveScreenshot("input-min-100.png");
    });
  });
  test.describe("Маскированное текстовое поле", () => {
    test("Отображает маскированное поле для телефона", async ({ page }) => {
      await page.goto("/?key=input-mask-phone");
      const widget = await page.getByTestId("fc-widget");
      const inputMasked = await widget.getByTestId("input-masked");
      const finishButton = await page.getByText("Завершить");
      const errorRequired = await widget.getByText("Обязательное поле");
      const errorFormat = await widget.getByText("Неверный формат");

      await expect(inputMasked).toBeVisible();
      await expect(widget).toHaveScreenshot("input-mask-phone.png");

      // Check for required error
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(errorRequired).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-phone-required-error.png",
      );

      // Fill the input with an invalid phone number and check for format error
      await inputMasked.fill("970");
      await page.waitForTimeout(300);
      await expect(errorFormat).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-phone-format-error.png",
      );
      await inputMasked.clear();
      await inputMasked.focus();
      await inputMasked.fill("9705680304");
      await expect(errorFormat).not.toBeVisible();
      await expect(widget).toHaveScreenshot("input-mask-phone-filled.png");
    });

    test("Отображает маскированное поле для email", async ({ page }) => {
      await page.goto("/?key=input-mask-email");
      const widget = await page.getByTestId("fc-widget");
      const inputMasked = await widget.getByTestId("input-masked");
      const finishButton = await page.getByText("Завершить");
      const errorRequired = await widget.getByText("Обязательное поле");

      await expect(inputMasked).toBeVisible();
      await expect(widget).toHaveScreenshot("input-mask-email.png");

      // Check for required error
      await finishButton.click();
      await page.waitForTimeout(300);
      const errorFormat = await widget.getByText("Неверный формат");
      await expect(errorRequired).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-email-required-error.png",
      );

      // Fill the input with an invalid email and check for format error
      await inputMasked.fill("invalid-email");
      await page.waitForTimeout(300);
      await expect(errorFormat).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-email-format-error.png",
      );

      // fill correct email
      await inputMasked.fill("<EMAIL>");
      await expect(errorFormat).not.toBeVisible();
    });

    test("Отображает маскированное поле для вебсайта", async ({ page }) => {
      await page.goto("/?key=input-mask-website");
      const widget = await page.getByTestId("fc-widget");
      const inputMasked = await widget.getByTestId("input-masked");
      const finishButton = await page.getByText("Завершить");
      const errorRequired = await widget.getByText("Обязательное поле");
      const errorFormat = await widget.getByText("Неверный формат");

      await expect(inputMasked).toBeVisible();
      await expect(widget).toHaveScreenshot("input-mask-website.png");

      // Check for required error
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(errorRequired).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-website-required-error.png",
      );

      // Fill the input with an invalid website and check for format error
      await inputMasked.fill("invalid-website");
      await expect(errorFormat).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-website-format-error.png",
      );

      // fill correct website
      await inputMasked.fill("https://example.com");
      await expect(errorFormat).not.toBeVisible();
    });
    test("Отображает маскированное поле для ФИО", async ({ page }) => {
      await page.goto("/?key=input-mask-fio");
      const widget = await page.getByTestId("fc-widget");
      const inputName = await widget.getByTestId("input-name");
      const inputSurname = await widget.getByTestId("input-surname");
      const inputPatronym = await widget.getByTestId("input-patronym");
      const finishButton = await page.getByText("Завершить");
      const errorRequired = await widget.getByText("Обязательное поле");

      await expect(inputName).toBeVisible();
      await expect(inputSurname).toBeVisible();
      await expect(inputPatronym).toBeVisible();
      await expect(widget).toHaveScreenshot("input-mask-fio.png");

      // Check for required error
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(errorRequired).toHaveCount(3);
      await expect(widget).toHaveScreenshot(
        "input-mask-fio-required-error.png",
      );

      // Fill the inputs with valid FIO data
      await inputName.fill("Иван");
      await inputSurname.fill("Иванов");
      await inputPatronym.fill("Иванович");
      await page.waitForTimeout(300);
      await expect(errorRequired).toHaveCount(0);
      await inputPatronym.blur();
      await expect(widget).toHaveScreenshot("input-mask-fio-filled.png");
    });

    test("Отображает маскированное поле для ФИО (сложный)", async ({
      page,
    }) => {
      await page.goto("/?key=input-mask-fio-complex");
      const widget = await page.getByTestId("fc-widget");
      const inputName = await widget.getByTestId("input-name");
      const inputSurname = await widget.getByTestId("input-surname");
      const inputPatronym = await widget.getByTestId("input-patronym");
      const finishButton = await page.getByText("Завершить");
      const errorRequired = await widget.getByText("Обязательное поле");

      await expect(inputName).not.toBeVisible(); // Name is hidden
      await expect(inputSurname).toBeVisible();
      await expect(inputPatronym).toBeVisible();
      await expect(widget).toHaveScreenshot("input-mask-fio-complex.png");

      // Check for required error
      await finishButton.click();
      await page.waitForTimeout(300);
      await expect(errorRequired).toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-fio-complex-required-error.png",
      );

      // Fill the inputs with valid FIO data
      await inputSurname.fill("Иванов");
      await inputPatronym.fill("Иванович");
      await inputPatronym.blur();
      await page.waitForTimeout(300);
      await expect(errorRequired).not.toBeVisible();
      await expect(widget).toHaveScreenshot(
        "input-mask-fio-complex-filled.png",
      );
    });

    test("Отображает маскированное поле для телефона при клике на кнопку триггер", async ({
      page,
    }) => {
      await page.goto("/?key=appearance-click:input-mask-phone");

      const triggerButton = await page.locator(".fc-widget-preview-button");
      await expect(triggerButton).toBeVisible();
      const finishButton = await page.getByText("Завершить");

      // Click the trigger button to show the widget
      await triggerButton.click();

      const widget = await page.getByTestId("fc-widget");
      await expect(widget).toBeVisible();

      const inputMasked = await widget.getByTestId("input-masked");
      await expect(inputMasked).toBeVisible();

      // Take a screenshot of the entire page to show the widget in modal
      await expect(page).toHaveScreenshot("input-mask-phone-modal.png");

      // Fill the input with an invalid phone number and check for format error
      await inputMasked.fill("970");

      await finishButton.click();
      await page.waitForTimeout(300);
      const errorFormat = await widget.getByText("Неверный формат");
      await expect(errorFormat).toBeVisible();
      await expect(page).toHaveScreenshot(
        "input-mask-phone-modal-format-error.png",
      );

      // Fill the input with a valid phone number
      await inputMasked.clear();
      await inputMasked.fill("9705680304");
      await expect(errorFormat).not.toBeVisible();

      // Take another screenshot after interaction
      await expect(page).toHaveScreenshot("input-mask-phone-modal-filled.png");

      // Submit the form
      await finishButton.click();

      // Check that the input is no longer visible (moved to next question or closed)
      await expect(inputMasked).not.toBeVisible();
    });
  });
});
