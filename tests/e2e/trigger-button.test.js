// tests/trigger-button.spec.js

import { test, expect } from "@playwright/test";

test.describe("Кнопка триггер для виджета", () => {
  test("Должен отображать кнопку триггер при загрузке страницы", async ({
    page,
  }) => {
    // Navigate to the page where the widget is embedded
    await page.goto("/?key=appearance-click");

    // Wait for the trigger button to be visible
    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();

    // Take a screenshot of the trigger button before clicking
    await expect(triggerButton).toHaveScreenshot(
      "trigger-button-before-click.png",
    );

    // Click the trigger button
    await triggerButton.click();

    // Wait for the widget to be visible
    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toBeVisible();
  });

  test("Отображает кнопку триггер в виде иконки", async ({ page }) => {
    // Navigate to the page where the widget is embedded with icon trigger
    await page.goto("/?key=button-type-icon");
    // Wait for the trigger button to be visible
    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();
    // Take a screenshot of the trigger button in icon mode
    await expect(triggerButton).toHaveScreenshot("trigger-button-icon.png");
  });

  test("Отображает кнопку триггер с настраиваемым дизайном", async ({
    page,
  }) => {
    // Navigate to the page with custom design parameters
    await page.goto("/?key=custom-button-style");

    // Wait for the trigger button to be visible
    const triggerButton = await page.locator(".fc-widget-preview-button");
    await expect(triggerButton).toBeVisible();

    // Take a screenshot of the trigger button with custom design
    await expect(triggerButton).toHaveScreenshot(
      "trigger-button-custom-design.png",
    );
  });
});
