import { expect, test } from "@playwright/test";

test.describe("Widget", () => {
  test("Отображает виджет", async ({ page }) => {
    await page.goto("/");
    const widget = await page.getByTestId("fc-widget");

    await expect(widget).toBeVisible();
  });

  test("Не отображает виджет если link: null", async ({ page }) => {
    await page.goto("/?link=null");
    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toBeHidden();
  });

  test("Закрывает виджет при клике на крестик", async ({ page }) => {
    await page.goto("/?key=default");
    const closeButton = await page.getByTestId("fc-widget-close");
    await closeButton.click();

    // Ждем 1 секунду, чтобы виджет успел закрыться
    await page.waitForTimeout(1000);

    const widget = await page.getByTestId("fc-widget");
    await expect(widget).toBeHidden();
  });

  test.describe("Шапка вопроса", () => {
    test("Отображает заголовок", async ({ page }) => {
      await page.goto("/?key=default");
      const description = await page.getByText("Звездный рейтинг");
      const subdescription = await page.getByText("Оцените наш сервис");
      const unrequiredText = await page.getByText("Необязательный");

      await expect(description).toBeVisible();

      // Проверяем, что подзаголовок и текст "Необязательный" по умолчанию скрыты
      await expect(subdescription).toBeHidden();
      await expect(unrequiredText).toBeHidden();
    });
  });

  test("Отображает заголовок и подзаголовок", async ({ page }) => {
    await page.goto("/?key=default-with-subdescription");
    const description = await page.getByText("Звездный рейтинг");
    const subdescription = await page.getByText("Оцените наш сервис");
    const unrequiredText = await page.getByText("Необязательный");

    await expect(description).toBeVisible();
    await expect(subdescription).toBeVisible();

    // Проверяем, что текст "Необязательный" по умолчанию скрыт
    await expect(unrequiredText).toBeHidden();
  });

  test("Отображает текст 'Необязательный'", async ({ page }) => {
    await page.goto("/?key=default-with-unrequired-text");
    const unrequiredText = await page.getByText("Необязательный");
    await expect(unrequiredText).toBeVisible();
  });

  test("Отображает свой текст для поля 'Необязательный'", async ({ page }) => {
    await page.goto("/?key=default-with-unrequired-custom-text");
    const unrequiredText = await page.getByText(
      "Необязательный (проходите, если хотите)",
    );

    await expect(unrequiredText).toBeVisible();
  });

  test.describe("Общий дизайн вопроса", () => {
    test("Отображает кнопку 'завершить'", async ({ page }) => {
      await page.goto("/?key=default");
      const prevButton = await page.getByText("Вернуться");
      const nextButton = await page.getByText("Далее");
      const finishButton = await page.getByText("Завершить");

      await expect(finishButton).toBeVisible();

      // Проверяем, что кнопки "Вернуться" и "Далее" скрыты
      await expect(prevButton).toBeHidden();
      await expect(nextButton).toBeHidden();
    });
    test("Отображает кнопку 'Назад' если вопрос не первый", async ({
      page,
    }) => {
      await page.goto("/?key=default-2-questions");
      const starRatingItem = await page
        .getByTestId("fc-widget-star-rating-item")
        .last();
      const nextButton = await page.getByText("Далее");
      const prevButton = await page.getByText("Вернуться");

      // Кликаем на последнюю звезду и переходим к следующему вопросу
      await starRatingItem.click();
      await nextButton.click();

      await expect(prevButton).toBeVisible();

      // Проверяем, что кнопка "Далее" скрыта
      await expect(nextButton).toBeHidden();
    });

    // test("Отображает нижний ряд кнопок, если это последний вопрос", async ({
    //   page,
    // }) => {
    //   await page.goto("/?key=default-without-prev-button");
    //   const finishButton = await page.getByText("Завершить");

    //   await expect(finishButton).toBeVisible();
    // });
  });
  test("Не отображает кнопку 'Назад' если она выключена в настройках", async ({
    page,
  }) => {
    await page.goto("/?key=default-without-prev-button-2-questions");
    const starRatingItem = await page
      .getByTestId("fc-widget-star-rating-item")
      .last();
    const prevButton = await page.getByText("Вернуться");

    // Кликаем на последнюю звезду и переходим к следующему вопросу
    await starRatingItem.click();

    await expect(prevButton).toBeHidden();
  });
  test("Отображает фоновый цвет вместо цвета обложки", async ({ page }) => {
    await page.goto("/?key=default-with-bg-color");
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("bg-color.png");
  });
  test("Отображает фоновое изображение", async ({ page }) => {
    await page.goto("/?key=default-with-bg-image");
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("bg-image.png");
  });
  test("Отображает фоновое изображение для мобильных устройств с затемнением", async ({
    page,
  }) => {
    await page.goto("/?key=default-with-mobile-bg-image");
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("mobile-bg-image-darkened.png");
  });
  test("Корректно заменяет переменные", async ({ page }) => {
    // Navigate to the page with the necessary URL parameters
    await page.goto(
      "/?key=replace-placeholders&test=ivan&test2=ivanov&alt=wow!&link=https%3A%2F%2Fwww.google.com%2Fwebhp%3Fhl%3Dru%26sa%3DX%26ved%3D0ahUKEwiRkuKqpvmGAxVlJRAIHbvHCawQPAgJ",
    );
    const nextButton = await page.getByText("Далее");
    const widget = await page.getByTestId("fc-widget");

    // 1. NPS rating question
    const npsRating = await page.getByTestId("rating-nps-item").nth(9);
    await npsRating.click();
    await expect(widget).toHaveScreenshot("replace-placeholders-step1-nps.png");
    await nextButton.click();
    await page.waitForTimeout(500);

    // 2. Star rating question
    const description = await page.getByText("Ответ на первый вопрос: 9");
    const subdescription = await page.getByText("URL param test: ivan");
    const stars = await page.getByTestId("fc-widget-star-rating-item");

    await stars.first().click();
    await expect(description).toBeVisible();
    await expect(subdescription).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "replace-placeholders-step2-star-rating.png",
    );

    await nextButton.click();
    await page.waitForTimeout(500);

    // 3. Variants question
    const variantsQuestion = await page.getByText("Почему 1 звезда?");
    const checks = await page.getByTestId("check");
    const selfVariantTextarea = await page.getByTestId("textarea");
    await checks.first().click();
    await page.waitForTimeout(50);
    await checks.last().click();

    await page.waitForTimeout(300);
    await selfVariantTextarea.fill("Self variant");
    await expect(variantsQuestion).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "replace-placeholders-step3-variants.png",
    );

    await nextButton.click();

    // 4. Text question
    const textQuestion = await page.getByText(
      "Ответ на третий вопрос: Раз, Self variant",
    );
    const textarea = await page.getByTestId("textarea").last();
    await textarea.fill("Some text answer");
    await expect(textQuestion).toBeVisible();
    await expect(widget).toHaveScreenshot(
      "replace-placeholders-step4-text.png",
    );

    await nextButton.click();
    await page.waitForTimeout(500);

    // 5. Intermediate block
    const intermediateBlock = await page.getByText("Промежуточный результат");
    await expect(intermediateBlock).toBeVisible();

    // Check that all placeholders are replaced correctly
    const answers = [
      "Ответ на первый вопрос 9",
      "Ответ на второй вопрос 1 звезда",
      "Ответ на третий вопрос Раз, Self variant",
      "Ответ на четверный вопрос Some text answer",
      "Ответ на ДЕСЯТЫЙ вопрос (должно быть пусто)",
    ];
    for (const answer of answers) {
      await expect(page.getByText(answer)).toBeVisible();
    }

    await expect(
      page.getByText("Значение параметра test в url = ivan"),
    ).toBeVisible();
    await expect(
      page.getByText("Значение параметра test2 в url = ivanov"),
    ).toBeVisible();

    const filialParams = [
      "Характеритика 1 филиала = https://wavehouse.ru/wp-content/uploads/2017/04/mini.jpeg",
      "Характеритика 2 филиала = https://rg.ru/uploads/images/192/68/94/987654.jpg",
      "Характеритика 3 филиала = https://naked-science.ru/wp-content/uploads/2017/08/field_image_1_624.jpg",
    ];
    for (const param of filialParams) {
      await expect(page.getByText(param)).toBeVisible();
    }

    const image = await page.getByAltText("wow!");
    const link = await page.locator("a[href^='https://www.google.com/']");
    await expect(image).toBeVisible();
    await expect(link).toBeVisible();

    // Take a final screenshot of the intermediate block
    await expect(widget).toHaveScreenshot(
      "replace-placeholders-final-intermediate-block.png",
    );
  });

  test("Отображает виджет с пользовательским дизайном", async ({ page }) => {
    await page.goto("/?key=custom-design");
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toHaveScreenshot("custom-design-widget.png");
  });
  test("Отображает виджет с опциями Без клика > Упрощенный Page Stop", async ({
    page,
  }) => {
    await page.goto("/?key=simplified-page-stop");
    const widget = await page.getByTestId("fc-widget");
    await page.waitForLoadState("networkidle");
    await expect(widget).toBeVisible();
    await expect(widget).toHaveScreenshot("simplified-page-stop-widget.png");
  });
  test("Закрывает виджет при включенной опции 'Закрыть по клику на кнопку 'Завершить'", async ({
    page,
  }) => {
    await page.goto("/?key=close-on-finish-click:default");
    const widget = await page.getByTestId("fc-widget");
    const finishButton = await page.getByText("Завершить");
    const star = await page.getByTestId("fc-widget-star-rating-item").first();
    await expect(widget).toBeVisible();

    await star.click();
    await finishButton.click();
    await expect(widget).toBeHidden();
  });
});
