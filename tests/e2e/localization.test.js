import { expect, test } from "@playwright/test";

test.describe("Localization - English", () => {
  test("Survey localization", async ({ page }) => {
    await page.goto("/?key=localization-english");

    // Start screen localization
    const startScreenText = await page.getByText(
      "We value your opinion and would like to learn about your experience.",
    );
    await expect(startScreenText).toBeVisible();

    const agreementText = await page.getByText(
      "I agree to participate in this survey",
    );
    await expect(agreementText).toBeVisible();
    await agreementText.click();

    const startButtonText = await page.getByText("Start Survey");
    await expect(startButtonText).toBeVisible();

    // Go to the next question (Star rating)
    await startButtonText.click();

    // Check for star rating error
    const nextButton = await page.getByText("Next");
    const finishButton = await page.getByText("Success!");
    await nextButton.click();
    const ratingError = await page.getByText("You need to evaluate");
    await expect(ratingError).toBeVisible();

    // Click first star and check for assessment error
    const firstStar = await page
      .getByTestId("fc-widget-star-rating-item")
      .first();
    await firstStar.click();
    await nextButton.click();
    const assessmentError = await page.getByText(
      "You must select at least one answer",
    );
    await expect(assessmentError).toBeVisible();

    // Click self-variant check and check for textarea error
    const selfVariantCheck = await page.getByTestId("assessments-check").last();
    await selfVariantCheck.click();
    await nextButton.click();
    const textareaError = await page.getByText("Required field");
    await expect(textareaError).toBeVisible();

    // Type one character and check for min length error
    const textarea = await page.locator("textarea");
    await textarea.fill("a");
    const minLengthError = await page.getByText(
      "At least 10 symbols must be entered",
    );
    await expect(minLengthError).toBeVisible();

    // Fill the textarea with valid input and proceed to the next question
    await textarea.fill("This is a valid input with more than 10 characters");
    await nextButton.click();

    // Check rating question localization
    const ratingItems = await page.getByTestId("rating-scale-item");
    const ratingQuestionText = await page.getByText("Rate our service");
    await expect(ratingQuestionText).toBeVisible();

    const ratingSubDescription = await page.getByText(
      "Your feedback is important to us",
    );
    await expect(ratingSubDescription).toBeVisible();

    await ratingItems.nth(3).click();
    const ratingLabel = await page.getByText("Good");

    // Click on a rating and check for comment field
    await expect(ratingLabel).toBeVisible();
    const commentLabel = await page.getByText("Additional comments");
    await expect(commentLabel).toBeVisible();

    const commentPlaceholder = await page.getByPlaceholder(
      "Enter your comment here",
    );
    await expect(commentPlaceholder).toBeVisible();

    // Check skip option
    const skipText = await page.getByText("Skip this question");
    await expect(skipText).toBeVisible();

    // Proceed without entering a comment
    await nextButton.click();
    const commentError = await page.getByText("Required field");
    await expect(commentError).toBeVisible();

    // Enter a comment and proceed
    await commentPlaceholder.fill("This is a test comment");
    await nextButton.click();

    // Check NPS Rating question localization
    const npsItems = await page.getByTestId("rating-nps-item");
    const npsQuestionText = await page.getByText(
      "How likely are you to recommend us to your friends?",
    );
    await expect(npsQuestionText).toBeVisible();

    const npsStartLabel = await page.getByText("Not likely");
    await expect(npsStartLabel).toBeVisible();

    const npsEndLabel = await page.getByText("Very likely");
    await expect(npsEndLabel).toBeVisible();

    await npsItems.nth(2).click();
    await nextButton.click();

    // Check Smile Rating question localization
    const smileRatingQuestionText = await page.getByText(
      "Rate your overall experience",
    );
    await expect(smileRatingQuestionText).toBeVisible();

    const smileRatingSubDescription = await page.getByText(
      "We value your feedback",
    );
    await expect(smileRatingSubDescription).toBeVisible();

    const smileLabels = ["Very bad", "Bad", "Average", "Good", "Excellent"];
    for (const label of smileLabels) {
      const smileLabel = await page.getByText(label, { exact: true });
      await expect(smileLabel).toBeVisible();
    }

    const smileItems = await page.getByTestId("img-rating-item");

    await smileItems.nth(2).click();

    await nextButton.click();

    // Check intermediate block localization
    const intermediateBlock = await page.getByText("Welcome to our survey!");
    const intermediateBlockAgreement = await page.getByText(
      "I agree to participate in this survey",
    );
    await expect(intermediateBlock).toBeVisible();
    await expect(intermediateBlockAgreement).toBeVisible();
    await intermediateBlockAgreement.click();

    await nextButton.click();

    // Check text question localization
    const textQuestionText = await page.getByText(
      "Please provide your phone number",
    );
    await expect(textQuestionText).toBeVisible();

    const textSubDescription = await page.getByText(
      "We may use this to contact you about your feedback",
    );
    await expect(textSubDescription).toBeVisible();

    const textInput = await page.getByPlaceholder("Enter your phone number");
    await expect(textInput).toBeVisible();

    // Check for required error
    await nextButton.click();
    const requiredError = await page.getByText("Required field");
    await expect(requiredError).toBeVisible();

    // Check for invalid format error
    await textInput.fill("123");
    const invalidFormatError = await page.getByText("Invalid format");
    await expect(invalidFormatError).toBeVisible();

    // Fill with valid input and proceed
    await textInput.clear();
    await page.waitForTimeout(300);
    await textInput.pressSequentially("951-999-99-99");
    await nextButton.click();
    await expect(requiredError).not.toBeVisible();
    await expect(invalidFormatError).not.toBeVisible();

    // Check FIO question localization
    const fioQuestionText = await page.getByText("Enter your full name");
    await expect(fioQuestionText).toBeVisible();

    const fioSubDescription = await page.getByText(
      "Please provide your first name, last name, and patronymic (if applicable)",
    );
    await expect(fioSubDescription).toBeVisible();

    const nameInput = await page.getByPlaceholder(
      "Name placeholder (English)",
      { exact: true },
    );
    const surnameInput = await page.getByPlaceholder(
      "Surname placeholder (English)",
      { exact: true },
    );
    const patronymicInput = await page.getByPlaceholder(
      "Patronymic placeholder (English)",
    );

    await expect(nameInput).toBeVisible();
    await expect(surnameInput).toBeVisible();
    await expect(patronymicInput).toBeVisible();

    // Check for required error
    await nextButton.click();
    const nameRequiredError = await page.getByText("Required field").first();
    const surnameRequiredError = await page.getByText("Required field").nth(1);
    await expect(nameRequiredError).toBeVisible();
    await expect(surnameRequiredError).toBeVisible();

    // Check for minLength error
    await nameInput.fill("A");
    await surnameInput.fill("B");
    const nameMinLengthError = await page
      .getByText("At least 2 symbols must be entered")
      .first();
    const surnameMinLengthError = await page
      .getByText("At least 2 symbols must be entered")
      .nth(1);
    await expect(nameMinLengthError).toBeVisible();
    await expect(surnameMinLengthError).toBeVisible();

    // Fill with valid input and proceed
    await nameInput.fill("John");
    await surnameInput.fill("Doe");
    await patronymicInput.fill("Smith");
    await nextButton.click();
    await expect(nameRequiredError).not.toBeVisible();
    await expect(surnameRequiredError).not.toBeVisible();
    await expect(nameMinLengthError).not.toBeVisible();
    await expect(surnameMinLengthError).not.toBeVisible();

    // Check variants question localization
    const variantsQuestionText = await page.getByText(
      "Select one or more options",
    );
    await expect(variantsQuestionText).toBeVisible();

    const variantsSubDescription = await page.getByText(
      "You can also add your own option if needed",
    );
    await expect(variantsSubDescription).toBeVisible();

    const variantOptions = await page.getByTestId("check");
    await expect(variantOptions).toHaveCount(4); // 3 options + 1 custom option

    // Check that options are rendered in English
    await expect(variantOptions.nth(0)).toHaveText("One");
    await expect(variantOptions.nth(1)).toHaveText("Two");
    await expect(variantOptions.nth(2)).toHaveText("Three");
    await expect(variantOptions.nth(3)).toHaveText("Custom option");

    // Check for required error
    await finishButton.click();
    const variantsRequiredError = await page.getByText(
      "You must select at least one answer",
    );
    await expect(variantsRequiredError).toBeVisible();

    // Check custom option
    await variantOptions.nth(3).click();
    const customOptionInput = await page.getByPlaceholder(
      "Enter your custom option",
    );
    await expect(customOptionInput).toBeVisible();

    // Check for required error in custom option
    const customOptionRequiredError = await page.getByText("Required field");
    await expect(customOptionRequiredError).toBeVisible();

    // Fill custom option and proceed
    await page.locator("textarea").fill("My custom answer");
    await finishButton.click();
    await expect(variantsRequiredError).not.toBeVisible();
    await expect(customOptionRequiredError).not.toBeVisible();

    const finishText = await page.getByText(
      "The survey has been successfully completed!",
    );
    await expect(finishText).toBeVisible();
  });
});
